import React from "react";
import { act, render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import { MemoryRouter } from "react-router-dom"; // Import MemoryRouter
import configureStore from "redux-mock-store";
import { useSelector } from "react-redux";
import { useGetAllowancesItemsQuery, useGetAllowanceItemsDifferenceQuery } from "../../create-event/service/apis/allowance-api";
import * as selectors from "@me/data-rtk";
import AllowanceMainEntryTable from "./allowance-main-entry-table";
import '@testing-library/jest-dom';
import { app_store } from "@me/data-rtk";
import userEvent from "@testing-library/user-event";

// Mocking dependencies
jest.mock("react-redux", () => ({
  ...jest.requireActual("react-redux"),
  useSelector: jest.fn(),
}));

jest.mock("../../create-event/service/apis/allowance-api", () => ({
  useGetAllowancesItemsQuery: jest.fn(),
  useGetAllowanceItemsDifferenceQuery: jest.fn(),
}));

jest.mock("../../../shared/helpers/event-flow-helpers", () => ({
  saveToSessionStorage: jest.fn(),
}));

jest.mock("./allowance-table-component", () => ({
  AllowanceTableComponent: ({ items }) => (
    <div>
      Mocked AllowanceTableComponent
      {items && items.length > 0 && (
        <ul>
          {items.map((item, index) => (
            <li key={index}>Item ID: {item.itemId}</li>
          ))}
        </ul>
      )}
    </div>
  ),
}));

jest.mock("./allowances-upc-details", () => ({
  AllowancesUpcDetails: () => <div>Mocked AllowancesUpcDetails</div>,
}));

jest.mock("./main-entry-overlaps-table", () => ({
  default: () => <div>Mocked MainEntryOverlapsTable</div>,
}));

describe("AllowanceMainEntryTable", () => {
  const mockStore = configureStore([]);
  let store;

  beforeEach(() => {
    store = mockStore({});
    (useSelector as jest.Mock).mockImplementation((selectorFn) => {
      const mockData = {
        selectedSwitchValue_rn: { data: { selectedSwitch: "mockSwitch" } },
        selectedFilters_rn: { data: { isFilterApplied: false, isFilterReset: true } },
        leadDistributors_rn: { data: { stepData: {}, leadDistMode: null } },
        leadDistributorsChanged_rn: { data: { isLeadChange: false } },
        allowanceTableData_rn: { data: { tableData: [], filteredAndSortedIds: [] } },
        allowance_temp_work: { data: { allowanceData: {} } },
        offer_amounts_details: { data: {} },
        event_details_data: { data: {} },
        allowanceTableColsGroupConfig_rn: { data: {} },
      };
      return selectorFn(mockData);
    });

    useGetAllowancesItemsQuery.mockReturnValue({
      data: { allowances: [] },
      error: null,
      isFetching: false,
    });

    useGetAllowanceItemsDifferenceQuery.mockReturnValue({
      data: null,
      error: null,
      isFetching: false,
    });
  });

  it("handles API errors gracefully", () => {
    useGetAllowancesItemsQuery.mockReturnValue({
      data: null,
      error: { data: { message: "API Error" } },
      isFetching: false,
    });

    render(
      <Provider store={store}>
        <MemoryRouter> {/* Wrap with MemoryRouter */}
          <AllowanceMainEntryTable />
        </MemoryRouter>
      </Provider>
    );

    expect(screen.getByText("API Error")).toBeInTheDocument();
  });

  it("dispatches actions on state changes", () => {
    const dispatch = jest.fn();
    store.dispatch = dispatch;

    render(
      <Provider store={store}>
        <MemoryRouter> {/* Wrap with MemoryRouter */}
          <AllowanceMainEntryTable />
        </MemoryRouter>
      </Provider>
    );

    expect(dispatch).toHaveBeenCalled();
  });
});