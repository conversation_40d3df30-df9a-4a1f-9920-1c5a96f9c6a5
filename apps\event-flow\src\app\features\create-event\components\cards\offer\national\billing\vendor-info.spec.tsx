import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import { VendorInfo } from "./vendor-info"; // adjust the import if necessary
import { displayData } from "../../offer-sections/billing/billing-info-allowance";

// Mock displayData to return a simple span element.
jest.mock("../../offer-sections/billing/billing-info-allowance", () => ({
  displayData: jest.fn((label, value, bold) => (
    <span data-testid="display-data">
      {label}: {value}
    </span>
  )),
}));

describe("VendorInfo", () => {
  const uniqueVendorNames = [
    {
      allowanceBillingInfo: {
        absMerchVendor: "Merch1",
        absVendorName: "Vendor1",
      },
    },
    {
      allowanceBillingInfo: {
        absMerchVendor: "Merch2",
        absVendorName: "Vendor2",
      },
    },
  ];

  beforeEach(() => {
    displayData.mockClear();
  });

  it("renders the header text", () => {
    render(<VendorInfo uniqueVendorNames={uniqueVendorNames} />);
    expect(
      screen.getByText(/For reference, the items in this Offer are setup under the following Merchandising System Vendors:/)
    ).toBeInTheDocument();
  });

  it("calls displayData for each vendor", () => {
    render(<VendorInfo uniqueVendorNames={uniqueVendorNames} />);
    // Each vendor calls displayData twice, so expect 4 calls total.
    expect(displayData).toHaveBeenCalledTimes(4);
    expect(displayData).toHaveBeenNthCalledWith(1, "ABS Merch Vendor", "Merch1", true);
    expect(displayData).toHaveBeenNthCalledWith(2, "ABS Vendor Name", "Vendor1");
    expect(displayData).toHaveBeenNthCalledWith(3, "ABS Merch Vendor", "Merch2", true);
    expect(displayData).toHaveBeenNthCalledWith(4, "ABS Vendor Name", "Vendor2");
  });

  it("renders displayData output for all vendors", () => {
    render(<VendorInfo uniqueVendorNames={uniqueVendorNames} />);
    const displayDataElements = screen.getAllByTestId("display-data");
    expect(displayDataElements).toHaveLength(4);
    expect(displayDataElements[0]).toHaveTextContent("ABS Merch Vendor: Merch1");
    expect(displayDataElements[1]).toHaveTextContent("ABS Vendor Name: Vendor1");
    expect(displayDataElements[2]).toHaveTextContent("ABS Merch Vendor: Merch2");
    expect(displayDataElements[3]).toHaveTextContent("ABS Vendor Name: Vendor2");
  });
});
