import { Column } from "@albertsons/uds/molecule/Table/Table.types";
import { formatTimestamp } from "@me-upp-js/utilities";
import { appConstants } from "@me/utils-root-props";
import {
  getAllowanceKey,
  getAllowanceTypeByPerformance,
  getOfferAllowanceAmount,
} from "../../../../../create-event/service/allowance/allowance-service";
import efConstants from "../../../../../../shared/ef-constants/ef-constants";
import React, { useMemo } from "react";
import { EEVENT_STATUS } from "@me/util-helpers";
import ViewEventLink from "./View-event-link";

function OfferDetailsColumnConfig({
  gridData,
  cardIndex,
  gridConfig,
}): Column<any>[] {
  const {
    columns: {
      divisionId,
      shipStartDate,
      shipEndDate,
      orderStartDate,
      orderEndDate,
      arrivalStartDate,
      arrivalEndDate,
      allowanceStartDate,
      allowanceEndDate,
      status,
    },
  } = gridConfig;

  const cardData = gridData?.[cardIndex];
  const createInd = cardData?.createInd;

  const isHideCols = offer =>
    [EEVENT_STATUS.CANCELED, EEVENT_STATUS.REJECTED]?.includes(
      offer?.allowanceStatus
    ) || !offer?.includeInd;

  const getAmtLabel = offerData => {
    const allowanceType = getAllowanceTypeByPerformance(offerData?.performance);
    const allowanceTypeKey = getAllowanceKey(allowanceType?.toUpperCase());
    const isHF = allowanceTypeKey === efConstants.ALLOWANCE_TYPES.HEADERFLAT.key;
    return getOfferAllowanceAmount(allowanceType, [offerData], isHF);
  };

  const WithStatusGuard = ({
    offer,
    children,
    isDisplayText = false,
    hideCustomText = ""
  }: {
    offer: any;
    children: React.ReactNode;
    isDisplayText?: boolean;
    hideCustomText?: string;
  }) => {
    const isHide = isHideCols(offer);
    return (
      <div
        className={`text-sm px-3 ${
          isHide ? "bg-[#f9d3d345] h-full flex items-center" : ""
        } ${isHide && !isDisplayText ? "italic" : ""}`}
      >
        {isHide && !isDisplayText ? hideCustomText || "" : children}
      </div>
    );
  };

  const getAdditionalDatesCols = (): Column<any>[] => {
    const ALLOWANCE_DATE_COLS_SCAN = [
      {
        id: arrivalStartDate?.id,
        label: "Allowance Start Date",
        sortable: false,
        width: arrivalStartDate?.width,
        value: offer => (
          <WithStatusGuard offer={offer}>
            {formatTimestamp({
              timestamp:
                offer?.arrivalStartDate === "0001-01-01"
                  ? offer?.allowanceStartDate
                  : offer?.arrivalStartDate,
              pattern: "MM/DD/YY",
            })}
          </WithStatusGuard>
        ),
      },
      {
        id: "scanAllowanceEndate",
        label: "Allowance End Date",
        sortable: false,
        width: "3vw",
        value: offer => (
          <WithStatusGuard offer={offer}>
            {formatTimestamp({
              timestamp:
                offer?.arrivalEndDate === "0001-01-01"
                  ? offer?.allowanceEndDate
                  : offer?.arrivalEndDate,
              pattern: "MM/DD/YY",
            })}
          </WithStatusGuard>
        ),
      },
    ];

    const COMMON_ALLOW_DATE_COLS = [
      {
        id: allowanceStartDate?.id,
        label: allowanceStartDate?.label,
        width: allowanceStartDate?.width,
        sortable: allowanceStartDate?.sortable,
        value: offer => (
          <WithStatusGuard offer={offer}>
            {formatTimestamp({
              timestamp: offer?.allowanceStartDate,
              pattern: "MM/DD/YY",
            })}
          </WithStatusGuard>
        ),
      },
      {
        id: allowanceEndDate?.id,
        label: allowanceEndDate?.label,
        width: allowanceEndDate?.width,
        sortable: allowanceEndDate?.sortable,
        value: offer => (
          <WithStatusGuard offer={offer}>
            {formatTimestamp({
              timestamp: offer?.allowanceEndDate,
              pattern: "MM/DD/YY",
            })}
          </WithStatusGuard>
        ),
      },
    ];

    const ARRIVAL_DATE_COLS = [
      {
        id: arrivalStartDate?.id,
        label: arrivalStartDate?.label,
        width: arrivalStartDate?.width,
        sortable: arrivalStartDate?.sortable,
        value: offer => (
          <WithStatusGuard offer={offer}>
            {formatTimestamp({
              timestamp: offer?.arrivalStartDate,
              pattern: "MM/DD/YY",
            })}
          </WithStatusGuard>
        ),
      },
      {
        id: arrivalEndDate?.id,
        label: arrivalEndDate?.label,
        width: arrivalEndDate?.width,
        sortable: false,
        value: offer => (
          <WithStatusGuard offer={offer}>
            {formatTimestamp({
              timestamp: offer?.arrivalEndDate,
              pattern: "MM/DD/YY",
            })}
          </WithStatusGuard>
        ),
      },
    ];

    const ORDER_DATES_COLS = [
      {
        ...orderStartDate,
        value: offer => (
          <WithStatusGuard offer={offer}>
            {formatTimestamp({
              timestamp: offer?.orderStartDate,
              pattern: "MM/DD/YY",
            })}
          </WithStatusGuard>
        ),
      },
      {
        ...orderEndDate,
        value: offer => (
          <WithStatusGuard offer={offer}>
            {formatTimestamp({
              timestamp: offer?.orderEndDate,
              pattern: "MM/DD/YY",
            })}
          </WithStatusGuard>
        ),
      },
    ];

    const SHIP_TO_STORE_DATES = [
      {
        id: shipStartDate?.id,
        label: shipStartDate?.label,
        width: shipStartDate?.width,
        sortable: shipStartDate?.sortable,
        value: offer => (
          <WithStatusGuard offer={offer}>
            {formatTimestamp({
              timestamp: offer?.shipStartDate,
              pattern: "MM/DD/YY",
            })}
          </WithStatusGuard>
        ),
      },
      {
        id: shipEndDate?.id,
        label: shipEndDate?.label,
        width: shipEndDate?.width,
        sortable: shipEndDate?.sortable,
        value: offer => (
          <WithStatusGuard offer={offer}>
            {formatTimestamp({
              timestamp: offer?.shipEndDate,
              pattern: "MM/DD/YY",
            })}
          </WithStatusGuard>
        ),
      },
    ];

    const map = {
      TS: ALLOWANCE_DATE_COLS_SCAN,
      TC: ALLOWANCE_DATE_COLS_SCAN,
      CD: [...COMMON_ALLOW_DATE_COLS, ...ARRIVAL_DATE_COLS],
      CC: [...COMMON_ALLOW_DATE_COLS, ...ARRIVAL_DATE_COLS],
      CW: [...COMMON_ALLOW_DATE_COLS, ...ORDER_DATES_COLS, ...ARRIVAL_DATE_COLS],
      SD: [...COMMON_ALLOW_DATE_COLS, ...ARRIVAL_DATE_COLS, ...SHIP_TO_STORE_DATES],
      SB: [...COMMON_ALLOW_DATE_COLS, ...ARRIVAL_DATE_COLS, ...SHIP_TO_STORE_DATES],
    };

    return map[createInd] || COMMON_ALLOW_DATE_COLS;
  };

  const columns = useMemo(() => {
    const CHILD_OFFERS_COLUMNS: Column<any>[] = [
      {
        id: divisionId?.id,
        label: divisionId?.label,
        width: divisionId?.width,
        sortable: divisionId?.sortable,
        value: offer => {
          const { ALL_DIVISION_DATA } = appConstants;
          const divisions = ALL_DIVISION_DATA?.find(
            division => division?.divisionId === offer?.divisionIds?.join("")
          );
          const { divisionId, divisionName } = divisions || {};
          return (
            <WithStatusGuard offer={offer} isDisplayText={true}>
              {`${divisionId} - ${divisionName}` || "-"}
            </WithStatusGuard>
          );
        },
      },
      {
        id: "allowanceAmount",
        label: "Allowance Amount",
        width: "8vw",
        sortable: false,
        value: offer => (
          <WithStatusGuard
            offer={offer}
            hideCustomText={
              !offer?.includeInd
                ? appConstants?.EXCLUDED
                : offer?.allowanceStatus
            }
          >
            {getAmtLabel(offer)}
          </WithStatusGuard>
        ),
      },
    ];

    const OTHER_COLS: Column<any>[] = [
      {
        id: status?.id,
        label: status?.label,
        width: status?.width,
        sortable: status?.sortable,
        value: offer => (
          <WithStatusGuard offer={offer}>
            <ViewEventLink
              parentEventStatus={offer?.allowanceStatus}
              eventId={offer?.childEventId}
            />
          </WithStatusGuard>
        ),
      },
    ];

    return [...CHILD_OFFERS_COLUMNS, ...getAdditionalDatesCols(), ...OTHER_COLS];
  }, [cardIndex, gridConfig.columns, createInd]);

  return columns;
}

export default OfferDetailsColumnConfig;
