import {
  generateCommonPayLoadForDashBoard,
  getPerformanceDataForApi,
} from "./common-view-api-service";

jest.mock("react-pdf", () => ({
  Document: jest.fn(({ children }) => children),
  Page: jest.fn(() => null),
  pdfjs: {
    GlobalWorkerOptions: {
      workerSrc: "",
    },
    version: "2.10.377",
  },
}));

describe("generateCommonPayLoadForDashBoard", () => {
  it("should return null if globalHeaderData is missing or does not have all required properties", () => {
    const result = generateCommonPayLoadForDashBoard({
      globalHeaderData: null,
      viewLevelFilterData: {},
      paging: {},
      view: "Planning",
      propsFromGlobal: {
        required: ["startDate", "endDate"],
        optional: [],
      },
      performanceApiData: [], // Add this line
    });
    expect(result).toBeNull();
  });
it("should generate payload for Task view", () => {
  // Mock sessionStorage to provide a valid sessionSearchValue
  const mockSessionSearchValue = "value1,value2";
  const mockSessionSearchKey = "Event Name";

  jest.spyOn(Storage.prototype, "getItem").mockImplementation((key) => {
    if (key === "searchValue") return mockSessionSearchValue;
    if (key === "searchKey") return mockSessionSearchKey;
    return null;
  });

  const globalHeaderData = { startDate: "2022-01-01", endDate: "2022-12-31" };
  const viewLevelFilterData = {
    "event-status": [
      { name: "status1", checked: true },
      { name: "status2", checked: false },
    ],
  };

  const result = generateCommonPayLoadForDashBoard({
    globalHeaderData,
    viewLevelFilterData,
    paging: {},
    view: "Tasks",
    propsFromGlobal: {
      required: ["startDate", "endDate"],
      optional: [],
    },
    performanceApiData: [],
  });

  expect(result).toEqual({
    startDate: "2022-01-01",
    endDate: "2022-12-31",
    eventNames: ["value1", "value2"],
    eventStatusTypes: [
      "Draft",
      "Pending With Merchant",
      "Pending With Vendor",
      "Agreed",
      "Agreed-Pending",
      "Active",
      "Executed",
      "Rejected",
      "Canceled",
    ],
    isSearchEvent: true,
    nationalEventSearchFilters: [
      { parentEventName: "value1" },
      { parentEventName: "value2" },
    ],
    paging: {},
    performanceTypes: [],
    taskAlertStatusType: "Pending", // Added to match the actual payload
    taskStatusType: undefined, // Added to match the actual payload
    taskType: "Task", // Added to match the actual payload
    tasksSortType: undefined, // Added to match the actual payload
    userType: null,
    isAllGrpCatSelected: undefined, // Added to match the actual payload
  });

  // Restore the original sessionStorage behavior
  jest.restoreAllMocks();
});
it("should generate payload for Alert view", () => {
  // Mock sessionStorage to provide a valid sessionSearchValue
  const mockSessionSearchValue = "value1,value2";
  const mockSessionSearchKey = "Event Name";

  jest.spyOn(Storage.prototype, "getItem").mockImplementation((key) => {
    if (key === "searchValue") return mockSessionSearchValue;
    if (key === "searchKey") return mockSessionSearchKey;
    return null;
  });

  const globalHeaderData = { startDate: "2022-01-01", endDate: "2022-12-31" };
  const viewLevelFilterData = {
    "event-status": [
      { name: "status1", checked: true },
      { name: "status2", checked: false },
    ],
  };

  const result = generateCommonPayLoadForDashBoard({
    globalHeaderData,
    viewLevelFilterData,
    paging: {},
    view: "Alerts",
    propsFromGlobal: {
      required: ["startDate", "endDate"],
      optional: [],
    },
    performanceApiData: [],
  });

  expect(result).toEqual({
    startDate: "2022-01-01",
    endDate: "2022-12-31",
    eventNames: ["value1", "value2"],
    eventStatusTypes: [
      "Draft",
      "Pending With Merchant",
      "Pending With Vendor",
      "Agreed",
      "Agreed-Pending",
      "Active",
      "Executed",
      "Rejected",
      "Canceled",
    ],
    isSearchEvent: true,
    nationalEventSearchFilters: [
      { parentEventName: "value1" },
      { parentEventName: "value2" },
    ],
    paging: {},
    performanceTypes: [],
    taskAlertStatusType: "Pending", // Added to match the actual payload
    taskStatusType: undefined, // Added to match the actual payload
    taskType: "Alert", // Added to match the actual payload
    tasksSortType: undefined, // Added to match the actual payload
    userType: null,
    isAllGrpCatSelected: undefined, // Added to match the actual payload
  });

  // Restore the original sessionStorage behavior
  jest.restoreAllMocks();
});
it("should generate payload for Planning view", () => {
  // Mock sessionStorage to provide a valid sessionSearchValue
  const mockSessionSearchValue = "value1,value2";
  const mockSessionSearchKey = "Event Name";

  jest.spyOn(Storage.prototype, "getItem").mockImplementation((key) => {
    if (key === "searchValue") return mockSessionSearchValue;
    if (key === "searchKey") return mockSessionSearchKey;
    return null;
  });

  const globalHeaderData = { startDate: "2022-01-01", endDate: "2022-12-31" };
  const viewLevelFilterData = {
    "event-status": [
      { name: "status1", checked: true },
      { name: "status2", checked: false },
    ],
  };

  const result = generateCommonPayLoadForDashBoard({
    globalHeaderData,
    viewLevelFilterData,
    paging: {},
    view: "Planning",
    propsFromGlobal: {
      required: ["startDate", "endDate"],
      optional: [],
    },
    performanceApiData: [],
  });

  expect(result).toEqual({
    startDate: "2022-01-01",
    endDate: "2022-12-31",
    eventStatusTypes: ["status1"],
    paging: {},
    userType: null,
    isSearchEvent: true,
    performanceTypes: [],
    eventNames: ["value1", "value2"], // Updated to match the actual payload
    nationalEventSearchFilters: [
      { parentEventName: "value1" }, // Updated to match the actual payload
      { parentEventName: "value2" }, // Updated to match the actual payload
    ],
    planEventSortType: undefined, // Added to match the actual payload
    timeScaleFilterEnum: undefined, // Added to match the actual payload
  });

  // Restore the original sessionStorage behavior
  jest.restoreAllMocks();
});

it("should generate payload for Allowance view", () => {
  // Mock sessionStorage to provide a valid sessionSearchValue
  const mockSessionSearchValue = "value1,value2";
  const mockSessionSearchKey = "Event Name";

  jest.spyOn(Storage.prototype, "getItem").mockImplementation((key) => {
    if (key === "searchValue") return mockSessionSearchValue;
    if (key === "searchKey") return mockSessionSearchKey;
    return null;
  });

  const globalHeaderData = {
    startDate: "2022-01-01",
    endDate: "2022-12-31",
  };
  const viewLevelFilterData = {
    "event-status": [
      { name: "status1", checked: true },
      { name: "status2", checked: false },
    ],
  };
  const result = generateCommonPayLoadForDashBoard({
    globalHeaderData,
    viewLevelFilterData,
    paging: {},
    view: "Allowances",
    propsFromGlobal: {
      required: ["startDate", "endDate"],
      optional: [],
    },
    performanceApiData: [],
  });

  expect(result).toEqual({
    startDate: "2022-01-01",
    endDate: "2022-12-31",
    eventStatusTypes: [],
    paging: {},
    userType: null,
    isSearchEvent: true,
    performanceTypes: [],
    dateFilterType: "EVENT_DATES",
    eventNames: ["value1", "value2"], // Updated to match the actual payload
    nationalEventSearchFilters: [
      { parentEventName: "value1" }, // Updated to match the actual payload
      { parentEventName: "value2" }, // Updated to match the actual payload
    ],
    isAllGrpCatSelected: undefined, // Added to match the actual payload
    offerPlanEventProjectionType: undefined, // Added to match the actual payload
    timeScaleFilterEnum: undefined, // Added to match the actual payload
  });

  // Restore the original sessionStorage behavior
  jest.restoreAllMocks();
});
});

describe("getPerformanceDataForApi", () => {
  it("should return performance types based on globalHeaderData and performanceApiData", () => {
    const globalHeaderData = {
      performanceTypes: ["type1", "type2"],
      allowanceTypes: ["allowance1", "allowance2"],
    };
    const performanceApiData = [
      { id: 1, performance: "type1", allowanceType: "allowance1" },
      { id: 2, performance: "type2", allowanceType: "allowance2" },
      { id: 3, performance: "type3", allowanceType: "allowance3" },
    ];
    const result = getPerformanceDataForApi({
      globalHeaderData,
      performanceApiData,
    });
    expect(result).toEqual({
      performanceTypes: [1, 2],
    });
  });

  it("should return performance types based on globalHeaderData when allowanceTypes is missing", () => {
    const globalHeaderData = {
      performanceTypes: ["type1", "type2"],
    };
    const performanceApiData = [
      { id: 1, performance: "type1", allowanceType: "allowance1" },
      { id: 2, performance: "type2", allowanceType: "allowance2" },
      { id: 3, performance: "type3", allowanceType: "allowance3" },
    ];
    const result = getPerformanceDataForApi({
      globalHeaderData,
      performanceApiData,
    });
    expect(result).toEqual({
      performanceTypes: [1, 2],
    });
  });

  it("should return empty performance types when globalHeaderData is missing", () => {
    const globalHeaderData = {};
    const performanceApiData = [
      { id: 1, performance: "type1", allowanceType: "allowance1" },
      { id: 2, performance: "type2", allowanceType: "allowance2" },
      { id: 3, performance: "type3", allowanceType: "allowance3" },
    ];
    const result = getPerformanceDataForApi({
      globalHeaderData,
      performanceApiData,
    });
    expect(result).toEqual({
      performanceTypes: [],
    });
  });

  it("should return empty performance types when performanceApiData is empty", () => {
    const globalHeaderData = {
      performanceTypes: ["type1", "type2"],
      allowanceTypes: ["allowance1", "allowance2"],
    };
    const performanceApiData = [];
    const result = getPerformanceDataForApi({
      globalHeaderData,
      performanceApiData,
    });
    expect(result).toEqual({
      performanceTypes: [],
    });
  });
});
