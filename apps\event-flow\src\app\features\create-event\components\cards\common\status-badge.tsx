import * as React from "react";
import Tag from "@albertsons/uds/molecule/Tag";

interface IStatusBadgeProps {
  status?: string;
}

const StatusBadge: React.FunctionComponent<IStatusBadgeProps> = ({
  status,
}) => {
  const statusName = status?.toUpperCase();
  const getPreset = (statusName: string) => {
    let presetColor: any = {};
    switch (true) {
      case ["DRAFT", "AGREED"].includes(statusName):
        presetColor = {
          textColor: "#124C81",
          backgroundColor: "#EBF3FA",
          borderColor: "#C8DAEB",
        };
        break;
      case ["READY", "ACTIVE", "EXECUTED", "PROCESS"].includes(statusName):
        presetColor = {
          textColor: "#2D7207",
          backgroundColor: "#E0FBEC",
          borderColor: "#B1F1D0",
        };
        break;
      case ["REJECTED", "CANCELED"].includes(statusName):
        presetColor = {
          textColor: "#2B303C",
          backgroundColor: "#EDEEEF",
          borderColor: "#D2D6DB",
        };
        break;
      case [
        "PENDING WITH VENDOR",
        "PENDING WITH MERCHANT",
        "AGREED-PENDING",
      ].includes(statusName):
        presetColor = {
          textColor: "#AB4205",
          backgroundColor: "#FFF1D1",
          borderColor: "#FAD97F",
        };
        break;
    }
    return presetColor;
  };

  return (
    <div
      className="flex gap-1 mr-2 items-center ml-auto whitespace-pre"
      id="abs-status-badge-container1"
    >
      <Tag
        className="capitalize"
        label={statusName?.toLocaleLowerCase()}
        textColor={getPreset(statusName || "").textColor}
        backgroundColor={getPreset(statusName || "").backgroundColor}
        borderColor={getPreset(statusName || "").borderColor}
      />
    </div>
  );
};

export default StatusBadge;
