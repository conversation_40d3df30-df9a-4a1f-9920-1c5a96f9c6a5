import { getDateByFormat } from "@me-upp-js/utilities";
import efConstants from "../../../../shared/ef-constants/ef-constants";
import {
  checkIsPerfChangable,
  formatAmount,
  getAllowanceMapKey,
  getValuetrimmed,
  isHfOrIfType,
  updateAllowanceFormValues,
} from "./allowance-service";
import { getFromSessionStorage } from "../../../../shared/helpers/event-flow-helpers";

const {
  ALLOWANCE_SCREEN_TYPES,
  ALLOWANCE_TYPES,
  OFFER_ALLOWANCE_GROUP,
  ITEM_AMOUNTS_SUMMARIZED_KEY,
} = efConstants;

interface IStepperCountObj {
  allowanceScreenType: string;
  allowanceType: string;
  allowMapKeys: string[] | [];
  createInd?: string;
}

export const getObjectKeys = (obj: object) => {
  return Object.keys(obj || {});
};

export const getObjectValues = (obj: object) => {
  return Object.values(obj || {});
};

//Test Input: allowanceScreenType = "DP", allowanceType = "SCAN", allowMapKeys:["DSD_WHSE_RETAIL_DIVISION"]
export function getStepperCount(stepperInfoObj: IStepperCountObj) {
  return getStepsByAllowanceType(stepperInfoObj);
}

export function getStepsByAllowanceType(stepperInfoObj: IStepperCountObj) {
  const { allowanceType } = stepperInfoObj;

  switch (allowanceType) {
    case ALLOWANCE_TYPES.CASE.key:
      return getCaseStepsByAllowanceScreenType(stepperInfoObj);

    case ALLOWANCE_TYPES.SCAN.key:
    case ALLOWANCE_TYPES.SHIPTOSTORE.key:
      return getS2SStepsByAllowanceScreenType(stepperInfoObj);

    case ALLOWANCE_TYPES.HEADERFLAT.key:
      return getHfStepsByAllowanceScreenType(stepperInfoObj);

    case ALLOWANCE_TYPES.ITEMFLAT.key:
      return getIfStepsByAllowanceScreenType(stepperInfoObj);

    default:
      return 0;
  }
}

export function getCaseStepsByAllowanceScreenType(
  stepperInfoObj: IStepperCountObj
) {
  const { allowanceScreenType, allowanceType, allowMapKeys } = stepperInfoObj;

  switch (allowanceScreenType) {
    case ALLOWANCE_SCREEN_TYPES.DP.key:
    case ALLOWANCE_SCREEN_TYPES.AO.key:
      return getCaseStepCountByAllowMapKeys(allowMapKeys, allowanceType);

    default:
      return 0;
  }
}

export function getCaseStepCountByAllowMapKeys(
  allowMapKeys: string[] | [],
  allowanceType: string
) {
  if (allowMapKeys.length === 1) return 5;
  else if (getConditionByAllowMapKeys(allowMapKeys, allowanceType)) return 8;
  return 5;
}

export function getS2SStepsByAllowanceScreenType(
  stepperInfoObj: IStepperCountObj
) {
  const { allowanceScreenType } = stepperInfoObj;

  switch (allowanceScreenType) {
    case ALLOWANCE_SCREEN_TYPES.DP.key:
      return 5;

    case ALLOWANCE_SCREEN_TYPES.AO.key:
      return 6;

    default:
      return 0;
  }
}

export function getHfStepsByAllowanceScreenType(
  stepperInfoObj: IStepperCountObj
) {
  const { allowanceScreenType } = stepperInfoObj;

  switch (allowanceScreenType) {
    case ALLOWANCE_SCREEN_TYPES.DP.key:
      return 3;

    case ALLOWANCE_SCREEN_TYPES.AO.key:
      return 4;

    default:
      return 0;
  }
}

export function getIfStepsByAllowanceScreenType(
  stepperInfoObj: IStepperCountObj
) {
  const { allowanceScreenType, createInd = "" } = stepperInfoObj;

  switch (allowanceScreenType) {
    case ALLOWANCE_SCREEN_TYPES.DP.key:
      return 4;

    case ALLOWANCE_SCREEN_TYPES.AO.key:
      return createInd === ALLOWANCE_TYPES.ITEMFLAT.createInd[1] ? 4 : 5;

    default:
      return 0;
  }
}

export function getConditionByAllowMapKeys(
  allowMapKeys: string[] | [],
  allowanceType: string
) {
  const keys: string[] | [] = Object.values(
    OFFER_ALLOWANCE_GROUP?.[allowanceType] || {}
  );
  return (
    allowMapKeys.length === 2 &&
    allowMapKeys.includes(keys?.[0]) &&
    allowMapKeys.includes(keys?.[1])
  );
}

export const setOverrideHeaderFlatAmt = ({
  allowanceTypeSpecificationAllow,
  reloadInfo,
}) => {
  const { allowanceType, overrideHeaderFlatAmt, createInd } =
    allowanceTypeSpecificationAllow;
  if (allowanceType && overrideHeaderFlatAmt >= 0 && createInd) {
    return setAllowanceTypeAndPerformance({
      allowanceTypeSpecificationAllow,
      reloadInfo,
      isHeaderFlat: true,
    });
  }
  return null;
};

export const setAllowanceTypeAndPerformance = async ({
  allowanceTypeSpecificationAllow,
  reloadInfo,
  isHeaderFlat = false,
}) => {
  const {
    allowanceType,
    allowancePerformanceId,
    productSources = [],
    allowancesMap = {},
    createInd = "",
  } = allowanceTypeSpecificationAllow;
  const { getPerformanceById, fetchAllowanceToBeCreated } = reloadInfo || {};
  if (allowanceType && allowancePerformanceId) {
    getPerformanceById(allowanceTypeSpecificationAllow || {});
    isHfOrIfType(allowanceType) &&
      (await fetchAllowanceToBeCreated(
        null,
        allowanceType,
        productSources,
        [],
        createInd
      ));
    return isHeaderFlat || getObjectKeys(allowancesMap).length;
  }
  return null;
};

export const setStoreSelection = async ({
  allowanceTypeSpecificationAllow,
  reloadInfo,
  isEditMode,
}) => {
  const { overrideStoreGroups } = allowanceTypeSpecificationAllow;
  const { storeGroupId, storeGroupName, storeGroupType } =
    overrideStoreGroups?.[0] || {};

  if (storeGroupId && storeGroupName && storeGroupType) {
    setStoreSelectionFormFieldsValue(
      overrideStoreGroups,
      reloadInfo,
      isEditMode
    );
    return true;
  }
  return null;
};

const setStoreSelectionFormFieldsValue = (
  overrideStoreGroups,
  reloadInfo,
  isEditMode
) => {
  const { allowanceRegisterField, getValues, setValue } = reloadInfo || {};
  const { storeGrpTypeName, groupType, groupInd } =
    overrideStoreGroups?.[0]?.storeGroupType || {};
  const allowanceStoreSelection = {
    groupInd,
    storeGroupType: { name: storeGrpTypeName, groupType, groupInd },
    storeGroups: overrideStoreGroups.map(value => {
      return {
        id: value?.storeGroupId,
        name: isEditMode
          ? `${value?.storeGroupName} (${overrideStoreGroups?.[0]?.storeCount} Stores)`
          : value?.storeGroupName,
      };
    }),
  };

  updateAllowanceFormValues({
    getValues,
    allowanceRegField: allowanceRegisterField,
    setValue,
    updateFormValuesObj: { allowanceStoreSelection },
  });
};

export const setAllowanceToBeCreated = async ({
  allowanceTypeSpecificationAllow,
  reloadInfo,
  step,
}) => {
  const {
    allowanceType,
    allowancesMap,
    productSources,
    createInd,
    offerAllowancesGroupInfoMap = {},
  } = allowanceTypeSpecificationAllow;
  const allowanceMapKeys = getObjectKeys(allowancesMap);
  await reloadInfo?.fetchAllowanceToBeCreated(
    step - 1,
    allowanceType,
    productSources,
    allowanceMapKeys,
    createInd,
    offerAllowancesGroupInfoMap
  );
  return allowanceMapKeys.length;
};

export const setDefaultAllowanceVehicleDates = async ({
  allowanceTypeSpecificationAllow,
  reloadInfo,
}) => {
  const { allowancesMap = {}, vehicleDatesMap = {} } =
    allowanceTypeSpecificationAllow;
  const offerKeys = getObjectKeys(allowancesMap);
  const vehicleDates = vehicleDatesMap?.[offerKeys[0]] || {};
  if (getObjectKeys(vehicleDates).length) {
    setDatesFormFieldsValue(vehicleDates, reloadInfo, offerKeys[0]);
    return true;
  }
  return null;
};

export const setDatesFormFieldsValue = (vehicleDates, reloadInfo, offerKey) => {
  const { allowanceRegisterField, setValue, getValues } = reloadInfo || {};
  let dateRange = vehicleDates?.dateRange;
  dateRange = {
    startDate: getDateByFormat(dateRange.startDate, "YYYY-MM-DD"),
    endDate: getDateByFormat(dateRange.endDate, "YYYY-MM-DD"),
  };
  vehicleDates = { ...vehicleDates, dateRange };
  updateAllowanceFormValues({
    getValues,
    allowanceRegField: allowanceRegisterField,
    setValue,
    updateFormValuesObj: {
      allowanceCreationVehicle: {
        [offerKey]: vehicleDates,
      },
    },
  });
};

export const setAllowanceVehicleAndOffsetDates = async ({
  allowanceTypeSpecificationAllow,
  reloadInfo,
  step = 3,
  stepCount = 5,
  isEditMode,
}) => {
  const { allowanceRegisterField, setValue, getValues } = reloadInfo || {};
  const allowanceMapKeys = getObjectKeys(
    allowanceTypeSpecificationAllow?.allowancesMap
  )?.sort();
  const offerKey =
    stepCount <= 6 || step <= 3 ? allowanceMapKeys?.[0] : allowanceMapKeys?.[1];

  const { vehicleDatesMap = {}, allowancesMap = {} } =
    allowanceTypeSpecificationAllow;
  let vehicleDates = vehicleDatesMap?.[offerKey] || {};
  if (getObjectKeys(vehicleDates).length) {
    let dateRange = vehicleDates?.dateRange;
    dateRange = {
      startDate: getDateByFormat(dateRange.startDate, "YYYY-MM-DD"),
      endDate: getDateByFormat(dateRange.endDate, "YYYY-MM-DD"),
    };
    vehicleDates = { ...vehicleDates, dateRange };
    const vendorsObject = allowancesMap?.[offerKey];

    updateAllowanceFormValues({
      getValues,
      allowanceRegField: allowanceRegisterField,
      setValue,
      updateFormValuesObj: {
        allowanceCreationVehicle: {
          ...getValues(`${allowanceRegisterField}.allowanceCreationVehicle`),
          [offerKey]: {
            ...vehicleDates,
            vendorsObject,
          },
        },
      },
    });
    if (!isEditMode && vendorsObject?.length) return true;
    else if (isEditMode) return true;
  }
  return null;
};

export const setHfAlowanceAmount = async ({
  allowanceTypeSpecificationAllow,
  reloadInfo,
  isEditMode = false,
}) => {
  const { allowancesMap = {} } = allowanceTypeSpecificationAllow || {};

  const { allowanceRegisterField, setValue, cancelState, getValues } =
    reloadInfo || {};
  const { isCancelled, offerKey } = cancelState;
  const allowanceMapKeys = getObjectKeys(allowancesMap);
  const allowKey = allowanceMapKeys?.[0];

  const { headerFlatAmt, finalizedAmountsInd = false } =
    allowancesMap?.[allowKey]?.[0] || {};

  if (!isEditMode && !finalizedAmountsInd) {
    return null;
  }
  const headerFlatAmtValue = Number(headerFlatAmt);
  const isAmountValid = !isNaN(headerFlatAmtValue) && headerFlatAmtValue >= 0;
  if (isAmountValid && (isEditMode || !isCancelled || offerKey !== allowKey)) {
    setHfAmountFormFieldsValue(
      allowanceRegisterField,
      setValue,
      getValues,
      allowKey,
      allowanceTypeSpecificationAllow?.allowancesMap?.[allowKey],
      headerFlatAmtValue
    );
    return true;
  }
  return null;
};

const setHfAmountFormFieldsValue = (
  allowanceRegisterField,
  setValue,
  getValues,
  key,
  headerFlatAmts,
  headerFlatAmt
) => {
  updateAllowanceFormValues({
    getValues,
    allowanceRegField: allowanceRegisterField,
    setValue,
    updateFormValuesObj: {
      allowanceAmountsData: {
        [key]: { headerFlatAmtItems: headerFlatAmts, headerFlatAmt },
      },
    },
  });
};

export const setDefaultAllowanceAmount = async ({
  allowanceTypeSpecificationAllow,
  reloadInfo,
  step = 3,
  stepCount = 5,
  isEditMode = false,
}) => {
  const { allowancesMap = {} } = allowanceTypeSpecificationAllow || {};

  const { allowanceRegisterField, setValue, getValues, cancelState } =
    reloadInfo || {};
  const { isCancelled, offerKey } = cancelState;
  const allowanceMapKeys = getObjectKeys(allowancesMap)?.sort();
  const allowKey =
    stepCount <= 6 || step <= 4 ? allowanceMapKeys?.[0] : allowanceMapKeys?.[1];

  const { allowanceItems, finalizedAmountsInd = false } =
    allowancesMap?.[allowKey]?.[0] || {};

  if (!isEditMode && !finalizedAmountsInd) {
    return null;
  }

  if (
    allowanceItems?.length &&
    (isEditMode || !isCancelled || offerKey !== allowKey)
  ) {
    setAmountFormFieldsValue(
      allowanceRegisterField,
      setValue,
      getValues,
      allowKey,
      allowanceItems,
      allowancesMap?.[allowKey]
    );
    return true;
  }
  return null;
};

const setAmountFormFieldsValue = (
  allowanceRegisterField,
  setValue,
  getValues,
  key,
  allowanceItems,
  allowances
) => {
  updateAllowanceFormValues({
    getValues,
    allowanceRegField: allowanceRegisterField,
    setValue,
    updateFormValuesObj: {
      allowanceAmountsData: {
        ...getValues(`${allowanceRegisterField}.allowanceAmountsData`),
        [key]: {
          allowanceAmount:
            formatAmount(allowanceItems?.[0]?.allowanceAmount?.toString()) ||
            "",
          uom: allowanceItems?.[0]?.allowUomType || "",
          allowanceItems,
          allowances,
        },
      },
    },
  });
};

export const setDefaultAllowanceBillingInfo = async ({
  allowanceTypeSpecificationAllow,
  reloadInfo,
  step = 4,
  stepCount = 5,
  isEditMode,
}) => {
  const { allowanceRegisterField, getValues, setValue, eventId, saveState } =
    reloadInfo || {};
  const allowanceMapKeys = getObjectKeys(
    allowanceTypeSpecificationAllow?.allowancesMap
  );
  const { isSaved, offerKey } = saveState || [];
  const allowKey =
    stepCount <= 6 || step >= 6 ? allowanceMapKeys?.[0] : allowanceMapKeys?.[1];

  const { allowanceBillingInfo } =
    allowanceTypeSpecificationAllow?.allowancesMap?.[allowKey]?.[0] || {};

  if (
    getObjectKeys(allowanceBillingInfo).length &&
    allowanceBillingInfo?.suggestedVendorPaymentType !== undefined &&
    (isEditMode || !isSaved || offerKey !== allowKey)
  ) {
    const allowanceBillingInfoList =
      allowanceTypeSpecificationAllow?.allowancesMap?.[allowKey].map(allow => {
        const {
          suggestedVendorPaymentType = "",
          suggestedAcPayableVendorNbr = "",
          suggestedAcReceivableVendorNbr = "",
        } = allow?.allowanceBillingInfo || {};
        return {
          ...allow?.allowanceBillingInfo,
          acApOrArNumber:
            suggestedVendorPaymentType === "Deduct"
              ? suggestedAcPayableVendorNbr
              : suggestedAcReceivableVendorNbr,
        };
      });
    setBillingFieldsValue(
      allowanceRegisterField,
      getValues,
      setValue,
      allowKey,
      allowanceBillingInfo,
      allowanceBillingInfoList,
      eventId,
      isEditMode
    );
    return true;
  }

  return null;
};

const setBillingFieldsValue = (
  allowanceRegisterField,
  getValues,
  setValue,
  key,
  allowanceBillingInfo,
  allowanceBillingInfoList,
  eventId,
  isEditMode
) => {
  const {
    absMerchVendor,
    absVendorName,
    absVendorPaymentType,
    acPayableVendorNbr,
    acReceivableVendorNbr,
    billingContactName = "",
    billingContactEmail = "",
    vendorComment = "",
    vendorOfferTrackingNbr = "",
  } = allowanceBillingInfo;

  if (!absMerchVendor) return null;

  updateAllowanceFormValues({
    getValues,
    allowanceRegField: allowanceRegisterField,
    setValue,
    updateFormValuesObj: {
      billingInformationData: {
        ...getValues(`${allowanceRegisterField}.billingInformationData.${key}`),
        [key]: {
          absMerchVendor: getValuetrimmed(absMerchVendor),
          absVendorName: getValuetrimmed(absVendorName),
          absVendorPaymentType,
          acPayableVendorNbr: getValuetrimmed(acPayableVendorNbr),
          acReceivableVendorNbr: getValuetrimmed(acReceivableVendorNbr),
          billingContactName,
          billingContactEmail,
          vendorComment,
          vendorOfferTrackingNbr: getValuetrimmed(vendorOfferTrackingNbr),
          allowanceBillingInfo: allowanceBillingInfoList,
        },
      },
    },
  });

  return null;
};

const getCaseConfigSteps = () => {
  return {
    5: [
      setAllowanceTypeAndPerformance,
      setAllowanceToBeCreated,
      setAllowanceVehicleAndOffsetDates,
      setDefaultAllowanceAmount,
      setDefaultAllowanceBillingInfo,
    ],
    8: [
      setAllowanceTypeAndPerformance,
      setAllowanceToBeCreated,
      setAllowanceVehicleAndOffsetDates,
      setDefaultAllowanceAmount,
      setDefaultAllowanceBillingInfo,
      setAllowanceVehicleAndOffsetDates,
      setDefaultAllowanceAmount,
      setDefaultAllowanceBillingInfo,
    ],
  };
};

const getScanDefaultDpSteps = () => {
  return [
    setAllowanceTypeAndPerformance,
    setAllowanceToBeCreated,
    setDefaultAllowanceVehicleDates,
    setDefaultAllowanceAmount,
    setDefaultAllowanceBillingInfo,
  ];
};

const getScanDefaultAoSteps = () => {
  return [
    setAllowanceTypeAndPerformance,
    setStoreSelection,
    setAllowanceToBeCreated,
    setDefaultAllowanceVehicleDates,
    setDefaultAllowanceAmount,
    setDefaultAllowanceBillingInfo,
  ];
};

const getS2SDefaultDpSteps = () => {
  return [
    setAllowanceTypeAndPerformance,
    setAllowanceToBeCreated,
    setAllowanceVehicleAndOffsetDates,
    setDefaultAllowanceAmount,
    setDefaultAllowanceBillingInfo,
  ];
};

const getS2SDefaultAoSteps = () => {
  return [
    setAllowanceTypeAndPerformance,
    setStoreSelection,
    setAllowanceToBeCreated,
    setAllowanceVehicleAndOffsetDates,
    setDefaultAllowanceAmount,
    setDefaultAllowanceBillingInfo,
  ];
};

const getHfDefaultDpSteps = () => {
  return [
    setOverrideHeaderFlatAmt,
    setDefaultAllowanceVehicleDates,
    setDefaultAllowanceBillingInfo,
  ];
};

const getHfDefaultAoSteps = (createInd?: string) => {
  if (createInd === ALLOWANCE_TYPES.HEADERFLAT.createInd[1]) {
    return [
      setAllowanceTypeAndPerformance,
      setDefaultAllowanceVehicleDates,
      setHfAlowanceAmount,
      setDefaultAllowanceBillingInfo,
    ];
  }
  return [
    setOverrideHeaderFlatAmt,
    setStoreSelection,
    setDefaultAllowanceVehicleDates,
    setDefaultAllowanceBillingInfo,
  ];
};

const getIfDefaultDpSteps = () => {
  return [
    setAllowanceTypeAndPerformance,
    setDefaultAllowanceVehicleDates,
    setDefaultAllowanceAmount,
    setDefaultAllowanceBillingInfo,
  ];
};

const getIfDefaultAoSteps = (createInd?: string) => {
  if (createInd === ALLOWANCE_TYPES.ITEMFLAT.createInd[1]) {
    return { 4: getIfDefaultDpSteps() };
  }
  return {
    5: [
      setAllowanceTypeAndPerformance,
      setStoreSelection,
      setDefaultAllowanceVehicleDates,
      setDefaultAllowanceAmount,
      setDefaultAllowanceBillingInfo,
    ],
  };
};

const getStepsConfig = (
  allowanceScreenType: string,
  allowanceType: string,
  stepCount: number,
  createInd?: string
) => {
  const stepsConfig = {
    [ALLOWANCE_SCREEN_TYPES.DP.key]: {
      [ALLOWANCE_TYPES.CASE.key]: getCaseConfigSteps(),
      [ALLOWANCE_TYPES.SCAN.key]: {
        5: getScanDefaultDpSteps(),
      },
      [ALLOWANCE_TYPES.SHIPTOSTORE.key]: {
        5: getS2SDefaultDpSteps(),
      },
      [ALLOWANCE_TYPES.HEADERFLAT.key]: { 3: getHfDefaultDpSteps() },
      [ALLOWANCE_TYPES.ITEMFLAT.key]: { 4: getIfDefaultDpSteps() },
    },
    [ALLOWANCE_SCREEN_TYPES.AO.key]: {
      [ALLOWANCE_TYPES.CASE.key]: getCaseConfigSteps(),
      [ALLOWANCE_TYPES.SCAN.key]: {
        6: getScanDefaultAoSteps(),
      },
      [ALLOWANCE_TYPES.SHIPTOSTORE.key]: {
        6: getS2SDefaultAoSteps(),
      },
      [ALLOWANCE_TYPES.HEADERFLAT.key]: { 4: getHfDefaultAoSteps(createInd) },
      [ALLOWANCE_TYPES.ITEMFLAT.key]: getIfDefaultAoSteps(createInd),
    },
  };

  return stepsConfig?.[allowanceScreenType]?.[allowanceType]?.[stepCount] || [];
};

const setFormValueAndGetCurrentStep = async (
  stepsConfig,
  allowanceTypeSpecificationAllow,
  reloadInfo = {},
  stepCount: number,
  isEditMode = false
) => {
  for (let index = 0; index <= stepsConfig?.length; index++) {
    const step = index + 1;
    const data = await stepsConfig?.[index]?.({
      allowanceTypeSpecificationAllow,
      reloadInfo,
      step,
      stepCount,
      isEditMode,
    });
    if (!data || stepCount === step) return step;
  }
  return 0;
};

export const setStepperOnEditMode = (
  allowanceScreenType: string,
  allowanceType: string,
  createInd: string,
  isInvalidDate?: boolean
) => {
  if (isHfOrIfType(allowanceType)) {
    return allowanceScreenType === ALLOWANCE_SCREEN_TYPES.AO.key &&
      [
        ALLOWANCE_TYPES.ITEMFLAT.createInd[1],
        ALLOWANCE_TYPES.HEADERFLAT.createInd[1],
      ].includes(createInd)
      ? 2
      : isInvalidDate
        ? allowanceScreenType === ALLOWANCE_SCREEN_TYPES.AO.key
          ? 3
          : 2
        : 1;
  } else if (
    allowanceScreenType === ALLOWANCE_SCREEN_TYPES.AO.key &&
    allowanceType === ALLOWANCE_TYPES.CASE.key
  ) {
    return isInvalidDate ? 3 : 1;
  }
  return allowanceScreenType === ALLOWANCE_SCREEN_TYPES.AO.key && isInvalidDate
    ? 4
    : allowanceScreenType === ALLOWANCE_SCREEN_TYPES.DP.key && isInvalidDate
      ? 3
      : 1;
};

export async function saveFormAndGetStepperValue(
  allowanceScreenType: string,
  allowanceTempWorkDTO,
  reloadInfo,
  isEditMode = false,
  isInvalidDate = false,
  isComingFromTaskView?
) {
  const allowanceType: string = allowanceTempWorkDTO?.allowanceType || "";

  const allowanceTypeAllow = getAllowanceMapKey(allowanceType);
  const isItemsSummarized =
    getFromSessionStorage(ITEM_AMOUNTS_SUMMARIZED_KEY) === "true";

  if (allowanceScreenType && allowanceTempWorkDTO?.allowanceTypeSpecification) {
    const {
      allowanceTypeSpecification: {
        [allowanceTypeAllow]: allowanceTypeSpecificationAllow,
      },
    } = allowanceTempWorkDTO;

    if (allowanceTypeSpecificationAllow) {
      const allowMapKeys: string[] | [] = getObjectKeys(
        allowanceTypeSpecificationAllow?.allowancesMap
      );
      const createInd = allowanceTypeSpecificationAllow?.createInd || "";

      const stepCount = getStepperCount({
        allowanceScreenType,
        allowanceType,
        allowMapKeys,
        createInd,
      });

      const stepsConfig = getStepsConfig(
        allowanceScreenType,
        allowanceType,
        stepCount,
        allowanceTypeSpecificationAllow?.createInd
      );

      let currentStep = await setFormValueAndGetCurrentStep(
        stepsConfig,
        allowanceTypeSpecificationAllow,
        reloadInfo,
        stepCount,
        isEditMode
      );
      const isPerfChangable = checkIsPerfChangable(createInd);

      if (isComingFromTaskView) {
        currentStep = setStepperOnEditMode(
          allowanceScreenType,
          allowanceType,
          createInd,
          isInvalidDate || isComingFromTaskView
        );
        return currentStep;
      } else if (isEditMode && !reloadInfo?.saveState?.isSaved) {
        currentStep = setStepperOnEditMode(
          allowanceScreenType,
          allowanceType,
          createInd,
          isInvalidDate
        );
      } else if (!isEditMode && reloadInfo?.cancelState?.isCancelled) {
        currentStep = isItemsSummarized ? currentStep : currentStep - 1;
      }
      if (isEditMode && reloadInfo?.cancelState?.isCancelled) {
        currentStep =
          isHfOrIfType(allowanceType) && !isPerfChangable
            ? currentStep + (isItemsSummarized ? 1 : 0)
            : currentStep +
            (isItemsSummarized &&
              allowanceScreenType === ALLOWANCE_SCREEN_TYPES.AO.key
              ? 3
              : 2);
      }

      return currentStep <= 0 ? 0 : currentStep - 1;
    }
  }
  return 0;
}
