import { useGetAppBasePath } from "@me/util-helpers";
import classNames from "classnames";
import efConstants from "../../../../shared/ef-constants/ef-constants";

const icons = {
  PLANNING: "circle_filled_8x8",
  DRAFT: "circle_transparent_8x8",
  "PENDING WITH VENDOR": "diamond_8x8",
  "PENDING WITH MERCHANT": "alert-triangle_16x16",
  AGREED: "nopa_transparent_8x8",
  APPROVED: "square_filled_8x8",
  EXECUTED: "square_transparent_8x8",
  READY: "square_filled_8x8",
  ACTIVE: "square_filled_8x8",
  REJECTED: "circle_slashed_8x8",
  CANCELED: "cancelled_slashed_8x8",
};

interface IEventStatusBadgeProps {
  event?: any;
}

const EventStatusBadge: React.FunctionComponent<IEventStatusBadgeProps> = ({
  event,
}) => {
  const { baseDomain } = useGetAppBasePath();
  const eventName = event.toUpperCase();
  const srcPath = `${baseDomain}assets/icons/8x8/${icons[eventName]}.svg`;

  const eventDetailsClassName = classNames({
    "text-[16px] font-semibold capitalize": true,
    "text-[#1b6ebb]": ["DRAFT", "AGREED"].includes(eventName),
    "text-[#9b3e08]": ["PENDING WITH VENDOR", "PENDING WITH MERCHANT"].includes(
      eventName
    ),
    "text-[#384250]": ["REJECTED", "CANCELED"].includes(eventName),
    "text-[#105f0e]": ["READY", "ACTIVE", "EXECUTED"].includes(eventName),
  });

  return (
    <div
      className={`flex gap-1 items-center ${efConstants.componentClassName.EVENT_STATUS_BADGE}`}
    >
      <img src={srcPath} className="h-[10px] w-[10px]" alt="event-status-icon" />
      <span className={eventDetailsClassName}>{eventName.toLowerCase()}</span>
    </div>
  );
};

export default EventStatusBadge;
