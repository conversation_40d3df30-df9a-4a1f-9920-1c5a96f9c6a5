/*
 * Imports
 * move interfaces to seperate file
 * store functions - (selectors)
 * API calls declaration
 * constants
 * useState variables - try to combine and reduce number of declarations
 * useEffect
 * functions
 * Return UI field elements - create multiple functions to render fields
 */

import { useSelectorWrap } from "@me/data-rtk";
import { useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import LoadingSpinner from "../../../../../constants/LoadingSpinner/LoadingSpinner";
import useAllowanceTempUpdate from "../../../../../hooks/useAllowanceTempUpdate";
import { useHandleAllowanceToBeCraetedOptions } from "../../../../../hooks/useHandleAllowanceCreatedOptions";
import {
  checkIsPerfChangable,
  formatAmount,
  formatTimestampToDate,
  getAllowanceCaseTypeValue,
  getAllowanceFormRegisterKey,
  getAllowance<PERSON>ey,
  getAllowance<PERSON>ap<PERSON>ey,
  getAllowancePerfOption,
  getUniqueItems,
  isHfOrIfType,
  saveAllowanceFormData,
  setProductSourceError,
  getCreateIndByLocation,
} from "../../../../../service/allowance/allowance-service";
import { IOption } from "../../../../fields/allowance-atoms/input-select";
import {
  InputSelectAtom,
  InputTextAtom as InputText,
} from "../../../../fields/index";
import AllowanceFormWrapper, {
  IFormControls,
} from "../../../common/allowance-form-wrapper";
import {
  IAllowanceTypePerformaceProps,
  IAllowanceTypePerformanceOption,
  IPerformanceProps,
} from "./allowance-type-performance.model";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";
import {
  usePostAllowanceToBeCreatedMutation,
  usePostOfferCreateUpdateMutation,
} from "../../../../../service/apis/allowance-api";
import {
  allowancePerfConfigHandler,
  allowanceProductSources,
  resetAllowanceStepSkipInfo,
  setAllowanceFormInfo,
  setAllowTypeChange,
} from "../../../../../service/slice/allowance-details-slice";
import { ALLOWANCE_TO_BE_CREATED_CD_MAPPER } from "../../../../../constants/fields/allowance/allowance-steps-config";
import { validateAmount } from "../../../../../hooks/allowance-amount-validations";
import { CommonModal } from "../../../common";
import { isAllowanceFeatureEnabled } from "@me-upp-js/utilities";
import { allowanceTempWorkHandler } from "../../../../../service/slice/allowance-temp-work-slice";
import { EEVENT_STATUS } from "@me/util-helpers";

const AllowanceTypePerformace = ({
  offerIndex,
  allowanceIndex,
  saveAndContinueHanlder,
  allowanceTypeHandler,
  stepperElement,
  step,
  isEditEnable,
}: IAllowanceTypePerformaceProps) => {
  const dispatch = useDispatch();
  const { data } = useSelectorWrap("allowance_form_data");
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { data: allowancePerfData } = useSelectorWrap(
    "allowance_type_performance_data"
  );
  const { data: allowanceTypeWithPerfData } = useSelectorWrap(
    "allowance_type_data"
  );
  const { productSources = [] } =
    useSelectorWrap("allowance_product_sources_rn")?.data || {};
  const {
    data: { apiErrorsMsgs: rtkErrorsArr, showTokenError = false },
  } = useSelectorWrap("apiError_rn");
  const { allowanceData } = useSelectorWrap("allowance_temp_work").data;
  const { isAllowanceTypeChanged } = useSelectorWrap(
    "allow_type_change_rn"
  ).data;
  const isAllowanceTypeUpdated = useRef(isAllowanceTypeChanged?.[offerIndex]);

  const {
      ALLOWANCE_TYPES,
      PRODUCT_SOURCE_INFO,
      ALLOWANCE_SCREEN_TYPES,
      BOTH_KEY,
      ALLOWANCE_TYPE_EDIT_MESSAGES,
    } = efConstants,
    { CASE, HEADERFLAT, ITEMFLAT, SCAN } = ALLOWANCE_TYPES,
    { AO, DP, NDP } = ALLOWANCE_SCREEN_TYPES,
    { HEADER_FLAT, ITEM_FLAT, CASE: CASE_SOURCE } = PRODUCT_SOURCE_INFO,
    {
      id,
      eventType,
      divisionIds,
      eventCreationVehicle,
      planProductGroups,
      eventStatus,
    } = eventDetailsData || {};
  const allowanceStatus =
    eventDetailsData?.offerAllowances?.[offerIndex]?.allowances[allowanceIndex]
      ?.allowanceStatus;

  const { addAllowanceTypeAndPerformanceId, isLoading } =
    useAllowanceTempUpdate();

  const { isPostEventDataloading, handleAllowanceToBeCreatedOptions } =
    useHandleAllowanceToBeCraetedOptions();

  const [
    postEventDetailsData,
    {
      isLoading: isProductSourceDataloading,
      data: productSourcesData,
      isError: isProductSourceError,
    },
  ] = usePostAllowanceToBeCreatedMutation();
  const [postOfferCreateUpdate, { isLoading: isUpdateLoading }] =
    usePostOfferCreateUpdateMutation();

  const {
    fields: {
      allowanceType: allowanceTypeField,
      performance: performanceField,
      allowanceAmount: allowanceAmountField,
      allowanceTypeAndPerformance,
    },
    create: { label: createLabel },
    edit: { label: editLabel },
    disable: { allowanceType: allowanceTypeInfo },
  } = stepperElement;

  const constRegField = getAllowanceFormRegisterKey(offerIndex, allowanceIndex);
  const allowanceFormData = data?.allowanceFormData?.[constRegField];

  const {
    allowanceType: allowanceTypeDefaultValue,
    performance: performanceDefaultValue,
    allowancePerformances,
    overrideHeaderFlatAmt: headerFlatAmtDefaultValue,
    createInd,
  } = allowanceFormData?.[allowanceTypeAndPerformance.registerKeyName] || {};

  const defaultValues = {
    allowanceType: getAllowanceCaseTypeValue(allowanceTypeDefaultValue),
    performance: performanceDefaultValue,
    overrideHeaderFlatAmt: formatAmount(headerFlatAmtDefaultValue),
  };

  const isPerfChangable = checkIsPerfChangable(createInd);

  const [formControls, setFormControls] = useState<IFormControls>();
  const {
    register = () => null,
    control,
    setValue = () => null,
    reset = (values?: object) => null,
    clearErrors = () => null,
    setError = () => null,
    getValues = () => null,
  } = formControls || {};

  const [showPopup, setShowPopup] = useState({
    optionData: {},
  });
  const [showModal, setShowModal] = useState(false);

  const allowanceTypeName = useRef("");
  const count = useRef(0);
  const [defaultPerfConfig, setDefaultPerfConfig] = useState({
    defaultCreateInd: "",
    allowancePerformanceChildId: "",
    allowOnlyOverrideStoreGroupsInd: false,
  });

  const [allowanceTypeOptions, setAllowanceTypeOptions] = useState<
    IAllowanceTypePerformanceOption[]
  >([]);
  const [performanceValue, setperformanceValue] = useState<IPerformanceProps>({
    allowanceCd: "",
    perfCode1: "",
    perfCode2: "",
    payType: "",
    id: "",
  });
  const [performanceOptions, setPerformanceOptions] = useState<
    { id?: string; performance?: string; item?: object }[]
  >([]);
  const [perfOptionsByEventType, setPerfOptionsByEventType] = useState([]);
  const [isProductSourceEmpty, setIsProductSourcesEmpty] = useState(false);
  const [digitError, setDigitError] = useState("");

  const createIndByLocation = getCreateIndByLocation(
    eventDetailsData?.offerAllowances?.[offerIndex],
    allowanceIndex
  );
  const allowCdMapper =
    ALLOWANCE_TO_BE_CREATED_CD_MAPPER?.[createIndByLocation];

  useEffect(() => {
    allowanceFormData && setFormInitialData();
  }, [allowanceFormData]);

  useEffect(() => {
    const perfDefaultConfig =
      allowancePerfData?.performanceConfig?.[
        isEditEnable ? `${constRegField}.edit` : constRegField
      ];
    perfDefaultConfig && setDefaultPerfConfig(perfDefaultConfig);
  }, [allowancePerfData?.performanceConfig]);

  useEffect(() => {
    allowanceTypeWithPerfData && getAllowanceTypeOptions();
  }, [JSON.stringify(allowanceTypeWithPerfData)]);

  useEffect(() => {
    setPerfOptionsByEventType(
      allowancePerfData?.getAllowancePerformanceTypes?.filter(
        ({ eventTypes, deleteInd }) =>
          eventTypes?.includes(eventType) && deleteInd !== "Y"
      ) || []
    );
  }, [allowancePerfData]);

  useEffect(() => {
    if (
      allowanceTypeWithPerfData &&
      allowanceTypeName.current &&
      perfOptionsByEventType?.length &&
      !isProductSourceDataloading &&
      !count.current
    ) {
      count.current = 1;
      if (productSources?.length) {
        setPerfOptionsByAllowanceType(productSources, performanceDefaultValue);
      } else {
        setPerformanceOptions(getPerfOptionsByAllowanceType());
        setValue(performanceField.registerField, performanceDefaultValue);
      }
    }
  }, [
    allowanceTypeWithPerfData,
    perfOptionsByEventType,
    allowanceTypeName.current,
  ]);

  const setFormInitialData = () => {
    allowanceTypeName.current = getAllowanceCaseTypeValue(
      allowanceTypeDefaultValue
    );
    setperformanceValue(allowancePerformances);
    reset(defaultValues);
  };

  const saveProductSourcesDataToSlice = (value: string[] | [] = []) => {
    dispatch(allowanceProductSources({ productSources: value }));
  };

  useEffect(() => {
    if (isProductSourceError) {
      saveProductSourcesDataToSlice();
      setPerformanceOptions([]);
    }
  }, [isProductSourceError]);

  useEffect(() => {
    if (
      productSourcesData &&
      productSourcesData?.[0]?.productSources?.length === 0
    ) {
      setProductSourceError(dispatch, rtkErrorsArr, showTokenError);
      setIsProductSourcesEmpty(true);
    } else if (productSourcesData?.[0]?.productSources?.length) {
      setIsProductSourcesEmpty(false);
      const uniqueps = getUniqueItems(productSourcesData, "productSources");
      saveProductSourcesDataToSlice(uniqueps);
      setPerfOptionsByAllowanceType(uniqueps);
    }
  }, [productSourcesData]);

  const getAllowanceTypeOptions = () => {
    const allowanceTypeFilteredData =
      allowanceTypeWithPerfData?.getAllowancePerformancesByEventType?.map(
        ele => {
          const allowanceType = ele?.allowanceType || "";
          return {
            id: allowanceType,
            name: allowanceType,
            allowanceCd: ele?.allowancePerformances?.[0]?.allowanceCd || "",
          };
        }
      ) || [];
    const filteredAllowTypeByDisplayValue = filterAllowTyeBasedOnDisplayType(
      allowanceTypeFilteredData
    );
    setAllowanceTypeOptions(filteredAllowTypeByDisplayValue);
  };
  const filterAllowTyeBasedOnDisplayType = allowTypeData => {
    if (planProductGroups?.length && allowTypeData) {
      const isAnyDisplayType = planProductGroups?.some(
        promoObj => promoObj?.displayInd
      );
      return isAnyDisplayType
        ? filterScanFromAllowType(allowTypeData)
        : allowTypeData;
    }
    return allowTypeData;
  };

  const filterScanFromAllowType = allowTypeData => {
    return allowTypeData?.filter(
      allowTypeObj => allowTypeObj?.id !== SCAN.label
    );
  };

  const getDisabledPerfOptions = (perfOptions: object[] | [] = []) => {
    return perfOptions?.map(perfOption => {
      return {
        ...perfOption,
        disabled: isAllowanceTypeUpdated.current
          ? false
          : !(
              perfOption?.["performanceConfig"]?.defaultCreateInd === createInd
            ),
      };
    });
  };

  const getSortedPerfOptions = (perfOptions: object[] | [] = []) => {
    let updatedPerfOptions =
      isEditEnable &&
      isPerfChangable &&
      isHfOrIfType(getAllowanceKey(defaultValues.allowanceType?.toUpperCase()))
        ? getDisabledPerfOptions(perfOptions)
        : perfOptions;

    const prooductInfo =
      productSourcesData?.[0]?.productSources || productSources;
    const isCaseLabel = CASE.label === allowanceTypeName.current;
    const isSingleProductDSD =
      prooductInfo?.length === 1 && prooductInfo[0] === CASE_SOURCE.DSD.key;

    const modifiedCreateInd = isCaseLabel
      ? isEditEnable && createInd === CASE.createInd[0]
        ? CASE.createInd[2]
        : isSingleProductDSD || createIndByLocation === CASE.createInd[1]
        ? CASE.createInd[1]
        : CASE.createInd[2]
      : createInd;

    updatedPerfOptions =
      CASE.label === allowanceTypeName.current &&
      (isEditEnable || prooductInfo?.length === 1)
        ? updatedPerfOptions?.filter(
            option =>
              option?.performanceConfig?.defaultCreateInd === modifiedCreateInd
          )
        : updatedPerfOptions;
    return updatedPerfOptions?.sort((a: object, b: object) =>
      a?.["performance"]?.localeCompare(b?.["performance"])
    );
  };

  const getPerfOptionsByAllowanceType = () => {
    const perfOptions =
      perfOptionsByEventType?.filter(
        ({ allowanceType = "" }) => allowanceType === allowanceTypeName.current
      ) || [];
    return getSortedPerfOptions(perfOptions);
  };

  const getCreateInd = (productKey: string) => {
    return PRODUCT_SOURCE_INFO?.[
      getAllowanceKey(allowanceTypeName.current?.toUpperCase())
    ]?.[productKey]?.createIndex;
  };

  const getProductSourceData = async () => {
    setPerformanceOptions([]);
    const response = await postEventDetailsData({
      URL_PARAM: id,
      queryParams: {
        responseFilter: "allowMin",
      },
      division: divisionIds,
      promoStartDate: formatTimestampToDate(eventCreationVehicle?.startDate),
    });
    if (response?.error) {
      setValue(performanceField.registerField, "");
    }
  };

  const setPerfOptionsByAllowanceType = async (
    productSourcesInfo?: string[] | [],
    perfOptionValue?: string
  ) => {
    if (
      [HEADERFLAT.label, ITEMFLAT.label].includes(allowanceTypeName.current) &&
      eventType === AO.key &&
      productSourcesInfo?.length === 1 &&
      productSourcesInfo[0] === ITEM_FLAT.DSD.key
    ) {
      const productKey = productSourcesInfo[0];
      const options = perfOptionsByEventType?.filter(
        option =>
          option?.["performanceConfig"]?.["defaultCreateInd"] ===
          getCreateInd(productKey)
      );
      setPerformanceOptions(getSortedPerfOptions(options));
      !perfOptionValue && setDefaultPerfOption({}, productKey);
    } else {
      let productKey = "";
      if (
        CASE.label === allowanceTypeName.current &&
        productSourcesInfo?.length
      ) {
        productKey = isEditEnable
          ? allowCdMapper?.offerKey
          : productSourcesInfo?.length === 1
          ? productSourcesInfo[0]
          : BOTH_KEY;
      }
      setPerformanceOptions(getPerfOptionsByAllowanceType());
      !perfOptionValue && setDefaultPerfOption({}, productKey);
    }
  };

  const getPerformanceOptionByName = (perfOptions, perfOptionName: string) => {
    const selectedPerfOption =
      perfOptions?.filter(
        option => option?.["performance"] === perfOptionName
      ) || [];

    if (selectedPerfOption.length) {
      const allowancePerformances = selectedPerfOption[0];
      return {
        performanceObj: {
          allowanceCd: allowancePerformances?.["allowanceCd"] || "",
          perfCode1: allowancePerformances?.["perfCode1"] || "",
          perfCode2: allowancePerformances?.["perfCode2"] || "",
          payType: allowancePerformances?.["payType"] || "",
          id: allowancePerformances?.["id"] || "",
        },
        perfName: allowancePerformances?.["performance"],
        perfConfig: allowancePerformances?.["performanceConfig"],
      };
    }
    return { performanceObj: null, perfName: "", perfConfig: {} };
  };

  const setDefaultPerfOption = (
    { id = "", allowanceCd = "" },
    productKey: string
  ) => {
    let defaultPerfObj: IPerformanceProps = {
      allowanceCd: allowanceCd,
      perfCode1: "",
      perfCode2: "",
      payType: id,
      id: "",
    };
    let perfLabel = "";
    const perfOptions = getPerfOptionsByAllowanceType();
    const { performanceObj, perfName, perfConfig } = getPerformanceOptionByName(
      perfOptions,
      getAllowancePerfOption(
        allowanceTypeName.current.toUpperCase(),
        eventType,
        getCreateInd(productKey)
      )
    );
    defaultPerfObj = performanceObj || defaultPerfObj;
    perfLabel = perfName || "";
    setDefaultPerfConfig(perfConfig);
    savePerfConfig(perfConfig);
    setValue(performanceField.registerField, perfLabel);
    perfLabel && clearErrors([performanceField.registerField]);
    setperformanceValue(defaultPerfObj);
  };

  const savePerfConfig = (value: object) => {
    dispatch(
      allowancePerfConfigHandler({
        performanceConfig: { [constRegField]: value },
      })
    );
  };

  const handlePerfOptions = () => {
    count.current = 1;
    if (
      [HEADERFLAT.label, ITEMFLAT.label, CASE.label].includes(
        allowanceTypeName.current
      )
    ) {
      getProductSourceData();
    } else {
      setPerformanceOptions(getPerfOptionsByAllowanceType());
      setDefaultPerfOption({}, "");
    }
  };

  const handlePerFormanceOptionChange = (option: IOption) => {
    const allowanceValue = (option?.["name"] as string) || "";
    setValue("isAllowanceChanged", true);
    setValue(allowanceTypeField.registerField, allowanceValue);
    allowanceTypeName.current = allowanceValue;
    setIsProductSourcesEmpty(false);
    dispatch(resetAllowanceStepSkipInfo());
    allowanceTypeHandler(
      allowanceValue ? getAllowanceKey(allowanceValue?.toUpperCase()) : "",
      ""
    );
    handlePerfOptions();
  };

  const handleAllowanceTypeChange = (option: IOption) => {
    if (isEditEnable && isAllowanceFeatureEnabled) {
      setShowPopup({
        optionData: option,
      });
      setShowModal(true);
    } else {
      handlePerFormanceOptionChange(option);
    }
  };

  const handlePerformanceChange = (option: IOption) => {
    if (option) {
      const {
        allowanceCd,
        perfCode1,
        perfCode2,
        payType,
        performance,
        performanceConfig,
      } = option as {
        allowanceCd: "";
        perfCode1: "";
        perfCode2: "";
        payType: "";
        performance: "";
        performanceConfig: {
          defaultCreateInd: "";
          allowOnlyOverrideStoreGroupsInd: false;
          allowancePerformanceChildId: "";
        };
      };
      const performanceObj = {
        allowanceCd: allowanceCd,
        perfCode1: perfCode1,
        perfCode2: perfCode2,
        payType: payType,
        id: option?.["id"] as string,
      };
      setValue(performanceField.registerField, performance);
      setDefaultPerfConfig(performanceConfig);
      savePerfConfig(performanceConfig);
      setperformanceValue(performanceObj);
    } else {
      const defPerfConfig = {
        defaultCreateInd: "",
        allowancePerformanceChildId: "",
        allowOnlyOverrideStoreGroupsInd: false,
      };
      setValue(performanceField.registerField, null);
      setDefaultPerfConfig(defPerfConfig);
      savePerfConfig(defPerfConfig);
      setperformanceValue({
        allowanceCd: "",
        perfCode1: "",
        perfCode2: "",
        payType: "",
        id: "",
      });
    }
  };

  const getFormControls = (controls: IFormControls) => {
    setFormControls(controls);
  };

  const handleSave = async formValues => {
    const { allowanceType, performance, overrideHeaderFlatAmt } = formValues;
    const allowanceKey = getAllowanceKey(allowanceType?.toUpperCase());
    const allowancePerformanceObj = {
      ...performanceValue,
      id: performanceValue?.id || "",
      performance: performance,
      allowanceType: allowanceType,
      performanceConfig: defaultPerfConfig,
    };
    let headerFlatAmt: number | undefined = undefined;
    const createIndValue =
      isEditEnable && !isAllowanceTypeChanged?.[offerIndex]
        ? createInd
        : defaultPerfConfig?.defaultCreateInd;
    const isHfOrIf = isHfOrIfType(allowanceKey);
    const mappedCreateInd = isEditEnable
      ? allowCdMapper
      : isHfOrIf
      ? ALLOWANCE_TO_BE_CREATED_CD_MAPPER?.[createIndValue]
      : {};
    const allowancesMap = isEditEnable
      ? {
          [mappedCreateInd?.key]: allowanceData?.allowanceTypeSpecification?.[
            getAllowanceMapKey(allowanceKey) || ""
          ]?.allowancesMap?.[mappedCreateInd?.key]?.map(item => {
            return { ...item, headerFlatAmt: overrideHeaderFlatAmt };
          }),
        }
      : isHfOrIf
      ? { [mappedCreateInd?.key]: [] }
      : {};

    let isAmountValid = false;
    if (checkAmountFieldCondition()) {
      headerFlatAmt = Number(overrideHeaderFlatAmt);
      if (headerFlatAmt === 0) {
        return;
      }

      isAmountValid = !isNaN(headerFlatAmt) && headerFlatAmt >= 0;

      if (!isAmountValid) {
        setError(allowanceAmountField.registerField, {
          message: allowanceAmountField?.errors?.formatError?.text,
        });
        return;
      }
    }

    const allowAndPerfData = {
      allowanceType: allowanceKey,
      performance,
      allowancePerformances: allowancePerformanceObj,
      overrideHeaderFlatAmt: isAmountValid ? overrideHeaderFlatAmt : null,
      allowancesMap,
      createInd: createIndValue,
      productSources: productSources,
      perfChildInfo:
        !isEditEnable &&
        defaultPerfConfig?.defaultCreateInd === CASE.createInd[2]
          ? allowancePerfData?.getAllowancePerformanceTypes?.filter(
              item =>
                item?.id === defaultPerfConfig?.allowancePerformanceChildId
            )
          : [],
    };

    saveAllowanceFormData({
      dispatch,
      key: allowanceTypeAndPerformance.registerKeyName,
      allowanceRegField: constRegField,
      value: allowAndPerfData,
      isPreviousDataRequired: isEditEnable,
      previousData: allowanceFormData,
    });

    const result = await addAllowanceTypeAndPerformanceId({
      isEditEnable,
      allowanceType: allowanceKey,
      allowancePerformances: allowancePerformanceObj,
      overrideHeaderFlatAmt: headerFlatAmt,
      allowancesMap,
      createInd: createIndValue,
      productSources: productSources,
    });

    if (result?.data) {
      if ([HEADERFLAT.label, ITEMFLAT.label].includes(allowanceType)) {
        const nextStep =
          isEditEnable &&
          eventType === AO.key &&
          [HEADER_FLAT.DSD.createIndex, ITEM_FLAT.DSD.createIndex].includes(
            defaultPerfConfig?.defaultCreateInd
          )
            ? step + 1
            : step;
        saveAndContinueHanlder(nextStep);
        return;
      }
      if (isEditEnable && !isAllowanceTypeChanged?.[offerIndex]) {
        const nextStep =
          eventType === AO.key && CASE.key !== allowanceKey
            ? step + 2
            : step + 1;
        saveAndContinueHanlder(nextStep);
        return;
      }
      await handleAllowanceToBeCreatedOptions({
        allowanceRegField: constRegField,
        allowanceTypeName: allowanceKey,
        step,
        allowanceTypeInfo,
        allowanceTypeHandler,
        saveAndContinueHanlder,
        previousFormData: { allowanceTypeAndPerformance: allowAndPerfData },
        updatedAllowanceData: result?.data,
      });
      return;
    }
  };

  const checkAmountFieldCondition = () => {
    return (
      allowanceTypeName.current === HEADERFLAT.label &&
      ([DP.key, NDP.key].includes(eventType) ||
        (eventType === AO.key &&
          defaultPerfConfig?.defaultCreateInd === HEADER_FLAT.DSD.createIndex))
    );
  };

  function onAmountChange(e) {
    const amount = validateAmount(e);
    amount && Number(amount) === 0
      ? setDigitError(allowanceAmountField?.errors?.digitError?.text)
      : setDigitError("");
    setValue(allowanceAmountField.registerField, amount);
  }

  const onPopupCloseHandler = () => {
    setShowPopup({ optionData: {} });
    isAllowanceFeatureEnabled &&
      dispatch(
        setAllowTypeChange({ isAllowanceTypeChanged: { [offerIndex]: false } })
      );
    isAllowanceTypeUpdated.current = false;
    setValue(allowanceTypeField.registerField, allowanceTypeName.current);
    setShowModal(false);
  };

  const onChangeHandler = async () => {
    const allowanceType = getAllowanceKey(
      getValues()?.allowanceType?.toUpperCase()
    );
    saveProductSourcesDataToSlice();

    const updatedOfferData = await postOfferCreateUpdate({
      URL_PARAMS: [
        eventDetailsData?.offerAllowances?.[offerIndex]?.offerNumber,
        allowanceType,
      ],
      allowanceType,
      offerNumber: eventDetailsData?.offerAllowances?.[offerIndex]?.offerNumber,
      overrideOfferNumbers: [
        eventDetailsData?.offerAllowances?.[offerIndex]?.offerNumber,
      ],
    });

    if (updatedOfferData?.data) {
      isAllowanceFeatureEnabled &&
        dispatch(
          setAllowTypeChange({
            isAllowanceTypeChanged: { [offerIndex]: true },
          })
        );
      isAllowanceTypeUpdated.current = true;
      setValue(allowanceAmountField.registerField, "");
      dispatch(
        allowanceTempWorkHandler({
          allowanceData: updatedOfferData?.data,
        })
      );
      dispatch(
        setAllowanceFormInfo({
          allowanceFormData: {},
        })
      );
      handlePerFormanceOptionChange(showPopup?.optionData);
    }
  };

  const isAllowTypeEditable = isEditEnable
    ? ![eventStatus, allowanceStatus].includes(EEVENT_STATUS.DRAFT) &&
      isAllowanceFeatureEnabled &&
      isPerfChangable
    : true;

  const allowanceTypeFieldContent = allowanceTypeField && (
    <div
      className="w-3/12"
      id="abs-allowance-type-performance-input-allowance-type-field"
    >
      <InputSelectAtom
        fieldProps={allowanceTypeField}
        register={register}
        control={control}
        options={allowanceTypeOptions}
        displayLabel={allowanceTypeField.displayLabel}
        disabled={!allowanceTypeOptions.length || !isAllowTypeEditable}
        onChange={handleAllowanceTypeChange}
      />
    </div>
  );

  const performanceFieldContent = performanceField && (
    <div
      className="w-4/12"
      id="abs-allowance-type-performance-input-performance-field"
    >
      <InputSelectAtom
        fieldProps={performanceField}
        register={register}
        control={control}
        options={performanceOptions}
        displayLabel={performanceField.displayLabel}
        disabled={
          !performanceOptions.length || (isEditEnable && !isPerfChangable)
        }
        onChange={handlePerformanceChange}
      />
    </div>
  );

  const allowanceAmountFieldContent = allowanceAmountField && (
    <div
      className="w-3/12"
      id="abs-allowance-type-performance-allowance-amount-field"
    >
      <InputText
        fieldProps={allowanceAmountField}
        register={register}
        control={control}
        onWheel={event => event.currentTarget.blur()}
        onChange={onAmountChange}
        onFocus={(value: string) => {
          setValue(allowanceAmountField.registerField, value);
        }}
        prefixValue={"$"}
        error={{ message: digitError }}
      />
    </div>
  );

  const bodyContent = (
    <div
      className="flex gap-3"
      id="abs-allowance-type-performance-loading-spinner-container"
    >
      {showModal ? (
        <CommonModal
          isModalPopupOpen={showModal}
          setModalPopupOpen={setShowModal}
          // title={ALLOWANCE_TYPE_EDIT_MESSAGES?.title}
          infoMessage={ALLOWANCE_TYPE_EDIT_MESSAGES?.INFO}
          confirmBtnTitle={ALLOWANCE_TYPE_EDIT_MESSAGES?.CONFIRM_BTN_TITLE}
          cancelBtnTitle={ALLOWANCE_TYPE_EDIT_MESSAGES?.CANCEL_BTN_TITLE}
          showHideBtns={true}
          height={255}
          minHeight={255}
          onClose={onPopupCloseHandler}
          cancelBtnHandler={onPopupCloseHandler}
          modalNameHandler={onChangeHandler}
          minBtnWidth={150}
        />
      ) : null}
      <LoadingSpinner
        classname="!h-full !w-full rounded-md"
        isLoading={
          isPostEventDataloading || isProductSourceDataloading || isLoading
        }
      />
      {allowanceTypeFieldContent}
      {performanceFieldContent}
      {checkAmountFieldCondition() && allowanceAmountFieldContent}
    </div>
  );

  const footerSubContent = isEditEnable && (
    <p
      className="text-sm text-left text-[#9D2210] my-[14px] italic"
      id="abs-allowance-type-performance-Allowance-type-text"
    >
      {isPerfChangable
        ? !isAllowTypeEditable && "Allowance type cannot be changed."
        : "Chosen Performance is not changeable.  Cancel this Offer and submit a new one if you need a different Performance Code for this Offer"}
    </p>
  );

  return (
    <AllowanceFormWrapper
      defaultValues={defaultValues}
      handleSave={handleSave}
      getFormControls={getFormControls}
      footerProps={{
        label: isEditEnable ? editLabel : createLabel,
        visable:
          !isEditEnable || isPerfChangable || checkAmountFieldCondition(),
        disabled: isProductSourceEmpty,
      }}
    >
      <>
        {control && bodyContent}
        {footerSubContent}
      </>
    </AllowanceFormWrapper>
  );
};

export default AllowanceTypePerformace;
