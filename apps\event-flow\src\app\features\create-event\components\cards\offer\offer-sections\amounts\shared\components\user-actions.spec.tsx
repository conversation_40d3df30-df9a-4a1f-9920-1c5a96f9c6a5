import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import userEvent from "@testing-library/user-event";
import UserActions from "./user-actions";
import { Provider } from "react-redux";
import { app_store } from "@me/data-rtk";

describe("UserActions Component", () => {
  const mockOnClick = jest.fn();
  const baseId = "testBaseId";

  const actionsConfig = {
    onClick: mockOnClick,
    sectionConfiguration: { create: { label: "Create" }, edit: { label: "Save" } },
    isEditEnable: false,
    isFormDirty: false,
    baseId: baseId,
    isHideFieldsForMainEntry: false,
    isSectionCompleted: false,
    isInValidOffer: false,
  };

  test("renders Edit/View All Items button", () => {
    render(<Provider store={app_store}><UserActions actionsConfig={actionsConfig} /></Provider>);
    expect(screen.getByText("Edit / View All Items")).toBeInTheDocument();
  });

  test("calls onClick when Edit/View All Items button is clicked", async () => {
    render(<Provider store={app_store}><UserActions actionsConfig={actionsConfig} /></Provider>);
    const editViewAllButton = screen.getByText("Edit / View All Items");

    await userEvent.click(editViewAllButton);
    expect(mockOnClick).toHaveBeenCalled();
  });

  test("renders Save button with 'Create' label when isEditEnable is false", () => {
    render(<Provider store={app_store}><UserActions actionsConfig={actionsConfig} /></Provider>);
    expect(screen.getByText("Create")).toBeInTheDocument();
  });

  test("renders Save button with 'Save' label when isEditEnable is true and form dirty", () => {
    render(<Provider store={app_store}><UserActions actionsConfig={{ ...actionsConfig, isEditEnable: true, isFormDirty: true }} /></Provider>);
    expect(screen.getByText("Save")).toBeInTheDocument();
  });

  test("Save button is disabled when isDisable logic is met", () => {
    render(<Provider store={app_store}><UserActions actionsConfig={{ ...actionsConfig, isEditEnable: false, isFormDirty: false }} /></Provider>);
    const saveButton = screen.getByText("Create");
    expect(saveButton.closest("button")).toBeDisabled();
  });

  test("Save button is enabled when isFormDirty is true", () => {
    render(<Provider store={app_store}><UserActions actionsConfig={{ ...actionsConfig, isEditEnable: false, isFormDirty: true }} /></Provider>);
    const saveButton = screen.getByText("Create");
    expect(saveButton.closest("button")).toBeEnabled();
  });
});
