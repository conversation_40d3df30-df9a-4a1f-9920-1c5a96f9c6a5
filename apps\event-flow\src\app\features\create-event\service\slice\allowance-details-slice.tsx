import { createGenericSlice } from "@me/data-rtk";
import {
  DEFAULT_OFFER_SECTIONS,
  DEFAULT_OFFER_SECTIONS_ENABLE_CONFIG,
} from "../../components/cards/offer/offer-flow-config";

export const offerAmountsDetails = createGenericSlice({
  name: "offer_amounts_details",
  initialState: {
    status: "loading",
    data: {
      offerAmounts: {},
      isAdditionalDatesChanged: {},
    },
  },
})({
  setOfferAmontsData(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
  setIsAdditionalDatesChnaged(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
  resetOfferAmountsData(state) {
    state.data = {
      offerAmounts: {},
      isAdditionalDatesChanged: {},
    };
  },
});
export const {
  setOfferAmontsData,
  setIsAdditionalDatesChnaged,
  resetOfferAmountsData,
} = offerAmountsDetails.actions;

export const offerSectionsEnableConfig = createGenericSlice({
  name: "offer_sections_enable_config",
  initialState: {
    status: "loading",
    data: DEFAULT_OFFER_SECTIONS_ENABLE_CONFIG,
  },
})({
  setOfferSectionsEnableConfig(state, { payload }) {
    state.data = { ...payload };
  },
  resetOfferSectionsEnableConfig(state) {
    state.data = DEFAULT_OFFER_SECTIONS_ENABLE_CONFIG;
  },
});
export const { setOfferSectionsEnableConfig, resetOfferSectionsEnableConfig } =
  offerSectionsEnableConfig.actions;

export const offerSectionsDetailsSlice = createGenericSlice({
  name: "offer_sections_data",
  initialState: {
    status: "loading",
    data: DEFAULT_OFFER_SECTIONS,
  },
})({
  setOfferSectionsData(state, { payload }) {
    state.data = payload;
  },
  resetOfferSectionsData(state) {
    state.data = DEFAULT_OFFER_SECTIONS;
  },
});
export const { setOfferSectionsData, resetOfferSectionsData } =
  offerSectionsDetailsSlice.actions;

export const allowanceDetailDataSlice = createGenericSlice({
  name: "allowance_details_data",
  initialState: {
    status: "loading",
    data: {},
  },
})({
  allowanceDetailDataHandler(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});
export const { allowanceDetailDataHandler } = allowanceDetailDataSlice.actions;

export const allowanceDetailConfigurationSlice = createGenericSlice({
  name: "allowance_details_configuration",
  initialState: {
    status: "loading",
    data: {},
  },
})({
  allowanceDetailConfiguration(state, { payload }) {
    state.data = { payload };
  },
});
export const { allowanceDetailConfiguration } =
  allowanceDetailConfigurationSlice.actions;

export const nationalOfferDivisionsData = createGenericSlice({
  name: "national_offer_divisions",
  initialState: {
    status: "loading",
    data: {
      eventDivisions: [],
      deletedDivisions: [],
      offerDivisions: [],
      offerDivisonErrorData: [],
    },
  },
})({
  setOfferDivisionsData(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
  resetOfferDivisionsData(state) {
    state.data = {
      eventDivisions: state.data?.eventDivisions || [],
      deletedDivisions: [],
      offerDivisions: [],
      offerDivisonErrorData: [],
    };
  },
});
export const { setOfferDivisionsData, resetOfferDivisionsData } =
  nationalOfferDivisionsData.actions;

//Allowance offer details Slice.
export const allowanceOfferDetailsConfigurationSlice = createGenericSlice({
  name: "allowance_offer_details_configuration",
  initialState: {
    status: "loading",
    data: {},
  },
})({
  allowanceOfferDetailsConfiguration(state, { payload }) {
    state.data = { payload };
  },
});
export const { allowanceOfferDetailsConfiguration } =
  allowanceOfferDetailsConfigurationSlice.actions;

export const offerCardConfigurationSlice = createGenericSlice({
  name: "offer_card_configutation_rn",
  initialState: {
    status: "loading",
    data: { editCardConfig: {}, openCardConfig: {}, offerData: "" },
  },
})({
  offerCardConfiguration(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const { offerCardConfiguration } = offerCardConfigurationSlice.actions;

export const offerSubCardConfigurationSlice = createGenericSlice({
  name: "offer_sub_card_configutation_rn",
  initialState: {
    status: "loading",
    data: { offerSubCardConfig: {} },
  },
})({
  setOfferSubCardConfiguration(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const { setOfferSubCardConfiguration } =
  offerSubCardConfigurationSlice.actions;

export const promoSubCardConfigurationSlice = createGenericSlice({
  name: "promo_sub_card_configutation_rn",
  initialState: {
    status: "loading",
    data: { promoSubCardConfig: {} },
  },
})({
  setPromoSubCardConfiguration(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const { setPromoSubCardConfiguration } =
  promoSubCardConfigurationSlice.actions;

export const promoCardConfigurationSlice = createGenericSlice({
  name: "promo_card_configutation_rn",
  initialState: {
    status: "loading",
    data: { editCardConfig: {}, openCardConfig: {}, isAddNewPromo: {} },
  },
})({
  promoCardConfiguration(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const { promoCardConfiguration } = promoCardConfigurationSlice.actions;

export const allowanceNewCardConfigurationSlice = createGenericSlice({
  name: "allowance_new_card_configuration_rn",
  initialState: {
    status: "loading",
    data: {},
  },
})({
  allowanceNewCardConfiguration(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
  allowanceNewCardConfigurationUnset(state) {
    state.data = {};
  },
  addRemoveNewAllowance(state, { payload }) {
    state.data = { ...state.data, isAddAnotherOffer: payload };
  },
});

export const {
  allowanceNewCardConfiguration,
  allowanceNewCardConfigurationUnset,
  addRemoveNewAllowance,
} = allowanceNewCardConfigurationSlice.actions;

export const allowanceStepperAdditionalDetailsSlice = createGenericSlice({
  name: "allowance_stepper_additional_details",
  initialState: {
    status: "loading",
    data: {},
  },
})({
  allowanceStepSkipInfo(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
  resetAllowanceStepSkipInfo(state) {
    state.data = {};
  },
});

export const { allowanceStepSkipInfo, resetAllowanceStepSkipInfo } =
  allowanceStepperAdditionalDetailsSlice.actions;

export const allowanceFormDataSlice = createGenericSlice({
  name: "allowance_form_data",
  initialState: {
    status: "loading",
    data: {},
  },
})({
  setAllowanceFormInfo(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
  allowanceFormReset(state) {
    state.data = {};
  },
});
export const { setAllowanceFormInfo, allowanceFormReset } =
  allowanceFormDataSlice.actions;

export const allowanceAmountsModalDetails = createGenericSlice({
  name: "allowance_amount_modal_details",
  initialState: {
    status: "loading",
    data: { isOpen: false, modalData: null },
  },
})({
  onModalToggle(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const overlappingAllowanceAmountsModal = createGenericSlice({
  name: "overlapping_allowance_amount_modal",
  initialState: {
    status: "loading",
    data: { isOpen: false, modalData: null },
  },
})({
  onModalToggleOverlapping(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const { onModalToggleOverlapping } =
  overlappingAllowanceAmountsModal.actions;

export const { onModalToggle } = allowanceAmountsModalDetails.actions;

// Allowance edit tempwork
export const allowanceEditDetailsDataSlice = createGenericSlice({
  name: "allowance_edit_tempwork_rn",
  initialState: {
    status: "loading",
    data: {},
  },
})({
  allowanceEditDetailsData(state, { payload }) {
    state.data = { ...state.data, ...payload };
    state.status = "finished";
  },
});

export const { allowanceEditDetailsData } =
  allowanceEditDetailsDataSlice.actions;
export const allowanceOnlyEventTypeConfigurationSlice = createGenericSlice({
  name: "allowance_only_event_type_configuration",
  initialState: {
    status: "loading",
    data: { isAllowanceOnlyEventType: false },
  },
})({
  allowanceOnlyEventTypeConfiguration(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const { allowanceOnlyEventTypeConfiguration } =
  allowanceOnlyEventTypeConfigurationSlice.actions;

// Allowance Delte tempwork
export const allowanceDeleteDetailsDataSlice = createGenericSlice({
  name: "allowance_delete_tempwork_rn",
  initialState: {
    status: "loading",
    data: {},
  },
})({
  allowanceDeleteDetailsData(state, { payload }) {
    state.data = { ...payload };
    state.status = "finished";
  },
});

export const { allowanceDeleteDetailsData } =
  allowanceDeleteDetailsDataSlice.actions;

export const allowanceProductSourcesSlice = createGenericSlice({
  name: "allowance_product_sources_rn",
  initialState: {
    status: "loading",
    data: { productSources: [] },
  },
})({
  allowanceProductSources(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const { allowanceProductSources } = allowanceProductSourcesSlice.actions;

export const getAllowanceTypePerformance = createGenericSlice({
  name: "allowance_type_performance_data",
  initialState: {
    status: "loading",
    data: {},
  },
})({
  allowanceTypeAndPerformanceDataHandler(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
  allowancePerfConfigHandler(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});
export const {
  allowanceTypeAndPerformanceDataHandler,
  allowancePerfConfigHandler,
} = getAllowanceTypePerformance.actions;

export const getAllowanceTypeData = createGenericSlice({
  name: "allowance_type_data",
  initialState: {
    status: "loading",
    data: {},
  },
})({
  saveAllowanceTypeDataHandler(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});
export const { saveAllowanceTypeDataHandler } = getAllowanceTypeData.actions;

export const getResendOffer = createGenericSlice({
  name: "offer_resend_status_rn",
  initialState: {
    status: "loading",
    data: {
      isResendOfferSuccess: false,
    },
  },
})({
  offerResendStatus(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});
export const { offerResendStatus } = getResendOffer.actions;

export const offerSectionUpdate = createGenericSlice({
  name: "offer_section_update",
  initialState: {
    status: "loading",
    data: {
      isOfferSectionsUpdated: false,
      isPrimeSectionSectionUpdated: false,
      isAmountsSectionUpdated: false,
    },
  },
})({
  setIsOfferSectionUpdated(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
  resetIsOfferSectionUpdated(state) {
    state.data = {
      isOfferSectionsUpdated: false,
      isPrimeSectionSectionUpdated: false,
      isAmountsSectionUpdated: false,
    };
  },
});
export const { setIsOfferSectionUpdated, resetIsOfferSectionUpdated } =
  offerSectionUpdate.actions;

export const allowConvEnableStatus = createGenericSlice({
  name: "allow_conv_status_rn",
  initialState: {
    status: "loading",
    data: {
      isAllowConvEnable: false,
    },
  },
})({
  setAllowConvEnable(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});
export const { setAllowConvEnable } = allowConvEnableStatus.actions;

export const isallowanceTypeChange = createGenericSlice({
  name: "allow_type_change_rn",
  initialState: {
    status: "loading",
    data: {
      isAllowanceTypeChanged: {},
    },
  },
})({
  setAllowTypeChange(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});
export const { setAllowTypeChange } = isallowanceTypeChange.actions;

export const vendorsDataForAllowance = createGenericSlice({
  name: "vendors_data_for_allowance_rn",
  initialState: {
    status: "loading",
    data: {
      vendors: {},
      isMultiVendor: false,
      isAllowMinSfoChanged: false,
      isMultiVendorEnabled: false,
    },
  },
})({
  setVendorsForAllowances(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const { setVendorsForAllowances } = vendorsDataForAllowance.actions;
