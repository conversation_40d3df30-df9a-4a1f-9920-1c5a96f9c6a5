import { getUpdatedYearBasedOnCurrentDate } from "../../../service/event-details/event-detail-service";

export const EVENT_DETAILS_FIELDS = {
  division: {
    label: "Division",
    placeholder: "Select Division",
    required: true,
    resetValue: null,
    registerField: "divisionIds",
    options: [
      {
        name: "Division1",
        id: 1,
      },
      {
        name: "Division2",
        id: 2,
      },
      {
        name: "Division3",
        id: 3,
      },
    ],
    optionUrl: `division`,
    type: "select",
    error: {
      required: {
        backgroundColor: "",
        text: "Division Required",
      },
    },
    clearErrorFieldsOnChange: [
      "planStoreGroupType",
      "vehicleType",
      "startDate",
      "endDate",
      "name",
      "planProductGroups",
      "storeGroups",
      "eventCreationVehicle.year",
      "startWeekVehicle",
    ],
    batchSetValueFields: [
        "vehicleType",
        "promoProductGroup",
        "storeGroupName",
        "startWeekVehicle",
        "startDate",
        "endDate",
    ]
  },
  planProductGroups: {
    label: "Promo Product Groups",
    required: true,
    resetValue: [],
    prop: "planProductGroupId",
    registerField: "planProductGroups",
    placeHolder: "Search Promo Product Groups",
    notFoundText: "No Promo Products Found",
    suggestionKey: "name",
    notSameUpcTypeErrorMsg:
      "Display type Promo Product Groups cannot be mixed with non-Display groups within an Event. Please change Product Groups chosen.",
    multiVendorErrorMsg:
      "Promo Product Groups contain items from multiple vendors",
    searchQueryName: "getPlanProductGroupByName",
    getQueryName: "getPromoProductGroupsByDivisionId",
    type: "multiSelect",
    options: [
      {
        name: "",
        id: "",
      },
    ],
    optionUrl: `promoProductGroups`,
    error: {
      required: {
        backgroundColor: "",
        text: "Promo Product Groups Required",
      },
    },
  },
  planStoreGroupType: {
    label: "Store Group Type",
    required: true,
    resetValue: [
      {
        name: "Division",
        groupType: "S",
      },
    ],
    registerField: "planStoreGroupType",
    optionUrl: "planStoreGroupType",
    options: [
      {
        name: "",
        groupType: "",
      },
      // {
      //   name: "Banner",
      //   id: 1,
      // },
      // {
      //   name: "Custom",
      //   id: 2,
      // },
      // {
      //   name: "Division",
      //   id: 3,
      // },
      // {
      //   name: "Division Price Area",
      //   id: 4,
      // },
      // {
      //   name: "Geography",
      //   id: 5,
      // },
      // {
      //   name: "Store Specific",
      //   id: 6,
      // },
    ],
    type: "select",
    error: {
      required: {
        backgroundColor: "",
        text: "Store Group Type Required",
      },
    },
  },
  storeGroups: {
    label: "Store Groups",
    required: true,
    resetValue: [],
    registerField: "storeGroups",
    placeHolder: "Search Store Groups",
    notFoundText: "No Store Groups Found",
    searchQueryName: "getStoreGroups",
    getQueryName: "getStoreGroups",
    suggestionKey: "storeGroupName",
    prop: "storeGroupId",
    optionUrl: `storeGroups`,
    type: "select",
    options: [
      {
        name: "",
        id: "",
      },
    ],
    error: {
      required: {
        backgroundColor: "",
        text: "Store Groups Required",
      },
    },
  },

  vehicleType: {
    label: "Vehicle Type/Custom Date",
    required: true,
    resetValue: {},
    registerField: "eventCreationVehicle.vehicleType.vehicleTypeId",
    gqlQueryConst: "events",
    type: "select",
    options: [
      {
        name: "Division1",
        id: 1,
      },
      {
        name: "Division2",
        id: 2,
      },
      {
        name: "Division3",
        id: 3,
      },
    ],
    error: {
      required: {
        backgroundColor: "",
        text: "Vehicle Type/Custom Date Required",
      },
    },
  },
  year: {
    label: "Year",
    required: true,
    registerField: "eventCreationVehicle.year",
    type: "select",
    options: [
      {
        name: getUpdatedYearBasedOnCurrentDate?.(Date.now())[0], //adds 2 weeks to the current date and returns year
        id: 1,
      },
      {
        name: getUpdatedYearBasedOnCurrentDate?.(Date.now())[1],
        id: 2,
      },
      {
        name: getUpdatedYearBasedOnCurrentDate?.(Date.now())[2],
        id: 3,
      },
      {
        name: getUpdatedYearBasedOnCurrentDate?.(Date.now())[3],
        id: 4,
      },
    ],
    error: {
      required: {
        backgroundColor: "",
        text: "Year Required",
      },
    },
  },
  startWeek: {
    label: "Start Week/Vehicle",
    required: true,
    resetValue: {},
    registerField: "eventCreationVehicle",
    optionUrl: "startWeekVehicle",
    type: "select",
    options: [
      {
        name: "",
        id: "",
      },
    ],
    error: {
      required: {
        backgroundColor: "",
        text: "Start Week/Vehicle Required",
      },
    },
  },
  startDate: {
    label: "Vehicle Start",
    required: true,
    disable: true,
    resetValue: "",
    registerField: "eventCreationVehicle.startDate",
    type: "text",
    error: {
      required: {
        backgroundColor: "",
        text: "Event Start Required",
      },
    },
  },
  endDate: {
    label: "Vehicle End",
    required: true,
    disable: true,
    resetValue: "",
    registerField: "eventCreationVehicle.endDate",
    type: "text",
    error: {
      required: {
        backgroundColor: "",
        text: "Event End Required",
      },
    },
  },
  eventName: {
    label: "Event Name",
    required: true,
    resetValue: "",
    registerField: "name",
    type: "text",
    errorMessage: "Please Enter Valid Event Name",
    maxLength: 100,
    error: {
      required: {
        backgroundColor: "",
        text: "Event Name Required",
      },
      maxLength: {
        backgroundColor: "",
        text: "Max Length Reached",
      },
    },
    tooltip:
      "This is auto generated based on your selections.\n You can edit this if you want.",
  },
  periscopeField: {
    label: "PID",
    required: false,
    resetValue: "",
    registerField: "periscopeFormField",
    type: "text",
    maxLength: 20,
    errorMessage: "This PID is invalid",
    error: {
      required: {
        backgroundColor: "",
        text: "Periscope ID required",
      },
      maxLength: {
        backgroundColor: "",
        text: "Max Periscope Length Reached",
      },
    },
  },
};
