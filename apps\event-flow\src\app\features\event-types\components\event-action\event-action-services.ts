import {
  CTAs_Keys,
  CTAs_label,
  Cancel_Modal_labels,
  Reject_Modal_labels,
  Delete_Modal_labels,
  No_Valid_Promotion_Modal_labels,
  Agree_Modal_labels,
} from "../../../create-event/constants/event-status/contsants";
import {
  EEVENT_STATUS,
  EUSER_ROLES,
  EVENT_ACTION_LABELS,
} from "./event-action.model";
import { isEventReturn } from "../../event-progress-container-service";
import {
  getAllowanceDraftCollections,
  getPromoDraftCollections,
} from "../../event-hook-form-wrapper-service";
import { apiCallEvent, saveModalData } from "./helper-event";
import { useSelectorWrap } from "@me/data-rtk";
import EventActionStatus from "./event-action-status";
import { eventTypes } from "../../constants/event-type-constant";
import efConstants from "../../../../../../../event-flow/src/app/shared/ef-constants/ef-constants";
import { compareAsc } from "date-fns";
import { formatDate } from "@me-upp-js/utilities";
import {
  PROMO_DETAILS_HIDDEN_PRICING_FEATURE,
  valid_promos,
} from "@me/util-helpers";
import { debounce } from "lodash";
import { EVENT_TYPE } from "../../../create-event/constants/constants";

const EventActionService = (
  startDate,
  endDate,
  executeAction,
  dispatchPending,
  setCommentOpen
) => {
  const { hiddenPricingFeatureEnabled } = PROMO_DETAILS_HIDDEN_PRICING_FEATURE;
  const eventActionStatus = EventActionStatus(dispatchPending);
  const {
    data: { allowanceEventInd, eventDetailsEventInd, promotionEventInd, otherDetailsChangedInd },
  } = useSelectorWrap("plan_event_indicators");
  const { data: eventDetailsData } =
    useSelectorWrap("event_details_data") || {};
  const getPostApiCall = (key, userRole, eventStatus) => {
    return eventActionStatus.statusMap[userRole]?.[eventStatus]?.[key]?.data
      ? eventActionStatus.statusMap[userRole]?.[eventStatus]?.[key]?.data
      : eventActionStatus.statusMap[userRole]?.[eventStatus]?.[key];
  };
  const checkIfValidPromotionPresent = (ctaKey, promotionsLists) => {
    const { SEND_CTS_BTNS } = efConstants;
    if (SEND_CTS_BTNS.includes(ctaKey)) {
      const promoList = promoStatusList(promotionsLists) || [];
      return promoList.length > 0;
    } else return true;
  };

  const promoStatusList = promotionsLists => {
    const countPromotionCardStatus = promotionsLists?.[0]?.promotionsList;
    return countPromotionCardStatus?.filter(promotion => {
      const { overridePromotionStatus, promotionWorkflowStatus } = promotion;
      return (
        !efConstants?.EXCLUDED_OFFER_PROMO_STATUS?.includes(
          promotionWorkflowStatus
        ) &&
        !(
          overridePromotionStatus === EEVENT_STATUS.CANCELED &&
          promotionWorkflowStatus === EEVENT_STATUS.AGREED_PENDING
        )
      );
    });
  };

  const isEventHasPastDates = () => {
    const result = compareAsc(new Date(startDate), new Date(Date.now()));
    return result < 1;
  };

  const handleStatus = (
    key,
    setModalType,
    setModalPopupOpen,
    onDispatchCommentSection,
    eId,
    apiCall,
    eventType,
    promotionsLists,
    eventStatus,
    userRole
  ) => {
    let isValidPromoPresent;
    if (eventType === eventTypes?.[0]?.eventType) {
      isValidPromoPresent = checkIfValidPromotionPresent(
        CTAs_Keys.SEND_TO_VENDOR,
        promotionsLists
      );
    }
    const sendToMethod = () => {
      if (
        !isValidPromoPresent &&
        eventType === eventTypes?.[0]?.eventType &&
        eventStatus !== EEVENT_STATUS.EXECUTED &&
        !isEventHasPastDates()
      ) {
        saveModalData(No_Valid_Promotion_Modal_labels[userRole]);
        setModalType("promo");
        setModalPopupOpen(!isValidPromoPresent);
        apiCallEvent(apiCall, key);
      } else {
        executeApiCall(eId, apiCall, key, setCommentOpen);
        // setCommentOpen(false);
      }
    };
    const agreeToEvent = () => {
      if (
        compareAsc(formatDate(startDate), formatDate(new Date())) < 1 ||
        compareAsc(formatDate(endDate), formatDate(new Date())) < 1
      ) {
        saveModalData({
          ...Agree_Modal_labels,
          WARNING_MESSAGE:
            compareAsc(formatDate(endDate), formatDate(new Date())) < 0
              ? "This event is executed."
              : "This event is active.",
        });
        setModalType("modal");
        setModalPopupOpen(true);
        apiCallEvent(apiCall, key);
      } else {
        executeApiCall(eId, apiCall, key, setCommentOpen);
      }
    };
    const keysObj = {
      [CTAs_Keys.SEND_TO_VENDOR]: sendToMethod,
      [CTAs_Keys.SEND_TO_MERCHANT]: sendToMethod,
      [CTAs_Keys.DELETE_EVENT]: () => {
        saveModalData(Delete_Modal_labels);
        setModalType("delete");
        setModalPopupOpen(true);
      },
      [CTAs_Keys.REJECT_EVENT]: () => {
        saveModalData(Reject_Modal_labels);
        apiCallEvent(eventActionStatus.postReject.data, key);
        setModalType("modal");
        setModalPopupOpen(true);
      },
      [CTAs_Keys.SEND_BACK_WITH_COMMENT]: () => {
        setCommentOpen(true);
        return userRole === EUSER_ROLES.MERCHANT && onDispatchCommentSection();
      },
      [CTAs_Keys.CANCEL_EVENT]: () => {
        saveModalData(Cancel_Modal_labels);
        setModalType("modal");
        setModalPopupOpen(true);
        return userRole === EUSER_ROLES.MERCHANT
          ? apiCallEvent(eventActionStatus.postCancel.data, key)
          : apiCallEvent(eventActionStatus.postAgreedToRequestCancel.data, key);
      },
      [CTAs_Keys.AUTO_APPROVE]: () => {
        executeApiCall(
          eId,
          eventActionStatus.postAgreed.data,
          key,
          setModalPopupOpen
        );
      },
      [CTAs_Keys.AGREE_TO_EVENT]: agreeToEvent,
      [CTAs_Keys.AGREE_TO_PENDING_REQUEST]: agreeToEvent,
    };
    const defaultKey = () => {
      executeApiCall(eId, apiCall, key, setModalPopupOpen);
      setCommentOpen(false);
    };
    keysObj[key] ? keysObj[key]() : defaultKey();
  };

  const executeApiCall = async (
    eId,
    apiCall,
    actionKeyName = "",
    setModalPopupOpen
  ) => {
    const isNcdpType = eventDetailsData?.eventType === EVENT_TYPE.NCDP;
    const parentEventId = eventDetailsData?.parentEvent?.eventId || null;
    const debounceApiCall = debounce(async () => {
      try {
        const data = await apiCall({
          URL_PARAM: isNcdpType && parentEventId ? parentEventId : eId,
          queryParams: {},
          ...(actionKeyName === CTAs_Keys.RETURN && {
            customHeaders: { isReturnEvent: true },
          }),
          ...(isNcdpType && {
            customHeaders: {
              divisionIds: eventDetailsData?.divisionIds?.join(","),
            },
          }),
        });
        if (data?.data?.eventId) {
          executeAction(data?.data?.eventStatus, actionKeyName);
        }
        setModalPopupOpen(false);
      } catch (error) {
        console.log(error);
      }
    }, 500);

    debounceApiCall();
  };

  const checkIsValid = (
    validate,
    label,
    planEvent,
    userRole,
    planEventHistory,
    eventStatus
  ) => {
    let isValid = true;
    if (validate) {
      const checkIndicators = [
        allowanceEventInd,
        eventDetailsEventInd && otherDetailsChangedInd,
        promotionEventInd,
      ].some(e => e);
      const checkIndicatorsWithHiddenPrice = () => {
        const isAllIndicators = [allowanceEventInd, eventDetailsEventInd && otherDetailsChangedInd].some(
          e => e
        );
        if (isAllIndicators) {
          return false;
        } else if (promotionEventInd) {
          const validPromos = valid_promos(eventDetailsData?.promotionsList);
          return (
            validPromos?.[0]?.hiddenPrice ||
            validPromos?.[0]?.createPricing ||
            validPromos?.[0]?.adDetails ||
            validPromos?.[0]?.doNotPrice ||
            validPromos?.[0]?.splitBIB ||
            !validPromos?.[0]?.hasPromoDetailsFieldsChanged
          );
        }
        return true;
      };
      const checkAllIndicators = !checkIndicators
        ? true
        : checkIndicatorsWithHiddenPrice();
      const isEventReturnedByCurrUser = checkAllIndicators
        ? !isEventReturn(
          [allowanceEventInd, eventDetailsEventInd && otherDetailsChangedInd, promotionEventInd],
          planEventHistory,
          userRole,
          EVENT_ACTION_LABELS.RETURN,
          eventStatus
        )
        : false;

      isValid =
        (hiddenPricingFeatureEnabled ? checkAllIndicators : !checkIndicators) &&
          !checkIsNewAdded(planEvent) &&
          isEventReturnedByCurrUser
          ? label === CTAs_label.SEND_BACK_WITH_COMMENT
          : label === CTAs_label.SEND_TO_VENDOR;
    } else if (
      userRole === EUSER_ROLES?.VENDOR &&
      label === CTAs_label?.AGREE_TO_EVENT &&
      planEvent?.isStatusUpdateWithComment
    ) {
      isValid = false;
    }
    return isValid;
  };

  const checkIsNewAdded = planEvent => {
    const draftOffers = getAllowanceDraftCollections(
      planEvent?.offerAllowances,
      "allowances",
      "allowanceStatus"
    );

    const draftPromotionsList = getPromoDraftCollections(
      planEvent?.promotionsList,
      "promotionsList",
      "promotionWorkflowStatus",
      "MERCHANT"
    );
    return draftPromotionsList?.length || draftOffers?.length;
  };

  return {
    eventActionStatus,
    promoStatusList,
    getPostApiCall,
    handleStatus,
    executeApiCall,
    checkIsValid,
    checkIsNewAdded,
  };
};

export default EventActionService;
