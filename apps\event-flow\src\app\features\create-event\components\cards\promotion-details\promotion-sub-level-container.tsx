import * as React from "react";
import SubLevelPromotionCustomer from "./stepper/promotion-customer/sub-level-promotion-customer";
import SubLevelPromotionDates from "./stepper/promotion-dates/sub-level-promotion-date";
import SubLevelPromotionDetails from "./stepper/promotion-details/sub-level-promotion-details";

interface IPromotionSubLevelContainerProps {
  container?: any;
  getValues?: any;
  offerIndex?: any;
  cardIndex?: any;
  cardItemIndex?: any;
  promotionIndex?: any;
  stepperIndex?: any;
  step?: any;
  isEdit?: boolean;
}

const PromotionSubLevelContainer: React.FunctionComponent<
  IPromotionSubLevelContainerProps
> = ({
  offerIndex,
  promotionIndex,
  getValues,
  container,
  stepperIndex,
  cardIndex,
  cardItemIndex,
  step,
  isEdit,
}) => {
  if ((step <= stepperIndex && !isEdit) || (step === stepperIndex && isEdit))
    return <></>;
  const component = container => {
    switch (container.toLowerCase()) {
      case "promotion-customer":
        return (
          <SubLevelPromotionCustomer
            cardIndex={cardIndex}
            cardItemIndex={cardItemIndex}
            getValues={getValues}
          />
        );

      case "promotion-dates":
        return (
          <SubLevelPromotionDates
            cardIndex={cardIndex}
            cardItemIndex={cardItemIndex}
            getValues={getValues}
          />
        );

      case "promotion-details":
        return (
          <SubLevelPromotionDetails
            cardIndex={cardIndex}
            cardItemIndex={cardItemIndex}
            getValues={getValues}
          />
        );

      default:
        return <></>;
    }
  };
  return (
    <>
      {/* <Suspense fallback={null}> <Component></Component></Suspense> */}
      {component(container)}
    </>
  );
};
export default PromotionSubLevelContainer;
