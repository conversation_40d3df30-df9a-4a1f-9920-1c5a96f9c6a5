import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import EventCommentsCheckBox from "./event-comments-check-box";

describe("Event Comments CheckBox suite", () => {
  const mockOffer = { title: "Offer # 123456" };
  const mockHandleSelectOffer = jest.fn();

  it("calls handleSelectOffer with the correct arguments when checked", () => {
    render(
      <EventCommentsCheckBox
        offer={mockOffer}
        handleSelectOffer={mockHandleSelectOffer}
        checked={true}
      />
    );
    const checkbox = screen.getByLabelText("Offer # 123456");
    fireEvent.click(checkbox);
    expect(mockHandleSelectOffer).toHaveBeenCalledWith(mockOffer, true);
  });

  it("calls handleSelectOffer with the correct arguments when unchecked", () => {
    render(
      <EventCommentsCheckBox
        offer={mockOffer}
        handleSelectOffer={mockHandleSelectOffer}
        checked={true}
      />
    );
    const checkbox = screen.getByLabelText("Offer # 123456");
    fireEvent.click(checkbox);
    fireEvent.click(checkbox);
    expect(mockHandleSelectOffer).toHaveBeenCalledWith(mockOffer, false);
  });

  it("updates the checked state correctly", () => {
    render(
      <EventCommentsCheckBox
        offer={mockOffer}
        handleSelectOffer={mockHandleSelectOffer}
        checked={true}
      />
    );
    const checkbox = screen.getByLabelText("Offer # 123456");
    fireEvent.click(checkbox);
    expect(checkbox).toBeChecked();
    fireEvent.click(checkbox);
    expect(checkbox).not.toBeChecked();
  });
});
