import React from "react";
import { getAllowanceKey } from "../../../../../service/allowance/allowance-service";
import NationalScanAdditionalDatesSection from "./national-scan-additional-dates-section";
import NationalCaseAdditionalDatesSection from "./national-case-additional-dates-section";

const NationalAdditionalDatesSection = ({
  cardIndex,
  cardItemIndex,
  formControls,
  sectionConfiguration,
  isEditEnable = false,
  offerMapKey = "",
  vehicleFields,
  sectionKey,
}) => {
  const { getValues = () => null } = formControls || {};
  const formData = getValues() || {};
  const { allowanceType = "" } = formData || {};
  const allowanceTypeKey = getAllowanceKey(allowanceType?.toUpperCase());

  const componentMap = {
    SCAN: {
      DSD_WHSE_RETAIL_DIVISION: NationalScanAdditionalDatesSection,
      DSD_LEAD_DISTRIBUTORS: NationalScanAdditionalDatesSection,
    },
    CASE: {
      DSD_LEAD_DISTRIBUTORS: NationalCaseAdditionalDatesSection,
      WAREHOUSE_DIST_CENTERS: NationalCaseAdditionalDatesSection,
      DSD_WHSE_RETAIL_DIVISION: NationalCaseAdditionalDatesSection,
    },
    SHIP_TO_STORE: {
      DSD_LEAD_DISTRIBUTORS: NationalCaseAdditionalDatesSection,
      DSD_WHSE_RETAIL_DIVISION: NationalCaseAdditionalDatesSection,
    },
  };

  const AdditonalDatesComponent =
    componentMap?.[allowanceTypeKey]?.[offerMapKey];

  return (
    <div>
      {AdditonalDatesComponent ? (
        <AdditonalDatesComponent
          cardIndex={cardIndex}
          cardItemIndex={cardItemIndex}
          formControls={formControls}
          sectionConfiguration={sectionConfiguration}
          isEditEnable={isEditEnable}
          offerMapKey={offerMapKey}
          vehicleFields={vehicleFields}
          sectionKey={sectionKey}
        />
      ) : null}
    </div>
  );
};

export default React.memo(NationalAdditionalDatesSection);
