import { useState } from "react";
import Card from "@albertsons/uds/molecule/Card";
import { ChevronRight as ChevronRightIcon } from "lucide-react";
import clsx from "clsx";
import { Table } from "@albertsons/uds/molecule/Table";
import { Column } from "@albertsons/uds/molecule/Table/Table.types";
import Tooltip from "@albertsons/uds/molecule/Tooltip";
import { usePostPlanItemsMutation } from "../../../service/apis/event-api";
import { sortViewItems } from "../../../../../shared/helpers/event-flow-helpers";
import {
  AllowanceExcludeItemInd,
  AsyncAdjustmnetMapper,
  InvalidItemStatuses,
} from "../../../constants/event-status/contsants";
import { useSelectorWrap } from "@me/data-rtk";
import "./event-details-card.scss";
import {
  compareDates,
  formatTimestampToUtcDate,
  useGetAppBasePath,
} from "@me/util-helpers";
import {
  formatTimestamp,
  isFeatureFlagEnabled,
  sortSize,
} from "@me-upp-js/utilities";
import { appConstants } from "@me/utils-root-props";
import { useDispatch } from "react-redux";
import { setItemsData } from "../../../service/slice/event-detail-slice";
import SkeletonLoader from "@albertsons/uds/molecule/SkeletonLoader";

interface EventDetailsPromoList {
  itemDescription: string;
  itemId: string;
  primaryUpc: string;
  upcs: string[];
  packRetail: number;
  size: string;
  invalidItemStatuses:
    | {
        itemId: string;
        itemValidInd: AllowanceExcludeItemInd;
      }[]
    | [];
  itemDataExcludes: {
    itemId: string;
    reason: string;
  }[];
  sourceProductGroupIds: number[];
  asyncAdjustments: {
    command: string;
    lastUpdTs: number;
    customEndDate: string;
  }[];
  itemStatus: "ACTIVE" | "INACTIVE";
  effectiveEndDate: string;
}

interface IViewEventItemsCardContainerProps {
  isItemsCardOpen: boolean;
  setIsItemsCardOpen: React.Dispatch<React.SetStateAction<boolean>>;
  promoProductGroups: any;
}

export const renderValue = (key, data) => {
  return <div className="text-sm ml-3">{data?.[key]}</div>;
};

const ViewEventItemsCardContainer: React.FunctionComponent<
  IViewEventItemsCardContainerProps
> = ({ isItemsCardOpen, setIsItemsCardOpen, promoProductGroups }) => {
  const { baseDomain } = useGetAppBasePath();
  const dispatch = useDispatch();
  const cicelAlertIconPath = `${baseDomain}assets/icons/8x8/circle-alert.svg`;
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const {
    id = "",
    divisionIds = [],
    endDate = "",
    createUser = null,
  } = eventDetailsData || {};

  const [postPlanPromoProductGroupItems, { isLoading }] =
    usePostPlanItemsMutation();

  const [allowancePromoData, setAllowancePromoData] = useState<
    EventDetailsPromoList[]
  >([]);

  const getIncludedStatus = (item: EventDetailsPromoList) => {
    const isItemInactive = item?.itemStatus === "INACTIVE";
    const isDiscontinued =
      compareDates([
        item?.effectiveEndDate,
        createUser?.createTs?.split("T")?.[0],
      ]) <= 0;

    const itemInvalidItemStatus = item?.invalidItemStatuses?.[0]?.itemValidInd;
    const itemDataExcludeReason = item?.itemDataExcludes?.filter(
      excludedItem =>
        excludedItem?.reason !== AllowanceExcludeItemInd.COST_EXCL_UNAUTH_VENDOR
    )?.[0]?.reason as AllowanceExcludeItemInd;

    const asyncAdjustments = item?.asyncAdjustments?.[0];
    const effectiveEndDate =
      asyncAdjustments?.customEndDate &&
      AsyncAdjustmnetMapper?.[asyncAdjustments?.command] ===
        AsyncAdjustmnetMapper.ITEM_DELETION
        ? formatTimestamp({
            timestamp: asyncAdjustments?.customEndDate,
            pattern: "MM/DD/YY",
          })
        : null;

    const invalidItem = isItemInactive
      ? InvalidItemStatuses[AllowanceExcludeItemInd.NO_LONGER_AVAILABLE]
      : itemInvalidItemStatus
      ? InvalidItemStatuses[itemInvalidItemStatus]
      : InvalidItemStatuses[itemDataExcludeReason]
      ? InvalidItemStatuses[itemDataExcludeReason]
      : isDiscontinued
      ? InvalidItemStatuses[AllowanceExcludeItemInd.DISCONTINUED_ITEM]
      : null;

    return effectiveEndDate ? (
      <div className="flex items-center">End {effectiveEndDate}</div>
    ) : invalidItem ? (
      <div className="flex gap-2 items-center">
        <span className="text-red-800">{invalidItem?.key}</span>
        {invalidItem?.value && (
          <Tooltip zIndex={100} anchor="top">
            <Tooltip.Popover>
              <span className="p-2">{invalidItem?.value}</span>
            </Tooltip.Popover>
            <img
              src={cicelAlertIconPath}
              className="h-3 w-3 cursor-pointer rotate-180"
              alt="info-icon"
            />
          </Tooltip>
        )}
      </div>
    ) : asyncAdjustments ? (
      <div
        className={`${
          AsyncAdjustmnetMapper?.[asyncAdjustments?.command] ===
          AsyncAdjustmnetMapper.ITEM_ADDITION
            ? "text-green-800"
            : "text-amber-800"
        }`}
      >
        {AsyncAdjustmnetMapper?.[asyncAdjustments?.command]}{" "}
        {formatTimestampToUtcDate(asyncAdjustments?.lastUpdTs, "MM/dd/yy")}
      </div>
    ) : null;
  };

  const renderPpgIds = (ppgs: number[] = []) => {
    const label = ppgs?.length > 1 ? ppgs?.join(", ") : ppgs?.[0] || "";
    return ppgs?.length > 1 ? (
      <div className="pl-3 flex gap-2 truncate whitespace-nowrap text-sm">
        <Tooltip zIndex={100} anchor="bottom" showDelay={300}>
          <Tooltip.Popover>
            <span className="p-1">{label}</span>
          </Tooltip.Popover>
          <div className="w-full truncate leading-6 cursor-pointer">
            {label}
          </div>
        </Tooltip>
      </div>
    ) : (
      <div className="w-full leading-6">{label}</div>
    );
  };

  const renderValue = (key: string, item: EventDetailsPromoList) => {
    return <div className="text-sm ml-3">{item?.[key]}</div>;
  };

  const PROMOTION_COLS: Column<EventDetailsPromoList>[] = [
    {
      id: "itemDescription",
      label: "Item Description",
      value: item => renderValue("itemDescription", item),
      sortable: (i1, i2) =>
        i1?.itemDescription?.localeCompare(i2?.itemDescription),
      minWidth: "12vw",
    },
    {
      id: "sourceProductGroupIds",
      label: "PPG",
      value: item => renderPpgIds(item?.sourceProductGroupIds),
      maxWidth: "56px",
    },
    {
      id: "itemId",
      label: "CIC",
      value: item => renderValue("itemId", item),
      sortable: (i1, i2) => i1.itemId.localeCompare(i2.itemId),
      maxWidth: "80px",
    },
    {
      id: "primaryUPC",
      label: "Primary UPC",
      maxWidth: "110px",
      value: item => (
        <span>
          {item?.primaryUpc?.replace(/^(.)(.)(.{5})(.*)$/, "$1-$2-$3-$4")}
        </span>
      ),
      sortable: (i1, i2) => i1?.primaryUpc?.localeCompare(i2?.primaryUpc),
    },
    {
      id: "UPCs",
      label: "UPCs",
      maxWidth: "60px",
      value: (item: EventDetailsPromoList) => {
        return (
          <div className={"text-center"}>
            <Tooltip zIndex={100} anchor={"bottom"} showDelay={300}>
              <Tooltip.Popover>
                <div className={"grid grid-cols-1 w-full "}>
                  {item?.upcs?.map((cur_upc: string, key: number) => {
                    return (
                      <div className={"m-1"} key={key}>
                        {cur_upc}
                      </div>
                    );
                  })}
                </div>
              </Tooltip.Popover>
              <div className={"w-full h-full cursor-pointer text-[#1B6EBB]"}>
                {item?.upcs?.length}
              </div>
            </Tooltip>
          </div>
        );
      },
      sortable: (i1: EventDetailsPromoList, i2: EventDetailsPromoList) =>
        i2?.upcs?.length - i1?.upcs?.length,
    },
    {
      id: "pack",
      label: "Pack",
      value: item => renderValue("packRetail", item),
      sortable: (i1, i2) => i1?.packRetail - i2?.packRetail,
      maxWidth: "60px",
    },
    {
      id: "descriptiveSize",
      label: "Size",
      value: item => renderValue("size", item),
      sortable: (i1, i2) => sortSize(i1?.size, i2?.size),
      maxWidth: "60px",
    },
    {
      id: "itemId",
      label: "Included?",
      value: item => getIncludedStatus(item),
      minWidth: "100px",
    },
  ];

  const fetchPPG = async () => {
    try {
      const promoIds = promoProductGroups.map(
        p => p.id || p.planProductGroupId
      );
      const ppgData =
        promoIds?.length &&
        (await postPlanPromoProductGroupItems({
          URL_PARAM: id,
          division: divisionIds,
          promoStartDate: endDate,
        }));
      dispatch(setItemsData(ppgData?.data?.planEventItemsData || []));
      setAllowancePromoData(ppgData?.data?.planEventItemsData || []);
    } catch (e) {
      console.log(e);
      setAllowancePromoData([]);
    }
  };

  const ViewEventDetailsPromoTable = () => {
    const isItemFeatureEnabled = isFeatureFlagEnabled(
      appConstants?.FEATURE_FLAGS?.EVENT_ITEM_VIEW_ACCESS
    );
    return (
      <Table
        className={"max-h-[65vh] component-scroll overflow-auto items-table"}
        noHeader
        items={sortViewItems(allowancePromoData, "itemId")}
        itemKey={item => `${item.itemId}-${item.primaryUpc}`}
        columns={PROMOTION_COLS.filter(
          col => col?.label !== "Included?" || isItemFeatureEnabled
        )}
        singleSort={true}
        loading={isLoading}
      />
    );
  };

  return (
    <Card
      collapsible
      isOpen={isItemsCardOpen}
      onChange={() => setIsItemsCardOpen(!isItemsCardOpen)}
      className="min-w-[600px] mb-2 rounded-lg
      "
      customHeader
    >
      <Card.Header>
        <div
          onClick={() => {
            setIsItemsCardOpen(!isItemsCardOpen);
            if (
              promoProductGroups &&
              allowancePromoData?.length === 0 &&
              !isLoading &&
              id
            ) {
              fetchPPG();
            }
          }}
          className={
            "flex items-center w-full h-[52px] -mb-[1px] px-4 cursor-pointer rounded-lg bg-blue-307 border-b border-gray-204"
          }
        >
          <div className="flex flex-col items-center justify-center min-w-[32px] w-[32px] min-h-[32px] h-[32px]">
            <ChevronRightIcon
              height={12}
              strokeWidth={1}
              color={"#5a697b"}
              className={clsx(
                "min-w-[24px] min-h-[24px] transition duration-200",
                {
                  "rotate-90": isItemsCardOpen,
                }
              )}
            />
          </div>
          <div className="flex items-center justify-start w-full">
            <span className="text-base text-dark-text mr-5 select-none">
              Items
            </span>
          </div>
        </div>
      </Card.Header>
      <Card.Content>
        <div className="font-normal text-sm leading-4 mx-[-24px] mt-[-24px] mb-[-32px]">
          {isLoading ? (
            <div>
              <SkeletonLoader height={50} />
              <SkeletonLoader height={50} />
              <SkeletonLoader height={50} />
              <SkeletonLoader height={50} />
            </div>
          ) : (
            <ViewEventDetailsPromoTable />
          )}
        </div>
      </Card.Content>
    </Card>
  );
};

export default ViewEventItemsCardContainer;
