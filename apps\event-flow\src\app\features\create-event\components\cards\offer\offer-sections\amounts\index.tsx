import React, { memo, useMemo, useState } from "react";
import BaseHfIf from "./base-hf-if-whse-amount";
import BaseAmount from "./base-allowance-amount";
import AllowanceFormWrapper, {
  IFormControls,
} from "../../../common/allowance-form-wrapper";
import LoadingSpinner from "../../../../../constants/LoadingSpinner/LoadingSpinner";
import { useBaseAmountConfig } from "../../hooks/amounts/useBaseAmountConfig";

interface IOfferAmountSectionProps {
  sectionKey: string;
  cardIndex: number;
  cardItemIndex: number;
  isEditEnable: boolean;
}

const OfferAmountSection = ({
  sectionKey,
  cardIndex = 0,
  cardItemIndex = 0,
  isEditEnable = false,
}: IOfferAmountSectionProps) => {
  const [formControls, setFormControls] = useState<IFormControls>();
  //TODO: NEED TO CHECK THIS STATE USEAGE
  const [values, setValues] = useState();
  const [allowanceAmountData, setAllowanceAmountData] = useState({});
  const [allowanceAmountFields, setAllowanceAmountFields] = useState({});
  //TODO: NEED TO CHECK THIS STATE USEAGE
  const [isFormValid, setIsFormValid] = useState(true);

  const {
    isFetching,
    sectionConfiguration,
    allowanceTypeKey,
    isHfOrIf,
    isHfIfWhseCase,
    isZeroCost,
    allowancesResp,
    amountsInitialValueOnLoad,
    isSectionCompleted,
    amtSubLabelDisplayVal,
    handleSave,
    editViewAllItems,
    isAmtSavedInTemp
  } = useBaseAmountConfig({
    sectionKey,
    cardIndex,
    cardItemIndex,
    isEditEnable,
    allowanceAmountData,
    setAllowanceAmountData,
    isFormValid,
    formControls,
  });
  const memoedFunc = useMemo(()=>{
    return editViewAllItems
  },[])
  const Component = isHfOrIf ? BaseHfIf : BaseAmount;
  return (
    <div className="mb-5" id="abs-common-allowance-amount-container">
      <LoadingSpinner
        isLoading={isFetching}
        classname="!h-full !w-full rounded-md"
      />

      <div className="text-xl text-[#3997EF] font-bold mb-3">
        {sectionConfiguration?.label}
      </div>
      <AllowanceFormWrapper
        defaultValues={amountsInitialValueOnLoad}
        handleSave={handleSave}
        getFormControls={setFormControls}
        setValues={setValues}
      >
        {formControls ? (
          <Component
            {...{
              isEditEnable,
              allowanceAmountFields,
              setAllowanceAmountFields,
              allowanceType: allowanceTypeKey,
              cardIndex,
              cardItemIndex,
              sectionConfiguration,
              allowanceAmountData,
              setAllowanceAmountData,
              allowancesResp,
              amountsInitialValueOnLoad,
              editViewAllItems: memoedFunc,
              setIsFormValid,
              isZeroCost,
              formControls,
              isHfIfWhseCase,
              isSectionCompleted,
              isAmtSavedInTemp,
              amtSubLabelDisplayVal,
            }}
          />
        ) : null}
      </AllowanceFormWrapper>
    </div>
  );
};

export default memo(OfferAmountSection);
