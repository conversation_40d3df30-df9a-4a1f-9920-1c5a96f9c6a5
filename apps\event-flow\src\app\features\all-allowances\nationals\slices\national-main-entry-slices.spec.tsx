import { configureStore } from "@reduxjs/toolkit";
import {
  nationalDivisionsConfig,
  setNationalDivisionsConfig,
  resetNationalDivisionsConfig,
  allDivisionWarningData,
  setAlDivisionWarningData,
  resetAllDivisionsWarningData,
  allDivisionAllowancesRespCopySlice,
  setAllDivAllowancesRespCopy,
  resetAllDivAllowancesRespCopy,
  nationalHeaderOnlyAmtSlice,
  setNationalHeaderOnlyAmt,
  resetNationalHeaderOnlyAmt,
} from "./national-main-entry-slices";

describe("nationalDivisionsConfig Slice", () => {
  it("should handle initial state correctly", () => {
    const initialState = nationalDivisionsConfig.reducer(undefined, {
      type: undefined,
    });
    expect(initialState.status).toEqual("loading");
    expect(initialState.data).toEqual({
      selectedDivisionData: {},
      divisionsList: [],
      isNdpType: false,
    });
  });

  it("should set national divisions config", () => {
    const data = { selectedDivisionData: { id: 1 }, isNdpType: true };
    const newState = nationalDivisionsConfig.reducer(
      undefined,
      setNationalDivisionsConfig(data)
    );
    expect(newState.data).toEqual({
      selectedDivisionData: { id: 1 },
      divisionsList: [],
      isNdpType: true,
    });
  });

  it("should reset national divisions config", () => {
    const newState = nationalDivisionsConfig.reducer(
      undefined,
      resetNationalDivisionsConfig()
    );
    expect(newState.data).toEqual({
      selectedDivisionData: {},
      divisionsList: [],
      isNdpType: false,
    });
  });
});

describe("allDivisionWarningData Slice", () => {
  it("should handle initial state correctly", () => {
    const initialState = allDivisionWarningData.reducer(undefined, {
      type: undefined,
    });
    expect(initialState.status).toEqual("loading");
    expect(initialState.data).toEqual({
      divisionErrObj: {},
      divisionWarningObj: {},
    });
  });

  it("should set all division warning data", () => {
    const data = { divisionErrObj: { error: true } };
    const newState = allDivisionWarningData.reducer(
      undefined,
      setAlDivisionWarningData(data)
    );
    expect(newState.data).toEqual({
      divisionErrObj: { error: true },
      divisionWarningObj: {},
    });
  });

  it("should reset all division warning data", () => {
    const newState = allDivisionWarningData.reducer(
      undefined,
      resetAllDivisionsWarningData()
    );
    expect(newState.data).toEqual({
      divisionErrObj: {},
      divisionWarningObj: {},
    });
  });
});

describe("allDivisionAllowancesRespCopySlice Slice", () => {
  it("should handle initial state correctly", () => {
    const initialState = allDivisionAllowancesRespCopySlice.reducer(undefined, {
      type: undefined,
    });
    expect(initialState.status).toEqual("loading");
    expect(initialState.data).toEqual([]);
  });

  it("should set all division allowances response copy", () => {
    const data = [1, 2, 3];
    const newState = allDivisionAllowancesRespCopySlice.reducer(
      undefined,
      setAllDivAllowancesRespCopy(data)
    );
    expect(newState.data).toEqual([1, 2, 3]);
  });

  it("should reset all division allowances response copy", () => {
    const newState = allDivisionAllowancesRespCopySlice.reducer(
      undefined,
      resetAllDivAllowancesRespCopy()
    );
    expect(newState.data).toEqual([]);
  });
});

describe("nationalHeaderOnlyAmtSlice Slice", () => {
  it("should handle initial state correctly", () => {
    const initialState = nationalHeaderOnlyAmtSlice.reducer(undefined, {
      type: undefined,
    });
    expect(initialState.status).toEqual("loading");
    expect(initialState.data).toEqual({});
  });

  it("should set national header only amount", () => {
    const actionPayload = { warehouseIndex: 0, val: 100, divisionId: "div1" };
    const newState = nationalHeaderOnlyAmtSlice.reducer(
      undefined,
      setNationalHeaderOnlyAmt(actionPayload)
    );
    expect(newState.data).toEqual({ div1: [100] });
  });

  it("should reset national header only amount", () => {
    const newState = nationalHeaderOnlyAmtSlice.reducer(
      undefined,
      resetNationalHeaderOnlyAmt()
    );
    expect(newState.data).toEqual({});
  });
});
