import { createGenericSlice } from "@me/data-rtk";
import eventDetailsData from "../../constants/event-details/eventDetails";

export const eventDetailDataSlice = createGenericSlice({
  name: "event_details_data",
  initialState: {
    status: "loading",
    data: eventDetailsData,
  },
})({
  eventDetailsDataHandler(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
  resetEventDetailsHandler(state) {
    state.data = eventDetailsData;
  },
});
export const { eventDetailsDataHandler, resetEventDetailsHandler } =
  eventDetailDataSlice.actions;

export const eventDetailConfigurationSlice = createGenericSlice({
  name: "event_details_configuration",
  initialState: {
    status: "loading",
    data: {},
  },
})({
  eventDetailsConfiguration(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const divisionDataSlice = createGenericSlice({
  name: "divisiondata_rn",
  initialState: { status: "loading" },
})({
  setDivisionData(state, { payload }) {
    state.data = payload;
  },
});
export const { eventDetailsConfiguration } =
  eventDetailConfigurationSlice.actions;

export const enableAllowanceTypeSlice = createGenericSlice({
  name: "event_details_enable_allowance",
  initialState: {
    status: "loading",
    data: { enableAllowance: true },
  },
})({
  eventDetailsAllowance(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const { eventDetailsAllowance } = enableAllowanceTypeSlice.actions;

export const planEventIndicatorsSlice = createGenericSlice({
  name: "plan_event_indicators",
  initialState: {
    status: "loading",
    data: {
      eventDetailsEventInd: false,
      allowanceEventInd: false,
      promotionEventInd: false,
      otherDetailsChangedInd: false,
      pidDetailsEventInd: false,
      planEvent: {},
      planEventPending: {},
    },
  },
})({
  setPlanEventIndicators(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const { setPlanEventIndicators } = planEventIndicatorsSlice.actions;

export const eventStatusChangedSlice = createGenericSlice({
  name: "event_status_change_indicators",
  initialState: {
    status: "loading",
    data: {
      isEventStatusChanged: false,
    },
  },
})({
  setEventStatusChangedIndicators(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const { setEventStatusChangedIndicators } =
  eventStatusChangedSlice.actions;

export const byPassOfferAllowanceSlice = createGenericSlice({
  name: "by_pass_offer_allowance",
  initialState: {
    status: "loading",
    data: { isOfferBypassed: false },
  },
})({
  byPassOfferAllowanceHandler(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});
export const { byPassOfferAllowanceHandler } =
  byPassOfferAllowanceSlice.actions;

export const isEventEditEnableStatusSlice = createGenericSlice({
  name: "is_event_edit_enable",

  initialState: {
    status: "loading",

    data: {
      isEventCardOpen: false,
    },
  },
})({
  editEventStatusHandler(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const { editEventStatusHandler } = isEventEditEnableStatusSlice.actions;

export const cicsWithUPCTypeSlice = createGenericSlice({
  name: "cic_upc_type_data",
  initialState: {
    status: "loading",
    data: {
      cicsWithUpcTypeInfo: [],
    },
  },
})({
  setCICsWithUPCTypeInfoHandler(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});
export const { setCICsWithUPCTypeInfoHandler } = cicsWithUPCTypeSlice.actions;

export const refetchEventPlanx_ApiSlice = createGenericSlice({
  name: "refetchEventPlanx_Api",

  initialState: {
    status: "loading",
    data: {},
  },
})({
  doRefetchEventsPlanxAapi(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const { doRefetchEventsPlanxAapi } = refetchEventPlanx_ApiSlice.actions;

export const ppgsAddedThroughCICItemsSlice = createGenericSlice({
  name: "ppgs_added_through_cic_items",

  initialState: {
    status: "loading",
    data: {
      isPPGsAddedThroughCICItems: false,
    },
  },
})({
  setPPGsAddedThroughCICItemsHandler(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const { setPPGsAddedThroughCICItemsHandler } =
  ppgsAddedThroughCICItemsSlice.actions;

export const isProductSourcesEmptySlice = createGenericSlice({
  name: "is_product_sources_empty",

  initialState: {
    status: "loading",
    data: {
      isProductSourcesEmpty: false,
    },
  },
})({
  setIsProductSourcesEmptyHandler(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const { setIsProductSourcesEmptyHandler } =
  isProductSourcesEmptySlice.actions;

export const itemMinApiResp = createGenericSlice({
  name: "item_min_res",
  initialState: {
    status: "loading",
    data: {},
  },
})({
  setItemMinApiResp(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const { setItemMinApiResp } = itemMinApiResp.actions;

export const storeGroupDivisions = createGenericSlice({
  name: "store_group_divisions",
  initialState: {
    status: "loading",
    data: {
      storeGroupDivisions: [],
    },
  },
})({
  setStoreGroupDivisions(state, { payload }) {
    state.data = payload;
  },
});

export const { setStoreGroupDivisions } = storeGroupDivisions.actions;

export const storeGroupsData = createGenericSlice({
  name: "store_groups_data",
  initialState: {
    status: "loading",
    data: {
      storeGroupsData: [],
    },
  },
})({
  setStoreGroupsData(state, { payload }) {
    state.data = payload;
  },
});

export const { setStoreGroupsData } = storeGroupsData.actions;

export const selectedStoreGroups = createGenericSlice({
  name: "selected_store_groups",
  initialState: {
    status: "loading",
    data: [],
  },
})({
  setSelectedStoreGroups(state, { payload }) {
    state.data = payload;
  },
});

export const { setSelectedStoreGroups } = selectedStoreGroups.actions;

export const vendorNDDivisionDataSlice = createGenericSlice({
  name: "vendor_nd_division_data",
  initialState: {
    status: "loading",
    data: {
      vendorDivsions: [],
    },
  },
})({
  setVendorNDDivisions(state, { payload }) {
    state.data = payload;
  },
});

export const { setVendorNDDivisions } = vendorNDDivisionDataSlice.actions;

export const itemDataSlice = createGenericSlice({
  name: "items_data",
  initialState: {
    status: "loading",
    data: {
      items: [],
    },
  },
})({
  setItemsData(state, { payload }) {
    state.data = payload;
  },
});

export const { setItemsData } = itemDataSlice.actions;

export const eventTypeAndDivisionsSlice = createGenericSlice({
  name: "event_type_and_divisions",

  initialState: {
    status: "loading",
    data: {
      eventType: "",
      divisions: [],
    },
  },
})({
  setEventTypeAndDivsionsHandler(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const { setEventTypeAndDivsionsHandler } =
  eventTypeAndDivisionsSlice.actions;
