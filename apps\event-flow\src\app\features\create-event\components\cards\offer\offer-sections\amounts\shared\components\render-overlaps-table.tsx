import { OverlappingAllowanceGrid } from "@me-upp-js/features/expandable-table";
import { memo } from "react";

const RenderOverlapsTable = ({ allowancesResp, switchEnabled }) => {
  const checkQuickEntryDataAndDisplayInd = () => {
    const quickEntryData =
      allowancesResp?.offerAllowanceOverlapResults?.offerAllowanceOverlaps;
    return (
      quickEntryData?.length && quickEntryData?.some(obj => obj?.displayInd)
    );
  };
  return (
    <>
      {checkQuickEntryDataAndDisplayInd() ? (
        <OverlappingAllowanceGrid
          quickEntryData={
            allowancesResp.offerAllowanceOverlapResults.offerAllowanceOverlaps
          }
          isCase={!switchEnabled}
        />
      ) : (
        <div className="text-sm text-red-600">
          There are no overlapping Allowances for this Offer.
        </div>
      )}
      <div
        className="flex-grow-0 flex-shrink-0 w-full mt-4 h-px  bg-[#fdfdff] border-t border-r border-b-0 border-l border-[#c8daeb]"
        id="abs-basic-allowance-amount-render-middle-section-one"
      ></div>
    </>
  );
};
export default memo(RenderOverlapsTable);
