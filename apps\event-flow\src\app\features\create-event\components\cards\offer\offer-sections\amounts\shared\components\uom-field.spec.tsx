import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import UOMField from "./uom-field"; // Adjust import path
import { IAmountStepFieldProps } from "../props-types";
import "@testing-library/jest-dom";

jest.mock("../../../../../../fields/allowance-atoms", () => ({
  InputSelect: ({ fieldProps, onChange }: any) => (
    <select data-testid="input-select" onChange={(e) => onChange(e.target.value)}>
      <option value="option1">{fieldProps?.displayLabel || "Option 1"}</option>
    </select>
  ),
}));

jest.mock("./disabled-field", () => () => <div data-testid="disabled-field">Disabled Field</div>);
jest.mock(
  "../../../../../allowance/stepper/common-stepper/allowance-amount/allowance-amounts-services",
  () => ({
    getFieldWithHighletedWrapper: (component: JSX.Element) => component,
  })
);

describe("UOMField Component", () => {
    const defaultProps: IAmountStepFieldProps = {
        fieldProps: { label: "Unit of Measure", required: true, displayLabel: "UOM" },
        onChange: jest.fn(),
        options: [{ value: "kg", label: "Kilogram" }],
        disabled: false,
        value: "kg",
        allowanceAmountFields: {},
        getEventData: jest.fn(),
        searchId: "123",
        baseId: "base123",
        formControls: {
            register: jest.fn(),
            control: {},
        },
    };

    it("renders without errors when formControls is undefined", () => {
        const propsWithoutFormControls = { ...defaultProps, formControls: undefined };
        expect(() => render(<UOMField {...propsWithoutFormControls} />)).not.toThrow();
    });

    it("renders without errors when cover is undefined", () => {
        const propsWithoutCover = { ...defaultProps, cover: undefined };
        expect(() => render(<UOMField {...propsWithoutCover} />)).not.toThrow();
    });

    it("renders InputSelect when disabled is explicitly set to false", () => {
        render(<UOMField {...defaultProps} disabled={false} />);
        expect(screen.getByTestId("input-select")).toBeInTheDocument();
    });

    it("calls onChange when a new option is selected", async () => {
        render(<UOMField {...defaultProps} />);
        const selectElement = screen.getByTestId("input-select");
        await userEvent.selectOptions(selectElement, "option1");
        expect(defaultProps.onChange).toHaveBeenCalledWith("option1");
    });
    
    it("renders DisabledField when disabled is true", () => {
        render(<UOMField {...defaultProps} disabled={true} />);
        expect(screen.getByTestId("disabled-field")).toBeInTheDocument();
    });
});
