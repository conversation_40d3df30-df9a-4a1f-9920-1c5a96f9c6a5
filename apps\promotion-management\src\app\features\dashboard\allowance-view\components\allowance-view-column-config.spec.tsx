import { render, screen, fireEvent } from "@testing-library/react";
import { ALLOWANCE_VIEW_GRID_COLUMNS } from "./allowance-view-column-config";

// Mock components if they are not available
jest.mock("../../../../shared/ui/atoms/tooltip-wrapper/tooltip-wrapper", () => {
  return ({ label, children }) => (
    <div data-testid="tooltip-wrapper">
      {children}
      <span>{label}</span>
    </div>
  );
});
jest.mock("react-router-dom", () => ({
  NavLink: jest.fn(({ to, className, id, children }) => (
    <a href={to} className={className} id={id} data-testid="offer-name">
      {children}
    </a>
  )),
}));
jest.mock("./allowance-view-deal-sheet", () => {
  return ({ offer }) => (
    <div data-testid="allowance-view-deal-sheet">
      Deal Sheet for {offer.offerNumber}
    </div>
  );
});
jest.mock("react-pdf", () => ({
  Document: jest.fn(({ children }) => children),
  Page: jest.fn(() => <div data-testid="mock-page"></div>),
  pdfjs: {
    GlobalWorkerOptions: {
      workerSrc: "",
    },
    version: "2.10.377",
  },
}));
// Mock data
const mockData = [
  {
    offerNumber: "12345",
    eventStatus: "Active",
    planEventIdNbr: "54321",
    allowanceTypes: ["Type1"],
    name: "Event Name",
    eventCreationVehicle: {
      vehicleNm: "Vehicle Name",
      startDate: "2023-01-01",
      endDate: "2023-12-31",
    },
    planEvent: "6245265376e8932892892",
    simsVendorList: [
      { supplierId: "001", vendorSubAccountName: "Vendor1" },
      { supplierId: "002", vendorSubAccountName: "Vendor2" },
    ],
    allowanceStatus: "Approved",
    minAllowAmount: 100,
    maxAllowAmount: 200,
    headerFlatSumAmt: 0,
  },
];

// Mock Table Component
const MockTable = ({ data }) => (
  <div>
    {data.map((offer, index) => (
      <div key={index} className="table-row">
        {ALLOWANCE_VIEW_GRID_COLUMNS.map((column: any) => (
          <div
            key={column.id}
            style={{ width: column.width }}
            className="table-cell"
          >
            {column?.value(offer)}
          </div>
        ))}
      </div>
    ))}
  </div>
);

// Test cases
describe("ALLOWANCE_VIEW_GRID_COLUMNS", () => {
  test("renders the correct data for each column", () => {
    render(<MockTable data={mockData} />);

    // Offer ID column
    expect(screen.getByText("12345")).toBeTruthy();

    // Offer / Associated events column
    screen.debug();
    const offerAndEventColumn = screen.getAllByTestId("offer-name");
    expect(offerAndEventColumn?.[0].textContent).toBe(
      "Type1 - Vehicle Name - 01/01/23 - 12/31/23"
    );

    // Vendor column
    expect(screen.getByText("001 Vendor1, 002 Vendor2")).toBeTruthy();

    // Event / Offer Status column
    expect(screen.getByText("Active")).toBeTruthy();
    expect(screen.getByText("Approved")).toBeTruthy();

    // Amount column
    expect(screen.getByText("$100 - $200")).toBeTruthy();

    // Actions column
    expect(screen.getByText("Deal Sheet for 12345")).toBeTruthy();
  });
  it('displays correct information in the "Vendor" column', () => {
    const alertData = {
      simsVendorList: [
        {
          supplierId: "234",
          vendorSubAccountName: "Acc123",
          vendorId: "V123",
          vendorName: "Test Vendor",
        },
      ],
    };
    const vendorColumn: any = ALLOWANCE_VIEW_GRID_COLUMNS.find(
      column => column.id === "vendor"
    );
    const { getByText } = render(<>{vendorColumn.value(alertData)}</>);

    expect(getByText(`234 Acc123`)).toBeTruthy();
  });

  it('displays correct information in the "Event" column', () => {
    const alertData = {
      id: "123",
      offerNumber: "1234",
      planEventIdNbr: "10122",
      eventName: "Test Event",
      startDate: "01/01/22",
      endDate: "01/10/22",
      taskId: "1",
    };
    const eventColumn: any = ALLOWANCE_VIEW_GRID_COLUMNS.find(
      column => column.id === "offerId"
    );
    const { getByText } = render(<>{eventColumn.value(alertData)}</>);

    expect(getByText("1234")).toBeTruthy();
  });

  it('displays correct information in the "amount" column if min and max are not equal', () => {
    const alertData = {
      id: "123",
      offerNumber: "1234",
      planEventIdNbr: "10122",
      eventName: "Test Event",
      startDate: "01/01/22",
      endDate: "01/10/22",
      taskId: "1",
      minAllowAmount: 2,
      maxAllowAmount: 3,
      headerFlatSumAmt: 0,
    };
    const eventColumn: any = ALLOWANCE_VIEW_GRID_COLUMNS.find(
      column => column.id === "amount"
    );
    const { getByText } = render(<>{eventColumn.value(alertData)}</>);

    expect(getByText(`$2 - $3`)).toBeTruthy();
  });
  it('displays correct information in the "amount" column if min and max are equal', () => {
    const alertData = {
      id: "123",
      offerNumber: "1234",
      planEventIdNbr: "10122",
      eventName: "Test Event",
      startDate: "01/01/22",
      endDate: "01/10/22",
      taskId: "1",
      minAllowAmount: 2,
      maxAllowAmount: 2,
      headerFlatSumAmt: 0,
    };
    const eventColumn: any = ALLOWANCE_VIEW_GRID_COLUMNS.find(
      column => column.id === "amount"
    );
    const { getByText } = render(<>{eventColumn.value(alertData)}</>);

    expect(getByText(`$2`)).toBeTruthy();
  });
  it('displays correct information in the "amount" column if header flat amount', () => {
    const alertData = {
      id: "123",
      offerNumber: "1234",
      planEventIdNbr: "10122",
      eventName: "Test Event",
      startDate: "01/01/22",
      endDate: "01/10/22",
      taskId: "1",
      minAllowAmount: 2,
      maxAllowAmount: 2,
      headerFlatSumAmt: 1,
      allowanceTypes: ["Header Flat"],
    };
    const eventColumn: any = ALLOWANCE_VIEW_GRID_COLUMNS.find(
      column => column.id === "amount"
    );
    const { getByText } = render(<>{eventColumn.value(alertData)}</>);

    expect(getByText(`$1`)).toBeTruthy();
  });
  it('displays correct information in the "eventAndOfferStatus"', () => {
    const alertData = {
      id: "123",
      offerNumber: "1234",
      planEventIdNbr: "10122",
      eventName: "Test Event",
      startDate: "01/01/22",
      endDate: "01/10/22",
      taskId: "1",
      minAllowAmount: 2,
      maxAllowAmount: 2,
      eventStatus: "Active",
      allowanceStatus: "Executed",
      headerFlatSumAmt: 1,
    };
    const eventColumn: any = ALLOWANCE_VIEW_GRID_COLUMNS.find(
      column => column.id === "eventStatus"
    );
    const { getByText } = render(<>{eventColumn.value(alertData)}</>);

    expect(getByText(`Active`)).toBeTruthy();
  });
  it('displays correct information in the "offerStatus"', () => {
    const alertData = {
      id: "123",
      offerNumber: "1234",
      planEventIdNbr: "10122",
      eventName: "Test Event",
      startDate: "01/01/22",
      endDate: "01/10/22",
      taskId: "1",
      minAllowAmount: 2,
      maxAllowAmount: 2,
      eventStatus: "Active",
      allowanceStatus: "Executed",
      headerFlatSumAmt: 1,
    };
    const eventColumn: any = ALLOWANCE_VIEW_GRID_COLUMNS.find(
      column => column.id === "offerStatus"
    );
    const { getByText } = render(<>{eventColumn.value(alertData)}</>);
    expect(getByText(`Executed`)).toBeTruthy();
  });
  it('displays correct information in the "allowance Type"', () => {
    const alertData = {
      id: "123",
      offerNumber: "1234",
      planEventIdNbr: "10122",
      eventName: "Test Event",
      startDate: "01/01/22",
      endDate: "01/10/22",
      taskId: "1",
      minAllowAmount: 2,
      maxAllowAmount: 2,
      eventStatus: "Active",
      allowanceStatus: "Executed",
      headerFlatSumAmt: 1,
      allowanceTypes: ["Header Flat"],
    };
    const eventColumn: any = ALLOWANCE_VIEW_GRID_COLUMNS.find(
      column => column.id === "allowanceType"
    );
    const { getByText } = render(<>{eventColumn.value(alertData)}</>);
    expect(getByText(`Header Flat`)).toBeTruthy();
  });
  test("opens a new tab with the correct URL when clicking the offer name", () => {
    global.open = jest.fn();
    const { getByTestId } = render(<MockTable data={mockData} />);
    const offerNameLink = getByTestId("offer-name");
    expect(offerNameLink).toBeTruthy();
    // fireEvent.click(offerNameLink);
    // expect(global.open).toHaveBeenCalledWith(
    //   "meupp/events/edit/6245265376e8932892892?offerIdForExpand=54321",
    //   "_blank"
    // );
  });
});
