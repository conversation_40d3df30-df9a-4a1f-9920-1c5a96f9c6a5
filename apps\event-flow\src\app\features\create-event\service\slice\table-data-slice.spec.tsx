import { configureStore } from "@reduxjs/toolkit";
import {
  setTableDataAction,
  updateIndividualItemAction,
  updateItemByItemAction,
  updateItemByDistributorAction,
  updateAllItemsAction,
  updateLeadDistributorsInfoAction,
  setFilteredAndSortedIdsAction,
  updateCaseAmountOnSwitch,
  updateIncludeIndicatorOnExclude,
  setIsUpdateAllChange,
  isUpdateAllClickedSlice,
} from "./table-data-slice";

import { tableDataSlice } from "./table-data-slice";

describe("allowance_temp_work slice", () => {
  const initialState = {
    status: "loading",
    data: {},
  };

  it("should handle allowanceTempWorkHandler correctly", () => {
    const payload = {};

    // Dispatch the action
    setTableDataAction({ payload });

    // Assert the state has been updated correctly
    expect(initialState.data).toEqual(payload);
    expect(initialState.status).toEqual("loading");
  });
});

describe("test allowance_temp_work slice", () => {
  it("should handle allowanceTempWork<PERSON><PERSON><PERSON> correctly", () => {
    const previousState = {
      status: "loading",
      data: {},
    };

    const action = {
      type: setTableDataAction.type,
      payload: {},
    };
    const nextState = tableDataSlice.reducer(previousState, action);
    expect(nextState.data).toEqual({ tableData: {} });
  });
  it("should handle allowanceTempWorkHandler correctly when data is null", () => {
    const previousState = {
      status: "loading",
      data: null,
    };

    const action = {
      type: setTableDataAction.type,
      payload: {},
    };
    const nextState = tableDataSlice.reducer(previousState, action);
    expect(nextState.data).toEqual(null);
  });
  it("should handle setFilteredAndSortedIdsAction", () => {
    const previousState = {
      status: "loading",
      data: null,
    };

    const action = {
      type: setFilteredAndSortedIdsAction.type,
      payload: {},
    };
    const nextState = tableDataSlice.reducer(previousState, action);
    expect(nextState.data).toEqual(null);
  });
  it("should handle updateIndividualItemAction when data is null", () => {
    const previousState = {
      status: "loading",
      data: null,
    };

    const action = {
      type: updateIndividualItemAction.type,
      payload: {
        itemId: "a1",
        vendorIndex: 1,
        amount: "0",
        uom: "VENDOR",
        switchValue: "switch",
      },
    };
    const nextState = tableDataSlice.reducer(previousState, action);
    expect(nextState.data).toEqual(null);
  });

  xit("should handle updateIndividualItemAction when data is present", () => {
    const expectedState = {
      filteredAndSortedIds: [1, 2, 3],
      tableData: [1, 2, 3],
    };
    const previousState = {
      status: "loading",
      data: {
        tableData: [1, 2, 3],
        filteredAndSortedIds: [1, 2, 3],
      },
    };
    const action = {
      type: updateIndividualItemAction.type,
      payload: {
        itemId: "a1",
        vendorIndex: 1,
        amount: "0",
        uom: "VENDOR",
        switchValue: "switch",
      },
    };
    const nextState = tableDataSlice.reducer(previousState, action);
    expect(nextState.data).toEqual(expectedState);
  });

  it("should handle updateItemByItemAction when data is null", () => {
    const previousState = {
      status: "loading",
      data: null,
    };

    const action = {
      type: updateItemByItemAction.type,
      payload: {
        itemId: "a1",
        amount: "0",
        uom: "VENDOR",
        switchValue: "switch",
      },
    };
    const nextState = tableDataSlice.reducer(previousState, action);
    expect(nextState.data).toEqual(null);
  });

  xit("should handle updateItemByItemAction when data is present", () => {
    const expectedState = {
      filteredAndSortedIds: [1, 2, 3],
      tableData: [1, 2, 3],
    };
    const previousState = {
      status: "loading",
      data: {
        tableData: [1, 2, 3],
        filteredAndSortedIds: [1, 2, 3],
      },
    };
    const action = {
      type: updateItemByItemAction.type,
      payload: {
        itemId: "a1",
        amount: "0",
        uom: "VENDOR",
        switchValue: "switch",
      },
    };
    const nextState = tableDataSlice.reducer(previousState, action);
    expect(nextState.data).toEqual(expectedState);
  });

  xit("should handle updateCaseAmountOnSwitch when data is null", () => {
    const previousState = {
      status: "loading",
      data: null,
    };
    const action = {
      type: updateCaseAmountOnSwitch.type,
      payload: {
        itemId: "a1",
        amount: "0",
        uom: "VENDOR",
        switchValue: "switch",
      },
    };
    const nextState = tableDataSlice.reducer(previousState, action);
    expect(nextState.data).toEqual(null);
  });

  xit("should update updateCaseAmountOnSwitch when data is null", () => {
    const expectedState = {
      filteredAndSortedIds: [1, 2, 3],
      tableData: [1, 2, 3],
    };
    const previousState = {
      status: "loading",
      data: {
        tableData: [1, 2, 3],
        filteredAndSortedIds: [1, 2, 3],
      },
    };
    const action = {
      type: updateCaseAmountOnSwitch.type,
      payload: {
        itemId: "a1",
        amount: "0",
        uom: "VENDOR",
        switchValue: "switch",
      },
    };
    const nextState = tableDataSlice.reducer(previousState, action);
    expect(nextState.data).toEqual(expectedState);
  });

  it("should handle updateItemByDistributorAction is data is null", () => {
    const previousState = {
      status: "loading",
      data: null,
    };
    const action = {
      type: updateItemByDistributorAction.type,
      payload: {
        itemId: "a1",
        amount: "0",
        uom: "VENDOR",
        switchValue: "switch",
      },
    };
    const nextState = tableDataSlice.reducer(previousState, action);
    expect(nextState.data).toEqual(null);
  });

  it("should handle updateAllItemsAction if data is null", () => {
    const previousState = {
      status: "loading",
      data: null,
    };
    const action = {
      type: updateAllItemsAction.type,
      payload: {
        amount: "0",
        uom: "VENDOR",
        switchValue: "switch",
      },
    };
    const nextState = tableDataSlice.reducer(previousState, action);
    expect(nextState.data).toEqual(null);
  });
  xit("should handle updateAllItemsAction if data is present", () => {
    const expectedState = {
      filteredAndSortedIds: [1, 2, 3],
      tableData: [1, 2, 3],
    };
    const previousState = {
      status: "loading",
      data: {
        tableData: [1, 2, 3],
        filteredAndSortedIds: [1, 2, 3],
      },
    };
    const action = {
      type: updateAllItemsAction.type,
      payload: {
        amount: "0",
        uom: "VENDOR",
        switchValue: "switch",
      },
    };
    const nextState = tableDataSlice.reducer(previousState, action);
    expect(nextState.data).toEqual(expectedState);
  });
  it("should handle setIsUpdateAllChange", () => {
    const expectedState = {
      filteredAndSortedIds: [1, 2, 3],
      tableData: [1, 2, 3],
    };
    const previousState = {
      status: "loading",
      data: {
        tableData: [1, 2, 3],
        filteredAndSortedIds: [1, 2, 3],
      },
    };
    const action = {
      type: setIsUpdateAllChange.type,
      payload: {},
    };
    const nextState = tableDataSlice.reducer(previousState, action);
    expect(nextState.data).toEqual(expectedState);
  });
});

describe("your-utils-file", () => {
  describe("tableDataSlice actions", () => {
    it("should create setTableDataAction", () => {
      const payload = [
        { id: 1, name: "Item 1" },
        { id: 2, name: "Item 2" },
      ];
      const action = setTableDataAction(payload);

      expect(action.type).toBe("allowanceTableData_rn/setTableDataAction");
      expect(action.payload).toEqual(payload);
    });

    it("should create updateIndividualItemAction", () => {
      const payload = {
        itemId: 1,
        vendorIndex: 0,
        amount: 10,
        uom: "unit",
        switchValue: true,
      };
      const action = updateIndividualItemAction(payload);

      expect(action.type).toBe(
        "allowanceTableData_rn/updateIndividualItemAction"
      );
      expect(action.payload).toEqual(payload);
    });

    // Add similar test cases for other actions...
  });
});

describe('isUpdateAllClickedSlice', () => {
  let store;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        isUpdateAllClicked: isUpdateAllClickedSlice.reducer,
      },
    });
  });

  it('should handle initial state correctly', () => {
    const initialState = store.getState().isUpdateAllClicked;
    expect(initialState.status).toEqual('loading');
    expect(initialState.data.isUpdateAll).toEqual(false);
  });

  it('should handle setIsUpdateAllChange correctly', () => {
    const testData = { isUpdateAll: true };

    store.dispatch(setIsUpdateAllChange(testData));

    const updatedState = store.getState().isUpdateAllClicked;
    expect(updatedState.data).toEqual(testData);
    expect(updatedState.status).toEqual('finished');
  });
});