import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import Accordion<PERSON>tom from "./accordion-atom";
import { useSelectorWrap } from "@me/data-rtk";

// Mocking the required dependencies
jest.mock("@me/data-rtk", () => ({
  useSelectorWrap: jest.fn() as jest.Mock, // Explicitly cast to jest.Mock to allow mockReturnValue
}));

jest.mock("@material-tailwind/react", () => ({
  Accordion: ({ children }) => <div>{children}</div>,
  AccordionHeader: ({ children, onClick }) => (
    <div onClick={onClick}>{children}</div>
  ),
  AccordionBody: ({ children }) => <div>{children}</div>,
}));

jest.mock("lucide-react", () => ({
  ChevronRight: ({ className }) => <div className={className}></div>,
}));

describe("AccordionAtom Component", () => {
  const division = {
    divisionId: "1",
    divisionName: "Division 1",
  };

  const children = <div>Child Content</div>;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render the Accordion correctly with the passed props", async () => {
    // Mocking the useSelectorWrap hook to return dummy data
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: {
        allDivisionStepsData: {
          "1": {
            selectedList: ["Vendor1"],
          },
        },
      },
    });

    render(
      <AccordionAtom
        division={division}
        children={children}
        isExpandAll={false}
      />
    );

    expect(screen.getByText("1 - Division 1")).not.toBeNull();

    // Simulate clicking to open the accordion
    fireEvent.click(screen.getByText("1 - Division 1"));

    // Ensure the child content is rendered after clicking the accordion header
    const childContent = await screen.findByText("Child Content");
    expect(childContent).not.toBeNull();
  });

  it("should set openAccordion to true if division has selected vendors", async () => {
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: {
        allDivisionStepsData: {
          "1": {
            selectedList: ["Vendor1"],
          },
        },
      },
    });

    render(
      <AccordionAtom
        division={division}
        children={children}
        isExpandAll={false}
      />
    );

    // Simulate clicking to open the accordion
    fireEvent.click(screen.getByText("1 - Division 1"));

    // Check if the child content appears
    const childContent = await screen.findByText("Child Content");
    expect(childContent).not.toBeNull();
  });

  it("should set openAccordion based on the isExpandAll prop", async () => {
    render(
      <AccordionAtom
        division={division}
        children={children}
        isExpandAll={true}
      />
    );

    // Check if the accordion is open based on the isExpandAll prop
    const childContent = await screen.findByText("Child Content");
    expect(childContent).not.toBeNull();
  });

  it("should toggle accordion open/close when clicked", async () => {
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: {
        allDivisionStepsData: {
          "1": {
            selectedList: ["Vendor1"],
          },
        },
      },
    });

    render(
      <AccordionAtom
        division={division}
        children={children}
        isExpandAll={false}
      />
    );

    const accordionHeader = screen.getByText("1 - Division 1");
    fireEvent.click(accordionHeader); // Open the accordion

    // Ensure that child content is visible after the click
    const childContentOpen = await screen.findByText("Child Content");
    expect(childContentOpen).not.toBeNull();

    // Now close the accordion
    fireEvent.click(accordionHeader);
    await waitFor(() => expect(screen.queryByText("Child Content")).toBeNull());
  });

  it("should not open the accordion if there are no selected vendors", async () => {
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: {
        allDivisionStepsData: {
          "1": {
            selectedList: [],
          },
        },
      },
    });

    render(
      <AccordionAtom
        division={division}
        children={children}
        isExpandAll={false}
      />
    );

    // Ensure the accordion remains closed
    await waitFor(() => expect(screen.queryByText("Child Content")).toBeNull());
  });

  it("should handle empty allDivisionStepsData gracefully", async () => {
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: {
        allDivisionStepsData: {},
      },
    });

    render(
      <AccordionAtom
        division={division}
        children={children}
        isExpandAll={false}
      />
    );

    // Ensure that no child content appears due to empty data
    await waitFor(() => expect(screen.queryByText("Child Content")).toBeNull());
  });

  it("should handle divisionId missing from allDivisionStepsData gracefully", async () => {
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: {
        allDivisionStepsData: {
          "2": {
            selectedList: ["Vendor2"],
          },
        },
      },
    });

    render(
      <AccordionAtom
        division={division}
        children={children}
        isExpandAll={false}
      />
    );

    // Ensure that no child content appears for a missing divisionId
    await waitFor(() => expect(screen.queryByText("Child Content")).toBeNull());
  });
});
