import Checkbox from "@albertsons/uds/molecule/Checkbox";
import React from "react";

const SelectionItemsList = ({
  configObj,
  updatedData,
  onChangeInput,
  selectedData,
}) => {
  const { uniqueId, type, displayKey, id } = configObj;
  const selectedSet = new Set(selectedData);

  const renderItemsList = (item, index) => {
    const label = item?.[displayKey];
    const value = item?.[uniqueId];
    const isChecked = selectedSet?.has(value);

    return (
      <div
        id={`${id}-${item[uniqueId]}`}
        className="py-3 flex cursor-pointer text-[#5A697B] itemWrap flex gap-1"
        style={{ lineHeight: "20px", fontWeight: 400 }}
        key={`${index}-${item[uniqueId]}`}
      >
        <Checkbox
          data-testid="filter-checkbox"
          id={`abs-selection-item-list-checkbox-${item[uniqueId]}-${index}`}
          className="w-4 h-4 cursor-pointer"
          size={10}
          value={value}
          checked={isChecked}
          onChange={e => onChangeInput(e, item)}
          type={type}
          aria-checked={isChecked}
        />
        <span
          className="ml-2 text-ellipsis inline-block overflow-hidden cursor-pointer text-dark-text"
          title={label}
        >
          {label}
        </span>
      </div>
    );
  };
  return (
    <div
      id={`abs-selection-item-list`}
      className="filter-item-list max-h-[250px] overflow-auto w-full"
    >
      {updatedData?.map(renderItemsList)}
    </div>
  );
};

export default React.memo(SelectionItemsList);
