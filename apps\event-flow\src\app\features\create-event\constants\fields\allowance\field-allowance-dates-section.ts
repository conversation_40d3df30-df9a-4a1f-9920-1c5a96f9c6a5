import efConstants from "../../../../../shared/ef-constants/ef-constants";
import { getUpdatedYearBasedOnCurrentDate } from "../../../service/event-details/event-detail-service";
const { ALLOWANCE_TYPES } = efConstants;

export const ALLOWANCE_DATES = {
  allowanceSpecificFields: {
    [ALLOWANCE_TYPES.CASE.key]: {
      DSD: {
        key: "DSD_LEAD_DISTRIBUTORS",
        routeKey: "DSD_LEAD_DISTRIBUTORS",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
        },
      },
      WAREHOUSE: {
        key: "WAREHOUSE_DIST_CENTERS",
        routeKey: "WAREHOUSE_DIST_CENTERS",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
        },
      },
      COMBINED: {
        key: "DSD_WHSE_RETAIL_DIVISION",
        routeKey: "DSD_WHSE_RETAIL_DIVISION",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
        },
      },
    },
    [ALLOWANCE_TYPES.SCAN.key]: {
      DSD: {
        key: "DSD_LEAD_DISTRIBUTORS",
        routeKey: "DSD_LEAD_DISTRIBUTORS",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
        },
      },
      WAREHOUSE: {
        key: "DSD_WHSE_RETAIL_DIVISION",
        routeKey: "DSD_WHSE_RETAIL_DIVISION",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
        },
      },
    },
    [ALLOWANCE_TYPES.SHIPTOSTORE.key]: {
      DSD: {
        key: "DSD_LEAD_DISTRIBUTORS",
        routeKey: "DSD_LEAD_DISTRIBUTORS",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
        },
      },
      WAREHOUSE: {
        key: "DSD_WHSE_RETAIL_DIVISION",
        routeKey: "DSD_WHSE_RETAIL_DIVISION",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
        },
      },
    },
    [ALLOWANCE_TYPES.HEADERFLAT.key]: {
      DSD: {
        key: "DSD_WHSE_RETAIL_DIVISION",
        routeKey: "DSD_WHSE_RETAIL_DIVISION",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
        },
      },
      WAREHOUSE: {
        key: "WAREHOUSE_DIST_CENTERS",
        routeKey: "WAREHOUSE_DIST_CENTERS",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
        },
      },
    },
    [ALLOWANCE_TYPES.ITEMFLAT.key]: {
      DSD: {
        key: "DSD_WHSE_RETAIL_DIVISION",
        routeKey: "DSD_WHSE_RETAIL_DIVISION",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
        },
      },
      WAREHOUSE: {
        key: "WAREHOUSE_DIST_CENTERS",
        routeKey: "WAREHOUSE_DIST_CENTERS",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
        },
      },
    },
    DEFAULT: {
      key: "DSD_WHSE_RETAIL_DIVISION",
      routeKey: "DSD_WHSE_RETAIL_DIVISION",
      allowanceDatesData: {
        registerKeyName: "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
      },
    },
  },
  vehicleTypeOrCustomDate: {
    label: "Vehicle Type/Custom Date",
    required: true,
    registerField: "allowanceCreationVehicle.vehicleTypeOrCustomDate",
    gqlQueryConst: "allowanceCreationVehicle",
    default: "Weekly Insert",
    type: "select",
    apiUrl: "",
    slice: "",
    displayLabel: "name",
    options: [
      {
        id: 1,
        name: "Weekly Insert",
      },
      {
        id: 2,
        name: "Monthly Insert",
      },
      {
        id: 3,
        name: "Yearly Insert",
      },
    ],
    errors: {
      required: {
        text: "Vehicle Type/Custom Date is Required",
      },
    },
    mapperKey: "offerallowances.allowances.vehicle.vehicleType.vehicleTypDesc",
  },
  year: {
    label: "Year",
    required: true,
    disabled: true,
    registerField: "allowanceCreationVehicle.year",
    default: "2023",
    type: "select",
    apiUrl: "",
    slice: "",
    displayLabel: "name",
    options: [
      {
        name: getUpdatedYearBasedOnCurrentDate(Date.now())[0], //adds 2 weeks to the current date and returns year
        id: 1,
      },
      {
        name: getUpdatedYearBasedOnCurrentDate(Date.now())[1],
        id: 2,
      },
      {
        name: getUpdatedYearBasedOnCurrentDate(Date.now())[2],
        id: 3,
      },
      {
        name: getUpdatedYearBasedOnCurrentDate(Date.now())[3],
        id: 4,
      },
    ],
    errors: {
      required: {
        text: "Year is Required",
      },
    },
  },
  startWeekOrVehicle: {
    label: "Start Week/Vehicle",
    required: true,
    disabled: true,
    registerField: "allowanceCreationVehicle.startWeekOrVehicle",
    optionUrl: "allowanceCreationVehicle.startWeek",
    default: "Week 02 Insert 2023",
    type: "select",
    apiUrl: "",
    slice: "",
    displayLabel: "vehicleNm",
    options: [
      {
        id: "",
        vehicleNm: "",
      },
    ],
    errors: {
      required: {
        text: "Start Week/Vehicle is Required",
      },
    },
    mapperKey: "offerallowances.allowances.vehicle.vehicleNm",
  },
  vehicleStart: {
    label: "Vehicle Start",
    required: true,
    disabled: true,
    isAllowFlow: true,
    registerField: "allowanceCreationVehicle.vehicleStart",
    registerKeyName: "vehicleStart",
    type: "date",
    displayLabel: "name",
    errors: {
      required: {
        text: "Allowance Start Required",
      },
    },
    mapperKey: "offerallowances.allowances.vehicle.startDate",
    registerKey: "vehicleStartDate",
  },
  vehicleEnd: {
    label: "Vehicle End",
    required: true,
    disabled: true,
    isAllowFlow: true,
    registerField: "allowanceCreationVehicle.vehicleEnd",
    registerKeyName: "vehicleEnd",
    type: "date",
    displayLabel: "name",
    errors: {
      required: {
        text: "Allowance End Required",
      },
    },
    mapperKey: "offerallowances.allowances.vehicle.endDate",
    registerKey: "vehicleEndDate",
  },
  orderStart: {
    label: "Order Start",
    required: true,
    disabled: true,
    isAllowFlow: true,
    registerField: "allowanceCreationVehicle.orderStart",
    type: "date",
    errors: {
      required: {
        text: "Order start Required",
      },
      invalid: {
        text: "Invalid Order Start",
      },
    },
    mapperKey: "offerallowances.allowances.orderStartDate",
    registerKey: "orderStartDate",
  },
  orderEnd: {
    label: "Order End",
    required: true,
    disabled: true,
    isAllowFlow: true,
    registerField: "allowanceCreationVehicle.orderEnd",
    type: "date",
    errors: {
      required: {
        text: "Order End Required",
      },
      invalid: {
        text: "Invalid Order End",
      },
    },
    mapperKey: "offerallowances.allowances.orderEndDate",
    registerKey: "orderEndDate",
  },
  arrivalStart: {
    label: "Arrival Start",
    required: true,
    disabled: true,
    isAllowFlow: true,
    registerField: "allowanceCreationVehicle.arrivalStart",
    type: "date",
    errors: {
      required: {
        text: "Arrival Start Required",
      },
      invalid: {
        text: "Invalid Arrival Start",
      },
    },
    mapperKey: "offerallowances.allowances.arrivalStartDate",
    registerKey: "arrivalStartDate",
  },
  arrivalEnd: {
    label: "Arrival End",
    required: true,
    disabled: true,
    isAllowFlow: true,
    registerField: "allowanceCreationVehicle.arrivalEnd",
    type: "date",
    errors: {
      required: {
        text: "Arrival End Required",
      },
      invalid: {
        text: "Invalid Arrival End",
      },
    },
    mapperKey: "offerallowances.allowances.arrivalEndDate",
    registerKey: "arrivalEndDate",
  },
  storeReceivingStart: {
    label: "Shipment Start",
    required: true,
    disabled: true,
    isAllowFlow: true,
    registerField: "allowanceCreationVehicle.storeReceivingStart",
    type: "date",
    errors: {
      required: {
        text: "Shipment Start Required",
      },
      invalid: {
        text: "Invalid Shipment Start",
      },
    },
    mapperKey: "offerallowances.allowances.storeReceivingStartDate",
    registerKey: "shipStartDate",
  },
  storeReceivingEnd: {
    label: "Shipment End",
    required: true,
    disabled: true,
    isAllowFlow: true,
    registerField: "allowanceCreationVehicle.storeReceivingEnd",
    type: "date",
    errors: {
      required: {
        text: "Shipment End Required",
      },
      invalid: {
        text: "Invalid Shipment End",
      },
    },
    mapperKey: "offerallowances.allowances.storeReceivingEndDate",
    registerKey: "shipEndDate",
  },
  startDate: {
    label: "Start Date",
    required: true,
    disabled: true,
    isAllowFlow: true,
    registerField: "allowanceCreationVehicle.startDate",
    type: "date",
    errors: {
      required: {
        text: "Start Date Required",
      },
      invalid: {
        text: "Invalid Start Date",
      },
    },
    mapperKey: "offerallowances.allowances.orderStartDate",
    registerKey: "orderStartDate",
  },
  endDate: {
    label: "End Date",
    required: true,
    disabled: true,
    isAllowFlow: true,
    registerField: "allowanceCreationVehicle.endDate",
    type: "date",
    errors: {
      required: {
        text: "End Date Required",
      },
      invalid: {
        text: "Invalid End Date",
      },
    },
    mapperKey: "offerallowances.allowances.orderEndDate",
    registerKey: "orderEndDate",
  },
  errors: {
    EMPTY_ALLOWANCES:
      "Invalid combination of Promo Product Groups and Store Groups. Please update Promo Product Groups or Store Groups to continue.",
    INVALID_DATES: {
      SCAN: {
        all: "Invalid Date combination, End date must be >= Start date",
        vehicle: "Allowance Dates must overlap Vehicle Dates by at least 1 day",
      },
      CASE: {
        orderStart:
          "Invalid Date combination, Arrival Start Date must be >= Order Start date",
        orderEnd:
          "Invalid Date combination, Arrival End Date must be >= Order End date",
        order:
          "Invalid Date combination, Order End date must be >= Order Start date",
        arrival:
          "Invalid Date combination, Arrival End date must be >= Arrival Start date",
        vehicle: "Allowance Dates must overlap Vehicle Dates by at least 1 day",
        ship: "Invalid Date combination, Arrival End date must be >= Arrival Start date",
      },
      SHIP_TO_STORE: {
        ship: "Invalid Date combination, Arrival End date must be >= Arrival Start date",
        arrival:
          "Invalid Date combination, Arrival End date must be >= Arrival Start date",
        vehicle: "Allowance Dates must overlap Vehicle Dates by at least 1 day",
      },
      OTHER: "Invalid Date combination, End date must be >= Start date",
    },
  },
  allowanceCreationVehicle: {
    registerKeyName: "allowanceCreationVehicle",
  },
  vehicleRegField: {
    registerField: "allowanceCreationVehicle.vehicleRef",
  },
  initialText: {
    SCAN: {
      DSD_WHSE_RETAIL_DIVISION: {
        header:
          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
      },
      DSD_LEAD_DISTRIBUTORS: {
        header:
          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
      },
    },
    CASE: {
      DSD_LEAD_DISTRIBUTORS: {
        header:
          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than that of the full Event. These dates will be used for O/I and/or Billing.",
        bulletPointsText:
          "Based on ((Albertsons Accounting Policies,)) the dates for DSD Case Allowances will be pre-calculated as:",
        bulletPoints: [
          "DSD Arrival Start: set to be 2 days prior to Vehicle Start",
          "DSD Arrival End: set to match the Vehicle End",
          "If an exception to these default dates is necessary due to either other Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
        ],
      },
      WAREHOUSE_DIST_CENTERS: {
        header:
          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for O/I and/or Billing.",
        bulletPointsText:
          "Based on ((Albertsons Accounting Policies,)) the dates for Warehouse Case Allowances will be pre-calculated as:",
        bulletPoints: [
          "Order Start: Arrival Start Date - Lead Time* ",
          "Order End: Arrival End Date - Lead Time*",
          "Arrival Start: 10 days prior to Vehicle Start. 16 days in Northern California due to Hawaii, 21 days in Alaska.",
          "Arrival End: Vehicle End date",
          "For Displayer items only, an additional 14 days will be incorporated into the Order Start and Arrival Start date calculations",
        ],
        footer:
          "* As setup in the Albertsons buying system to represent total time in days from P.O. generation through warehouse receiving. If an exception to these default dates is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
      },
      DSD_WHSE_RETAIL_DIVISION: {
        header:
          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than that of the full Event. These dates will be used for O/I and/or Billing.",
        bulletPointsText:
          "Based on ((Albertsons Accounting Policies,)) the dates for DSD Case Allowances will be pre-calculated as:",
        bulletPoints: [
          "DSD Arrival Start: set to be 2 days prior to Vehicle Start",
          "DSD Arrival End: set to match the Vehicle End",
          "If an exception to these default dates is necessary due to either other Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
        ],
      },
    },
    SHIP_TO_STORE: {
      DSD_WHSE_RETAIL_DIVISION: {
        header:
          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period shorter than the full Event.",
        subHeader:
          "Based on ((Albertsons Accounting Policies,)) the dates for Ship To Store Allowances are not tied only to the Vehicle / Performance.  These allowances are earned upon Arrival and have no less than 7 days prior to the Performance start and 3 days after the end date.  14 Days prior for Displays.",
        footer:
          "If an exception to these defaults is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
      },
      DSD_LEAD_DISTRIBUTORS: {
        header:
          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period shorter than the full Event.",
        subHeader:
          "Based on ((Albertsons Accounting Policies,)) the dates for Ship To Store Allowances are not tied only to the Vehicle / Performance.  These allowances are earned upon Arrival and have no less than 7 days prior to the Performance start and 3 days after the end date.  14 Days prior for Displays.",
        footer:
          "If an exception to these defaults is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
      },
    },
    HEADER_FLAT: {
      DSD_WHSE_RETAIL_DIVISION: {
        header:
          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
      },
      WAREHOUSE_DIST_CENTERS: {
        header:
          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
      },
    },
    ITEM_FLAT: {
      DSD_WHSE_RETAIL_DIVISION: {
        header:
          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
      },
      WAREHOUSE_DIST_CENTERS: {
        header:
          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
      },
    },
  },
};
