import Button from "@albertsons/uds/molecule/Button";
import { useSelectorWrap } from "@me/data-rtk";
import { cloneDeep, isArray, isEmpty } from "lodash";
import { useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import efConstants from "../../../../../../../../shared/ef-constants/ef-constants";
import "../../../../../../../create-event/components/edit-field-highlight.scss";
import LoadingSpinner from "../../../../../../../create-event/constants/LoadingSpinner/LoadingSpinner";
import useAllowanceTempUpdate from "../../../../../../../create-event/hooks/useAllowanceTempUpdate";
import {
  getAllowanceFormRegister<PERSON>ey,
  getAllowance<PERSON>ap<PERSON><PERSON>,
  get<PERSON><PERSON><PERSON><PERSON>,
  getVehicleDatesMapData,
  saveAllowanceFormData,
} from "../../../../../../service/allowance/allowance-service";
import { getObjectKeys } from "../../../../../../service/allowance/allowance-stepper-service";
import {
  useGetAllowancesItemsQuery,
  useLazyGetAllowancesItemsQuery,
  usePostAllowanceStartDateDataMutation,
} from "../../../../../../service/apis/allowance-api";
import AllowanceFormWrapper from "../../../../common/allowance-form-wrapper";
import AllowanceDatesInitialText from "./allowance-dates-initial-text";
import BasicAllowanceDates from "./allowance-dates-options/basic-allowance-dates";
import ScanAllowanceDates from "./allowance-dates-options/scan-allowance-dates";
import MultipleDsdAllowanceDates from "./allowance-dates-options/multiple-dsd-allowance-dates";
import MultipleWarehouseAllowanceDates from "./allowance-dates-options/multiple-warehouse-allowance-dates";
import BaseAllowanceDate from "./base-allownace-dates";
import { getDateFormat } from "./allowance-dates-service";
import { FormFieldError } from "@me/util-form-wrapper";

export default function CommonAllowanceDates({
  stepperElement,
  saveAndContinueHanlder,
  offerIndex,
  allowanceIndex,
  step,
  isEditEnable,
  isInvalidDate,
}) {
  const dispatch = useDispatch();
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { data } = useSelectorWrap("allowance_form_data");
  const { addAllowancesDates, addAllowancesAmounts, isLoading } =
    useAllowanceTempUpdate();
  const { allowanceData: tempAllowanceData } = useSelectorWrap(
    "allowance_temp_work"
  ).data;
  const { data: allowancePerfData } = useSelectorWrap(
    "allowance_type_performance_data"
  );
  const { isAllowConvEnable } = useSelectorWrap("allow_conv_status_rn").data;

  const {
    fields: { allowanceSpecificFields, year, errors },
  } = stepperElement;

  const {
      ALLOWANCE_TYPES,
      ALLOWANCE_SCREEN_TYPES,
      PRODUCT_SOURCE_INFO,
      NEW_DIRECTION_FEATURES_FLAGS,
      OFFER_ALLOWANCE_GROUP,
    } = efConstants,
    { SCAN, HEADERFLAT, ITEMFLAT, CASE, SHIPTOSTORE } = ALLOWANCE_TYPES,
    { AO, DP, NDP } = ALLOWANCE_SCREEN_TYPES,
    { HEADER_FLAT } = PRODUCT_SOURCE_INFO,
    { isDirectionalChangesEnable } = NEW_DIRECTION_FEATURES_FLAGS,
    constRegField = getAllowanceFormRegisterKey(offerIndex, allowanceIndex),
    allowanceFormData = data?.allowanceFormData?.[constRegField],
    { allowanceType, createInd } =
      allowanceFormData?.allowanceTypeAndPerformance || {},
    allowanceDatesRegFieldsData =
      allowanceSpecificFields?.[allowanceType]?.[
        getOfferKey(stepperElement?.offerTypeKey, allowanceType, createInd)
      ] || allowanceSpecificFields?.DEFAULT,
    { key } = allowanceDatesRegFieldsData,
    datesInitialValueOnLoad =
      allowanceFormData?.["allowanceCreationVehicle"]?.[key] || {};
  const [isInvalid, setIsInvalid] = useState(!isInvalidDate);
  const [isEmptyData, setIsEmptyData] = useState(false);
  const [isInvalidDates, setIsInvalidDates] = useState(false);
  const allowName = getAllowanceMapKey(tempAllowanceData?.allowanceType) || "";
  const [isDateChanged, setIsDateChanged] = useState(false);
  const [isOrderDatesChanged, setIsOrderDatesChanged] = useState(false);
  const [vehicleFields, setVehicleFields] = useState({
    vehicleTypeId: "",
    customStartDate: "",
    customEndDate: "",
    vehicleType: {},
    vehicleTabType: "",
    vehicleId: "",
    vehicleNm: "",
    vehicleTypeDesc: "",
    year: "",
  });

  const [extraDates, setExtraDates] = useState({});
  const [datesArray, setDatesArray] = useState<any[]>([]);
  const allowances = useRef([]);
  const [dateFieldsChange, setDateFieldsChange] = useState({
    vehicleTypeOrCustomDateChange: false,
    startWeekOrVehicleChange: false,
    vehicleStartChange: false,
    vehicleEndChange: false,
  });
  const [makeAmountsCall, setMakeAmountsCall] = useState(false);
  const [isVehicleDatesChanged, setIsVehicleDatesChanged] = useState(false);
  const [isAmountsApiLoading, setIsAmountsApiLoading] = useState(false);
  const [isAmountsApiInvalid, setIsAmountsApiInvalid] = useState(false);
  const [makeAmountsCallOnDateChange, setMakeAmountsCallOnDateChange] =
    useState(false);
  const count = useRef(0);
  const skipAmountsCall = useRef(true);

  const allowanceAmountsPayload = {
    URL_PARAMS: [eventDetailsData.id, key],
  };
  const [fetchAmountsData, rest] = useLazyGetAllowancesItemsQuery();

  const { data: allowancesResp, isFetching: isAllowanceRespFetched } =
    useGetAllowancesItemsQuery(allowanceAmountsPayload, {
      refetchOnMountOrArgChange: true,
      skip: !tempAllowanceData?.tempWorkAllowanceId || skipAmountsCall.current,
    });

  useEffect(() => {
    if (allowancesResp?.allowances?.length && !isAllowanceRespFetched) {
      allowances.current = allowancesResp?.allowances;
      skipAmountsCall.current = true;
    }
  }, [allowancesResp, isAllowanceRespFetched]);

  const componentMap = {
    SCAN: {
      DSD_WHSE_RETAIL_DIVISION: ScanAllowanceDates,
      DSD_LEAD_DISTRIBUTORS: ScanAllowanceDates,
    },
    CASE: {
      DSD_LEAD_DISTRIBUTORS: MultipleDsdAllowanceDates,
      WAREHOUSE_DIST_CENTERS: MultipleWarehouseAllowanceDates,
      DSD_WHSE_RETAIL_DIVISION: MultipleWarehouseAllowanceDates,
    },
    SHIP_TO_STORE: {
      DSD_LEAD_DISTRIBUTORS: MultipleDsdAllowanceDates,
      DSD_WHSE_RETAIL_DIVISION: MultipleWarehouseAllowanceDates,
    },
    HEADER_FLAT: {
      DSD_WHSE_RETAIL_DIVISION: BasicAllowanceDates,
      WAREHOUSE_DIST_CENTERS: BasicAllowanceDates,
    },
    ITEM_FLAT: {
      DSD_WHSE_RETAIL_DIVISION: BasicAllowanceDates,
      WAREHOUSE_DIST_CENTERS: BasicAllowanceDates,
    },
  };

  const Component = componentMap?.[allowanceType]?.[key];

  const postStartDatePayload = {
    URL_PARAM: eventDetailsData.id,
    queryParams: {
      responseFilter: "refresh",
    },
    division: eventDetailsData.divisionIds,
    promoStartDate: new Date(vehicleFields.customStartDate).toLocaleDateString(
      "en-CA"
    ),
  };

  const [postStartDateData, { isLoading: isPostEventDataloading }] =
    usePostAllowanceStartDateDataMutation();

  const amountRefreshCheck =
    HEADERFLAT.key === allowanceType && checkAmountRefreshCondition();

  const handleOrderDatesChange = async (allowances, vehicleDatesMap) => {
    const updatedDatesRes = await addAllowancesAmounts({
      allowances,
      offerAllowanceGroup: key,
      vehicleDatesMap,
    });
    updatedDatesRes?.data && saveAndContinueHanlder(step);
  };

  const handleInitialStepLoad = async () => {
    if (allowanceType) {
      if (amountRefreshCheck && !isEditEnable) {
        skipAmountsCall.current = false;
      }

      if (
        [HEADERFLAT.key, ITEMFLAT.key].includes(allowanceType) &&
        isEditEnable
      ) {
        skipAmountsCall.current = false;
      } else {
        if ([HEADERFLAT.key, ITEMFLAT.key].includes(allowanceType)) return;
      }

      if (
        [SCAN.key].includes(allowanceType) ||
        (!isEmpty(datesInitialValueOnLoad) &&
          !isDirectionalChangesEnable &&
          !isAllowConvEnable)
      ) {
        setMakeAmountsCall(true);
        return;
      }

      if (!isAllowConvEnable) {
        const result = await saveDatesVehicle(
          cloneDeep({ vehicleRef: eventDetailsData?.eventCreationVehicle })
        );
        result?.["data"] && setMakeAmountsCall(true);
      }
    }
  };

  useEffect(() => {
    handleInitialStepLoad();
  }, [allowanceType]);

  const handleDateChange = async () => {
    if ([SCAN.key, HEADERFLAT.key, ITEMFLAT.key].includes(allowanceType))
      return;
    if (isDateChanged) {
      await makeTempCall();
      setIsDateChanged(false);
    }
  };

  useEffect(() => {
    handleDateChange();
  }, [isDateChanged]);

  async function makeTempCall() {
    const dateRange = {
      startDate: getDateFormat(vehicleFields.customStartDate),
      endDate: getDateFormat(vehicleFields.customEndDate),
    };

    const vehicleObject = {
      ...dateRange,
      vehicleId: vehicleFields.vehicleId,
      vehicleNm: vehicleFields.vehicleNm,
      vehicleType: {
        vehicleTypeId: vehicleFields.vehicleTypeId,
        vehicleTypDesc: vehicleFields.vehicleTypeDesc,
      },
      year: vehicleFields.year,
    };

    const apiObject = {
      dateRange,
      vehicleId: vehicleFields.vehicleId,
      vehicleRef: vehicleObject,
    };

    const vehicleDatesMap = getVehicleDatesMapData(
      key,
      apiObject,
      tempAllowanceData?.allowanceTypeSpecification?.[allowName]
        ?.vehicleDatesMap
    );

    const result = await addAllowancesDates({
      vehicleDatesMap,
      datesArray: [],
      offerAllowanceGroup: key,
      isEditEnable,
    });

    result?.data && setMakeAmountsCallOnDateChange(true);
  }

  function checkExtraDates() {
    return Object.values(extraDates)?.find((value: any) =>
      isArray(value) ? value.length : getObjectKeys(value || {}).length
    );
  }

  useEffect(() => {
    if (checkExtraDates() && isEditEnable && count.current === 0) {
      count.current = count.current + 1;
      saveAllowanceFormData({
        dispatch,
        key: "allowanceCreationVehicle",
        allowanceRegField: constRegField,
        value: {
          ...allowanceFormData?.allowanceCreationVehicle,
        },
        isPreviousDataRequired: true,
        previousData: allowanceFormData,
      });
    }
  }, [extraDates]);

  useEffect(() => {
    if (
      !datesArray?.[0]?.allowanceDateOffsets &&
      [CASE.key, SHIPTOSTORE.key].includes(allowanceType) &&
      !isAmountsApiLoading &&
      isAmountsApiInvalid
    ) {
      setIsEmptyData(true);
    } else setIsEmptyData(false);
  }, [datesArray, isAmountsApiLoading]);

  async function saveDatesVehicle(formData) {
    const vehicleObject = {
      startDate: getDateFormat(formData.vehicleRef?.startDate),
      endDate: getDateFormat(formData.vehicleRef?.endDate),
      vehicleId: formData.vehicleRef?.vehicleId,
      vehicleNm: formData.vehicleRef?.vehicleNm,
      vehicleType: {
        vehicleTypeId: formData.vehicleRef?.vehicleType.vehicleTypeId,
        vehicleTypDesc: formData.vehicleRef?.vehicleType.vehicleTypDesc,
      },
      year: vehicleFields.year,
    };

    const dateRange = {
      startDate: getDateFormat(formData.vehicleRef?.startDate),
      endDate: getDateFormat(formData.vehicleRef?.endDate),
    };

    const apiObject = {
      dateRange,
      vehicleId: vehicleFields.vehicleId,
      vehicleRef: vehicleObject,
    };

    const vehicleDatesMap = getVehicleDatesMapData(
      key,
      apiObject,
      tempAllowanceData?.allowanceTypeSpecification?.[allowName]
        ?.vehicleDatesMap
    );

    return await addAllowancesDates({
      vehicleDatesMap,
      datesArray: [],
      offerAllowanceGroup: key,
      isEditEnable,
    });
  }

  function checkAmountRefreshCondition() {
    const defaultCreateInd =
      allowancePerfData?.performanceConfig?.[
        isEditEnable ? `${constRegField}.edit` : constRegField
      ]?.defaultCreateInd;
    return (
      allowanceType === HEADERFLAT.key &&
      ([DP.key, NDP.key].includes(eventDetailsData?.eventType) ||
        (eventDetailsData?.eventType === AO.key &&
          defaultCreateInd === HEADER_FLAT.DSD.createIndex))
    );
  }

  const getHfAllowanceItems = dateRange => {
    const hfAllowanceItems =
      tempAllowanceData?.allowanceTypeSpecification?.[allowName]
        ?.allowancesMap?.[key] || allowances?.current;
    return hfAllowanceItems?.map(item => {
      return {
        ...item,
        allowanceStartDate: dateRange?.startDate,
        allowanceEndDate: dateRange?.endDate,
        vehicleId: vehicleFields.vehicleId,
      };
    });
  };

  const handleSave = async () => {
    const isHfRefetch = amountRefreshCheck && isEditEnable;
    if (isDateChanged) {
      await postStartDateData(postStartDatePayload);
      if (isHfRefetch) {
        skipAmountsCall.current = false;
      }
    }

    const dateRange = {
      startDate: getDateFormat(vehicleFields.customStartDate),
      endDate: getDateFormat(vehicleFields.customEndDate),
    };

    const vehicleObject = {
      ...dateRange,
      vehicleId: vehicleFields.vehicleId,
      vehicleNm: vehicleFields.vehicleNm,
      vehicleType: {
        vehicleTypeId: vehicleFields.vehicleTypeId,
        vehicleTypDesc: vehicleFields.vehicleTypeDesc,
      },
      year: vehicleFields.year,
    };

    const tempVehicleDatesMap =
      tempAllowanceData?.allowanceTypeSpecification?.[allowName]
        ?.vehicleDatesMap;
    const tempVehicle = tempVehicleDatesMap?.[key];
    const tempVehicleObjet = {
      ...tempVehicle?.dateRange,
      vehicleId: tempVehicle?.vehicleId,
      vehicleNm: vehicleFields.vehicleNm,
      vehicleType: tempVehicle?.vehicleType,
      year: vehicleFields.year,
    };

    const apiObject = isAllowConvEnable
      ? {
          dateRange: tempVehicle?.dateRange,
          vehicleId: tempVehicle?.vehicleId,
          vehicleRef: tempVehicleObjet,
        }
      : {
          dateRange,
          vehicleId: vehicleFields.vehicleId,
          vehicleRef: vehicleObject,
        };

    const vehicleDatesMap = isAllowConvEnable
      ? tempAllowanceData?.allowanceTypeSpecification?.[allowName]
          ?.vehicleDatesMap
      : getVehicleDatesMapData(
          key,
          apiObject,
          tempAllowanceData?.allowanceTypeSpecification?.[allowName]
            ?.vehicleDatesMap
        );

    const vendorsObject = {
      vendorsObject: datesArray,
    };

    const amountsDataObj = amountRefreshCheck
      ? {
          isAmountsIncluded: true,
          allowances: isEditEnable
            ? getHfAllowanceItems(dateRange)
            : allowances.current?.map((item: object) => {
                return {
                  ...item,
                  allowanceStartDate: dateRange?.startDate,
                  allowanceEndDate: dateRange?.endDate,
                  vehicleId: vehicleFields.vehicleId,
                };
              }),
          offerAllowanceGroup: key,
        }
      : {};

    const result = await addAllowancesDates({
      vehicleDatesMap,
      ...amountsDataObj,
      datesArray,
      offerAllowanceGroup: key,
      isEditEnable,
    });
    saveAllowanceFormData({
      dispatch,
      key: "allowanceCreationVehicle",
      allowanceRegField: constRegField,
      value: {
        ...allowanceFormData?.allowanceCreationVehicle,
        [key]: {
          ...apiObject,
          ...vendorsObject,
        },
      },
      isPreviousDataRequired: true,
      previousData: allowanceFormData,
    });
    if (
      (isOrderDatesChanged &&
        allowanceType === CASE.key &&
        [
          OFFER_ALLOWANCE_GROUP.CASE.WAREHOUSE,
          OFFER_ALLOWANCE_GROUP.CASE.COMBINED,
        ].includes(key)) ||
      isHfRefetch
    ) {
      const amountsRes = await fetchAmountsData(allowanceAmountsPayload);
      handleOrderDatesChange(
        amountsRes?.data?.allowances || [],
        isHfRefetch ? vehicleDatesMap : null
      );
    } else if (!isOrderDatesChanged && result?.data) {
      saveAndContinueHanlder(step);
    }
  };

  return (
    <>
      <AllowanceFormWrapper
        defaultValues={{
          year: year.options[0].name,
          startWeekOrVehicle: datesInitialValueOnLoad
            ? datesInitialValueOnLoad?.vehicleRef?.vehicleNm
            : eventDetailsData.eventCreationVehicle?.vehicleNm,
        }}
        handleSave={() => null}
        // eslint-disable-next-line @typescript-eslint/no-empty-function
        getFormControls={() => {}}
        footerProps={{
          label: "Save & Continue",
          visable: false,
        }}
      >
        <>
          <LoadingSpinner
            isLoading={
              isPostEventDataloading ||
              isAllowanceRespFetched ||
              isLoading ||
              rest?.isLoading
            }
            classname="!h-full !w-full rounded-md"
          />

          <AllowanceDatesInitialText
            allowanceType={allowanceType}
            option={key}
            stepperElement={stepperElement}
          />

          <BaseAllowanceDate
            {...{
              stepperElement,
              offerIndex,
              allowanceIndex,
              isDateChanged,
              setIsDateChanged,
              vehicleFields,
              setVehicleFields,
              setIsVehicleDatesChanged,
              dateFieldsChange,
              setDateFieldsChange,
              keyVal: key,
              isInvalidDate,
              setIsInvalid,
              isEditEnable,
              isVehicleDatesChanged,
            }}
          />
        </>
      </AllowanceFormWrapper>

      {(makeAmountsCall || isAllowConvEnable) && Component && (
        <Component
          {...{
            vehicleFields,
            offerIndex,
            allowanceIndex,
            stepperElement,
            setExtraDates,
            isEditEnable,
            isVehicleDatesChanged,
            datesArray,
            setDatesArray,
            keyVal: key,
            setIsInvalid,
            makeAmountsCallOnDateChange,
            setMakeAmountsCallOnDateChange,
            setIsInvalidDates,
            setIsAmountsApiLoading,
            setIsAmountsApiInvalid,
            errors,
            tempAllowItems:
              tempAllowanceData?.allowanceTypeSpecification?.[allowName]
                ?.allowancesMap?.[key],
            setIsOrderDatesChanged,
          }}
        />
      )}

      <div
        className="py-3 pl-[0px]"
        id="abs-common-allowance-dates-save-button-container"
      >
        <Button
          width={180}
          onClick={(event: any) => {
            event?.preventDefault();
            event?.stopPropagation();
            handleSave();
          }}
          disabled={
            (!isDirectionalChangesEnable && !isInvalid) ||
            isInvalidDates ||
            isEmptyData
          }
        >
          {!isEditEnable
            ? stepperElement.create.label
            : stepperElement.edit.label}
        </Button>

        {isEmptyData && <FormFieldError error={errors.EMPTY_ALLOWANCES} />}
      </div>
    </>
  );
}
