import "@testing-library/jest-dom";
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { useController } from "react-hook-form";
import BillingCommentAllSection from "./billing-comment-all-section";

// Mock the useController hook
jest.mock("react-hook-form", () => ({
  ...jest.requireActual("react-hook-form"),
  useController: jest.fn(),
}));

describe("BillingCommentAllSection", () => {
  const mockSetValue = jest.fn();
  const mockOnUpdateAllCommentSubmit = jest.fn();

  const formControlsMock = {
    control: {}, // This will be mocked by useController
    register: jest.fn(),
    setValue: mockSetValue,
  };

  const commonCommentMock = {
    registerField: "commentField",
    value: "Initial comment",
    errors: {
      required: "This field is required.",
    },
  };

  beforeEach(() => {
    // Mock the behavior of useController
    (useController as jest.Mock).mockReturnValue({
      field: {
        value: "Initial comment",
        onChange: jest.fn(),
        onBlur: jest.fn(),
      },
      formState: {
        errors: {},
      },
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders the component with the input and button", () => {
    render(
      <BillingCommentAllSection
        formControls={formControlsMock}
        commonComment={commonCommentMock}
        onUpdateAllCommentSubmit={mockOnUpdateAllCommentSubmit}
        isCommentRequired={false}
      />
    );

    // Check if the input and button are rendered
    expect(screen.getByRole("textbox")).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: /Save Comment & Apply to All/i })
    ).toBeInTheDocument();
  });

  it("calls setValue when the input value changes", () => {
    render(
      <BillingCommentAllSection
        formControls={formControlsMock}
        commonComment={commonCommentMock}
        onUpdateAllCommentSubmit={mockOnUpdateAllCommentSubmit}
        isCommentRequired={false}
      />
    );

    const input = screen.getByRole("textbox");
    fireEvent.change(input, { target: { value: "Updated comment" } });

    expect(mockSetValue).toHaveBeenCalledWith(
      "commentField",
      "Updated comment"
    );
  });

  it("renders the error message when isCommentRequired is true", () => {
    render(
      <BillingCommentAllSection
        formControls={formControlsMock}
        commonComment={commonCommentMock}
        onUpdateAllCommentSubmit={mockOnUpdateAllCommentSubmit}
        isCommentRequired={true}
      />
    );

    // Check if the error message is displayed
    expect(
      screen.getByText(
        "* Selected Performance = Other (99) - Vendor Comment is required."
      )
    ).toBeInTheDocument();
  });

  it("does not render the error message when isCommentRequired is false", () => {
    render(
      <BillingCommentAllSection
        formControls={formControlsMock}
        commonComment={commonCommentMock}
        onUpdateAllCommentSubmit={mockOnUpdateAllCommentSubmit}
        isCommentRequired={false}
      />
    );

    // Check if the error message is not displayed
    expect(
      screen.queryByText(
        "* Selected Performance = Other (99) - Vendor Comment is required."
      )
    ).not.toBeInTheDocument();
  });

  it("calls onUpdateAllCommentSubmit when the button is clicked", () => {
    render(
      <BillingCommentAllSection
        formControls={formControlsMock}
        commonComment={commonCommentMock}
        onUpdateAllCommentSubmit={mockOnUpdateAllCommentSubmit}
        isCommentRequired={false}
      />
    );

    const button = screen.getByRole("button", {
      name: /Save Comment & Apply to All/i,
    });
    fireEvent.click(button);

    expect(mockOnUpdateAllCommentSubmit).toHaveBeenCalled();
  });
});