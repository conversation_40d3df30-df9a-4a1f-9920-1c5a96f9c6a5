import { cloneDeep } from "lodash";
import efConstants from "../../../shared/ef-constants/ef-constants";
import { formatAmount } from "../../create-event/service/allowance/allowance-service";
import { toFixedIfNecessary } from "../allowances-table-service";
import { setAmoutAndNewCost } from "./allowance-table-data-utils";
import { appConstants } from "@me/utils-root-props";
import { isFeatureFlagEnabled, isVendorLoggedIn } from "@me-upp-js/utilities";

const {
  TRANSFORMED_VENDOR_KEY,
  TRANSFORMED_VENDOR_NAME,
  COST_AREA_DESC,
  CREATE_ALLOW_IND,
  INCLUDE_IND,
  ALLOWANCE_HF_AMT_KEY,
  LEAD_DIST_IND,
  SWITCH_OPTIONS,
  CASE_WHSE_CREATE_IND,
} = efConstants;
export const transformAllowancesData = (
  vendorsData,
  headerOnlyAmt = null,
  isDataFromTemp = false,
  isEdit = false,
  allowanceType,
  isSummary = false,
  allowIndValue = false,
  skipCalculateNewFinalCostInTable = false,
  isDsdOffer = false,
  isAllowTypeChange = false,
  isAllSummary = false
) => {
  const itemsKey = "allowanceItems";
  const currentValueKey = allowIndValue ? "allowancePending" : "allowance",
    oldValueKey = allowIndValue ? "allowance" : "allowancePending";
  const isCsdFeatureEnabled = isFeatureFlagEnabled(
    appConstants.FEATURE_FLAGS.CSD_VENDOR_ACCESS
  );

  const result: any = [];

  const getItemById = itemId => {
    return result.find(item => item.itemId === itemId);
  };

  const isAllLeadIndFalse = vendorsData?.every(
    vendor => !vendor?.leadDistributorInd
  );
  const updateVendorDetails = (item, vendor, v_index, itemData) => {
    const newVendor = {
      allowanceBillingInfo: vendor?.allowanceBillingInfo,
      vendorNbr: vendor[TRANSFORMED_VENDOR_KEY],
      vendorName: vendor[TRANSFORMED_VENDOR_NAME],
      costAreaDesc: vendor[COST_AREA_DESC],
      createAllowInd: vendor[CREATE_ALLOW_IND],
      includeInd: vendor[INCLUDE_IND],
      leadDistributorInd:
        isDsdOffer &&
        (!isEdit || isAllowTypeChange) &&
        isAllLeadIndFalse &&
        !isSummary
          ? true
          : vendor[LEAD_DIST_IND],
      leadDistributorMode: vendor["leadDistributorMode"] || null,
      leadDistributorInfos: vendor["leadDistributorInfos"],
      headerFlatAmt: vendor[ALLOWANCE_HF_AMT_KEY],
      isSharedWhse: vendor?.isSharedWhse,
      sharedWhse: vendor?.sharedWhse,
      distCenter: isAllSummary
        ? vendor?.location.distCenter
        : vendor?.distCenter,
      isAllSummary,
      itemLevelModCommand: itemData?.modCommand,
      modCommand: vendor?.allowanceItems?.some(
        item =>
          item?.modCommand ===
          (isVendorLoggedIn()
            ? efConstants.ZERO_COST_TEXT
            : efConstants.SKIP_COST_TEXT)
      )
        ? efConstants.ZERO_COST_TEXT
        : efConstants.MOD_COMMAND_NONE,
    };
    efConstants.VENDOR_KEYS_ARR.forEach(key => {
      newVendor[key] = itemData[key];
    });
    item.vendorDetails[v_index] = newVendor;
  };

  const getShippingCost = item => {
    const { vendorPackConversionFactor = 1, caseListCost } = item;
    // TODO : In coming stories, need to set shipping cost by dividing vendorPackConversionFactor for item Flat
    return caseListCost ? toFixedIfNecessary(caseListCost) : "";
  };
  vendorsData?.forEach((vendor, v_index) => {
    const vendorItems = isSummary ? vendor?.[currentValueKey] : vendor;
    vendorItems?.[itemsKey]?.forEach((itemData, itemIndex) => {
      const itemId = itemData.itemId;

      const { cost: unitCost, costAllow: unitCostAllow } =
        itemData[SWITCH_OPTIONS[allowanceType]?.dataMapping?.Unit?.key] || {};

      const { cost: caseCost, costAllow: caseCostAllow } =
        itemData[SWITCH_OPTIONS[allowanceType]?.dataMapping?.Case?.key] || {};

      // to get new newCaseCostAllow and newUnitCostAllow for each individual cell
      const objectToGetNewCosts = cloneDeep({
        allowType: allowanceType,
        amount: skipCalculateNewFinalCostInTable
          ? 0
          : isAllSummary
          ? itemData?.allowanceItemComps?.[0]?.allowAmount
          : itemData?.allowanceAmount,
        packWhse: itemData?.packWhse,
        vendorObj: {
          caseCostAllow,
          unitCostAllow,
          // dummy values to reuse the setAmoutAndNewCost fnc
          includeInd: true,
          newCaseCostAllow: 0,
          newUnitCostAllow: 0,
        },
        vendorPackConversionFactor: itemData?.vendorPackConversionFactor,
      });
      setAmoutAndNewCost(objectToGetNewCosts);

      const item = getItemById(itemId);
      const itemObj = item
        ? item
        : {
            ...itemData,
            distCenter: vendorItems?.distCenter,
            createInd: vendorItems?.createInd,
            vendorDetails: [],
          };

      updateVendorDetails(itemObj, vendorItems, v_index, {
        ...itemData,
        caseListCost: caseCost,
        unitListCost: unitCost,
        newCaseCostAllow: toFixedIfNecessary(
          objectToGetNewCosts?.vendorObj?.newCaseCostAllow
        ),
        newUnitCostAllow: toFixedIfNecessary(
          objectToGetNewCosts?.vendorObj?.newUnitCostAllow
        ),
        unitCostAllow: toFixedIfNecessary(unitCostAllow),
        caseCostAllow: toFixedIfNecessary(caseCostAllow),
        shippingCost: getShippingCost(itemObj),
        allowanceAmount:
          isDataFromTemp || headerOnlyAmt
            ? formatAmount(itemData?.allowanceAmount || 0)
            : "",
        ...(isAllSummary && {
          allowUomType: itemData?.uom,
          allowanceAmount: itemData?.allowanceItemComps?.[0]?.allowAmount,
          currentHeaderAmtValue: vendor?.headerFlatAmt,
        }),
        ...(isSummary && {
          prevAllowAmtValue:
            vendor?.[oldValueKey]?.[itemsKey]?.[itemIndex]?.allowanceAmount ||
            "",
          prevHeaderAmtValue: vendor?.[oldValueKey]?.headerFlatAmt,
          currentHeaderAmtValue: vendor?.[currentValueKey]?.headerFlatAmt,
          updateUser: vendor?.[currentValueKey]?.updateUser,
          prevUomValue:
            vendor?.[oldValueKey]?.[itemsKey]?.[itemIndex]?.allowUomType || "",
          pendingChangeFlag: allowIndValue,
        }),
      });

      if (!item) {
        result.push(itemObj);
      }
    });
  });

  /**
   * Below, we need to fill the empty spaces in vendor details array to identify this is a empty vendor in table
   */
  const updatedResultObj = result?.map(itemObj => {
    for (let i = 0; i < vendorsData?.length; i++) {
      const isVendorObj = itemObj?.vendorDetails?.[i];
      if (!isVendorObj) {
        const vendorObj =
          isSummary && currentValueKey
            ? vendorsData?.[i]?.[currentValueKey]
            : vendorsData[i];
        itemObj.vendorDetails[i] = {
          isEmptyVendor: true,
          ...vendorObj,
          leadDistributorInd: vendorObj?.leadDistributorInd,
          leadDistributorMode: vendorObj?.leadDistributorMode || null,
          leadDistributorInfos: vendorObj?.["leadDistributorInfos"],
          isSharedWhse: vendorObj?.isSharedWhse,
          sharedWhse: vendorObj?.sharedWhse,
          itemLevelModCommand: itemObj?.modCommand,
          isAllSummary,
          modCommand: vendorObj?.allowanceItems?.some(
            item =>
              item?.modCommand ===
              (isVendorLoggedIn()
                ? efConstants.ZERO_COST_TEXT
                : efConstants.SKIP_COST_TEXT)
          )
            ? efConstants.ZERO_COST_TEXT
            : efConstants.MOD_COMMAND_NONE,
        };
      }
    }
    if (isCsdFeatureEnabled) {
      const isAllExternal = itemObj?.vendorDetails?.every(
        e => e?.modCommand === efConstants.ZERO_COST_TEXT
      );
      if (isAllExternal) {
        itemObj.isAllExternal = isAllExternal;
        itemObj?.vendorDetails?.forEach(
          vendor => (vendor.isAllExternal = isAllExternal)
        );
      }
    }

    return itemObj;
  });
  return updatedResultObj;
};
