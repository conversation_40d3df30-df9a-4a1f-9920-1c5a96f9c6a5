import { useGetAppBasePath } from "@me/util-helpers";
import { useState } from "react";
import { CommonModal, PdfViewer } from "../../../../common";

// moving from const to function as we have to return links as well.
export default function AllowanceDatesInitialText({
  allowanceType,
  option,
  stepperElement,
  className = "",
}) {
  const {
    fields: { initialText },
  } = stepperElement;
  const { baseDomain } = useGetAppBasePath();
  const textSet = initialText?.[allowanceType]?.[option] || {};
  const [isPolicyDocumentOpen, setIsPolicyDocumentOpen] = useState(false);

  const filename = "AlbertsonsAllowancePolicies2024-02-27";
  const PDF_URL = `${baseDomain}assets/${filename}.pdf`;

  const getTermsContent = () => {
    return (
      <div
        className="flex flex-col gap-[16px] mx-[56px] pt-6"
        id="abs-allowance-dates-initial-text-terms-container"
      >
        <div
          className="text-center select-none font-bold text-[28px] text-[#2B303C]"
          id="abs-allowance-dates-initial-text-terms-text"
        >
          Terms and Conditions
        </div>
        <PdfViewer filePath={PDF_URL} showPagination={false} />
        <a
          href={PDF_URL}
          download={filename}
          target="_blank"
          rel="noreferrer"
          id="abs-allowance-dates-initial-text-a1"
        >
          <button
            className="mb-8 font-semibold text-[#1B6EBB]"
            id="abs-allowance-dates-initial-text-button-text"
          >
            Download Terms & Conditions
          </button>
        </a>
      </div>
    );
  };

  return (
    <div
      className={`pb-2 ${className}`}
      id="abs-allowance-dates-initial-text-commonmodal-container"
    >
      <CommonModal
        isModalPopupOpen={isPolicyDocumentOpen}
        setModalPopupOpen={setIsPolicyDocumentOpen}
        modalContent={getTermsContent()}
        onClose={() => setIsPolicyDocumentOpen(false)}
        minHeight={650}
        cancelBtnHandler={() => setIsPolicyDocumentOpen(false)}
      />

      {textSet.header && (
        <p
          className="w-full text-sm text-left text-[#5a697b] pt-0"
          id="abs-allowance-dates-initial-text-add-link-to-text"
        >
          <span className="w-full text-sm text-left text-[#5a697b]">
            <AddLinkToText
              text={textSet.header}
              setIsPolicyDocumentOpen={setIsPolicyDocumentOpen}
            />
          </span>
        </p>
      )}

      {textSet.subHeader && (
        <p
          className="w-full text-sm text-left text-[#5a697b] pt-2"
          id="abs-allowance-dates-initial-text-add-link-to-text-two"
        >
          <span className="w-full text-sm text-left text-[#5a697b]">
            <AddLinkToText
              text={textSet.subHeader}
              setIsPolicyDocumentOpen={setIsPolicyDocumentOpen}
            />
          </span>
        </p>
      )}

      {textSet.bulletPointsText && (
        <>
          <p
            className="p-1 w-full text-sm text-left text-[#5a697b] pt-2"
            id="abs-allowance-dates-initial-text-bullet-point-text"
          >
            <span className="w-full text-sm text-left text-[#5a697b]">
              <AddLinkToText
                text={textSet.bulletPointsText}
                setIsPolicyDocumentOpen={setIsPolicyDocumentOpen}
              />
            </span>
          </p>
          <ul
            className="p-1 w-full text-sm text-left text-[#5a697b] pl-1"
            style={{ listStyle: "inside" }}
            id="abs-allowance-dates-initial-text-bullet-point-text-two"
          >
            {textSet.bulletPoints.map(text => (
              <li>{text}</li>
            ))}
          </ul>
        </>
      )}

      {textSet.footer && (
        <p
          className="w-full text-sm text-left text-[#5a697b] pt-2"
          id="abs-allowance-dates-initial-text-footer-text"
        >
          <span className="w-full text-sm text-left text-[#5a697b]">
            {textSet.footer}
          </span>
        </p>
      )}
    </div>
  );
}

export function AddLinkToText({
  text = "",
  setIsPolicyDocumentOpen,
}: {
  text: string | any;
  setIsPolicyDocumentOpen: any;
}) {
  const LINK_WORD_TO_SEARCH = "((Albertsons Accounting Policies,))";
  const LINK_WORD_TO_REPLACE = "Albertsons Accounting Policies,";

  const arrayOfText = text.split(LINK_WORD_TO_SEARCH);

  function openAccountPoliciesModal() {
    setIsPolicyDocumentOpen(true);
  }

  if (arrayOfText.length === 2)
    return (
      <span>
        {arrayOfText[0]}
        <span
          className="text-[#3873a4] cursor-pointer underline decoration-[#3873a4] font-bold"
          onClick={openAccountPoliciesModal}
        >
          {LINK_WORD_TO_REPLACE}
        </span>
        {arrayOfText[1]}
      </span>
    );

  return text;
}
