.skipStep-2 > div:nth-child(2) {
  pointer-events: none;
  &  div {
    & > div:has(svg):nth-child(1) {
      // background: #d2d6db;
        & div:nth-child(1) .font-bold {
            color: #d2d6db;
          }
                    & div:nth-child(2) {
                      display: none;
                    }
    }

   
  }
}

.skipStep-3 > div:nth-child(3) {
  pointer-events: none;
  & > div {
    & > div:has(svg):nth-child(1) {
      & div:nth-child(1) .font-bold {
          color: #d2d6db;
        }
      
        & div:nth-child(2) {
          display: none; 
        }
    }

   
            
    }
  }

.allowance-stepper-2>div:nth-child(3)>div:nth-child(2) div.overflow-y-hidden:not([style*="height: 0"]) {
  overflow-y: visible;
}
.allowance-stepper-3 > div:nth-child(4) > div:nth-child(2) div.overflow-y-hidden:not([style*="height: 0"]) {
    overflow-y: visible;
}
label[id^="headlessui-combobox-label"] {
  display: flex;
}

label[id^="headlessui-combobox-label"] .truncate {
  font-size: 16px;
}
