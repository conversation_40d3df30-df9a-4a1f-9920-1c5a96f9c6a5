import TaskGridCollapsedActions from "./task-grid-collapsed-actions";
import Button from "@albertsons/uds/molecule/Button";
import { Task } from "./task-view-model";
import {
  useGetPlanEventDataQuery,
  useGetCommentsDataQuery,
} from "apps/promotion-management/src/app/library/data-service/dashboard/dashboard-service";
import { useState } from "react";
import {
  formatUPC,
  getPromoProductGroups,
  getPromotionList,
  payloadForViewDealSheetFromTaskView,
  renderOfferPromoSummary,
  renderValues,
  sortItems,
} from "./task-service";
import { getModifiedOffersList } from "./task-service";
import { SkeletonLoader } from "apps/promotion-management/src/app/shared/ui/atoms";
import pmConstants from "apps/promotion-management/src/app/shared/pm-constants/pm-constants";
import { STATUS_COMPLETED } from "apps/promotion-management/src/app/config/dashboard-config";
import {
  ALLOWANCE_INQUIRY,
  BILLING_INQUIRY,
  COMMENT,
  DROPPED_ITEM,
  EXTERNAL,
  NEW_ITEM,
  PPG_UNIT_TYPE_MAPPER,
  WORKFLOW,
} from "./task-view-columns-config";
import Tooltip from "@albertsons/uds/molecule/Tooltip";
import AllowanceViewDealSheet from "../../allowance-view/components/allowance-view-deal-sheet";
import { useGetItemsData } from "apps/promotion-management/src/app/shared/hooks/useGetItemsData";
import { EEVENT_STATUS, EUSER_ROLES } from "@me/util-helpers";
import useMarkTaskCompleted from "apps/promotion-management/src/app/shared/hooks/useMarkTaskCompleted";
import { showAddEventCTA } from "@me-upp-js/utilities";
import { filterCommentsByUser } from "./task-view-util";

export function TaskViewGridCollapsedTable({
  task,
  columns = [],
  showActionBtns = true,
}: {
  task: Task;
  columns;
  showActionBtns?: boolean;
}) {
  const [itemsToShow, setItemsToShow] = useState(2);
  const isItemsColsRender = [NEW_ITEM, DROPPED_ITEM].includes(task?.subType);
  const {
    data: eventDataResp,
    isLoading,
    isFetching,
  } = useGetPlanEventDataQuery(task?.id, {
    skip: !task?.id,
    refetchOnMountOrArgChange: true,
  });

  const { itemsData, isPlanEventItemsByCICSLoading } = useGetItemsData({
    task,
  });

  const userType = localStorage.getItem("USER_TYPE");

  const { data: commentsData } = useGetCommentsDataQuery(
    [ALLOWANCE_INQUIRY, COMMENT, WORKFLOW].includes(task?.subType)
      ? { URL_PARAM: [task?.id] }
      : null,
    {
      skip:
        (![ALLOWANCE_INQUIRY, COMMENT].includes(task?.subType) &&
          !(
            task?.subType === WORKFLOW &&
            task?.eventStatus === EEVENT_STATUS.AGREED_PENDING
          )) ||
        !task?.id,
      refetchOnMountOrArgChange: true,
    }
  );

  const loading = isLoading || isFetching || isPlanEventItemsByCICSLoading;

  const markTaskCompleted = useMarkTaskCompleted();

  const handleMarkAsReadBtn = async () => {
    const { taskId } = task;
    await markTaskCompleted(taskId); // Use the function directly here
  };

  const getColumnsToRender = () => {
    if (
      [ALLOWANCE_INQUIRY, COMMENT].includes(task?.subType) ||
      (task?.subType === WORKFLOW &&
        task?.eventStatus === EEVENT_STATUS.AGREED_PENDING)
    ) {
      const columnsToRender = [
        ...columns,
        {
          title: "Comments",
          key: "comments",
          methodMapper: "renderComments",
          id: "abs-task-view-grid-collapsed-table-comments",
          containerClass:
            "pl-6 pr-6 col-span-2 flex flex-col gap-2 py-2 my-3 text-sm abs-pm-task-cols-collapsed",
        },
      ];
      return task?.subType === WORKFLOW &&
        task?.eventStatus === EEVENT_STATUS.AGREED_PENDING
        ? columnsToRender
        : [
            ...columnsToRender,
            {
              key: "button",
              methodMapper: "displayMarkBtn",
              id: "abs-task-view-grid-collapsed-table-comments",
              containerClass:
                "pl-6 pr-6 col-span-2 flex flex-col gap-2 py-2 my-3 text-sm abs-pm-task-cols-collapsed",
            },
          ];
    }
    return columns;
  };

  const renderColsHeader = () => {
    return getColumnsToRender().map((columnObj, index) => (
      <div
        key={index}
        className={`bg-[#F1F4F9] abs-pm-task-collapsed-grid-header ${
          columnObj?.headerClass || "pl-6 col-span-2 font-bold py-4 text-sm"
        }`}
        id={`${columnObj?.id}-column-header`}
        style={{ ...(columnObj?.colClassName || {}) }}
      >
        {columnObj?.title}
      </div>
    ));
  };

  const renderCols = (item?) => {
    return getColumnsToRender().map((columnObj, index) => {
      const valueFuncKey = columnObj?.methodMapper,
        renderValue = TASK_GRID_METHOD_MAPPER?.[valueFuncKey];
      return (
        <RenderWithLoader loading={loading} key={index}>
          <div
            className={`${columnObj?.containerClass}`}
            id={`${columnObj?.id}-column-value`}
            style={{ ...(columnObj?.colClassName || {}) }}
          >
            {renderValue && renderValue(item, columnObj)}
            {columnObj?.renderShowMore && renderShowMore()}
          </div>
        </RenderWithLoader>
      );
    });
  };

  const renderItemColsVal = () => {
    return itemsData?.map((item: any, index) => {
      return (
        <div
          key={index}
          className={`flex flex-nowrap bg-[#F7F8FA]`}
          id={`abs-task-view-grid-collapsed-item-table-column-${index}`}
        >
          {renderCols(item)}
        </div>
      );
    });
  };

  const renderColsValues = () => {
    return (
      <RenderWithLoader loading={loading}>
        <>
          {isItemsColsRender && showActionBtns
            ? renderItemColsVal()
            : renderCols()}
        </>
      </RenderWithLoader>
    );
  };

  const renderPack = item => (
    <div> {item?.rogPackRetails?.primaryUpcFirstPackRetail || item?.pack}</div>
  );

  const renderPrimaryUpc = item => <div>{formatUPC(item?.primaryUpc)}</div>;
  const renderItemField = (item, colObj) => <div> {item?.[colObj?.key]}</div>;
  const renderUpcs = item => (
    <div> {item?.plannedCicUpcs?.length || item?.upcs?.length}</div>
  );

  const renderPPG = () => {
    return getPromoProductGroups(eventDataResp?.planEvent?.planProductGroups)
      ?.slice(0, itemsToShow)
      ?.map((item, index) => (
        <div id={`abs-task-view-grid-collapsed-table-ppg-${index}`}>{item}</div>
      ));
  };

  const renderShowMore = () => {
    const ppgArray = getPromoProductGroups(
      eventDataResp?.planEvent?.planProductGroups
    );
    return ppgArray?.length > 2 && itemsToShow === 2 ? (
      <div
        className="text-brand text-[#1B6EBB] cursor-pointer font-bold text-base"
        onClick={() => setItemsToShow(ppgArray?.length)}
        id="abs-task-view-grid-collapsed-table-show-more"
      >
        Show more
      </div>
    ) : (
      itemsToShow > 2 && (
        <div
          className="text-brand text-[#1B6EBB] cursor-pointer font-bold text-base"
          onClick={() => setItemsToShow(2)}
          id="abs-task-view-grid-collapsed-table-show-less"
        >
          Show less
        </div>
      )
    );
  };

  const renderOffers = () => {
    const offersList = getModifiedOffersList(eventDataResp) || [];
    return sortItems(offersList)?.map(offer => (
      <div
        id={`abs-task-service-offer${offer?.id}`}
        className="flex gap-2 items-start justify-start"
      >
        <div className="flex gap-2 border-r-[1px] pr-2 border-[#C8DAEB] shrink-0">
          {`Offer #${offer?.id}`}
          <Tooltip label="View Deal Sheet" zIndex={10}>
            <AllowanceViewDealSheet
              offer={payloadForViewDealSheetFromTaskView(eventDataResp, offer)}
              isCopyShow={true}
            />
          </Tooltip>
        </div>

        <div id="abs-task-service">{renderValues(offer, true)}</div>
      </div>
    ));
  };

  const renderPromotions = () => {
    const promotionsList = getPromotionList(eventDataResp) || [];
    return sortItems(promotionsList)?.map(promo =>
      renderOfferPromoSummary({
        id: promo?.id,
        values: promo,
        isOffer: false,
      })
    );
  };

  const renderComment = (eventComment, eventCommentIndex) => (
    <Tooltip
      className="abs-task-view-collapsed-table-comment-tooltip"
      zIndex={10}
      anchor="bottom"
      key={eventCommentIndex}
    >
      <Tooltip.Popover>
        <div className="m-1 w-fit overflow-auto rounded text-black text-sm font-normal leading-4">
          {decodeURIComponent(eventComment?.messageText)}
        </div>
      </Tooltip.Popover>
      <div
        className={`${
          eventCommentIndex === 0
            ? "abs-task-view-collapsed-table-first-comment"
            : ""
        } cursor-pointer truncate w-fit`}
      >
        {eventCommentIndex === 0 ? (
          <span className="abs-task-view-collapsed-table-first-comment-green-dot"></span>
        ) : null}
        {decodeURIComponent(eventComment?.messageText)}
      </div>
    </Tooltip>
  );

  const renderComments = () => {
    if (!commentsData) return null;
    // [{eventcomments: {userType: ""}}] with this mock in eventcomments filter the comments based on userType

    // const userComments =
    // Filter the comments based on userType
    // const userComments = commentsData.filter(comment =>
    //   comment.eventcomments.every(
    //     eventComment =>
    //       eventComment.userType.toLowerCase() === userType?.toLowerCase()
    //   )
    // );
    const eventComments = commentsData?.reduce((acc, commentGroup) => {
      const isBillingInquiry =
        commentGroup?.commentCategory === BILLING_INQUIRY;

      const filtered = commentGroup?.eventComments.filter(comment => {
        const isMerchant =
          comment?.userType?.toUpperCase() === EUSER_ROLES.MERCHANT;
        const isVendor =
          comment?.userType?.toUpperCase() === EUSER_ROLES?.VENDOR;
        if (userType === EUSER_ROLES.VENDOR) {
          return isMerchant && !isBillingInquiry;
        }
        if (userType === EUSER_ROLES.MERCHANT) {
          return isVendor || isBillingInquiry;
        }
        return false;
      });
      return acc.concat(filtered);
    }, []);

    return (
      <div>
        {eventComments.length > 0 ? (
          eventComments.map((comment, index) => (
            <div key={index}>{renderComment(comment, index)}</div>
          ))
        ) : (
          <div>No comments available</div>
        )}
      </div>
    );
  };

  const displayMarkBtn = () => {
    if ([COMMENT].includes(task?.subType) && task?.taskStatus !== "COMPLETED") {
      return (
        <div className="flex justify-center items-center">
          <Button
            variant="secondary"
            width={140}
            className="!h-8 text-[16px]"
            onClick={handleMarkAsReadBtn}
          >
            Mark As Read
          </Button>
        </div>
      );
    }
    return null;
  };

  const RenderWithLoader = ({ children, loading, colSpan = 2 }) => {
    if (loading)
      return (
        <div
          id="abs-task-view-grid-collapsed-table"
          className={`col-span-${colSpan} p-1 rounded-1`}
        >
          <SkeletonLoader height={200} baseColor="#EBF3FA" />
        </div>
      );
    return children;
  };

  const isTaskActionsVisible = !(
    [ALLOWANCE_INQUIRY, COMMENT].includes(task?.subType) ||
    task?.taskStatus === STATUS_COMPLETED.toUpperCase()
  );

  const RenderTaskExpandActions = isTaskActionsVisible &&
    showAddEventCTA() &&
    (isItemsColsRender ? showActionBtns : true) && (
      <>
        <TaskGridCollapsedActions
          taskDetails={task}
          eventDetails={eventDataResp}
        />
      </>
    );

  const renderUnitType = item => {
    const validUpcData = item?.plannedCicUpcs?.find(
      upcObj => upcObj?.rogs?.length > 0
    );
    const isUnitTypeFromApi = validUpcData?.rogs[0]?.unitType;
    const unitType = isUnitTypeFromApi
      ? PPG_UNIT_TYPE_MAPPER?.[isUnitTypeFromApi]?.code
      : item?.unitType;
    return (
      <div id={`abs-task-view-grid-collapsed-table-unitType`}>
        {unitType || ""}
      </div>
    );
  };
  const renderSize = item => (
    <div id={`abs-task-view-grid-collapsed-table-size`}>
      {item?.descriptiveSize || item?.size}
    </div>
  );
  const TASK_GRID_METHOD_MAPPER = {
    renderPromotions,
    renderOffers,
    renderPPG,
    renderItemField,
    renderUpcs,
    renderUnitType,
    renderPrimaryUpc,
    renderPack,
    renderSize,
    renderComments,
    displayMarkBtn,
  };
  const renderColsAction = () => {
    return (
      <RenderWithLoader loading={loading} colSpan={3}>
        <div
          className={`pl-6 flex flex-col gap-2 ${
            isItemsColsRender && showActionBtns
              ? "col-span-3"
              : "col-span-2 pr-6 py-2"
          }`}
          id="abs-task-view-grid-collapsed-table-actions-container"
        >
          {RenderTaskExpandActions}
        </div>
      </RenderWithLoader>
    );
  };

  const renderItemsDetails = (
    <div className="w-full overflow-x-auto">
      <div className="w-full bg-[#F1F4F9]">
        <div
          className={`flex flex-nowrap ${
            isItemsColsRender && showActionBtns
              ? "item-header-container"
              : "abs-pm-task-view-grid-collapsed-table-ppg-header"
          }`}
        >
          {renderColsHeader()}
        </div>
      </div>
      <div className={`${isItemsColsRender && showActionBtns && "flex"}`}>
        <div
          className={`${
            isItemsColsRender && showActionBtns
              ? "item-row-container"
              : "abs-pm-task-view-grid-collapsed-table-ppg-col-values"
          }`}
        >
          {renderColsValues()}
        </div>
        {renderColsAction()}
      </div>
    </div>
  );

  const renderPPGDetails = () => (
    <div
      id="abs-task-view-grid-collapsed-table"
      className={`grid w-full ${
        isItemsColsRender && showActionBtns
          ? "grid-cols-12"
          : "grid-cols-9 abs-pm-task-view-grid-collapsed-table-ppg"
      }`}
    >
      <>
        <div className="abs-pm-task-view-grid-collapsed-table-ppg-header">
          {renderColsHeader()}
        </div>
        <div className="abs-pm-task-view-grid-collapsed-table-ppg-col-values">
          {renderColsValues()}
          {![ALLOWANCE_INQUIRY, COMMENT].includes(task?.subType)
            ? renderColsAction()
            : ""}
        </div>
      </>
    </div>
  );

  return (
    <div
      className={`flex bg-[#F7F8FA] text-[#2B303C] ${pmConstants.componentClassName.TASK_VIEW_GRID_COLLAPSED_TABLE}`}
      id="abs-task-view-grid-collapsed-table-container"
    >
      <div
        id="abs-task-view-grid-collapsed-table"
        className="pl-[34px] shrink-0 h-[40px] bg-[#F1F4F9]"
      ></div>
      {isItemsColsRender ? renderItemsDetails : renderPPGDetails()}
    </div>
  );
}
