import { app_store } from "@me/data-rtk";
import { render } from "@testing-library/react";
import UpdateAllAllowances from "./update-all-allowances";
import userEvent from "@testing-library/user-event";
import { Provider } from "react-redux";
import * as selectors from "@me/data-rtk";

jest.mock("../all-allowances-container/text-mapping", () => ({
  getAllowTypeConfig: jest.fn().mockReturnValue({
    groupText: "",
    newCostCalculateKey: "unitCostAllow",
    allowanceTypeDisplay: "Scan",
    description:
      "You can update Allowance amounts for items together or individually.",
  }),
  textMapping: jest.fn().mockReturnValue({
    customAmtWidth: 0,
    isShowUOMOption: false,
    isSkipLessThanAmtValidation: true,
  }),
}));

function setup(jsx) {
  return {
    user: userEvent.setup(),
    ...render(<Provider store={app_store}>{jsx}</Provider>),
  };
}

describe("UpdateAllAllowances", () => {
  beforeEach(() => {
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      switch (sel_name) {
        case "allowance_temp_work":
          return {
            data: { allowanceData: { allowanceType: "SCAN" } },
          };
        case "selectedSwitchValue_rn":
          return {
            data: { switchValue: { selectedSwitch: "Case" } },
          };
        case "allowanceTableData_rn":
          return {
            data: { tableData: [{ vendorDetails: [{ unitListCost: 3 }] }] },
          };

        case "isUpdateAllClicked_rn":
          return {
            data: {},
          };

        default:
          break;
      }
    });
  });
  xit("renders the component without errors", () => {
    setup(<UpdateAllAllowances />);
  });

  /*   it("renders the 'Update All' button", () => {
    render(<UpdateAllAllowances />);
    const updateAllButton = screen.getByRole("button", { name: /update all/i });
    expect(updateAllButton).toBeInTheDocument();
  });

  it("disables the 'Update All' button when there is a calculation error", () => {
    render(<UpdateAllAllowances />);
    const amountInput = screen.getByRole("textbox", { name: /allowanceamt/i });
    fireEvent.change(amountInput, { target: { value: "abc" } });
    const updateAllButton = screen.getByRole("button", { name: /update all/i });
    expect(updateAllButton).toBeDisabled();
  });

  it("calls the 'updateAllItemsAction' function with the correct arguments when 'Update All' button is clicked", () => {
    const mockDispatch = jest.fn();
    jest.mock("react-redux", () => ({
      useDispatch: () => mockDispatch,
    }));
    render(<UpdateAllAllowances />);
    const amountInput = screen.getByRole("textbox", { name: /allowanceamt/i });
    const uomSelect = screen.getByRole("combobox", { name: /uom/i });
    fireEvent.change(amountInput, { target: { value: "10" } });
    fireEvent.change(uomSelect, { target: { value: "EA" } });
    const updateAllButton = screen.getByRole("button", { name: /update all/i });
    fireEvent.click(updateAllButton);
    expect(mockDispatch).toHaveBeenCalledWith({
      type: "allowanceTableData/updateAllItems",
      payload: {
        amount: "10",
        uom: "EA",
      },
    });
  }); */
});
