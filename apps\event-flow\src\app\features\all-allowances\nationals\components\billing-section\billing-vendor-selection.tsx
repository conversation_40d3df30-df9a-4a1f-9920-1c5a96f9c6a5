import React from "react";
import useBillingSelection from "../../../../create-event/hooks/useBillingSelection";
import { useSelectorWrap } from "@me/data-rtk";
import { isNationalType } from "../../../allowance-lead-distributors/billing-selection-utils";
import Checkbox from "@albertsons/uds/molecule/Checkbox";
import { ChevronDown, ChevronUp } from "lucide-react";
import classNames from "classnames";

function BillingVendorSelection({
  currentDivision = { divisionId: "" },
  isNdpType = false,
}) {
  // Extract necessary data from Redux store
  const {
    data: { leadOptions, allDivisionLeadOptions } = {
      leadOptions: [],
      allDivisionLeadOptions: {},
    },
  } = useSelectorWrap("leadDistributors_rn") || {};
  const { divisionId = "" } = currentDivision || {};
  const isNational = isNationalType(isNdpType);

  // Determine current division's lead options
  const currentDivLeadOptions = isNational
    ? allDivisionLeadOptions?.[divisionId] || []
    : leadOptions;

  // Use custom hook for billing selection logic
  const { moveDown, moveUp, mainVendors, mapping } = useBillingSelection({
    vendors: currentDivLeadOptions,
    currentDivId: isNational ? divisionId : "",
    isNdpType: isNational,
  });

  // Helper function to render distributor details
  const getDistributorsNameSummary = id => {
    const vendor = currentDivLeadOptions?.find(item => item.vendorNbr === id);
    return (
      <span className="abs-ef-get-distributors-name-summary">
        <span className="font-bold">{vendor?.vendorName} </span>- {id}{" "}
        {vendor?.costAreaDesc}
      </span>
    );
  };

  return (
    <div className="pl-[3.2rem] pr-4">
      <section className="flex flex-col pb-3 w-full p-2 modal-cls overflow-auto lead-dist-vendor-section">
        {mainVendors?.map((vendorNbr, index) => {
          const { child = [] } = mapping?.[index] || {};
          return (
            <div key={vendorNbr} className="flex flex-col gap-1">
              {/* Main Vendor Checkbox */}
              <div className="flex">
                <Checkbox checked disabled className="py-[2px]">
                  {getDistributorsNameSummary(vendorNbr)}
                </Checkbox>
              </div>
              {/* Child Vendors */}
              {child.length > 0 && (
                <>
                  {child.map(vendor => (
                    <div
                      key={vendor}
                      className="flex pl-4 justify-between w-full"
                    >
                      <Checkbox
                        disabled
                        className={classNames("py-[2px]", "[&>label]:w-full")}
                      >
                        {getDistributorsNameSummary(vendor)}
                      </Checkbox>
                      {/* Move Up / Move Down Actions */}
                      <div className="flex text-[#1B6EBB] hover:cursor-pointer">
                        {index > 0 && (
                          <div className="flex" onClick={() => moveUp(vendor)}>
                            Move <ChevronUp fill="white" />
                          </div>
                        )}
                        {index < mainVendors.length - 1 && (
                          <div
                            className="flex"
                            onClick={() => moveDown(vendor)}
                          >
                            Move <ChevronDown fill="white" />
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </>
              )}
            </div>
          );
        })}
      </section>
    </div>
  );
}

export default BillingVendorSelection;
