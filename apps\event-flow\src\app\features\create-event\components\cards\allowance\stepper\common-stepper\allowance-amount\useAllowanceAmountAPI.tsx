import { useSelectorWrap } from "@me/data-rtk";
import { useGetAppBasePath } from "@me/util-helpers";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useGetAllowancesItemsQuery } from "../../../../../../service/apis/allowance-api";
import { buildURLforAllowanceDashboard } from "../../allowance-amounts/allowance-amounts-helper";
import {
  getQueryParams,
  sortAllowanceById,
} from "../../../../../../../create-event/service/allowance/allowance-service";
import { appConstants } from "@me/utils-root-props";
import efConstants from "../../../../../../../../shared/ef-constants/ef-constants";
import { saveToSessionStorage } from "../../../../../../../../shared/helpers/event-flow-helpers";
import { isAllowanceFeatureEnabled } from "@me-upp-js/utilities";

/**
 * this hook is used to make the amounts API call,
 * navigate to main entry screen
 * as well as return path for main entry screen
 * @param isEditEnable
 * @param key
 * @param productSources
 * @returns
 */
export default function useAllowanceAmountAPI(
  isEditEnable: boolean,
  key: string,
  productSources: string[],
  offerIndex: number
) {
  const { basePath } = useGetAppBasePath();
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const {
      data: { allowanceData: allowanceTempWorkData },
    } = useSelectorWrap("allowance_temp_work"),
    { isAllowConvEnable } = useSelectorWrap("allow_conv_status_rn").data;
  const { isAllowanceTypeChanged } = useSelectorWrap(
    "allow_type_change_rn"
  ).data;
  const [sortedAllowData, setSortedAllowData] = useState<any>(undefined);

  const navigate = useNavigate();
  const { taskType } = getQueryParams();
  const ROUTE_PARAM = {
    eventId: eventDetailsData.id,
    offerAllowanceGroup: key,
    isEdit: isEditEnable,
    ...(taskType === appConstants.TASK_TYPE_NEW_ITEM && { taskType }),
    isAllowTypeChange:
      isAllowanceFeatureEnabled && isAllowanceTypeChanged?.[offerIndex],
  };
  const allAllowancesPath = `${basePath}/${buildURLforAllowanceDashboard(
    ROUTE_PARAM,
    isAllowConvEnable
  )}`;

  const allowanceAmountsPayload = {
    URL_PARAMS: [eventDetailsData.id, key],
  };
  const { data: allowancesResp, isFetching } = useGetAllowancesItemsQuery(
    allowanceAmountsPayload,
    {
      refetchOnMountOrArgChange: true,
      skip:
        !allowanceTempWorkData?.tempWorkAllowanceId || !productSources?.length,
    }
  );

  useEffect(() => {
    const sortedAllowances =
      (allowancesResp?.allowances?.length > 0 &&
        sortAllowanceById([...(allowancesResp?.allowances || [])])) ||
      allowancesResp?.allowances;
    const updatedAllowRespData =
      sortedAllowances?.length > 0
        ? { ...allowancesResp, allowances: sortedAllowances }
        : allowancesResp;
    setSortedAllowData(updatedAllowRespData);
    const isItemAmountsSummarized: boolean =
      allowancesResp?.summary?.itemAmountsCouldBeSummarized;
    if (allowancesResp?.summary) {
      saveToSessionStorage(
        efConstants.ITEM_AMOUNTS_SUMMARIZED_KEY,
        isItemAmountsSummarized
      );
    }
    if (isItemAmountsSummarized === false) {
      navigate(allAllowancesPath);
    }
  }, [allowancesResp]);

  return {
    allAllowancesPath,
    allowancesResp: sortedAllowData || allowancesResp,
    isFetching,
    basePath,
  };
}
