import React, { memo } from "react";

interface ITopAmountSectionProps {
  children?: any;
  description: string;
  baseId: string;
}
function TopAmountSection({
  children,
  description,
  baseId,
}: ITopAmountSectionProps) {
  return (
    <>
      <p
        className="pb-2 py-4 px-3 bg-[#F3F4F6]"
        id={`${baseId}-description`}
      >
        <span className="w-full text-sm text-left text-[#5a697b]">
          {description}
        </span>
      </p>
      {children}
    </>
  );
}

export default memo(TopAmountSection);
