import React from 'react'
import Modal from "@albertsons/uds/molecule/Modal";
import { useSelectorWrap } from "@me/data-rtk";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { OverlappingAllowanceGrid } from "@me-upp-js/features/expandable-table";
import { onModalToggleOverlapping } from "../../create-event/service/slice/allowance-details-slice";
import {
  useGetAllowanceItemsDifferenceQuery,
} from "../../create-event/service/apis/allowance-api";
import efConstants from "../../../shared/ef-constants/ef-constants";


function MainEntryOverlapsTable({ isSummary, offerAllowancesId, allowRespData = {}, allowDiffRespData = null })  {
  const [overlapsData, setOverlapsData] = useState();

  const { data: modalDetails } = useSelectorWrap(
    "overlapping_allowance_amount_modal"
  );
  const { data: allowItemDiffResp } = useGetAllowanceItemsDifferenceQuery(
    {
      URL_PARAM: offerAllowancesId,
    },
    {
      skip: !isSummary || allowDiffRespData,
    }
  );
  const dispatch = useDispatch();

  useEffect(() => {
    modalDetails.modalData && setOverlapsData(findMatchingObj());
  }, [allowRespData, modalDetails, allowItemDiffResp, allowDiffRespData]);

  const findMatchingObj = () => {
    const {
      offerAllowanceOverlapResults: { offerAllowanceOverlaps } = {
        offerAllowanceOverlaps: [],
      },
    }: any = (isSummary ? allowDiffRespData || allowItemDiffResp :  allowRespData) || {};

    const selectedItemId = modalDetails?.modalData?.itemId;
    if (!selectedItemId) {
      return;
    }
    const matchedItemParentArr: any = [];

    // Loop through each offer object in the array
    offerAllowanceOverlaps?.forEach(offer => {
      // Loop through each location overlap object in the offer object
      offer.locationOverlaps?.forEach(locationOverlap => {
        // Loop through each item overlap object in the location overlap object
        locationOverlap?.itemOverlaps?.forEach(itemOverlap => {
          // Check if the itemId matches the itemId in the item overlap object
          if (itemOverlap?.itemId === selectedItemId) {
            const updatedObj = { ...offer, ...itemOverlap };
            matchedItemParentArr.push(updatedObj);
          }
        });
      });
    });

    // Return an object containing the matched item object and its parent object
    return matchedItemParentArr;
  };

  const displayTable = (
    <div className="p-5">
      <OverlappingAllowanceGrid quickEntryData={overlapsData} />
    </div>
  );

  const noRecords = (
    <div className="flex justify-center py-4 w-full">
      No records available. Please update filters and try again.
    </div>
  );
  return (
    <Modal
      isOpen={modalDetails.isOpen}
      onClose={() => {
        dispatch(
          onModalToggleOverlapping({ isOpen: false, modalDetails: null })
        );
      }}
      width={1100}
      className={`h-auto overflow-auto bg-white overflow-scroll ${efConstants.componentClassName.ALLOWANCES_UNIT_COST_DETAILS}`}
    >
      <div className="p-6 border-solid border-b-[1px] border-[#C8DAEB]">
        <h2 className="text-2xl font-bold">Allowances in Item Unit Cost</h2>
      </div>
      {overlapsData?.[0] ? displayTable : noRecords}
      {}
    </Modal>
  );
}

export default MainEntryOverlapsTable
