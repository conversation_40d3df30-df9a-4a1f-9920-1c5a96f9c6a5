import { isFeatureFlagEnabled } from "@me-upp-js/utilities";
import { STATUS_CONSTANTS } from "@me/util-helpers";
import { appConstants } from "@me/utils-root-props";

export const DASHBOARD_SLICE_CONFIG = {
  dashboard: {
    DASHBOARD_CONFIG_SLICE_KEY: "dashboardConfig_rn",
    SORT_KEYS_SLICE_KEY: "dashboardSortKeys_rn",
  },
  taskView: {
    SLICE_KEY: "taskViewData_rn",
    FILTER_SLICE_KEY: "tasksFilterData_rn",
  },
  alertView: {
    SLICE_KEY: "alertViewData_rn",
    FILTER_SLICE_KEY: "alertsFilterData_rn",
    SELECTION_SLICE_KEY: "selectedAlertsData_rn",
  },
  allowanceView: {
    SLICE_KEY: "allowanceViewData_rn",
    FILTER_SLICE_KEY: "allowanceFilterData_rn",
    DRAGGABLE_OPTIONS_SLICE_KEY: "allowanceDraggableColumnData_rn",
    DEFAULT_COULMN_CONFIG_SLICE_KEY: "allowancedefaultColumnConfig_rn",
  },
  planningView: {
    SLICE_KEY: "planningViewData_rn",
    FILTER_SLICE_KEY: "planningFilterData_rn",
  },
  dsdFundingView: {
    SLICE_KEY: "dsdFundingViewData_rn",
    FILTER_SLICE_KEY: "dsdFundingFilterData_rn",
    TOGGLE_ITEM_DETAILS_COLS_SLICE_KEY: "toggleItemDetailsColsData_rn",
    TOGGLE_POTENTIAL_FUNDING_SLICE_KEY: "togglePotentialFundingData_rn",
    DSD_UPC_MODAL_DETAILS: "dsd_upc_modal_details",
  },
};

export const STATUS_COMPLETED = "Completed";
export const STATUS_PENDING = "Pending";
export const FILTER_BY_TYPE_DEFAULT_VAL = [
  "Inquiry",
  "New Event",
  "Changed Event",
  "New Item",
  "Comment",
  "Dropped Item",
];
export const SORT_BY_DEFAULT_OPTION = {
  name: "Event Start + Event ID",
  eventKey: "Event Start",
  isDefaultValue: true,
};
export const PROMOTION_ID_SEARCH_KEY = "promotionId";

export const DEFAULT_SEARCH_CONFIG = {
  selectSearchValue: null,
  searchValue: null,
};

/**
 * Can add properties in below object to reset it on any action
 */
export const DEFAULT_FILTER_SEARCH_CONFIG = {
  taskAlertStatusType: STATUS_PENDING,
  tasksSortType: SORT_BY_DEFAULT_OPTION,
  taskStatusType: FILTER_BY_TYPE_DEFAULT_VAL,
};

export const DEFAULT_PAGINATION_CONFIG = {
  pagination: {
    pageNumber: 0,
    pageSize: 100,
  },
};

export const DEFAULT_PAGINATION_CONFIG_WITH_TOTAL_ELEMENTS = {
  defaultPageNumber: 0,
  defaultPageSize: 100,
  totalElements: 0,
};

export type SearchFilterOption = {
  name: string;
  eventKey: string;
  isOnlyNumber?: boolean;
  required?: boolean;
  isDefaultValue?: boolean;
  isSpaceAllowed?: boolean;
  isList?: boolean;
  isNumber?: boolean;
  isShow?: string[];
  isSearchDropdown?: boolean;
  nationalSearchKey?: string
};

export type NationalEventSearchFilter = {
  parentEventId?: number;
  parentOfferNumber?: string;
  parentEventName?: string;
  promotionId?: string;
  periscopeId?: string;
  cicId?: string;
  upcId?: string;
  ppgId?: string;
  vendorTrackingNumber?: string;
  childDivisionIds?: string[];
};

export const SEARCH_FILTER_OPTIONS: SearchFilterOption[] = [
  {
    name: "Event Name",
    eventKey: "eventNames",
    isOnlyNumber: false,
    isSpaceAllowed: true,
    required: true,
    isDefaultValue: false,
    isShow: ["planningView", "taskView", "alertView", "allowanceView"],
    isSearchDropdown: true,
    nationalSearchKey: "parentEventName",
  },
  {
    name: "Event ID#",
    eventKey: "planEventIdNbrs",
    isOnlyNumber: false,
    required: true,
    isDefaultValue: false,
    isList: true,
    isSpaceAllowed: true,
    isNumber: false,
    isShow: ["planningView", "taskView", "alertView", "allowanceView"],
    isSearchDropdown: true,
    nationalSearchKey: "parentEventNumberId"
  },
  {
    name: "Offer ID#",
    eventKey: "offerNumbers",
    isOnlyNumber: true,
    required: true,
    isSpaceAllowed: true,
    isList: true,
    isNumber: true,
    isDefaultValue: true,
    isShow: [
      "planningView",
      "taskView",
      "alertView",
      "allowanceView",
      "dsdFundingView",
    ],
    isSearchDropdown: true,
    nationalSearchKey: "parentOfferNumber",
  },
  {
    name: "Promo ID#",
    eventKey: "promotionIds",
    isOnlyNumber: true,
    required: true,
    isSpaceAllowed: true,
    isList: true,
    isNumber: true,
    isShow: ["planningView", "taskView", "alertView", "dsdFundingView"],
    isSearchDropdown: true,
    nationalSearchKey: "promotionId",
  },
  {
    name: "Periscope ID#",
    eventKey: "periscopeIds",
    isOnlyNumber: true,
    required: true,
    isSpaceAllowed: true,
    isList: true,
    isNumber: true,
    isShow: ["planningView", "taskView", "alertView", "allowanceView"],
    isSearchDropdown: true,
    nationalSearchKey: "periscopeId",
  },
  {
    name: "CIC ID#",
    eventKey: "cics",
    isOnlyNumber: true,
    required: true,
    isSpaceAllowed: true,
    isList: true,
    isShow: ["planningView", "taskView", "alertView", "allowanceView"],
    isSearchDropdown: true,
    nationalSearchKey: "cicId",
  },
  {
    name: "UPC ID#",
    eventKey: "itemUpcs",
    isSpaceAllowed: true,
    isOnlyNumber: true,
    required: true,
    isList: true,
    isShow: ["planningView", "taskView", "alertView", "allowanceView"],
    isSearchDropdown: true,
    nationalSearchKey: "upcId",
  },
  {
    name: "PPG ID#",
    eventKey: "ppgIds",
    isOnlyNumber: true,
    required: true,
    isSpaceAllowed: true,
    isList: true,
    isNumber: true,
    isDefaultValue: true,
    isShow: isFeatureFlagEnabled(appConstants.FEATURE_FLAGS.PPG_ID)
      ? ["planningView", "taskView", "alertView", "allowanceView"]
      : [],
    isSearchDropdown: true,
    nationalSearchKey: "ppgId",
  },
  {
    name: "Vendor Tracking #",
    eventKey: "vendorTrackingNumbers",
    isOnlyNumber: false,
    required: true,
    isSpaceAllowed: true,
    isList: true,
    isShow: isFeatureFlagEnabled(appConstants.FEATURE_FLAGS.VENDOR_NUMBER)
      ? ["taskView", "planningView", "alertView", "allowanceView"]
      : [],
    isSearchDropdown: true,
    nationalSearchKey: "vendorTrackingNumber",
  },
];

export const DEFAULT_EVENT_STATUS_VALUE = [
  { name: STATUS_CONSTANTS.STATUS.DRAFT.text, checked: true },
  {
    name: STATUS_CONSTANTS.STATUS["PENDING WITH MERCHANT"].filtertext,
    checked: true,
  },
  {
    name: STATUS_CONSTANTS.STATUS["PENDING WITH VENDOR"].filtertext,

    checked: true,
  },
  { name: STATUS_CONSTANTS.STATUS.AGREED.text, checked: true },
  {
    name: STATUS_CONSTANTS.STATUS["AGREED-PENDING"].filtertext,
    checked: true,
  },
  // { name: STATUS_CONSTANTS.STATUS.READY.text, checked: true },
  { name: STATUS_CONSTANTS.STATUS.ACTIVE.text, checked: true },
  { name: STATUS_CONSTANTS.STATUS.EXECUTED.text, checked: true },
  { name: STATUS_CONSTANTS.STATUS.CANCELED.text, checked: true },
  { name: STATUS_CONSTANTS.STATUS.REJECTED.text, checked: true },
];

export const EVENT_STATUS_OPTIONS = {
  PENDING_WITH_VENDOR: "Pending With Vendor",
  PENDING_WITH_MERCHANT: "Pending With Merchant",
  DRAFT: "Draft",
  AGREED: "Agreed",
  ACTIVE: "Active",
  AGREED_PENDING: "Agreed-Pending",
  EXECUTED: "Executed",
  CANCELED: "Canceled",
  REJECTED: "Rejected",
};

const COMMON_DASHBOARD_HEADER_FIELDS = {
  selectSearchOptionField: {
    type: "select",
    options: SEARCH_FILTER_OPTIONS,
    itemKey: "name",
    errorMsg: "Please check your input!",
    popoverClassName: "left-2",
    keysToSendInArray: [
      "eventNames",
      "planEventIdNbrs",
      "offerNumbers",
      "promotionIds",
      "periscopeIds",
      "cics",
      "itemUpcs",
    ],
    variant: "tertiary",
    variantClass: "",
  },
  searchOptionField: {
    searchFieldclass: "!rounded",
    searchFieldSize: "lg",
    type: "search",
    placeholder: "Search",
  },
  sortByField: {
    type: "select",
    label: "Sort",
    width: 200,
    popoverClassName: "left-10",
    itemKey: "name",
    variant: "tertiary",
    variantClass: "",
    placeholder: "Select",
    options: [
      {
        name: "Event Start + Event ID",
        eventKey: "Event Start",
        isDefaultValue: true,
      },
      {
        name: "Vendor + Event Start",
        eventKey: "Vendor Start",
      },
      {
        name: "Task Type",
        eventKey: "Task Type",
      },
    ],
  },
  filterByTypeField: {
    type: "select",
    label: "Filter by Type",
    itemKey: "name",
    variant: "tertiary",
    variantClass: "right-0",
    placeholder: "All",
    width: 200,
    options: [
      {
        name: "All",
        eventKey: FILTER_BY_TYPE_DEFAULT_VAL,
        isDefaultValue: true,
      },
      {
        name: "Billing Inquiry",
        eventKey: ["Inquiry"],
      },
      {
        name: "New Event",
        eventKey: ["New Event"],
      },
      {
        name: "Change Request",
        eventKey: ["Changed Event"],
      },
      {
        name: "New Item",
        eventKey: ["New Item"],
      },
      {
        name: "New Comment",
        eventKey: ["Comment"],
      },
      {
        name: "Item Removed",
        eventKey: ["Dropped Item"],
      },
    ],
  },
};

export const DASHBOARD_VIEW_KEY_MAPPER = {
  TASK_VIEW_KEY: "taskView",
  ALERT_VIEW_KEY: "alertView",
  PLANNING_VIEW_KEY: "planningView",
  ALLOWANCE_VIEW_KEY: "allowanceView",
  DSD_FUNDING_VIEW_KEY: "dsdFundingView",
};

export const DEFAULT_SORT_KEYS_MAPPER = {
  taskView: {
    event: 0,
    date: 0,
    vendor: 0,
    type: 0,
    taskType: 0,
    createdOn: 0,
  },
  alertView: {
    event: 0,
    date: 0,
    vendor: 0,
    type: 0,
    alertType: 0,
  },
  allowanceView: {
    offerId: 0,
    allowanceType: 0,
    event: 0,
    date: 0,
    vendor: 0,
    type: 0,
    offerStatus: 0,
  },
  planningView: {
    event: 0,
    date: 0,
    vendor: 0,
    type: 0,
    eventStatus: 0,
  },
};

export const SORT_KEYS_MAPPER = {
  taskView: {
    optionKey: "tasksSortType",
    event: { 1: "Event Nbr Asc", 2: "Event Nbr Desc" },
    date: { 1: "Event Date", 2: "Event Date Desc" },
    vendor: { 1: "Vendor", 2: "Vendor Desc" },
    type: { 1: "Event Status", 2: "Event Status Desc" },
    taskType: { 1: "Task Type", 2: "Task Type Desc" },
    createdOn: { 1: "Created On", 2: "Created On Desc" },
  },
  alertView: {
    optionKey: "tasksSortType",
    event: { 1: "Event Nbr Asc", 2: "Event Nbr Desc" },
    date: { 1: "Event Date", 2: "Event Date Desc" },
    vendor: { 1: "Vendor", 2: "Vendor Desc" },
    type: { 1: "Event Status", 2: "Event Status Desc" },
    alertType: { 1: "Alert Type", 2: "Alert Type Desc" },
  },
  allowanceView: {
    optionKey: "offerPlanEventProjectionType",
    offerId: { 1: "OFFER_ID_ASC", 2: "OFFER_ID_DESC" },
    allowanceType: { 1: "ALLOWANCE_TYPE_ASC", 2: "ALLOWANCE_TYPE_DESC" },
    event: { 1: "EVENT_ID_ASC", 2: "EVENT_ID_DESC" },
    date: { 1: "EVENT_DATE_ASC", 2: "EVENT_DATE_DESC" },
    vendor: { 1: "VENDOR_ASC", 2: "VENDOR_DESC" },
    type: { 1: "EVENT_STATUS_ASC", 2: "EVENT_STATUS_DESC" },
    offerStatus: { 1: "OFFER_STATUS_ASC", 2: "OFFER_STATUS_DESC" },
  },
  planningView: {
    optionKey: "planEventSortType",
    event: { 1: "PLAN_EVENT_NBR_ASC", 2: "PLAN_EVENT_NBR_DESC" },
    date: {
      1: "PRODUCT_GROUP_AGGR_EVENT_DATE_ASC",
      2: "PRODUCT_GROUP_AGGR_EVENT_DATE_DESC",
    },
    vendor: { 1: "PLAN_EVENT_VENDOR_ASC", 2: "PLAN_EVENT_VENDOR_DESC" },
    eventStatus: { 1: "PLAN_EVENT_STATUS_ASC", 2: "PLAN_EVENT_STATUS_DESC" },
  },
};

export function updateSearchKeyConfig(currentViewKey) {
  return {
    key: "search-key",
    type: "select",
    options: getOptionsForView(currentViewKey),
    extraConfig: {
      width: 200,
      variant: "tertiary",
    },
  };
}

export const SEARCH_KEY_CONFIG = {
  key: "search-key",
  type: "select",
  options: SEARCH_FILTER_OPTIONS,
  extraConfig: {
    width: 200,
    variant: "tertiary",
  },
};

export const SEARCH_VALUE_CONFIG = {
  key: "search-text",
  type: "search",
  extraConfig: {
    placeHolder: "Search",
    className: "!rounded",
  },
};

export interface DRAGGABLECOLUMNCONFIG {
  key: string;
  displayKey?: string;
  secDisplaykey?: string;
  secKey?: string;
  label: string;
  visible: boolean;
  renderMethod?: string;
  isRenderDate?: boolean;
  width?: string;
}

export const EXPORTABLE_COLUMNS_ARRAY = [
  {
    key: "DIVISION",
    label: "Division",
    defaultVisible: { allowanceView: false, planningView: true },
    visible: false,
    dashboardToVisible: ["allowanceView", "planningView"],
  },
  {
    key: "ASM",
    label: "ASM",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView", "planningView"],
  },
  {
    key: "PRIMARY_RETAIL_SECTION",
    label: "Primary Retail Section",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView", "planningView"],
  },
  {
    key: "PRIMARY_CATEGORY",
    label: "Primary Category",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView", "planningView"],
  },
  {
    key: "CATEGORIES",
    label: "Categories",
    defaultVisible: { allowanceView: false, planningView: true },
    visible: false,
    dashboardToVisible: ["allowanceView", "planningView"],
  },
  {
    key: "EVENT_ID",
    label: "Event Id",
    defaultVisible: { allowanceView: false, planningView: true },
    visible: false,
    dashboardToVisible: ["allowanceView", "planningView"],
  },
  {
    key: "EVENT_NAME",
    label: "Event Name",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView", "planningView"],
  },
  {
    key: "EVENT_TYPE",
    label: "Event Type",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView", "planningView"],
  },
  {
    key: "VEHICLE_TYPE",
    label: "Vehicle Type",
    defaultVisible: { allowanceView: false, planningView: true },
    visible: false,
    dashboardToVisible: ["allowanceView", "planningView"],
  },
  {
    key: "VEHICLE_VERSION",
    label: "Vehicle Version",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView", "planningView"],
  },
  {
    key: "VEHICLE_START",
    label: "Vehicle Start",
    defaultVisible: { allowanceView: false, planningView: true },
    visible: false,
    dashboardToVisible: ["allowanceView", "planningView"],
  },
  {
    key: "VEHICLE_END",
    label: "Vehicle End",
    defaultVisible: { allowanceView: false, planningView: true },
    visible: false,
    dashboardToVisible: ["allowanceView", "planningView"],
  },
  {
    key: "EVENT_STATUS",
    label: "Event Status",
    defaultVisible: { allowanceView: false, planningView: true },
    visible: false,
    dashboardToVisible: ["allowanceView", "planningView"],
  },
  {
    key: "PID",
    label: "PID",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView", "planningView"],
  },
  {
    key: "STORE_GROUP_TYPE",
    label: "Store Group Type",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView", "planningView"],
  },
  {
    key: "STORE_GROUP",
    label: "Store Groups",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "STORE_GROUPS",
    label: "Store Groups",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["planningView"],
  },
  {
    key: "STORE_GROUP_STORE_COUNT",
    label: "Store Group Store Count",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView", "planningView"],
  },
  {
    key: "PPG_NBRS",
    label: "PPG #",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView", "planningView"],
  },
  {
    key: "PRODUCT_GROUP_NAMES",
    displayKey: "name",
    label: "Product Group Names",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView", "planningView"],
  },
  {
    key: "PPG_TYPE",
    label: "PPG Type",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView", "planningView"],
  },
  {
    key: "OFFER_ID",
    label: "Offer ID",
    defaultVisible: { allowanceView: true, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView", "planningView"],
  },
  {
    key: "OFFER_DESC",
    label: "Offer Desc",
    defaultVisible: { allowanceView: true, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "OFFER_STATUS",
    label: "Offer Status",
    defaultVisible: { allowanceView: true, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "ALLOWANCE_ID",
    label: "Allowance ID",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "ALLOWANCE_LOC",
    label: "Allowance Loc",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "MERCHANT_VENDOR",
    label: "Merch Vendor #",
    defaultVisible: { allowanceView: true, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "MERCHANT_VENDOR_NAME",
    label: "Merch Vendor Name",
    defaultVisible: { allowanceView: true, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "ALLOWANCE_TYPE",
    label: "Allowance Type",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "ALLOWANCE_PERFORMANCE",
    label: "Allowance Performance",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "ALLOWANCE_START",
    label: "Allowance Start",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "ALLOWANCE_END",
    label: "Allowance End",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "CASE_ORDER_START",
    label: "Order Start",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "CASE_ORDER_END",
    label: "Order End",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "CASE_ARRIVAL_START",
    label: "Arrival Start",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "CASE_ARRIVAL_END",
    label: "Arrival End",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "ALLOWANCE_MIN_ALLOW_AMOUNT",
    label: "Allowance Min Amount",
    defaultVisible: { allowanceView: true, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "ALLOWANCE_MAX_ALLOW_AMOUNT",
    label: "Allowance Max Amount",
    defaultVisible: { allowanceView: true, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "UOM",
    label: "UOM",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "HEADER_FLAT",
    label: "Total Header Flat",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "DEFAULT_PAYMENT_TYPE",
    label: "Default Payment Type",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "DEFAULT_BILLING_AP_OR_AR",
    label: "Default Billing AP or AR #",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "SUGGESTED_PAYMENT_TYPE",
    label: "Suggested Payment Type",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "SUGGESTED_AR_OR_AP",
    label: "Suggested AR or AP #",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
  {
    key: "PROMOTION_ID",
    label: "Promotion ID",
    defaultVisible: { allowanceView: false, planningView: true },
    visible: false,
    dashboardToVisible: ["planningView"],
  },
  {
    key: "PROMOTION_START",
    label: "Promotion Start",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["planningView"],
  },
  {
    key: "PROMOTION_END",
    label: "Promotion end",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["planningView"],
  },
  {
    key: "PROMOTION_TYPE",
    label: "Promotion Type",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["planningView"],
  },
  {
    key: "PROMOTION_FACTOR",
    label: "Promotion Factor",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["planningView"],
  },
  {
    key: "MIN_PROMOTION_AMOUNT",
    label: "Min Promotion Amount",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["planningView"],
  },
  {
    key: "MAX_PROMOTION_AMOUNT",
    label: "Max Promotion Amount",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["planningView"],
  },
  {
    key: "PROMOTION_UOM",
    label: "Promotion UOM",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["planningView"],
  },
  {
    key: "PROMOTION_ITEM_LIMIT",
    label: "Promotion Item Limit",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["planningView"],
  },
  {
    key: "PROMOTION_MINIMUM_QUANTITY",
    label: "Promotion Minimum Quantity",
    defaultVisible: { allowanceView: false, planningView: false },
    visible: false,
    dashboardToVisible: ["planningView"],
  },
  {
    key: "VENDOR_OFFER_TRACKING_NBR",
    label: "Vendor Tracking #",
    defaultVisible: { allowanceView: true, planningView: false },
    visible: false,
    dashboardToVisible: ["allowanceView"],
  },
];

export const ALLOWVIEW_DRAGGABLE_OPTIONS_MAPPER: {
  [key: string]: DRAGGABLECOLUMNCONFIG;
} = {
  ASM: {
    key: "asmsmic.dasm",
    label: "ASM",
    visible: false,
  },
  "Allowance ID": {
    key: "allowanceIdNbr",
    label: "Allowance ID",
    visible: true,
  },
  "Allowance Start": {
    key: "minAggrAllowanceStartDate",
    label: "Allowance Start",
    visible: true,
    isRenderDate: true,
  },
  "Allowance End": {
    key: "maxAggrAllowanceEndDate",
    label: "Allowance End",
    visible: true,
    isRenderDate: true,
  },
  "Allowance Performance": {
    key: "performance.performance",
    label: "Allowance Performance",
    visible: true,
    width: "13vw",
  },
  "Allowance Amount": {
    key: "allowanceAmount",
    label: "Allowance Amount",
    visible: true,
    renderMethod: "renderAllowAmt",
  },

  "Allowance Loc": {
    key: "vendorNbr",
    label: "Allowance Loc",
    visible: false,
    width: "12vw",
  },

  "Allowance Type": {
    key: "allowanceType",
    label: "Allowance Type",
    visible: true,
    renderMethod: "renderAllowanceType",
  },
  "Arrival End": {
    key: "arrivalEndDate",
    label: "Arrival End",
    visible: false,
    isRenderDate: true,
  },
  "Arrival Start": {
    key: "arrivalStartDate",
    label: "Arrival Start",
    visible: false,
    isRenderDate: true,
  },
  "Default Payment Type": {
    key: "allowanceBillingInfo.absVendorPaymentType",
    label: "Default Payment Type",
    visible: false,
  },
  "Default Billing AP or AR #": {
    key: "defaultBillingApOrAr",
    label: "Default Billing AP or AR #",
    visible: false,
    renderMethod: "renderDefaultBillingApOrAr",
    width: "13vw",
  },
  Division: {
    key: "divisionIds",
    label: "Division",
    visible: true,
    renderMethod: "renderDivision",
  },
  "Event Type": {
    key: "eventType",
    label: "Event Type",
    visible: false,
    renderMethod: "renderEventType",
    width: "13vw",
  },
  "Order End": {
    key: "orderEndDate",
    label: "Order End",
    visible: false,
    isRenderDate: true,
  },
  "Order Start": {
    key: "orderStartDate",
    label: "Order Start",
    visible: false,
    isRenderDate: true,
  },
  PID: {
    key: "periscopeDetails.periscopeId",
    label: "PID",
    visible: false,
    renderMethod: "renderPid",
  },
  "PPG #": {
    key: "planProductGroups",
    displayKey: "sourceProductGroupId",
    label: "PPG #",
    visible: false,
    renderMethod: "renderSgPGDetails",
  },
  "PPG Type": {
    key: "ppgType",
    label: "PPG Type",
    visible: false,
    renderMethod: "renderPPGType",
  },
  "Primary Category": {
    key: "asmsmic.smic",
    label: "Primary Category",
    visible: false,
  },
  "Primary Retail Section": {
    key: "asmsmic.sect",
    label: "Primary Retail Section",
    visible: false,
    width: "13vw",
  },
  "Product Group Names": {
    key: "planProductGroups",
    displayKey: "name",
    label: "Product Group Names",
    visible: false,
    renderMethod: "renderSgPGDetails",
    width: "30vw",
  },
  "Store Group Store Count": {
    key: "totalSgCount",
    label: "Store Group Store Count",
    visible: false,
    renderMethod: "renderSGDetails",
    width: "12vw",
  },
  "Store Group Type": {
    key: "storeGroupsType",
    label: "Store Group Type",
    visible: false,
    renderMethod: "renderSGDetails",
  },
  "Store Groups": {
    key: "storeGroups",
    displayKey: "storeGroupName",
    secDisplaykey: "storeCount",
    label: "Store Groups",
    visible: false,
    renderMethod: "renderSgPGDetails",
  },
  "Suggested Payment Type": {
    key: "allowanceBillingInfo.suggestedVendorPaymentType",
    label: "Suggested Payment Type",
    visible: false,
    width: "13vw",
  },
  "Suggested AR or AP #": {
    key: "suggestedBillingApOrAr",
    label: "Suggested AR or AP #",
    visible: false,
    renderMethod: "renderDefaultBillingApOrAr",
    width: "13vw",
  },
  UOM: {
    key: "uoms",
    label: "UOM",
    visible: false,
    renderMethod: "renderUomInfo",
  },
  "Vehicle End": {
    key: "vehicle.endDate",
    label: "Vehicle End",
    visible: false,
    isRenderDate: true,
  },
  "Vehicle Start": {
    key: "vehicle.startDate",
    label: "Vehicle Start",
    visible: false,
    isRenderDate: true,
  },
  "Vehicle Type": {
    key: "vehicle.vehicleType.vehicleTypDesc",
    label: "Vehicle Type",
    visible: false,
  },
  "Vehicle Version": {
    key: "vehicle.vehicleNm",
    label: "Vehicle Version",
    visible: false,
  },
  "Vendor Tracking #": {
    key: "allowanceBillingInfo.vendorOfferTrackingNbr",
    label: "Vendor Tracking #",
    visible: false,
  },
};

export const ITEM_COLS_COLLAPSED_TABLE_DATA = [
  {
    title: "Item Descriptions",
    key: "itemDescription",
    methodMapper: "renderItemField",
    headerClass: "item-col-header px-3 font-bold py-4",
    id: "abs-task-view-grid-collapsed-table-itemDesc",
    containerClass: "item-col-value flex flex-col gap-2 py-2 px-3 pr-2",
    colClassName: {
      flex: "1 0 22%",
    },
  },
  {
    title: "CIC",
    key: "itemId",
    methodMapper: "renderItemField",
    headerClass: "item-col-header px-3 font-bold py-4",
    id: "abs-task-view-grid-collapsed-table-cic",
    containerClass: "item-col-value flex flex-col gap-2 py-2 px-3 col-span-1",
    colClassName: {
      flex: "1 0 10%",
    },
  },
  {
    title: "Primary UPC",
    key: "primaryUpc",
    methodMapper: "renderPrimaryUpc",
    headerClass: "item-col-header px-3 font-bold py-4",
    id: "abs-task-view-grid-collapsed-table-primary-upc",
    containerClass: "item-col-value flex flex-col gap-2 py-2 px-3",
    colClassName: {
      flex: "1 0 15%",
    },
  },
  {
    title: "UPCs",
    key: "upcs",
    methodMapper: "renderUpcs",
    headerClass: "item-col-header px-3 font-bold py-4",
    id: "abs-task-view-grid-collapsed-table-upcs",
    containerClass: "item-col-value pl-6 pr-6 flex gap-2 py-2 ",
    colClassName: {
      flex: "1 0 2%",
    },
  },
  {
    title: "UnitType",
    key: "unitType",
    methodMapper: "renderUnitType",
    headerClass: "item-col-header px-3 font-bold py-4",
    id: "abs-task-view-grid-collapsed-table-primary-unitType",
    containerClass: "item-col-value flex gap-2 py-2 px-3",
    colClassName: {
      flex: "1 0 2%",
    },
  },
  {
    title: "Pack",
    key: "pack",
    methodMapper: "renderPack",
    headerClass: "item-col-header px-3 font-bold py-4",
    id: "abs-task-view-grid-collapsed-table-pack",
    containerClass: "item-col-value flex gap-2 py-2 px-3",
    colClassName: {
      flex: "1 0 2%",
    },
  },
  {
    title: "Size",
    key: "descriptiveSize",
    methodMapper: "renderSize",
    headerClass: "item-col-header px-3 font-bold py-4",
    id: "abs-task-view-grid-collapsed-table-size",
    containerClass: "item-col-value flex gap-2 py-2 px-3",
    colClassName: {
      flex: "1 0 5%",
    },
  },
];

export const DASHBOARD_MAPPING_ITEMS = [
  {
    name: "Tasks",
    value: "TaskView",
    key: DASHBOARD_VIEW_KEY_MAPPER.TASK_VIEW_KEY,
    isDisabled: false,
    isSearchDropdown: false,
    isDefaultTab: true,
    header: {
      isRequired: true,
      fields: {
        ...COMMON_DASHBOARD_HEADER_FIELDS,
        toggleCompletedField: {
          type: "toggle",
          label: "Completed",
          defaultValue: false,
        },
        selectSearchOptionField: {
          ...COMMON_DASHBOARD_HEADER_FIELDS.selectSearchOptionField,
          options: getOptionsForView(DASHBOARD_VIEW_KEY_MAPPER.TASK_VIEW_KEY),
        },
      },
    },
    grid: {
      id: "tasks-table",
      itemKey: "taskId",
      dividers: "horizontal",
      columnsKeys: ["event", "vendor", "type", "createdOn"],
      columns: [],
      taskTypeExpandColumns: {
        NEW_ITEM: {
          columns: ITEM_COLS_COLLAPSED_TABLE_DATA,
        },
        DROPPED_ITEM: {
          columns: ITEM_COLS_COLLAPSED_TABLE_DATA,
        },
      },
      expandableColumns: [
        {
          title: "Promo Product Groups",
          key: "ppg",
          renderShowMore: true,
          methodMapper: "renderPPG",
          id: "abs-task-view-grid-collapsed-table-ppg",
          containerClass:
            "pl-6 pr-6 col-span-2 flex flex-col gap-2 py-2 my-3 text-sm border-r-[1px] border-dashed border-[#7296B8] abs-pm-task-cols-collapsed",
        },
        {
          title: "Offers",
          key: "offers",
          methodMapper: "renderOffers",
          id: "abs-task-view-grid-collapsed-table-offers",
          containerClass:
            "pl-6 pr-6 col-span-2 flex flex-col gap-2 py-2 my-3 text-sm border-r-[1px] border-dashed border-[#7296B8] abs-pm-task-cols-collapsed",
        },
        {
          title: "Promotions",
          key: "promotions",
          methodMapper: "renderPromotions",
          id: "abs-task-view-grid-collapsed-table-promotions",
          containerClass:
            "pl-6 pr-6 col-span-3 flex flex-col gap-2 py-2 my-3 text-sm border-r-[1px] border-dashed border-[#7296B8] abs-pm-task-cols-collapsed",
        },
      ],
    },
    tabs: [],
    pagination: DEFAULT_PAGINATION_CONFIG_WITH_TOTAL_ELEMENTS,
  },
  {
    name: "Alerts",
    value: "AlertsView",
    key: DASHBOARD_VIEW_KEY_MAPPER.ALERT_VIEW_KEY,
    isDisabled: false,
    isSearchDropdown: false,
    isDefaultTab: false,
    header: {
      isRequired: true,
      fields: {
        clearSelectedField: {
          type: "button",
          label: "Clear Selected",
          variant: "secondary",
        },
        clearAllField: {
          type: "button",
          label: "Clear All",
          variant: "secondary",
        },
        ...COMMON_DASHBOARD_HEADER_FIELDS,
        toggleClearedField: {
          type: "toggle",
          label: "Cleared",
          defaultValue: false,
        },
        selectSearchOptionField: {
          ...COMMON_DASHBOARD_HEADER_FIELDS.selectSearchOptionField,
          options: getOptionsForView(DASHBOARD_VIEW_KEY_MAPPER.ALERT_VIEW_KEY),
        },
        sortByField: {
          ...COMMON_DASHBOARD_HEADER_FIELDS.sortByField,
          options: [
            {
              name: "Event Start + Event ID",
              eventKey: "Event Start",
              isDefaultValue: true,
            },
            {
              name: "Vendor + Event Start",
              eventKey: "Vendor Start",
            },
          ],
        },
        filterByTypeField: {
          ...COMMON_DASHBOARD_HEADER_FIELDS.filterByTypeField,
          options: [
            {
              name: "All",
              eventKey: FILTER_BY_TYPE_DEFAULT_VAL,
              isDefaultValue: true,
            },
            {
              name: "Event Approved",
              eventKey: ["Event Approved"],
            },
          ],
        },
      },
    },
    grid: {
      id: "alerts-table",
      itemKey: "taskId",
      dividers: "horizontal",
      columnsKeys: ["event", "vendor", "type"],
      columns: [],
    },
    tabs: [],
    pagination: DEFAULT_PAGINATION_CONFIG_WITH_TOTAL_ELEMENTS,
  },
  {
    name: "Planning",
    value: "PlanningView",
    key: DASHBOARD_VIEW_KEY_MAPPER.PLANNING_VIEW_KEY,
    isDisabled: false,
    isSearchDropdown: false,
    isDefaultTab: false,
    component: "PlanningView",
    grid: {
      id: "planning-table",
      itemKey: "id",
      dividers: "horizontal",
      columnsKeys: ["event", "type", "status", "actions"],
      columns: [],
    },
    pagination: DEFAULT_PAGINATION_CONFIG_WITH_TOTAL_ELEMENTS,
    tabs: [
      {
        name: "Invite",
      },
      {
        name: "Plan",
      },
      {
        name: "Optimize",
      },
    ],
  },
  {
    name: "Allowances",
    value: "AllowanceView",
    key: DASHBOARD_VIEW_KEY_MAPPER.ALLOWANCE_VIEW_KEY,
    isDisabled: false,
    isSearchDropdown: false,
    isDefaultTab: false,
    header: {
      isRequired: true,
      fields: {
        ...COMMON_DASHBOARD_HEADER_FIELDS,
        selectSearchOptionField: {
          ...COMMON_DASHBOARD_HEADER_FIELDS.selectSearchOptionField,

          options: getOptionsForView(DASHBOARD_VIEW_KEY_MAPPER.TASK_VIEW_KEY),
        },
        filterByStatusField: {
          type: "selectCheckbox",
          label: "Status",
          width: 200,
          itemKey: "label",
          placeholder: "All",
          variant: "tertiary",
          options: [
            { label: "Agreed", key: "1", isChecked: true },
            { label: "Agreed Pending", key: "2", isChecked: true },
            { label: "Draft", key: "3", isChecked: true },
            { label: "Pending With Merchant", key: "4", isChecked: true },
            { label: "Pending With Vendor", key: "5", isChecked: true },
          ],
        },
        // filterByAllwTypeField: {
        //   type: "selectCheckbox",
        //   label: "Allw Type",
        //   width: 200,
        //   itemKey: "label",
        //   placeholder: "All",
        //   variant: "tertiary",
        //   options: [
        //     { label: "Case", key: "1", isChecked: true },
        //     { label: "Header Flat", key: "2", isChecked: true },
        //     { label: "Item Flat", key: "3", isChecked: true },
        //     { label: "Scan", key: "4", isChecked: true },
        //     { label: "Ship to Store", key: "5", isChecked: true },
        //   ],
        // },
        // filterByPerformanceField: {
        //   type: "selectCheckbox",
        //   label: "Performance",
        //   width: 200,
        //   itemKey: "label",
        //   placeholder: "All",
        //   variant: "tertiary",
        //   options: [
        //     { label: "Price / Ad / Display (88)", key: "1", isChecked: true },
        //     { label: "4U Event (52)", key: "2", isChecked: true },
        //     { label: "Cupon/Grand Opening (75)", key: "3", isChecked: true },
        //     { label: "Discontinued (66)", key: "4", isChecked: true },
        //     { label: "Fuel Rewards (77)", key: "5", isChecked: true },
        //     { label: "Liquor Only (38)", key: "6", isChecked: true },
        //   ],
        // },
        // filterByVehicleTypeField: {
        //   type: "selectCheckbox",
        //   label: "Vehicle Type",
        //   width: 200,
        //   itemKey: "label",
        //   placeholder: "All",
        //   variant: "tertiary",
        //   options: [
        //     { label: "Disco", key: "Disco", isChecked: true },
        //     { label: "Friday ROP", key: "FridayROP", isChecked: true },
        //     {
        //       label: "GO/COS/Opp Vehicle",
        //       key: "GOCOSOppVehicle",
        //       isChecked: true,
        //     },
        //     { label: "Other", key: "Other", isChecked: true },
        //     { label: "Promo Cycle", key: "PromoCycle", isChecked: true },
        //     { label: "Savings Guide", key: "SavingsGuide", isChecked: true },
        //     { label: "Sunday Insert", key: "SundayInsert", isChecked: true },
        //     { label: "Weekly Insert", key: "WeeklyInsert", isChecked: true },
        //   ],
        // },
      },
    },
    grid: {
      id: "allowance-table",
      itemKey: "offerNumber",
      dividers: "horizontal",
      columnsKeys: ["event", "vendor", "type"],
      columns: [],
      expandableColumns: ["Promo Product Groups", "Offers", "Promotions"],
    },
    tabs: [],
    pagination: DEFAULT_PAGINATION_CONFIG_WITH_TOTAL_ELEMENTS,
  },
  {
    name: "DSD Funding",
    value: "DSDFundingView",
    key: DASHBOARD_VIEW_KEY_MAPPER.DSD_FUNDING_VIEW_KEY,
    isDisabled: false,
    isSearchDropdown: false,
    isDefaultTab: false,
    header: {
      isRequired: true,
      fields: {
        togglePotentialField: {
          type: "toggle",
          label: "Show Potential funding Omissions only",
          defaultValue: false,
        },
        selectSearchOptionField: {
          ...COMMON_DASHBOARD_HEADER_FIELDS.selectSearchOptionField,
          options: getOptionsForView(
            DASHBOARD_VIEW_KEY_MAPPER.DSD_FUNDING_VIEW_KEY
          ),
        },
        toggleClearedField: {
          type: "toggle",
          label: "Show potentional funding omissions only",
          defaultValue: false,
        },
      },
    },
  },
];

export const PLANNING_VIEW_FILTER = {
  searchByOptions: [
    {
      name: "Event Name",
      eventKey: "eventNames",
      default: true,
    },
    {
      name: "CIC ID#",
      eventKey: "cics",
    },
    {
      name: "Event ID#",
      eventKey: "planEventIdNbrs",
    },
    {
      name: "Periscope ID#",
      eventKey: "periscopeId",
    },
    {
      name: "Offer ID#",
      eventKey: "offerNumber",
    },
  ],

  periscopeStatusOption: [
    {
      name: "Approved",
      eventKey: "approved",
      default: true,
    },
    {
      name: "Done",
      eventKey: "done",
    },
    {
      name: "PreApproved",
      eventKey: "preApproved",
    },
    {
      name: "Proposed",
      eventKey: "proposed",
    },
    {
      name: "Rejected",
      eventKey: "rejected",
    },
    {
      name: "Return To Vendor Portal",
      eventKey: "returnToVendorPortal",
    },
  ],

  eventStatusOptions: [
    {
      name: STATUS_CONSTANTS?.STATUS?.DRAFT?.text,
      checked: true,
      default: true,
    },
    {
      name: STATUS_CONSTANTS?.STATUS?.["PENDING WITH MERCHANT"]?.filtertext,
      checked: true,
    },
    {
      name: STATUS_CONSTANTS?.STATUS?.["PENDING WITH VENDOR"]?.filtertext,

      checked: true,
    },
    { name: STATUS_CONSTANTS?.STATUS?.AGREED?.text, checked: true },
    {
      name: STATUS_CONSTANTS?.STATUS?.["AGREED-PENDING"]?.filtertext,
      checked: true,
    },
    // { name: STATUS_CONSTANTS.STATUS.READY.text, checked: true },
    { name: STATUS_CONSTANTS?.STATUS?.ACTIVE?.text, checked: true },
    { name: STATUS_CONSTANTS?.STATUS?.EXECUTED?.text, checked: true },
    { name: STATUS_CONSTANTS?.STATUS?.CANCELED?.text, checked: true },
    { name: STATUS_CONSTANTS?.STATUS?.REJECTED?.text, checked: true },
  ],
};

export const BILLING_INFO_MAPPER = {
  defaultBillingApOrAr: {
    paymentType: "absVendorPaymentType",
    Deduct: "acPayableVendorNbr",
    Invoice: "acReceivableVendorNbr",
  },
  suggestedBillingApOrAr: {
    paymentType: "suggestedVendorPaymentType",
    Deduct: "suggestedAcPayableVendorNbr",
    Invoice: "suggestedAcReceivableVendorNbr",
  },
};

export const EVENT_TYPE_ABBREVIATION = {
  DP: "Division Promotion",
  AO: "Division Allowance Only",
  NP: "Non-Promotion",
  MD: "Multi-Division",
};

export function getOptionsForView(viewName) {
  return SEARCH_FILTER_OPTIONS.filter(option =>
    option?.isShow?.includes(viewName)
  );
}

export enum DSD_SEARCHINPUT_KEY_MAPPING {
  offerNumbers = "OFFER",
  promotionIds = "PROMO",
}

export const OFFER_ID = "Offer ID#";
