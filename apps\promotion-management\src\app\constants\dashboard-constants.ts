const ALLOWANCE_TYPES = {
  CASE: {
    key: "CASE",
    value: "CASE",
    label: "Case",
    allowMapKey: "caseAllow",
    allowanceCd: "C",
    createInd: ["CW", "CD", "CB", "CC"],
    defaultPerfOption: {
      DP: { CB: "Off Invoice (01)", CD: "DSD Off Invoice (01)" },
      AO: { CB: "Auto-Deduct (01)", CD: "DSD Off Invoice (01)" },
    },
  },
  SCAN: {
    key: "SCAN",
    value: "SCAN",
    label: "Scan",
    allowMapKey: "scanAllow",
    allowanceCd: "T",
    createInd: ["TC", "TS"],
    defaultPerfOption: {
      DP: { TC: "", TS: "" },
      AO: { TC: "Non-Performance (01)", TS: "Non-Performance (01)" },
    },
  },
  SHIPTOSTORE: {
    key: "SHIP_TO_STORE",
    value: "SHIP TO STORE",
    label: "Ship To Store",
    allowMapKey: "shipAllow",
    allowanceCd: "S",
    createInd: ["SB", "SD"],
    defaultPerfOption: {
      DP: {
        SB: "Price / Ad / Display (88)",
        SD: "Price / Ad / Display (88)",
      },
      AO: {
        SB: "Non-Performance (01)",
        SD: "Non-Performance (01)",
      },
    },
  },
  HEADERFLAT: {
    key: "HEADER_FLAT",
    value: "HEADER FLAT",
    label: "Header Flat",
    allowMapKey: "headerFlatAllow",
    allowanceCd: "A",
    createInd: ["LC", "LW"],
    defaultPerfOption: {
      DP: {
        LC: "Price / Ad / Display (88)",
        LW: "Price / Ad / Display (88)",
      },
      AO: {},
    },
  },
  ITEMFLAT: {
    key: "ITEM_FLAT",
    value: "ITEM FLAT",
    label: "Item Flat",
    allowMapKey: "itemFlatAllow",
    allowanceCd: "A",
    createInd: ["AC", "AW"],
    defaultPerfOption: {
      DP: {
        AC: "Price / Ad / Display (88)",
        AW: "Price / Ad / Display (88)",
      },
      AO: {
        AC: "Placement (04)",
        AW: "Placement (04)",
      },
    },
  },
};

const ALLOWANCE_CD_MAPPER = {
  A: "Item/Header Flat",
  S: "Ship To Store",
  T: "Scan",
  C: "Case",
};

const HF_IF_KEY_ABBRIVATE_MAPPER = {
  HEADER_FLAT: "Header Flat",
  ITEM_FLAT: "Item Flat",
};

const STATUS_USER_MAPPER = {
  VENDOR: "Pending With Vendor",
  MERCHANT: "Pending With Merchant",
};

const UOM_KEY_VALUE_MAPPER = {
  CA: "CA",
  EA: "EA",
  WE: "LB",
  RW: "LB",
  LB: "LB",
};

const CREATE_IND_ALLOW_TYPE_MAP = {
  CW: "CASE",

  CD: "CASE",

  CB: "CASE",

  TC: "SCAN",

  TS: "SCAN",

  SB: "SHIP_TO_STORE",

  SD: "SHIP_TO_STORE",

  LC: "HEADER_FLAT",

  LW: "HEADER_FLAT",

  AC: "ITEM_FLAT",

  AW: "ITEM_FLAT",
};

const OFFER_AMOUNT_FIELDS = [
  "offerallowances.allowances.allowanceItems.allowanceItemComps.allowAmount",
  "offerallowances.allowances.headerFlatAmt",
  "offerallowances.allowances.allowanceItems.uom",
];

const PROMOTION_AMOUNT_FIELDS = [
  "promotions.promoDetails.amount",
  "promotions.promoDetails.promotionType",
];

const STRIKE_OFF_FIELDS = {
  OFFER: {
    itemName: "offer",
    itemField: "offerAllowanceChanges",
    itemNumberField: "offerNumber",
  },
  PROMO: {
    itemName: "promo",
    itemField: "promotionsChanges",
    itemNumberField: "promotionId",
  },
};

const PROMOTION_TYPES_WITHOUT_AMOUNTS = [
  "BUY_ONE_GET_ONE",
  "BUY_X_GET_ONE",
  "UN_SUPPORTED",
];
const FORECAST_DISPLAY = {
  MERCHANT: false,
  VENDOR: false,
};
export const DASHBOARD_CONSTANTS = {
  ALLOWANCE_TYPES,
  ALLOWANCE_CD_MAPPER,
  HF_IF_KEY_ABBRIVATE_MAPPER,
  STATUS_USER_MAPPER,
  UOM_KEY_VALUE_MAPPER,
  OFFER_AMOUNT_FIELDS,
  PROMOTION_AMOUNT_FIELDS,
  STRIKE_OFF_FIELDS,
  PROMOTION_TYPES_WITHOUT_AMOUNTS,
  FORECAST_DISPLAY,
  CREATE_IND_ALLOW_TYPE_MAP,
};
export enum EVENT_TYPE {
  NCDP = "NCDP",
  NDP = "NDP",
}