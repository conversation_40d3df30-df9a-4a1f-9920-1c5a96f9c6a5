import AllowanceFormWrapper, {
  IFormControls,
} from "../../../common/allowance-form-wrapper";
import {
  OFFER_ALLOWANCE,
  OFFER_DATES_MAPPER,
  OFFER_FORM_FIELDS,
  OFFER_SECTIONS,
} from "../../offer-flow-config";
import AllowanceTypePerformace from "./allowancetype-performance-section";
import {
  checkIsStoreGroupSectionEnable,
  formatTimestampToDate,
  getAllowanceFormRegisterKey,
  getAllowanceKey,
  getAllowanceMapKey,
  isHfOrIfType,
} from "../../../../../service/allowance/allowance-service";
import { useSelectorWrap } from "@me/data-rtk";
import { useEffect, useState, memo } from "react";
import AllowanceDatesSection from "../dates/dates-section";
import useHandleOfferSections from "../../hooks/useHandleOfferSections";
import AllowanceToBeCreated from "./allowance-to-be-created-section";
import { useDispatch } from "react-redux";
import {
  setAllowanceFormInfo,
  setIsOfferSectionUpdated,
  setOfferAmontsData,
} from "../../../../../service/slice/allowance-details-slice";
import {
  buildOverRideArray,
  checkAmountRefreshCondition,
  checkIsNationalEvent,
  getBatchPayloadByDivisions,
  getNationalVehicleForTemp,
  getOfferMapKey,
  getStoregroupFormData,
  getUpdatedAllowanceTempData,
  resetOfferSectionData,
  sortDivisionIds,
} from "../../offer-service";
import useAllowTempworkUpdate from "../../../../../hooks/useAllowTempworkUpdate";
import useGetOfferSectionConfiguration from "../../hooks/useGetOfferSectionConfiguration";
import StoreSelection from "./store-selection";
import Modal from "@albertsons/uds/molecule/Modal";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";
import {
  useLazyGetAllowancesItemsQuery,
  useLazyGetNationalAllowancesItemsQuery,
  usePostAllowanceToBeCreatedMutation,
} from "../../../../../service/apis/allowance-api";
import LoadingSpinner from "../../../../../constants/LoadingSpinner/LoadingSpinner";
import useNationalAllowTempUpdate from "../../../../../hooks/useNationalAllowTempUpdate";
import { getObjectKeys } from "../../../../../service/allowance/allowance-stepper-service";
import useFetchCombinedData from "../../national/hooks/useFetchCombinedData";
import { isFeatureFlagEnabled, isVendorLoggedIn } from "@me-upp-js/utilities";
import { appConstants } from "@me/utils-root-props";
import { useHandleAmountsResponse } from "../../hooks/amounts/useHandleAmountsResponse";
import { AlertTriangle } from "lucide-react";
import Button from "@albertsons/uds/molecule/Button";

interface IOfferPrimeSectionProps {
  sectionKey: string;
  cardIndex: number;
  cardItemIndex: number;
  isEditEnable: boolean;
  isLastStep?: boolean;
}

function OfferPrimeSection({
  sectionKey,
  cardIndex = 0,
  cardItemIndex = 0,
  isEditEnable = false,
}: IOfferPrimeSectionProps) {
  const configuration = OFFER_ALLOWANCE?.[sectionKey] || {};
  const { create, edit } = configuration;
  const {
    perfConfigKey,
    createIndKey,
    allowanceToBeCreatedOptionKey,
    storeGroupsRegField,
    allowanceTypeChangeKey,
    performanceChangeKey,
    hfAmountChangeKey,
    hfAmountRegField,
    AllowanceToBeCreatedChangeKey,
    storeSelectionChangeKey,
    additionalDatesChangeKey,
    hfAmountHeaderField,
  } = OFFER_FORM_FIELDS;
  const { ALLOWANCE_TYPES, OFFER_ALLOWANCE_GROUP, OFFER_ITEM_ERRORS } =
    efConstants;
  const { CASE } = ALLOWANCE_TYPES;
  const sectionChangeKeys = [
    allowanceTypeChangeKey,
    performanceChangeKey,
    hfAmountChangeKey,
    storeSelectionChangeKey,
    AllowanceToBeCreatedChangeKey,
    additionalDatesChangeKey,
    hfAmountHeaderField,
  ];

  const dispatch = useDispatch();

  const { allowanceFormData: allowanceForm } =
    useSelectorWrap("allowance_form_data")?.data || {};
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { productSources = [] } =
    useSelectorWrap("allowance_product_sources_rn")?.data || {};
  const { allowanceData: tempworkData } =
    useSelectorWrap("allowance_temp_work")?.data || {};
  const { data: allowancePerfData } = useSelectorWrap(
    "allowance_type_performance_data"
  );
  const { offerAmounts = {}, isAdditionalDatesChanged = {} } =
    useSelectorWrap("offer_amounts_details")?.data || {};
  const { isPrimeSectionSectionUpdated = false } =
    useSelectorWrap("offer_section_update")?.data || {};
  const offerSectionsEnableConfig =
    useSelectorWrap("offer_sections_enable_config")?.data || {};
  const { isMultiVendorEnabled } = useSelectorWrap(
    "vendors_data_for_allowance_rn"
  ).data;
  const { offerDivisions = [] } =
    useSelectorWrap("national_offer_divisions")?.data || {};

  const allowanceRegField = getAllowanceFormRegisterKey(
    cardIndex,
    cardItemIndex
  );

  // API call to save data in temp
  const {
    getTempworkObject,
    getVehicleDatesMap,
    getAllowanceMapData,
    saveAllowancePrimeSectionData,
    isLoading: isTempLoading,
  } = useAllowTempworkUpdate();

  // API call to save National data in temp
  const {
    getNationalTempworkObject,
    saveNationalAllowancePrimeSectionData,
    isLoading: isNationalTempLoading,
  } = useNationalAllowTempUpdate();

  const { handleTempDeleteForInvalidItems, isTempDelInprogress } =
    useHandleAmountsResponse();
  const { setOfferSections } = useHandleOfferSections();
  const { moveToNextSectionOnCreate, moveToNextSectionOnUpdate } =
    useGetOfferSectionConfiguration({
      allowanceRegField,
    });

  const [postEventDetailsData, { isLoading: postEventsLoading }] =
    usePostAllowanceToBeCreatedMutation();

  const {
    id: eventId = "",
    eventType = "",
    offerAllowances = [],
    inValidAllowances = [],
    eventCreationVehicle = {},
    nationalInfos = {},
  } = eventDetailsData || {};
  const isOfferInvalid = inValidAllowances?.includes(
    offerAllowances?.[cardIndex]?.id
  );
  const isNationalEvent = checkIsNationalEvent(eventType);
  const updatedTempworkData = getUpdatedAllowanceTempData(
    tempworkData,
    eventType
  );
  const isMultiVendorFeatureEnable = isFeatureFlagEnabled(
    appConstants.FEATURE_FLAGS.MULTI_VENDOR
  );

  const defaultPerfConfig =
    allowancePerfData?.performanceConfig?.[allowanceRegField];
  const [formControls, setFormControls] = useState<IFormControls>();
  const {
    getValues = () => null,
    setValue = () => null,
    reset,
    control,
  } = formControls || {};
  const [values, setValues] = useState<any>();
  const [isSaveBtnVisable, setIsSaveBtnVisable] = useState(false);
  const [isAllowminSfoTriggered, setIsAllowminSfoTriggered] = useState(
    isMultiVendorFeatureEnable && isVendorLoggedIn()
  );
  const [isModalPopupOpen, setIsModalPopupOpen] = useState(true);

  const formData = getValues() || {};
  const { allowanceType = "", performance = "" } = formData || {};
  const allowanceTypeKey = getAllowanceKey(allowanceType?.toUpperCase());
  const allowanceToBeCreatedOption = formData?.[allowanceToBeCreatedOptionKey];
  const perfOption = formData?.[perfConfigKey];
  const defaultCreateInd = formData?.[createIndKey];
  const offerMapKey = getOfferMapKey(defaultCreateInd);
  const isDsdOffer = offerMapKey === OFFER_ALLOWANCE_GROUP.CASE.DSD;
  const nationalVehicles = nationalInfos?.nationalDivs || [];

  const allowName =
    getAllowanceMapKey(
      allowanceTypeKey || updatedTempworkData?.allowanceType
    ) || "";
  const isHfOrIf = isHfOrIfType(allowanceTypeKey);
  const dateSectionConfig =
    OFFER_DATES_MAPPER.find(item =>
      item.allowanceToBeCreated.includes(allowanceToBeCreatedOption?.name)
    ) || OFFER_ALLOWANCE?.[OFFER_SECTIONS.ALLOWANCE_DATES];
  const allowanceFormData = allowanceForm?.[allowanceRegField];
  const isSectionFocus = offerSectionsEnableConfig?.[sectionKey]?.scrollTo;

  const allowanceAmountsPayload = {
    URL_PARAMS: [eventId, offerMapKey],
  };
  const allowanceAmountQueryParams = getBatchPayloadByDivisions(
    {
      URL_PARAMS: [eventId, offerMapKey],
    },
    offerDivisions,
    { skipOverlaps: false }
  );

  const [fetchAmountsData, rest] = useLazyGetAllowancesItemsQuery();
  const [fetchNationalAmountsData] = useLazyGetNationalAllowancesItemsQuery();

  const {
    state: {
      loading: isAmountsDataLoading = false,
      error: isAmountsFetchError = null,
    },
    fetchCombinedData,
  } = useFetchCombinedData();

  // HF - Check if the amount refresh is required
  const amountRefreshCheck = checkAmountRefreshCondition(
    defaultCreateInd,
    allowanceTypeKey
  );

  useEffect(() => {
    getObjectKeys(formData).length &&
      setOfferSections({ createInd: defaultCreateInd });
  }, [defaultCreateInd]);

  useEffect(() => {
    if (allowanceForm?.[cardIndex]?.cancel) {
      resetOfferSectionData(dispatch);
      reset?.({
        allowanceType: null,
        performance: null,
        [perfConfigKey]: null,
        [createIndKey]: null,
        overrideHeaderFlatAmt: null,
      });
    }
  }, [allowanceForm]);

  useEffect(() => {
    const checkIsUpdateBtnVisable = () => {
      const isVisable =
        allowanceFormData?.isAdditionalDatesChanged ||
        sectionChangeKeys.find(key => formData?.[key]);
      setIsSaveBtnVisable(!!isVisable);
    };
    checkIsUpdateBtnVisable();
  }, [formData, allowanceFormData?.isAdditionalDatesChanged]);

  const getFormControls = (controls: IFormControls) => {
    setFormControls(controls);
  };

  const handleSectionsEnableConfig = (key: string) => {
    if (!configuration) return false;
    else if (key === configuration?.allowanceToBeCreated?.key) {
      return (
        !configuration?.allowanceToBeCreated?.hidden?.[eventType]?.includes(
          allowanceTypeKey
        ) &&
        allowanceType &&
        performance
      );
    } else if (key === configuration?.storeSelection?.key) {
      return (
        !configuration?.storeSelection?.hidden?.[eventType]?.includes(
          allowanceTypeKey
        ) &&
        allowanceType &&
        performance &&
        checkIsStoreGroupSectionEnable(defaultCreateInd)
      );
    } else if (key === configuration?.allowanceDates?.key) {
      return (
        allowanceType &&
        performance &&
        perfOption?.id &&
        (isHfOrIf ||
          (!isAllowminSfoTriggered &&
            defaultCreateInd &&
            allowanceToBeCreatedOption?.key))
      );
    }
    return false;
  };
  const showStoreSelection = handleSectionsEnableConfig(
    configuration?.storeSelection?.key
  );

  const allowanceTypePerformanceContent = (
    <AllowanceTypePerformace
      formControls={formControls}
      cardIndex={cardIndex}
      cardItemIndex={cardItemIndex}
      allowanceRegField={allowanceRegField}
      sectionConfiguration={configuration}
      isEditEnable={isEditEnable}
    />
  );

  const storeSelectionContent = showStoreSelection ? (
    <StoreSelection
      formControls={formControls}
      allowanceRegField={allowanceRegField}
      sectionConfiguration={configuration}
      isEditEnable={isEditEnable}
    />
  ) : null;

  const allowanceToBeCreatedContent = handleSectionsEnableConfig(
    configuration?.allowanceToBeCreated?.key
  ) ? (
    <AllowanceToBeCreated
      formControls={formControls}
      allowanceRegField={allowanceRegField}
      sectionConfiguration={configuration}
      isEditEnable={isEditEnable}
      values={values}
      setIsAllowminSfoTriggered={setIsAllowminSfoTriggered}
    />
  ) : null;

  const allowanceDatesContent = handleSectionsEnableConfig(
    configuration?.allowanceDates?.key
  ) ? (
    <AllowanceDatesSection
      cardIndex={cardIndex}
      cardItemIndex={cardItemIndex}
      formControls={formControls}
      allowanceRegField={allowanceRegField}
      sectionConfiguration={dateSectionConfig}
      sectionKey={sectionKey}
      isEditEnable={isEditEnable}
      values={values}
    />
  ) : null;

  const checkIsSaveBtnHidden = () => {
    return (
      (!isEditEnable && isSectionFocus && allowanceTypeKey) ||
      (isEditEnable && allowanceFormData?.isAdditionalDatesChanged) ||
      (isEditEnable && isOfferInvalid && !isPrimeSectionSectionUpdated) ||
      (isSaveBtnVisable &&
        handleSectionsEnableConfig(configuration?.allowanceDates?.key))
    );
  };

  const handleNextSectionRedirection = (offerMapKey, updatedFormValues) => {
    isEditEnable &&
      dispatch(
        setIsOfferSectionUpdated({
          isOfferSectionUpdated: true,
          ...(isOfferInvalid && { isPrimeSectionSectionUpdated: true }),
          ...(isOfferInvalid &&
            amountRefreshCheck && { isAmountsSectionUpdated: true }),
        })
      );
    !isEditEnable
      ? moveToNextSectionOnCreate(sectionKey, offerMapKey, updatedFormValues)
      : moveToNextSectionOnUpdate(sectionKey);
    reset?.(undefined, { keepValues: true });
  };

  const handleOrderDatesChange = async (
    tempwork,
    amountsData,
    vehicleDatesMap,
    updatedFormValues
  ) => {
    let result: any = null;
    if (isNationalEvent) {
      const nationalPayload = tempwork?.map((tempData, index: number) => {
        return {
          ...tempData,
          allowanceType: allowanceTypeKey,
          allowanceTypeSpecification: {
            [allowName]: {
              ...tempData?.allowanceTypeSpecification?.[allowName],
              allowancesMap: {
                [offerMapKey]: [...(amountsData?.[index]?.allowances || [])],
              },
            },
          },
        };
      });
      result = await saveNationalAllowancePrimeSectionData(nationalPayload);
    } else {
      result = await saveAllowancePrimeSectionData({
        ...tempwork,
        allowanceType: allowanceTypeKey,
        allowanceTypeSpecification: {
          [allowName]: {
            ...tempwork?.allowanceTypeSpecification?.[allowName],
            allowancesMap: {
              [offerMapKey]: [...(amountsData?.allowances || [])],
            },
            ...((vehicleDatesMap && { vehicleDatesMap }) || {}),
          },
        },
      });
    }

    const isValidResposne = isNationalEvent
      ? result?.data?.[0]?.allowanceType
      : result?.data?.allowanceType;
    if (isValidResposne) {
      handleNextSectionRedirection(offerMapKey, updatedFormValues);
    }
  };

  const getNationalPrimeSectionTempData = (
    allowancePerformances,
    offerAllowancesGroupInfoMap,
    overrideHeaderFlatAmt,
    overrideStoreGroups,
    additionalDates,
    createInd
  ) => {
    const nationalVehicleMapData = getNationalVehicleForTemp(
      offerDivisions,
      nationalVehicles,
      offerMapKey
    );
    const vehicleKeys = getObjectKeys(nationalVehicleMapData)?.sort(
      sortDivisionIds
    );
    try {
      // // Initial TEMPWORK construction when there is no temp data or dependent fields are changed related to date.
      const updatedTempwork = vehicleKeys?.map(
        (divId: string, index: number) => {
          return getNationalTempworkObject({
            allowanceTypeKey,
            allowancePerformanceId: allowancePerformances?.id,
            allowancePerformances,
            createInd,
            offerAllowancesGroupInfoMap,
            allowanceToBeCreatedOption,
            amountRefreshCheck,
            overrideHeaderFlatAmt: overrideHeaderFlatAmt?.[index],
            productSources,
            showStoreSelection,
            overrideStoreGroups,
            vehicleDatesMap: nationalVehicleMapData?.[divId],
            additionalDates:
              additionalDates?.find(
                additionalDates => additionalDates?.[0]?.divisionId === divId
              ) || [],
            allowKey: allowName,
            offerMapKey,
            isEditEnable,
            divisionId: divId,
            planEventId: eventDetailsData?.id,
            planEventType: "NDP",
          });
        }
      );

      // console.log("National updatedTempwork dates=>>", updatedTempwork);
      return updatedTempwork;
    } catch (e) {
      console.log(e);
    }
    return {};
  };

  const handleSave = async (values: any) => {
    // Reset the section change keys
    sectionChangeKeys.map(key => setValue(key, false));

    isDsdOffer &&
      isMultiVendorEnabled &&
      (await postEventDetailsData({
        URL_PARAM: eventId,
        queryParams: {
          responseFilter: "allowMin",
        },
        division: offerDivisions,
        promoStartDate: formatTimestampToDate(eventCreationVehicle?.startDate),
      }));

    const overrideStoreGroups = showStoreSelection
      ? buildOverRideArray(values, offerDivisions)
      : [];

    const allowancePerformances = {
      allowanceType: allowanceType,
      ...perfOption,
      performance,
      performanceConfig: defaultPerfConfig,
    };

    const vehicleDatesMap = getVehicleDatesMap(values, offerMapKey);

    const tempAllowData =
      updatedTempworkData?.allowanceTypeSpecification?.[allowName];

    let payload: any = null;
    if (!isNationalEvent) {
      const tempWork = getTempworkObject({
        ...values,
        allowanceTypeKey,
        allowancePerformanceId: allowancePerformances?.id,
        allowancePerformances,
        offerAllowancesGroupInfoMap:
          tempAllowData?.offerAllowancesGroupInfoMap || {},
        defaultPerfConfig,
        allowKey: allowName,
        amountRefreshCheck,
        productSources,
        vehicleDatesMap,
      });

      const allowancesMap = getAllowanceMapData(
        values?.additionalDates || [],
        vehicleDatesMap,
        isEditEnable,
        offerMapKey,
        amountRefreshCheck,
        isEditEnable
      );

      payload = {
        ...updatedTempworkData,
        allowanceType: allowanceTypeKey,
        allowanceTypeSpecification: {
          [allowName]: {
            ...tempAllowData,
            ...tempWork?.allowanceTypeSpecification?.[allowName],
            allowancesMap,
            ...(showStoreSelection && {
              overrideStoreGroups,
            }),
          },
        },
      };
    } else {
      payload = getNationalPrimeSectionTempData(
        allowancePerformances,
        tempAllowData?.offerAllowancesGroupInfoMap || {},
        values?.overrideHeaderFlatAmt,
        overrideStoreGroups,
        values?.additionalDates || [],
        values?.createInd
      );
    }

    const postQuery = isNationalEvent
      ? saveNationalAllowancePrimeSectionData
      : saveAllowancePrimeSectionData;

    const result = await postQuery(payload);
    const isValidResposne = isNationalEvent
      ? result?.data?.[0]?.allowanceType
      : result?.data?.allowanceType;

    if (isValidResposne) {
      if (isEditEnable && isHfOrIf && isOfferInvalid) {
        dispatch(
          setOfferAmontsData({
            offerAmounts: { ...offerAmounts, [offerMapKey]: null },
          })
        );
      }
      const { additionalDates, isDatesValid, ...rest } = values || {};
      const updatedFormValues = {
        [allowanceRegField]: {
          ...(isEditEnable ? allowanceFormData : {}),
          allowancePrimeData: {
            ...rest,
            ...(showStoreSelection && {
              allowanceStoreSelection: getStoregroupFormData(
                overrideStoreGroups,
                isEditEnable
              ),
            }),
          },
          allowanceCreationVehicle: {
            ...vehicleDatesMap,
          },
          additionalDates: {
            ...allowanceFormData?.additionalDates,
            [offerMapKey]: values?.additionalDates || [],
          },
          isAdditionalDatesChanged: false,
        },
      };
      isEditEnable &&
        dispatch(
          setAllowanceFormInfo({
            allowanceFormData: updatedFormValues,
          })
        );

      if (
        (values?.isOrderDatesUpdated &&
          allowanceType === CASE.label &&
          [OFFER_ALLOWANCE_GROUP.CASE.WAREHOUSE].includes(offerMapKey)) ||
        amountRefreshCheck
      ) {
        let amountsResult = isNationalEvent
          ? await fetchCombinedData(
              fetchNationalAmountsData,
              allowanceAmountQueryParams
            )
          : await fetchAmountsData(allowanceAmountsPayload);

        if (!amountsResult?.error) {
          const { amountsResp: updatedAmountsResp = null, tempList = [] } =
            isNationalEvent
              ? await handleTempDeleteForInvalidItems(
                  amountsResult?.data || [],
                  result?.data
                )
              : {};

          amountsResult = isNationalEvent
            ? updatedAmountsResp
            : amountsResult?.data;

          dispatch(
            setOfferAmontsData({
              offerAmounts: {
                ...offerAmounts,
                [offerMapKey]: updatedAmountsResp,
              },
              isAdditionalDatesChanged: {
                ...isAdditionalDatesChanged,
                [offerMapKey]: 0,
              },
            })
          );
          handleOrderDatesChange(
            isNationalEvent && tempList?.length ? tempList : result?.data,
            amountsResult || [],
            amountRefreshCheck ? vehicleDatesMap : null,
            updatedFormValues
          );
        }
        return;
      }
      if (values?.isAdditionalDatesChanged) {
        // Additional dates are changed
        dispatch(
          setOfferAmontsData({
            isAdditionalDatesChanged: {
              ...isAdditionalDatesChanged,
              [offerMapKey]: (isAdditionalDatesChanged?.[offerMapKey] || 0) + 1,
            },
          })
        );
      }
      handleNextSectionRedirection(offerMapKey, updatedFormValues);
    }
  };

  const getInvalidOfferDivisionModal = () => {
    return (
      <Modal
        isOpen={isModalPopupOpen}
        onClose={() => setIsModalPopupOpen(!isModalPopupOpen)}
        className="overflow-hidden national-lead-distributor"
        width={400}
      >
        <div
          className={`flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 gap-6 `}
        >
          <div className="flex flex-col pt-2.5 justify-start items-center flex-grow-0 flex-shrink-0 relative gap-4">
            <AlertTriangle
              viewBox="0 0 28 28"
              height={96}
              width={108}
              strokeWidth={1.5}
            />
            <p
              className={`flex-grow-0 flex-shrink-0 w-[688px] text-[28px] text-center text-[#2b303c] font-bold`}
            >
              {OFFER_ITEM_ERRORS.NO_ITEMS_FOR_ALL_DIVISIONS}
            </p>
          </div>
          <div className="flex justify-center items-start flex-grow-0 flex-shrink-0 gap-4 py-2">
            <Button
              variant="primary"
              width={172}
              onClick={() => setIsModalPopupOpen(!isModalPopupOpen)}
            >
              Return
            </Button>
          </div>
        </div>
      </Modal>
    );
  };

  return (
    <div className="mb-4">
      <LoadingSpinner
        classname="!h-full !w-full rounded-md"
        isLoading={
          isTempLoading ||
          isNationalTempLoading ||
          rest?.isLoading ||
          isAmountsDataLoading ||
          postEventsLoading ||
          isTempDelInprogress
        }
      />
      {offerDivisions?.length === 0 && isNationalEvent
        ? getInvalidOfferDivisionModal()
        : null}
      <div className="text-xl text-[#3997EF] font-bold mb-3">
        {configuration?.label}
      </div>
      <AllowanceFormWrapper
        defaultValues={{}}
        handleSave={handleSave}
        getFormControls={getFormControls}
        footerProps={{
          label: amountRefreshCheck
            ? create?.hfLabel
            : isEditEnable
            ? edit?.label
            : create?.label,
          visable: checkIsSaveBtnHidden(),
          disabled:
            !productSources?.length ||
            !offerDivisions?.length ||
            !!isAmountsFetchError ||
            (isNationalEvent &&
              !isHfOrIf &&
              !offerAmounts?.[offerMapKey]?.length),
        }}
        setValues={setValues}
        variant="primary"
        watchFields={[
          allowanceToBeCreatedOptionKey,
          perfConfigKey,
          storeGroupsRegField,
          hfAmountRegField,
        ]}
      >
        {control ? (
          <>
            {allowanceTypePerformanceContent}
            {storeSelectionContent}
            {allowanceToBeCreatedContent}
            {allowanceType && performance && (
              <div
                className={`border-t-[1px] border-[#C8DAEB] ${
                  isHfOrIf ? "mt-4" : ""
                }`}
              ></div>
            )}
            {allowanceDatesContent}
          </>
        ) : null}
      </AllowanceFormWrapper>
    </div>
  );
}

export default memo(OfferPrimeSection);
