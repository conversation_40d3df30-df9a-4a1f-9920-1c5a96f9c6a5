import { useState, FunctionComponent } from "react";
import { CalendarViewWeek } from "../../../library/features/calendar-view";
import pmConstants from "../../../shared/pm-constants/pm-constants";
import CalendarDashboard from "./calendar-dashboard";
import { getLoggedInUserType } from "@me-upp-js/utilities";
import { EUSER_ROLES } from "@me/util-helpers";
import SortAndFilter from "../../../library/features/calendar-view/lib/filters/sortAndFilter/sort-and-filter";
import { filterDetails } from "../../events-container/event-status-helper";
import PromotionSubHeader from "../../promotion-sub-header/promotion-sub-header";
import PromotionHeader from "../../promotion-header/promotion-header";
import { useSelectorWrap } from "@me/data-rtk";

// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface ICalendarComponentProps {
  selectedTab?: any;
  setSelectedTabValue?: any;
}

const CalendarViewDashboard: FunctionComponent<ICalendarComponentProps> = ({
  selectedTab,
  setSelectedTabValue,
}) => {
  const { data: calendarViewState } = useSelectorWrap("expandedDefaultView_rn");
  const [showSortAndFilter, setShowSortAndFilter] = useState(false);
  const loggedInUser = getLoggedInUserType(),
    isUserMerchant = loggedInUser === EUSER_ROLES.MERCHANT;

  const handleShowSortAndFilterChange = (value: boolean) => {
    setShowSortAndFilter(value);
  };
  return (
    <div
      className={`w-full max-h-[92vh] ${pmConstants.componentClassName.CALENDAR_VIEW_DASHBOARD}`}
      id={pmConstants.CALENDER_EXPANDED_VIEW_CONTAINER_ID}
    >
      <PromotionHeader
        id="abs-pm-calendar-promotion-header"
        selectedTab={selectedTab}
        setSelectedTabValue={setSelectedTabValue}
      />
      <PromotionSubHeader
        selectedTab={selectedTab}
        onButtonClick={() => setShowSortAndFilter(!showSortAndFilter)}
        showSortAndFilter={showSortAndFilter}
      />
      <div
        className={`flex items-start`}
        id="abs-calender-view-dashboard-flex-container"
      >
        <div
          className="abs-pm-event-calendar-container"
          id="abs-calender-view-dashboard-event-calendar-container"
        >
          <CalendarViewWeek />
          <CalendarDashboard filterData={filterDetails} />
        </div>
          <SortAndFilter showSortAndFilter={showSortAndFilter}
            onShowSortAndFilterChange={handleShowSortAndFilterChange} />
      </div>
    </div>
  );
};

export default CalendarViewDashboard;
