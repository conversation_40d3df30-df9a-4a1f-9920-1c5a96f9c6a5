import { appConstants } from "@me/utils-root-props";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";

export const buildURLforAllowanceDashboard = (
  ROUTE_PARAM,
  isAllowConvEnable = false
) => {
  const isAllowConvEnableParam = isAllowConvEnable
    ? `&isAllowConvEnable=${isAllowConvEnable}`
    : "";
  const allowanceTypeChangedParam = ROUTE_PARAM.isAllowTypeChange
    ? `&isAllowTypeChange=${ROUTE_PARAM.isAllowTypeChange}`
    : "";
  return `events/${efConstants.ALLOWANCE_DASHBOARD_BASE_URL}?eventId=${
    ROUTE_PARAM.eventId
  }&group=${ROUTE_PARAM.offerAllowanceGroup}&isEdit=${
    ROUTE_PARAM.isEdit
  }${isAllowConvEnableParam}${
    ROUTE_PARAM.taskType === appConstants.TASK_TYPE_NEW_ITEM
      ? `&taskType=${ROUTE_PARAM.taskType}`
      : ""
  }${allowanceTypeChangedParam}${ ROUTE_PARAM.isNdpType ? `&isNdpType=${ROUTE_PARAM.isNdpType}` : ""}`;
};

export const buildURLforAllowanceDashboardSummary = ROUTE_PARAM => {
  const isIncludeNdp = ROUTE_PARAM.isNdpType ? `&isNdpType=${ROUTE_PARAM.isNdpType}` : "";
  return `events/${efConstants.ALLOWANCE_DASHBOARD_BASE_URL}?eventId=${ROUTE_PARAM.eventId}&offerAllowancesId=${ROUTE_PARAM.offerAllowancesId}&group=${ROUTE_PARAM.offerAllowanceGroup}&isSummary=${ROUTE_PARAM.isSummary}${isIncludeNdp}`;
};
