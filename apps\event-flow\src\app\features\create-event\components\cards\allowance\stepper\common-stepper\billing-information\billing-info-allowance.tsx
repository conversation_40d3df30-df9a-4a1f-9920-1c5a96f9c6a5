import { useState } from "react";
import { Tag } from "@albertsons/uds/molecule/Tag";
import { CornerDownRight } from "lucide-react";
import {
  InputSelectAtom,
  InputTextAreaAtom,
  InputTextAtom,
} from "../../../../../fields";
import efConstants from "../../../../../../../../shared/ef-constants/ef-constants";
import { getLocation } from "../allowance-dates/allowance-dates-service";
import { checkIsPaymentTypeDeduct } from "../../../../../../service/allowance/allowance-service";

export default function BillingInfoForAllowance({
  register,
  control,
  stepperElement,
  allowance,
  allowancesResp,
  index,
  onPaymentTypeChange,
  onApArNumberChange,
  onVendorCommentChange,
  isCommentRequired,
  getHighletedClassName,
  error,
  getValues,
  keyValue,
  allowanceType,
  isEditEnable,
  performance,
}) {
  const {
    fields: { suggestedVendorPaymentType, acApOrArNumber, vendorComment },
  } = stepperElement;

  const paymentType = getValues(
    getRegKeyForBilling(suggestedVendorPaymentType, index)
  );
  const suggestedVendorPaymentTypeProps = {
    ...suggestedVendorPaymentType,
    registerField: getRegKeyForBilling(suggestedVendorPaymentType, index),
  };

  const acApOrArNumberProps = {
    ...acApOrArNumber,
    registerField: getRegKeyForBilling(acApOrArNumber, index),
    // added dynamic mapperkey
    mapperKey:
      paymentType === "Deduct"
        ? "offerallowances.allowances.allowanceBillingInfo.suggestedAcPayableVendorNbr"
        : "offerallowances.allowances.allowanceBillingInfo.suggestedAcReceivableVendorNbr",
  };

  const vendorCommentProps = {
    ...vendorComment,
    registerField: getRegKeyForBilling(vendorComment, index),
  };

  const [changedFields, setChangedFields] = useState({
    suggestedVendorPaymentType: false,
    acApOrArNumber: false,
    vendorComment: false,
  });

  const isDefaultPaymentTypeDeduct = checkIsPaymentTypeDeduct(
    performance,
    allowance
  );

  return (
    <>
      <BillingInfoAllowanceNesting
        mainVendor={allowance}
        allowancesResp={allowancesResp}
        keyValue={keyValue}
        allowanceType={allowanceType}
      />
      <span id="abs-billing-info-allowance-default-info">Default Billing Information</span>
      <div className="flex gap-4" id="abs-billing-info-allowance-container">
        {displayData(
          "Payment Type",
          isDefaultPaymentTypeDeduct ? "Deduct" : "Invoice"
        )}
        {displayData(
          "A/P or A/R Number",
          isDefaultPaymentTypeDeduct
            ? allowance?.allowanceBillingInfo?.acPayableVendorNbr
            : allowance?.allowanceBillingInfo?.acReceivableVendorNbr
        )}
        {displayData(
          "Vendor Name",
          allowance?.allowanceBillingInfo?.billingName === undefined ? allowance?.allowanceBillingInfo?.absVendorName:allowance?.allowanceBillingInfo?.billingName
        )}
      </div>
      <hr />
      <div className="flex gap-4" id="abs-billing-info-allowance-input-select-atom-cont">
        <div id="abs-billing-info-allowance-input-select-atom-section">
          <InputSelectAtom
            fieldProps={suggestedVendorPaymentTypeProps}
            register={register}
            control={control}
            options={suggestedVendorPaymentTypeProps.options}
            displayLabel={suggestedVendorPaymentTypeProps.displayLabel}
            disabled={!suggestedVendorPaymentTypeProps.options.length}
            onChange={e => {
              setChangedFields({
                ...changedFields,
                suggestedVendorPaymentType: true,
              });
              onPaymentTypeChange(e, index);
            }}
            className={getHighletedClassName(
              suggestedVendorPaymentTypeProps,
              changedFields.suggestedVendorPaymentType
            )}
            error={{
              message: error?.suggestedVendorPaymentType,
            }}
          />
        </div>

        <div className="w-[250px]" id="abs-billing-info-allowance-input-text-atom-cont">
          <InputTextAtom
            control={control}
            register={register}
            fieldProps={acApOrArNumberProps}
            onChange={e => {
              setChangedFields({
                ...changedFields,
                acApOrArNumber: true,
              });
              onApArNumberChange(e, index);
            }}
            className={getHighletedClassName(
              acApOrArNumberProps,
              changedFields.acApOrArNumber
            )}
            error={{
              message: error?.acApOrArNumber,
            }}
            tooltip={""}
            onWheel={event => event?.currentTarget?.blur()}
          />
        </div>
      </div>
      <InputTextAreaAtom
        control={control}
        fieldProps={vendorCommentProps}
        onChange={e => {
          setChangedFields({
            ...changedFields,
            vendorComment: true,
          });
          onVendorCommentChange(e, index);
        }}
        register={register}
        className={getHighletedClassName(
          vendorCommentProps,
          changedFields.vendorComment
        )}
        error={{
          message: error?.vendorComment,
        }}
        addAsterisk={isCommentRequired}
      />
      <hr />
    </>
  );
}

export function getRegKeyForBilling(field, allowIndex) {
  return `allowanceBillingInfo.[${allowIndex}].${field.registerField}`;
}

export function BillingInfoAllowanceNesting({
  mainVendor,
  allowancesResp,
  keyValue,
  allowanceType,
}) {
  if (keyValue === "DSD_WHSE_RETAIL_DIVISION")
    return (
      <div className="w-full flex p-2 pl-4 border border-[#bfdffd] rounded-lg bg-[#ebf3fa] font-bold"
        id="abs-billing-info-allowance-one-allowance-section">
        One Allowance
      </div>
    );

  const isShowLeadBadge =
    [efConstants.LEAD_DIST_ONLY, efConstants.BILL_LEAD_DIST].includes(
      mainVendor?.leadDistributorMode
    ) && mainVendor?.leadDistributorInd;

  const vertical_line_height = mainVendor?.leadDistributorInfos?.length
    ? 16 + (mainVendor?.leadDistributorInfos?.length - 1) * 24 + 8
    : 0;

  return (
    <>
      <div className="w-full flex p-2 pl-4 border border-[#bfdffd] rounded-lg bg-[#ebf3fa] " id="abs-billing-info-allowance-lead-cont">
        {getVendorWhseDisplayName(mainVendor, keyValue, allowanceType)}
        {isShowLeadBadge && (
          <Tag
            className="font-bold px-3 ml-2"
            backgroundColor="#bcdffd"
            borderColor="#ebf3fa"
          >
            Lead
          </Tag>
        )}
      </div>

      <div className="relative" id="abs-billing-info-allowance-corner-down-right-cont">
        <div id="abs-billing-info-allowance-corner-down-right-section" className={`absolute z-50 left-[38px] top-[-16px] bg-[#6296c7] h-[${vertical_line_height}px] w-[1px]`}
        ></div>

        {mainVendor?.leadDistributorInfos?.map(vendor => {
          vendor = allowancesResp?.find(v => v.vendorNbr === vendor.vendorNbr);
          return (
            <div className="flex pl-8" id="abs-billing-info-allowance-corner-down-right-section2">
              <CornerDownRight
                className="pb-[8px]"
                strokeWidth={1}
                color="#6296c7"
              />
              <div id="abs-billing-info-allowance-get-vendor-whsedisplay-name-section">
                {getVendorWhseDisplayName(vendor, keyValue, allowanceType)}
              </div>
            </div>
          );
        })}
      </div>
    </>
  );
}

export const displayData = (
  name: string,
  value: string,
  showDividerAtEnd = false
) => {
  return (
    <div id="abs-billing-info-allowance-show-divider-atend-section"
      className={
        showDividerAtEnd
          ? "border-r-[1px] border-dashed border-[#c8daeb] pr-4"
          : ""
      }
    >
      <p className="text-base font-bold text-left text-[#2b303c]" id="abs-billing-info-allowance-name-section">{name}</p>
      <p className="text-base font-bold text-left text-[#033b69] mt-2" id="abs-billing-info-allowance-value-section">
        {value}
      </p>
    </div>
  );
};

export function getVendorWhseDisplayName(allowance, keyValue, allowanceType) {
  const { vendorName, vendorNbr, costAreaDesc, distCenter, locationName } =
    allowance || {};

  const createSpan = (boldText, text) => (
    <span>
      <span className="font-bold">{boldText}</span>
      {""} - {text}
    </span>
  );

  if (vendorName && vendorNbr) {
    return createSpan(vendorName, `${vendorNbr} - ${costAreaDesc}`);
  }

  const location =
    allowanceType === "CASE" && keyValue === "WAREHOUSE_DIST_CENTERS"
      ? `${getLocation(locationName)} Warehousing`
      : locationName;

  return createSpan(distCenter, location);
}
