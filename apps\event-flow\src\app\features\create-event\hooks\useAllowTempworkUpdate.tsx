import { useSelectorWrap } from "@me/data-rtk";
import { useDispatch } from "react-redux";
import {
  formatAmount,
  getAllowanceMapKey,
  getVehicleDatesMapData,
} from "../service/allowance/allowance-service";
import { usePostAllowanceTempWorkDataMutation } from "../service/apis/allowance-api";
import { allowanceTempWorkHandler } from "../service/slice/allowance-temp-work-slice";
import { getDateFormat } from "../components/cards/allowance/stepper/common-stepper/allowance-dates/allowance-dates-service";
import { getObjectKeys } from "../service/allowance/allowance-stepper-service";

const useAllowTempworkUpdate = () => {
  const dispatch = useDispatch();
  const { allowanceData: allowanceTempWorkInfo } =
    useSelectorWrap("allowance_temp_work")?.data || {};
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { isAllowConvEnable } = useSelectorWrap("allow_conv_status_rn").data;

  const [postAllowanceTempWork, { isSuccess, isError, error, isLoading }] =
    usePostAllowanceTempWorkDataMutation();

  const allowName =
    getAllowanceMapKey(allowanceTempWorkInfo?.allowanceType) || "";

  async function saveTempWorkData(allowanceTempData, isFormSubmitted = false) {
    try {
      let updatedAllowanceData = {
        ...allowanceTempData,
        URL_PARAM: eventDetailsData?.id,
        queryParams: {},
      };

      if (!isFormSubmitted) {
        if (allowanceTempData?.planEventId !== eventDetailsData?.id) {
          updatedAllowanceData = {
            ...updatedAllowanceData,
            planEventId: eventDetailsData?.id,
            tempWorkAllowanceId: null,
          };
        }
        const updatedTempData = await postAllowanceTempWork(
          updatedAllowanceData
        );
        !updatedTempData?.error &&
          dispatch(
            allowanceTempWorkHandler({
              allowanceData: updatedTempData?.data || {},
            })
          );
        return updatedTempData;
      } else if (isFormSubmitted) return updatedAllowanceData;
      return {};
    } catch (e) {
      console.error(e);
      return {};
    }
  }

  const getTempworkObject = primeData => {
    const {
      allowanceTypeKey,
      allowKey,
      allowancePerformanceId,
      allowancePerformances,
      offerAllowancesGroupInfoMap = {},
      createInd,
      allowanceToBeCreatedOption,
      amountRefreshCheck,
      overrideHeaderFlatAmt,
      productSources,
      vehicleDatesMap,
      isEditEnable,
    } = primeData;

    return {
      allowanceType: allowanceTypeKey,
      allowanceTypeSpecification: {
        [allowKey]: {
          allowanceType: allowanceTypeKey,
          allowancePerformanceId,
          allowancePerformances,
          offerAllowancesGroupInfoMap,
          createInd: allowanceToBeCreatedOption?.createIndex || createInd,
          allowancesMap: isEditEnable
            ? allowanceTempWorkInfo?.allowanceTypeSpecification?.[allowName]
                ?.allowancesMap
            : allowanceToBeCreatedOption?.allowanceMap || {},
          ...(amountRefreshCheck && {
            overrideHeaderFlatAmt: formatAmount(overrideHeaderFlatAmt),
          }),
          productSources,
          vehicleDatesMap,
        },
      },
    };
  };

  const getVehicleDatesMap = (
    values,
    offerMapKey: string,
    isSavePrevious = false
  ) => {
    const vehicle = values?.allowanceCreationVehicle?.vehicleRef;

    const dateRange = {
      startDate: getDateFormat(vehicle?.startDate),
      endDate: getDateFormat(vehicle?.endDate),
    };
    const vehicleObject = {
      dateRange,
      vehicleId: vehicle?.vehicleId,
      vehicleRef: vehicle,
    };

    return isAllowConvEnable
      ? allowanceTempWorkInfo?.allowanceTypeSpecification?.[allowName]
          ?.vehicleDatesMap
      : getVehicleDatesMapData(
          offerMapKey,
          vehicleObject,
          isSavePrevious
            ? allowanceTempWorkInfo?.allowanceTypeSpecification?.[allowName]
                ?.vehicleDatesMap
            : {}
        );
  };

  const getAllowanceMapData = (
    datesArray,
    vehicleDatesMap,
    isEditEnable = false,
    offerMapKey: string,
    isAmountsIncluded = false,
    isSavePrevious = false
  ) => {
    const defaultValuesForAllowance = {
      leadDistributorMode: null,
      leadDistributorInfos: [],
      leadDistributorInd: false,
      includeInd: true,
      createAllowInd: true,
    };

    const allowMapData = isSavePrevious
      ? allowanceTempWorkInfo?.allowanceTypeSpecification?.[allowName]
          ?.allowancesMap || {}
      : {};

    const allowanesOffsetDates =
      isEditEnable && allowMapData?.[offerMapKey]?.length // Edit All
        ? allowMapData?.[offerMapKey]?.map((values, index) => {
            return {
              ...values,
              ...datesArray?.[index],
              vehicleId: vehicleDatesMap?.[offerMapKey]?.vehicleId,
              finalizedAmountsInd: isAmountsIncluded,
              allowanceBillingInfo: values?.allowanceBillingInfo,
            };
          })
        : datesArray?.length
        ? datesArray.map(values => {
            // Case Scan and S2S Create
            return {
              ...values,
              vehicleId: vehicleDatesMap?.[offerMapKey]?.vehicleId,
              finalizedAmountsInd: isAmountsIncluded,
              ...defaultValuesForAllowance,
            };
          })
        : allowMapData?.[offerMapKey]?.map(values => {
            //  HF IF Create
            return {
              ...values,
              vehicleId: vehicleDatesMap?.[offerMapKey]?.vehicleId,
              finalizedAmountsInd: isAmountsIncluded,
              ...defaultValuesForAllowance,
            };
          });

    return {
      ...allowMapData,
      [offerMapKey]: allowanesOffsetDates || [],
    };
  };

  async function saveAllowancePrimeSectionData(tempData) {
    return await saveTempWorkData({
      ...tempData,
      tempWorkAllowanceId: allowanceTempWorkInfo?.tempWorkAllowanceId || null,
      planEventId: allowanceTempWorkInfo?.planEventId || null,
    });
  }

  async function saveAllowanceAmountSectionData({
    allowances,
    allowName,
    mapperKey,
  }) {
    const updatedTempWork = {
      ...allowanceTempWorkInfo,
      allowanceTypeSpecification: {
        [allowName]: {
          ...allowanceTempWorkInfo?.allowanceTypeSpecification?.[allowName],
          allowancesMap: {
            ...allowanceTempWorkInfo?.allowanceTypeSpecification?.[allowName]
              ?.allowancesMap,
            [mapperKey]: [...(allowances || [])],
          },
        },
      },
    };
    return await saveTempWorkData({
      ...updatedTempWork,
      tempWorkAllowanceId: allowanceTempWorkInfo?.tempWorkAllowanceId || null,
      planEventId: allowanceTempWorkInfo?.planEventId || null,
    });
  }
  function checkTempDefaultDataAvailablity() {
    if (
      allowanceTempWorkInfo === undefined ||
      !getObjectKeys(allowanceTempWorkInfo)?.length
    ) {
      return;
    }
  }

  const setAllowancesInfo = (
    allowances,
    offerAllowanceGroup,
    isDatesStep = false,
    vehicleDatesMap = null
  ) => {
    const { allowanceTypeSpecification: allowanceTypeSpecificationValue } =
      allowanceTempWorkInfo || {};

    const allowanceTypeSpecification = {
      ...allowanceTypeSpecificationValue,
    };

    const allowancesMap = {
      ...allowanceTypeSpecification?.[allowName]?.allowancesMap,
      [offerAllowanceGroup]: [...(allowances || [])],
    };

    if (isDatesStep) return allowancesMap;

    allowanceTypeSpecification[allowName] = {
      ...allowanceTypeSpecification?.[allowName],
      allowancesMap,
      ...((vehicleDatesMap && { vehicleDatesMap }) || {}),
    };
    return allowanceTypeSpecification;
  };

  async function addAllowancesBillingInformation({
    createAllowInd,
    allowances,
    offerAllowanceGroup,
    isLastStep,
  }) {
    checkTempDefaultDataAvailablity();

    const allowanceTypeSpecification = setAllowancesInfo(
      allowances,
      offerAllowanceGroup
    );

    return await saveTempWorkData(
      {
        ...allowanceTempWorkInfo,
        allowanceTypeSpecification,
      },
      isLastStep
    );
  }

  return {
    saveAllowancePrimeSectionData,
    getTempworkObject,
    getVehicleDatesMap,
    getAllowanceMapData,
    saveAllowanceAmountSectionData,
    addAllowancesBillingInformation,
    isSuccess,
    isError,
    error,
    isLoading,
  };
};

export default useAllowTempworkUpdate;
