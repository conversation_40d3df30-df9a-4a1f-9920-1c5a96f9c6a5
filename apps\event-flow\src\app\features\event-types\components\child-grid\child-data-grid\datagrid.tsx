import Table from "@albertsons/uds/molecule/Table";
import React, { memo, useEffect } from "react";
import { doRefetchChildEventAapi } from "../slices/child-events-data-slice";
import _ from "lodash";
import { useDispatch } from "react-redux";

function Datagrid({ cardIndex, gridData, gridConfig, columns, isFetching, key }) {
  const dispatch = useDispatch();

  useEffect(() => {
    const observer = new MutationObserver(() => {
      const childElement = document.querySelectorAll(
        ".bg-red.childEventsClass"
      );

      if (childElement?.length > 0) {
        childElement.forEach(element => {
          if (
            element?.parentElement &&
            !element?.parentElement?.classList?.contains("parent-child-events")
          ) {
            element?.parentElement?.classList?.add("parent-child-events");
          }
        });
      }
    });

    observer.observe(document.body, { childList: true, subtree: true });

    return () => observer.disconnect();
  }, [key]);

  const refetchChildEvents = () => {
    dispatch(
      doRefetchChildEventAapi({
        isRefetch: _.uniqueId("refetch"),
      })
    );
  };

  const noRecordMessage = (
    <p className="pl-5 pt-5">
      No records available Please{" "}
      <span
        className="underline decoration-[#1B6EBB] decoration-2 underline-offset-1 text-[#1B6EBB] cursor-pointer"
        onClick={refetchChildEvents}
      >
        Retry
      </span>
    </p>
  );

  return (
    <>
      <Table
        loading={isFetching}
        id={`${gridConfig?.id}-${cardIndex}`}
        itemKey={gridConfig?.itemKey}
        items={gridData}
        columns={columns}
        singleSort={true}
        dividers={true}
        noPagination={true}
        noHeader={true}
        className="bg-red childEventsClass"
        // checkboxes={gridConfig?.checkboxes}
        onChange={(e, a) => console.log(e, a)}
        selected={gridData?.filter(data => data.isSelected)}
      />
      {!gridData?.length && noRecordMessage}
    </>
  );
}

export default memo(Datagrid);
