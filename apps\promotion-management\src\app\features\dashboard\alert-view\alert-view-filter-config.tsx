import {
  DASHBOARD_SLICE_CONFIG,
  DASHBOARD_VIEW_KEY_MAPPER,
} from "apps/promotion-management/src/app/config/dashboard-config";
import { setAlertsFilterData } from "../../../library/data-access/dashboard";
import DashboardSearch from "../common/components/dashboard-search";
import { HeaderItem } from "../common/components/render-header-item";
import { PlanningPropsFromGlobalHeader } from "../planning-view/planning-view-filter-config";
import { nationalEventsFlagEnabled } from "@me-upp-js/utilities";

export const ALERT_SORT_TYPE_OPTIONS = [
  {
    name: "Event Start + Event ID",
    eventKey: "Event Start",
    isDefaultValue: true,
  },
  {
    name: "Vendor + Event Start",
    eventKey: "Vendor Start",
  },
];

export const ALERT_STATUS_TYPE_OPTIONS = [
  {
    name: "All",
    eventKey: [
      "Event Approved",
      "Dropped Item",
      "New Item",
      "Event Updated",
      "Event Canceled",
      "Event Published",
    ],
    isDefaultValue: true,
  },
  {
    name: "Event Approved",
    eventKey: ["Event Approved"],
  },
  {
    name: "Item Removed",
    eventKey: ["Dropped Item"],
  },
  {
    name: "New Item",
    eventKey: ["New Item"],
  },
  {
    name: "Event Updated",
    eventKey: ["Event Updated"],
  },
  {
    name: "Event Canceled",
    eventKey: ["Event Canceled"],
  },
  {
    name: "Event Published",
    eventKey: ["Event Published"],
  },
];

export const ALERT_SUB_HEADER_CONFIG: HeaderItem[] = [
  {
    key: "clear-selected",
    label: "Clear Selected",
    type: "button",
    extraConfig: {
      width: 244,
      variant: "secondary",
    },
  },
  {
    key: "divider-1",
    type: "divider",
  },
  {
    key: "search-box",
    type: "custom-component",
    customComponent: (
      <DashboardSearch
        action={setAlertsFilterData}
        sliceKey={DASHBOARD_SLICE_CONFIG.alertView.FILTER_SLICE_KEY}
        viewType={DASHBOARD_VIEW_KEY_MAPPER.ALERT_VIEW_KEY}
      />
    ),
  },
  {
    key: "taskAlertStatusType",
    type: "toggle",
    label: "Cleared",
  },
  {
    key: "divider-2",
    type: "divider",
  },
  {
    key: "tasksSortType",
    type: "select",
    options: ALERT_SORT_TYPE_OPTIONS,
    label: "Sort",
    extraConfig: {
      variant: "tertiary",
      width: 220,
    },
  },
  {
    key: "taskStatusType",
    type: "select",
    options: nationalEventsFlagEnabled()
      ? ALERT_STATUS_TYPE_OPTIONS
      : ALERT_STATUS_TYPE_OPTIONS?.filter(
          type => type.name !== "Event Published"
        ),
    label: "Filter by Type",
    extraConfig: {
      variant: "tertiary",
      width: 200,
    },
  },
];

export const ALERT_PROPS_FROM_GLOBAL_HEADER: PlanningPropsFromGlobalHeader = {
  required: ["divisionIds"],
  optional: [
    "asmNamesOrCategorGroupCodes",
    "smicCategoryCodes",
    "smicGroupCodes",
    "productSelectionType",
    "promotionTypes",
    "vehicleTypes",
    "allowanceTypes",
    "planEventSortType",
    "supplierCodes",
    "promoProductGroups",
    "banners",
    "eventTypes",
  ],
};

export const ALERT_EVENT_STATUS_TYPES = [
  "Draft",
  "Pending With Merchant",
  "Pending With Vendor",
  "Agreed",
  "Agreed-Pending",
  "Active",
  "Executed",
  "Rejected",
  "Canceled",
];
