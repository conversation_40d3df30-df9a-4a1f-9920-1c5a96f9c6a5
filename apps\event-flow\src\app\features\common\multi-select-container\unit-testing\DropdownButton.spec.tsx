import { render, screen, fireEvent } from "@testing-library/react";
import DropdownButton from "../DropdownButton"; // adjust path as necessary
import "@testing-library/jest-dom";

describe("DropdownButton", () => {
  const mockHandleClick = jest.fn();
  const configObj = { title: "item" };

  afterEach(() => {
    mockHandleClick.mockClear();
  });

  it("renders with the correct text when selectedItemsCount is 0", () => {
    render(
      <DropdownButton
        disabled={false}
        handleClick={mockHandleClick}
        configObj={configObj}
        selectedItemsCount={0}
      />
    );

    expect(screen.getByText("No items selected")).toBeInTheDocument();
  });

  it("renders with the correct text when selectedItemsCount is 1", () => {
    render(
      <DropdownButton
        disabled={false}
        handleClick={mockHandleClick}
        configObj={configObj}
        selectedItemsCount={1}
      />
    );

    expect(screen.getByText("1 item is selected")).toBeInTheDocument();
  });

  it("renders with the correct text when selectedItemsCount is greater than 1", () => {
    render(
      <DropdownButton
        disabled={false}
        handleClick={mockHandleClick}
        configObj={configObj}
        selectedItemsCount={5}
      />
    );

    expect(screen.getByText("5 items are selected")).toBeInTheDocument();
  });

  it("should call handleClick when clicked and not disabled", () => {
    render(
      <DropdownButton
        disabled={false}
        handleClick={mockHandleClick}
        configObj={configObj}
        selectedItemsCount={2}
      />
    );

    const button = screen.getByRole("button");
    fireEvent.click(button);

    expect(mockHandleClick).toHaveBeenCalledTimes(1);
  });

  it("should not call handleClick when clicked and disabled", () => {
    render(
      <DropdownButton
        disabled={true}
        handleClick={mockHandleClick}
        configObj={configObj}
        selectedItemsCount={2}
      />
    );

    const button = screen.getByRole("button");
    fireEvent.click(button);

    expect(mockHandleClick).toHaveBeenCalledTimes(0); // handleClick should not be called
  });

  it("should have pointer-events-none class when disabled", () => {
    render(
      <DropdownButton
        disabled={true}
        handleClick={mockHandleClick}
        configObj={configObj}
        selectedItemsCount={2}
      />
    );

    const button = screen.getByRole("button");
    expect(button).toHaveClass("pointer-events-none");
  });

  it("should not have pointer-events-none class when not disabled", () => {
    render(
      <DropdownButton
        disabled={false}
        handleClick={mockHandleClick}
        configObj={configObj}
        selectedItemsCount={2}
      />
    );

    const button = screen.getByRole("button");
    expect(button).not.toHaveClass("pointer-events-none");
  });
});
