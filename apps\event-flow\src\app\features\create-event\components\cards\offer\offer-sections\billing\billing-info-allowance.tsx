import { useState } from "react";
import { Tag } from "@albertsons/uds/molecule/Tag";
import { CornerDownRight } from "lucide-react";
import {
  checkIsPaymentTypeDeduct,
  isHfOrIfTypeValue,
} from "../../../../../service/allowance/allowance-service";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";
import { InputSelect, InputText } from "../../../../fields/allowance-atoms";
import { getLocation } from "../../../allowance/stepper/common-stepper/allowance-dates/allowance-dates-service";
import "./billing-info-allowance.scss";
import { getVendorDetailsForCombined } from "../../offer-service";

const {
  ALLOWANCE_TYPES,
  OFFER_ALLOWANCE_GROUP,
  BILL_LEAD_DIST,
  LEAD_DIST_ONLY,
} = efConstants;

export default function BillingInfoForAllowance({
  register,
  control,
  sectionKey,
  allowance,
  allowancesResp,
  index,
  onPaymentTypeChange,
  onApArNumberChange,
  onVendorCommentChange,
  getHighletedClassName,
  error,
  getValues,
  keyValue,
  allowanceType,
  performance,
}) {
  const {
    fields: { suggestedVendorPaymentType, acApOrArNumber, vendorComment },
  } = sectionKey;

  const paymentType = getValues(
    getRegKeyForBilling(suggestedVendorPaymentType, index)
  );
  const suggestedVendorPaymentTypeProps = {
    ...suggestedVendorPaymentType,
    registerField: getRegKeyForBilling(suggestedVendorPaymentType, index),
  };

  const acApOrArNumberProps = {
    ...acApOrArNumber,
    registerField: getRegKeyForBilling(acApOrArNumber, index),
    // added dynamic mapperkey
    mapperKey:
      paymentType === "Deduct"
        ? "offerallowances.allowances.allowanceBillingInfo.suggestedAcPayableVendorNbr"
        : "offerallowances.allowances.allowanceBillingInfo.suggestedAcReceivableVendorNbr",
  };

  const vendorCommentProps = {
    ...vendorComment,
    registerField: getRegKeyForBilling(vendorComment, index),
  };

  const [changedFields, setChangedFields] = useState({
    suggestedVendorPaymentType: false,
    acApOrArNumber: false,
    vendorComment: false,
  });

  const isDefaultPaymentTypeDeduct = checkIsPaymentTypeDeduct(
    performance,
    allowance
  );

  return (
    <BillingInfoAllowanceNesting
      mainVendor={allowance}
      allowancesResp={allowancesResp}
      keyValue={keyValue}
      allowanceType={allowanceType}
      isDefaultPaymentTypeDeduct={isDefaultPaymentTypeDeduct}
      control={control}
      register={register}
      index={index}
      onPaymentTypeChange={onPaymentTypeChange}
      onApArNumberChange={onApArNumberChange}
      onVendorCommentChange={onVendorCommentChange}
      getHighletedClassName={getHighletedClassName}
      error={error}
      changedFields={changedFields}
      setChangedFields={setChangedFields}
      suggestedVendorPaymentTypeProps={suggestedVendorPaymentTypeProps}
      acApOrArNumberProps={acApOrArNumberProps}
      vendorCommentProps={vendorCommentProps}
    />
  );
}

export function getRegKeyForBilling(field, allowIndex) {
  return `allowanceBillingInfo.[${allowIndex}].${field?.registerField}`;
}

export function BillingInfoAllowanceNesting({
  mainVendor,
  allowancesResp,
  keyValue,
  allowanceType,
  register,
  control,
  index,
  onPaymentTypeChange,
  onApArNumberChange,
  onVendorCommentChange,
  getHighletedClassName,
  error,
  changedFields,
  setChangedFields,
  isDefaultPaymentTypeDeduct,
  suggestedVendorPaymentTypeProps,
  acApOrArNumberProps,
  vendorCommentProps,
}) {
  if (keyValue === OFFER_ALLOWANCE_GROUP.SCAN.COMBINED)
    return (
      <>
        <div
          className="w-full flex flex-wrap p-2 pl-4 bg-gray-206 font-bold"
          id="abs-billing-info-allowance-one-allowance-section"
        >
          <div className="flex flex-wrap w-full gap-3 text-[15px]">
            <div
              className={`select-none font-bold ${
                !isHfOrIfTypeValue(allowanceType)
                  ? "w-[440px] min-w-[150px]"
                  : "w-auto min-w-[0px]"
              }`}
              data-testid="tag-id"
            >
              &nbsp;
            </div>
            <div className="min-w-[80px] md:w-[100px] break-words">
              Payment
              <br /> Type
            </div>
            <div className="min-w-[100px] md:w-[100px] break-words">
              A/P or A/R Number
            </div>
            <div
              className={`min-w-[150px] break-words ${
                !isHfOrIfTypeValue(allowanceType)
                  ? "md:w-[200px]"
                  : "md:w-[400px]"
              }`}
            >
              Vendor <br /> Name
            </div>
            <div className="min-w-[120px] md:w-[130px] break-words">
              Suggested Payment Type
            </div>
            <div className="min-w-[140px] md:w-[150px] break-words">
              Suggested A/P or A/R Number
            </div>
          </div>
        </div>
        <div className="w-full pl-4">
          <div className="flex flex-wrap gap-3">
            <div
              className={`select-none font-bold ${
                !isHfOrIfTypeValue(allowanceType)
                  ? "w-[440px] min-w-[150px]"
                  : "w-auto min-w-[0px]"
              }`}
              data-testid="tag-id"
            >
              {allowancesResp?.[0] && allowanceType
                ? getVendorDetailsForCombined(
                    allowanceType,
                    keyValue,
                    allowancesResp?.[0] || {}
                  )
                : null}
            </div>
            <div className="min-w-[80px] md:w-[100px]">
              <p className="text-base font-bold text-left text-[#033b69]">
                {isDefaultPaymentTypeDeduct ? "Deduct" : "Invoice"}
              </p>
            </div>
            <div className="min-w-[100px] md:w-[100px]">
              <p className="text-base font-bold text-left text-[#033b69]">
                {isDefaultPaymentTypeDeduct
                  ? mainVendor?.allowanceBillingInfo?.acPayableVendorNbr
                  : mainVendor?.allowanceBillingInfo?.acReceivableVendorNbr}
              </p>
            </div>
            <div
              className={`min-w-[150px] break-words ${
                !isHfOrIfTypeValue(allowanceType)
                  ? "md:w-[200px]"
                  : "md:w-[400px]"
              }`}
            >
              <p className="text-base font-bold text-left text-[#033b69] truncate">
                {mainVendor?.allowanceBillingInfo?.billingName === undefined
                  ? mainVendor?.allowanceBillingInfo?.absVendorName
                  : mainVendor?.allowanceBillingInfo?.billingName}
              </p>
            </div>
            <div className="min-w-[120px] md:w-[130px]">
              <InputSelect
                fieldProps={suggestedVendorPaymentTypeProps}
                register={register}
                control={control}
                options={suggestedVendorPaymentTypeProps?.options}
                displayLabel={suggestedVendorPaymentTypeProps?.displayLabel}
                disabled={!suggestedVendorPaymentTypeProps?.options?.length}
                onChange={e => {
                  setChangedFields({
                    ...changedFields,
                    suggestedVendorPaymentType: true,
                  });
                  onPaymentTypeChange(e, index);
                }}
                className={getHighletedClassName(
                  suggestedVendorPaymentTypeProps,
                  changedFields?.suggestedVendorPaymentType
                )}
                error={{ message: error?.suggestedVendorPaymentType }}
              />
            </div>
            <div className="min-w-[140px] md:w-[150px] break-words">
              <InputText
                control={control}
                register={register}
                fieldProps={acApOrArNumberProps}
                onChange={e => {
                  setChangedFields({ ...changedFields, acApOrArNumber: true });
                  onApArNumberChange(e, index);
                }}
                className={getHighletedClassName(
                  acApOrArNumberProps,
                  changedFields?.acApOrArNumber
                )}
                error={{ message: error?.acApOrArNumber }}
                tooltip={""}
                onWheel={event => event?.currentTarget?.blur()}
              />
            </div>
          </div>

          <div className="flex flex-wrap gap-3 mt-4">
            <div
              className={`select-none font-bold ${
                !isHfOrIfTypeValue(allowanceType)
                  ? "w-[440px] min-w-[150px]"
                  : "w-auto min-w-[0px]"
              }`}
              data-testid="tag-id"
            >
              &nbsp;
            </div>
            <div className="flex-grow">
              <InputText
                control={control}
                fieldProps={vendorCommentProps}
                onChange={e => {
                  setChangedFields({ ...changedFields, vendorComment: true });
                  onVendorCommentChange(e, index);
                }}
                register={register}
                className={getHighletedClassName(
                  vendorCommentProps,
                  changedFields?.vendorComment
                )}
                placeHolder={vendorCommentProps.placeholder}
                error={{ message: error?.vendorComment }}
              />
            </div>
          </div>
        </div>
      </>
    );

  const isShowLeadBadge =
    [LEAD_DIST_ONLY, BILL_LEAD_DIST].includes(
      mainVendor?.leadDistributorMode
    ) && mainVendor?.leadDistributorInd;

  const vertical_line_height = mainVendor?.leadDistributorInfos?.length
    ? 16 + (mainVendor?.leadDistributorInfos?.length - 1) * 48 + 8
    : 0;

  return (
    <>
      <div
        className="w-full flex p-2 pl-4 bg-gray-206 items-center"
        id="abs-billing-info-allowance-lead-cont"
      >
        {!isShowLeadBadge ? (
          <div className="select-none font-bold w-[600px]" data-testid="tag-id">
            &nbsp;
          </div>
        ) : null}
        {isShowLeadBadge && (
          <>
            <div className="w-[500px]">
              {getVendorWhseDisplayName(mainVendor, keyValue, allowanceType)}
            </div>
            <Tag
              className="font-bold px-3 ml-2"
              backgroundColor="#bcdffd"
              borderColor="#ebf3fa"
              borderRadius={10}
            >
              Lead
            </Tag>
          </>
        )}

        <div
          className="w-full flex pl-4 bg-gray-206 font-bold"
          id="abs-billing-info-allowance-one-allowance-section"
        >
          <div className="flex w-full gap-3 text-[15px]">
            <div className="w-[100px] break-words">
              Payment <br /> Type
            </div>
            <div className="w-[100px] break-words">A/P or A/R Number</div>
            <div className="w-[200px] break-words">
              Vendor <br /> Name
            </div>
            <div className="w-[130px] break-words">Suggested Payment Type</div>
            <div className="w-[150px] break-words">
              Suggested A/P or A/R Number
            </div>
          </div>
        </div>
      </div>
      <div className="w-full">
        <div className="flex gap-[72px]">
          <div className="w-[400px]">
            <div
              className="relative"
              id="abs-billing-info-allowance-corner-down-right-cont"
            >
              <div
                id="abs-billing-info-allowance-corner-down-right-section"
                className={`absolute z-50 left-[38px] top-[-16px] bg-[#6296c7] w-[1px]`}
                style={{ height: `${vertical_line_height}px` }}
              ></div>
              {!isShowLeadBadge
                ? getVendorWhseDisplayName(mainVendor, keyValue, allowanceType)
                : mainVendor?.leadDistributorInfos?.map(vendor => {
                    vendor = allowancesResp?.find(
                      v => v.vendorNbr === vendor.vendorNbr
                    );
                    return (
                      <div
                        className="flex !pl-[32px]"
                        id="abs-billing-info-allowance-corner-down-right-section2"
                        key={vendor.vendorNbr}
                      >
                        <CornerDownRight
                          className="pb-[8px]"
                          strokeWidth={1}
                          color="#6296c7"
                        />
                        <div id="abs-billing-info-allowance-get-vendor-whsedisplay-name-section">
                          <div className="truncate-vendornumber pl-[5px]">
                            {getVendorWhseDisplayName(
                              vendor,
                              keyValue,
                              allowanceType
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
            </div>
          </div>
          <div className="flex-1">
            <div className="flex gap-3">
              <div className="w-[100px]">
                <p className="text-base font-bold text-left text-[#033b69]">
                  {isDefaultPaymentTypeDeduct ? "Deduct" : "Invoice"}
                </p>
              </div>
              <div className="w-[100px]">
                <p className="text-base font-bold text-left text-[#033b69]">
                  {isDefaultPaymentTypeDeduct
                    ? mainVendor?.allowanceBillingInfo?.acPayableVendorNbr
                    : mainVendor?.allowanceBillingInfo?.acReceivableVendorNbr}
                </p>
              </div>
              <div className="w-[200px]">
                <p className="text-base font-bold text-left text-[#033b69] overflow-auto overflow-ellipsis">
                  {mainVendor?.allowanceBillingInfo?.billingName === undefined
                    ? mainVendor?.allowanceBillingInfo?.absVendorName
                    : mainVendor?.allowanceBillingInfo?.billingName}
                </p>
              </div>
              <div className="w-[130px]">
                <InputSelect
                  fieldProps={suggestedVendorPaymentTypeProps}
                  register={register}
                  control={control}
                  options={suggestedVendorPaymentTypeProps?.options}
                  displayLabel={suggestedVendorPaymentTypeProps?.displayLabel}
                  disabled={!suggestedVendorPaymentTypeProps?.options?.length}
                  onChange={e => {
                    setChangedFields({
                      ...changedFields,
                      suggestedVendorPaymentType: true,
                    });
                    onPaymentTypeChange(e, index);
                  }}
                  className={getHighletedClassName(
                    suggestedVendorPaymentTypeProps,
                    changedFields?.suggestedVendorPaymentType
                  )}
                  error={{ message: error?.suggestedVendorPaymentType }}
                />
              </div>
              <div className="w-[150px]">
                <InputText
                  control={control}
                  register={register}
                  fieldProps={acApOrArNumberProps}
                  onChange={e => {
                    setChangedFields({
                      ...changedFields,
                      acApOrArNumber: true,
                    });
                    onApArNumberChange(e, index);
                  }}
                  className={getHighletedClassName(
                    acApOrArNumberProps,
                    changedFields?.acApOrArNumber
                  )}
                  error={{ message: error?.acApOrArNumber }}
                  tooltip={""}
                  onWheel={event => event?.currentTarget?.blur()}
                />
              </div>
            </div>
            <div className="mt-4 w-full">
              <InputText
                control={control}
                fieldProps={vendorCommentProps}
                onChange={e => {
                  setChangedFields({ ...changedFields, vendorComment: true });
                  onVendorCommentChange(e, index);
                }}
                placeHolder={vendorCommentProps.placeholder}
                register={register}
                className={`w-full ${getHighletedClassName(
                  vendorCommentProps,
                  changedFields?.vendorComment
                )}`}
                error={{ message: error?.vendorComment }}
              />
            </div>
          </div>
        </div>
        <div
          className="flex-grow-0 flex-shrink-0 w-full h-px mt-2  bg-[#fdfdff] border-t border-r border-b-0 border-l border-[#c8daeb]"
          id="abs-basic-allowance-amount-render-middle-section-one"
        ></div>
      </div>
    </>
  );
}

export const displayData = (
  name: string,
  value: any,
  showDividerAtEnd = false
) => {
  return (
    <div
      id="abs-billing-info-allowance-show-divider-atend-section"
      className={
        showDividerAtEnd
          ? "border-r-[1px] border-dashed border-[#c8daeb] pr-4"
          : ""
      }
    >
      <p
        className="text-base font-bold text-left text-[#2b303c]"
        id="abs-billing-info-allowance-name-section"
      >
        {name}
      </p>
      <p
        className="text-base font-bold text-left text-[#033b69] mt-2"
        id="abs-billing-info-allowance-value-section"
      >
        {value}
      </p>
    </div>
  );
};

const createDiv = (boldText, text) => (
  <span className="">
    <span className="font-bold">{boldText}</span>
    {""} - {text}
  </span>
);

export function getVendorWhseDisplayName(allowance, keyValue, allowanceType) {
  const { vendorName, vendorNbr, costAreaDesc, distCenter, locationName } =
    allowance || {};

  const isCaseWhse =
    allowanceType?.toUpperCase() === ALLOWANCE_TYPES.CASE.key &&
    keyValue === OFFER_ALLOWANCE_GROUP.CASE.WAREHOUSE;

  const createSpan = (boldText, text) => (
    <div className="">
      <span>
        <div className="font-bold">{boldText}</div>
        {""} - {text}
      </span>
    </div>
  );

  if (vendorName && vendorNbr) {
    return createSpan(vendorName, `${vendorNbr} - ${costAreaDesc}`);
  }
  const location = isCaseWhse
    ? `${getLocation(locationName)} Warehousing`
    : locationName;

  return isCaseWhse
    ? createDiv(distCenter, location)
    : createSpan(distCenter, location);
}
