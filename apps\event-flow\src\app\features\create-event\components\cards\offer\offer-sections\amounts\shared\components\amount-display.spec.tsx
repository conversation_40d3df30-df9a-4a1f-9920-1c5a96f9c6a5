import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';;
import { formatAmount } from 'apps/event-flow/src/app/features/create-event/service/allowance/allowance-service';
import AmountDisplay from './amount-display';

// Mock the formatAmount function
jest.mock('apps/event-flow/src/app/features/create-event/service/allowance/allowance-service', () => ({
  formatAmount: jest.fn(),
}));

describe('AmountDisplay Component', () => {
  const defaultProps = {
    label: 'Test Label',
    value: '1000',
    icon: <span data-testid="icon">Icon</span>,
    baseId: 'test-id',
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('renders label correctly', () => {
    render(<AmountDisplay {...defaultProps} />);
    expect(screen.getByText('Test Label')).toBeInTheDocument();
  });

  test('renders value directly when directDisplay is true', () => {
    render(<AmountDisplay {...defaultProps} directDisplay={true} />);
    expect(screen.getByText('1000')).toBeInTheDocument();
    expect(screen.queryByTestId('icon')).not.toBeInTheDocument();
  });

  test('formats and renders value with icon when directDisplay is false', () => {
    (formatAmount as jest.Mock).mockReturnValue('1,000.00');
    render(<AmountDisplay {...defaultProps} directDisplay={false} />);
    expect(screen.getByText('$1,000.00')).toBeInTheDocument();
    expect(screen.getByTestId('icon')).toBeInTheDocument();
    expect(formatAmount).toHaveBeenCalledWith('1000');
  });
});
