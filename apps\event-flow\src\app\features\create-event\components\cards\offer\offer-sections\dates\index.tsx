import { memo, useEffect, useState } from "react";
import { useSelectorWrap } from "@me/data-rtk";
import { OFFER_ALLOWANCE, OFFER_FORM_FIELDS } from "../../offer-flow-config";
import WarehouseAllowanceDatesSection from "./warehouse-dates-section";
import AllowanceFormWrapper, {
  IFormControls,
} from "../../../common/allowance-form-wrapper";
import {
  getAllowanceFormRegisterKey,
  getAllowanceKey,
  getAllowanceMapKey,
} from "../../../../../service/allowance/allowance-service";
import useAllowTempworkUpdate from "../../../../../hooks/useAllowTempworkUpdate";
import {
  checkIsNationalEvent,
  getBatchPayloadByDivisions,
  getNationalVehicleForTemp,
  getOfferMapKey,
  sortDivisionIds,
} from "../../offer-service";
import { useDispatch } from "react-redux";
import {
  setAllowanceFormInfo,
  setOfferAmontsData,
} from "../../../../../service/slice/allowance-details-slice";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";
import {
  useLazyGetAllowancesItemsQuery,
  useLazyGetNationalAllowancesItemsQuery,
} from "../../../../../service/apis/allowance-api";
import useGetOfferSectionConfiguration from "../../hooks/useGetOfferSectionConfiguration";
import LoadingSpinner from "../../../../../constants/LoadingSpinner/LoadingSpinner";
import { getObjectKeys } from "../../../../../service/allowance/allowance-stepper-service";
import useNationalAllowTempUpdate from "../../../../../hooks/useNationalAllowTempUpdate";
import useFetchCombinedData from "../../national/hooks/useFetchCombinedData";
import { useHandleAmountsResponse } from "../../hooks/amounts/useHandleAmountsResponse";
interface IOfferDateSectionProps {
  sectionKey: string;
  cardIndex: number;
  cardItemIndex: number;
  isEditEnable: boolean;
}

const DatesSection = ({
  sectionKey,
  cardIndex = 0,
  cardItemIndex = 0,
  isEditEnable = false,
}: IOfferDateSectionProps) => {
  const dispatch = useDispatch();

  const configuration = OFFER_ALLOWANCE?.[sectionKey] || {};
  const { create, edit } = configuration;
  const { ALLOWANCE_TYPES, OFFER_ALLOWANCE_GROUP } = efConstants;
  const { CASE } = ALLOWANCE_TYPES;
  const { additionalDatesChangeKey } = OFFER_FORM_FIELDS;

  const { productSources = [] } =
    useSelectorWrap("allowance_product_sources_rn")?.data || {};
  const { allowanceFormData: allowanceForm } =
    useSelectorWrap("allowance_form_data")?.data || {};
  const { allowanceData: tempworkData } =
    useSelectorWrap("allowance_temp_work")?.data || {};
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { offerAmounts = {}, isAdditionalDatesChanged = {} } =
    useSelectorWrap("offer_amounts_details")?.data || {};
  const offerSectionsEnableConfig =
    useSelectorWrap("offer_sections_enable_config")?.data || {};
  const { offerDivisions = [] } =
    useSelectorWrap("national_offer_divisions")?.data || {};

  const allowanceRegField = getAllowanceFormRegisterKey(
    cardIndex,
    cardItemIndex
  );
  // get data from form
  const allowanceFormData = allowanceForm?.[allowanceRegField];
  const { allowanceType = "", createInd } =
    allowanceFormData?.allowancePrimeData || {};
  const allowanceTypeKey = getAllowanceKey(allowanceType?.toUpperCase());
  const allowName = getAllowanceMapKey(allowanceTypeKey) || "";
  const offerMapKey = getOfferMapKey(createInd, configuration?.offerTypeKey);
  const { moveToNextSectionOnCreate, moveToNextSectionOnUpdate } =
    useGetOfferSectionConfiguration({
      allowanceRegField,
    });
  const { handleTempDeleteForInvalidItems, isTempDelInprogress } =
    useHandleAmountsResponse();
  const {
    id: eventId = "",
    eventType = "",
    nationalInfos = {},
  } = eventDetailsData || {};
  const isSectionFocus = offerSectionsEnableConfig?.[sectionKey]?.scrollTo;
  const isNationalEvent = checkIsNationalEvent(eventType);
  const nationalVehicles = nationalInfos?.nationalDivs || [];

  const allowanceAmountsPayload = {
    URL_PARAMS: [eventId, offerMapKey],
  };
  const allowanceAmountQueryParams = getBatchPayloadByDivisions(
    {
      URL_PARAMS: [eventId, offerMapKey],
    },
    offerDivisions,
    { skipOverlaps: false }
  );

  const [fetchAmountsData, rest] = useLazyGetAllowancesItemsQuery();
  const [fetchNationalAmountsData] = useLazyGetNationalAllowancesItemsQuery();

  // API call to save data in temp
  const {
    saveAllowancePrimeSectionData,
    getVehicleDatesMap,
    getAllowanceMapData,
    isLoading: isTempLoading,
  } = useAllowTempworkUpdate();

  const {
    getNationalAllowanceMapData,
    saveNationalAllowancePrimeSectionData,
    isLoading: isNationalTempLoading,
  } = useNationalAllowTempUpdate();

  const {
    state: {
      loading: isAmountsDataLoading = false,
      error: isAmountsFetchError = null,
    },
    fetchCombinedData,
  } = useFetchCombinedData();

  const [formControls, setFormControls] = useState<IFormControls>();
  const { getValues = () => null, control, reset } = formControls || {};
  const formData = getValues() || {};

  const [values, setValues] = useState<any>();
  const [isSaveBtnVisable, setIsSaveBtnVisable] = useState(isSectionFocus);

  useEffect(() => {
    const checkIsUpdateBtnVisable = () => {
      const isVisable = formData?.[additionalDatesChangeKey];
      isVisable && !isSaveBtnVisable && setIsSaveBtnVisable(true);
    };
    checkIsUpdateBtnVisable();
  }, [formData?.[additionalDatesChangeKey]]);

  const getFormControls = (controls: IFormControls) => {
    setFormControls(controls);
  };

  const handleNextSectionRedirection = (offerMapKey, updatedFormValues) => {
    !isEditEnable
      ? moveToNextSectionOnCreate(sectionKey, offerMapKey, updatedFormValues)
      : moveToNextSectionOnUpdate(sectionKey);
    reset?.(undefined, { keepValues: true });
  };

  const handleOrderDatesChange = async (
    tempwork,
    amountsData,
    vehicleDatesMap,
    updatedFormValues
  ) => {
    let result: any = null;
    if (isNationalEvent) {
      const nationalPayload = tempwork?.map((tempData, index: number) => {
        return {
          ...tempData,
          allowanceType: allowanceTypeKey,
          allowanceTypeSpecification: {
            [allowName]: {
              ...tempData?.allowanceTypeSpecification?.[allowName],
              allowancesMap: {
                [offerMapKey]: [...(amountsData?.[index]?.allowances || [])],
              },
            },
          },
        };
      });
      result = await saveNationalAllowancePrimeSectionData(nationalPayload);
    } else {
      result = await saveAllowancePrimeSectionData({
        ...tempwork,
        allowanceType: allowanceTypeKey,
        allowanceTypeSpecification: {
          [allowName]: {
            ...tempwork?.allowanceTypeSpecification?.[allowName],
            allowancesMap: {
              [offerMapKey]: [...(amountsData?.allowances || [])],
            },
            ...((vehicleDatesMap && { vehicleDatesMap }) || {}),
          },
        },
      });
    }

    const isValidResposne = isNationalEvent
      ? result?.data?.[0]?.allowanceType
      : result?.data?.allowanceType;
    if (isValidResposne) {
      handleNextSectionRedirection(offerMapKey, updatedFormValues);
    }
  };

  const getNationalPrimeSectionTempData = additionalDates => {
    const nationalVehicleMapData = getNationalVehicleForTemp(
      offerDivisions,
      nationalVehicles,
      offerMapKey
    );
    const vehicleKeys = getObjectKeys(nationalVehicleMapData)?.sort(
      sortDivisionIds
    );
    try {
      // // Initial TEMPWORK construction when there is no temp data or dependent fields are changed related to date.
      const updatedTempwork = vehicleKeys?.map((divId: string) => {
        const tempAllowancesData: any = tempworkData?.find(
          tempWork => tempWork?.divisionId === divId
        );
        const allowancesMap = getNationalAllowanceMapData(
          additionalDates?.find(
            additionalDates => additionalDates?.[0]?.divisionId === divId
          ) || [],
          nationalVehicleMapData?.[divId],
          isEditEnable,
          offerMapKey,
          false,
          true,
          tempAllowancesData?.allowanceTypeSpecification?.[allowName]
            ?.allowancesMap
        );

        return {
          ...tempAllowancesData,
          allowanceType: allowanceTypeKey,
          allowanceTypeSpecification: {
            [allowName]: {
              ...tempAllowancesData?.allowanceTypeSpecification?.[allowName],
              allowancesMap,
            },
          },
        };
      });
      return updatedTempwork;
    } catch (e) {
      console.log(e);
    }
    return {};
  };

  const handleSave = async (values: any) => {
    setIsSaveBtnVisable(false);

    const vehicleDatesMap = getVehicleDatesMap(values, offerMapKey, true);

    const allowancesMap = getAllowanceMapData(
      values?.additionalDates || [],
      vehicleDatesMap,
      isEditEnable,
      offerMapKey,
      false,
      true
    );

    let payload: any = null;
    if (!isNationalEvent) {
      payload = {
        ...tempworkData,
        allowanceType: allowanceTypeKey,
        allowanceTypeSpecification: {
          [allowName]: {
            ...tempworkData?.allowanceTypeSpecification?.[allowName],
            allowancesMap,
          },
        },
      };
    } else {
      payload = getNationalPrimeSectionTempData(values?.additionalDates);
    }

    const postQuery = isNationalEvent
      ? saveNationalAllowancePrimeSectionData
      : saveAllowancePrimeSectionData;

    const result = await postQuery(payload);
    const isValidResposne = isNationalEvent
      ? result?.data?.[0]?.allowanceType
      : result?.data?.allowanceType;

    if (isValidResposne) {
      const {
        additionalDates,
        allowanceCreationVehicle,
        isDatesValid,
        ...rest
      } = values || {};
      const updatedFormValues = {
        [allowanceRegField]: {
          ...allowanceFormData,
          allowanceCreationVehicle: {
            ...vehicleDatesMap,
          },
          additionalDates: {
            ...allowanceFormData?.additionalDates,
            [offerMapKey]: values?.additionalDates || [],
          },
        },
      };

      isEditEnable &&
        dispatch(
          setAllowanceFormInfo({
            allowanceFormData: updatedFormValues,
          })
        );

      if (
        values?.isOrderDatesUpdated &&
        allowanceType === CASE.key &&
        [OFFER_ALLOWANCE_GROUP.CASE.WAREHOUSE].includes(offerMapKey)
      ) {
        let amountsResult = isNationalEvent
          ? await fetchCombinedData(
              fetchNationalAmountsData,
              allowanceAmountQueryParams
            )
          : await fetchAmountsData(allowanceAmountsPayload);

        if (!amountsResult?.error) {
          const { amountsResp: updatedAmountsResp = null, tempList = [] } =
            isNationalEvent
              ? await handleTempDeleteForInvalidItems(
                  amountsResult?.data || [],
                  result?.data
                )
              : {};

          amountsResult = isNationalEvent
            ? updatedAmountsResp
            : amountsResult?.data;

          dispatch(
            setOfferAmontsData({
              offerAmounts: {
                ...offerAmounts,
                [offerMapKey]: updatedAmountsResp,
              },
              isAdditionalDatesChanged: {
                ...isAdditionalDatesChanged,
                [offerMapKey]: 0,
              },
            })
          );
          handleOrderDatesChange(
            isNationalEvent && tempList?.length ? tempList : result?.data,
            amountsResult || [],
            vehicleDatesMap,
            updatedFormValues
          );
        }
        return;
      }
      if (values?.isAdditionalDatesChanged) {
        // Additional dates are changed
        dispatch(
          setOfferAmontsData({
            isAdditionalDatesChanged: {
              ...isAdditionalDatesChanged,
              [offerMapKey]: (isAdditionalDatesChanged?.[offerMapKey] || 0) + 1,
            },
          })
        );
      }
      handleNextSectionRedirection(offerMapKey, updatedFormValues);
    }
  };

  return (
    <>
      <LoadingSpinner
        classname="!h-full !w-full rounded-md"
        isLoading={
          isTempLoading ||
          rest?.isLoading ||
          isNationalTempLoading ||
          isAmountsDataLoading ||
          isTempDelInprogress
        }
      />
      <AllowanceFormWrapper
        defaultValues={{}}
        handleSave={handleSave}
        getFormControls={getFormControls}
        footerProps={{
          label: isEditEnable ? edit?.label : create?.label,
          visable: isSaveBtnVisable,
          disabled:
            !productSources?.length ||
            !offerDivisions?.length ||
            !!isAmountsFetchError ||
            (isNationalEvent && !offerAmounts?.[offerMapKey]?.length),
        }}
        setValues={setValues}
        variant="secondary"
        watchFields={[additionalDatesChangeKey]}
      >
        {control ? (
          <WarehouseAllowanceDatesSection
            cardIndex={cardIndex}
            cardItemIndex={cardItemIndex}
            formControls={formControls}
            sectionConfiguration={configuration}
            isEditEnable={isEditEnable}
            allowanceRegField={allowanceRegField}
            sectionKey={sectionKey}
          />
        ) : null}
      </AllowanceFormWrapper>
    </>
  );
};

export default memo(DatesSection);
