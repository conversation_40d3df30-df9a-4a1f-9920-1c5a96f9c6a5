import Radio from "@albertsons/uds/molecule/Radio";
import Divider from "@albertsons/uds/molecule/Divider";
import { useDispatch } from "react-redux";
import { useEffect, useMemo, useState } from "react";
import LeadDistributorsModal from "../allowance-lead-distributors/lead-distributors-modal";
import {
  leadDistributors<PERSON><PERSON><PERSON>,
  leadDistributorsModeHandler,
  resetDivisionWiseBillingSectionData,
  resetDivisionWiseLeadDistData,
  setIsLeadDistributorError,
} from "../../create-event/service/slice/lead-distributors-slice";
import { useSelectorWrap } from "@me/data-rtk";
import efConstants from "../../../shared/ef-constants/ef-constants";
import { setIsUpdateAllowanceDistributorSelection } from "../../create-event/service/slice/allowances-dashboard-slices";
import BillingSelectionModal from "../allowance-lead-distributors/billing-selection-modal";
import {
  isNationalType,
  tableDataToVendorsArray,
  validateToShowExternalVendorOption,
} from "../allowance-lead-distributors/billing-selection-utils";
import NationalLeadDistributorsModal from "../nationals/components/lead-distributor/national-lead-distributor-modal";
import { isEmpty } from "lodash";
import NationalBillingSelectionModal from "../nationals/components/billing-section/national-billing-selection-modal";

export default function AllowancesFiltersSelection({
  allowanceCreatedTypeConfig,
}) {
  //todo: need to check this component
  const {
    filterTabs = [],
    showLeadDistributorSection,
    leadDistConfig,
  } = allowanceCreatedTypeConfig || {};
  const { DISTRIBUTORS_DATA, ALL_DIST, LEAD_DIST_LABEL, BILL_DIST_LABEL } = efConstants;
  const [selectedTab, setSelectedTab] = useState(0); // remove once uds Updated
  const [openLeadDistributorModel, setOpenLeadDistributorModel] =
    useState(false);
  const [openBillingSelectionModal, setOpenBillingSelectionModal] =
    useState(false);
  const {
    data: { tableData, allDivisionsTableData = [] },
  } = useSelectorWrap("allowanceTableData_rn");
  const { data: {isLeadDistributorError} } = useSelectorWrap("leadDistErrorslice_rn");
  const { isNdpType } =
    useSelectorWrap("national_divisions_config")?.data || {};
  const isNational = isNationalType(isNdpType);
  const showBillingSelectionOption = useMemo(() => {
    const vendors = tableDataToVendorsArray(tableData);
    return validateToShowExternalVendorOption(vendors, isNational ? allDivisionsTableData : []);
  }, [tableData]);

  const {
    data: { stepData, leadDistMode, allDivisionStepsData = {} },
  } = useSelectorWrap("leadDistributors_rn");

  const {
    data: { leadDistributorsMode },
  } = useSelectorWrap("leadDistributorsMode_rn");

  useEffect(() => {
    dispatch(
      setIsUpdateAllowanceDistributorSelection({ isAllowancefilterType: 0 })
    );
  }, []);

  useEffect(() => {
    leadDistributorsMode === LEAD_DIST_LABEL &&
      isLeadDistributorError &&
      setOpenLeadDistributorModel(true);
  },[isLeadDistributorError])

  useEffect(() => {
    isLeadDistributorError &&
      [ALL_DIST, BILL_DIST_LABEL].includes(leadDistributorsMode) &&
      dispatch(setIsLeadDistributorError({ isLeadDistributorError: false }));
  },[leadDistributorsMode])

  const dispatch = useDispatch();

  const onChangeAllowanceFilterTab = e => {
    setSelectedTab(e); // remove once uds Updated
    dispatch(
      setIsUpdateAllowanceDistributorSelection({ isAllowancefilterType: e })
    );
  };
  const onDistributorChangeHandler = element => {
    if (element === "LEAD") {
      setOpenLeadDistributorModel(true);
    } else if (element === "BILLING") {
      setOpenBillingSelectionModal(true);
    }

    dispatch(
      leadDistributorsModeHandler({
        leadDistributorsMode: element,
      })
    );
  if(element === "ALL") {
    dispatch(
      leadDistributorsHandler({
        stepData: [],
        leadDistMode: null,
        leadSelectionType: null,
        allDivisionStepsData: {},
      })
    );
    if(isNational) {
      dispatch(resetDivisionWiseLeadDistData());
      dispatch(resetDivisionWiseBillingSectionData());
    }
  }

    dispatch(
      setIsUpdateAllowanceDistributorSelection({
        isAllowancesDistributorType: element,
      })
    );
  };

  const loadDistributors = () => {
    return (
      <Radio.Group
        horizontal={true}
        variant="medium"
        value={leadDistributorsMode}
        onChange={onDistributorChangeHandler}
      >
        {DISTRIBUTORS_DATA.options
          .filter(item => {
            return !(item.hideIfSameVendor && !showBillingSelectionOption);
          })
          .map(item => {
            return (
              <Radio key={item.key} value={item.key} label={item.name}></Radio>
            );
          })}
      </Radio.Group>
    );
  };
  const LeadDistComponent = isNational
    ? NationalLeadDistributorsModal
    : LeadDistributorsModal;
  const BillingSelectionComponent = isNational
    ? NationalBillingSelectionModal
    : BillingSelectionModal;

  const loadFilteredTabs = () => {
    return (
      <div
        className={`flex justify-start items-start w-[1313px] pr-[373px] ${efConstants.componentClassName.ALLOWANCES_FILTER_SELECTION}`}
      >
        {filterTabs?.map((tabObj, index) => (
          <div
            className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative cursor-pointer"
            onClick={() => onChangeAllowanceFilterTab(tabObj?.value)}
            key={tabObj?.displayText}
          >
            <div className="flex flex-col justify-end items-center flex-grow-0 flex-shrink-0 h-12 relative gap-1.5">
              <p className="flex-grow-0 flex-shrink-0 text-base font-semibold text-center text-[#2b303c]">
                {tabObj?.displayText}
              </p>

              <div
                className={`self-stretch flex-grow-0 flex-shrink-0 h-1 ${
                  selectedTab === tabObj?.value ? "bg-[#3997ef]" : ""
                }`}
              />
            </div>
            {filterTabs?.length - 1 !== index ? (
              <div className="flex-grow-0 flex-shrink-0 w-px h-6 mx-4 bg-[#c8daeb]" />
            ) : null}
          </div>
        ))}
      </div>
    );
  };

  return (
    <>
      {showLeadDistributorSection ? (
        <>
          <div className="flex">
            {loadDistributors()}

            {(stepData?.length > 0 || leadDistMode || (isNational && !isEmpty(allDivisionStepsData))) && (
              <>
                <Divider className="mx-3" height={24} color="#C8DAEB" />
                <p className="text-[#5a697b] mx-3">
                  {leadDistMode && leadDistConfig
                    ? leadDistConfig[leadDistMode]?.displayLabel
                    : ""}
                </p>
                <p
                  className="text-[#1B6EBB] hover:cursor-pointer"
                  onClick={() => {
                    if (leadDistributorsMode === "LEAD")
                      setOpenLeadDistributorModel(true);
                    else if (leadDistributorsMode === "BILLING")
                      setOpenBillingSelectionModal(true);
                  }}
                >
                  {leadDistributorsMode === "LEAD" &&
                    "Change Lead Distributor(s)"}
                  {leadDistributorsMode === "BILLING" &&
                    "Change Billing Selection(s)"}
                </p>
              </>
            )}
          </div>

          <hr className="mt-[15px]" />
        </>
      ) : null}

      {loadFilteredTabs()}

      <hr />
      {openLeadDistributorModel ? (
        <LeadDistComponent
          isModelOpen={openLeadDistributorModel}
          setIsModelOpen={setOpenLeadDistributorModel}
        />
      ) : null}
      {openBillingSelectionModal ? (
        <BillingSelectionComponent
          isModelOpen={openBillingSelectionModal}
          setIsModelOpen={setOpenBillingSelectionModal}
        />
      ) : null}
    </>
  );
}
