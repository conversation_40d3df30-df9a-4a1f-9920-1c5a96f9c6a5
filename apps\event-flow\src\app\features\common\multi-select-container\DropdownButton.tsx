import React from "react";

const DropdownButton = ({
  disabled,
  handleClick,
  configObj,
  selectedItemsCount,
  error,
}) => {
  const { title } = configObj;

  const getButtonText = () => {
    if (selectedItemsCount === 0) return `No ${title}s selected`;
    if (selectedItemsCount === 1) return `1 ${title} selected`;
    return `${selectedItemsCount} ${title}s selected`;
  };

  return (
    <button
      className={`relative w-full px-3 min-h-[40px] bg-gray-205 rounded border border-solid border-disabled ${
        disabled ? "pointer-events-none" : ""
      } ${error ? "border-[red]" : ""}`}
      id="selection-dropdown-button"
      type="button"
      aria-haspopup={disabled ? "listbox" : "true"}
      aria-expanded={false}
      data-headlessui-state={disabled ? "disabled" : ""}
      disabled={disabled}
      onClick={handleClick}
    >
      <div className="flex items-center justify-between w-full">
        <div className="flex flex-wrap my-[6px] truncate">
          {getButtonText()}
        </div>
        <span className="flex items-center justify-end min-w-[24px]">
          <DropdownIcon />
        </span>
      </div>
    </button>
  );
};

const DropdownIcon = () => (
  <svg
    className="transition duration-200"
    width="12"
    height="8"
    viewBox="0 0 12 8"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.59 0.589844L6 5.16984L1.41 0.589844L0 1.99984L6 7.99984L12 1.99984L10.59 0.589844Z"
      fill="#2B303B"
    ></path>
  </svg>
);

export default React.memo(DropdownButton);
