import Button from "@albertsons/uds/molecule/Button";
import Modal from "@albertsons/uds/molecule/Modal";
import { useSelectorWrap } from "@me/data-rtk";

import { useDispatch } from "react-redux";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import { onCloseDistPopup } from "../../../allowance-lead-distributors/billing-selection-utils";
import {
  leadDistributorsHandler,
  resetDivisionWiseBillingSectionData,
  setInitialLeadDistData,
} from "../../../../create-event/service/slice/lead-distributors-slice";
import DivisionAccordion from "../division-accordion/division-accordion-wrapper";
import DistributorModeSelection from "../common/distributor-mode-selection";

type Props = {
  isModelOpen: boolean;
  setIsModelOpen: any;
};

export default function NationalBillingSelectionModal({
  isModelOpen = false,
  setIsModelOpen,
}: Props) {
  const {
      data: {
        stepData: selectedSteps,
        leadDistMode: defaultModeSelection,
        leadSelectionType,
        allDivisionStepsData,
      },
    } = useSelectorWrap("leadDistributors_rn") || {},
    { data: allowGroupConfigObj } =
      useSelectorWrap("allowanceTableColsGroupConfig_rn") || {};
  const { data: divisionWiseBillingData } = useSelectorWrap(
    "division_wise_billing_selection_data_rn"
  );
  const { data: nationalDivisionsConfig = {} } =
    useSelectorWrap("national_divisions_config") || {};
  const leadDistMode = efConstants.BILL_LEAD_DIST;
  const { selectedDivisionData = {}, divisionsList = [] } =
    nationalDivisionsConfig;
  const { billingInfoConfig = {} } = allowGroupConfigObj || {};
  const dispatch = useDispatch();

  const currentDivisionSteps =
    allDivisionStepsData?.[selectedDivisionData?.divisionId]?.stepData || [];

  const updatedInitialLeadDistData = () => {
    return leadDistMode && divisionWiseBillingData
      ? Object.keys(divisionWiseBillingData)?.reduce((acc, key) => {
          acc[key] = divisionWiseBillingData?.[key]?.stepData || [];
          return acc;
        }, {})
      : {};
  };

  const popperCloseArgs = {
    selectedSteps: currentDivisionSteps || selectedSteps,
    defaultModeSelection,
    dispatch,
    setIsModelOpen,
    leadSelectionType,
  };
  const saveButton = (
    <div className="flex items-center justify-center w-full my-4 abs-ef-billing-selection-modal-save-btn">
      <Button
        width={82}
        size="lg"
        className="mr-2 whitespace-nowrap"
        variant="secondary"
        onClick={() => {
          dispatch(resetDivisionWiseBillingSectionData());
          onCloseDistPopup(popperCloseArgs)
        }}
      >
        Cancel
      </Button>
      <Button
        width={92}
        size="lg"
        className="ml-2 whitespace-nowrap"
        onClick={() => {
          dispatch(
            leadDistributorsHandler({
              stepData: selectedSteps,
              leadDistMode,
              leadSelectionType: efConstants.BILL_DIST_LABEL,
              allDivisionStepsData: divisionWiseBillingData || {},
            })
          );
          dispatch(
            setInitialLeadDistData({
              leadDistData: leadDistMode ? updatedInitialLeadDistData() : [],
            })
          );
          dispatch(resetDivisionWiseBillingSectionData());
          setIsModelOpen(false);
        }}
      >
        Confirm
      </Button>
    </div>
  );

  return (
    <Modal
      isOpen={isModelOpen}
      onClose={() => onCloseDistPopup(popperCloseArgs)}
      height={720}
      className="overflow-hidden national-lead-distributor"
      width="65vw"
    >
      <div className="flex flex-col h-full">
        <div className="flex-shrink-0 mb-4">{leadDistStaticHtml}</div>

        <div className="flex-grow overflow-y-auto display-scroll">
          <DistributorModeSelection
            distMode={leadDistMode}
            distConfig={billingInfoConfig}
            isBillingSection={true}
          />
          {subLabelText}
          <DivisionAccordion isBilling={true} />
        </div>
        <div className="flex-shrink-0">{saveButton}</div>
      </div>
    </Modal>
  );
}

const leadDistStaticHtml = (
  <div className="text-center select-none font-bold text-[28px] mt-8 abs-ef-lead-dist-static-html">
    Added Vendor Billing Selections
  </div>
);

const subLabelText = (
  <div className="flex flex-col mx-[70px] my-[24px] text-center text-sm leading-4 text-[#5A697B] gap-2 abs-ef-sub-label-text">
    <p className="">
      Select the Distributor(s) that you wish to have the Additional Cost Areas
      Billed to
    </p>
  </div>
);
