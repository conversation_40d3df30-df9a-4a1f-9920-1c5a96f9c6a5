const dsdFundingMockData = {
  request: {
    offerId: 7016539,
    planEventId: "668234724ff07a6138b17cad",
    performStartDate: "2024-08-21",
    performEndDate: "2024-08-27",
  },
  dsdFundingHeader: {
    dsdFundingChildType: "OFFER",
    dsdFundingChildId: 7016539,
    planProductGroups: [
      {
        planProductGroupId: "636bde8d9665d0440e006f0e",
        sourceProductGroupId: 185457,
        productGroupType: "PPG",
        name: "Bonne Maman Preserves Jam (13) OUNCE - 85321",
        divisionId: "27",
        smicGroupCode: 4,
        smicCategoryCode: "0405",
        supplier: {
          supplierId: "1447",
          supplierName: "WORLD FINER FOODS",
        },
        itemCount: 7,
        displayInd: false,
        simsVendors: ["017935"],
        simsSubAccntVendors: ["017935-002"],
        negotiationSimsVendors: ["017935"],
        unitType: 1,
      },
      {
        planProductGroupId: "636bde929665d0440e0073a5",
        sourceProductGroupId: 185752,
        productGroupType: "PPG",
        name: "Annies Homegrown Snack Mix - 85016",
        divisionId: "27",
        smicGroupCode: 2,
        smicCategoryCode: "0210",
        supplier: {
          supplierId: "515",
          supplierName: "GENERAL MILLS",
        },
        itemCount: 2,
        displayInd: false,
        simsVendors: ["006446"],
        simsSubAccntVendors: ["**********", "**********"],
        negotiationSimsVendors: ["006446"],
        unitType: 1,
      },
      {
        planProductGroupId: "636bdea29665d0440e008d1d",
        sourceProductGroupId: 192873,
        productGroupType: "PPG",
        name: "Cbs Nuts Peanut Bttr Crunchy Org (16) OUNCE - 137263",
        divisionId: "27",
        smicGroupCode: 4,
        smicCategoryCode: "0410",
        supplier: {
          supplierId: "1576",
          supplierName: "CB'S NUTS",
        },
        itemCount: 1,
        displayInd: false,
        simsVendors: ["006446"],
        simsSubAccntVendors: ["**********", "**********"],
        negotiationSimsVendors: ["006446"],
        unitType: 1,
      },
      {
        planProductGroupId: "636bdea29665d0440e008d5b",
        sourceProductGroupId: 193066,
        productGroupType: "PPG",
        name: "Aunt Sue Honey Raw/Wild Natural (16) OUNCE - 137237",
        divisionId: "27",
        smicGroupCode: 4,
        smicCategoryCode: "0401",
        supplier: {
          supplierId: "1242",
          supplierName: "SIOUX HONEY",
        },
        itemCount: 1,
        displayInd: false,
        simsVendors: ["003053"],
        simsSubAccntVendors: ["003053-004", "003053-005", "003053-009"],
        negotiationSimsVendors: ["003053"],
        unitType: 1,
      },
      {
        planProductGroupId: "636be0a29665d0440e03817b",
        sourceProductGroupId: 615106,
        productGroupType: "PPG",
        name: "BISCOFF COOKIES 195560",
        divisionId: "27",
        smicGroupCode: 2,
        smicCategoryCode: "0201",
        supplier: {
          supplierId: "805",
          supplierName: "LOTUS BAKERIES NORTH AMERICA",
        },
        itemCount: 1,
        displayInd: false,
        simsVendors: ["019802"],
        simsSubAccntVendors: ["019802-002"],
        negotiationSimsVendors: ["019802"],
        unitType: 1,
      },
    ],
    storeGroups: [
      {
        storeGroupId: "6614614a5996dcc293a6b7bd",
        sourceStoreGroupId: "EDM",
        storeGroupName: "Seattle All Stores",
        storeGroupType: {
          groupType: "S",
          storeGrpTypeName: "Division",
          groupInd: "D",
        },
        divisionIds: ["27"],
        storeCount: 221,
        storeGrpNbr: 1713327680,
      },
    ],
    eventCreationVehicle: {
      vehicleId: "656644934710ba7ffc49aed7",
      vehicleNm: "27 Week 34 Insert 2024",
      sourceVehicleSk: 59234,
      startDate: "2024-08-21",
      endDate: "2024-08-27",
      vehicleType: {
        vehicleTypeId: "636abba1b426ee543a94d3ac",
        sourceVehicleTypeSk: 198,
        vehicleTypNm: "insrt",
        vehicleTypDesc: "Weekly Insert",
      },
    },
    childVehicle: {
      vehicleId: "656644934710ba7ffc49aed7",
      vehicleNm: "27 Week 34 Insert 2024",
      sourceVehicleSk: 59234,
      startDate: "2024-08-21",
      endDate: "2024-08-27",
      vehicleType: {
        vehicleTypeId: "636abba1b426ee543a94d3ac",
        sourceVehicleTypeSk: 198,
        vehicleTypNm: "insrt",
        vehicleTypDesc: "Weekly Insert",
      },
    },
  },
  dsdVendorFunding: {
    offerAllowanceOverlaps: [
      {
        offerNumber: 7016525,
        firstAllowanceLocationTypeTxt: "Store Group",
        vehicleRef: {
          vehicleId: "656644934710ba7ffc49aed7",
          vehicleNm: "27 Week 34 Insert 2024",
          sourceVehicleSk: 59234,
          startDate: "2024-08-21",
          endDate: "2024-08-27",
          vehicleType: {
            vehicleTypeId: "636abba1b426ee543a94d3ac",
            sourceVehicleTypeSk: 198,
            vehicleTypNm: "insrt",
            vehicleTypDesc: "Weekly Insert",
          },
        },
        allowanceLocationTypeTxts: ["Store Group"],
        storeGroupRefs: [
          {
            storeGroupId: "6614614a5996dcc293a6b7bd",
            sourceStoreGroupId: "EDM",
            storeGroupName: "Seattle All Stores",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Division",
              groupInd: "D",
            },
            divisionIds: ["27"],
            storeCount: 219,
            storeGrpNbr: 1713327680,
          },
        ],
        performance: {
          id: "63a3a12743a6cee87995b81d",
          performance: "Price / Ad / Display (88)",
          allowanceType: "Case",
          allowanceCd: "C",
          perfCode1: "48",
          perfCode2: "88",
          allwPerfSystemCode: "C-48-88",
          payType: "B",
          eventTypes: ["DP", "MD"],
          allowanceOverlaps: {
            minDisplayMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
            minNetCostInclude: {
              enableMatch: true,
              itemMatchPercent: 0.75,
              locationMatchPercent: 0.75,
              daysMatchPercent: 0.75,
            },
            performanceMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
          },
          performanceConfig: {
            defaultCreateInd: "CB",
            allowOnlyOverrideStoreGroupsInd: false,
            allowancePerformanceChildId: "63a3a12743a6cee87995b81b",
          },
          deleteInd: "",
          productSources: ["WAREHOUSE"],
        },
        allowanceType: "CASE",
        allowanceStatus: "Draft",
        allowanceAggrStartDate: "2024-03-10",
        allowanceAggrEndDate: "2024-08-27",
        packRetail: 8,
        vendorPackConversionFactor: 1,
        allowanceAmount: 2,
        minAllowanceAmount: 0,
        maxAllowanceAmount: 2,
        overlapMetrics: {
          name: "7016525",
          level: 1,
          itemsMatchPercent: 0,
          itemsMatched: 0,
          totalItems: 3,
          locationsMatchPercent: 0.991,
          locationsMatched: 219,
          totalLocations: 221,
          daysMatchPercent: 1,
          daysMatched: 7,
          totalDays: 7,
        },
        includedInNetCost: false,
        displayInd: false,
        performMatchInd: true,
        allowTypeMatchInd: true,
        cancelled: false,
        vendorNumber: "003053-004",
        vendorName: "SIOUX HONEY ASSOCIATION",
        performStartDate: "2024-08-21",
        performEndDate: "2024-08-27",
      },
      {
        offerNumber: 7016526,
        firstAllowanceLocationTypeTxt: "Store Group",
        vehicleRef: {
          vehicleId: "656644934710ba7ffc49aed7",
          vehicleNm: "27 Week 34 Insert 2024",
          sourceVehicleSk: 59234,
          startDate: "2024-08-21",
          endDate: "2024-08-27",
          vehicleType: {
            vehicleTypeId: "636abba1b426ee543a94d3ac",
            sourceVehicleTypeSk: 198,
            vehicleTypNm: "insrt",
            vehicleTypDesc: "Weekly Insert",
          },
        },
        allowanceLocationTypeTxts: ["Store Group"],
        storeGroupRefs: [
          {
            storeGroupId: "6614614a5996dcc293a6b7bd",
            sourceStoreGroupId: "EDM",
            storeGroupName: "Seattle All Stores",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Division",
              groupInd: "D",
            },
            divisionIds: ["27"],
            storeCount: 219,
            storeGrpNbr: 1713327680,
          },
        ],
        performance: {
          id: "63a3a12743a6cee87995b81b",
          performance: "DSD Off Invoice (01)",
          allowanceType: "Case",
          allowanceCd: "C",
          perfCode1: "51",
          perfCode2: "01",
          allwPerfSystemCode: "C-51-01",
          payType: "I",
          eventTypes: ["DP", "AO", "MD"],
          allowanceOverlaps: {
            minDisplayMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
            minNetCostInclude: {
              enableMatch: true,
              itemMatchPercent: 0.75,
              locationMatchPercent: 0.75,
              daysMatchPercent: 0.75,
            },
            performanceMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
          },
          performanceConfig: {
            defaultCreateInd: "CD",
            allowOnlyOverrideStoreGroupsInd: false,
          },
          deleteInd: "",
          productSources: ["DSD"],
        },
        allowanceType: "CASE",
        allowanceStatus: "Draft",
        allowanceAggrStartDate: "2024-08-19",
        allowanceAggrEndDate: "2024-08-27",
        packRetail: 12,
        vendorPackConversionFactor: 1,
        allowanceAmount: 2,
        minAllowanceAmount: 0,
        maxAllowanceAmount: 2,
        overlapMetrics: {
          name: "7016526",
          level: 1,
          itemsMatchPercent: 1,
          itemsMatched: 3,
          totalItems: 3,
          locationsMatchPercent: 0.991,
          locationsMatched: 219,
          totalLocations: 221,
          daysMatchPercent: 1,
          daysMatched: 7,
          totalDays: 7,
        },
        includedInNetCost: true,
        displayInd: true,
        performMatchInd: true,
        allowTypeMatchInd: true,
        cancelled: false,
        vendorNumber: "**********",
        vendorName: "KEHE DISTRIBUTORS",
        performStartDate: "2024-08-21",
        performEndDate: "2024-08-27",
      },
      {
        offerNumber: 7016539,
        firstAllowanceLocationTypeTxt: "Store Group",
        vehicleRef: {
          vehicleId: "656644934710ba7ffc49aed7",
          vehicleNm: "27 Week 34 Insert 2024",
          sourceVehicleSk: 59234,
          startDate: "2024-08-21",
          endDate: "2024-08-27",
          vehicleType: {
            vehicleTypeId: "636abba1b426ee543a94d3ac",
            sourceVehicleTypeSk: 198,
            vehicleTypNm: "insrt",
            vehicleTypDesc: "Weekly Insert",
          },
        },
        allowanceLocationTypeTxts: ["Store Group"],
        storeGroupRefs: [
          {
            storeGroupId: "6614614a5996dcc293a6b7bd",
            sourceStoreGroupId: "EDM",
            storeGroupName: "Seattle All Stores",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Division",
              groupInd: "D",
            },
            divisionIds: ["27"],
            storeCount: 219,
            storeGrpNbr: 1713327680,
          },
        ],
        performance: {
          id: "63a3a12743a6cee87995b81b",
          performance: "DSD Off Invoice (01)",
          allowanceType: "Case",
          allowanceCd: "C",
          perfCode1: "51",
          perfCode2: "01",
          allwPerfSystemCode: "C-51-01",
          payType: "I",
          eventTypes: ["DP", "AO", "MD"],
          allowanceOverlaps: {
            minDisplayMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
            minNetCostInclude: {
              enableMatch: true,
              itemMatchPercent: 0.75,
              locationMatchPercent: 0.75,
              daysMatchPercent: 0.75,
            },
            performanceMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
          },
          performanceConfig: {
            defaultCreateInd: "CD",
            allowOnlyOverrideStoreGroupsInd: false,
          },
          deleteInd: "",
          productSources: ["DSD"],
        },
        allowanceType: "CASE",
        allowanceStatus: "Draft",
        allowanceAggrStartDate: "2024-08-19",
        allowanceAggrEndDate: "2024-08-27",
        packRetail: 12,
        vendorPackConversionFactor: 1,
        allowanceAmount: 1,
        minAllowanceAmount: 0,
        maxAllowanceAmount: 1,
        overlapMetrics: {
          name: "7016539",
          level: 1,
          itemsMatchPercent: 1,
          itemsMatched: 3,
          totalItems: 3,
          locationsMatchPercent: 0.991,
          locationsMatched: 219,
          totalLocations: 221,
          daysMatchPercent: 1,
          daysMatched: 7,
          totalDays: 7,
        },
        includedInNetCost: true,
        displayInd: true,
        performMatchInd: true,
        allowTypeMatchInd: true,
        cancelled: false,
        vendorNumber: "**********",
        vendorName: "KEHE DISTRIBUTORS",
        performStartDate: "2024-08-21",
        performEndDate: "2024-08-27",
      },
      {
        offerNumber: 7016500,
        firstAllowanceLocationTypeTxt: "Store Group",
        vehicleRef: {
          vehicleId: "656644934710ba7ffc49aed7",
          vehicleNm: "27 Week 34 Insert 2024",
          sourceVehicleSk: 0,
          startDate: "2024-08-21",
          endDate: "2024-08-27",
          vehicleType: {
            vehicleTypeId: "636abba1b426ee543a94d3ac",
            sourceVehicleTypeSk: 0,
            vehicleTypDesc: "Weekly Insert",
          },
        },
        allowanceLocationTypeTxts: ["Store Group"],
        storeGroupRefs: [
          {
            storeGroupId: "6614614a5996dcc293a6b7bd",
            sourceStoreGroupId: "EDM",
            storeGroupName: "Seattle All Stores",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Division",
              groupInd: "D",
            },
            divisionIds: ["27"],
            storeCount: 219,
            storeGrpNbr: 1713327680,
          },
        ],
        performance: {
          id: "63a3a12743a6cee87995b82d",
          performance: "Price / Ad / Display (88)",
          allowanceType: "Item Flat",
          allowanceCd: "A",
          perfCode1: " ",
          perfCode2: "88",
          allwPerfSystemCode: "A- -88",
          payType: "B",
          eventTypes: ["DP", "MD"],
          allowanceOverlaps: {
            minDisplayMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
            minNetCostInclude: {
              enableMatch: false,
              itemMatchPercent: 0.75,
              locationMatchPercent: 0.75,
              daysMatchPercent: 0.75,
            },
            performanceMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
          },
          performanceConfig: {
            defaultCreateInd: "AW",
            allowOnlyOverrideStoreGroupsInd: false,
          },
          deleteInd: "",
          productSources: ["DSD", "WAREHOUSE"],
        },
        allowanceType: "ITEM_FLAT",
        allowanceStatus: "Pending With Vendor",
        allowanceAggrStartDate: "2024-08-21",
        allowanceAggrEndDate: "2024-08-27",
        packRetail: 12,
        vendorPackConversionFactor: 1,
        allowanceAmount: 4,
        minAllowanceAmount: 0,
        maxAllowanceAmount: 4,
        overlapMetrics: {
          name: "7016500",
          level: 1,
          itemsMatchPercent: 0,
          itemsMatched: 0,
          totalItems: 3,
          locationsMatchPercent: 0.991,
          locationsMatched: 219,
          totalLocations: 221,
          daysMatchPercent: 1,
          daysMatched: 7,
          totalDays: 7,
        },
        includedInNetCost: false,
        displayInd: false,
        performMatchInd: true,
        allowTypeMatchInd: false,
        cancelled: false,
        vendorNumber: "000756-013",
        vendorName: "KELLANOVA",
        performStartDate: "2024-08-21",
        performEndDate: "2024-08-27",
      },
      {
        offerNumber: 7016519,
        firstAllowanceLocationTypeTxt: "Store Group",
        vehicleRef: {
          vehicleId: "656644934710ba7ffc49aed7",
          vehicleNm: "27 Week 34 Insert 2024",
          sourceVehicleSk: 0,
          startDate: "2024-08-21",
          endDate: "2024-08-27",
          vehicleType: {
            vehicleTypeId: "636abba1b426ee543a94d3ac",
            sourceVehicleTypeSk: 0,
            vehicleTypDesc: "Weekly Insert",
          },
        },
        allowanceLocationTypeTxts: ["Store Group"],
        storeGroupRefs: [
          {
            storeGroupId: "6614614a5996dcc293a6b7bd",
            sourceStoreGroupId: "EDM",
            storeGroupName: "Seattle All Stores",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Division",
              groupInd: "D",
            },
            divisionIds: ["27"],
            storeCount: 219,
            storeGrpNbr: 1713327680,
          },
        ],
        performance: {
          id: "63a3a12743a6cee87995b82d",
          performance: "Price / Ad / Display (88)",
          allowanceType: "Item Flat",
          allowanceCd: "A",
          perfCode1: " ",
          perfCode2: "88",
          allwPerfSystemCode: "A- -88",
          payType: "B",
          eventTypes: ["DP", "MD"],
          allowanceOverlaps: {
            minDisplayMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
            minNetCostInclude: {
              enableMatch: false,
              itemMatchPercent: 0.75,
              locationMatchPercent: 0.75,
              daysMatchPercent: 0.75,
            },
            performanceMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
          },
          performanceConfig: {
            defaultCreateInd: "AW",
            allowOnlyOverrideStoreGroupsInd: false,
          },
          deleteInd: "",
          productSources: ["DSD", "WAREHOUSE"],
        },
        allowanceType: "ITEM_FLAT",
        allowanceStatus: "Pending With Vendor",
        allowanceAggrStartDate: "2024-08-21",
        allowanceAggrEndDate: "2024-08-27",
        packRetail: 6,
        vendorPackConversionFactor: 1,
        allowanceAmount: 5,
        minAllowanceAmount: 0,
        maxAllowanceAmount: 5,
        overlapMetrics: {
          name: "7016519",
          level: 1,
          itemsMatchPercent: 0,
          itemsMatched: 0,
          totalItems: 3,
          locationsMatchPercent: 0.991,
          locationsMatched: 219,
          totalLocations: 221,
          daysMatchPercent: 1,
          daysMatched: 7,
          totalDays: 7,
        },
        includedInNetCost: false,
        displayInd: false,
        performMatchInd: true,
        allowTypeMatchInd: false,
        cancelled: false,
        vendorNumber: "000756-013",
        vendorName: "KELLANOVA",
        performStartDate: "2024-08-21",
        performEndDate: "2024-08-27",
      },
      {
        offerNumber: 7016521,
        firstAllowanceLocationTypeTxt: "Store Group",
        vehicleRef: {
          vehicleId: "656644934710ba7ffc49aed7",
          vehicleNm: "27 Week 34 Insert 2024",
          sourceVehicleSk: 0,
          startDate: "2024-08-21",
          endDate: "2024-08-27",
          vehicleType: {
            vehicleTypeId: "636abba1b426ee543a94d3ac",
            sourceVehicleTypeSk: 0,
            vehicleTypDesc: "Weekly Insert",
          },
        },
        allowanceLocationTypeTxts: ["Store Group"],
        storeGroupRefs: [
          {
            storeGroupId: "6614614a5996dcc293a6b7bd",
            sourceStoreGroupId: "EDM",
            storeGroupName: "Seattle All Stores",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Division",
              groupInd: "D",
            },
            divisionIds: ["27"],
            storeCount: 219,
            storeGrpNbr: 1713327680,
          },
        ],
        performance: {
          id: "63a3a12743a6cee87995b82d",
          performance: "Price / Ad / Display (88)",
          allowanceType: "Item Flat",
          allowanceCd: "A",
          perfCode1: " ",
          perfCode2: "88",
          allwPerfSystemCode: "A- -88",
          payType: "B",
          eventTypes: ["DP", "MD"],
          allowanceOverlaps: {
            minDisplayMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
            minNetCostInclude: {
              enableMatch: false,
              itemMatchPercent: 0.75,
              locationMatchPercent: 0.75,
              daysMatchPercent: 0.75,
            },
            performanceMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
          },
          performanceConfig: {
            defaultCreateInd: "AW",
            allowOnlyOverrideStoreGroupsInd: false,
          },
          deleteInd: "",
          productSources: ["DSD", "WAREHOUSE"],
        },
        allowanceType: "ITEM_FLAT",
        allowanceStatus: "Pending With Vendor",
        allowanceAggrStartDate: "2024-08-21",
        allowanceAggrEndDate: "2024-08-27",
        packRetail: 6,
        vendorPackConversionFactor: 1,
        allowanceAmount: 7,
        minAllowanceAmount: 0,
        maxAllowanceAmount: 7,
        overlapMetrics: {
          name: "7016521",
          level: 1,
          itemsMatchPercent: 0,
          itemsMatched: 0,
          totalItems: 3,
          locationsMatchPercent: 0.991,
          locationsMatched: 219,
          totalLocations: 221,
          daysMatchPercent: 1,
          daysMatched: 7,
          totalDays: 7,
        },
        includedInNetCost: false,
        displayInd: false,
        performMatchInd: true,
        allowTypeMatchInd: false,
        cancelled: false,
        vendorNumber: "017935-002",
        vendorName: "WORLD FINER FOODS",
        performStartDate: "2024-08-21",
        performEndDate: "2024-08-27",
      },
      {
        offerNumber: 7016460,
        firstAllowanceLocationTypeTxt: "Store Group",
        vehicleRef: {
          vehicleId: "656644934710ba7ffc49aed7",
          vehicleNm: "27 Week 34 Insert 2024",
          sourceVehicleSk: 59234,
          startDate: "2024-08-21",
          endDate: "2024-08-27",
          vehicleType: {
            vehicleTypeId: "636abba1b426ee543a94d3ac",
            sourceVehicleTypeSk: 198,
            vehicleTypNm: "insrt",
            vehicleTypDesc: "Weekly Insert",
          },
        },
        allowanceLocationTypeTxts: ["Store Group"],
        storeGroupRefs: [
          {
            storeGroupId: "6614614a5996dcc293a6b7bd",
            sourceStoreGroupId: "EDM",
            storeGroupName: "Seattle All Stores",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Division",
              groupInd: "D",
            },
            divisionIds: ["27"],
            storeCount: 219,
            storeGrpNbr: 1713327680,
          },
        ],
        performance: {
          id: "63a3a12743a6cee87995b81b",
          performance: "DSD Off Invoice (01)",
          allowanceType: "Case",
          allowanceCd: "C",
          perfCode1: "51",
          perfCode2: "01",
          allwPerfSystemCode: "C-51-01",
          payType: "I",
          eventTypes: ["DP", "AO", "MD"],
          allowanceOverlaps: {
            minDisplayMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
            minNetCostInclude: {
              enableMatch: true,
              itemMatchPercent: 0.75,
              locationMatchPercent: 0.75,
              daysMatchPercent: 0.75,
            },
            performanceMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
          },
          performanceConfig: {
            defaultCreateInd: "CD",
            allowOnlyOverrideStoreGroupsInd: false,
          },
          deleteInd: "",
          productSources: ["DSD"],
        },
        allowanceType: "CASE",
        allowanceStatus: "Draft",
        allowanceAggrStartDate: "2024-08-19",
        allowanceAggrEndDate: "2024-08-27",
        packRetail: 12,
        vendorPackConversionFactor: 1,
        allowanceAmount: 1,
        minAllowanceAmount: 0,
        maxAllowanceAmount: 2,
        overlapMetrics: {
          name: "7016460",
          level: 1,
          itemsMatchPercent: 0.6667,
          itemsMatched: 2,
          totalItems: 3,
          locationsMatchPercent: 0.991,
          locationsMatched: 219,
          totalLocations: 221,
          daysMatchPercent: 1,
          daysMatched: 7,
          totalDays: 7,
        },
        includedInNetCost: false,
        displayInd: true,
        performMatchInd: true,
        allowTypeMatchInd: true,
        cancelled: false,
        vendorNumber: "**********",
        vendorName: "KEHE DISTRIBUTORS",
        performStartDate: "2024-08-21",
        performEndDate: "2024-08-27",
      },
      {
        offerNumber: 7016462,
        firstAllowanceLocationTypeTxt: "Store Group",
        vehicleRef: {
          vehicleId: "656644934710ba7ffc49aed7",
          vehicleNm: "27 Week 34 Insert 2024",
          sourceVehicleSk: 59234,
          startDate: "2024-08-21",
          endDate: "2024-08-27",
          vehicleType: {
            vehicleTypeId: "636abba1b426ee543a94d3ac",
            sourceVehicleTypeSk: 198,
            vehicleTypNm: "insrt",
            vehicleTypDesc: "Weekly Insert",
          },
        },
        allowanceLocationTypeTxts: ["Store Group"],
        storeGroupRefs: [
          {
            storeGroupId: "6614614a5996dcc293a6b7bd",
            sourceStoreGroupId: "EDM",
            storeGroupName: "Seattle All Stores",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Division",
              groupInd: "D",
            },
            divisionIds: ["27"],
            storeCount: 219,
            storeGrpNbr: 1713327680,
          },
        ],
        performance: {
          id: "63a3a12743a6cee87995b81b",
          performance: "DSD Off Invoice (01)",
          allowanceType: "Case",
          allowanceCd: "C",
          perfCode1: "51",
          perfCode2: "01",
          allwPerfSystemCode: "C-51-01",
          payType: "I",
          eventTypes: ["DP", "AO", "MD"],
          allowanceOverlaps: {
            minDisplayMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
            minNetCostInclude: {
              enableMatch: true,
              itemMatchPercent: 0.75,
              locationMatchPercent: 0.75,
              daysMatchPercent: 0.75,
            },
            performanceMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
          },
          performanceConfig: {
            defaultCreateInd: "CD",
            allowOnlyOverrideStoreGroupsInd: false,
          },
          deleteInd: "",
          productSources: ["DSD"],
        },
        allowanceType: "CASE",
        allowanceStatus: "Agreed",
        allowanceAggrStartDate: "2024-08-19",
        allowanceAggrEndDate: "2024-08-27",
        packRetail: 12,
        vendorPackConversionFactor: 1,
        allowanceAmount: 2,
        minAllowanceAmount: 0,
        maxAllowanceAmount: 2,
        overlapMetrics: {
          name: "7016462",
          level: 1,
          itemsMatchPercent: 0.6667,
          itemsMatched: 2,
          totalItems: 3,
          locationsMatchPercent: 0.991,
          locationsMatched: 219,
          totalLocations: 221,
          daysMatchPercent: 1,
          daysMatched: 7,
          totalDays: 7,
        },
        includedInNetCost: false,
        displayInd: true,
        performMatchInd: true,
        allowTypeMatchInd: true,
        cancelled: false,
        vendorNumber: "**********",
        vendorName: "KEHE DISTRIBUTORS",
        performStartDate: "2024-08-21",
        performEndDate: "2024-08-27",
      },
      {
        offerNumber: 7016463,
        firstAllowanceLocationTypeTxt: "Store Group",
        vehicleRef: {
          vehicleId: "656644934710ba7ffc49aed7",
          vehicleNm: "27 Week 34 Insert 2024",
          sourceVehicleSk: 59234,
          startDate: "2024-08-21",
          endDate: "2024-08-27",
          vehicleType: {
            vehicleTypeId: "636abba1b426ee543a94d3ac",
            sourceVehicleTypeSk: 198,
            vehicleTypNm: "insrt",
            vehicleTypDesc: "Weekly Insert",
          },
        },
        allowanceLocationTypeTxts: ["Store Group"],
        storeGroupRefs: [
          {
            storeGroupId: "6614614a5996dcc293a6b7bd",
            sourceStoreGroupId: "EDM",
            storeGroupName: "Seattle All Stores",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Division",
              groupInd: "D",
            },
            divisionIds: ["27"],
            storeCount: 219,
            storeGrpNbr: 1713327680,
          },
        ],
        performance: {
          id: "63a3a12743a6cee87995b81b",
          performance: "DSD Off Invoice (01)",
          allowanceType: "Case",
          allowanceCd: "C",
          perfCode1: "51",
          perfCode2: "01",
          allwPerfSystemCode: "C-51-01",
          payType: "I",
          eventTypes: ["DP", "AO", "MD"],
          allowanceOverlaps: {
            minDisplayMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
            minNetCostInclude: {
              enableMatch: true,
              itemMatchPercent: 0.75,
              locationMatchPercent: 0.75,
              daysMatchPercent: 0.75,
            },
            performanceMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
          },
          performanceConfig: {
            defaultCreateInd: "CD",
            allowOnlyOverrideStoreGroupsInd: false,
          },
          deleteInd: "",
          productSources: ["DSD"],
        },
        allowanceType: "CASE",
        allowanceStatus: "Agreed",
        allowanceAggrStartDate: "2024-08-19",
        allowanceAggrEndDate: "2024-08-27",
        packRetail: 8,
        vendorPackConversionFactor: 1,
        allowanceAmount: 2,
        minAllowanceAmount: 0,
        maxAllowanceAmount: 2,
        overlapMetrics: {
          name: "7016463",
          level: 1,
          itemsMatchPercent: 0.6667,
          itemsMatched: 2,
          totalItems: 3,
          locationsMatchPercent: 0.991,
          locationsMatched: 219,
          totalLocations: 221,
          daysMatchPercent: 1,
          daysMatched: 7,
          totalDays: 7,
        },
        includedInNetCost: false,
        displayInd: true,
        performMatchInd: true,
        allowTypeMatchInd: true,
        cancelled: false,
        vendorNumber: "**********",
        vendorName: "KEHE DISTRIBUTORS",
        performStartDate: "2024-08-21",
        performEndDate: "2024-08-27",
      },
      {
        offerNumber: 7016469,
        firstAllowanceLocationTypeTxt: "Store Group",
        vehicleRef: {
          vehicleId: "656644934710ba7ffc49aed7",
          vehicleNm: "27 Week 34 Insert 2024",
          sourceVehicleSk: 59234,
          startDate: "2024-08-21",
          endDate: "2024-08-27",
          vehicleType: {
            vehicleTypeId: "636abba1b426ee543a94d3ac",
            sourceVehicleTypeSk: 198,
            vehicleTypNm: "insrt",
            vehicleTypDesc: "Weekly Insert",
          },
        },
        allowanceLocationTypeTxts: ["Store Group"],
        storeGroupRefs: [
          {
            storeGroupId: "6614614a5996dcc293a6b7bd",
            sourceStoreGroupId: "EDM",
            storeGroupName: "Seattle All Stores",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Division",
              groupInd: "D",
            },
            divisionIds: ["27"],
            storeCount: 219,
            storeGrpNbr: 1713327680,
          },
        ],
        performance: {
          id: "63a3a12743a6cee87995b81b",
          performance: "DSD Off Invoice (01)",
          allowanceType: "Case",
          allowanceCd: "C",
          perfCode1: "51",
          perfCode2: "01",
          allwPerfSystemCode: "C-51-01",
          payType: "I",
          eventTypes: ["DP", "AO", "MD"],
          allowanceOverlaps: {
            minDisplayMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
            minNetCostInclude: {
              enableMatch: true,
              itemMatchPercent: 0.75,
              locationMatchPercent: 0.75,
              daysMatchPercent: 0.75,
            },
            performanceMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
          },
          performanceConfig: {
            defaultCreateInd: "CD",
            allowOnlyOverrideStoreGroupsInd: false,
          },
          deleteInd: "",
          productSources: ["DSD"],
        },
        allowanceType: "CASE",
        allowanceStatus: "Draft",
        allowanceAggrStartDate: "2024-08-19",
        allowanceAggrEndDate: "2024-08-27",
        packRetail: 12,
        vendorPackConversionFactor: 1,
        allowanceAmount: 2,
        minAllowanceAmount: 0,
        maxAllowanceAmount: 2,
        overlapMetrics: {
          name: "7016469",
          level: 1,
          itemsMatchPercent: 0.6667,
          itemsMatched: 2,
          totalItems: 3,
          locationsMatchPercent: 0.991,
          locationsMatched: 219,
          totalLocations: 221,
          daysMatchPercent: 1,
          daysMatched: 7,
          totalDays: 7,
        },
        includedInNetCost: false,
        displayInd: true,
        performMatchInd: true,
        allowTypeMatchInd: true,
        cancelled: false,
        vendorNumber: "**********",
        vendorName: "KEHE DISTRIBUTORS",
        performStartDate: "2024-08-21",
        performEndDate: "2024-08-27",
      },
      {
        offerNumber: 7016470,
        firstAllowanceLocationTypeTxt: "Store Group",
        vehicleRef: {
          vehicleId: "656644934710ba7ffc49aed7",
          vehicleNm: "27 Week 34 Insert 2024",
          sourceVehicleSk: 59234,
          startDate: "2024-08-21",
          endDate: "2024-08-27",
          vehicleType: {
            vehicleTypeId: "636abba1b426ee543a94d3ac",
            sourceVehicleTypeSk: 198,
            vehicleTypNm: "insrt",
            vehicleTypDesc: "Weekly Insert",
          },
        },
        allowanceLocationTypeTxts: ["Store Group"],
        storeGroupRefs: [
          {
            storeGroupId: "6614614a5996dcc293a6b7bd",
            sourceStoreGroupId: "EDM",
            storeGroupName: "Seattle All Stores",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Division",
              groupInd: "D",
            },
            divisionIds: ["27"],
            storeCount: 219,
            storeGrpNbr: 1713327680,
          },
        ],
        performance: {
          id: "63a3a12743a6cee87995b81b",
          performance: "DSD Off Invoice (01)",
          allowanceType: "Case",
          allowanceCd: "C",
          perfCode1: "51",
          perfCode2: "01",
          allwPerfSystemCode: "C-51-01",
          payType: "I",
          eventTypes: ["DP", "AO", "MD"],
          allowanceOverlaps: {
            minDisplayMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
            minNetCostInclude: {
              enableMatch: true,
              itemMatchPercent: 0.75,
              locationMatchPercent: 0.75,
              daysMatchPercent: 0.75,
            },
            performanceMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
          },
          performanceConfig: {
            defaultCreateInd: "CD",
            allowOnlyOverrideStoreGroupsInd: false,
          },
          deleteInd: "",
          productSources: ["DSD"],
        },
        allowanceType: "CASE",
        allowanceStatus: "Draft",
        allowanceAggrStartDate: "2024-08-19",
        allowanceAggrEndDate: "2024-08-27",
        packRetail: 12,
        vendorPackConversionFactor: 1,
        allowanceAmount: 2,
        minAllowanceAmount: 0,
        maxAllowanceAmount: 2,
        overlapMetrics: {
          name: "7016470",
          level: 1,
          itemsMatchPercent: 0.6667,
          itemsMatched: 2,
          totalItems: 3,
          locationsMatchPercent: 0.991,
          locationsMatched: 219,
          totalLocations: 221,
          daysMatchPercent: 1,
          daysMatched: 7,
          totalDays: 7,
        },
        includedInNetCost: false,
        displayInd: true,
        performMatchInd: true,
        allowTypeMatchInd: true,
        cancelled: false,
        vendorNumber: "**********",
        vendorName: "KEHE DISTRIBUTORS",
        performStartDate: "2024-08-21",
        performEndDate: "2024-08-27",
      },
      {
        offerNumber: 7016369,
        firstAllowanceLocationTypeTxt: "Store Group",
        vehicleRef: {
          vehicleId: "656644934710ba7ffc49aed7",
          vehicleNm: "27 Week 34 Insert 2024",
          sourceVehicleSk: 59234,
          startDate: "2024-08-21",
          endDate: "2024-08-27",
          vehicleType: {
            vehicleTypeId: "636abba1b426ee543a94d3ac",
            sourceVehicleTypeSk: 198,
            vehicleTypNm: "insrt",
            vehicleTypDesc: "Weekly Insert",
          },
        },
        allowanceLocationTypeTxts: ["Store Group"],
        storeGroupRefs: [
          {
            storeGroupId: "6614614a5996dcc293a6b7bd",
            sourceStoreGroupId: "EDM",
            storeGroupName: "Seattle All Stores",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Division",
              groupInd: "D",
            },
            divisionIds: ["27"],
            storeCount: 219,
          },
        ],
        performance: {
          id: "63a3a12743a6cee87995b835",
          performance: "Coupon/OMS (75)",
          allowanceType: "Scan",
          allowanceCd: "T",
          perfCode1: "20",
          perfCode2: "75",
          allwPerfSystemCode: "T-20-75",
          payType: "B",
          eventTypes: ["DP"],
          allowanceOverlaps: {
            minDisplayMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
            minNetCostInclude: {
              enableMatch: false,
              itemMatchPercent: 0.75,
              locationMatchPercent: 0.75,
              daysMatchPercent: 0.75,
            },
            performanceMatch: {
              enableMatch: false,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
          },
          performanceConfig: {
            defaultCreateInd: "TC",
            allowOnlyOverrideStoreGroupsInd: false,
          },
          deleteInd: "",
          productSources: ["DSD", "WAREHOUSE"],
        },
        allowanceType: "SCAN",
        allowanceStatus: "Draft",
        allowanceAggrStartDate: "2024-08-21",
        allowanceAggrEndDate: "2024-08-27",
        packRetail: 12,
        vendorPackConversionFactor: 1,
        allowanceAmount: 1,
        minAllowanceAmount: 0,
        maxAllowanceAmount: 1,
        overlapMetrics: {
          name: "7016369",
          level: 1,
          itemsMatchPercent: 0.6667,
          itemsMatched: 2,
          totalItems: 3,
          locationsMatchPercent: 0.991,
          locationsMatched: 219,
          totalLocations: 221,
          daysMatchPercent: 1,
          daysMatched: 7,
          totalDays: 7,
        },
        includedInNetCost: false,
        displayInd: true,
        performMatchInd: false,
        allowTypeMatchInd: true,
        cancelled: false,
        vendorNumber: "**********",
        vendorName: "KEHE DISTRIBUTORS {JBG WHLSALE}",
        performStartDate: "2024-08-21",
        performEndDate: "2024-08-27",
      },
      {
        offerNumber: 7014647,
        firstAllowanceLocationTypeTxt: "Store Group",
        vehicleRef: {
          vehicleId: "656644934710ba7ffc49ae06",
          vehicleNm: "27 Discontinued Wk 24 2024",
          sourceVehicleSk: 59077,
          startDate: "2024-06-10",
          endDate: "2024-09-01",
          vehicleType: {
            vehicleTypeId: "6509e0ddfe1e39b92eb19be8",
            sourceVehicleTypeSk: 288,
            vehicleTypNm: "disco",
            vehicleTypDesc: "Disco",
          },
        },
        allowanceLocationTypeTxts: ["Store Group"],
        storeGroupRefs: [
          {
            storeGroupId: "6454047e31c0c4e30d9580a8",
            sourceStoreGroupId: "EDM",
            storeGroupName: "ALBERTSONS",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Banner",
              groupInd: "B",
            },
            divisionIds: ["27"],
            storeCount: 20,
            storeGrpNbr: 1772882768,
          },
          {
            storeGroupId: "6454047e31c0c4e30d9580bd",
            sourceStoreGroupId: "EDM",
            storeGroupName: "SAFEWAY",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Banner",
              groupInd: "B",
            },
            divisionIds: ["27"],
            storeCount: 188,
            storeGrpNbr: 1032449881,
          },
        ],
        performance: {
          id: "63a3a12743a6cee87995b829",
          performance: "Price / Ad / Display (88)",
          allowanceType: "Header Flat",
          allowanceCd: "A",
          perfCode1: "88",
          perfCode2: " ",
          allwPerfSystemCode: "A-88- ",
          payType: "B",
          eventTypes: ["DP", "MD"],
          allowanceOverlaps: {
            minDisplayMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
            minNetCostInclude: {
              enableMatch: false,
              itemMatchPercent: 0.75,
              locationMatchPercent: 0.75,
              daysMatchPercent: 0.75,
            },
            performanceMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
          },
          performanceConfig: {
            defaultCreateInd: "LC",
            allowOnlyOverrideStoreGroupsInd: false,
          },
          deleteInd: "",
          productSources: ["DSD", "WAREHOUSE"],
        },
        allowanceType: "HEADER_FLAT",
        allowanceStatus: "Pending With Vendor",
        allowanceAggrStartDate: "2024-06-10",
        allowanceAggrEndDate: "2024-09-01",
        packRetail: 12,
        vendorPackConversionFactor: 1,
        allowanceAmount: 1000,
        minAllowanceAmount: 0,
        maxAllowanceAmount: 1000,
        overlapMetrics: {
          name: "7014647",
          level: 1,
          itemsMatchPercent: 0.6667,
          itemsMatched: 2,
          totalItems: 3,
          locationsMatchPercent: 0.9412,
          locationsMatched: 208,
          totalLocations: 221,
          daysMatchPercent: 1,
          daysMatched: 7,
          totalDays: 7,
        },
        includedInNetCost: false,
        displayInd: true,
        performMatchInd: true,
        allowTypeMatchInd: false,
        cancelled: false,
        vendorNumber: "017702-001",
        vendorName: "ARIZONA BEVERAGES USA LLC",
        performStartDate: "2024-08-21",
        performEndDate: "2024-08-27",
      },
      {
        offerNumber: 7014881,
        firstAllowanceLocationTypeTxt: "Store Group",
        vehicleRef: {
          vehicleId: "656644934710ba7ffc49af1f",
          vehicleNm: "27 Week 38 Insert 2024",
          sourceVehicleSk: 59296,
          startDate: "2024-09-18",
          endDate: "2024-09-24",
          vehicleType: {
            vehicleTypeId: "636abba1b426ee543a94d3ac",
            sourceVehicleTypeSk: 198,
            vehicleTypNm: "insrt",
            vehicleTypDesc: "Weekly Insert",
          },
        },
        allowanceLocationTypeTxts: ["Store Group"],
        storeGroupRefs: [
          {
            storeGroupId: "6614614a5996dcc293a6b7bd",
            sourceStoreGroupId: "EDM",
            storeGroupName: "Seattle All Stores",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Division",
              groupInd: "D",
            },
            divisionIds: ["27"],
            storeCount: 219,
            storeGrpNbr: 1905966089,
          },
        ],
        performance: {
          id: "63a3a12743a6cee87995b81d",
          performance: "Price / Ad / Display (88)",
          allowanceType: "Case",
          allowanceCd: "C",
          perfCode1: "48",
          perfCode2: "88",
          allwPerfSystemCode: "C-48-88",
          payType: "B",
          eventTypes: ["DP", "MD"],
          allowanceOverlaps: {
            minDisplayMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
            minNetCostInclude: {
              enableMatch: true,
              itemMatchPercent: 0.75,
              locationMatchPercent: 0.75,
              daysMatchPercent: 0.75,
            },
            performanceMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
          },
          performanceConfig: {
            defaultCreateInd: "CB",
            allowOnlyOverrideStoreGroupsInd: false,
            allowancePerformanceChildId: "63a3a12743a6cee87995b81b",
          },
          deleteInd: "",
          productSources: ["WAREHOUSE"],
        },
        allowanceType: "CASE",
        allowanceStatus: "Draft",
        allowanceAggrStartDate: "2024-08-14",
        allowanceAggrEndDate: "2024-09-24",
        packRetail: 1,
        vendorPackConversionFactor: 1,
        allowanceAmount: 1,
        minAllowanceAmount: 0,
        maxAllowanceAmount: 1,
        overlapMetrics: {
          name: "7014881",
          level: 1,
          itemsMatchPercent: 0,
          itemsMatched: 0,
          totalItems: 3,
          locationsMatchPercent: 0.991,
          locationsMatched: 219,
          totalLocations: 221,
          daysMatchPercent: 1,
          daysMatched: 7,
          totalDays: 7,
        },
        includedInNetCost: false,
        displayInd: false,
        performMatchInd: true,
        allowTypeMatchInd: true,
        cancelled: false,
        vendorNumber: "017702-001",
        vendorName: "ARIZONA BEVERAGES USA LLC",
        performStartDate: "2024-08-21",
        performEndDate: "2024-08-27",
      },
      {
        offerNumber: 7016450,
        firstAllowanceLocationTypeTxt: "Store Group",
        vehicleRef: {
          vehicleId: "656644934710ba7ffc49aed7",
          vehicleNm: "27 Week 34 Insert 2024",
          sourceVehicleSk: 59234,
          startDate: "2024-08-21",
          endDate: "2024-08-27",
          vehicleType: {
            vehicleTypeId: "636abba1b426ee543a94d3ac",
            sourceVehicleTypeSk: 198,
            vehicleTypNm: "insrt",
            vehicleTypDesc: "Weekly Insert",
          },
        },
        allowanceLocationTypeTxts: ["Store Group"],
        storeGroupRefs: [
          {
            storeGroupId: "6453d8fc31c0c4e30d8f1293",
            sourceStoreGroupId: "EDM",
            storeGroupName: "SSEA",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "ROG",
              groupInd: "G",
            },
            divisionIds: ["27"],
            storeCount: 141,
            storeGrpNbr: 407960937,
          },
          {
            storeGroupId: "6453d8fc31c0c4e30d8f12ac",
            sourceStoreGroupId: "EDM",
            storeGroupName: "SACG",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "ROG",
              groupInd: "G",
            },
            divisionIds: ["27"],
            storeCount: 26,
            storeGrpNbr: 1003792681,
          },
        ],
        performance: {
          id: "63a3a12743a6cee87995b833",
          performance: "Liquor Only (38)",
          allowanceType: "Scan",
          allowanceCd: "T",
          perfCode1: "38",
          perfCode2: "88",
          allwPerfSystemCode: "T-38-88",
          payType: "B",
          eventTypes: ["DP"],
          allowanceOverlaps: {
            minDisplayMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
            minNetCostInclude: {
              enableMatch: true,
              itemMatchPercent: 0.75,
              locationMatchPercent: 0.75,
              daysMatchPercent: 0.75,
            },
            performanceMatch: {
              enableMatch: true,
              itemMatchPercent: 0.1,
              locationMatchPercent: 0.1,
              daysMatchPercent: 0.1,
            },
          },
          performanceConfig: {
            defaultCreateInd: "TC",
            allowOnlyOverrideStoreGroupsInd: false,
          },
          deleteInd: "",
          productSources: ["DSD", "WAREHOUSE"],
        },
        allowanceType: "SCAN",
        allowanceStatus: "Draft",
        allowanceAggrStartDate: "2024-08-21",
        allowanceAggrEndDate: "2024-08-27",
        packRetail: 10,
        vendorPackConversionFactor: 1,
        allowanceAmount: 2,
        minAllowanceAmount: 0,
        maxAllowanceAmount: 2,
        overlapMetrics: {
          name: "7016450",
          level: 1,
          itemsMatchPercent: 0,
          itemsMatched: 0,
          totalItems: 3,
          locationsMatchPercent: 0.7557,
          locationsMatched: 167,
          totalLocations: 221,
          daysMatchPercent: 1,
          daysMatched: 7,
          totalDays: 7,
        },
        includedInNetCost: false,
        displayInd: false,
        performMatchInd: true,
        allowTypeMatchInd: true,
        cancelled: false,
        vendorNumber: "019802-002",
        vendorName: "LOTUS BAKERIES N AMERICA INC{CPS}",
        performStartDate: "2024-08-21",
        performEndDate: "2024-08-27",
      },
    ],
    dsdVendors: [
      {
        vendorId: "**********",
        vendorName: "KEHE DISTRIBUTORS",
      },
      {
        vendorId: "**********",
        vendorName: "KEHE DISTRIBUTORS {JBG WHLSALE}",
      },
    ],
    dsdFundingItems: [
      {
        itemId: "2021581",
        itemDescription: "ANNIES HOMEGROWN SNACK MIX CHEDDAR ORG  ",
        primaryUpc: "************",
        consumerUpc: "************",
        caseUpc: "*************",
        itemUpcs: ["*************", "************"],
        packWhse: 12,
        size: "9.0 OZ",
        dsdOffersFundings: [
          {
            itemId: "2021581",
            vendorId: {
              vendorNbr: "006446",
              vendorSubAccount: "001",
              costArea: "1",
              distCenter: "",
              fullVendorNbr: "**********-1",
            },
            dsdVendorNbr: "**********-1",
            costAreaDesc: "JBG STORES",
            itemDsdVendorAuthorized: false,
            dsdOfferOverlapFunding: [],
          },
          {
            itemId: "2021581",
            vendorId: {
              vendorNbr: "006446",
              vendorSubAccount: "001",
              costArea: "2",
              distCenter: "",
              fullVendorNbr: "**********-2",
            },
            dsdVendorNbr: "**********-2",
            costAreaDesc: "JBG STORES",
            itemDsdVendorAuthorized: false,
            dsdOfferOverlapFunding: [],
          },
          {
            itemId: "2021581",
            vendorId: {
              vendorNbr: "006446",
              vendorSubAccount: "001",
              costArea: "4",
              distCenter: "",
              fullVendorNbr: "**********-4",
            },
            dsdVendorNbr: "**********-4",
            costAreaDesc: "JBG STORES",
            itemDsdVendorAuthorized: false,
            dsdOfferOverlapFunding: [],
          },
          {
            itemId: "2021581",
            vendorId: {
              vendorNbr: "006446",
              vendorSubAccount: "013",
              costArea: "1",
              distCenter: "",
              fullVendorNbr: "**********-1",
            },
            dsdVendorNbr: "**********-1",
            costAreaDesc: "JBG STORES",
            itemDsdVendorAuthorized: true,
            dsdOfferOverlapFunding: [
              {
                offer: 7016526,
                basisDsc: "C",
                amount: 2,
                convertedAmount: 0.1667,
              },
              {
                offer: 7016539,
                basisDsc: "C",
                amount: 1,
                convertedAmount: 0.0833,
              },
            ],
          },
        ],
      },
      {
        itemId: "2021601",
        itemDescription: "ANNIES HOM SNACK MIX BUNNY O            ",
        primaryUpc: "************",
        consumerUpc: "************",
        caseUpc: "*************",
        itemUpcs: ["*************", "************"],
        packWhse: 12,
        size: "9.0 OZ",
        dsdOffersFundings: [
          {
            itemId: "2021601",
            vendorId: {
              vendorNbr: "006446",
              vendorSubAccount: "001",
              costArea: "1",
              distCenter: "",
              fullVendorNbr: "**********-1",
            },
            dsdVendorNbr: "**********-1",
            costAreaDesc: "MAINLAND",
            itemDsdVendorAuthorized: true,
            dsdOfferOverlapFunding: [
              {
                offer: 7016526,
                basisDsc: "C",
                amount: 2,
                convertedAmount: 0.1667,
              },
              {
                offer: 7016539,
                basisDsc: "C",
                amount: 1,
                convertedAmount: 0.0833,
              },
            ],
          },
          {
            itemId: "2021601",
            vendorId: {
              vendorNbr: "006446",
              vendorSubAccount: "001",
              costArea: "2",
              distCenter: "",
              fullVendorNbr: "**********-2",
            },
            dsdVendorNbr: "**********-2",
            costAreaDesc: "ALASKA",
            itemDsdVendorAuthorized: true,
            dsdOfferOverlapFunding: [],
          },
          {
            itemId: "2021601",
            vendorId: {
              vendorNbr: "006446",
              vendorSubAccount: "001",
              costArea: "4",
              distCenter: "",
              fullVendorNbr: "**********-4",
            },
            dsdVendorNbr: "**********-4",
            costAreaDesc: "SWEETENED BEV TAX STORES",
            itemDsdVendorAuthorized: true,
            dsdOfferOverlapFunding: [
              {
                offer: 7016526,
                basisDsc: "C",
                amount: 2,
                convertedAmount: 0.1667,
              },
              {
                offer: 7016539,
                basisDsc: "C",
                amount: 1,
                convertedAmount: 0.0833,
              },
            ],
          },
          {
            itemId: "2021601",
            vendorId: {
              vendorNbr: "006446",
              vendorSubAccount: "013",
              costArea: "1",
              distCenter: "",
              fullVendorNbr: "**********-1",
            },
            dsdVendorNbr: "**********-1",
            costAreaDesc: "JBG STORES",
            itemDsdVendorAuthorized: true,
            dsdOfferOverlapFunding: [
              {
                offer: 7016526,
                basisDsc: "C",
                amount: 2,
                convertedAmount: 0.1667,
              },
              {
                offer: 7016539,
                basisDsc: "C",
                amount: 1,
                convertedAmount: 0.0833,
              },
            ],
          },
        ],
      },
      {
        itemId: "4100274",
        itemDescription: "CBS NUTS PEANUT BTTR CRMMNCHY ORG       ",
        primaryUpc: "************",
        consumerUpc: "************",
        caseUpc: "*************",
        itemUpcs: ["*************", "************"],
        packWhse: 12,
        size: "16.0 OZ",
        dsdOffersFundings: [
          {
            itemId: "4100274",
            vendorId: {
              vendorNbr: "006446",
              vendorSubAccount: "001",
              costArea: "1",
              distCenter: "",
              fullVendorNbr: "**********-1",
            },
            dsdVendorNbr: "**********-1",
            costAreaDesc: "MAINLAND",
            itemDsdVendorAuthorized: true,
            dsdOfferOverlapFunding: [
              {
                offer: 7016526,
                basisDsc: "C",
                amount: 2,
                convertedAmount: 0.1667,
              },
              {
                offer: 7016539,
                basisDsc: "C",
                amount: 1,
                convertedAmount: 0.0833,
              },
            ],
          },
          {
            itemId: "4100274",
            vendorId: {
              vendorNbr: "006446",
              vendorSubAccount: "001",
              costArea: "2",
              distCenter: "",
              fullVendorNbr: "**********-2",
            },
            dsdVendorNbr: "**********-2",
            costAreaDesc: "ALASKA",
            itemDsdVendorAuthorized: true,
            dsdOfferOverlapFunding: [
              {
                offer: 7016526,
                basisDsc: "C",
                amount: 2,
                convertedAmount: 0.1667,
              },
              {
                offer: 7016539,
                basisDsc: "C",
                amount: 1,
                convertedAmount: 0.0833,
              },
            ],
          },
          {
            itemId: "4100274",
            vendorId: {
              vendorNbr: "006446",
              vendorSubAccount: "001",
              costArea: "4",
              distCenter: "",
              fullVendorNbr: "**********-4",
            },
            dsdVendorNbr: "**********-4",
            costAreaDesc: "SWEETENED BEV TAX STORES",
            itemDsdVendorAuthorized: true,
            dsdOfferOverlapFunding: [
              {
                offer: 7016526,
                basisDsc: "C",
                amount: 2,
                convertedAmount: 0.1667,
              },
              {
                offer: 7016539,
                basisDsc: "C",
                amount: 1,
                convertedAmount: 0.0833,
              },
            ],
          },
          {
            itemId: "4100274",
            vendorId: {
              vendorNbr: "006446",
              vendorSubAccount: "013",
              costArea: "1",
              distCenter: "",
              fullVendorNbr: "**********-1",
            },
            dsdVendorNbr: "**********-1",
            costAreaDesc: "JBG STORES",
            itemDsdVendorAuthorized: true,
            dsdOfferOverlapFunding: [
              {
                offer: 7016526,
                basisDsc: "C",
                amount: 2,
                convertedAmount: 0.1667,
              },
              {
                offer: 7016539,
                basisDsc: "C",
                amount: 1,
                convertedAmount: 0.0833,
              },
            ],
          },
        ],
      },
    ],
  },
};
export default dsdFundingMockData;
