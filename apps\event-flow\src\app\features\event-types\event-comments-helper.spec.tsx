import { EEVENT_STATUS, PROMOTION_TYPES } from "@me/util-helpers";
import efConstants from "../../shared/ef-constants/ef-constants";
import {
  getOfferTitle,
  getPromoTittle,
  getHeaderTitlesList,
  handleSelectOfferPromoList,
} from "./event-comments-helper";

import {
  getAllowanceTypeByPerformance,
  getOfferAllowanceAmount,
} from "../create-event/service/allowance/allowance-service";

import * as MEUPP_UTILS from "@me-upp-js/utilities";
jest.mock("../create-event/service/allowance/allowance-service", () => ({
  getAllowanceTypeByPerformance: jest.fn(),
  getOfferAllowanceAmount: jest.fn(),
}));

const { ALLOWANCE_TYPES } = efConstants;
const { HEADERFLAT } = ALLOWANCE_TYPES;

describe("event-comments-helper", () => {
  beforeEach(() => {
    jest.spyOn(MEUPP_UTILS, "isPromoChanges").mockReturnValue(false);
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  describe("getOfferTitle", () => {
    it("should return the correct offer title", () => {
      const allowanceType = "Scan";
      const offerAmount = "$1.05";
      const responseData = {
        offerNumber: "123",
      };
      (getAllowanceTypeByPerformance as jest.Mock).mockReturnValue(
        allowanceType
      );
      (getOfferAllowanceAmount as jest.Mock).mockReturnValue(offerAmount);

      const result = getOfferTitle(responseData);

      expect(result).toEqual({
        title: `Offer # 123 - ${allowanceType} ${offerAmount}`,
        id: "123",
        type: allowanceType,
        isOffer: true,
      });
    });
  });

  describe("getPromoTittle", () => {
    it("should return the correct promo title", () => {
      const promotion = {
        promoDetails: {
          promotionType: "NET_PRICE",
          amount: 10,
          unitMeasure: "unit",
          itemsLimit: 5,
          minQuantity: 2,
        },
        promotionId: "456",
      };
      const promotionType = PROMOTION_TYPES.NET_PRICE;

      const result = getPromoTittle(promotion);

      expect(result).toEqual({
        title: `Promo # 456 - ${promotionType}, $10 Each`,
        id: "456",
        type: "NET_PRICE",
        isOffer: false,
      });
    });
  });

  describe("getHeaderTitlesList", () => {
    it("should return the correct header titles list", () => {
      const eventDetailsData = {
        eventType: "someEventType",
        offerAllowances: [
          {
            allowances: [
              {
                allowanceStatus: EEVENT_STATUS.AGREED,
                performance: "performance",
              },
            ],
            offerNumber: "123",
          },
        ],
        promotionsList: [
          {
            promotionWorkflowStatus: EEVENT_STATUS.AGREED,
            promoDetails: {
              promotionType: "NET_PRICE",
              amount: 10,
              unitMeasure: "unit",
              itemsLimit: 5,
              minQuantity: 2,
            },
            promotionId: "456",
          },
        ],
      };
      const allowanceType = HEADERFLAT.label;
      const offerAmount = "100";
      const promotionType = PROMOTION_TYPES.NET_PRICE;

      (getAllowanceTypeByPerformance as jest.Mock).mockReturnValue(
        allowanceType
      );
      (getOfferAllowanceAmount as jest.Mock).mockReturnValue(offerAmount);

      const result = getHeaderTitlesList(eventDetailsData);

      expect(result).toEqual([
        {
          title: `Offer # 123 - ${allowanceType} ${offerAmount}`,
          id: "123",
          type: allowanceType,
          isOffer: true,
        },
      ]);
    });
    it("should return an empty array when eventDetailsData is empty", () => {
      const eventDetailsData = {};
      const result = getHeaderTitlesList(eventDetailsData);
      expect(result).toEqual([]);
    });
    it("should return an empty array when offer and promo are not in agreed statuses", () => {
      const eventDetailsData = {
        offerAllowances: [
          {
            allowances: [
              {
                allowanceStatus: EEVENT_STATUS.DRAFT,
                performance: "performance",
              },
            ],
            offerNumber: "123",
          },
        ],
        promotionsList: [
          {
            promotionWorkflowStatus: EEVENT_STATUS.DRAFT,
            promoDetails: {
              promotionType: "NET_PRICE",
              amount: 10,
              unitMeasure: "unit",
              itemsLimit: 5,
              minQuantity: 2,
            },
            promotionId: "456",
          },
        ],
      };
      const result = getHeaderTitlesList(eventDetailsData);
      expect(result).toEqual([]);
    });
  });

  describe("handleSelectOfferPromoList", () => {
    it("should add the offerPromo to the list when checked is true", () => {
      const offerPromo = { id: "123", type: "Scan" };
      const checked = true;
      const setFn = jest.fn();
      const offerPromoVal = [];

      handleSelectOfferPromoList(offerPromo, checked, setFn, offerPromoVal);

      expect(setFn).toHaveBeenCalledWith([{ id: "123", type: "Scan" }]);
    });

    it("should remove the offerPromo from the list when checked is false", () => {
      const offerPromo = { id: "123", type: "Scan" };
      const checked = false;
      const setFn = jest.fn();
      const offerPromoVal = [{ id: "123", type: "Scan" }];

      handleSelectOfferPromoList(offerPromo, checked, setFn, offerPromoVal);

      expect(setFn).toHaveBeenCalledWith([]);
    });
  });
});
