import { useEffect, useRef, useState } from "react";
import { useSelectorWrap } from "@me/data-rtk";
import {
  addDaysHelper,
  checkInvalidScanDates,
  createDateKeyMapperObject,
  getFieldWithHighletedWrapper,
  getUpdatedDatesArray,
  renderVendorDetails,
  renderWarehouseDetails,
} from "../../../allowance/stepper/common-stepper/allowance-dates/allowance-dates-service";
import { FormFieldError } from "@me/util-form-wrapper";
import LoadingSpinner from "../../../../../constants/LoadingSpinner/LoadingSpinner";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";
import { useGetAllowancesItemsQuery } from "../../../../../service/apis/allowance-api";
import { getAllowanceFormRegisterKey } from "../../../../../service/allowance/allowance-service";
import { DatePickerAtom } from "../../../../fields/allowance-atoms";
import { useDispatch } from "react-redux";
import {
  setAllowanceFormInfo,
  setOfferAmontsData,
  setVendorsForAllowances,
} from "../../../../../service/slice/allowance-details-slice";
import useGetOfferSectionConfiguration from "../../hooks/useGetOfferSectionConfiguration";
import { OFFER_FORM_FIELDS } from "../../offer-flow-config";
import { checkIsNationalEvent } from "../../offer-service";

export default function ScanAdditionalDates({
  cardIndex,
  cardItemIndex,
  formControls,
  sectionConfiguration,
  isEditEnable = false,
  offerMapKey = "",
  vehicleFields,
  sectionKey,
}) {
  const {
    ALLOWANCE_TYPES,
    OFFER_ALLOWANCE_GROUP,
    WAREHOUSE_HEADER_DISABLE_DISTCENTER_KEY,
  } = efConstants;
  const {
    fields: { startDate, endDate, errors },
  } = sectionConfiguration;
  const { additionalDatesChangeKey, additionalDatesKey, datesValidateKey } =
    OFFER_FORM_FIELDS;

  const {
    register = () => null,
    control,
    setValue = () => null,
    trigger,
  } = formControls || {};

  const dispatch = useDispatch();
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { isAllowConvEnable } = useSelectorWrap("allow_conv_status_rn").data;
  const {
    data: { allowanceData: allowanceTempWorkData },
  } = useSelectorWrap("allowance_temp_work");
  const { data: allowanceSliceData } = useSelectorWrap("allowance_form_data");
  const { productSources = [] } =
    useSelectorWrap("allowance_product_sources_rn")?.data || {};
  const { offerAmounts = {}, isAdditionalDatesChanged = {} } =
    useSelectorWrap("offer_amounts_details")?.data || {};
  const {
    vendors: { myVendors = [] } = {},
    isMultiVendor = false,
    isAllowMinSfoChanged = false,
    allowancesAmontsResp = {},
    isMultiVendorEnabled,
  } = useSelectorWrap("vendors_data_for_allowance_rn").data;

  const allowanceRegField = getAllowanceFormRegisterKey(
    cardIndex,
    cardItemIndex
  );

  const allowanceFormData =
    allowanceSliceData?.allowanceFormData?.[allowanceRegField];
  const isCancelled = allowanceFormData?.isCancelled?.[offerMapKey];
  const tempAllowItems = allowanceFormData?.additionalDates?.[offerMapKey];
  const vehicleStart = vehicleFields?.startDate;
  const vehicleEnd = vehicleFields?.endDate;
  const isDsdOffer = offerMapKey === OFFER_ALLOWANCE_GROUP.CASE.DSD;
  const isWarehouseOffer = offerMapKey === OFFER_ALLOWANCE_GROUP.SCAN.COMBINED;
  const typeKey = isWarehouseOffer ? "distCenter" : "vendorNbr";
  const searchId =
    eventDetailsData?.offerAllowances?.[cardIndex || 0]?.offerNumber;
  const offerId = eventDetailsData?.offerAllowances?.[cardIndex || 0]?.id;
  const amountsData = offerAmounts?.[offerMapKey]?.allowances || [];
  const isNationalEvent = checkIsNationalEvent(eventDetailsData?.eventType);

  const allowanceAmountsPayload = {
    URL_PARAMS: [eventDetailsData.id, offerMapKey],
  };

  const dates = useRef<any>([]);
  const additionalDatesCount = useRef(0);
  const addionalDatesUpdateCount = useRef(0);
  const [count, setCount] = useState(0);
  const [datesArray, setDatesArray] = useState<any>([]);
  const [invalidDatesVendor, setInvalidDatesVendor] = useState<any>([]);
  const [dateType, setDateType] = useState<any>([]);
  const [refreshDates, setRefreshDates] = useState(false);

  const { clearNextSectionsOnOptionChange } = useGetOfferSectionConfiguration({
    allowanceRegField,
  });

  const {
    data: allowancesResp,
    isFetching,
    isLoading,
  } = useGetAllowancesItemsQuery(allowanceAmountsPayload, {
    refetchOnMountOrArgChange: true,
    skip:
      isNationalEvent ||
      isAllowConvEnable ||
      !allowanceTempWorkData?.tempWorkAllowanceId ||
      !productSources?.length ||
      !!amountsData?.length ||
      isAllowMinSfoChanged ||
      !!count,
  });

  const isAmountsDataLoading = isFetching || isLoading;
  useEffect(() => {
    if (
      (amountsData?.length ||
        allowancesResp?.allowances?.length ||
        isAllowConvEnable) &&
      (!count || isAllowMinSfoChanged)
    ) {
      setRefreshDates(isAllowMinSfoChanged);
      const amountsResult =
        !isAllowMinSfoChanged && amountsData?.length
          ? offerAmounts?.[offerMapKey]
          : allowancesResp || allowancesAmontsResp;

      let filteredResp: any = amountsResult;
      if (isMultiVendorEnabled && isDsdOffer) {
        filteredResp = !isMultiVendor
          ? {
              ...amountsResult,
              allowances: amountsResult?.allowances?.filter(item =>
                myVendors?.some(
                  vendor => vendor?.dsdVendorNbr === item?.vendorNbr
                )
              ),
            }
          : amountsResult;
        dispatch(
          setVendorsForAllowances({
            ...myVendors,
            isMultiVendor,
            isAllowMinSfoChanged: false,
            ...(allowancesResp && { allowancesAmontsResp: allowancesResp }),
          })
        );
      }

      (!amountsData?.length || isAllowMinSfoChanged) &&
        dispatch(
          setOfferAmontsData({
            offerAmounts: { ...offerAmounts, [offerMapKey]: filteredResp },
            ...(!isCancelled && {
              isAdditionalDatesChanged: {
                ...isAdditionalDatesChanged,
                [offerMapKey]: 0,
              },
            }),
          })
        );
      const vendorsArray = isAllowConvEnable
        ? tempAllowItems
        : filteredResp?.allowances;
      additionalDatesCount.current = (vendorsArray?.length || 1) * 2;
      vendorsArray?.length &&
        setDatesArray(() =>
          getUpdatedDatesArray(
            vendorsArray,
            {
              vehicleStart,
              vehicleEnd,
            },
            !eventDetailsData?.inValidAllowances?.includes(offerId)
          )
        );
      setValue(datesValidateKey, !!vendorsArray?.length);
      setValue(additionalDatesChangeKey, false);
    } else {
      setValue(datesValidateKey, false);
    }
  }, [allowancesResp, isMultiVendor]);

  useEffect(() => {
    if (!count || refreshDates) {
      dates.current = datesArray;
      setRefreshDates(false);
    }
    setValue(additionalDatesKey, datesArray);
    if (datesArray?.length) {
      datesArray?.map((vendor: any, index: number) => {
        setValue(
          getUpdatedFieldProps(startDate, index)?.registerField,
          vendor?.orderStartDate
        );
        setValue(
          getUpdatedFieldProps(endDate, index)?.registerField,
          vendor?.orderEndDate
        );
        return vendor;
      });
    }
    const { inValidVendors, type } = checkInvalidScanDates(
      datesArray,
      vehicleStart,
      vehicleEnd,
      typeKey
    );
    setInvalidDatesVendor(inValidVendors);
    setDateType(type);
    trigger?.();
  }, [JSON.stringify(datesArray)]);

  useEffect(() => {
    count && setDatesArray(() => dates.current);
  }, [count]);

  const dateKeyMapper = (key: string, date: string) => {
    return createDateKeyMapperObject(key === "Start Date", date);
  };

  const onVehicleDateChange = (
    vendorKey: string,
    dateType: string,
    date: string,
    index: number
  ) => {
    addionalDatesUpdateCount.current = addionalDatesUpdateCount.current + 1;
    dates.current = dates.current.map((vendor, i) => {
      return i === index
        ? {
            ...vendor,
            ...(vendorKey && vendor?.[typeKey] === vendorKey
              ? dateKeyMapper(dateType, date)
              : {}),
          }
        : vendor;
    });
    setCount(count => count + 1);
    if (addionalDatesUpdateCount.current > additionalDatesCount.current) {
      setValue(additionalDatesChangeKey, true);
      onSectionUpdate();
    }
  };

  const getEventData = () => {
    return eventDetailsData;
  };

  const onSectionUpdate = () => {
    if (!isEditEnable) {
      clearNextSectionsOnOptionChange(sectionKey);
    } else {
      !allowanceFormData?.isAdditionalDatesChanged &&
        dispatch(
          setAllowanceFormInfo({
            allowanceFormData: {
              [allowanceRegField]: {
                ...allowanceFormData,
                isAdditionalDatesChanged: true,
              },
            },
          })
        );
    }
  };

  const getUpdatedFieldProps = (fieldProps: any, index: number) => {
    return {
      ...fieldProps,
      registerField: `${fieldProps.registerField}.${index}`,
    };
  };

  const StartDateField = (record, index: number) => {
    const _key = record?.[typeKey];
    return startDate
      ? getFieldWithHighletedWrapper(
          <DatePickerAtom
            key={index}
            fieldProps={getUpdatedFieldProps(startDate, index)}
            register={register}
            control={control}
            customValidation={{
              value: !(
                invalidDatesVendor?.includes(_key) &&
                (dateType?.[_key]?.all || dateType?.[_key]?.vehicle)
              ),
              message: startDate?.errors?.invalid?.text,
            }}
            onChange={e => {
              onVehicleDateChange(_key, startDate?.label, e, index);
            }}
            minDate={addDaysHelper(vehicleStart, -30)}
            maxDate={addDaysHelper(vehicleEnd, 30)}
          />,
          startDate,
          getEventData,
          searchId
        )
      : null;
  };

  const EndDateField = (record, index: number) => {
    const _key = record?.[typeKey];
    return endDate
      ? getFieldWithHighletedWrapper(
          <DatePickerAtom
            key={index}
            fieldProps={getUpdatedFieldProps(endDate, index)}
            register={register}
            control={control}
            customValidation={{
              value: !(
                invalidDatesVendor?.includes(_key) &&
                (dateType?.[_key]?.all || dateType?.[_key]?.vehicle)
              ),
              message: endDate?.errors?.invalid?.text,
            }}
            onChange={e => {
              onVehicleDateChange(_key, endDate?.label, e, index);
            }}
            minDate={addDaysHelper(vehicleStart, -30)}
            maxDate={addDaysHelper(vehicleEnd, 30)}
          />,
          endDate,
          getEventData,
          searchId
        )
      : null;
  };

  const renderScanDateFields = (index: number) => {
    return [StartDateField, EndDateField]?.map(field =>
      field(dates?.current?.[index], index)
    );
  };

  return (
    <div className="pb-2">
      <LoadingSpinner
        isLoading={isAmountsDataLoading}
        classname="!h-full !w-full rounded-md"
      />
      {dates?.current?.length
        ? dates.current?.map((vendor: any, index: number) => (
            <>
              {isWarehouseOffer
                ? dates.current?.length === 1 &&
                  vendor?.distCenter === WAREHOUSE_HEADER_DISABLE_DISTCENTER_KEY
                  ? null
                  : renderWarehouseDetails(vendor, false, true)
                : renderVendorDetails(vendor)}
              <div>
                <div className="grid grid-cols-4 gap-2 mt-4">
                  {control && renderScanDateFields(index)}
                </div>
                {invalidDatesVendor?.includes(vendor?.[typeKey]) && (
                  <div className="flex flex-col gap-1 my-1">
                    {Object.keys(dateType?.[vendor?.[typeKey]] || {})?.map(
                      (type: string) =>
                        dateType?.[vendor?.[typeKey]]?.[type] && (
                          <FormFieldError
                            error={
                              errors.INVALID_DATES?.[
                                ALLOWANCE_TYPES.SCAN.key
                              ]?.[type]
                            }
                          />
                        )
                    )}
                  </div>
                )}
              </div>
            </>
          ))
        : null}
    </div>
  );
}
