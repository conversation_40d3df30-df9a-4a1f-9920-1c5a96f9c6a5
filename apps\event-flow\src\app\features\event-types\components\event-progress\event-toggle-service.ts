import { CTAs_Keys } from "../../../create-event/constants/event-status/contsants";
import {
  EEVENT_STATUS,
  EUSER_ROLES,
  EVENT_ACTION_LABELS,
} from "../event-action/event-action.model";
import { eventStatusMapObj } from "../event-action/event-status/event-map-obj";
import { getCTAProps } from "../../../create-event/constants/event-status/utility";
import * as eventStatusButtons from "../event-status-buttons";
import { getEnumValue } from "@me-upp-js/utilities";
import { EventWorkFlowIndicatorAndData, WorkFlowActionFlag, WorkFlowPlanEventUserDetails } from "../event-status-buttons/event-buttons";
import { DeepClone } from "@me/util-helpers";
import { EVENT_TYPE } from "../../../create-event/constants/constants";

const EventToggleService = (
  eventProgressService,
  eventDetailsData,
  eventStatusData,
  userRole,
  allowanceData,
  eventStatus,
  planAndPendingDatas
) => {
  const eventStatusClasses = {
    SEND_TO_VENDOR: eventStatusButtons.SEND_TO_VENDOR,
    AUTO_APPROVE: eventStatusButtons.AUTO_APPROVE,
    DELETE_EVENT: eventStatusButtons.DELETE_EVENT,
    SEND_TO_MERCHANT: eventStatusButtons.SEND_TO_MERCHANT,
    AGREE_TO_PENDING_REQUEST: eventStatusButtons.AGREE_TO_PENDING_REQUEST,
    REJECT_PENDING_REQUEST: eventStatusButtons.REJECT_PENDING_REQUEST,
    CANCEL_EVENT: eventStatusButtons.CANCEL_EVENT,
    SEND_BACK_WITH_COMMENT: eventStatusButtons.SEND_BACK_WITH_COMMENT,
    AGREE_TO_CANCEL_EVENT: eventStatusButtons.AGREE_TO_CANCEL_EVENT,
    RETURN: eventStatusButtons.RETURN,
    AGREE: eventStatusButtons.AGREE_TO_EVENT,
    REJECT_EVENT: eventStatusButtons.REJECT_EVENT,
  }

  const toggleDisableAgreedPendingAddEdit = (eventStatusCTADetails: any) => {
    if (userRole === EUSER_ROLES.VENDOR && userRole === WorkFlowPlanEventUserDetails.updateUser) {
      if (WorkFlowActionFlag.getIsModifyOrCancelEvent() === "Cancel") {
        eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.VENDOR.add =
          eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.VENDOR.sendBackWithCommentEdit;
        eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.VENDOR.edit =
          eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.MERCHANT.edit;
      }
      else {
        eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.VENDOR.edit =
          eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.MERCHANT.edit;
        eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.VENDOR.add =
          eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.MERCHANT.add;
        if (
          WorkFlowPlanEventUserDetails.updateUser === EUSER_ROLES.VENDOR &&
          EventWorkFlowIndicatorAndData.latestHistory?.createUser?.type?.toUpperCase() !== EUSER_ROLES.VENDOR
        ) {
          eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.VENDOR.edit =
            eventStatusCTADetails[EEVENT_STATUS.AGREED].USERS.VENDOR.edit;
          eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.VENDOR.add =
            eventStatusCTADetails[EEVENT_STATUS.AGREED].USERS.VENDOR.add;
        }
        if (
          WorkFlowActionFlag.getEventReturnOnAgreedPending("VENDOR").isReturnEvent &&
          WorkFlowActionFlag.getEventReturnOnAgreedPending("VENDOR").isReturnByCurrUser
        ) {
          //add and edit button for vendor after return on Agreed Pending
          eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.VENDOR.edit =
            eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.VENDOR.sendBackWithCommentEdit;
          eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.VENDOR.add =
            eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.VENDOR.sendBackWithCommentEdit;
        }
      }
    }
    const returnButtonIdx = eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.VENDOR.buttons.findIndex(
      ({ key }: any) => key === CTAs_Keys.RETURN
    );
    if (
      WorkFlowActionFlag.getEventReturnOnAgreedPending("VENDOR").isReturnEvent &&
      WorkFlowActionFlag.getEventReturnOnAgreedPending("VENDOR").isReturnByCurrUser &&
      returnButtonIdx > -1
    ) {
      eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.VENDOR.edit =
        eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.VENDOR.sendBackWithCommentEdit;
      eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.VENDOR.add =
        eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.VENDOR.sendBackWithCommentEdit;
    } else if (userRole !== WorkFlowPlanEventUserDetails.updateUser) {
      if (!(userRole === EUSER_ROLES.MERCHANT && WorkFlowActionFlag.getIsModifyOrCancelEvent() === "Modify")) {
        if (WorkFlowActionFlag.getIsEventReturn(EVENT_ACTION_LABELS.STATUS_UPDATE_WITH_COMMENT, "VENDOR") || WorkFlowActionFlag.isEventStatusUpdateWithComment()) {
          eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.VENDOR.edit =
            eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.VENDOR.sendBackWithCommentEdit;
          eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.VENDOR.add =
            eventStatusCTADetails[EEVENT_STATUS.AGREED_PENDING].USERS.VENDOR.sendBackWithCommentEdit;
        }
      }
    }
  };

  const handleToggleDisable = eventStatusFormData => {
    const eventStatusCTADetails = new DeepClone(
      eventStatusFormData
    ).getDeepClone();
    const eventStatus = eventDetailsData.eventStatus;
    const statusValue = eventDetailsData?.eventStatus
      ?.toString()
      .replaceAll(/[- ]/g, "");
    const mapobj = eventStatusMapObj;
    const buttons = mapobj?.filter((obj) => obj.role === userRole && getEnumValue(obj.eventStatus) === eventStatus);
    const returnButtons = buttons.map((button) => {
      const eventButtonStatus = getEnumValue(button.eventButton);
      if (eventStatusClasses[eventButtonStatus]) {
        const eventStatusButton = eventStatusClasses[eventButtonStatus][statusValue]()[userRole]();
        return eventStatusButton.isShow() && getCTAProps(
          getEnumValue(button.eventLabel),
          getEnumValue(button.eventButton?.toString().replace(/CTAs_Keys/g, "CTAs_Keys_Variant")),
          eventStatusButton?.isDisabled(),
          getEnumValue(button.eventButton)
        );
      } else {
        console.log(`Class not found for key: ${eventButtonStatus}`);
      }
      return null;
    });
    if (eventStatus === "Agreed-Pending") {
      toggleDisableAgreedPendingAddEdit(eventStatusCTADetails);
    }

    const deepClonedStatusCTADetails = new DeepClone(
      eventStatusCTADetails
    ).getDeepClone();
    if (deepClonedStatusCTADetails?.[eventStatus]?.USERS?.[userRole]) {
      deepClonedStatusCTADetails[eventStatus].USERS[userRole].buttons =
        returnButtons.filter(button => button) ?? [];
    }
    const buttonsToDisplay =
      eventDetailsData?.eventType === EVENT_TYPE.NCDP
        ? deepClonedStatusCTADetails?.[eventStatus]?.USERS?.[userRole]?.buttons?.filter(
          button =>
            [
              CTAs_Keys?.DELETE_EVENT,
              CTAs_Keys?.REJECT_EVENT,
              userRole === EUSER_ROLES.MERCHANT
                ? CTAs_Keys?.CANCEL_EVENT
                : null,
            ].includes(button.key)
        )
        : deepClonedStatusCTADetails?.[eventStatus]?.USERS?.[userRole]?.buttons;
    deepClonedStatusCTADetails[eventStatus].USERS[userRole].buttons =
      buttonsToDisplay;

    return deepClonedStatusCTADetails;
  };

  return { handleToggleDisable };
};

export default EventToggleService;
