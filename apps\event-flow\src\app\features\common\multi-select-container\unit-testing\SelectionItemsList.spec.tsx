import { render, screen, fireEvent } from "@testing-library/react";
import SelectionItemsList from "../SelectionItemsList";
import "@testing-library/jest-dom";

// Mock Checkbox component
jest.mock("@albertsons/uds/molecule/Checkbox", () => ({
  __esModule: true,
  default: ({ checked, onChange, value }) => (
    <input
      type="checkbox"
      checked={checked}
      value={value}
      onChange={onChange}
      data-testid="filter-checkbox"
    />
  ),
}));

describe("SelectionItemsList", () => {
  const mockOnChangeInput = jest.fn();

  const configObj = {
    uniqueId: "id",
    type: "checkbox",
    displayKey: "name",
    id: "test-id",
  };

  const updatedData = [
    { id: 1, name: "Item 1" },
    { id: 2, name: "Item 2" },
    { id: 3, name: "Item 3" },
  ];

  const selectedData = [1, 3]; // Assuming these are selected initially

  beforeEach(() => {
    mockOnChangeInput.mockClear();
  });

  it("should render the list of items correctly", () => {
    render(
      <SelectionItemsList
        configObj={configObj}
        updatedData={updatedData}
        onChangeInput={mockOnChangeInput}
        selectedData={selectedData}
      />
    );

    // Check if all items are rendered
    updatedData.forEach(item => {
      expect(screen.getByText(item.name)).toBeInTheDocument();
    });

    // Get all checkboxes
    const checkboxes = screen.getAllByTestId("filter-checkbox");

    // Check that the correct checkboxes are checked based on selectedData
    expect((checkboxes[0] as HTMLInputElement).checked).toBe(true); // Item 1 (id: 1)
    expect((checkboxes[1] as HTMLInputElement).checked).toBe(false); // Item 2 (id: 2)
    expect((checkboxes[2] as HTMLInputElement).checked).toBe(true); // Item 3 (id: 3)
  });

  it("should call onChangeInput when a checkbox is clicked", () => {
    render(
      <SelectionItemsList
        configObj={configObj}
        updatedData={updatedData}
        onChangeInput={mockOnChangeInput}
        selectedData={selectedData}
      />
    );

    // Find the checkbox for Item 2 (id: 2)
    const item2Checkbox = screen.getAllByTestId("filter-checkbox")[1];

    // Simulate a change event on the checkbox
    fireEvent.click(item2Checkbox);

    // Check if onChangeInput was called with the correct arguments
    expect(mockOnChangeInput).toHaveBeenCalledWith(
      expect.any(Object),
      updatedData[1]
    );
  });

  it("should display the label of each item", () => {
    render(
      <SelectionItemsList
        configObj={configObj}
        updatedData={updatedData}
        onChangeInput={mockOnChangeInput}
        selectedData={selectedData}
      />
    );

    // Verify that labels are displayed for each item
    updatedData.forEach(item => {
      expect(screen.getByText(item.name)).toBeInTheDocument();
    });
  });

  it("should properly handle the checked state for each checkbox", () => {
    render(
      <SelectionItemsList
        configObj={configObj}
        updatedData={updatedData}
        onChangeInput={mockOnChangeInput}
        selectedData={selectedData}
      />
    );

    // Ensure that the checkboxes for selected items are checked
    const checkboxes = screen.getAllByTestId("filter-checkbox");
    expect(checkboxes[0] as HTMLInputElement).toBeChecked(); // Item 1
    expect(checkboxes[1] as HTMLInputElement).not.toBeChecked(); // Item 2
    expect(checkboxes[2] as HTMLInputElement).toBeChecked(); // Item 3
  });
});
