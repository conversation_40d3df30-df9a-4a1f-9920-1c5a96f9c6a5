import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { useSelectorWrap } from "@me/data-rtk";
import { useDispatch } from "react-redux";
import NationalBillingSelectionModal from "./national-billing-selection-modal";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";

jest.mock("react-redux", () => ({
  useDispatch: jest.fn(),
}));

jest.mock("@albertsons/uds/molecule/Button", () => (props) => (
  <button onClick={props.onClick}>{props.children}</button>
));

jest.mock("@albertsons/uds/molecule/Modal", () => ({ isOpen, children }) =>
  isOpen ? <div data-testid="modal">{children}</div> : null
);

jest.mock("../common/distributor-mode-selection", () => () => (
  <div data-testid="distributor-mode-selection" />
));

jest.mock("../division-accordion/division-accordion-wrapper", () => () => (
  <div data-testid="division-accordion" />
));

jest.mock("@me/data-rtk", () => ({
  ...jest.requireActual("@me/data-rtk"),
  useSelectorWrap: jest.fn(),
}));

describe("NationalBillingSelectionModal", () => {
  let mockDispatch;

  beforeEach(() => {
    mockDispatch = jest.fn();
    useDispatch.mockReturnValue(mockDispatch);

    useSelectorWrap.mockImplementation((key) => {
      const mockData = {
        leadDistributors_rn: { data: { stepData: [], leadDistMode: "mode" } },
        allowanceTableColsGroupConfig_rn: { data: {} },
        division_wise_billing_selection_data_rn: { data: {} },
        national_divisions_config: { data: { selectedDivisionData: {} } },
      };
      return mockData[key] || {};
    });
  });

  it("renders the modal when isModelOpen is true", () => {
    render(<NationalBillingSelectionModal isModelOpen={true} setIsModelOpen={jest.fn()} />);
    expect(screen.getByTestId("modal")).toBeInTheDocument();
  });

  it("calls setIsModelOpen on confirm button click", () => {
    const setIsModelOpenMock = jest.fn();
    render(<NationalBillingSelectionModal isModelOpen={true} setIsModelOpen={setIsModelOpenMock} />);
    
    fireEvent.click(screen.getByText("Confirm"));
    expect(mockDispatch).toHaveBeenCalledTimes(3);
    expect(setIsModelOpenMock).toHaveBeenCalledWith(false);
  });

  it("calls resetDivisionWiseBillingSectionData and onCloseDistPopup on cancel button click", () => {
    render(<NationalBillingSelectionModal isModelOpen={true} setIsModelOpen={jest.fn()} />);
    fireEvent.click(screen.getByText("Cancel"));
    expect(mockDispatch).toHaveBeenCalled();
  });
});
