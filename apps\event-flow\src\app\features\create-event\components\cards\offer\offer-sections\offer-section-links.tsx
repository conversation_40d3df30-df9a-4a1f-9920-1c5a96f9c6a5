import { useSelectorWrap } from "@me/data-rtk";
import React, { memo } from "react";
import { dashedVerticalDivider } from "../../allowance/stepper/common-stepper/allowance-amount/allowance-amounts-services";
import { appConstants } from "@me/utils-root-props";
import { scrollToSection } from "../offer-service";

function OfferSectionLinks({ isEditEnable, cardIndex }) {
  const offerSections = useSelectorWrap("offer_sections_data")?.data || [];

  const handleSectionClick = (event, section) => {
    event?.stopPropagation();
    const ele = document?.querySelector(`#${section?.containerId}`);
    ele && scrollToSection(ele);
  };

  const renderSectionLinks =
    appConstants.IS_ENABLE_REDESIGN_ALLOWANCE_CHANGES &&
    isEditEnable?.[cardIndex];

  return (
    <>
      {renderSectionLinks && (
        <div className="flex gap-3 mr-2 px-4 pl-8">
          {offerSections?.map((section, index) => {
            return (
              <div
                onClick={e => handleSectionClick(e, section)}
                key={section?.sectionKey}
                className="flex gap-3"
              >
                <span className="text-[#1B6EBB] font-semibold">
                  {section?.headerLinkLabel}
                </span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  className="lucide lucide-corner-right-down mt-0.5 text-[#1B6EBB]"
                >
                  <polyline points="10 15 15 20 20 15" />
                  <path d="M4 4h7a4 4 0 0 1 4 4v12" />
                </svg>
                {index !== offerSections?.length - 1 && dashedVerticalDivider}
              </div>
            );
          })}
        </div>
      )}
    </>
  );
}

export default memo(OfferSectionLinks);
