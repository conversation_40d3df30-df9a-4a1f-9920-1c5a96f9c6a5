import { EVENT_ACTION_LABELS } from "./components/event-action/event-action.model";
import {
  getPendingModule,
  getIsPendingCollection,
  getIsModifyOrCancelEvent,
  latestUpdatedModule,
  isEventReturn,
  getIsPromoOnlyEditByMerchant,
  isEventStatusAgreedPrev,
} from "./event-progress-container-service";
import * as eventProgressContainerServiceUtils from "./event-progress-container-service";
import * as eventHookFormServiceUtils from "./event-hook-form-wrapper-service";
import { EEVENT_STATUS } from "@me/util-helpers";
import {
  getLoggedInUserType,
  isFeatureFlagEnabled as originalIsFeatureFlagEnabled,
} from "@me-upp-js/utilities";

const isFeatureFlagEnabled = jest.fn(originalIsFeatureFlagEnabled);

jest.mock("@me-upp-js/utilities", () => ({
  getLoggedInUserType: jest.fn(),
  isFeatureFlagEnabled: jest.fn(),
}));

jest.mock("moment", () => {
  const mockMoment = jest.fn(() => ({
    diff: jest.fn(),
    duration: jest.fn(),
    unix: jest.fn(),
  }));
  return mockMoment;
});
jest.mock("moment-timezone", () => ({
  tz: jest.fn(),
}));
jest.mock("./event-hook-form-wrapper-service", () => ({
  isPendingCollectionUser: jest.fn(),
}));

// Mock data for testing
const eventDetails = {
  eventDetailsEventInd: true,
  allowanceEventInd: false,
  promotionEventInd: false,
  planEventPending: {
    id: 1,
    name: "Test Plan",
    updateUser: {
      type: "Vendor",
    },
  },
  offerAllowances: [
    {
      id: 1,
      createTs: "2022-01-01T00:00:00Z",
      updateUser: "user1",
    },
    {
      id: 2,
      createTs: "2022-02-01T00:00:00Z",
      updateUser: {
        createTs: "2022-04-01T00:00:00Z",
        id: 2,
        updateUser: "user4",
      },
    },
  ],
  promotionsList: [
    {
      id: 1,
      createTs: "2022-03-01T00:00:00Z",
      updateUser: "user3",
    },
    {
      id: 2,
      createTs: "2022-04-01T00:00:00Z",
      updateUser: {
        createTs: "2022-04-01T00:00:00Z",
        id: 2,
        updateUser: "user4",
      },
    },
  ],
  eventType: "DP",
  updateUser: { createTs: "2022-04-01T00:00:00Z", id: 2, updateUser: "user4" },
  planEvent: {
    planEventWorkFlowType: "MODIFY",
  },
};

const eventDetailsWithAllowanceIndicator = {
  eventDetailsEventInd: false,
  allowanceEventInd: true,
  promotionEventInd: false,
  planEventPending: {
    id: 1,
    name: "Test Plan",
    updateUser: {
      type: "Vendor",
    },
    offerAllowances: [
      {
        id: 1,
        createTs: "2022-01-01T00:00:00Z",
        updateUser: { user: "user1", type: "Vendor" },
      },
      {
        id: 2,
        createTs: "2022-02-01T00:00:00Z",
        updateUser: {
          createTs: "2022-04-01T00:00:00Z",
          id: 2,
          type: "Vendor",
          updateUser: "user4",
        },
      },
    ],
  },

  promotionsList: [
    {
      id: 1,
      createTs: "2022-03-01T00:00:00Z",
      updateUser: "user3",
    },
    {
      id: 2,
      createTs: "2022-04-01T00:00:00Z",
      updateUser: {
        createTs: "2022-04-01T00:00:00Z",
        id: 2,
        updateUser: "user4",
      },
    },
  ],
  eventType: "DP",
  updateUser: { createTs: "2022-04-01T00:00:00Z", id: 2, updateUser: "user4" },
  planEvent: {
    planEventWorkFlowType: "MODIFY",
  },
};
const eventDetailsWithPromoIndicator = {
  eventDetailsEventInd: false,
  allowanceEventInd: false,
  promotionEventInd: true,
  planEventPending: {
    id: 1,
    name: "Test Plan",
    updateUser: {
      type: "Vendor",
    },
    promotionsList: [
      {
        id: 1,
        createTs: "2022-03-01T00:00:00Z",
        updateUser: {
          createTs: "2022-04-01T00:00:00Z",
          id: 2,
          type: "Vendor",
          updateUser: "user3",
        },
      },
      {
        id: 2,
        createTs: "2022-04-01T00:00:00Z",
        updateUser: {
          createTs: "2022-04-01T00:00:00Z",
          id: 2,
          type: "Vendor",
          updateUser: "user4",
        },
      },
    ],
  },
  offerAllowances: [
    {
      id: 1,
      createTs: "2022-01-01T00:00:00Z",
      updateUser: "user1",
    },
    {
      id: 2,
      createTs: "2022-02-01T00:00:00Z",
      updateUser: {
        createTs: "2022-04-01T00:00:00Z",
        id: 2,
        type: "Vendor",
        updateUser: "user4",
      },
    },
  ],

  eventType: "DP",
  updateUser: { createTs: "2022-04-01T00:00:00Z", id: 2, updateUser: "user4" },
  planEvent: {
    planEventWorkFlowType: "MODIFY",
  },
};
describe("getPendingModule", () => {
  test("returns an array of pending modules", () => {
    const eventIndicators = {
      EVENT: true,
      ALLOWANCE: false,
      PROMOTION: true,
    };
    expect(getPendingModule(eventIndicators)).toEqual(["EVENT", "PROMOTION"]);
  });

  test("returns an empty array if no modules are pending", () => {
    const eventIndicators = {
      EVENT: false,
      ALLOWANCE: false,
      PROMOTION: false,
    };
    expect(getPendingModule(eventIndicators)).toEqual([]);
  });
});

describe("getIsPendingCollection", () => {
  test("returns false if there are event pending collection", () => {
    expect(getIsPendingCollection(eventDetails)).toBeFalsy();
  });

  test("returns false if there are no pending collections", () => {
    const eventDetailsNoPendingCollections = {
      eventDetailsEventInd: false,
      allowanceEventInd: false,
      promotionEventInd: false,
      otherDetailsChangedInd: false,
      planEventPending: {
        id: 1,
        name: "Test Plan",
        updateUser: {
          type: "Vendor",
        },
      },
      offerAllowances: [
        {
          id: 1,
          createTs: "2022-01-01T00:00:00Z",
          updateUser: "user1",
        },
      ],
      promotionsList: [],
      eventType: "DP",
      updateUser: "user2",
      planEvent: {
        planEventWorkFlowType: "MODIFY",
        promotionsList: [],
        offerAllowances: [],
      },
    };
    expect(
      getIsPendingCollection(eventDetailsNoPendingCollections)
    ).toBeFalsy();
  });
});

describe("getIsModifyOrCancelEvent", () => {
  test("returns the event workflow type", () => {
    expect(getIsModifyOrCancelEvent(eventDetails)).toBe("MODIFY");
  });
});

describe("latestUpdatedModule", () => {
  test("returns the latest updated user", () => {
    expect(latestUpdatedModule(eventDetails)).toEqual({
      id: 2,
      createTs: "2022-04-01T00:00:00Z",
      updateUser: "user4",
    });
  });
});

describe("isEventReturn", () => {
  const createHistory = (type, labelFieldName) => [
    {
      createUser: { type },
      eventChanges: [{ labelFieldName }],
    },
  ];

  it("returns true for status update event with matching user type and key", () => {
    const history = createHistory(
      EVENT_ACTION_LABELS.STATUS_UPDATE_WITH_COMMENT,
      "someKey"
    );
    const userType = "UserType";
    const key = EVENT_ACTION_LABELS.STATUS_UPDATE_WITH_COMMENT;

    const result = isEventReturn([], history, userType, key, "status");

    expect(result).toBe(false);
  });

  it("returns false for return event with mismatching user type and key", () => {
    const history = createHistory(EVENT_ACTION_LABELS.RETURN, "someKey");
    const userType = "DifferentUserType";
    const key = EVENT_ACTION_LABELS.RETURN;

    const result = isEventReturn([], history, userType, key, "status");

    expect(result).toBe(false);
  });

  it("returns false when eventChanges is not an array", () => {
    const history = [{ createUser: { type: "someType" }, eventChanges: null }];
    const userType = "UserType";
    const key = EVENT_ACTION_LABELS.STATUS_UPDATE_WITH_COMMENT;

    const result = isEventReturn([], history, userType, key, "status");

    expect(result).toBe(false);
  });

  it("returns false when key is not found in eventChanges", () => {
    const history = createHistory(
      EVENT_ACTION_LABELS.STATUS_UPDATE_WITH_COMMENT,
      "anotherKey"
    );
    const userType = "UserType";
    const key = EVENT_ACTION_LABELS.STATUS_UPDATE_WITH_COMMENT;

    const result = isEventReturn([], history, userType, key, "status");

    expect(result).toBe(false);
  });

  it("returns false when returnChangeObj is not an object", () => {
    const history = createHistory(
      EVENT_ACTION_LABELS.STATUS_UPDATE_WITH_COMMENT,
      "someKey"
    );
    const userType = "UserType";
    const key = EVENT_ACTION_LABELS.STATUS_UPDATE_WITH_COMMENT;

    const result = isEventReturn([], history, userType, key, "status");

    expect(result).toBe(false);
  });
});

describe("getIsPendingCollection", () => {
  const mockEventDetails = {
    eventDetailsEventInd: true,
    allowanceEventInd: false,
    promotionEventInd: true,
    otherDetailsChangedInd: true,
    planEventPending: {
      /* Mocked planEventPending data here */
    },
    planEvent: {
      /* Mocked planEvent data here */
    },
  };

  it("returns false when there is a pending module for EVENT", () => {
    const result = getIsPendingCollection(mockEventDetails);

    // Assert that the result is false
    expect(result).toBeFalsy();
  });

  it("returns false when there is a pending module for PROMOTION", () => {
    // Update the mocked getPendingModule function
    const mockEventDetails = {
      eventDetailsEventInd: false,
      allowanceEventInd: false,
      promotionEventInd: true,
      otherDetailsChangedInd: true,
      planEventPending: {
        promotionsList: [{ id: "1" }],
      },
      planEvent: {
        /* Mocked planEvent data here */
      },
    };
    jest.mock("./event-progress-container-service", () => ({
      ...jest.requireActual("./event-progress-container-service"),
      getPendingModule: () => ["PROMOTION"],
    }));

    const result = getIsPendingCollection(mockEventDetails);

    // Assert that the result is false
    expect(result).toBeFalsy();
  });

  it("returns false when there is a pending module for ALLOWANCE", () => {
    // Update the mocked getPendingModule function
    const mockEventDetails = {
      eventDetailsEventInd: true,
      allowanceEventInd: false,
      promotionEventInd: true,
      otherDetailsChangedInd: true,
      planEventPending: {
        promotionsList: [{ id: "1" }],
      },
      planEvent: {
        /* Mocked planEvent data here */
      },
    };
    jest.mock("./event-progress-container-service", () => ({
      ...jest.requireActual("./event-progress-container-service"),
      getPendingModule: () => ["ALLOWANCE"],
    }));

    const result = getIsPendingCollection(mockEventDetails);

    // Assert that the result is false
    expect(result).toBeFalsy();
  });

  it("returns false when there is no pending module and no draft record", () => {
    // Update the mocked getPendingModule function
    const mockEventDetailsData = {
      eventDetailsEventInd: true,
      otherDetailsChangedInd: true,
      allowanceEventInd: false,
      promotionEventInd: true,
      planEventPending: {
        promotionsList: [{ somedata: "test" }],
      },
      planEvent: {
        /* Mocked planEvent data here */
      },
    };
    jest.mock("./event-progress-container-service", () => ({
      ...jest.requireActual("./event-progress-container-service"),
      getPendingModule: jest.fn(() => []),
      loggedInUserDraftCollection: jest.fn(() => false),
    }));

    const result = getIsPendingCollection(mockEventDetailsData);

    // Assert that the result is false
    expect(result).toBeFalsy();
  });
});

describe("isEventStatusAgreedPrev", () => {
  jest
    .spyOn(eventProgressContainerServiceUtils, "isEventStatusAgreedPrev")
    .mockImplementationOnce(() => {
      return {
        isAgreed: false,
        userType: "NA",
      };
    });
  it("returns false when eventHistory is undefined", () => {
    const { isAgreed, userType } = isEventStatusAgreedPrev(
      undefined,
      "eventStatus"
    );
    expect(isAgreed).toEqual(false);
  });

  it("returns false when eventHistory is an empty array", () => {
    const { isAgreed, userType } = isEventStatusAgreedPrev([], "eventStatus");
    expect(isAgreed).toEqual(false);
  });

  it("returns false when the last object in eventHistory does not contain eventChanges", () => {
    const eventHistory = [{}];
    const { isAgreed, userType } = isEventStatusAgreedPrev(
      eventHistory,
      "eventStatus"
    );
    expect(isAgreed).toEqual(false);
  });

  it("returns false when eventChanges does not contain an object with labelFieldName equal to eventStatus", () => {
    const eventHistory = [
      { eventChanges: [{ labelFieldName: "eventStatus" }] },
    ];
    const { isAgreed, userType } = isEventStatusAgreedPrev(
      eventHistory,
      "eventStatus"
    );
    expect(isAgreed).toEqual(false);
  });

  it("returns true when eventChanges contains an object with labelFieldName equal to eventStatus and beforeVal equal to AGREED", () => {
    const eventHistory = [
      {
        eventChanges: [
          { labelFieldName: "eventStatus", beforeVal: EEVENT_STATUS.AGREED },
        ],
      },
    ];
    const { isAgreed } = isEventStatusAgreedPrev(eventHistory, "eventStatus");
    expect(isAgreed).toEqual(true);
  });
});

describe("isEventSendBackWtComment", () => {
  test("should return false if eventHistory is empty", () => {
    const result = eventProgressContainerServiceUtils.isEventSendBackWtComment(
      [],
      EVENT_ACTION_LABELS.STATUS_UPDATE_WITH_COMMENT
    );
    expect(result).toBe(false);
  });

  test("should return false if eventHistory does not contain the specified key", () => {
    const eventHistory = [
      {
        eventChanges: [{ labelFieldName: "eventStatus", beforeVal: "Agreed" }],
      },
      {
        eventChanges: [
          { labelFieldName: "eventStatus", beforeVal: "Agreed-Pending" },
        ],
      },
    ];
    const result = eventProgressContainerServiceUtils.isEventSendBackWtComment(
      eventHistory,
      EVENT_ACTION_LABELS.STATUS_UPDATE_WITH_COMMENT
    );
    expect(result).toBe(false);
  });

  test("should return the beforeVal if eventHistory contains the specified key and beforeVal is truthy", () => {
    const eventHistory = [
      {
        eventChanges: [
          {
            labelFieldName: "planevents.isStatusUpdateWithComment",
            beforeVal: true,
          },
        ],
      },
    ];
    const result = eventProgressContainerServiceUtils.isEventSendBackWtComment(
      eventHistory,
      EVENT_ACTION_LABELS.STATUS_UPDATE_WITH_COMMENT
    );
    expect(result).toBe(true);
  });

  test("should return the beforeVal if eventHistory contains the specified key and beforeVal is falsy", () => {
    const eventHistory = [
      {
        eventChanges: [
          {
            labelFieldName: "planevents.isStatusUpdateWithComment",
            beforeVal: false,
          },
        ],
      },
    ];
    const result = eventProgressContainerServiceUtils.isEventSendBackWtComment(
      eventHistory,
      EVENT_ACTION_LABELS.STATUS_UPDATE_WITH_COMMENT
    );
    expect(result).toBe(false);
  });
});

describe("filterHistory", () => {
  const key = EVENT_ACTION_LABELS.STATUS_UPDATE_WITH_COMMENT;
  it("should return false if no event changes match the key", () => {
    const eventHistory = [
      {
        eventChanges: [
          { labelFieldName: "eventStatus", beforeVal: "Agreed-Pending" },
        ],
      },
      {
        eventChanges: [
          {
            labelFieldName: "eventStatus",
            beforeVal: "Agreed",
          },
        ],
      },
    ];

    expect(
      eventProgressContainerServiceUtils.filterHistory(eventHistory, key)
    ).toBe(false);
  });

  it("should return true if all events after the matched key are created by VENDOR", () => {
    const eventHistory = [
      {
        eventChanges: [
          {
            labelFieldName: "eventStatus",
            beforeVal: "Agreed",
            AfterVal: "Agreed-Pending",
          },
        ],
        createUser: { type: "Merchant" },
      },
      {
        eventChanges: [
          {
            labelFieldName: "planevents.isStatusUpdateWithComment",
            beforeVal: false,
            AfterVal: true,
          },
        ],
        createUser: { type: "Vendor" },
      },
      {
        eventChanges: [
          {
            labelFieldName: "eventStatus",
            beforeVal: "Agreed-Pending",
            AfterVal: "Agreed-Pending",
          },
        ],
        createUser: { type: "Vendor" },
      },
    ];
    expect(
      eventProgressContainerServiceUtils.filterHistory(eventHistory, key)
    ).toBe(true);
  });

  it("should return false if any event after the matched key is not created by VENDOR", () => {
    const eventHistory = [
      {
        eventChanges: [
          { labelFieldName: "eventStatus", AfterVal: "Agreed-Pending" },
        ],
        createUser: { type: "Merchant" },
      },
      {
        eventChanges: [
          {
            labelFieldName: "planevents.isStatusUpdateWithComment",
            beforeVal: false,
            AfterVal: true,
          },
        ],
        createUser: { type: "Merchant" },
      },
      {
        eventChanges: [{ labelFieldName: "eventStatus", AfterVal: "Agreed" }],
        createUser: { type: "Merchant" },
      },
    ];

    expect(
      eventProgressContainerServiceUtils.filterHistory(eventHistory, key)
    ).toBe(false);
  });

  it("should handle empty eventHistory gracefully", () => {
    const eventHistory = [];
    const key = "eventStatus";
    expect(
      eventProgressContainerServiceUtils.filterHistory(eventHistory, key)
    ).toBe(false);
  });
});

describe("isEventReturnOnAgreedPending", () => {
  it("should return true when eventIndicators has true, returnChangeObj exists, and userRole matches type", () => {
    const eventIndicator = true,
      offerindicator = false,
      promoIndicator = true;
    const eventIndicators = [eventIndicator, offerindicator, promoIndicator];
    const eventHistory = [
      {
        createUser: { type: "Vendor" },
        eventChanges: [{ labelFieldName: "isReturnEvent", afterVal: "true" }],
      },
    ];
    const userRole = "Vendor";
    const key = "isReturnEvent";
    const eventStatus = "Agreed-Pending";

    const { isReturnEvent, isReturnByCurrUser } =
      eventProgressContainerServiceUtils.isEventReturnOnAgreedPending(
        eventIndicators,
        eventHistory,
        userRole,
        key,
        eventStatus
      );
    expect(isReturnEvent).toBe(true);
    expect(isReturnByCurrUser).toBe(true);
  });

  it("should return false when eventIndicators has no true values", () => {
    const eventIndicator = false,
      offerindicator = false,
      promoIndicator = false;
    const eventIndicators = [eventIndicator, offerindicator, promoIndicator];
    const eventHistory = [
      {
        createUser: { type: "Merchant" },
        eventChanges: [{ labelFieldName: "eventStatus" }],
      },
    ];
    const userRole = "Vendor";
    const key = "isReturnEvent";
    const eventStatus = "Agreed-Pending";

    const { isReturnEvent, isReturnByCurrUser } =
      eventProgressContainerServiceUtils.isEventReturnOnAgreedPending(
        eventIndicators,
        eventHistory,
        userRole,
        key,
        eventStatus
      );
    expect(isReturnEvent).toBe(false);
    expect(isReturnByCurrUser).toBe(false);
  });

  it("should return false when eventHistory is empty", () => {
    const eventIndicator = false,
      offerindicator = false,
      promoIndicator = false;
    const eventIndicators = [eventIndicator, offerindicator, promoIndicator];
    const eventHistory = [];
    const userRole = "Merchant";
    const key = "isReturnEvent";
    const eventStatus = "Agreed-Pending";

    const { isReturnEvent, isReturnByCurrUser } =
      eventProgressContainerServiceUtils.isEventReturnOnAgreedPending(
        eventIndicators,
        eventHistory,
        userRole,
        key,
        eventStatus
      );
    expect(isReturnEvent).toBe(false);
    expect(isReturnByCurrUser).toBe(false);
  });

  it("should return false when userRole does not match type", () => {
    const eventIndicator = false,
      offerindicator = false,
      promoIndicator = false;
    const eventIndicators = [eventIndicator, offerindicator, promoIndicator];
    const eventHistory = [
      {
        createUser: { type: "Merchant" },
        eventChanges: [{ labelFieldName: "eventStatus" }],
      },
    ];
    const userRole = "Vendor";
    const key = "isReturnEvent";
    const eventStatus = "Agreed-Pending";

    const { isReturnEvent, isReturnByCurrUser } =
      eventProgressContainerServiceUtils.isEventReturnOnAgreedPending(
        eventIndicators,
        eventHistory,
        userRole,
        key,
        eventStatus
      );
    expect(isReturnEvent).toBe(false);
    expect(isReturnByCurrUser).toBe(false);
  });

  it("should return false when key does not match labelFieldName", () => {
    const eventIndicator = false,
      offerindicator = false,
      promoIndicator = false;
    const eventIndicators = [eventIndicator, offerindicator, promoIndicator];
    const eventHistory = [
      {
        createUser: { type: "Vendor" },
        eventChanges: [{ labelFieldName: "eventStatus" }],
      },
    ];
    const userRole = "Vendor";
    const key = "isReturnEvent";
    const eventStatus = "Agreed-Pending";

    const { isReturnEvent, isReturnByCurrUser } =
      eventProgressContainerServiceUtils.isEventReturnOnAgreedPending(
        eventIndicators,
        eventHistory,
        userRole,
        key,
        eventStatus
      );
    expect(isReturnEvent).toBe(false);
    expect(isReturnByCurrUser).toBe(false);
  });

  it("should return false when returnChangeObj is empty", () => {
    const eventIndicator = false,
      offerindicator = false,
      promoIndicator = true;
    const eventIndicators = [eventIndicator, offerindicator, promoIndicator];
    const eventHistory = [
      {
        createUser: { type: "Vendor" },
        eventChanges: [{}],
      },
    ];
    const userRole = "Vendor";
    const key = "isReturnEvent";
    const eventStatus = "Agreed-Pending";

    const { isReturnEvent, isReturnByCurrUser } =
      eventProgressContainerServiceUtils.isEventReturnOnAgreedPending(
        eventIndicators,
        eventHistory,
        userRole,
        key,
        eventStatus
      );
    expect(isReturnEvent).toBe(false);
    expect(isReturnByCurrUser).toBe(false);
  });
});

describe("event-progress-container-service", () => {
  beforeEach(() => {
    // Reset the mock before each test
    (isFeatureFlagEnabled as jest.Mock).mockReset();
  });
  describe("getIsPendingCollection", () => {
    it("should return true if there is a pending module and user is a pending collection user", () => {
      const eventDetails = {
        eventDetailsEventInd: true,
        allowanceEventInd: false,
        promotionEventInd: false,
        otherDetailsChangedInd: true,
        planEventPending: { someData: "data" },
        planEvent: {},
      };
      isFeatureFlagEnabled.mockReturnValue(true);

      (getLoggedInUserType as jest.Mock).mockReturnValue("MERCHANT");
      (
        eventHookFormServiceUtils.isPendingCollectionUser as jest.Mock
      ).mockReturnValue(true);

      const result = getIsPendingCollection(eventDetails);

      expect(result).toBe(true);
      expect(
        eventHookFormServiceUtils.isPendingCollectionUser
      ).toHaveBeenCalledWith({
        someData: "data",
      });
    });

    it("should return false if there is no pending module and no draft record present", () => {
      const eventDetails = {
        eventDetailsEventInd: false,
        allowanceEventInd: false,
        promotionEventInd: false,
        planEventPending: {},
        planEvent: {},
      };
      isFeatureFlagEnabled.mockReturnValue(true);
      (getLoggedInUserType as jest.Mock).mockReturnValue("MERCHANT");

      const result = getIsPendingCollection(eventDetails);

      expect(result).toBeFalsy();
    });
  });

  describe("isReturnOrLastUpdatedByCurrUser", () => {
    it("should return correct values when there is a return event and it is by the current user", () => {
      const eventDetailsWtPlanPending = {
        planEvent: {},
        planEventPending: {},
        planEventHistory: [
          { createUser: { type: "MERCHANT" }, eventChanges: [] },
          {
            createUser: { type: "MERCHANT" },
            eventChanges: [{ labelFieldName: "isReturnEvent" }],
          },
        ],
        allowanceEventInd: false,
        eventDetailsEventInd: false,
        promotionEventInd: false,
      };
      isFeatureFlagEnabled.mockReturnValue(true);
      (
        eventHookFormServiceUtils.isPendingCollectionUser as jest.Mock
      ).mockReturnValue(true);

      const result =
        eventProgressContainerServiceUtils.isReturnOrLastUpdatedByCurrUser(
          "MERCHANT",
          eventDetailsWtPlanPending
        );

      expect(result).toEqual({
        isReturnEvent: true,
        isReturnEventByCurrUser: true,
        isLastUpdatedByCurrUser: true,
      });
    });

    it("should return correct values when there is no return event", () => {
      const eventDetailsWtPlanPending = {
        planEvent: {},
        planEventPending: {},
        planEventHistory: [
          { createUser: { type: "MERCHANT" }, eventChanges: [] },
        ],
        allowanceEventInd: false,
        eventDetailsEventInd: false,
        promotionEventInd: false,
      };
      isFeatureFlagEnabled.mockReturnValue(true);
      (
        eventHookFormServiceUtils.isPendingCollectionUser as jest.Mock
      ).mockReturnValue(false);

      const result =
        eventProgressContainerServiceUtils.isReturnOrLastUpdatedByCurrUser(
          "MERCHANT",
          eventDetailsWtPlanPending
        );

      expect(result).toEqual({
        isReturnEvent: false,
        isReturnEventByCurrUser: false,
        isLastUpdatedByCurrUser: false,
      });
    });
  });
});
