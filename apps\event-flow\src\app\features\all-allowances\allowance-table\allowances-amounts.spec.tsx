import { fireEvent, render, screen } from "@testing-library/react";
import AllowancesAmounts from "./allowances-amounts";
import * as selectors from "@me/data-rtk";
import "@testing-library/jest-dom";
import * as allowanceHelper from "../all-allowances-container/text-mapping";

jest.mock("react-redux", () => ({
  ...jest.requireActual("react-redux"),
  useDispatch: jest.fn().mockImplementation(() => {
    return jest.fn(() => null);
  }),
}));

describe("AllowancesAmounts", () => {
  jest.mock("../all-allowances-container/text-mapping", () => ({
    getAllowTypeConfig: jest.fn().mockReturnValue({
      groupText: "",
      newCostCalculateKey: "unitCostAllow",
      allowanceTypeDisplay: "Scan",
      description:
        "You can update Allowance amounts for items together or individually.",
    }),
  }));
  const rowData = {
    itemId: "123",
    vendorDetails: [
      {
        vendorNbr: "456",
        ALLOWANCE_AMT_INPUT_KEY: "2",
        ALLOWANCE_UOM_KEY: "EA",
      },
    ],
    rowIndex: 0,
  };
  const warehouseIndex = 0;
  const type = "";

  beforeEach(() => {
    jest.spyOn(allowanceHelper, "textMapping").mockReturnValue({
      customAmtWidth: 0,
      isShowUOMOption: false,
      isSkipLessThanAmtValidation: true,
    });
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      switch (sel_name) {
        case "emptyFields_rn":
          return {
            data: {},
          };
        case "selectedSwitchValue_rn":
          return {
            data: {
              selectedSwitch: "Case",
            },
          };
        case "excludedVendorForAllowance_rn":
          return {
            data: {
              0: { vendorNbr: "123", isExclude: true },
              excludedVendors: ["123"],
            },
          };
        case "allowanceTableData_rn": {
          return {
            data: {
              tableData: [{ vendorDetails: [{ unitListCost: 12 }] }],
            },
          };
        }
        case "allowance_temp_work": {
          return {
            data: {
              allowanceType: "SCAN",
            },
          };
        }
        default:
          break;
      }
    });
  });
  xit("should render component with inputs and select", () => {
    render(
      <AllowancesAmounts
        rowData={rowData}
        warehouseIndex={warehouseIndex}
        type={type}
      />
    );
    const input = screen.getByTestId("uom-text");
    fireEvent.blur(input);
    expect(input).toBeInTheDocument();
  });
  xit("should render component with inputs and select of byItem type", () => {
    render(
      <AllowancesAmounts
        rowData={rowData}
        warehouseIndex={warehouseIndex}
        type={"byItem"}
      />
    );
    const input = screen.getByTestId("uom-text");
    fireEvent.blur(input);
    expect(input).toBeInTheDocument();
  });
  xit("should render component with inputs and select of byDistributor type", () => {
    render(
      <AllowancesAmounts
        rowData={rowData}
        warehouseIndex={warehouseIndex}
        type={"byDistributor"}
      />
    );
    const input = screen.getByTestId("uom-text");
    fireEvent.blur(input);
    expect(input).toBeInTheDocument();
  });
  xit("should render component with inputs and select with no type", () => {
    render(
      <AllowancesAmounts
        rowData={rowData}
        warehouseIndex={warehouseIndex}
        type={undefined}
      />
    );
    const input = screen.getByTestId("uom-text");
    fireEvent.blur(input);
    expect(input).toBeInTheDocument();
  });
});
