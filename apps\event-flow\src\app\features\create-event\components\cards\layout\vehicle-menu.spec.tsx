import { Mocked<PERSON>rovider } from "@apollo/client/testing";
import { app_store } from "@me/data-rtk";
import { render, screen } from "@testing-library/react";
import * as selectors from "@me/data-rtk";
import { FormProvider, useForm } from "react-hook-form";
import { Provider } from "react-redux";
import VehicleMenu from "./vehicle-menu";
// import * as vehicleTypeService from "apps/event-flow/src/app/graphql/generated/schema";
import userEvent from "@testing-library/user-event";

const Wrapper = props => {
  const formMethods = useForm<any>();
  return (
    <Provider store={app_store}>
      <FormProvider {...formMethods}>{props.children}</FormProvider>
    </Provider>
  );
};

const mockProps1 = {
  onChange: jest.fn(),
  onClose: jest.fn(),
  setTimeframeEnd: jest.fn(),
  setTimeframeStart: jest.fn(),
  minDate: "2023-03-01T21:33:25.308Z",
  fieldProps: {
    label: "Vehicle Type/Custom Date",
    required: true,
    resetValue: {},
    registerField: "eventCreationVehicle.vehicleType.vehicleTypeId",
    type: "select",
    options: [
      {
        name: "Division1",
        id: 1,
      },
      {
        name: "Division2",
        id: 2,
      },
      {
        name: "Division3",
        id: 3,
      },
    ],
    error: {
      required: {
        backgroundColor: "",
        text: "Vehicle Type/Custom Date Required",
      },
    },
  },
};

describe("Vehicle Menu Test Suite", () => {
  beforeEach(() => {
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      switch (sel_name) {
        case "event_details_data": {
          return {
            data: {
              name: "Annies Homegrown Snacks Box - 84882 - S - 9 - 27 Week 50 Insert 2023",
              startDate: "2023-12-13",
            },
          };
        }
        case "event_type_and_divisions": {
          return {
            data: {
              eventTypeAndDivisionsData: [],
            },
          };
        }
        default:
          break;
      }
    });
  });

  beforeEach(() => {
    window.HTMLElement.prototype.scrollIntoView = jest.fn();
  });
  it("should render Vehicle Menu component with selectedType as CustomDate and show being true", async () => {
    const user = userEvent.setup();
    const { baseElement } = render(
      <Wrapper>
        <MockedProvider>
          <VehicleMenu
            show={true}
            optionValue={{
              name: "TEST",
              value: "TEST VALUE",
              selectedType: "CustomDate",
            }}
            {...mockProps1}
          ></VehicleMenu>
        </MockedProvider>
      </Wrapper>
    );

    await user.click(screen.getByText("Custom Date"));
    expect(baseElement).toBeTruthy();
  });

  it("should render Vehicle Menu component with selectedType as Vehicle and show being false", () => {
    const { baseElement } = render(
      <Wrapper>
        <MockedProvider>
          <VehicleMenu
            show={false}
            optionValue={{
              name: "TEST",
              value: "TEST VALUE",
              selectedType: "Vehicle",
            }}
            {...mockProps1}
          ></VehicleMenu>
        </MockedProvider>
      </Wrapper>
    );
    expect(baseElement).toBeTruthy();
  });
});
