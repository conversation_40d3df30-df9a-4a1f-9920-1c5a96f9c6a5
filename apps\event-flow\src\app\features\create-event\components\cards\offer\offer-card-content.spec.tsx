import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import OfferCardContent from './offer-card-content';
import OfferSections from './offer-sections';

jest.mock('./offer-sections', () => (props: any) => (
  <div data-testid="offer-sections-mock">
    <span data-testid="card-index">{props.cardIndex}</span>
    <span data-testid="card-item-index">{props.cardItemIndex}</span>
    <span data-testid="is-edit-enable">{props.isEditEnable.toString()}</span>
  </div>
));

describe('OfferCardContent Component', () => {
  const renderComponent = (props: Partial<React.ComponentProps<typeof OfferCardContent>> = {}) => {
    const defaultProps = {
      cardIndex: 0,
      cardItemIndex: 0,
      isEditEnable: false,
    };
    return render(<OfferCardContent {...defaultProps} {...props} />);
  };

  it('should render without crashing', () => {
    renderComponent();
    expect(screen.getByTestId('offer-sections-mock')).toBeInTheDocument();
  });

  it('should pass correct props to OfferSections component', () => {
    const testProps = {
      cardIndex: 2,
      cardItemIndex: 3,
      isEditEnable: true,
    };
    renderComponent(testProps);

    expect(screen.getByTestId('card-index')).toHaveTextContent('2');
    expect(screen.getByTestId('card-item-index')).toHaveTextContent('3');
    expect(screen.getByTestId('is-edit-enable')).toHaveTextContent('true');
  });

  it('should use default props when not provided', () => {
    renderComponent();
    expect(screen.getByTestId('card-index')).toHaveTextContent('0');
    expect(screen.getByTestId('card-item-index')).toHaveTextContent('0');
    expect(screen.getByTestId('is-edit-enable')).toHaveTextContent('false');
  });

  it('should render OfferSections component only once', () => {
    renderComponent();
    const sections = screen.getAllByTestId('offer-sections-mock');
    expect(sections).toHaveLength(1);
  });

  it('should maintain prop types correctly', () => {
    renderComponent({
      cardIndex: 1,
      cardItemIndex: 1,
      isEditEnable: true,
    });
    expect(screen.getByTestId('card-index')).toHaveTextContent('1');
  });

  describe('Default Prop Values', () => {
    it('should use default cardIndex (0) when not provided', () => {
      render(<OfferCardContent cardItemIndex={0} isEditEnable={false} />);
      expect(screen.getByTestId('card-index')).toHaveTextContent('0');
    });

    it('should use default cardItemIndex (0) when not provided', () => {
      render(<OfferCardContent cardIndex={0} isEditEnable={false} />);
      expect(screen.getByTestId('card-item-index')).toHaveTextContent('0');
    });

    it('should use default isEditEnable (false) when not provided', () => {
      render(<OfferCardContent cardIndex={0} cardItemIndex={0} />);
      expect(screen.getByTestId('is-edit-enable')).toHaveTextContent('false');
    });

    it('should use all default values when no props are provided', () => {
      render(<OfferCardContent />);
      expect(screen.getByTestId('card-index')).toHaveTextContent('0');
      expect(screen.getByTestId('card-item-index')).toHaveTextContent('0');
      expect(screen.getByTestId('is-edit-enable')).toHaveTextContent('false');
    });
  });
});