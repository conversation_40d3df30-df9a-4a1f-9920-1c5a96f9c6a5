import { useDispatch } from "react-redux";
import { useSelectorWrap } from "@me/data-rtk";
import { setNationalDivisionsConfig } from "../../slices/national-main-entry-slices";
import Divider from "@albertsons/uds/molecule/Divider";
import { Tooltip } from "@albertsons/uds/molecule/Tooltip";
import { useMemo } from "react";
import React from "react";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import { SharedWhseIcon } from "@me-upp-js/utilities";

function DivisionsList() {
  const dispatch = useDispatch();

  // 🟢 Fetch Redux store data safely
  const nationalDivisionConfig =
    useSelectorWrap("national_divisions_config")?.data || {};
  const {
    data: { allDivisionsTableData },
  } = useSelectorWrap("allowanceTableData_rn");
  const { data: allDivisionsWarningData } =
    useSelectorWrap("all_divisions_warning_data") || {};
  const {
    data: { allowanceData: allowanceTempWorkData = [] },
  } = useSelectorWrap("allowance_temp_work");
  const { data: divisionWiseShrdWhse } = useSelectorWrap(
    "division_wise_shared_whse_data_rn"
  );

  // 🟢 Extract relevant data with defaults
  const { selectedDivisionData = {}, divisionsList = [] } =
    nationalDivisionConfig || {};
  const { divisionErrObj = {} } = allDivisionsWarningData || {};
  const allowanceType = useMemo(() => {
    if (Array.isArray(allowanceTempWorkData) && allowanceTempWorkData?.length) {
      return allowanceTempWorkData[0]?.allowanceType || null;
    }
    return null;
  }, [allowanceTempWorkData]);

  const isHf = useMemo(
    () => allowanceType === efConstants.ALLOWANCE_TYPES.HEADERFLAT.key,
    [allowanceType]
  );

  // 🟢 Handle division selection
  const onSelectDivision = divisionObj => {
    if (divisionObj) {
      dispatch(
        setNationalDivisionsConfig({ selectedDivisionData: divisionObj })
      );
    }
  };

  // 🟢 Utility functions for styling and tooltips
  const isSelectedDivision = division =>
    selectedDivisionData?.divisionId === division?.divisionId;

  const getTooltipMsg = divisionId => {
    const isWarning = !isHf && checkDivisionWarning(divisionId);
    return divisionErrObj?.[divisionId]?.isError
      ? "Click here to review/resolve errors"
      : isWarning
      ? "Click here to review/resolve warnings"
      : "";
  };
  const renderShrdWhse = () => {
    return divisionWiseShrdWhse?.find(
      shrdWhseData =>
        shrdWhseData?.divisionId === selectedDivisionData.divisionId &&
        shrdWhseData?.isAnyShrdWhse
    );
  };

  const getDivisionTextColor = divisionId => {
    const isWarning = !isHf && checkDivisionWarning(divisionId);
    return divisionErrObj?.[divisionId]?.isError
      ? "text-[#bf2912]"
      : isWarning
      ? "text-[#C77D14]"
      : "";
  };

  // 🟢 Render division label
  const renderDivisionLabel = division => (
    <span
      className={
        isSelectedDivision(division)
          ? "underline decoration-[#3997ef] decoration-4 underline-offset-4 text-[#3997ef] font-bold"
          : ""
      }
    >
      {`${division?.divisionId} - ${division?.divisionName}`}
    </span>
  );

  const checkDivisionWarning = useMemo(() => {
    return divisionId => {
      const divisionData = allDivisionsTableData?.find(
        divObj => divObj?.divisionId === divisionId
      );
      const hasWarning = divisionData?.tableData?.some(rowData =>
        rowData?.vendorDetails?.some(
          vendor => vendor?.newCaseCostAllow < 0 || vendor?.newUnitCostAllow < 0
        )
      );
      return hasWarning;
    };
  }, [JSON.stringify(allDivisionsTableData)]);

  // 🟢 Render individual division item
  const renderDivisionItem = division => {
    const tooltipMessage = getTooltipMsg(division?.divisionId);
    const divisionLabel = renderDivisionLabel(division);
    const showShrdWhse = divisionWiseShrdWhse?.find(
      (shrdWhseData: any) =>
        shrdWhseData?.divisionId === division?.divisionId &&
        shrdWhseData?.isAnyShrdWhse
    );
    const shrdWhseIcon = showShrdWhse && (
      <SharedWhseIcon customClass="mt-1 font-bold" />
    );
    return (
      <div
        key={division?.divisionId}
        className={`flex cursor-pointer ${getDivisionTextColor(
          division?.divisionId
        )}`}
        onClick={() => onSelectDivision(division)}
      >
        {tooltipMessage ? (
          <Tooltip zIndex={10} label={tooltipMessage}>
            <span className="flex gap-1">
              {divisionLabel}
              {shrdWhseIcon}
            </span>
          </Tooltip>
        ) : (
          <span className="flex gap-1">
            {divisionLabel}
            {shrdWhseIcon}
          </span>
        )}
      </div>
    );
  };

  return (
    <>
      <section
        id="abs-nationals-main-entry-divisions-list"
        className="flex px-[20px] pb-3"
      >
        {divisionsList?.map((division, index) => (
          <div
            key={`division-container-${division?.divisionId}`}
            className="flex items-center"
          >
            {renderDivisionItem(division)}
            {index !== divisionsList?.length - 1 && (
              <Divider className="mx-2" height={24} color="#C8DAEB" />
            )}
          </div>
        ))}
      </section>
      <section className="flex px-[20px] pb-3">
        {renderShrdWhse() && (
          <div className="text-[#A0491C] text-base font-semibod flex gap-1 my-2">
            {<SharedWhseIcon customClass="mt-1 font-bold" />}
            {efConstants?.SHARED_WAREHSE_TEXT}
          </div>
        )}
      </section>
    </>
  );
}

export default React.memo(DivisionsList);
