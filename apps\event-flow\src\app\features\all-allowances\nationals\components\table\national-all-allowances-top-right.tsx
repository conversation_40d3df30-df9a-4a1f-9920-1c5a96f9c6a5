import { useSelectorWrap } from "@me/data-rtk";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import { useEffect, useState } from "react";
import {
  getAllowAmtLabel,
  getAllowanceMapKey,
  getQueryParams,
} from "../../../../create-event/service/allowance/allowance-service";
import {
  isInValidLeadDists,
  isNationalType,
  validateIfAllOtherVendorsAssignedLeads,
} from "../../../allowance-lead-distributors/billing-selection-utils";
import { useGetPlanEventDataQuery } from "../../../../create-event/service/apis/event-api";
import {
  useDeleteNationalAllowanceTempWorkDataMutation,
  usePutNationalOfferUpdateAllowanceMutation,
} from "../../../../create-event/service/apis/allowance-api";
import { useGetAppBasePath } from "@me/util-helpers";
import { useDispatch } from "react-redux";
import {
  allowanceProductSources,
  offerCardConfiguration,
  resetIsOfferSectionUpdated,
  resetOfferAmountsData,
  resetOfferSectionsData,
  resetOfferSectionsEnableConfig,
  setIsOfferSectionUpdated,
  setOfferAmontsData,
} from "../../../../create-event/service/slice/allowance-details-slice";
import {
  allowanceTempWorkHandler,
  allowanceTempWorkReset,
} from "../../../../create-event/service/slice/allowance-temp-work-slice";
import { checkForEmptyFieldsDivisionWise, resetSlicesForMainEntry } from "../../service/table-service";
import { appConstants } from "@me/utils-root-props";
import { isComingFromtask } from "@me-upp-js/utilities";
import {
  check_isAnyColumnAllZero,
  generateAllowanceTempObjectForAllDiv,
} from "../../../allowances-table-service";
import { setIsLeadDistributorError } from "../../../../create-event/service/slice/lead-distributors-slice";
import { setSelectedFilters } from "@me/feature-primary-filters";
import Alert from "@albertsons/uds/molecule/Alert";
import Button from "@albertsons/uds/molecule/Button";
import { CommonModal } from "../../../../create-event/components/cards";
import { RenderStates } from "@me/ui-render-states";
import _ from "lodash";
import { useNavigate } from "react-router-dom";
import useNationalAllowTempUpdate from "../../../../create-event/hooks/useNationalAllowTempUpdate";
import { isEmpty } from 'lodash';
import { compareDivisionIdFn } from "../../../../create-event/components/cards/offer/offer-service";

export default function NationalAllAllowancesTopRight({ offerId, isDsd }) {
  const { OFFER_POPUP_MESSAGES } = efConstants;
  const [isDisplayFieldAlert, setIsDisplayFieldAlert] = useState(false);
  const [isAmountsError, setIsAmountsError] = useState(false);
  const queryString = window.location.search;
  const urlParams = new URLSearchParams(queryString);
  const isEditEnable: boolean = urlParams.get("isEdit") === "true";
  const isAllowConvEnable: boolean =
    urlParams.get("isAllowConvEnable") === "true";
  const isAllowanceChanged: boolean =
    urlParams.get("isAllowTypeChange") === "true";
  const eventId = urlParams.get("eventId");
  const offerGroup = urlParams.get("group") || "";
  const { data: switchValue } = useSelectorWrap("selectedSwitchValue_rn");
  const { data: allDivisionAllowancesRespCopy } = useSelectorWrap(
    "all_divisions_allowancesRespCopy_rn"
  );
  const { data: all_divisions_warning_data } = useSelectorWrap(
    "all_divisions_warning_data"
  );
  const { isAdditionalDatesChanged = {}, offerAmounts = {} } =
    useSelectorWrap("offer_amounts_details")?.data || {};
  const { selectedDivisionData = {}, isNdpType = false, divisionsList = [] } =
    useSelectorWrap("national_divisions_config")?.data || {};
  const { taskType } = getQueryParams();
  const isNational = isNationalType(isNdpType);
  const [inValidOtherVendorError, setInValidOtherVendorError] = useState(false);
  const {
    data: eventDataResp,
    isFetching: isEventDataFetched,
    refetch,
  } = useGetPlanEventDataQuery(eventId, {
    skip: !eventId,
  });
  const {
      data: { tableData, allDivisionsTableData },
    } = useSelectorWrap("allowanceTableData_rn");
  const {
    data: { leadDistributorsMode },
  } = useSelectorWrap("leadDistributorsMode_rn");
  const allowanceTempWorkData = useSelectorWrap("allowance_temp_work")?.data
    ?.allowanceData;

  const {
    saveAllowanceAmountSectionData,
    isLoading: isNationalTempLoading,
    isSuccess: isSaveSuccess,
    isError: isNationalTempError,
    error: nationalTempError,
  }: any = useNationalAllowTempUpdate();

  const currentTempData = allowanceTempWorkData && Array.isArray(allowanceTempWorkData)
    ? allowanceTempWorkData?.find(
        e => e?.divisionId === selectedDivisionData?.divisionId
      )
    : allowanceTempWorkData;

  const [putNationalOfferAllowance] = usePutNationalOfferUpdateAllowanceMutation();
  const [deleteNationalAllowanceTempWorkData] = useDeleteNationalAllowanceTempWorkDataMutation();
  const { isAllowanceTypeChanged } = useSelectorWrap(
    "allow_type_change_rn"
  ).data;
  const { data: allowGroupConfigObj } = useSelectorWrap(
      "allowanceTableColsGroupConfig_rn"
    ),
    allowName = getAllowanceMapKey(currentTempData?.allowanceType) || "",
    offerNumber =
      currentTempData?.allowanceTypeSpecification?.[allowName]?.offerNumber ||
      currentTempData?.offerNumber ||
      "";
  const { basePath } = useGetAppBasePath();
  const dispatch = useDispatch(),
    navigate = useNavigate();

  useEffect(() => {
    dispatch(allowanceProductSources({ productSources: [] }));
  }, []);


  const clearTableSlices = () => {
    resetSlicesForMainEntry({ dispatch });
  };
  const isTempExist = () =>
        allowanceTempWorkData && Array.isArray(allowanceTempWorkData)
          ? allowanceTempWorkData?.some(e => e?.tempWorkAllowanceId)
          : false;

  const updateOfferDateSlice = postTempData => {
    const offerAmountsData = offerAmounts?.[offerGroup] && offerAmounts?.[offerGroup]?.map(amtObj => {
      const currentDiv = amtObj?.divisionId;
      const currentTemp = postTempData?.find(
        tempObj => tempObj?.divisionId === currentDiv
      );
      if (currentTemp) {
        const allowMap =
          currentTemp?.allowanceTypeSpecification?.[allowName]?.allowancesMap;
        const updatedAllowances =
          offerGroup && allowMap ? allowMap?.[offerGroup] : null;
        const getUpdatedAmtLabel = getAllowAmtLabel(
          currentTemp?.allowanceType,
          updatedAllowances || [],
          efConstants.ALLOWANCE_TYPES.HEADERFLAT.key ===
            currentTemp?.allowanceType,
          true
        );
        const isSameAmtAndUOM = getUpdatedAmtLabel
          ? !/-|and/.test(getUpdatedAmtLabel)
          : null;
        const hasRequiredKeys = (obj, keys) =>
          keys?.some(key => obj?.hasOwnProperty(key));
        const setItemSummarized = () => {
          const amountSummary = amtObj?.summary;
          const isCostsAvailable = amountSummary
            ? hasRequiredKeys(amountSummary, [
                "masterCaseNetCosts",
                "unitNetCosts",
                "allowUomType",
              ])
            : false;
          return isSameAmtAndUOM
            ? isCostsAvailable || amountSummary?.itemAmountsCouldBeSummarized
            : false;
        };
        return {
          ...amtObj,
          ...(updatedAllowances && { allowances: updatedAllowances }),
          summary: {
            ...amtObj?.summary,
            itemAmountsCouldBeSummarized: setItemSummarized(),
          },
        };
      } else {
        return amtObj;
      }
    });
    offerGroup && offerAmountsData &&
      dispatch(
        setOfferAmontsData({
          isAdditionalDatesChanged: {
            ...isAdditionalDatesChanged,
            [offerGroup]: 0,
          },
          offerAmounts: {
            ...offerAmounts,
            [offerGroup]: offerAmountsData,
          },
        })
      );
    isEditEnable &&
      dispatch(
        setIsOfferSectionUpdated({
          isOfferSectionUpdated: true,
          isAmountsSectionUpdated: true,
        })
      );
  };
  const navigateOnsaveChanges = async postTempWorkResp => {
    updateOfferDateSlice(postTempWorkResp);
    if (isComingFromtask(taskType, eventDataResp?.planEvent) && isEditEnable) {
      const putOfferData = await putNationalOfferAllowance({
        URL_PARAMS: [offerNumber],
      });
      putOfferData && (await refetch());
      putOfferData &&
        dispatch(
          offerCardConfiguration({
            offerData: `${offerId}_${_.uniqueId()}`,
            editCardConfig: {},
          })
        );
      if (isTempExist()) {
        const isDeletedTemp = await deleteNationalAllowanceTempWorkData({
          URL_PARAM: eventId,
        });
        isDeletedTemp && dispatch(allowanceTempWorkReset());
        if (appConstants.IS_ENABLE_REDESIGN_ALLOWANCE_CHANGES) {
          // On main entry if coming from task, on save amount and after Update Offer reset slices
          dispatch(resetOfferAmountsData());
          dispatch(resetIsOfferSectionUpdated());
          dispatch(resetOfferSectionsEnableConfig());
          dispatch(resetOfferSectionsData());
        }
      }

      navigate(
        `${basePath}/events/edit/${eventId}?${
          taskType === appConstants.TASK_TYPE_NEW_ITEM
            ? `taskType=${taskType}`
            : ""
        }`
      );
    } else {
      navigateToStepper();
    }
  };

  const navigateToStepper = () => {
    clearTableSlices();
    const allowanceTypeChangedParam = isAllowanceChanged
      ? `&isAllowTypeChange=${isAllowanceChanged}`
      : "";
    navigate(
      `${basePath}/events/edit/${eventId}?isSaved=true&offerKey=${offerGroup}&offerId=${offerNumber}${allowanceTypeChangedParam}${
        taskType === appConstants.TASK_TYPE_NEW_ITEM
          ? `&taskType=${taskType}`
          : ""
      }`
    );
  };

  const getAllowanceTempFromTable = () => {
    const updatedAllowanceAmountsData = generateAllowanceTempObjectForAllDiv({
      allDivisionsTableData,
      allDivisionAllowancesRespCopy,
      switchValue,
      allowGroupConfigObj,
      allowName,
      isAllowConvEnable,
      allowanceTempWorkData: _.cloneDeep(allowanceTempWorkData),
    });

    return (
      Array.isArray(allowanceTempWorkData) &&
      allowanceTempWorkData?.map(tempObj => {
        // Find matching division in allowRespcopy
        const updatedAllowDivWise = updatedAllowanceAmountsData?.find(
          updatedObj => updatedObj?.divisionId === tempObj?.divisionId
        );
        if (updatedAllowDivWise && offerGroup && allowName) {
          return {
            ...tempObj,
            allowanceTypeSpecification: {
              ...tempObj?.allowanceTypeSpecification,
              [allowName]: {
                ...tempObj?.allowanceTypeSpecification?.[allowName],
                allowancesMap: {
                  ...tempObj?.allowanceTypeSpecification?.[allowName]
                    ?.allowancesMap,
                  [offerGroup]: updatedAllowDivWise?.allowances,
                },
              },
            },
          };
        }
        return tempObj;
      })?.sort(compareDivisionIdFn)
    );
  };

  const check_isAnyColumnAllZeroDivWise = () => {
    const isAnyColumnAllZero: any = [];
    allDivisionsTableData?.forEach(divisionData => {
      const isAnyColumnAllZeroDivision = check_isAnyColumnAllZero(
        divisionData?.tableData,
        allowGroupConfigObj?.showLeadDistributorSection
      );
      isAnyColumnAllZeroDivision?.length &&
        isAnyColumnAllZero?.push({
          divisionId: divisionData?.divisionId,
          isAnyColumnAllZeroDivision,
        });
    });
    return isAnyColumnAllZero;
  };
  const handle_saveAllowances = async () => {
    if (leadDistributorsMode === efConstants.LEAD_DIST_LABEL) {
      await dispatch(
        setIsLeadDistributorError({ isLeadDistributorError: false })
      );
      const isInvalidLeadDists = isInValidLeadDists(tableData);
      if (isInvalidLeadDists) {
        dispatch(setIsLeadDistributorError({ isLeadDistributorError: true }));
        return;
      }
    }
    const isHeaderOnlyAmt =
      allowGroupConfigObj?.headerOnlyAmt &&
      !isEmpty(allowGroupConfigObj?.headerOnlyAmt)
        ? true
        : false;
    const isAnyAmountFieldEmtpy = checkForEmptyFieldsDivisionWise(
      allDivisionsTableData,
      allowGroupConfigObj?.isDistCenter,
      dispatch,
      all_divisions_warning_data,
      isHeaderOnlyAmt
    );

    const isAnyColumnAllZero = check_isAnyColumnAllZeroDivWise();
    if (
      isDsd &&
      !validateIfAllOtherVendorsAssignedLeads({
        tableData,
        allDivisionsTableData: isNational ? allDivisionsTableData : [],
      })
    ) {
      setInValidOtherVendorError(true);
      return;
    }

    if (isAnyAmountFieldEmtpy) {
      //Would be true, if any field is cleared or empty
      setIsDisplayFieldAlert(true);
      dispatch(setSelectedFilters({ isFilterReset: true }));
    } else if (!isAnyAmountFieldEmtpy) {
      //If user clicks on update All button / Data has all non empty values
      setIsDisplayFieldAlert(false);
      if (isAnyColumnAllZero?.length) {
        setIsAmountsError(true);
        return;
      }
      const updatedTempData = getAllowanceTempFromTable();
      if (updatedTempData) {
        const divisionIds = updatedTempData?.map(tempObj => tempObj?.divisionId);
        const result = await saveAllowanceAmountSectionData({updatedTempwork: updatedTempData, divisionIds, eventId});
        if (result?.data?.length && result?.data?.every(e => e)) {
          dispatch(
            allowanceTempWorkHandler({
              allowanceData: updatedTempData,
              isTempLoaded: true,
            })
          );
          navigateOnsaveChanges(result?.data);
        }
      }
    }
  };
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const onClosePopup = () => {
    setIsDisplayFieldAlert(false);
    setInValidOtherVendorError(false);
    setIsAmountsError(false);
  };
  const navigateOnCancel = () => {
    if (isComingFromtask(taskType, eventDataResp?.planEvent) && isEditEnable) {
      setIsPopupOpen(true);
    } else {
      onChangeHandler();
    }
  };

  function getAlertDetails(): any {
    if (isDisplayFieldAlert) {
      return {
        variant: "error",
        msg: ` One or more required fields are missing! Please update those fields
          below before you submit again.`,
      };
    } else if (isSaveSuccess) {
      return {
        variant: "success",
        msg: `Successfully submitted`,
      };
    } else if (isNationalTempError) {
      return {
        variant: "error",
        msg: nationalTempError?.data?.message || nationalTempError?.message,
      };
    } else if (isAmountsError) {
      return {
        variant: "error",
        msg: "Overall allowance amount cannot be zero",
      };
    } else if (inValidOtherVendorError) {
      return {
        variant: "error",
        msg: "You have chosen to add additional Vendor Cost Areas and need to specify where they are to be Billed to.  Please utilize the Lead Distributor(s) Only or Billing Selections function to provide where to Bill them to",
      };
    } else {
      return { variant: null, msg: null };
    }
  }

  const SaveStatus = () => {
    const isOpen =
      isDisplayFieldAlert ||
      isSaveSuccess ||
      isNationalTempError ||
      isAmountsError ||
      inValidOtherVendorError;
    if (!isOpen) {
      return false;
    }
    const { variant, msg } = getAlertDetails();
    return (
      <Alert
        isOpen={isOpen}
        dismissible={true}
        variant={variant}
        onClose={onClosePopup}
      >
        {msg}
      </Alert>
    );
  };

  const cancelBtn = (
    <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 abs-ef-all-allowances-top-right-cancel-btn">
      <div className="flex flex-col justify-center items-center flex-grow-0 flex-shrink-0 h-10 overflow-hidden gap-2.5 px-3 py-2.5 rounded-lg">
        <button
          onClick={() => navigateOnCancel()}
          className="flex-grow-0 flex-shrink-0 text-base font-semibold text-center text-[#1b6ebb]"
          data-testid="cta-navigate-to-cancel"
        >
          Cancel
        </button>
      </div>
    </div>
  );
  const disableSaveAllBtn = () => allDivisionsTableData?.every(divTableObj => !divTableObj?.tableData?.length)

  const saveBtn = (
    <div
      className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative abs-ef-all-allowances-top-right-save-btn"
      data-testid="cta-atr-save-allowance"
    >
      <div className="absolute z-50 right-[244px]">{SaveStatus()}</div>
      <Button
        width={190}
        disabled={disableSaveAllBtn()}
        onClick={handle_saveAllowances}
      >
        Save All Changes
      </Button>
    </div>
  );

  const onChangeHandler = async () => {
    clearTableSlices();
    if (isComingFromtask(taskType, eventDataResp?.planEvent) && isEditEnable) {
      const onDeleteTemp =
        isTempExist() &&
        (await deleteNationalAllowanceTempWorkData({
          URL_PARAM: eventId,
        }));
      onDeleteTemp &&
        dispatch(
          offerCardConfiguration({
            offerData: `${offerId}_${_.uniqueId()}`,
          })
        );
      navigate(
        `${basePath}/events/edit/${eventId}?${
          taskType === appConstants.TASK_TYPE_NEW_ITEM
            ? `taskType=${taskType}`
            : ""
        }`
      );
    } else {
      const allowanceTypeChangedParam = isAllowanceChanged
        ? `&isAllowTypeChange=${isAllowanceChanged}`
        : "";
      const isAnyOverlaps = allDivisionAllowancesRespCopy?.find(
          allowRespObj =>
            allowRespObj?.offerAllowanceOverlapResults?.offerAllowanceOverlaps
              ?.length > 0
        ),
        isOverlapsAvailable = isAnyOverlaps ? true : false,
        isAnySummarizedFlagFalse = allDivisionAllowancesRespCopy?.find(
          allowRespObj =>
            allowRespObj?.summary?.itemAmountsCouldBeSummarized === false
        ),
        isItemSummarized = isAnySummarizedFlagFalse ? false : true,
        redirectToMainEntry =
          !isOverlapsAvailable && isItemSummarized === false;
      const isRedirectToMainEntryParam =
        appConstants.IS_ENABLE_REDESIGN_ALLOWANCE_CHANGES
          ? `&isRedirectToMainEntry=${redirectToMainEntry}`
          : "";
      navigate(
        `${basePath}/events/edit/${eventId}?isCancelled=true&offerKey=${offerGroup}&offerId=${offerNumber}${allowanceTypeChangedParam}${isRedirectToMainEntryParam}`
      );
    }
  };

  const onCloseHandler = () => {
    setIsPopupOpen(false);
  };

  const showPopup = (
    <div className="abs-ef-all-allowances-top-right-show-popup">
      {isPopupOpen ? (
        <CommonModal
          isModalPopupOpen={isPopupOpen}
          setModalPopupOpen={setIsPopupOpen}
          title={OFFER_POPUP_MESSAGES?.TITLE}
          confirmBtnTitle={OFFER_POPUP_MESSAGES?.CONFIRM_BTN_TITLE}
          cancelBtnTitle={OFFER_POPUP_MESSAGES?.CANCEL_BTN_TITLE}
          showHideBtns={true}
          height={316}
          onClose={onCloseHandler}
          cancelBtnHandler={onCloseHandler}
          modalNameHandler={onChangeHandler}
          isCancelAmountStep={true}
          minBtnWidth={240}
        />
      ) : null}
    </div>
  );

  const renderDetails = {
    isApiLoading: isNationalTempLoading || isEventDataFetched,
    isPageLevelSpinner: true,
    isRenderMainHtml: true,
    renderHtml: (
      <div className="flex justify-end items-end gap-4 abs-ef-all-allowances-top-right-render-details-container">
        {showPopup}
        {cancelBtn}
        {saveBtn}
      </div>
    ),
  };
  return <RenderStates details={renderDetails} />;
}
