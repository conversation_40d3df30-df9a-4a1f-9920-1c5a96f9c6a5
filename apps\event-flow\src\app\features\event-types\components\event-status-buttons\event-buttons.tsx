import { EUSER_ROLES, getLoggedInUserType } from "@me/util-helpers";
import {
  filterHistory,
  getIsModifyOrCancelEvent,
  getIsPendingCollection,
  getIsPromoOnlyEditByMerchant,
  isEventReturn,
  isEventReturnOnAgreedPending,
  isEventSendBackWtComment,
  isEventStatusAgreedPrev,
  isValidItemPresent,
  latestUpdatedModule,
  isReturnOrLastUpdatedByCurrUser,
  isValidItemsPresentForNationals,
} from "../../event-progress-container-service";
import { EVENT_ACTION_LABELS } from "../event-action/event-action.model";
import { getIsNationalEvent } from "../../event-types-helper";

// Base class that handles common initialization
class LoadBaseEventWorkFlowData {
  // Private variables for encapsulation
  static #eventDetailsData: any;
  static #planAndPendingData: any;
  static #eventProgressService: any;
  static #allowanceData: any;

  // Initialize with necessary data
  static init(
    eventProgressService: any,
    eventDetailsData: any,
    planAndPendingData: any,
    allowanceData: any
  ) {
    this.#eventProgressService = eventProgressService;
    this.#eventDetailsData = eventDetailsData;
    this.#planAndPendingData = planAndPendingData;
    this.#allowanceData = allowanceData;
  }

  // Getters for accessing private variables
  static get eventDetailsData() {
    return this.#eventDetailsData;
  }
  static get planAndPendingData() {
    return this.#planAndPendingData;
  }
  static get eventProgressService() {
    return this.#eventProgressService;
  }
  static get allowanceData() {
    return this.#allowanceData;
  }
}

// Event indicator and data class
class EventWorkFlowIndicatorAndData extends LoadBaseEventWorkFlowData {
  // Computed properties using static getters
  static get eventDetailsEventInd() {
    return LoadBaseEventWorkFlowData.eventDetailsData?.eventDetailsEventInd;
  }
  static get allowanceEventInd() {
    return LoadBaseEventWorkFlowData.eventDetailsData?.allowanceEventInd;
  }
  static get promotionEventInd() {
    return LoadBaseEventWorkFlowData.eventDetailsData?.promotionEventInd;
  }
  static get otherDetailsChangedInd() {
    return LoadBaseEventWorkFlowData.eventDetailsData?.otherDetailsChangedInd;
  }
  static get planEvent() {
    return LoadBaseEventWorkFlowData.planAndPendingData.planEvent;
  }
  static get isPendingCollection() {
    return getIsPendingCollection(LoadBaseEventWorkFlowData.planAndPendingData);
  }
  static get latestHistory() {
    return LoadBaseEventWorkFlowData.eventDetailsData?.planEventHistory[
      LoadBaseEventWorkFlowData.eventDetailsData?.planEventHistory?.length - 1
    ];
  }
}

// Form-related workflow details
class WorkFlowFormDetails extends LoadBaseEventWorkFlowData {
  static isFormComplete() {
    return LoadBaseEventWorkFlowData.eventProgressService?.checkIsFormComplete();
  }

  static get isValidOffers() {
    const { isNDPAndFlagEnabled: isNational } = getIsNationalEvent(LoadBaseEventWorkFlowData.eventDetailsData?.eventType);
    return isNational ? isValidItemsPresentForNationals(LoadBaseEventWorkFlowData.eventDetailsData) : isValidItemPresent(
      LoadBaseEventWorkFlowData.eventDetailsData?.offerAllowances,
      "allowances.0.allowanceStatus",
      "overrideStatus"
    );
  }

  static get isValidPromos() {
    return isValidItemPresent(
      LoadBaseEventWorkFlowData.eventDetailsData?.promotionsLists?.[0]
        ?.promotionsList,
      "promotionWorkflowStatus",
      "overridePromotionStatus"
    );
  }
}

// Event action-related workflow logic
class WorkFlowActionFlag extends LoadBaseEventWorkFlowData {
  static getEventReturnOnAgreedPending(userType) {
    const { isReturnEvent, isReturnByCurrUser } = isEventReturnOnAgreedPending(
      [
        LoadBaseEventWorkFlowData.eventDetailsData?.eventDetailsEventInd &&
          LoadBaseEventWorkFlowData.eventDetailsData?.otherDetailsChangedInd,
        LoadBaseEventWorkFlowData.eventDetailsData?.allowanceEventInd,
        LoadBaseEventWorkFlowData.eventDetailsData?.promotionEventInd,
      ],
      LoadBaseEventWorkFlowData.eventDetailsData?.planEventHistory,
      userType,
      EVENT_ACTION_LABELS.RETURN,
      LoadBaseEventWorkFlowData.eventDetailsData?.eventStatus
    );
    return { isReturnEvent, isReturnByCurrUser };
  }

  static getEventReturnOnAgreedPendingCondition(userType) {
    return (
      WorkFlowActionFlag.getEventReturnOnAgreedPending(userType)
        .isReturnEvent &&
      !WorkFlowActionFlag.getEventReturnOnAgreedPending(userType)
        .isReturnByCurrUser
    );
  }
  static getIsModifyOrCancelEvent() {
    return getIsModifyOrCancelEvent(
      LoadBaseEventWorkFlowData.planAndPendingData
    );
  }

  static getIsEventReturn(label: string, userRole: string) {
    const pidCheck =
      LoadBaseEventWorkFlowData.eventDetailsData?.pidDetailsEventInd &&
      userRole === EUSER_ROLES.VENDOR;
    return isEventReturn(
      [
        LoadBaseEventWorkFlowData.eventDetailsData?.eventDetailsEventInd &&
          (WorkFlowActionFlag.isPrevStatusDraftByMerchant() ||
            LoadBaseEventWorkFlowData.eventDetailsData
              ?.otherDetailsChangedInd ||
            pidCheck),
        LoadBaseEventWorkFlowData.eventDetailsData?.allowanceEventInd,
        LoadBaseEventWorkFlowData.eventDetailsData?.promotionEventInd,
      ],
      LoadBaseEventWorkFlowData.eventDetailsData?.planEventHistory,
      userRole,
      label,
      LoadBaseEventWorkFlowData.eventDetailsData?.eventStatus
    );
  }
  static isPrevStatusDraftByMerchant() {
    return LoadBaseEventWorkFlowData.eventDetailsData?.planEventHistory?.every(
      item => item?.createUser?.type.toUpperCase() === "MERCHANT"
    );
  }

  static isVendorSentAfterSendBackWComm() {
    return filterHistory(
      LoadBaseEventWorkFlowData.eventDetailsData?.planEventHistory,
      EVENT_ACTION_LABELS.STATUS_UPDATE_WITH_COMMENT
    );
  }

  static isAgreedPrevStatus() {
    const { isAgreed: isAgreedPrevStatus } = isEventStatusAgreedPrev(
      LoadBaseEventWorkFlowData.eventDetailsData?.planEventHistory,
      "planevents.eventStatus"
    );
    return isAgreedPrevStatus;
  }

  static isEventStatusUpdateWithComment() {
    return isEventSendBackWtComment(
      LoadBaseEventWorkFlowData.eventDetailsData?.planEventHistory,
      EVENT_ACTION_LABELS.STATUS_UPDATE_WITH_COMMENT
    );
  }

  static isPromoOnlyEditByMerchant() {
    return getIsPromoOnlyEditByMerchant(
      LoadBaseEventWorkFlowData.eventDetailsData
    );
  }
  static getEventReturnStatus(userType = "MERCHANT") {
    const { isReturnEvent } = isReturnOrLastUpdatedByCurrUser(
      userType,
      LoadBaseEventWorkFlowData.eventDetailsData
    );
    return isReturnEvent;
  }
}

// History-related details
class WorkFlowPlanEventHistoryDetails extends LoadBaseEventWorkFlowData {
  static get latestHistory() {
    return LoadBaseEventWorkFlowData.eventDetailsData?.planEventHistory[
      LoadBaseEventWorkFlowData.eventDetailsData?.planEventHistory?.length - 1
    ];
  }

  static get createUser() {
    return this.latestHistory.createUser?.type?.toUpperCase();
  }
}

// Pending event details
class WorkFlowPlanEventPendingUserDetails extends LoadBaseEventWorkFlowData {
  static get updateUser() {
    const updatedUser = latestUpdatedModule(
      LoadBaseEventWorkFlowData.planAndPendingData.planEventPending
    );
    return updatedUser?.type?.toUpperCase();
  }
  static get checkLastUpdateUserWithCurrUser() {
    return (
      WorkFlowPlanEventPendingUserDetails.updateUser === getLoggedInUserType()
    );
  }
}

// User-specific details
class WorkFlowPlanEventUserDetails extends LoadBaseEventWorkFlowData {
  static get updateUser() {
    const updatedUser = latestUpdatedModule(
      LoadBaseEventWorkFlowData.planAndPendingData.planEvent
    );
    return updatedUser?.type?.toUpperCase();
  }
}

export {
  LoadBaseEventWorkFlowData,
  EventWorkFlowIndicatorAndData,
  WorkFlowFormDetails,
  WorkFlowActionFlag,
  WorkFlowPlanEventHistoryDetails,
  WorkFlowPlanEventPendingUserDetails,
  WorkFlowPlanEventUserDetails,
};
