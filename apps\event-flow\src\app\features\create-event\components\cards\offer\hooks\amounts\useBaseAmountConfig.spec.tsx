import { renderHook, act } from "@testing-library/react";
import { useBaseAmountConfig } from "./useBaseAmountConfig";
import { useGetSelectors } from "./useGetSelectors";
import { useGetAmountDerivedValues } from "./useGetAmountDerivedValues";
import { usePutOfferAllowanceMutation } from "../../../../../service/apis/allowance-api";
import { useAllowanceService } from "./useAllowanceAmountService";
import useOfferAmountApi from "./useOfferAmountApi";
import useAllowTempworkUpdate from "../../../../../hooks/useAllowTempworkUpdate";
import { getQueryParams, getUOMFromNameOrValue, handleZeroCostVendors } from "../../../../../service/allowance/allowance-service";

jest.mock("./useGetSelectors", () => ({
  useGetSelectors: jest.fn(),
}));
jest.mock("./useGetAmountDerivedValues", () => ({
  useGetAmountDerivedValues: jest.fn(),
}));
jest.mock("../../../../../service/apis/allowance-api", () => ({
  usePutOfferAllowanceMutation: jest.fn(),
}));
jest.mock("./useAllowanceAmountService", () => ({
  useAllowanceService: jest.fn(),
}));
jest.mock("./useOfferAmountApi", () => jest.fn());
jest.mock("../../../../../hooks/useAllowTempworkUpdate", () => jest.fn());
jest.mock("../../../../../service/allowance/allowance-service", () => ({
  getQueryParams: jest.fn(),
  getUOMFromNameOrValue: jest.fn(),
  handleZeroCostVendors: jest.fn(),
}));

describe("useBaseAmountConfig hook", () => {
  let defaultProps;
  let mockPutOfferAllowance;
  let mockSaveAllowanceAmountSectionData;
  let mockHandlePostSave;

  beforeEach(() => {
    jest.clearAllMocks();

    defaultProps = {
      sectionKey: "test_section",
      cardIndex: 0,
      cardItemIndex: 0,
      isEditEnable: false,
      allowanceAmountData: { allowanceAmount: 100 },
      setAllowanceAmountData: jest.fn(),
      isFormValid: true,
      formControls: { reset: jest.fn() },
    };

    mockPutOfferAllowance = jest.fn();
    mockSaveAllowanceAmountSectionData = jest.fn().mockResolvedValue({ data: true });
    mockHandlePostSave = jest.fn();

    (useGetSelectors as jest.Mock).mockReturnValue({
      allowanceTempWorkData: {},
      productSources: [],
      allowanceForm: {},
      eventDetailsData: { offerAllowances: [], inValidAllowances: [] },
      isAllowConvEnable: false,
      offerSectionsEnableConfig: {},
    });

    (useGetAmountDerivedValues as jest.Mock).mockReturnValue({
      sectionConfiguration: {},
      mapperKey: "test_mapper_key",
      allowanceTypeKey: "HEADERFLAT",
      isHfOrIf: false,
      isHfIfWhseCase: false,
      tempAllowItems: [{ finalizedAmountsInd: false }],
      offerNumber: "12345",
      allowanceRegField: {},
      allowanceFormData: {},
      amountsInitialValueOnLoad: {},
      allowName: "Test Allowance",
    });

    (usePutOfferAllowanceMutation as jest.Mock).mockReturnValue([mockPutOfferAllowance]);

    (useAllowanceService as jest.Mock).mockReturnValue({
      processAllowanceData: jest.fn(() => ({ allowances: [] })),
      saveAllowanceDataInForm: jest.fn(() => ({})),
      handlePostSave: mockHandlePostSave,
      editViewAllItemsClick: jest.fn(),
    });

    (useOfferAmountApi as jest.Mock).mockReturnValue({
      allAllowancesPath: "test/path",
      allowancesResp: { summary: { itemAmountsCouldBeSummarized: false }, allowances: [] },
      isFetching: false,
    });

    (useAllowTempworkUpdate as jest.Mock).mockReturnValue({
      saveAllowanceAmountSectionData: mockSaveAllowanceAmountSectionData,
      isLoading: false,
    });

    (getQueryParams as jest.Mock).mockReturnValue({ taskType: "testTask" });
    (getUOMFromNameOrValue as jest.Mock).mockReturnValue({ name: "KG" });
    (handleZeroCostVendors as jest.Mock).mockReturnValue(false);
  });

  it("should return expected default values", () => {
    const { result } = renderHook(() => useBaseAmountConfig(defaultProps));

    expect(result.current.isFetching).toBe(false);
    expect(result.current.isSectionCompleted).toBe(false);
    expect(result.current.isAmtSavedInTemp).toBe(false);
    expect(result.current.allowancesResp).toEqual({ summary: { itemAmountsCouldBeSummarized: false }, allowances: [] });
    expect(result.current.amtSubLabelDisplayVal).toBe("");
  });

  it("should call handleSave correctly", async () => {
    const { result } = renderHook(() => useBaseAmountConfig(defaultProps));

    await act(async () => {
      await result.current.handleSave({ allowanceAmount: "100" });
    });

    expect(mockSaveAllowanceAmountSectionData).toHaveBeenCalled();
    expect(mockHandlePostSave).toHaveBeenCalled();
    expect(defaultProps.formControls.reset).toHaveBeenCalled();
  });

  it("should not call handleSave when form is invalid", async () => {
    defaultProps.isFormValid = false;
    const { result } = renderHook(() => useBaseAmountConfig(defaultProps));

    await act(async () => {
      await result.current.handleSave({ allowanceAmount: "100" });
    });

    expect(mockSaveAllowanceAmountSectionData).not.toHaveBeenCalled();
  });

  it("should call editViewAllItems correctly", () => {
    const { result } = renderHook(() => useBaseAmountConfig(defaultProps));

    act(() => {
      result.current.editViewAllItems();
    });

    expect(useAllowanceService(defaultProps.sectionKey, {} as any).editViewAllItemsClick).toHaveBeenCalledWith("test/path");
  });
});
