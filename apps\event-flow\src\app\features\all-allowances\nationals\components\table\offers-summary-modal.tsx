import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import Modal from "@albertsons/uds/molecule/Modal";
import { useSelectorWrap } from "@me/data-rtk";
import { transformAllowancesData } from "../../../allowance-table/transformer";
import { cloneDeep } from "lodash";
import { table } from "console";
import DivisionWiseOfferSummaryTable from "./divisionwise-offer-summary-table";
import { X } from "lucide-react";
import { setSelectedSwitchValue } from "../../../../create-event/service/slice/allowances-dashboard-slices";

function OffersSummaryModal({
  setIsOpenOfferSummaryModal,
  allowanceType,
  isOpenOfferSummaryModal,
  cardContainerData,
}) {
  const dispatch = useDispatch();
  const { data: allowGroupConfigObj } = useSelectorWrap(
    "allowanceTableColsGroupConfig_rn"
  );
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const [divisionWiseTableData, setDivisionWiseTableData] = useState<any>([]);

  useEffect(() => {
    const groupedDataByDivision = groupAllowancesByDivision(eventDetailsData);
    const transformed = groupedDataByDivision?.map((division: any) => ({
      ...division,
      allowances: division?.allowances?.map(allowance => ({
        allowance: { ...allowance },
      })),
    }));
    const {
      headerOnlyAmt,
      isHideColGroupHeader,
      showLeadDistributorSection,
      excludeByKey,
    } = allowGroupConfigObj || {};

    const OfferSummaryTransformedData = transformed.map(allowItemDiffResp => {
      let transformed_data = transformAllowancesData(
        cloneDeep(allowItemDiffResp?.allowances),
        headerOnlyAmt,
        true,
        false,
        eventDetailsData?.allowanceType,
        true,
        allowItemDiffResp?.pendingChangesFlag,
        false,
        showLeadDistributorSection,
        false,
        true
      );
      //Adding index information to get the index of the updated text fields when validating for empty fields
      transformed_data = transformed_data.map((item, rowIndex) => ({
        rowIndex,
        ...item,
      }));
      return {
        divisionId: allowItemDiffResp?.divisionId,
        tableData: transformed_data,
        offerNumber: allowItemDiffResp?.offerNumber,
      };
    });
    const filteredData = OfferSummaryTransformedData.filter(item => {
      return item?.offerNumber === cardContainerData?.offerNumber;
    });
    setDivisionWiseTableData(filteredData);
  }, [eventDetailsData, allowGroupConfigObj, cardContainerData?.offerNumber]);

  const groupAllowancesByDivision = planEvent => {
    const offerAllowances = planEvent?.offerAllowances ?? [];
    const grouped: any = [];

    for (const { offerNumber, allowances = [] } of offerAllowances) {
      if (!Array.isArray(allowances)) continue;
      const divisionMap = new Map();

      for (const allowance of allowances) {
        const divisionIds = allowance?.divisionIds ?? [];
        if (!Array.isArray(divisionIds) || divisionIds.length === 0) continue;

        for (const divisionId of divisionIds) {
          if (!divisionMap.has(divisionId)) {
            divisionMap.set(divisionId, []);
          }
          divisionMap.get(divisionId).push(allowance);
        }
      }

      for (const [divisionId, groupedAllowances] of divisionMap.entries()) {
        grouped.push({
          divisionId,
          allowances: groupedAllowances,
          offerNumber,
        });
      }
    }
    return grouped;
  };

  const onModalClose = () => {
    setIsOpenOfferSummaryModal();
    document.body.style.overflow = "visible";
    document.body.classList.remove("modal-open");
    document.body.style.overflow = "visible";
    dispatch(
      setSelectedSwitchValue({
        selectedSwitch: "Case",
      })
    );
  };

  const modalHeader = (
    <div className="sticky top-0 bg-white z-10 flex justify-between items-center px-4 py-4 pb-4 border-b mb-5">
      <div className="text-xl font-bold">
        Summary: Allowance Amounts | {allowanceType}
      </div>

      <div data-testid="close-icon-div-id">
        <X
          size={44}
          strokeWidth={1.2}
          onClick={onModalClose}
          className="cursor-pointer"
        />
      </div>
    </div>
  );

  return (
    <Modal
      isOpen={isOpenOfferSummaryModal}
      className="overflow-y-auto display-scroll my-4 pb-4 mb-4 max-w-4xl w-full bg-white rounded-lg"
    >
      {modalHeader}
      {divisionWiseTableData?.map((divisionTableObj, index) => {
        return (
          <div key={index} className="mb-6 m-4">
            <DivisionWiseOfferSummaryTable
              divisionTableObj={divisionTableObj}
            />
          </div>
        );
      })}
    </Modal>
  );
}

export default OffersSummaryModal;
