import { usePostEventCommentsDataMutation } from "../../event-comments-section-service";
import { useGetCommentsDataQuery } from "../../../create-event/service/apis/event-api";
import { IPlanEventCommentDTO } from "../../interfaces/plan-event-comment-interface";
import { EUSER_ROLES } from "./event-action.model";

export const usePostEventComments = (
  eventId,
  userRole,
  eventStatus,
  commentCategoryType,
  isSendBackWithComment,
  nextStatusToBe,
  eventCommentsList,
  requestForChange
) => {
  const [
    postEventCommentsData,
    {
      isSuccess: isPostEventCommentsDataSuccess,
      data: toPostEventCommentsData,
      error: postError,
    },
  ] = usePostEventCommentsDataMutation();

  const { data: commentAllData, refetch } = useGetCommentsDataQuery(
    { URL_PARAM: [eventId] },
    { skip: !eventId }
  );
  const checkCurrentIsoTime = () => {
    const now = new Date();
    return now.toISOString();
  };
  const createEventCommentPostBody = (comment: string) => {
    const regex = /@(\w+)/g;
    const matches = comment?.match(regex);
    const usernames = matches ? matches.map(match => match.slice(1)) : [];
    const user = window["OAM_REMOTE_USER"] || "TESTUSER";
    const { offers, promos } = eventCommentsList || {};

    const eventCommentBody: IPlanEventCommentDTO = {
      planEvent: eventId,
      commentCategory: commentCategoryType,
      eventComments: [
        {
          messageText: encodeURIComponent(comment) || "",
          createUserNm: user,
          createUserId: user,
          lastUpdUserId: user,
          userType: userRole === EUSER_ROLES.MERCHANT ? "Merchant" : "Vendor",
          notifiedUsers: usernames,
          createTs: checkCurrentIsoTime() || "",
          lastUpdTs: checkCurrentIsoTime() || "",
          sendBackWithComment: isSendBackWithComment,
          requestForChange: requestForChange,
          eventStatus: nextStatusToBe || eventStatus,
        },
      ],
      sendBackWithComment: isSendBackWithComment,
      sendBackWithCommentsDtls: { offers, promos },
      eventStatus: nextStatusToBe || eventStatus,
      requestForChange: requestForChange,
      createUserId: user,
      createTs: checkCurrentIsoTime() || "",
    };
    return eventCommentBody;
  };

  const postComment = (comment: string) => {
    const currentBody = createEventCommentPostBody(comment);
    return postEventCommentsData({ URL_PARAM: eventId, ...currentBody }).then(
      () => refetch()
    );
  };

  return {
    postComment,
    toPostEventCommentsData,
    commentAllData,
    isPostEventCommentsDataSuccess,
  };
};
