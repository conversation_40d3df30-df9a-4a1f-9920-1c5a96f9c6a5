import { renderHook } from '@testing-library/react';
import { useAllowanceService } from './useAllowanceAmountService';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useSelectorWrap } from '@me/data-rtk';
import useGetOfferSectionConfiguration from '../useGetOfferSectionConfiguration';
import { setAllowancesResponse } from '../../../../../service/slice/allowances-response-slice';
import { allowanceNewCardConfiguration, offerCardConfiguration, setAllowanceFormInfo, setOfferAmontsData } from '../../../../../service/slice/allowance-details-slice';

// Mock the dependencies
jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
}));

jest.mock('react-router-dom', () => ({
  useNavigate: jest.fn(),
}));

jest.mock('@me/data-rtk', () => ({
  useSelectorWrap: jest.fn(),
}));

jest.mock('@me-upp-js/utilities', () => ({
  isComingFromtask: jest.fn(),
  isFeatureFlagEnabled: jest.fn(() => false),
}));

jest.mock('@me/utils-root-props', () => ({
  appConstants: {
    FEATURE_FLAGS: {
      ROG_HIDDEN_PRICE: 'ROG_HIDDEN_PRICE'
    }
  }
}));

jest.mock('../../../../../../../shared/ef-constants/ef-constants', () => ({
  ALLOWANCE_TYPES: {
    HEADERFLAT: { key: 'HEADERFLAT' },
    ITEMFLAT: { key: 'ITEMFLAT' },
  },
}));

jest.mock('../../../../../service/allowance/allowance-service', () => ({
  checkIsWarehouseItem: jest.fn(() => false),
  getAllowanceMapKey: jest.fn((key) => key),
  getDatesFromTempWork: jest.fn((item) => item),
  getUOMFromNameOrValue: jest.fn(({ name }) => ({ value: name })),
}));

jest.mock('../../../../../service/slice/allowance-details-slice', () => ({
  allowanceNewCardConfiguration: jest.fn(),
  offerCardConfiguration: jest.fn(),
  setAllowanceFormInfo: jest.fn(),
  setIsOfferSectionUpdated: jest.fn(),
  setOfferAmontsData: jest.fn(),
}));

jest.mock('../../../../../service/slice/allowances-response-slice', () => ({
  setAllowancesResponse: jest.fn(),
}));

jest.mock('../useGetOfferSectionConfiguration', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    moveToNextSectionOnCreate: jest.fn(),
    moveToNextSectionOnUpdate: jest.fn(),
  })),
}));

describe('useAllowanceService', () => {
  const mockDispatch = jest.fn();
  const mockNavigate = jest.fn();
  const mockSelectorWrap = jest.fn();
  
  const sectionKey = 'testSection';
  const allowanceRegField = 'testField';
  
  beforeAll(() => {
    (useDispatch as jest.Mock).mockReturnValue(mockDispatch);
    (useNavigate as jest.Mock).mockReturnValue(mockNavigate);
    (useSelectorWrap as jest.Mock).mockImplementation((key) => {
      if (key === 'offer_amounts_details') {
        return {
          data: {
            offerAmounts: {},
            isAdditionalDatesChanged: {},
          },
        };
      }
      return {};
    });
    
    // Mock isFeatureFlagEnabled to return true for specific test cases if needed
    require('@me-upp-js/utilities').isFeatureFlagEnabled.mockImplementation((flag) => {
      return flag === 'ROG_HIDDEN_PRICE';
    });
  });
  
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('should return all the expected functions', () => {
    const { result } = renderHook(() => useAllowanceService(sectionKey, allowanceRegField));
    
    expect(result.current).toEqual({
      processAllowanceData: expect.any(Function),
      handlePostSave: expect.any(Function),
      getOfferNumber: expect.any(Function),
      saveAllowanceDataInForm: expect.any(Function),
      editViewAllItemsClick: expect.any(Function),
    });
  });
  
  describe('processAllowanceData', () => {
    it('should process allowance data correctly for non-edit mode', () => {
      const { result } = renderHook(() => useAllowanceService(sectionKey, allowanceRegField));
      
      const allowanceAmountData = {
        allowanceAmount: 100,
        finalAmountKey: 'finalAmount',
        finalAmountVal: 100,
        uom: 'testUOM',
      };
      
      const allowancesResp = {
        allowances: [
          {
            vendorNbr: '123',
            locationTypeCd: 'test',
            leadDistributorInd: false,
            allowanceItems: [{ itemId: 1 }],
          },
        ],
      };
      
      const resultData = result.current.processAllowanceData({
        allowanceAmountData,
        allowancesResp,
        isEditEnable: false,
        isAllowConvEnable: true,
        allowanceTypeKey: 'HEADERFLAT',
        tempAllowItems: [{ startDate: '2023-01-01', endDate: '2023-01-31' }],
      });
      
      expect(resultData.allowances[0]).toMatchObject({
        createAllowInd: true,
        leadDistributorInd: true, // Should be true since it's not warehouse item
        finalizedAmountsInd: true,
        headerFlatAmt: 100,
        startDate: '2023-01-01',
        endDate: '2023-01-31',
      });
      
      expect(resultData.amountsData).toEqual({
        allowanceAmount: 100,
        uom: 'testUOM',
        allowUomType: 'testUOM',
        finalAmount: 100,
      });
    });
    
    it('should process allowance data correctly for edit mode', () => {
      const { result } = renderHook(() => useAllowanceService(sectionKey, allowanceRegField));
      
      const allowanceAmountData = {
        allowanceAmount: 200,
        finalAmountKey: 'finalAmount',
        finalAmountVal: 200,
        uom: 'testUOM',
      };
      
      const allowancesResp = {
        allowances: [
          {
            vendorNbr: '123',
            locationTypeCd: 'WAREHOUSE',
            leadDistributorInd: true,
            allowanceItems: [{ itemId: 1 }],
          },
        ],
      };
      
      const resultData = result.current.processAllowanceData({
        allowanceAmountData,
        allowancesResp,
        isEditEnable: true,
        isAllowConvEnable: false,
        allowanceTypeKey: 'ITEMFLAT',
        tempAllowItems: [],
      });
      
      expect(resultData.allowances[0]).toMatchObject({
        createAllowInd: true,
        leadDistributorInd: true, // Should remain true since it's edit mode
        finalizedAmountsInd: true,
      });
      
      expect(resultData.allowances[0].allowanceItems[0]).toMatchObject({
        allowanceAmount: 200,
      });
    });
  });
  
  describe('getOfferNumber', () => {
    it('should return the correct offer number', () => {
      const { result } = renderHook(() => useAllowanceService(sectionKey, allowanceRegField));
      
      const allowanceTempWorkData = {
        allowanceTypeSpecification: {
          HEADERFLAT: {
            offerNumber: 'OFFER123',
          },
        },
      };
      
      const offerNumber = result.current.getOfferNumber(allowanceTempWorkData, 'HEADERFLAT');
      
      expect(offerNumber).toBe('OFFER123');
    });
    
    it('should return undefined when no offer number exists', () => {
      const { result } = renderHook(() => useAllowanceService(sectionKey, allowanceRegField));
      
      const allowanceTempWorkData = {
        allowanceTypeSpecification: {},
      };
      
      const offerNumber = result.current.getOfferNumber(allowanceTempWorkData, 'NONEXISTENT');
      
      expect(offerNumber).toBeUndefined();
    });
  });
  
  describe('saveAllowanceDataInForm', () => {
    it('should save allowance data in form for header flat type', () => {
      const { result } = renderHook(() => useAllowanceService(sectionKey, allowanceRegField));
      
      const allowanceAmountData = {
        allowanceAmount: 100,
        uom: 'testUOM',
      };
      
      const allowanceFormData = {
        existingField: 'value',
        allowanceAmountsData: {},
      };
      
      const allowances = [{ id: 1, headerFlatAmt: 100 }];
      
      const resultData = result.current.saveAllowanceDataInForm({
        allowanceAmountData,
        allowanceTypeKey: 'HEADERFLAT',
        mapperKey: 'testMapper',
        allowanceRegField,
        allowanceFormData,
        allowances,
        isEditEnable: true,
      });
      
      expect(resultData).toEqual({
        [allowanceRegField]: {
          existingField: 'value',
          allowanceAmountsData: {
            testMapper: {
              headerFlatAmt: 100,
              headerFlatAmtItems: allowances,
            },
          },
        },
      });
      
      expect(mockDispatch).toHaveBeenCalledWith(
        setAllowanceFormInfo({
          allowanceFormData: expect.any(Object),
        })
      );
      
      expect(mockDispatch).toHaveBeenCalledWith(
        setOfferAmontsData({
          offerAmounts: {
            testMapper: {
              allowances,
            },
          },
          isAdditionalDatesChanged: {
            testMapper: 0,
          },
        })
      );
    });
    
    it('should save allowance data in form for item flat type', () => {
      const { result } = renderHook(() => useAllowanceService(sectionKey, allowanceRegField));
      
      const allowanceAmountData = {
        allowanceAmount: 200,
        uom: 'itemUOM',
      };
      
      const allowanceFormData = {
        existingField: 'value',
        allowanceAmountsData: {},
      };
      
      const allowances = [{ id: 1, allowanceItems: [{ itemId: 1 }] }];
      
      const resultData = result.current.saveAllowanceDataInForm({
        allowanceAmountData,
        allowanceTypeKey: 'ITEMFLAT',
        mapperKey: 'itemMapper',
        allowanceRegField,
        allowanceFormData,
        allowances,
        isEditEnable: false,
      });
      
      expect(resultData).toEqual({
        [allowanceRegField]: {
          existingField: 'value',
          allowanceAmountsData: {
            itemMapper: {
              allowanceAmount: 200,
              allowances,
              allowanceItems: allowances[0].allowanceItems,
              uom: 'itemUOM',
            },
          },
        },
      });
    });
  });
  
  describe('handlePostSave', () => {
    it('should handle post save for task edit mode', async () => {
  const { result } = renderHook(() => useAllowanceService(sectionKey, allowanceRegField));
  
  const mockPutOfferAllowance = jest.fn().mockResolvedValue({ success: true });
  const allowanceTempWorkData = { 
    offerAllowancesId: '123',
    // Add other required properties from your actual implementation
    allowanceTypeSpecification: {},
    allowances: []
  };
  const updatedFormValues = { test: 'value' };
  
  // Mock isComingFromtask to return true for this test
  require('@me-upp-js/utilities').isComingFromtask.mockImplementation(() => true);
  
  await result.current.handlePostSave({
    isEditEnable: true,
    eventDetailsData: { taskType: 'TASK' },
    allowanceTempWorkData,
    putOfferAllowance: mockPutOfferAllowance,
    taskType: 'TASK',
    offerNumber: 'OFFER123',
    mapperKey: 'testMapper',
    updatedFormValues,
    isInValidOffer: false,
  });
  
  // Add a small delay to allow async operations to complete
  await new Promise(resolve => setTimeout(resolve, 0));
  
  expect(mockPutOfferAllowance).toHaveBeenCalledWith({
    URL_PARAMS: ['OFFER123'],
    ...allowanceTempWorkData,
  });
  
  expect(mockDispatch).toHaveBeenCalledWith(
    offerCardConfiguration({
      offerData: expect.stringContaining('123_'),
    })
  );
  
  expect(mockDispatch).toHaveBeenCalledWith(
    setAllowancesResponse({ success: true })
  );
});
    
    it('should handle post save for non-task create mode', async () => {
      const mockMoveToNextSectionOnCreate = jest.fn();
      (useGetOfferSectionConfiguration as jest.Mock).mockReturnValueOnce({
        moveToNextSectionOnCreate: mockMoveToNextSectionOnCreate,
        moveToNextSectionOnUpdate: jest.fn(),
      });
      
      const { result } = renderHook(() => useAllowanceService(sectionKey, allowanceRegField));
      
      const updatedFormValues = { test: 'value' };
      
      await result.current.handlePostSave({
        isEditEnable: false,
        eventDetailsData: {},
        allowanceTempWorkData: {},
        putOfferAllowance: jest.fn(),
        taskType: 'NON_TASK',
        offerNumber: 'OFFER123',
        mapperKey: 'testMapper',
        updatedFormValues,
        isInValidOffer: false,
      });
      
      expect(mockMoveToNextSectionOnCreate).toHaveBeenCalledWith(
        sectionKey,
        'testMapper',
        updatedFormValues
      );
    });
  });
  
  describe('editViewAllItemsClick', () => {
    it('should navigate to all allowances path and dispatch configuration', () => {
      const { result } = renderHook(() => useAllowanceService(sectionKey, allowanceRegField));
      
      const allAllowancesPath = '/all-allowances';
      
      result.current.editViewAllItemsClick(allAllowancesPath);
      
      expect(mockDispatch).toHaveBeenCalledWith(
        allowanceNewCardConfiguration({
          isNewAllowance: true,
          stepperType: '',
          isAddAnotherOffer: false,
        })
      );
      
      expect(mockNavigate).toHaveBeenCalledWith(allAllowancesPath);
    });
  });
});