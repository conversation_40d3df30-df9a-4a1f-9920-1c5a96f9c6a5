import { render, screen } from "@testing-library/react";
import {
  validateIfAllOtherVendorsAssignedLeads,
  checkIfExternal,
  checkIfAnyExternalVendorPresent,
  tableDataToVendorsArray,
  validateToShowExternalVendorOption,
  getLeadDistributorInitialState,
  getMainAndOtherVendors,
  createStepData,
  getFilteredLeads,
  updatedLeadDistSet,
  moveItemBetweenParents,
  disableIntrnlVendor,
  updateLeadData,
  isInValidLeadDists,
  leadDistError,
  getExternalDistDetails,
  handleReIncludeExternalVend,
  setLeadsForOwnDistOnReInclude,
} from "./billing-selection-utils";
import "@testing-library/jest-dom";
import efConstants from "../../../shared/ef-constants/ef-constants";

describe("validateIfAllOtherVendorsAssignedLeads", () => {
  it("returns true when isBillingInfoShown is false", () => {
    const tableData = [];
    const isBillingInfoShown = false;

    const result = validateIfAllOtherVendorsAssignedLeads({
      tableData,
      addIsBillingCheck: false,
    });

    expect(result).toBe(true);
  });

  it("returns false when isBillingInfoShown is true and vendors have external vendors without other leads", () => {
    const tableData = [
      {
        vendorDetails: [
          {
            vendorNbr: "vendor1",
            allowanceItems: [
              {
                modCommand: "ZERO_COST",
              },
            ],
          },
        ],
      },
      {
        vendorDetails: [
          {
            vendorNbr: "vendor2",
            allowanceItems: [
              {
                modCommand: "ZERO_COST",
              },
            ],
          },
        ],
      },
    ];
    const isBillingInfoShown = true;

    const result = validateIfAllOtherVendorsAssignedLeads({
      tableData,
      addIsBillingCheck: isBillingInfoShown,
    });

    expect(result).toBe(true);
  });

  it("returns false when isBillingInfoShown is true and vendors have internal vendors with other leads", () => {
    const tableData = [
      {
        vendorDetails: [
          {
            vendorNbr: "vendor1",
            allowanceItems: [
              {
                modCommand: "SOME_OTHER_COMMAND",
              },
            ],
          },
        ],
      },
      {
        vendorDetails: [
          {
            vendorNbr: "vendor2",
            allowanceItems: [
              {
                modCommand: "SOME_OTHER_COMMAND",
              },
            ],
          },
        ],
      },
    ];
    const isBillingInfoShown = true;

    const result = validateIfAllOtherVendorsAssignedLeads({
      tableData,
      addIsBillingCheck: isBillingInfoShown,
    });

    expect(result).toBe(true);
  });

  it("returns true when isBillingInfoShown is true and vendors have external vendors with other leads", () => {
    const tableData = [
      {
        vendorDetails: [
          {
            vendorNbr: "vendor1",
            allowanceItems: [
              {
                modCommand: "ZERO_COST",
              },
            ],
          },
        ],
      },
      {
        vendorDetails: [
          {
            vendorNbr: "vendor2",
            allowanceItems: [
              {
                modCommand: "SOME_OTHER_COMMAND",
              },
            ],
          },
        ],
      },
    ];
    const isBillingInfoShown = true;

    const result = validateIfAllOtherVendorsAssignedLeads({
      tableData,
      addIsBillingCheck: isBillingInfoShown,
    });

    expect(result).toBe(true);
  });
});

describe("checkIfExternal", () => {
  it("returns true when vendor has ZERO_COST_TEXT modCommand", () => {
    const vendor = {
      allowanceItems: [
        {
          modCommand: efConstants.ZERO_COST_TEXT,
        },
      ],
    };

    const result = checkIfExternal(vendor);

    expect(result).toBe(true);
  });

  it("returns true when vendor has ZERO_COST_TEXT modCommand at vendor level", () => {
    const vendor = {
      modCommand: efConstants.ZERO_COST_TEXT,
    };

    const result = checkIfExternal(vendor);

    expect(result).toBe(true);
  });

  it("returns false when vendor does not have ZERO_COST_TEXT modCommand", () => {
    const vendor = {
      allowanceItems: [{}],
    };

    const result = checkIfExternal(vendor);

    expect(result).toBe(false);
  });
});


describe('getMainAndOtherVendors', () => {
  it('should return main and other vendors', () => {
        const mockTableData = [
      {
        vendorDetails: [
          {
            vendorNbr: "vendor1",
          },
          {
            vendorNbr: "vendor2",
          },
        ],
      },
      {
        vendorDetails: [
          {
            vendorNbr: "vendor3",
             modCommand: "ZERO_COST",
          },
        ],
      },
    ];
    const mockLeadOptions = ["vendor1","vendor2","vendor3"];

    const result = getMainAndOtherVendors(mockTableData, mockLeadOptions);

    expect(result).toEqual({isAllExternal: false, mainVendors: ["vendor1", "vendor2"], otherVendors: ["vendor3"] });
  });
});

describe('createStepData', () => {
  it('should map selectedList to step data', () => {
    const mockSelectedList = ['item1', 'item2', 'item3'];
    const mockFilteredLeads = ['lead1', 'lead2'];

    const result = createStepData(mockSelectedList, mockFilteredLeads);

    const expected = [
      { id: 'item1', child: mockFilteredLeads },
      { id: 'item2', child: [] },
      { id: 'item3', child: [] },
    ];

    expect(result).toEqual(expected);
  });
});

describe('getFilteredLeads', () => {
  it('should return leads that are not in excludedVendors or selectedList', () => {
    const mockLeadOptions = ['lead1', 'lead2', 'lead3', 'lead4'];
    const mockSelectedList = ['lead2', 'lead3'];
    const mockExcludedVendorData = { excludedVendors: ['lead1'] };

    const result = getFilteredLeads(mockLeadOptions, mockSelectedList, mockExcludedVendorData);

    const expected = ['lead4'];

    expect(result).toEqual(expected);
  });
});

describe("getExternalDistDetails", () => {
  it("should call setExternalVendData with correct parameters", () => {
    // Arrange
    const tableData = [
      {
        rowIndex: 0,
        itemId: "********",
        itemDescription: "TONYS COFFEE WHOLE BEAN CARMELITA       ",
        primaryUpc: "075033975009",
        consumerUpc: "075033975009",
        caseUpc: "0075033975009",
        itemUpcs: ["0075033975009", "075033975009"],
        effectiveStartDate: "0001-12-26",
        effectiveEndDate: "9999-01-01",
        allowanceType: "CASE",
        packWhse: 6,
        ringType: 0,
        size: "12.0 OZ",
        vendorPackConversionFactor: 1,
        unitNetCosts: {
          netCostType: "UNIT",
          cost: 8.96,
          costAllow: 8.5433,
          initialAllowAmt: 0,
          newCostAllow: 8.5433,
        },
        masterCaseNetCosts: {
          netCostType: "MASTER_CASE",
          cost: 53.76,
          costAllow: 51.26,
          initialAllowAmt: 0,
          newCostAllow: 51.26,
        },
        shipCaseNetCosts: {
          netCostType: "SHIP_CASE",
          cost: 53.76,
          costAllow: 51.26,
          initialAllowAmt: 0,
          newCostAllow: 51.26,
        },
        allowanceAmount: 0,
        allowUomType: "CA",
        allowUomTypes: ["CA"],
        bindMetrics: {
          group: "AMOUNTS",
          mode: "AMOUNTS_DEFAULT_BIND",
          bind: true,
        },
        modCommand: "NONE",
        distCenter: "DDSE",
        vendorDetails: [
          {
            vendorNbr: "**********-1",
            vendorName: "KEHE DISTRIBUTORS                       ",
            costAreaDesc: "MAINLAND",
            createAllowInd: true,
            includeInd: true,
            leadDistributorInd: true,
            leadDistributorMode: null,
            leadDistributorInfos: [],
            headerFlatAmt: 0,
            distCenter: "DDSE",
            modCommand: "NONE",
            caseListCost: 53.76,
            unitListCost: 8.96,
            unitCostAllow: "8.54",
            allowanceAmount: "",
            allowUomType: "CA",
            newUnitCostAllow: "8.54",
            caseCostAllow: "51.26",
            newCaseCostAllow: "51.26",
            shippingCost: "",
          },
          {
            isEmptyVendor: true,
            allowanceIdNbr: 5,
            vendorNbr: "**********-1",
            costAreaDesc: "SAFEWAY",
            vendorName: "R & K FOODS INC                         ",
            defaultAllowanceDates: {
              allowanceStartDate: "2024-10-09",
              allowanceEndDate: "2024-10-15",
              performStartDate: "2024-10-09",
              performEndDate: "2024-10-15",
              orderStartDate: "2024-10-06",
              orderEndDate: "2024-10-15",
              shipStartDate: "2024-10-06",
              shipEndDate: "2024-10-15",
              arrivalStartDate: "2024-10-06",
              arrivalEndDate: "2024-10-15",
              overrideInd: false,
              notPreSaved: false,
            },
            allowanceStartDate: "2024-10-09",
            allowanceEndDate: "2024-10-15",
            performStartDate: "2024-10-09",
            performEndDate: "2024-10-15",
            orderStartDate: "2024-10-06",
            orderEndDate: "2024-10-15",
            shipStartDate: "2024-10-06",
            shipEndDate: "2024-10-15",
            arrivalStartDate: "2024-10-06",
            arrivalEndDate: "2024-10-15",
            vehicleId: "664f6a697d0bbbe6913f931f",
            createInd: "CD",
            locationId: "664f67c77d0bbbe69134aed3",
            locationName: "27 - Seattle",
            distCenter: "DDSE",
            locationTypeCd: "D",
            allowanceBillingInfo: {
              vendorIds: [
                {
                  vendorNbr: "020338",
                  vendorSubAccount: "001",
                  costArea: "1",
                  fullVendorNbr: "**********-1",
                },
              ],
              absMerchVendor: "**********",
              absVendorName: "R & K FOODS INC",
              absVendorPaymentType: "D",
              acPayableVendorNbr: "155141",
              acReceivableVendorNbr: "138696",
              billingContactName: "LARRY TRUSELO            ",
              billingContactEmail: "<EMAIL>",
              vendorComment: "",
              vendorOfferTrackingNbr: "",
              vendorBillingList: [
                {
                  billingContactName: "LARRY TRUSELO            ",
                  billingContactEmail: "<EMAIL>",
                },
                {
                  billingContactName: "LARRY TRUSELO            ",
                  billingContactEmail: "<EMAIL>",
                },
              ],
              vendorItemCount: 1,
              vendorItemCountsSet: [
                {
                  vendorDsdWhseId: {
                    vendorNbr: "020338",
                    vendorSubAccount: "001",
                    costArea: "1",
                    vendorRank: "B.DSD",
                    fullVendorNbr: "**********-1",
                    valid: true,
                  },
                  itemIdSet: ["********"],
                  vendorDsdWhseItemCount: 1,
                },
              ],
              source: "SIMS_VENDOR",
              matched: "SIMS_ITEM_VENDOR",
            },
            allowanceBillingInfos: [
              {
                vendorIds: [
                  {
                    vendorNbr: "020338",
                    vendorSubAccount: "001",
                    costArea: "1",
                    fullVendorNbr: "**********-1",
                  },
                ],
                absMerchVendor: "**********",
                absVendorName: "R & K FOODS INC",
                absVendorPaymentType: "D",
                acPayableVendorNbr: "155141",
                acReceivableVendorNbr: "138696",
                billingContactName: "LARRY TRUSELO            ",
                billingContactEmail: "<EMAIL>",
                vendorComment: "",
                vendorOfferTrackingNbr: "",
                vendorBillingList: [
                  {
                    billingContactName: "LARRY TRUSELO            ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "LARRY TRUSELO            ",
                    billingContactEmail: "<EMAIL>",
                  },
                ],
                vendorItemCount: 1,
                vendorItemCountsSet: [
                  {
                    vendorDsdWhseId: {
                      vendorNbr: "020338",
                      vendorSubAccount: "001",
                      costArea: "1",
                      vendorRank: "B.DSD",
                      fullVendorNbr: "**********-1",
                      valid: true,
                    },
                    itemIdSet: ["********"],
                    vendorDsdWhseItemCount: 1,
                  },
                ],
                source: "SIMS_VENDOR",
                matched: "SIMS_ITEM_VENDOR",
              },
            ],
            allowanceDateOffsets: {
              allowanceTypes: ["CASE"],
              startDateOffset: -3,
              endDateOffset: 0,
              defaultOrderLeadTimeDays: 0,
              defaultShipTransitDays: 0,
              resolvedLeadTimeDays: 0,
              resolvedShipTransitDays: 0,
            },
            leadDistributorInfos: [],
            createAllowInd: true,
            allowanceItems: [
              {
                itemId: "********",
                itemDescription: "TONY'S COFFEE OFT GROUND SONGBIRD       ",
                primaryUpc: "075033978219",
                consumerUpc: "075033978219",
                caseUpc: "0000000000000",
                itemUpcs: ["0000000000000", "075033978219"],
                consumerUpcs: [
                  {
                    upc: "075033978219",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "001",
                    sizeDesc: "12 OZ  ",
                  },
                ],
                effectiveStartDate: "0001-12-26",
                effectiveEndDate: "9999-01-01",
                allowanceType: "CASE",
                packWhse: 1,
                ringType: 0,
                size: "12.0 OZ",
                vendorPackConversionFactor: 1,
                unitNetCosts: {
                  netCostType: "UNIT",
                  cost: 0,
                  costAllow: 0,
                  initialAllowAmt: 0,
                  newCostAllow: 0,
                },
                masterCaseNetCosts: {
                  netCostType: "MASTER_CASE",
                  cost: 0,
                  costAllow: 0,
                  initialAllowAmt: 0,
                  newCostAllow: 0,
                },
                shipCaseNetCosts: {
                  netCostType: "SHIP_CASE",
                  cost: 0,
                  costAllow: 0,
                  initialAllowAmt: 0,
                  newCostAllow: 0,
                },
                allowanceAmount: 0,
                allowUomType: "CA",
                allowUomTypes: ["CA"],
                overlaps: {
                  offerAllowAmounts: [
                    {
                      offer: 7028330,
                      rog: "SACG",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSEA",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSPK",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7029146,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029149,
                      rog: "SACG",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSEA",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSPK",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                  ],
                  unitizedOverlaps: {
                    netCostType: "UNIT",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 0.3,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 0.3,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 1,
                  },
                  shipCaseOverlaps: {
                    netCostType: "SHIP_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 0.3,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 0.3,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 1,
                  },
                  masterCaseOverlaps: {
                    netCostType: "MASTER_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 0.3,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 0.3,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 1,
                  },
                },
                bindMetrics: {
                  group: "AMOUNTS",
                  mode: "AMOUNTS_DEFAULT_BIND",
                  bind: true,
                },
                modCommand: "ZERO_COST",
              },
            ],
            headerFlatAmt: 0,
            allowanceStatus: "Draft",
            storeGroups: [],
            leadDistributorInd: false,
            includeInd: true,
            finalizedAmountsInd: false,
            dateBindMetrics: {
              group: "DATES",
              mode: "DATES_CASE_BIND",
              bindValues: {
                srcDates: {
                  allow: {
                    startDate: "2024-10-09",
                    endDate: "2024-10-15",
                  },
                  order: {
                    startDate: "2024-10-06",
                    endDate: "2024-10-15",
                  },
                  ship: {
                    startDate: "2024-10-06",
                    endDate: "2024-10-15",
                  },
                  arrival: {
                    startDate: "2024-10-06",
                    endDate: "2024-10-15",
                  },
                },
                dstDates: {
                  allow: {
                    startDate: "2024-10-09",
                    endDate: "2024-10-15",
                  },
                  order: {
                    startDate: "2024-10-06",
                    endDate: "2024-10-15",
                  },
                  ship: {
                    startDate: "2024-10-06",
                    endDate: "2024-10-15",
                  },
                  arrival: {
                    startDate: "2024-10-06",
                    endDate: "2024-10-15",
                  },
                },
              },
              bind: true,
            },
            excludedItems: [
              {
                itemId: "********",
                excludeInd: "NO_VENDOR_LOCATION",
              },
              {
                itemId: "********",
                excludeInd: "NO_VENDOR_LOCATION",
              },
              {
                itemId: "********",
                excludeInd: "NO_VENDOR_LOCATION",
              },
              {
                itemId: "********",
                excludeInd: "NO_VENDOR_LOCATION",
              },
              {
                itemId: "********",
                excludeInd: "NO_VENDOR_LOCATION",
              },
              {
                itemId: "********",
                excludeInd: "NO_VENDOR_LOCATION",
              },
              {
                itemId: "2010289",
                excludeInd: "NO_VENDOR_LOCATION",
              },
              {
                itemId: "2011373",
                excludeInd: "NO_VENDOR_LOCATION",
              },
              {
                itemId: "2020118",
                excludeInd: "NO_VENDOR_LOCATION",
              },
              {
                itemId: "2020143",
                excludeInd: "NO_VENDOR_LOCATION",
              },
              {
                itemId: "2020154",
                excludeInd: "NO_VENDOR_LOCATION",
              },
              {
                itemId: "2020205",
                excludeInd: "NO_VENDOR_LOCATION",
              },
              {
                itemId: "2020251",
                excludeInd: "NO_VENDOR_LOCATION",
              },
              {
                itemId: "2020255",
                excludeInd: "NO_VENDOR_LOCATION",
              },
              {
                itemId: "2020257",
                excludeInd: "NO_VENDOR_LOCATION",
              },
              {
                itemId: "2020393",
                excludeInd: "NO_VENDOR_LOCATION",
              },
              {
                itemId: "2021543",
                excludeInd: "NO_VENDOR_LOCATION",
              },
              {
                itemId: "2021577",
                excludeInd: "NO_VENDOR_LOCATION",
              },
            ],
            allowanceProcessStatus: "",
            allowDownstreamStatus: {},
            leadDistributorMode: null,
            modCommand: "ZERO_COST",
          },
        ],
      },
      {
        rowIndex: 16,
        itemId: "********",
        itemDescription: "TONY'S COFFEE OFT GROUND SONGBIRD       ",
        primaryUpc: "075033978219",
        consumerUpc: "075033978219",
        caseUpc: "0000000000000",
        itemUpcs: ["0000000000000", "075033978219"],
        consumerUpcs: [
          {
            upc: "075033978219",
            rog: "SSEA",
            primaryInd: true,
            labelSize: "M",
            packDesc: "001",
            sizeDesc: "12 OZ  ",
          },
        ],
        effectiveStartDate: "0001-12-26",
        effectiveEndDate: "9999-01-01",
        allowanceType: "CASE",
        packWhse: 1,
        ringType: 0,
        size: "12.0 OZ",
        vendorPackConversionFactor: 1,
        unitNetCosts: {
          netCostType: "UNIT",
          cost: 0,
          costAllow: 0,
          initialAllowAmt: 0,
          newCostAllow: 0,
        },
        masterCaseNetCosts: {
          netCostType: "MASTER_CASE",
          cost: 0,
          costAllow: 0,
          initialAllowAmt: 0,
          newCostAllow: 0,
        },
        shipCaseNetCosts: {
          netCostType: "SHIP_CASE",
          cost: 0,
          costAllow: 0,
          initialAllowAmt: 0,
          newCostAllow: 0,
        },
        allowanceAmount: 0,
        allowUomType: "CA",
        allowUomTypes: ["CA"],
        overlaps: {
          offerAllowAmounts: [
            {
              offer: 7028330,
              rog: "SACG",
              basisDsc: "T",
              amount: 0.3,
            },
            {
              offer: 7028330,
              rog: "SSEA",
              basisDsc: "T",
              amount: 0.3,
            },
            {
              offer: 7028330,
              rog: "SSPK",
              basisDsc: "T",
              amount: 0.3,
            },
            {
              offer: 7029146,
              rog: "SACG",
              basisDsc: "S",
              amount: 0.1,
            },
            {
              offer: 7029146,
              rog: "SSEA",
              basisDsc: "S",
              amount: 0.1,
            },
            {
              offer: 7029146,
              rog: "SSPK",
              basisDsc: "S",
              amount: 0.1,
            },
            {
              offer: 7029148,
              rog: "SACG",
              basisDsc: "S",
              amount: 0.1,
            },
            {
              offer: 7029148,
              rog: "SSEA",
              basisDsc: "S",
              amount: 0.1,
            },
            {
              offer: 7029148,
              rog: "SSPK",
              basisDsc: "S",
              amount: 0.1,
            },
            {
              offer: 7029149,
              rog: "SACG",
              basisDsc: "C",
              amount: 0.5,
            },
            {
              offer: 7029149,
              rog: "SSEA",
              basisDsc: "C",
              amount: 0.5,
            },
            {
              offer: 7029149,
              rog: "SSPK",
              basisDsc: "C",
              amount: 0.5,
            },
          ],
          unitizedOverlaps: {
            netCostType: "UNIT",
            convertedAllowanceAmounts: [
              {
                offer: 7028330,
                basisDsc: "T",
                amount: 0.3,
                convertedAmount: 0.3,
              },
              {
                offer: 7029146,
                basisDsc: "S",
                amount: 0.1,
                convertedAmount: 0.1,
              },
              {
                offer: 7029148,
                basisDsc: "S",
                amount: 0.1,
                convertedAmount: 0.1,
              },
              {
                offer: 7029149,
                basisDsc: "C",
                amount: 0.5,
                convertedAmount: 0.5,
              },
            ],
            scanAllow: 0.3,
            caseAllow: 0.5,
            shipAllow: 0.2,
            allowSum: 1,
          },
          shipCaseOverlaps: {
            netCostType: "SHIP_CASE",
            convertedAllowanceAmounts: [
              {
                offer: 7028330,
                basisDsc: "T",
                amount: 0.3,
                convertedAmount: 0.3,
              },
              {
                offer: 7029146,
                basisDsc: "S",
                amount: 0.1,
                convertedAmount: 0.1,
              },
              {
                offer: 7029148,
                basisDsc: "S",
                amount: 0.1,
                convertedAmount: 0.1,
              },
              {
                offer: 7029149,
                basisDsc: "C",
                amount: 0.5,
                convertedAmount: 0.5,
              },
            ],
            scanAllow: 0.3,
            caseAllow: 0.5,
            shipAllow: 0.2,
            allowSum: 1,
          },
          masterCaseOverlaps: {
            netCostType: "MASTER_CASE",
            convertedAllowanceAmounts: [
              {
                offer: 7028330,
                basisDsc: "T",
                amount: 0.3,
                convertedAmount: 0.3,
              },
              {
                offer: 7029146,
                basisDsc: "S",
                amount: 0.1,
                convertedAmount: 0.1,
              },
              {
                offer: 7029148,
                basisDsc: "S",
                amount: 0.1,
                convertedAmount: 0.1,
              },
              {
                offer: 7029149,
                basisDsc: "C",
                amount: 0.5,
                convertedAmount: 0.5,
              },
            ],
            scanAllow: 0.3,
            caseAllow: 0.5,
            shipAllow: 0.2,
            allowSum: 1,
          },
        },
        bindMetrics: {
          group: "AMOUNTS",
          mode: "AMOUNTS_DEFAULT_BIND",
          bind: true,
        },
        modCommand: "ZERO_COST",
        distCenter: "DDSE",
        vendorDetails: [
          {
            isEmptyVendor: true,
            allowanceIdNbr: 1,
            vendorNbr: "**********-1",
            costAreaDesc: "MAINLAND",
            vendorName: "KEHE DISTRIBUTORS                       ",
            defaultAllowanceDates: {
              allowanceStartDate: "2024-10-09",
              allowanceEndDate: "2024-10-15",
              performStartDate: "2024-10-09",
              performEndDate: "2024-10-15",
              orderStartDate: "2024-10-06",
              orderEndDate: "2024-10-15",
              shipStartDate: "2024-10-06",
              shipEndDate: "2024-10-15",
              arrivalStartDate: "2024-10-06",
              arrivalEndDate: "2024-10-15",
              overrideInd: false,
              notPreSaved: false,
            },
            allowanceStartDate: "2024-10-09",
            allowanceEndDate: "2024-10-15",
            performStartDate: "2024-10-09",
            performEndDate: "2024-10-15",
            orderStartDate: "2024-10-06",
            orderEndDate: "2024-10-15",
            shipStartDate: "2024-10-06",
            shipEndDate: "2024-10-15",
            arrivalStartDate: "2024-10-06",
            arrivalEndDate: "2024-10-15",
            vehicleId: "664f6a697d0bbbe6913f931f",
            createInd: "CD",
            locationId: "664f67c77d0bbbe69134aed3",
            locationName: "27 - Seattle",
            distCenter: "DDSE",
            locationTypeCd: "D",
            allowanceBillingInfo: {
              vendorIds: [
                {
                  vendorNbr: "006446",
                  vendorSubAccount: "001",
                  costArea: "1",
                  fullVendorNbr: "**********-1",
                },
              ],
              absMerchVendor: "**********",
              absVendorName: "KEHE DISTRIBUTORS",
              absVendorPaymentType: "D",
              acPayableVendorNbr: "110056",
              acReceivableVendorNbr: "082099",
              billingContactName: "<EMAIL>        ",
              billingContactEmail: "<EMAIL>",
              vendorComment: "",
              vendorOfferTrackingNbr: "",
              vendorBillingList: [
                {
                  billingContactName: "<EMAIL>        ",
                  billingContactEmail: "<EMAIL>",
                },
                {
                  billingContactName: "BRANDON SWEET            ",
                  billingContactEmail: "<EMAIL>",
                },
                {
                  billingContactName: "<EMAIL>        ",
                  billingContactEmail: "<EMAIL>",
                },
                {
                  billingContactName: "<EMAIL>     ",
                  billingContactEmail: "<EMAIL>",
                },
                {
                  billingContactName: "<EMAIL>        ",
                  billingContactEmail: "<EMAIL>",
                },
                {
                  billingContactName: "BRANDON SWEET            ",
                  billingContactEmail: "<EMAIL>",
                },
                {
                  billingContactName: "<EMAIL>        ",
                  billingContactEmail: "<EMAIL>",
                },
                {
                  billingContactName: "<EMAIL>     ",
                  billingContactEmail: "<EMAIL>",
                },
              ],
              vendorItemCount: 16,
              vendorItemCountsSet: [
                {
                  vendorDsdWhseId: {
                    vendorNbr: "006446",
                    vendorSubAccount: "001",
                    costArea: "1",
                    vendorRank: "B.DSD",
                    fullVendorNbr: "**********-1",
                    valid: true,
                  },
                  itemIdSet: [
                    "********",
                    "********",
                    "********",
                    "********",
                    "********",
                    "********",
                    "2010289",
                    "2011373",
                    "2020143",
                    "2020154",
                    "2020205",
                    "2020255",
                    "2020257",
                    "2020393",
                    "2021543",
                    "2021577",
                  ],
                  vendorDsdWhseItemCount: 16,
                },
              ],
              source: "SIMS_VENDOR",
              matched: "SIMS_ITEM_VENDOR_SUBMITTER_OVERRIDE",
            },
            allowanceBillingInfos: [
              {
                vendorIds: [
                  {
                    vendorNbr: "006446",
                    vendorSubAccount: "001",
                    costArea: "1",
                    fullVendorNbr: "**********-1",
                  },
                ],
                absMerchVendor: "**********",
                absVendorName: "KEHE DISTRIBUTORS",
                absVendorPaymentType: "D",
                acPayableVendorNbr: "110056",
                acReceivableVendorNbr: "082099",
                billingContactName: "<EMAIL>        ",
                billingContactEmail: "<EMAIL>",
                vendorComment: "",
                vendorOfferTrackingNbr: "",
                vendorBillingList: [
                  {
                    billingContactName: "<EMAIL>        ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "BRANDON SWEET            ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>        ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>     ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>        ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "BRANDON SWEET            ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>        ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>     ",
                    billingContactEmail: "<EMAIL>",
                  },
                ],
                vendorItemCount: 16,
                vendorItemCountsSet: [
                  {
                    vendorDsdWhseId: {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "1",
                      vendorRank: "B.DSD",
                      fullVendorNbr: "**********-1",
                      valid: true,
                    },
                    itemIdSet: [
                      "********",
                      "********",
                      "********",
                      "********",
                      "********",
                      "********",
                      "2010289",
                      "2011373",
                      "2020143",
                      "2020154",
                      "2020205",
                      "2020255",
                      "2020257",
                      "2020393",
                      "2021543",
                      "2021577",
                    ],
                    vendorDsdWhseItemCount: 16,
                  },
                ],
                source: "SIMS_VENDOR",
                matched: "SIMS_ITEM_VENDOR_SUBMITTER_OVERRIDE",
              },
            ],
            allowanceDateOffsets: {
              allowanceTypes: ["CASE"],
              startDateOffset: -3,
              endDateOffset: 0,
              defaultOrderLeadTimeDays: 0,
              defaultShipTransitDays: 0,
              resolvedLeadTimeDays: 0,
              resolvedShipTransitDays: 0,
            },
            leadDistributorInfos: [],
            createAllowInd: true,
            allowanceItems: [
              {
                itemId: "********",
                itemDescription: "TONYS COFFEE WHOLE BEAN CARMELITA       ",
                primaryUpc: "075033975009",
                consumerUpc: "075033975009",
                caseUpc: "0075033975009",
                itemUpcs: ["0075033975009", "075033975009"],
                consumerUpcs: [
                  {
                    upc: "075033975009",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "12 OZ  ",
                  },
                  {
                    upc: "075033975009",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "12 OZ  ",
                  },
                  {
                    upc: "075033975009",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "12 OZ  ",
                  },
                ],
                effectiveStartDate: "0001-12-26",
                effectiveEndDate: "9999-01-01",
                allowanceType: "CASE",
                packWhse: 6,
                ringType: 0,
                size: "12.0 OZ",
                vendorPackConversionFactor: 1,
                unitNetCosts: {
                  netCostType: "UNIT",
                  cost: 8.96,
                  costAllow: 8.5433,
                  initialAllowAmt: 0,
                  newCostAllow: 8.5433,
                },
                masterCaseNetCosts: {
                  netCostType: "MASTER_CASE",
                  cost: 53.76,
                  costAllow: 51.26,
                  initialAllowAmt: 0,
                  newCostAllow: 51.26,
                },
                shipCaseNetCosts: {
                  netCostType: "SHIP_CASE",
                  cost: 53.76,
                  costAllow: 51.26,
                  initialAllowAmt: 0,
                  newCostAllow: 51.26,
                },
                allowanceAmount: 0,
                allowUomType: "CA",
                allowUomTypes: ["CA"],
                overlaps: {
                  offerAllowAmounts: [
                    {
                      offer: 7028330,
                      rog: "SACG",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSEA",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSPK",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7029146,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029149,
                      rog: "SACG",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSEA",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSPK",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                  ],
                  unitizedOverlaps: {
                    netCostType: "UNIT",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 0.3,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0167,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0167,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.0833,
                      },
                    ],
                    scanAllow: 0.3,
                    caseAllow: 0.0833,
                    shipAllow: 0.0334,
                    allowSum: 0.4167,
                  },
                  shipCaseOverlaps: {
                    netCostType: "SHIP_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 1.8,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 1.8,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 2.5,
                  },
                  masterCaseOverlaps: {
                    netCostType: "MASTER_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 1.8,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 1.8,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 2.5,
                  },
                },
                bindMetrics: {
                  group: "AMOUNTS",
                  mode: "AMOUNTS_DEFAULT_BIND",
                  bind: true,
                },
                modCommand: "NONE",
              },
              {
                itemId: "********",
                itemDescription: "TONYS COFFEE GROUND CARMELITAGRND COF   ",
                primaryUpc: "075033975019",
                consumerUpc: "075033975019",
                caseUpc: "0075033975019",
                itemUpcs: ["0075033975019", "075033975019"],
                consumerUpcs: [
                  {
                    upc: "075033975019",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "12 OZ  ",
                  },
                ],
                effectiveStartDate: "0001-12-26",
                effectiveEndDate: "9999-01-01",
                allowanceType: "CASE",
                packWhse: 6,
                ringType: 0,
                size: "12.0 OZ",
                vendorPackConversionFactor: 1,
                unitNetCosts: {
                  netCostType: "UNIT",
                  cost: 8.96,
                  costAllow: 8.5433,
                  initialAllowAmt: 0,
                  newCostAllow: 8.5433,
                },
                masterCaseNetCosts: {
                  netCostType: "MASTER_CASE",
                  cost: 53.76,
                  costAllow: 51.26,
                  initialAllowAmt: 0,
                  newCostAllow: 51.26,
                },
                shipCaseNetCosts: {
                  netCostType: "SHIP_CASE",
                  cost: 53.76,
                  costAllow: 51.26,
                  initialAllowAmt: 0,
                  newCostAllow: 51.26,
                },
                allowanceAmount: 0,
                allowUomType: "CA",
                allowUomTypes: ["CA"],
                overlaps: {
                  offerAllowAmounts: [
                    {
                      offer: 7028330,
                      rog: "SACG",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSEA",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSPK",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7029146,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029149,
                      rog: "SACG",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSEA",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSPK",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                  ],
                  unitizedOverlaps: {
                    netCostType: "UNIT",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 0.3,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0167,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0167,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.0833,
                      },
                    ],
                    scanAllow: 0.3,
                    caseAllow: 0.0833,
                    shipAllow: 0.0334,
                    allowSum: 0.4167,
                  },
                  shipCaseOverlaps: {
                    netCostType: "SHIP_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 1.8,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 1.8,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 2.5,
                  },
                  masterCaseOverlaps: {
                    netCostType: "MASTER_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 1.8,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 1.8,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 2.5,
                  },
                },
                bindMetrics: {
                  group: "AMOUNTS",
                  mode: "AMOUNTS_DEFAULT_BIND",
                  bind: true,
                },
                modCommand: "NONE",
              },
              {
                itemId: "********",
                itemDescription: "TONYS COFFEE GROUND UPLAND BLEND        ",
                primaryUpc: "075033973819",
                consumerUpc: "075033973819",
                caseUpc: "0075033973819",
                itemUpcs: ["0075033973819", "075033973819"],
                consumerUpcs: [
                  {
                    upc: "075033973819",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "12 OZ  ",
                  },
                  {
                    upc: "075033973819",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "12 OZ  ",
                  },
                  {
                    upc: "075033973819",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "12 OZ  ",
                  },
                ],
                effectiveStartDate: "0001-12-26",
                effectiveEndDate: "9999-01-01",
                allowanceType: "CASE",
                packWhse: 6,
                ringType: 0,
                size: "12.0 OZ",
                vendorPackConversionFactor: 1,
                unitNetCosts: {
                  netCostType: "UNIT",
                  cost: 8.96,
                  costAllow: 8.5433,
                  initialAllowAmt: 0,
                  newCostAllow: 8.5433,
                },
                masterCaseNetCosts: {
                  netCostType: "MASTER_CASE",
                  cost: 53.76,
                  costAllow: 51.26,
                  initialAllowAmt: 0,
                  newCostAllow: 51.26,
                },
                shipCaseNetCosts: {
                  netCostType: "SHIP_CASE",
                  cost: 53.76,
                  costAllow: 51.26,
                  initialAllowAmt: 0,
                  newCostAllow: 51.26,
                },
                allowanceAmount: 0,
                allowUomType: "CA",
                allowUomTypes: ["CA"],
                overlaps: {
                  offerAllowAmounts: [
                    {
                      offer: 7028330,
                      rog: "SACG",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSEA",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSPK",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7029146,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029149,
                      rog: "SACG",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSEA",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSPK",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                  ],
                  unitizedOverlaps: {
                    netCostType: "UNIT",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 0.3,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0167,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0167,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.0833,
                      },
                    ],
                    scanAllow: 0.3,
                    caseAllow: 0.0833,
                    shipAllow: 0.0334,
                    allowSum: 0.4167,
                  },
                  shipCaseOverlaps: {
                    netCostType: "SHIP_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 1.8,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 1.8,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 2.5,
                  },
                  masterCaseOverlaps: {
                    netCostType: "MASTER_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 1.8,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 1.8,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 2.5,
                  },
                },
                bindMetrics: {
                  group: "AMOUNTS",
                  mode: "AMOUNTS_DEFAULT_BIND",
                  bind: true,
                },
                modCommand: "NONE",
              },
              {
                itemId: "********",
                itemDescription: "TONYS COFFEE WHOLE BEAB UPLAND BLENDAN  ",
                primaryUpc: "075033973809",
                consumerUpc: "075033973809",
                caseUpc: "0075033973809",
                itemUpcs: ["0075033973809", "075033973809"],
                consumerUpcs: [
                  {
                    upc: "075033973809",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "12 OZ  ",
                  },
                  {
                    upc: "075033973809",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "12 OZ  ",
                  },
                  {
                    upc: "075033973809",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "12 OZ  ",
                  },
                ],
                effectiveStartDate: "0001-12-26",
                effectiveEndDate: "9999-01-01",
                allowanceType: "CASE",
                packWhse: 6,
                ringType: 0,
                size: "12.0 OZ",
                vendorPackConversionFactor: 1,
                unitNetCosts: {
                  netCostType: "UNIT",
                  cost: 8.96,
                  costAllow: 8.5433,
                  initialAllowAmt: 0,
                  newCostAllow: 8.5433,
                },
                masterCaseNetCosts: {
                  netCostType: "MASTER_CASE",
                  cost: 53.76,
                  costAllow: 51.26,
                  initialAllowAmt: 0,
                  newCostAllow: 51.26,
                },
                shipCaseNetCosts: {
                  netCostType: "SHIP_CASE",
                  cost: 53.76,
                  costAllow: 51.26,
                  initialAllowAmt: 0,
                  newCostAllow: 51.26,
                },
                allowanceAmount: 0,
                allowUomType: "CA",
                allowUomTypes: ["CA"],
                overlaps: {
                  offerAllowAmounts: [
                    {
                      offer: 7028330,
                      rog: "SACG",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSEA",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSPK",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7029146,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029149,
                      rog: "SACG",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSEA",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSPK",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                  ],
                  unitizedOverlaps: {
                    netCostType: "UNIT",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 0.3,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0167,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0167,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.0833,
                      },
                    ],
                    scanAllow: 0.3,
                    caseAllow: 0.0833,
                    shipAllow: 0.0334,
                    allowSum: 0.4167,
                  },
                  shipCaseOverlaps: {
                    netCostType: "SHIP_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 1.8,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 1.8,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 2.5,
                  },
                  masterCaseOverlaps: {
                    netCostType: "MASTER_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 1.8,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 1.8,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 2.5,
                  },
                },
                bindMetrics: {
                  group: "AMOUNTS",
                  mode: "AMOUNTS_DEFAULT_BIND",
                  bind: true,
                },
                modCommand: "NONE",
              },
              {
                itemId: "********",
                itemDescription: "TONYS COFFEE WHOLE BEAN ESPRESSO NOIR   ",
                primaryUpc: "075033975809",
                consumerUpc: "075033975809",
                caseUpc: "0075033975809",
                itemUpcs: ["0075033975809", "075033975809"],
                consumerUpcs: [
                  {
                    upc: "075033975809",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "12 OZ  ",
                  },
                  {
                    upc: "075033975809",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "12 OZ  ",
                  },
                  {
                    upc: "075033975809",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "12 OZ  ",
                  },
                ],
                effectiveStartDate: "0001-12-26",
                effectiveEndDate: "9999-01-01",
                allowanceType: "CASE",
                packWhse: 6,
                ringType: 0,
                size: "12.0 OZ",
                vendorPackConversionFactor: 1,
                unitNetCosts: {
                  netCostType: "UNIT",
                  cost: 8.96,
                  costAllow: 8.5433,
                  initialAllowAmt: 0,
                  newCostAllow: 8.5433,
                },
                masterCaseNetCosts: {
                  netCostType: "MASTER_CASE",
                  cost: 53.76,
                  costAllow: 51.26,
                  initialAllowAmt: 0,
                  newCostAllow: 51.26,
                },
                shipCaseNetCosts: {
                  netCostType: "SHIP_CASE",
                  cost: 53.76,
                  costAllow: 51.26,
                  initialAllowAmt: 0,
                  newCostAllow: 51.26,
                },
                allowanceAmount: 0,
                allowUomType: "CA",
                allowUomTypes: ["CA"],
                overlaps: {
                  offerAllowAmounts: [
                    {
                      offer: 7028330,
                      rog: "SACG",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSEA",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSPK",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7029146,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029149,
                      rog: "SACG",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSEA",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSPK",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                  ],
                  unitizedOverlaps: {
                    netCostType: "UNIT",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 0.3,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0167,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0167,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.0833,
                      },
                    ],
                    scanAllow: 0.3,
                    caseAllow: 0.0833,
                    shipAllow: 0.0334,
                    allowSum: 0.4167,
                  },
                  shipCaseOverlaps: {
                    netCostType: "SHIP_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 1.8,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 1.8,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 2.5,
                  },
                  masterCaseOverlaps: {
                    netCostType: "MASTER_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 1.8,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 1.8,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 2.5,
                  },
                },
                bindMetrics: {
                  group: "AMOUNTS",
                  mode: "AMOUNTS_DEFAULT_BIND",
                  bind: true,
                },
                modCommand: "NONE",
              },
              {
                itemId: "********",
                itemDescription: "TONYS COFFEE GROUND FRENCH ROYALE OFT   ",
                primaryUpc: "075033975919",
                consumerUpc: "075033975919",
                caseUpc: "0075033975919",
                itemUpcs: ["0075033975919", "075033975919"],
                consumerUpcs: [
                  {
                    upc: "075033975919",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "12 OZ  ",
                  },
                  {
                    upc: "075033975919",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "12 OZ  ",
                  },
                  {
                    upc: "075033975919",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "12 OZ  ",
                  },
                ],
                effectiveStartDate: "0001-12-26",
                effectiveEndDate: "9999-01-01",
                allowanceType: "CASE",
                packWhse: 6,
                ringType: 0,
                size: "12.0 OZ",
                vendorPackConversionFactor: 1,
                unitNetCosts: {
                  netCostType: "UNIT",
                  cost: 8.96,
                  costAllow: 8.5433,
                  initialAllowAmt: 0,
                  newCostAllow: 8.5433,
                },
                masterCaseNetCosts: {
                  netCostType: "MASTER_CASE",
                  cost: 53.76,
                  costAllow: 51.26,
                  initialAllowAmt: 0,
                  newCostAllow: 51.26,
                },
                shipCaseNetCosts: {
                  netCostType: "SHIP_CASE",
                  cost: 53.76,
                  costAllow: 51.26,
                  initialAllowAmt: 0,
                  newCostAllow: 51.26,
                },
                allowanceAmount: 0,
                allowUomType: "CA",
                allowUomTypes: ["CA"],
                overlaps: {
                  offerAllowAmounts: [
                    {
                      offer: 7028330,
                      rog: "SACG",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSEA",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSPK",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7029146,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029149,
                      rog: "SACG",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSEA",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSPK",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                  ],
                  unitizedOverlaps: {
                    netCostType: "UNIT",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 0.3,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0167,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0167,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.0833,
                      },
                    ],
                    scanAllow: 0.3,
                    caseAllow: 0.0833,
                    shipAllow: 0.0334,
                    allowSum: 0.4167,
                  },
                  shipCaseOverlaps: {
                    netCostType: "SHIP_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 1.8,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 1.8,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 2.5,
                  },
                  masterCaseOverlaps: {
                    netCostType: "MASTER_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 1.8,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 1.8,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 2.5,
                  },
                },
                bindMetrics: {
                  group: "AMOUNTS",
                  mode: "AMOUNTS_DEFAULT_BIND",
                  bind: true,
                },
                modCommand: "NONE",
              },
              {
                itemId: "2010289",
                itemDescription: "ANNIES BUNNY GRAHAMS CHOC CHIP          ",
                primaryUpc: "001356200018",
                consumerUpc: "001356200018",
                caseUpc: "0000000000000",
                itemUpcs: ["0000000000000", "001356200018"],
                consumerUpcs: [
                  {
                    upc: "001356200018",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356200018",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356200018",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                ],
                effectiveStartDate: "0001-12-26",
                effectiveEndDate: "9999-01-01",
                allowanceType: "CASE",
                packWhse: 12,
                ringType: 0,
                size: "7.5 OZ",
                vendorPackConversionFactor: 1,
                unitNetCosts: {
                  netCostType: "UNIT",
                  cost: 3.44,
                  costAllow: 3.0817,
                  initialAllowAmt: 0,
                  newCostAllow: 3.0817,
                },
                masterCaseNetCosts: {
                  netCostType: "MASTER_CASE",
                  cost: 41.28,
                  costAllow: 36.98,
                  initialAllowAmt: 0,
                  newCostAllow: 36.98,
                },
                shipCaseNetCosts: {
                  netCostType: "SHIP_CASE",
                  cost: 41.28,
                  costAllow: 36.98,
                  initialAllowAmt: 0,
                  newCostAllow: 36.98,
                },
                allowanceAmount: 0,
                allowUomType: "CA",
                allowUomTypes: ["CA"],
                overlaps: {
                  offerAllowAmounts: [
                    {
                      offer: 7028330,
                      rog: "SACG",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSEA",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSPK",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7029146,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029149,
                      rog: "SACG",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSEA",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSPK",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                  ],
                  unitizedOverlaps: {
                    netCostType: "UNIT",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 0.3,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0083,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0083,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.0417,
                      },
                    ],
                    scanAllow: 0.3,
                    caseAllow: 0.0417,
                    shipAllow: 0.0166,
                    allowSum: 0.3583,
                  },
                  shipCaseOverlaps: {
                    netCostType: "SHIP_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 3.6,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 3.6,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 4.3,
                  },
                  masterCaseOverlaps: {
                    netCostType: "MASTER_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 3.6,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 3.6,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 4.3,
                  },
                },
                bindMetrics: {
                  group: "AMOUNTS",
                  mode: "AMOUNTS_DEFAULT_BIND",
                  bind: true,
                },
                modCommand: "NONE",
              },
              {
                itemId: "2011373",
                itemDescription: "ANNIES GRAHAM CRACKER SMORES ORG        ",
                primaryUpc: "001356212213",
                consumerUpc: "001356212213",
                caseUpc: "0001356212213",
                itemUpcs: ["0001356212213", "001356212213"],
                consumerUpcs: [
                  {
                    upc: "001356212213",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356212213",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356212213",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                ],
                effectiveStartDate: "0001-12-26",
                effectiveEndDate: "9999-01-01",
                allowanceType: "CASE",
                packWhse: 12,
                ringType: 0,
                size: "7.5 OZ",
                vendorPackConversionFactor: 1,
                unitNetCosts: {
                  netCostType: "UNIT",
                  cost: 4.06,
                  costAllow: 3.7017,
                  initialAllowAmt: 0,
                  newCostAllow: 3.7017,
                },
                masterCaseNetCosts: {
                  netCostType: "MASTER_CASE",
                  cost: 48.72,
                  costAllow: 44.42,
                  initialAllowAmt: 0,
                  newCostAllow: 44.42,
                },
                shipCaseNetCosts: {
                  netCostType: "SHIP_CASE",
                  cost: 48.72,
                  costAllow: 44.42,
                  initialAllowAmt: 0,
                  newCostAllow: 44.42,
                },
                allowanceAmount: 0,
                allowUomType: "CA",
                allowUomTypes: ["CA"],
                overlaps: {
                  offerAllowAmounts: [
                    {
                      offer: 7028330,
                      rog: "SACG",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSEA",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSPK",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7029146,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029149,
                      rog: "SACG",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSEA",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSPK",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                  ],
                  unitizedOverlaps: {
                    netCostType: "UNIT",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 0.3,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0083,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0083,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.0417,
                      },
                    ],
                    scanAllow: 0.3,
                    caseAllow: 0.0417,
                    shipAllow: 0.0166,
                    allowSum: 0.3583,
                  },
                  shipCaseOverlaps: {
                    netCostType: "SHIP_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 3.6,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 3.6,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 4.3,
                  },
                  masterCaseOverlaps: {
                    netCostType: "MASTER_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 3.6,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 3.6,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 4.3,
                  },
                },
                bindMetrics: {
                  group: "AMOUNTS",
                  mode: "AMOUNTS_DEFAULT_BIND",
                  bind: true,
                },
                modCommand: "NONE",
              },
              {
                itemId: "2020143",
                itemDescription: "ANNIES CRACKERS WHT CHDR SQR ORG SNCK   ",
                primaryUpc: "001356249539",
                consumerUpc: "001356249539",
                caseUpc: "1001356249539",
                itemUpcs: ["001356249539", "1001356249539"],
                consumerUpcs: [
                  {
                    upc: "001356249539",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356249539",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356249539",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                ],
                effectiveStartDate: "0001-12-26",
                effectiveEndDate: "9999-01-01",
                allowanceType: "CASE",
                packWhse: 12,
                ringType: 0,
                size: "7.5 OZ",
                vendorPackConversionFactor: 1,
                unitNetCosts: {
                  netCostType: "UNIT",
                  cost: 4.06,
                  costAllow: 3.7017,
                  initialAllowAmt: 0,
                  newCostAllow: 3.7017,
                },
                masterCaseNetCosts: {
                  netCostType: "MASTER_CASE",
                  cost: 48.72,
                  costAllow: 44.42,
                  initialAllowAmt: 0,
                  newCostAllow: 44.42,
                },
                shipCaseNetCosts: {
                  netCostType: "SHIP_CASE",
                  cost: 48.72,
                  costAllow: 44.42,
                  initialAllowAmt: 0,
                  newCostAllow: 44.42,
                },
                allowanceAmount: 0,
                allowUomType: "CA",
                allowUomTypes: ["CA"],
                overlaps: {
                  offerAllowAmounts: [
                    {
                      offer: 7028330,
                      rog: "SACG",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSEA",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSPK",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7029146,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029149,
                      rog: "SACG",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSEA",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSPK",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                  ],
                  unitizedOverlaps: {
                    netCostType: "UNIT",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 0.3,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0083,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0083,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.0417,
                      },
                    ],
                    scanAllow: 0.3,
                    caseAllow: 0.0417,
                    shipAllow: 0.0166,
                    allowSum: 0.3583,
                  },
                  shipCaseOverlaps: {
                    netCostType: "SHIP_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 3.6,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 3.6,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 4.3,
                  },
                  masterCaseOverlaps: {
                    netCostType: "MASTER_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 3.6,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 3.6,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 4.3,
                  },
                },
                bindMetrics: {
                  group: "AMOUNTS",
                  mode: "AMOUNTS_DEFAULT_BIND",
                  bind: true,
                },
                modCommand: "NONE",
              },
              {
                itemId: "2020154",
                itemDescription: "ANNIES HIDDEN VEGGIES CHEESY CHEDDAR    ",
                primaryUpc: "001356212203",
                consumerUpc: "001356212203",
                caseUpc: "1001356212203",
                itemUpcs: ["001356212203", "1001356212203"],
                consumerUpcs: [
                  {
                    upc: "001356212203",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356212203",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356212203",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                ],
                effectiveStartDate: "0001-12-26",
                effectiveEndDate: "9999-01-01",
                allowanceType: "CASE",
                packWhse: 12,
                ringType: 0,
                size: "7.5 OZ",
                vendorPackConversionFactor: 1,
                unitNetCosts: {
                  netCostType: "UNIT",
                  cost: 4.05,
                  costAllow: 3.6917,
                  initialAllowAmt: 0,
                  newCostAllow: 3.6917,
                },
                masterCaseNetCosts: {
                  netCostType: "MASTER_CASE",
                  cost: 48.6,
                  costAllow: 44.3,
                  initialAllowAmt: 0,
                  newCostAllow: 44.3,
                },
                shipCaseNetCosts: {
                  netCostType: "SHIP_CASE",
                  cost: 48.6,
                  costAllow: 44.3,
                  initialAllowAmt: 0,
                  newCostAllow: 44.3,
                },
                allowanceAmount: 0,
                allowUomType: "CA",
                allowUomTypes: ["CA"],
                overlaps: {
                  offerAllowAmounts: [
                    {
                      offer: 7028330,
                      rog: "SACG",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSEA",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSPK",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7029146,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029149,
                      rog: "SACG",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSEA",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSPK",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                  ],
                  unitizedOverlaps: {
                    netCostType: "UNIT",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 0.3,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0083,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0083,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.0417,
                      },
                    ],
                    scanAllow: 0.3,
                    caseAllow: 0.0417,
                    shipAllow: 0.0166,
                    allowSum: 0.3583,
                  },
                  shipCaseOverlaps: {
                    netCostType: "SHIP_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 3.6,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 3.6,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 4.3,
                  },
                  masterCaseOverlaps: {
                    netCostType: "MASTER_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 3.6,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 3.6,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 4.3,
                  },
                },
                bindMetrics: {
                  group: "AMOUNTS",
                  mode: "AMOUNTS_DEFAULT_BIND",
                  bind: true,
                },
                modCommand: "NONE",
              },
              {
                itemId: "2020205",
                itemDescription: "ANNIES BUNNY GRAHAMS CHOCOLATE          ",
                primaryUpc: "001356200017",
                consumerUpc: "001356200017",
                caseUpc: "0000000000000",
                itemUpcs: ["0000000000000", "001356200017"],
                consumerUpcs: [
                  {
                    upc: "001356200017",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356200017",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356200017",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                ],
                effectiveStartDate: "0001-12-26",
                effectiveEndDate: "9999-01-01",
                allowanceType: "CASE",
                packWhse: 12,
                ringType: 0,
                size: "7.5 OZ",
                vendorPackConversionFactor: 1,
                unitNetCosts: {
                  netCostType: "UNIT",
                  cost: 4.06,
                  costAllow: 3.7017,
                  initialAllowAmt: 0,
                  newCostAllow: 3.7017,
                },
                masterCaseNetCosts: {
                  netCostType: "MASTER_CASE",
                  cost: 48.72,
                  costAllow: 44.42,
                  initialAllowAmt: 0,
                  newCostAllow: 44.42,
                },
                shipCaseNetCosts: {
                  netCostType: "SHIP_CASE",
                  cost: 48.72,
                  costAllow: 44.42,
                  initialAllowAmt: 0,
                  newCostAllow: 44.42,
                },
                allowanceAmount: 0,
                allowUomType: "CA",
                allowUomTypes: ["CA"],
                overlaps: {
                  offerAllowAmounts: [
                    {
                      offer: 7028330,
                      rog: "SACG",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSEA",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSPK",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7029146,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029149,
                      rog: "SACG",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSEA",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSPK",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                  ],
                  unitizedOverlaps: {
                    netCostType: "UNIT",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 0.3,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0083,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0083,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.0417,
                      },
                    ],
                    scanAllow: 0.3,
                    caseAllow: 0.0417,
                    shipAllow: 0.0166,
                    allowSum: 0.3583,
                  },
                  shipCaseOverlaps: {
                    netCostType: "SHIP_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 3.6,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 3.6,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 4.3,
                  },
                  masterCaseOverlaps: {
                    netCostType: "MASTER_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 3.6,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 3.6,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 4.3,
                  },
                },
                bindMetrics: {
                  group: "AMOUNTS",
                  mode: "AMOUNTS_DEFAULT_BIND",
                  bind: true,
                },
                modCommand: "NONE",
              },
              {
                itemId: "2020255",
                itemDescription: "ANNIES HOMEGROWN CHEDDAR SQUARES        ",
                primaryUpc: "001356249399",
                consumerUpc: "001356249399",
                caseUpc: "1001356249399",
                itemUpcs: ["001356249399", "1001356249399"],
                consumerUpcs: [
                  {
                    upc: "001356249399",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "11.25OZ",
                  },
                  {
                    upc: "001356249399",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "11.25OZ",
                  },
                  {
                    upc: "001356249399",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "11.25OZ",
                  },
                ],
                effectiveStartDate: "0001-12-26",
                effectiveEndDate: "9999-01-01",
                allowanceType: "CASE",
                packWhse: 6,
                ringType: 0,
                size: "11.25 OZ",
                vendorPackConversionFactor: 1,
                unitNetCosts: {
                  netCostType: "UNIT",
                  cost: 4.83,
                  costAllow: 4.4133,
                  initialAllowAmt: 0,
                  newCostAllow: 4.4133,
                },
                masterCaseNetCosts: {
                  netCostType: "MASTER_CASE",
                  cost: 28.98,
                  costAllow: 26.48,
                  initialAllowAmt: 0,
                  newCostAllow: 26.48,
                },
                shipCaseNetCosts: {
                  netCostType: "SHIP_CASE",
                  cost: 28.98,
                  costAllow: 26.48,
                  initialAllowAmt: 0,
                  newCostAllow: 26.48,
                },
                allowanceAmount: 0,
                allowUomType: "CA",
                allowUomTypes: ["CA"],
                overlaps: {
                  offerAllowAmounts: [
                    {
                      offer: 7028330,
                      rog: "SACG",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSEA",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSPK",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7029146,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029149,
                      rog: "SACG",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSEA",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSPK",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                  ],
                  unitizedOverlaps: {
                    netCostType: "UNIT",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 0.3,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0167,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0167,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.0833,
                      },
                    ],
                    scanAllow: 0.3,
                    caseAllow: 0.0833,
                    shipAllow: 0.0334,
                    allowSum: 0.4167,
                  },
                  shipCaseOverlaps: {
                    netCostType: "SHIP_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 1.8,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 1.8,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 2.5,
                  },
                  masterCaseOverlaps: {
                    netCostType: "MASTER_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 1.8,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 1.8,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 2.5,
                  },
                },
                bindMetrics: {
                  group: "AMOUNTS",
                  mode: "AMOUNTS_DEFAULT_BIND",
                  bind: true,
                },
                modCommand: "NONE",
              },
              {
                itemId: "2020257",
                itemDescription: "ANNIES HOMEGROWN CHED BUNNY BIG BOX ORG ",
                primaryUpc: "001356249401",
                consumerUpc: "001356249401",
                caseUpc: "1001356249401",
                itemUpcs: ["001356249401", "1001356249401"],
                consumerUpcs: [
                  {
                    upc: "001356249401",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "11.25OZ",
                  },
                  {
                    upc: "001356249401",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "11.25OZ",
                  },
                  {
                    upc: "001356249401",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "006",
                    sizeDesc: "11.25OZ",
                  },
                ],
                effectiveStartDate: "0001-12-26",
                effectiveEndDate: "9999-01-01",
                allowanceType: "CASE",
                packWhse: 6,
                ringType: 0,
                size: "11.25 OZ",
                vendorPackConversionFactor: 1,
                unitNetCosts: {
                  netCostType: "UNIT",
                  cost: 4.83,
                  costAllow: 4.4133,
                  initialAllowAmt: 0,
                  newCostAllow: 4.4133,
                },
                masterCaseNetCosts: {
                  netCostType: "MASTER_CASE",
                  cost: 28.98,
                  costAllow: 26.48,
                  initialAllowAmt: 0,
                  newCostAllow: 26.48,
                },
                shipCaseNetCosts: {
                  netCostType: "SHIP_CASE",
                  cost: 28.98,
                  costAllow: 26.48,
                  initialAllowAmt: 0,
                  newCostAllow: 26.48,
                },
                allowanceAmount: 0,
                allowUomType: "CA",
                allowUomTypes: ["CA"],
                overlaps: {
                  offerAllowAmounts: [
                    {
                      offer: 7028330,
                      rog: "SACG",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSEA",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSPK",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7029146,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029149,
                      rog: "SACG",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSEA",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSPK",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                  ],
                  unitizedOverlaps: {
                    netCostType: "UNIT",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 0.3,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0167,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0167,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.0833,
                      },
                    ],
                    scanAllow: 0.3,
                    caseAllow: 0.0833,
                    shipAllow: 0.0334,
                    allowSum: 0.4167,
                  },
                  shipCaseOverlaps: {
                    netCostType: "SHIP_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 1.8,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 1.8,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 2.5,
                  },
                  masterCaseOverlaps: {
                    netCostType: "MASTER_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 1.8,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 1.8,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 2.5,
                  },
                },
                bindMetrics: {
                  group: "AMOUNTS",
                  mode: "AMOUNTS_DEFAULT_BIND",
                  bind: true,
                },
                modCommand: "NONE",
              },
              {
                itemId: "2020393",
                itemDescription: "ANNIES CRACKER HOMEGROWN CHEDDAR SQUARES",
                primaryUpc: "001356200053",
                consumerUpc: "001356200053",
                caseUpc: "0000000000000",
                itemUpcs: ["0000000000000", "001356200053"],
                consumerUpcs: [
                  {
                    upc: "001356200053",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356200053",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356200053",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                ],
                effectiveStartDate: "0001-12-26",
                effectiveEndDate: "9999-01-01",
                allowanceType: "CASE",
                packWhse: 12,
                ringType: 0,
                size: "7.5 OZ",
                vendorPackConversionFactor: 1,
                unitNetCosts: {
                  netCostType: "UNIT",
                  cost: 3.44,
                  costAllow: 3.0817,
                  initialAllowAmt: 0,
                  newCostAllow: 3.0817,
                },
                masterCaseNetCosts: {
                  netCostType: "MASTER_CASE",
                  cost: 41.28,
                  costAllow: 36.98,
                  initialAllowAmt: 0,
                  newCostAllow: 36.98,
                },
                shipCaseNetCosts: {
                  netCostType: "SHIP_CASE",
                  cost: 41.28,
                  costAllow: 36.98,
                  initialAllowAmt: 0,
                  newCostAllow: 36.98,
                },
                allowanceAmount: 0,
                allowUomType: "CA",
                allowUomTypes: ["CA"],
                overlaps: {
                  offerAllowAmounts: [
                    {
                      offer: 7028330,
                      rog: "SACG",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSEA",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSPK",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7029146,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029149,
                      rog: "SACG",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSEA",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSPK",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                  ],
                  unitizedOverlaps: {
                    netCostType: "UNIT",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 0.3,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0083,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0083,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.0417,
                      },
                    ],
                    scanAllow: 0.3,
                    caseAllow: 0.0417,
                    shipAllow: 0.0166,
                    allowSum: 0.3583,
                  },
                  shipCaseOverlaps: {
                    netCostType: "SHIP_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 3.6,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 3.6,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 4.3,
                  },
                  masterCaseOverlaps: {
                    netCostType: "MASTER_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 3.6,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 3.6,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 4.3,
                  },
                },
                bindMetrics: {
                  group: "AMOUNTS",
                  mode: "AMOUNTS_DEFAULT_BIND",
                  bind: true,
                },
                modCommand: "NONE",
              },
              {
                itemId: "2021543",
                itemDescription: "ANNIES HONEY GRAHAMS                    ",
                primaryUpc: "001356200052",
                consumerUpc: "001356200052",
                caseUpc: "0000000000000",
                itemUpcs: ["0000000000000", "001356200052"],
                consumerUpcs: [
                  {
                    upc: "001356200052",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "14.4 OZ",
                  },
                  {
                    upc: "001356200052",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "14.4 OZ",
                  },
                  {
                    upc: "001356200052",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "14.4 OZ",
                  },
                ],
                effectiveStartDate: "0001-12-26",
                effectiveEndDate: "9999-01-01",
                allowanceType: "CASE",
                packWhse: 12,
                ringType: 0,
                size: "14.4 OZ",
                vendorPackConversionFactor: 1,
                unitNetCosts: {
                  netCostType: "UNIT",
                  cost: 5.03,
                  costAllow: 4.6717,
                  initialAllowAmt: 0,
                  newCostAllow: 4.6717,
                },
                masterCaseNetCosts: {
                  netCostType: "MASTER_CASE",
                  cost: 60.36,
                  costAllow: 56.06,
                  initialAllowAmt: 0,
                  newCostAllow: 56.06,
                },
                shipCaseNetCosts: {
                  netCostType: "SHIP_CASE",
                  cost: 60.36,
                  costAllow: 56.06,
                  initialAllowAmt: 0,
                  newCostAllow: 56.06,
                },
                allowanceAmount: 0,
                allowUomType: "CA",
                allowUomTypes: ["CA"],
                overlaps: {
                  offerAllowAmounts: [
                    {
                      offer: 7028330,
                      rog: "SACG",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSEA",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSPK",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7029146,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029149,
                      rog: "SACG",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSEA",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSPK",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                  ],
                  unitizedOverlaps: {
                    netCostType: "UNIT",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 0.3,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0083,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0083,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.0417,
                      },
                    ],
                    scanAllow: 0.3,
                    caseAllow: 0.0417,
                    shipAllow: 0.0166,
                    allowSum: 0.3583,
                  },
                  shipCaseOverlaps: {
                    netCostType: "SHIP_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 3.6,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 3.6,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 4.3,
                  },
                  masterCaseOverlaps: {
                    netCostType: "MASTER_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 3.6,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 3.6,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 4.3,
                  },
                },
                bindMetrics: {
                  group: "AMOUNTS",
                  mode: "AMOUNTS_DEFAULT_BIND",
                  bind: true,
                },
                modCommand: "NONE",
              },
              {
                itemId: "2021577",
                itemDescription: "ANNIES HOMEGROWN GRAHAM CRACKER CINNAMON",
                primaryUpc: "001356200051",
                consumerUpc: "001356200051",
                caseUpc: "0000000000000",
                itemUpcs: ["0000000000000", "001356200051"],
                consumerUpcs: [
                  {
                    upc: "001356200051",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "14.4 OZ",
                  },
                  {
                    upc: "001356200051",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "14.4 OZ",
                  },
                  {
                    upc: "001356200051",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "14.4 OZ",
                  },
                ],
                effectiveStartDate: "0001-12-26",
                effectiveEndDate: "9999-01-01",
                allowanceType: "CASE",
                packWhse: 12,
                ringType: 0,
                size: "14.4 OZ",
                vendorPackConversionFactor: 1,
                unitNetCosts: {
                  netCostType: "UNIT",
                  cost: 5.03,
                  costAllow: 4.6717,
                  initialAllowAmt: 0,
                  newCostAllow: 4.6717,
                },
                masterCaseNetCosts: {
                  netCostType: "MASTER_CASE",
                  cost: 60.36,
                  costAllow: 56.06,
                  initialAllowAmt: 0,
                  newCostAllow: 56.06,
                },
                shipCaseNetCosts: {
                  netCostType: "SHIP_CASE",
                  cost: 60.36,
                  costAllow: 56.06,
                  initialAllowAmt: 0,
                  newCostAllow: 56.06,
                },
                allowanceAmount: 0,
                allowUomType: "CA",
                allowUomTypes: ["CA"],
                overlaps: {
                  offerAllowAmounts: [
                    {
                      offer: 7028330,
                      rog: "SACG",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSEA",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7028330,
                      rog: "SSPK",
                      basisDsc: "T",
                      amount: 0.3,
                    },
                    {
                      offer: 7029146,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029146,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SACG",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSEA",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029148,
                      rog: "SSPK",
                      basisDsc: "S",
                      amount: 0.1,
                    },
                    {
                      offer: 7029149,
                      rog: "SACG",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSEA",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                    {
                      offer: 7029149,
                      rog: "SSPK",
                      basisDsc: "C",
                      amount: 0.5,
                    },
                  ],
                  unitizedOverlaps: {
                    netCostType: "UNIT",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 0.3,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0083,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.0083,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.0417,
                      },
                    ],
                    scanAllow: 0.3,
                    caseAllow: 0.0417,
                    shipAllow: 0.0166,
                    allowSum: 0.3583,
                  },
                  shipCaseOverlaps: {
                    netCostType: "SHIP_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 3.6,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 3.6,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 4.3,
                  },
                  masterCaseOverlaps: {
                    netCostType: "MASTER_CASE",
                    convertedAllowanceAmounts: [
                      {
                        offer: 7028330,
                        basisDsc: "T",
                        amount: 0.3,
                        convertedAmount: 3.6,
                      },
                      {
                        offer: 7029146,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029148,
                        basisDsc: "S",
                        amount: 0.1,
                        convertedAmount: 0.1,
                      },
                      {
                        offer: 7029149,
                        basisDsc: "C",
                        amount: 0.5,
                        convertedAmount: 0.5,
                      },
                    ],
                    scanAllow: 3.6,
                    caseAllow: 0.5,
                    shipAllow: 0.2,
                    allowSum: 4.3,
                  },
                },
                bindMetrics: {
                  group: "AMOUNTS",
                  mode: "AMOUNTS_DEFAULT_BIND",
                  bind: true,
                },
                modCommand: "NONE",
              },
            ],
            headerFlatAmt: 0,
            allowanceStatus: "Draft",
            storeGroups: [],
            leadDistributorInd: false,
            includeInd: true,
            finalizedAmountsInd: false,
            dateBindMetrics: {
              group: "DATES",
              mode: "DATES_CASE_BIND",
              bindValues: {
                srcDates: {
                  allow: {
                    startDate: "2024-10-09",
                    endDate: "2024-10-15",
                  },
                  order: {
                    startDate: "2024-10-06",
                    endDate: "2024-10-15",
                  },
                  ship: {
                    startDate: "2024-10-06",
                    endDate: "2024-10-15",
                  },
                  arrival: {
                    startDate: "2024-10-06",
                    endDate: "2024-10-15",
                  },
                },
                dstDates: {
                  allow: {
                    startDate: "2024-10-09",
                    endDate: "2024-10-15",
                  },
                  order: {
                    startDate: "2024-10-06",
                    endDate: "2024-10-15",
                  },
                  ship: {
                    startDate: "2024-10-06",
                    endDate: "2024-10-15",
                  },
                  arrival: {
                    startDate: "2024-10-06",
                    endDate: "2024-10-15",
                  },
                },
              },
              bind: true,
            },
            excludedItems: [
              {
                itemId: "********",
                excludeInd: "NO_VENDOR_LOCATION",
              },
              {
                itemId: "2020118",
                excludeInd: "NO_VENDOR_LOCATION",
              },
              {
                itemId: "2020251",
                excludeInd: "NO_VENDOR_LOCATION",
              },
            ],
            allowanceProcessStatus: "",
            allowDownstreamStatus: {},
            leadDistributorMode: null,
            modCommand: "NONE",
          },
          {
            vendorNbr: "**********-1",
            vendorName: "R & K FOODS INC                         ",
            costAreaDesc: "SAFEWAY",
            createAllowInd: true,
            includeInd: true,
            leadDistributorInd: true,
            leadDistributorMode: null,
            leadDistributorInfos: [],
            headerFlatAmt: 0,
            distCenter: "DDSE",
            modCommand: "ZERO_COST",
            caseListCost: 0,
            unitListCost: 0,
            unitCostAllow: "0.00",
            allowanceAmount: "",
            allowUomType: "CA",
            newUnitCostAllow: "0.00",
            caseCostAllow: "0.00",
            newCaseCostAllow: "0.00",
            shippingCost: "",
          },
        ],
      },
    ];
    const leadOptions = [
      {
        vendorNbr: "**********-1",
        costAreaDesc: "MAINLAND",
        vendorName: "KEHE DISTRIBUTORS                       ",
      },
      {
        vendorNbr: "**********-2",
        costAreaDesc: "ALASKA",
        vendorName: "KEHE DISTRIBUTORS                       ",
      },
      {
        vendorNbr: "**********-4",
        costAreaDesc: "SWEETENED BEV TAX STORES",
        vendorName: "KEHE DISTRIBUTORS                       ",
      },
      {
        vendorNbr: "**********-1",
        costAreaDesc: "JBG STORES",
        vendorName: "KEHE DISTRIBUTORS {JBG WHLSALE}         ",
      },
      {
        vendorNbr: "**********-1",
        costAreaDesc: "SAFEWAY",
        vendorName: "R & K FOODS INC                         ",
      },
      {
        vendorNbr: "**********-3",
        costAreaDesc: "SWEETENED BEV TAX STORES",
        vendorName: "R & K FOODS INC                         ",
      },
      {
        vendorNbr: "**********-1",
        costAreaDesc: "WESTERN WASHINGTON",
        vendorName: "TONYS COFFEE & TEAS INC                 ",
      },
    ];
    const setExternalVendData = jest.fn();
    // Act
    getExternalDistDetails(tableData, leadOptions, setExternalVendData);
    // expect(getMainAndOtherVendors).toHaveBeenCalledWith(
    //   tableData,
    //   ['vendor1', 'vendor2']
    // );
    expect(setExternalVendData).toHaveBeenCalledWith({
      mainVendors: [
        "**********-1",
        "**********-2",
        "**********-4",
        "**********-1",
        "**********-3",
        "**********-1",
      ],
      otherVendors: ["**********-1"],
      isExternal: true,
    });
  });
});

describe('updateLeadData', () => {
  it('should add a new vendor when not adding and not an external vendor', () => {
    const vendorNbr = 'vendor1';
    const leadDistData = [{ id: 'vendor2', child: [] }];
    const isAdding = false;
    const isExternalVendor = false;

    const result = updateLeadData(vendorNbr, leadDistData, isAdding, isExternalVendor);

    expect(result).toEqual([
      { id: 'vendor2', child: [] },
      { id: 'vendor1', child: [] }
    ]);
  });

  it('should handle external vendors correctly when not adding', () => {
    const vendorNbr = 'vendor1';
    const leadDistData = [{ id: 'vendor2', child: [] }];
    const isAdding = false;
    const isExternalVendor = true;

    const result = updateLeadData(vendorNbr, leadDistData, isAdding, isExternalVendor);

    expect(result).toEqual([{ id: 'vendor2', child: ['vendor1'] }]);
  });

  it('should remove a vendor from children when adding', () => {
    const vendorNbr = 'vendor1';
    const leadDistData = [{ id: 'vendor2', child: ['vendor1'] }];
    const isAdding = true;
    const isExternalVendor = false;

    const result = updateLeadData(vendorNbr, leadDistData, isAdding, isExternalVendor);

    expect(result).toEqual([{ id: 'vendor2', child: [] }]);
  });
});

describe('handleReIncludeExternalVend', () => {
  it('should add vendor to the child array if not already present', () => {
    const vendorNbr = 'vendor1';
    const leadDistData = [{ id: 'vendor2', child: [] }];

    const result = handleReIncludeExternalVend(leadDistData, vendorNbr);

    expect(result).toEqual([{ id: 'vendor2', child: ['vendor1'] }]);
  });

  it('should not modify leadDistData if vendor is already a child', () => {
    const vendorNbr = 'vendor1';
    const leadDistData = [{ id: 'vendor2', child: ['vendor1'] }];

    const result = handleReIncludeExternalVend(leadDistData, vendorNbr);

    expect(result).toEqual(leadDistData);
  });

  it('should return the same data if leadDistData is empty', () => {
    const vendorNbr = 'vendor1';
    const leadDistData = [];

    const result = handleReIncludeExternalVend(leadDistData, vendorNbr);

    expect(result).toEqual([]);
  });
});

describe('setLeadsForOwnDistOnReInclude', () => {
  it("should dispatch the correct actions when we reinclude the internal vendor", () => {
    const dispatch = jest.fn();
    const checked = false;
    const leadDistData = [
      {
        child: [
          "**********-4",
          "**********-1",
          "**********-1",
          "**********-3",
          "**********-1",
        ],
        id: "**********-1",
      },
    ];
    const vendorNbr = "**********-2";
    const leadDistSliceData = {
      stepData: [
        {
          child: [
            "**********-4",
            "**********-1",
            "**********-1",
            "**********-3",
            "**********-1",
          ],
          id: "**********-1",
        },
      ],
      leadOptions: [
        {
          vendorNbr: "**********-1",
          costAreaDesc: "MAINLAND",
          vendorName: "KEHE DISTRIBUTORS                       ",
        },

        {
          vendorNbr: "**********-2",
          costAreaDesc: "ALASKA",
          vendorName: "KEHE DISTRIBUTORS                       ",
        },

        {
          vendorNbr: "**********-4",
          costAreaDesc: "SWEETENED BEV TAX STORES",
          vendorName: "KEHE DISTRIBUTORS                       ",
        },
        {
          vendorNbr: "**********-1",
          costAreaDesc: "JBG STORES",
          vendorName: "KEHE DISTRIBUTORS {JBG WHLSALE}         ",
        },

        {
          vendorNbr: "**********-1",
          costAreaDesc: "SAFEWAY",
          vendorName: "R & K FOODS INC                         ",
        },

        {
          vendorNbr: "**********-3",
          costAreaDesc: "SWEETENED BEV TAX STORES",
          vendorName: "R & K FOODS INC                         ",
        },

        {
          vendorNbr: "**********-1",
          costAreaDesc: "WESTERN WASHINGTON",
          vendorName: "TONYS COFFEE & TEAS INC                 ",
        },
      ],
      isLeadChange: false,
      leadDistMode: "BILL_LEAD_DIST",
    };
    const isExternalVendor = false;

    setLeadsForOwnDistOnReInclude({
      dispatch,
      checked,
      leadDistData,
      vendorNbr,
      leadDistSliceData,
      isExternalVendor,
    });

    expect(dispatch).toHaveBeenCalledWith({
      type: "initialLeadDistSelected_rn/setInitialLeadDistData",
      payload: {
        leadDistData: [
          {
            id: "**********-1",
            child: [
              "**********-4",
              "**********-1",
              "**********-1",
              "**********-3",
              "**********-1",
            ],
          },
          { id: "**********-2", child: [] },
        ],
      },
    });

    expect(dispatch).toHaveBeenCalledWith({
      type: "leadDistributors_rn/leadDistributorsHandler",
      payload: {
        ...leadDistSliceData,
        stepData: [
          {
            child: [
              "**********-4",
              "**********-1",
              "**********-1",
              "**********-3",
              "**********-1",
            ],
            id: "**********-1",
          },
          {
            id: "**********-2",
            child: [],
          },
        ],
      },
    });
  });
  it("should dispatch the correct actions when we reinclude the external vendor", () => {
    const dispatch = jest.fn();
    const checked = false;
    const leadDistData = [
      {
        child: ["**********-4", "**********-1", "**********-3", "**********-1"],
        id: "**********-1",
      },
    ];
    const vendorNbr = "**********-1";
    const leadDistSliceData = {
      stepData: [
        {
          child: [
            "**********-4",
            "**********-1",
            "**********-3",
            "**********-1",
          ],
          id: "**********-1",
        },
      ],
      leadOptions: [
        {
          vendorNbr: "**********-1",
          costAreaDesc: "MAINLAND",
          vendorName: "KEHE DISTRIBUTORS                       ",
        },

        {
          vendorNbr: "**********-2",
          costAreaDesc: "ALASKA",
          vendorName: "KEHE DISTRIBUTORS                       ",
        },

        {
          vendorNbr: "**********-4",
          costAreaDesc: "SWEETENED BEV TAX STORES",
          vendorName: "KEHE DISTRIBUTORS                       ",
        },
        {
          vendorNbr: "**********-1",
          costAreaDesc: "JBG STORES",
          vendorName: "KEHE DISTRIBUTORS {JBG WHLSALE}         ",
        },

        {
          vendorNbr: "**********-1",
          costAreaDesc: "SAFEWAY",
          vendorName: "R & K FOODS INC                         ",
        },

        {
          vendorNbr: "**********-3",
          costAreaDesc: "SWEETENED BEV TAX STORES",
          vendorName: "R & K FOODS INC                         ",
        },

        {
          vendorNbr: "**********-1",
          costAreaDesc: "WESTERN WASHINGTON",
          vendorName: "TONYS COFFEE & TEAS INC                 ",
        },
      ],
      isLeadChange: false,
      leadDistMode: "BILL_LEAD_DIST",
    };
    const isExternalVendor = true;
    setLeadsForOwnDistOnReInclude({
      dispatch,
      checked,
      leadDistData,
      vendorNbr,
      leadDistSliceData,
      isExternalVendor,
    });
    expect(dispatch).toHaveBeenCalledWith({
      type: "initialLeadDistSelected_rn/setInitialLeadDistData",
      payload: {
        leadDistData: [
          {
            id: "**********-1",
            child: [
              "**********-4",
              "**********-1",
              "**********-3",
              "**********-1",
              "**********-1",
            ],
          },
        ],
      },
    });

    expect(dispatch).toHaveBeenCalledWith({
      type: "leadDistributors_rn/leadDistributorsHandler",
      payload: {
        ...leadDistSliceData,
        stepData: [
          {
            child: [
              "**********-4",
              "**********-1",
              "**********-3",
              "**********-1",
              "**********-1",
            ],
            id: "**********-1",
          },
        ],
      },
    });
  });
  it("should dispatch the correct actions when we exclude vendor", () => {
    const dispatch = jest.fn();
    const checked = true;
    const leadDistData = [
      {
        child: [
          "**********-2",
          "**********-4",
          "**********-1",
          "**********-1",
          "**********-3",
          "**********-1",
        ],
        id: "**********-1",
      },
    ];
    const vendorNbr = "**********-2";
    const leadDistSliceData = {
      stepData: [
        {
          child: [
            "**********-2",
            "**********-4",
            "**********-1",
            "**********-1",
            "**********-3",
            "**********-1",
          ],
          id: "**********-1",
        },
      ],
      leadOptions: [
        {
          vendorNbr: "**********-1",
          costAreaDesc: "MAINLAND",
          vendorName: "KEHE DISTRIBUTORS                       ",
        },

        {
          vendorNbr: "**********-2",
          costAreaDesc: "ALASKA",
          vendorName: "KEHE DISTRIBUTORS                       ",
        },

        {
          vendorNbr: "**********-4",
          costAreaDesc: "SWEETENED BEV TAX STORES",
          vendorName: "KEHE DISTRIBUTORS                       ",
        },
        {
          vendorNbr: "**********-1",
          costAreaDesc: "JBG STORES",
          vendorName: "KEHE DISTRIBUTORS {JBG WHLSALE}         ",
        },

        {
          vendorNbr: "**********-1",
          costAreaDesc: "SAFEWAY",
          vendorName: "R & K FOODS INC                         ",
        },

        {
          vendorNbr: "**********-3",
          costAreaDesc: "SWEETENED BEV TAX STORES",
          vendorName: "R & K FOODS INC                         ",
        },

        {
          vendorNbr: "**********-1",
          costAreaDesc: "WESTERN WASHINGTON",
          vendorName: "TONYS COFFEE & TEAS INC                 ",
        },
      ],
      isLeadChange: false,
      leadDistMode: "BILL_LEAD_DIST",
    };
    const isExternalVendor = false;

    setLeadsForOwnDistOnReInclude({
      dispatch,
      checked,
      leadDistData,
      vendorNbr,
      leadDistSliceData,
      isExternalVendor,
    });
    expect(dispatch).toHaveBeenCalledWith({
      type: "initialLeadDistSelected_rn/setInitialLeadDistData",
      payload: {
        leadDistData: [
          {
            id: "**********-1",
            child: [
              "**********-4",
              "**********-1",
              "**********-1",
              "**********-3",
              "**********-1",
            ],
          },
        ],
      },
    });

    expect(dispatch).toHaveBeenCalledWith({
      type: "leadDistributors_rn/leadDistributorsHandler",
      payload: {
        ...leadDistSliceData,
        stepData: [
          {
            child: [
              "**********-4",
              "**********-1",
              "**********-1",
              "**********-3",
              "**********-1",
            ],
            id: "**********-1",
          },
        ],
      },
    });
  });
});

describe('updatedLeadDistSet', () => {
  it('should return updated lead distribution set', () => {
    const mockLeadDistSets = [
  {
    "id": "**********-1",
    "child": [
      "**********-2",
      "**********-4",
      "**********-1",
      "**********-3",
      "**********-1"
    ]
  },
  {
    "id": "**********-1",
    "child": []
  }
];
    const mockId = '**********-1';
    const mockDirection = 'DOWN';


    const result = updatedLeadDistSet(mockLeadDistSets, mockId, mockDirection);

    const expected = [{
      "id": "**********-1",
      "child": [
        "**********-2",
        "**********-4",
        "**********-1",
        "**********-3"
      ]
    },
    {
      "id": "**********-1",
      "child": ["**********-1"]
    }];

    expect(result).toEqual(expected);
  });
});

describe('moveItemBetweenParents', () => {
  it('should move item between parents', () => {
    const mockFromVal = { id: 'fromId', child: ['item1', 'item2'] };
    const mockToVal = { id: 'toId', child: [] };
    const mockId = 'item1';
    const mockLeadDistSets = [mockFromVal, mockToVal];

    const result = moveItemBetweenParents(mockFromVal, mockToVal, mockId, mockLeadDistSets);

    const expected = [
      { id: 'fromId', child: ['item2'] },
      { id: 'toId', child: ['item1'] },
    ];

    expect(result).toEqual(expected);
  });
});

describe('disableIntrnlVendor', () => {
  it('should return true if the number of main vendors minus the number of excluded vendors is less than or equal to 1', () => {
    const mockExternalvendData = {isAllExternal: false, mainVendors: ['vendor1', 'vendor2', 'vendor3'] };
    const mockExcludedVendors = [{ vendorNbr: 'vendor1' }, { vendorNbr: 'vendor2' }];

    const result = disableIntrnlVendor(mockExternalvendData, mockExcludedVendors);

    expect(result).toBe(true);
  });

  it('should return false if the number of main vendors minus the number of excluded vendors is greater than 1', () => {
    const mockExternalvendData = {isAllExternal: false, mainVendors: ['vendor1', 'vendor2', 'vendor3', 'vendor4'] };
    const mockExcludedVendors = [{ vendorNbr: 'vendor1' }];

    const result = disableIntrnlVendor(mockExternalvendData, mockExcludedVendors);

    expect(result).toBe(false);
  });
});
describe('leadDistError', () => {
  it('should render error message if isLeadDistributorError is true', () => {
    const { getByText } = render(leadDistError(true));
    expect(getByText(efConstants.LEAD_DIST_ERR_MSG)).toBeInTheDocument();
  });

  it('should not render error message if isLeadDistributorError is false', () => {
    const { queryByText } = render(leadDistError(false));
    expect(queryByText(efConstants.LEAD_DIST_ERR_MSG)).not.toBeInTheDocument();
  });
});

describe('isInValidLeadDists', () => {
  it('should return true if any item has more than 2 vendors with leadDistributorInd', () => {
    const mockTableData = [
      { vendorDetails: [{ leadDistributorInd: true }, { leadDistributorInd: true }, { leadDistributorInd: true }] },
      { vendorDetails: [{ leadDistributorInd: false }] },
    ];

    const result = isInValidLeadDists(mockTableData);

    expect(result).toBe(true);
  });

  it('should return false if no item has more than 2 vendors with leadDistributorInd', () => {
    const mockTableData = [
      { vendorDetails: [{ leadDistributorInd: true }, { leadDistributorInd: true }] },
      { vendorDetails: [{ leadDistributorInd: false }] },
    ];

    const result = isInValidLeadDists(mockTableData);

    expect(result).toBe(false);
  });
});
describe("checkIfAnyExternalVendorPresent", () => {
  it("returns false when no external vendor is present", () => {
    const vendors = [
      {
        allowanceItems: [{}],
      },
      {
        allowanceItems: [{}],
      },
    ];

    const result = checkIfAnyExternalVendorPresent(vendors);

    expect(result).toBe(false);
  });

  it("returns true when at least one external vendor is present", () => {
    const vendors = [
      {
        allowanceItems: [
          {
            modCommand: "ZERO_COST",
          },
        ],
      },
      {
        allowanceItems: [{}],
      },
    ];

    const result = checkIfAnyExternalVendorPresent(vendors);

    expect(result).toBe(true);
  });
});

describe("tableDataToVendorsArray", () => {
  it("returns an array of vendors from the table data", () => {
    const tableData = [
      {
        vendorDetails: [
          {
            vendorNbr: "vendor1",
          },
          {
            vendorNbr: "vendor2",
          },
        ],
      },
      {
        vendorDetails: [
          {
            vendorNbr: "vendor3",
          },
        ],
      },
    ];

    const result = tableDataToVendorsArray(tableData);

    expect(result).toEqual([
      {
        vendorNbr: "vendor1",
      },
      {
        vendorNbr: "vendor2",
      },
      {
        vendorNbr: "vendor3",
      },
    ]);
  });
});

describe("validateToShowExternalVendorOption", () => {
  it("returns false when no external vendor is present", () => {
    const tableData = [
      {
        vendorDetails: [
          {
            vendorNbr: "vendor1",
            allowanceItems: [
              {
                modCommand: "SOME_OTHER_COMMAND",
              },
            ],
          },
        ],
      },
      {
        vendorDetails: [
          {
            vendorNbr: "vendor2",
            allowanceItems: [
              {
                modCommand: "SOME_OTHER_COMMAND",
              },
            ],
          },
        ],
      },
    ];

    const vendors = tableDataToVendorsArray(tableData);
    const result = validateToShowExternalVendorOption(vendors);

    expect(result).toBe(false);
  });

  it("returns true when at least one external vendor is present and billing details are not common", () => {
    const tableData = [
      {
        vendorDetails: [
          {
            vendorNbr: "vendor1",
            allowanceItems: [
              {
                modCommand: "ZERO_COST",
              },
            ],
            allowanceBillingInfo: {
              acPayableVendorNbr: "123",
              acReceivableVendorNbr: "456",
            },
          },
        ],
      },
      {
        vendorDetails: [
          {
            vendorNbr: "vendor2",
            allowanceItems: [
              {
                modCommand: "SOME_OTHER_COMMAND",
              },
            ],
            allowanceBillingInfo: {
              acPayableVendorNbr: "789",
              acReceivableVendorNbr: "012",
            },
          },
        ],
      },
      {
        vendorDetails: [
          {
            vendorNbr: "vendor3",
            allowanceItems: [
              {
                modCommand: "SOME_OTHER_COMMAND",
              },
            ],
            allowanceBillingInfo: {
              acPayableVendorNbr: "7890",
              acReceivableVendorNbr: "0123",
            },
          },
        ],
      },
    ];

    const vendors = tableDataToVendorsArray(tableData);
    const result = validateToShowExternalVendorOption(vendors);

    expect(result).toBe(true);
  });

  it("returns false when at least one external vendor is present and billing details are common", () => {
    const tableData = [
      {
        vendorDetails: [
          {
            vendorNbr: "vendor1",
            allowanceItems: [
              {
                modCommand: "ZERO_COST",
              },
            ],
            allowanceBillingInfo: {
              acPayableVendorNbr: "123",
              acReceivableVendorNbr: "456",
            },
          },
        ],
      },
      {
        vendorDetails: [
          {
            vendorNbr: "vendor2",
            allowanceItems: [
              {
                modCommand: "SOME_OTHER_COMMAND",
              },
            ],
            allowanceBillingInfo: {
              acPayableVendorNbr: "123",
              acReceivableVendorNbr: "456",
            },
          },
        ],
      },
    ];

    const vendors = tableDataToVendorsArray(tableData);
    const result = validateToShowExternalVendorOption(vendors);

    expect(result).toBe(false);
  });

  it("returns false when at least one external vendor is present and billing details are common, single internal", () => {
    const tableData = [
      {
        vendorDetails: [
          {
            vendorNbr: "vendor1",
            allowanceItems: [
              {
                modCommand: "ZERO_COST",
              },
            ],
            allowanceBillingInfo: {
              acPayableVendorNbr: "123",
              acReceivableVendorNbr: "456",
            },
          },
        ],
      },
      {
        vendorDetails: [
          {
            vendorNbr: "vendor2",
            allowanceItems: [
              {
                modCommand: "SOME_OTHER_COMMAND",
              },
            ],
            allowanceBillingInfo: {
              acPayableVendorNbr: "123",
              acReceivableVendorNbr: "456",
            },
          },
        ],
      },
      {
        vendorDetails: [
          {
            vendorNbr: "vendor3",
            allowanceItems: [
              {
                modCommand: "SOME_OTHER_COMMAND",
              },
            ],
            allowanceBillingInfo: {
              acPayableVendorNbr: "123",
              acReceivableVendorNbr: "456",
            },
          },
        ],
      },
    ];

    const vendors = tableDataToVendorsArray(tableData);
    const result = validateToShowExternalVendorOption(vendors);

    expect(result).toBe(false);
  });
});

describe("getLeadDistributorInitialState", () => {
  it("returns 'ALL' when defaultMode is falsy", () => {
    const tableData = [
      { vendorDetails: [{ vendorNbr: "vendor1", leadDistributorMode: "" }] },
    ];

    const result = getLeadDistributorInitialState(tableData);

    expect(result).toBe("ALL");
  });

  it("returns ' LEADS' when defaultMode is 'BILL_LEAD_DIST' and all other vendors are not assigned leads", () => {
    const tableData = [
      {
        vendorDetails: [
          {
            vendorNbr: "vendor1",
            leadDistributorMode: "BILL_LEAD_DIST",
            allowanceItems: [{ modCommand: efConstants.ZERO_COST_TEXT }],
          },
          {
            vendorNbr: "vendor2",
            leadDistributorMode: "BILL_LEAD_DIST",
            allowanceItems: [{ modCommand: "NONE" }],
            leadDistributorInfos: [{ vendorNbr: "vendor1" }],
          },
          {
            vendorNbr: "vendor3",
            leadDistributorMode: "BILL_LEAD_DIST",
            allowanceItems: [{ modCommand: "NONE" }],
            leadDistributorInfos: [],
          },
          {
            vendorNbr: "vendor4",
            leadDistributorMode: "BILL_LEAD_DIST",
            allowanceItems: [{ modCommand: "NONE" }],
            leadDistributorInfos: [{ vendorNbr: "vendor3" }],
          },
        ],
      },
    ];

    const result = getLeadDistributorInitialState(tableData);

    expect(result).toBe("LEAD");
  });

  it("returns 'LEAD' in all other cases", () => {
    const tableData = [
      {
        vendorDetails: [
          {
            vendorNbr: "vendor1",
            leadDistributorMode: "SOME_OTHER_MODE",
          },
        ],
      },
    ];

    const result = getLeadDistributorInitialState(tableData);

    expect(result).toBe("LEAD");
  });
});
