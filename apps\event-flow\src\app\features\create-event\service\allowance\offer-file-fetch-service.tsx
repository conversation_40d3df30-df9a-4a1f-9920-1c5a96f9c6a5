import { getDecodedCookie, getOAMRemoteUser } from "@me/util-helpers";
import { eventDetailsDataHandler } from "../slice/event-detail-slice";

export const fetchHeaders = () => {
  const flowId = `EVT-${
    localStorage.getItem("EVENT_ID")
      ? localStorage.getItem("EVENT_ID")
      : undefined
  }-EN-${
    localStorage.getItem("PLAN_EVENT_ID_NBR")
      ? localStorage.getItem("PLAN_EVENT_ID_NBR")
      : undefined
  }-DIVS-${
    localStorage.getItem("DIVISION_ID")
      ? localStorage.getItem("DIVISION_ID")
      : undefined
  }`;
  const { OAM_REMOTE_USER, USER_EMAIL } = getOAMRemoteUser();
  const headers = new Headers({
    Accept: "application/json",
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Credentials": "true",
    "Ocp-Apim-Subscription-Key":
      window["UPP_CONFIG"]?.["apim_subscription_key"] ||
      process.env["NX_REACT_APIM_SUBSCRIPTION_KEY"],
    "Ocp-Apim-Trace": "true",
    AUTHORIZATION: `Bearer ${getDecodedCookie("ent-abs-auth") || ""}`,
    Flow_id: flowId,
    OAM_REMOTE_USER: OAM_REMOTE_USER || "AGUDU01",
    user_email: USER_EMAIL || "<EMAIL>",
  });
  const baseUrl = `${
    window["UPP_CONFIG"]?.["base_url"] === "/meupp/"
      ? window.location.origin
      : window["UPP_CONFIG"]?.["base_url"] ||
        process.env["NX_REACT_API_BASE_URL"]
  }${window["UPP_CONFIG"]?.["apim_url"] || process.env["NX_REACT_APIM_URL"]}`;
  return { headers, baseUrl };
};
export const prepareRequest = (endpoint: string) => {
  const { headers, baseUrl } = fetchHeaders();
  if (endpoint.includes("blob")) {
    headers.delete("Content-Type");
  }
  return {
    url: `${baseUrl}${endpoint}`,
    headers: Object.fromEntries((headers as any).entries()),
  };
};

export const fetchApi = async (
  url: string,
  method: string,
  body?: any,
  headers: Record<string, string> = {}
) => {
  try {
    const response = await fetch(url, {
      method,
      headers: {
        Accept: "application/json",
        ...headers,
      },
      body,
    });

    if (!response.ok) {
      const error = await response?.json();
      throw new Error(error.message || "Something went wrong");
    }

    return response;
  } catch (error) {
    console.error(`Error in ${method} request to ${url}:`, error);
    throw error;
  }
};

export const uploadFile = async (file: File, endpoint: string) => {
  const { url, headers } = prepareRequest(endpoint);
  const formData = new FormData();
  formData.append("file", file);

  return fetchApi(url, "POST", formData, headers);
};

export const downloadFile = async (endpoint: string, fileName: string) => {
  const { url, headers } = prepareRequest(endpoint);
  const response = await fetchApi(url, "GET", undefined, {
    ...headers,
    Accept: "application/octet-stream",
  });

  const blob = await response.blob();
  const downloadUrl = window.URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = downloadUrl;
  link.setAttribute("download", fileName);
  document.body.appendChild(link);
  link.click();
  link.remove();
};

export const deleteFile = async (endpoint: string) => {
  const { url, headers } = prepareRequest(`${endpoint}`);
  return fetchApi(url, "DELETE", undefined, headers);
};

export const updateEventDetailsDataForAttachments = (
  dispatch,
  eventDetailsData,
  offerNumber,
  fileName,
  actionType
) => {
  const offerAllowances = eventDetailsData?.offerAllowances || [];
  const updatedOffers = offerAllowances.map(offer => {
    if (offer?.offerNumber === offerNumber) {
      if (actionType === "UPLOAD") {
        return {
          ...offer,
          fileNames: [...(offer.fileNames || []), fileName], // Create a new array for fileNames
        };
      } else if (actionType === "DELETE") {
        return {
          ...offer,
          fileNames: (offer.fileNames || []).filter(name => name !== fileName), // Filter out the fileName
        };
      }
    }
    return offer;
  });

  dispatch(
    eventDetailsDataHandler({
      ...eventDetailsData,
      offerAllowances: updatedOffers,
    })
  );
};
