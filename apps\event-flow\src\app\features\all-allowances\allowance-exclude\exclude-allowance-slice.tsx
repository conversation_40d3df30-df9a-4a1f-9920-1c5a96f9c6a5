import { createGenericSlice } from "@me/data-rtk";

export const excludedVendorForAllowance = createGenericSlice({
  name: "excludedVendorForAllowance_rn",
  initialState: {
    status: "loading",
    data: { excludedVendors: [], divisionWiseExcludedVendors: {} },
  },
})({
  setExcludedVendorData(state, { payload }) {
    state.data = { ...state.data, ...payload };
    state.status = "finished";
  },
});
export const { setExcludedVendorData } = excludedVendorForAllowance.actions;
