import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import CICErrorModal from "./cic-error-modal";

describe("CICErrorModal Component", () => {
  const mockSetOpen = jest.fn();
  const mockOnCloseHandler = jest.fn();
  const defaultProps = {
    errorTitle: "Test Error Title",
    isOpen: true,
    setOpen: mockSetOpen,
    onCloseHandler: mockOnCloseHandler,
    width: 800,
    height: 600,
    cicErrorList: [
      {
        itemId: "12345",
        description: "Test Item 1",
        itemValidInd: "NO_UPC_ROG_SETUP",
      },
      {
        itemId: "67890",
        description: "Test Item 2",
        itemValidInd: "NOT_FOUND",
      },
    ],
  };

  it("renders correctly with given props", () => {
    render(<CICErrorModal {...defaultProps} />);

    expect(screen.getByText("Test Error Title")).toBeInTheDocument();
    expect(screen.getByText("12345")).toBeInTheDocument();
    expect(screen.getByText("Test Item 1")).toBeInTheDocument();
    expect(screen.getByText("ROG not set up for item")).toBeInTheDocument();
    expect(screen.getByText("67890")).toBeInTheDocument();
    expect(screen.getByText("Test Item 2")).toBeInTheDocument();
    expect(screen.getByText("Item not found")).toBeInTheDocument();
  });

  it("does not render when isOpen is false", () => {
    const props = { ...defaultProps, isOpen: false };
    render(<CICErrorModal {...props} />);

    expect(screen.queryByText("Test Error Title")).not.toBeInTheDocument();
  });

  it("displays correct error messages based on cicErrorList", () => {
    render(<CICErrorModal {...defaultProps} />);
    expect(screen.getByText("ROG not set up for item")).toBeInTheDocument();
    expect(screen.getByText("Item not found")).toBeInTheDocument();
    expect(screen.getByText("Item Description")).toBeInTheDocument();
    expect(screen.getByText("CIC")).toBeInTheDocument();
  });
});
