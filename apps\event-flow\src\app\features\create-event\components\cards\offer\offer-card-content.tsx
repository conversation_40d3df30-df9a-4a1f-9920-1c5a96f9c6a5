import OfferSections from "./offer-sections";

interface IOfferCardContentProps {
  cardIndex: number;
  cardItemIndex: number;
  isEditEnable: boolean;
}

const OfferCardContent = ({
  cardIndex = 0,
  cardItemIndex = 0,
  isEditEnable = false,
}: IOfferCardContentProps) => {
  return (
    <div>
      <OfferSections
        cardIndex={cardIndex}
        cardItemIndex={cardItemIndex}
        isEditEnable={isEditEnable}
      />
    </div>
  );
};

export default OfferCardContent;
