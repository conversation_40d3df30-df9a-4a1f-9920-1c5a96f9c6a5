import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import RenderOverlapsTable from './render-overlaps-table';

// Mock the OverlappingAllowanceGrid component
jest.mock('@me-upp-js/features/expandable-table', () => ({
  OverlappingAllowanceGrid: jest.fn(() => <div data-testid="overlapping-allowance-grid" />),
}));

describe('RenderOverlapsTable Component', () => {
  const mockAllowancesRespWithOverlaps = {
    offerAllowanceOverlapResults: {
      offerAllowanceOverlaps: [{ id: 1, name: 'Overlap 1' }],
    },
  };

  const mockAllowancesRespWithoutOverlaps = {
    offerAllowanceOverlapResults: {
      offerAllowanceOverlaps: [],
    },
  };

  test.skip('renders OverlappingAllowanceGrid when there are overlapping allowances', () => {
    render(<RenderOverlapsTable allowancesResp={mockAllowancesRespWithOverlaps} switchEnabled={true} />);

    // Verify the OverlappingAllowanceGrid is rendered
    const gridElement = screen.getByTestId('overlapping-allowance-grid');
    expect(gridElement).toBeInTheDocument();

    // Verify the absence of the no overlaps message
    const noOverlapsMessage = screen.queryByText('There are no overlapping Allowances for this Offer.');
    expect(noOverlapsMessage).not.toBeInTheDocument();
  });

  test('renders no overlaps message when there are no overlapping allowances', () => {
    render(<RenderOverlapsTable allowancesResp={mockAllowancesRespWithoutOverlaps} switchEnabled={true} />);

    // Verify the no overlaps message is rendered
    const noOverlapsMessage = screen.getByText('There are no overlapping Allowances for this Offer.');
    expect(noOverlapsMessage).toBeInTheDocument();

    // Verify the OverlappingAllowanceGrid is not rendered
    const gridElement = screen.queryByTestId('overlapping-allowance-grid');
    expect(gridElement).not.toBeInTheDocument();
  });
});
