import Alert from "@albertsons/uds/molecule/Alert";
import efConstants, {
  EEVENT_STATUS,
} from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import { PPG_mismatch_labels } from "../../../../constants/event-status/contsants";
import _ from "lodash";
import { getYear, add, parseISO } from "date-fns";
import { EVENT_TYPES } from "../../../../constants/event-types/event-type-index";
import { getTimeObject } from "apps/event-flow/src/app/shared/helpers/event-flow-helpers";
import {
  NationalVendorRoles,
  comparePrimitiveData,
  getLoggedInUserType,
} from "@me/util-helpers";
import { eventTypes } from "apps/event-flow/src/app/features/event-types/constants/event-type-constant";
import { PeriscopeDetailsProps } from "../event-details.model";
export interface IEventDetailsCardContentProps {
  cardConfiguration?: any;
  updateEventHandler?: any;
  isEditEvent?: any;
  wasError?: any;
  isCreateEvent: boolean;
}

export interface EventGroupFilter {
  promoProductGroups?: string[];
  divisionIds?: string[];
  storedIds?: string[];
  periscopeIds?: string[];
  startDate: string;
  endDate: string;
}
const futureYear = add(getTimeObject(), {
  weeks: 8,
});
const defaultFutureYear = getYear(futureYear);

export const batchSetValueFields = (
  valueFields: Array<string>,
  targetField: string,
  setValue
) => {
  valueFields?.forEach(currentValueField => {
    setValue(currentValueField, targetField);
  });
};
export const resetRegisterFields = (targetFields, setValue) => {
  targetFields &&
    Object.keys(targetFields)?.forEach((element: any) => {
      const { registerField, resetValue } = targetFields?.[element] || {};
      if (resetValue) {
        setValue(registerField, resetValue);
      }
    });
};
export const itemOrderAlphabeticalChecker = (
  previousElem,
  currentElem,
  criteria: string
) => {
  // check the objects based on the criteria provided
  if (+previousElem?.[criteria] > +currentElem?.[criteria]) {
    return previousElem;
  }
  // if item counts are equal. check alphabetical order
  if (+previousElem?.[criteria] === +currentElem?.[criteria]) {
    // if previousElem appears alphabetically before currentElem, return previousElem
    if (previousElem.name.localeCompare(currentElem.name) < 0) {
      return previousElem;
    }
  }
  return currentElem;
};

export const renderPastEventDateWarning = () => {
  return (
    <Alert
      isOpen={true}
      variant="warning"
      size="large"
      className={`mt-2 w-[100%] ${efConstants.componentClassName.RENDER_PAST_EVENT_DATE_WARNING}`}
    >
      <div className="text-base font-bold">
        {efConstants?.PAST_DATE_WARNING}
      </div>
      <div className="text-base font-normal">{efConstants?.HELPER_TEXT}</div>
    </Alert>
  );
};
export const EVENT_DATES_MEMO_DEPENDENCIES = formFields => [
  formFields?.customStartDate,
  formFields?.customEndDate,
  formFields?.isDivisionChanged,
];
export const START_WEEK_MEMO_DEPENDENCIES = formFields => [
  formFields?.enableStartWeekVehicle,
  formFields?.vehicleTypeId,
  formFields?.isPastDate,
  formFields?.vehicleTypeProps,
  formFields?.yearVal,
];
export const VEHICLE_TYPE_MEMO_DEPENDENCIES = formFields => [
  formFields?.isDivisionChanged,
  formFields?.vehicleTypeProps,
  formFields?.isPastDate,
  formFields?.vehicleTypeId,
];
export const STORE_GROUPS_MEMO_DEPENDENCIES = formFields => [
  formFields?.groupInd,
  formFields?.promoProductGroupName,
  formFields?.divisionId,
  formFields?.disableStoreGroups,
];
export const PPG_MEMO_DEPENDENCIES = formFields => [
  formFields?.divisionId,
  formFields?.promoProductGroupName,
  formFields?.isCICModalOpen,
  formFields?.cicsErrorMessage,
  formFields?.unresolvedItemsError,
];
export const STORE_GROUP_TYPE_MEMO_DEPENDENCIES = formFields => [
  formFields?.groupInd,
  formFields?.disableStoreGroupType,
  formFields?.enableStoreGroupType,
];

export const excludeStatuses = [
  EEVENT_STATUS?.REJECTED,
  EEVENT_STATUS?.CANCELED,
  EEVENT_STATUS?.ACTIVE,
];
export const fieldValuesToWatch = [
  "name",
  "promoProductGroup",
  "divisionIds",
  "storeGroupName",
  "vehicleType",
  "storeGroupType",
  "eventCreationVehicle.year",
  "periscopeFormField",
  "planProductGroups",
];

export const checkIfAnyNotValidFields = watchAll => {
  return (
    watchAll[0] !== "" ||
    watchAll[1] !== undefined ||
    watchAll[2]?.length > 0 ||
    watchAll[3] !== undefined ||
    watchAll[4] !== undefined ||
    watchAll[5] !== "" ||
    watchAll[6] !== ""
  );
};
/**
 * creates field values based on the configuration of the event.
 * @returns a string joined based on various form values based on event config
 */
export const getFieldNameValue = (state, eventDetailsData, getValues) => {
  const eventType = _.isEmpty(state?.eventType)
    ? eventDetailsData?.eventType
    : state?.eventType;
  const eventConfig = _.isEmpty(state?.eventType)
    ? EVENT_TYPES?.[EVENT_TYPES?.event_abbreviations?.[eventType]]
    : EVENT_TYPES?.[eventType];
  const eventName: any = eventConfig?.generate_event_name
    ?.map(targetField => {
      return getValues
        ? getValues(targetField)
        : eventDetailsData?.[targetField];
    })
    ?.filter(name => name);
  return eventName?.join(" - ");
};
export const mismatchPPGsError = (
  <>
    <div className="font-bold text-base text-[#9B3E08]">
      {PPG_mismatch_labels.TITLE}
    </div>
    <div className="text-[#9B3E08] font-normal">
      {PPG_mismatch_labels.MESSAGE}
    </div>
  </>
);
export const disableStoreGroupTypeFn = (
  eventId,
  eventDetailsData,
  defaultValues
) => {
  if (eventId) {
    return eventDetailsData?.offerAllowances?.length ||
      eventDetailsData?.promotionsList?.length
      ? true
      : false;
  } else {
    return defaultValues?.["planProductGroups"]?.length ? false : true;
  }
};
export const getEventDefaultState = (getValues, eventDetailsData, eventId) => {
  const userFilters = localStorage.getItem("USER_FILTERS");
  const parsedUserFilters = JSON.parse(userFilters || "{}");
  const { divisionIds } = parsedUserFilters;
  const defaultValues = getValues();
  const eventType = getValues("eventType") || eventDetailsData?.eventType || "";
  const divs = ["NDP", "NAO"].includes(eventType)
    ? eventDetailsData?.divisionIds
    : defaultValues?.["divisionIds"]?.[0] || divisionIds?.[0] || "";

  return {
    isDivisionChanged: false,
    divisionId: divs,
    groupInd:
      defaultValues?.["storeGroupType"] ||
      defaultValues?.["storeGroups"]?.[0]?.["storeGroupType"]?.["groupInd"] ||
      "",
    count: 0,
    yearVal:
      defaultValues?.["eventCreationVehicle"]?.["year"] || defaultFutureYear,
    customEndDate: ["Custom Date", "CustomDate"].includes(
      getValues("vehicleType")
    ) // need to check
      ? getValues("endDate")
      : getValues("eventCreationVehicle.endDate") || "",
    customStartDate: ["Custom Date", "CustomDate"].includes(
      getValues("vehicleType")
    ) // need to check
      ? getValues("startDate")
      : getValues("eventCreationVehicle.startDate") || "",
    vehicleTypeId: "",
    vehicleTypeProps: getValues("vehicleType") || "",
    disableStoreGroupType: disableStoreGroupTypeFn(
      eventId,
      eventDetailsData,
      defaultValues
    ),
    isMutliVendorPPG: false,
    canCreateEvent: true,
    periscopeValid: true,
    periscopeValidFromApi: true,
    isPastDate: false,
    enableStartWeekVehicle: false,
    enableStoreGroupType: defaultValues?.["planProductGroups"]?.length
      ? false
      : true,
    disableStoreGroups: disableStoreGroupTypeFn(
      eventId,
      eventDetailsData,
      defaultValues
    ),
    promoProductGroupName: "",
    periscopeIdState:
      defaultValues?.["periscopeDetails"]?.[0]?.periscopeId || "",
    warningCount: 0,
    isUpdateBtnDisabled: getValues("id"),
    cicsErrorMessage: "",
    unresolvedItemsError: false,
    isCICModalOpen: false,
    eventError: false,
  };
};
export const updateEventDetaultStateFromPeriscope = eventDetailsData => {
  const userFilters = localStorage.getItem("USER_FILTERS");
  const parsedUserFilters = JSON.parse(userFilters || "{}");
  const { divisionIds } = parsedUserFilters;
  const getTheStartYear =
    eventDetailsData?.["eventCreationVehicle"]?.["startDate"];
  const yearValue = getTheStartYear ? parseISO(getTheStartYear) : new Date();
  const fullYearVal = yearValue.getFullYear();
  const periscopeValidValue = isPeriscopeValidFromApi(
    eventDetailsData?.periscopeDetails
  );

  return {
    isDivisionChanged: false,
    divisionId:
      eventDetailsData?.["divisionIds"]?.[0] || divisionIds?.[0] || "",
    groupInd:
      eventDetailsData?.["storeGroups"]?.[0]?.["storeGroupType"]?.[
        "groupInd"
      ] || "",
    count: 0,
    yearVal: fullYearVal || defaultFutureYear,
    eventCreationVehicle: eventDetailsData?.["eventCreationVehicle"],
    startDate: eventDetailsData?.["eventCreationVehicle"]?.["startDate"] || "",
    endDate: eventDetailsData?.["eventCreationVehicle"]?.["endDate"] || "",
    customEndDate:
      eventDetailsData?.["eventCreationVehicle"]?.["endDate"] || "",
    customStartDate:
      eventDetailsData?.["eventCreationVehicle"]?.["startDate"] || "",
    vehicleTypeId:
      eventDetailsData?.["eventCreationVehicle"]?.["vehicleType"]?.[
        "vehicleTypeId"
      ] || "",
    vehicleTypeProps:
      eventDetailsData?.["eventCreationVehicle"]?.["vehicleType"]?.[
        "vehicleTypDesc"
      ] || "",
    vehicleNm: eventDetailsData?.["eventCreationVehicle"]?.["vehicleNm"] || "",
    disableStoreGroupType: false,
    isMutliVendorPPG: false,
    canCreateEvent: true,
    periscopeValid: periscopeValidValue,
    periscopeValidFromApi: periscopeValidValue,
    isPastDate: false,
    enableStartWeekVehicle: false,
    enableStoreGroupType: eventDetailsData?.["planProductGroups"]?.length
      ? false
      : true,
    disableStoreGroups: false,
    promoProductGroupName: "",
    periscopeIdState:
      eventDetailsData?.["periscopeDetails"]?.[0]?.periscopeId || "",
    warningCount: 0,
    isUpdateBtnDisabled: false,
    cicsErrorMessage: "",
    unresolvedItemsError: false,
    isCICModalOpen: false,
    eventError: false,
    startWeekVehicle: eventDetailsData?.["eventCreationVehicle"]?.["vehicleNm"],
    vehicleType:
      eventDetailsData?.["eventCreationVehicle"]?.["vehicleType"] || "",
    promoProductGroup: eventDetailsData?.["planProductGroups"]?.[0]?.name || "",
  };
};

export const checkItContainsDigit = (PID, setFormFields) => {
  const regex = /^\d+$/;
  const matches = PID?.match(regex);
  const periscopeIds = matches ? matches?.map(match => match.slice(1)) : [];
  setFormFields(prevState => ({
    ...prevState,
    periscopeValid: periscopeIds?.length ? true : false,
  }));
  return periscopeIds?.length;
};
export const resetValuesMemoDependency = getValues => [
  getValues("divisionIds"),
  getValues("planProductGroups"),
  getValues("planStoreGroupType"),
  getValues("storeGroups"),
  getValues("eventCreationVehicle.vehicleType.vehicleTypeId"),
  getValues("eventCreationVehicle.year"),
  getValues("eventCreationVehicle.startDate"),
  getValues("eventCreationVehicle.endDate"),
  getValues("name"),
  getValues("periscopeFormField"),
];
export const nameChangeMemoDependencies = getValues => [
  getValues("promoProductGroup"),
  getValues("storeGroupName"),
  getValues("vehicleType"),
  getValues("startWeekVehicle"),
];
export const compareFieldValuesAndSetValue = (
  eventDetailsData,
  getValues,
  setFormFields
) => {
  const previousValues = {
    divisionIds: eventDetailsData.divisionIds,
    planProductGroups: eventDetailsData.planProductGroups,
    planStoreGroupType: eventDetailsData.planStoreGroupType,
    vehicleType: eventDetailsData.eventCreationVehicle.vehicleType,
  };

  const currentValues = {
    divisionIds: getValues("divisionIds"),
    planProductGroups: getValues("planProductGroups"),
    planStoreGroupType: getValues("planStoreGroupType"),
    vehicleType: getValues("eventCreationVehicle.vehicleType"),
  };

  const { DP, NDP } = efConstants.ALLOWANCE_SCREEN_TYPES;

  if ([DP.key, NDP.key].includes(eventDetailsData?.eventType)) {
    previousValues["storeGroups"] = eventDetailsData?.storeGroups;
    currentValues["storeGroups"] = getValues("storeGroups");
  }
  if (
    !comparePrimitiveData(previousValues, currentValues) ||
    eventDetailsData.eventCreationVehicle.startDate !==
      getValues("eventCreationVehicle.startDate") ||
    eventDetailsData.eventCreationVehicle.endDate !==
      getValues("eventCreationVehicle.endDate") ||
    eventDetailsData.name !== getValues("name") ||
    (getValues("periscopeFormField") !== undefined &&
      eventDetailsData?.periscopeDetails?.[0]?.periscopeId !==
        getValues("periscopeFormField"))
  ) {
    setFormFields(prevState => ({
      ...prevState,
      isUpdateBtnDisabled: false,
    }));
  }
};
export const analyticsLinkClickEvent = (eventType, getValues) => {
  const DP = eventTypes[0]?.eventType;
  if (
    window["AB"] &&
    window["AB"]["DATALAYER"] &&
    typeof window["AB"]["DATALAYER"].setLinkClickEvents === "function" &&
    eventType === DP &&
    !getValues("id")
  ) {
    window["AB"]["DATALAYER"].setLinkClickEvents("promotions:create-event");
  }
};
export const preparePlanEventPaylod = (
  data,
  formFields,
  getValues,
  eventType,
  storeGroupDivs
): EventGroupFilter => {
  const planProductGroups = data?.planProductGroups?.map(item =>
    item?.id ? item?.id : item?.planProductGroupId
  );

  const filteredStores = data?.storeGroups?.filter(store =>
    store?.divisionIds?.some(divisionIds =>
      storeGroupDivs.includes(divisionIds)
    )
  );

  const storeGroupIds = filteredStores?.map(
    obj => obj?.id || obj?.storeGroupId
  );
  return {
    promoProductGroups: planProductGroups,
    divisionIds: eventType === "NDP" ? storeGroupDivs : data?.divisionIds,
    storedIds: storeGroupIds,
    periscopeIds: formFields?.periscopeIdState
      ? [formFields?.periscopeIdState?.trim()]
      : [],
    startDate: getValues("startDate"),
    endDate: getValues("endDate"),
  };
};

export const invalidPeriscopeChecker = (
  itemData,
  setDisplayPeriscopeErrors,
  setLabels
) => {
  const periscopeResponse: PeriscopeDetailsProps[] =
    itemData?.data?.periscopeDetails;
  const displayMessage: any = [];
  setLabels(periscopeResponse?.[0]?.periscopeMessages);
  periscopeResponse?.forEach(({ periscopeMessages }) => {
    periscopeMessages?.forEach(message => {
      const error = efConstants.DISPLAY_PERISCOPE_ERRORS[message];
      error && displayMessage.push(error?.label);
    });
    setDisplayPeriscopeErrors(displayMessage);
  });

  return displayMessage?.length;
};
export const shouldUpdateName = (getValues, formFields) => {
  return (
    getValues("promoProductGroup") &&
    (getValues("vehicleType") || getValues("startWeekVehicle")) &&
    formFields?.count === 0
  );
};

export const onChangeDateAndNameHandler = ({
  element,
  getValues,
  setValue,
  setFormFields,
}) => {
  const trimmedValue = element?.target?.value?.trim();
  setFormFields &&
    setFormFields(prevState => ({
      ...prevState,
      eventError: trimmedValue === "" ? true : false,
    }));
  if (trimmedValue && getValues("name")?.trim() !== trimmedValue) {
    setValue("name", trimmedValue);
  }
};

export const nameChangedHandler = ({
  state,
  getValues,
  setValue,
  eventName,
  eventDetailsData,
  formFields,
  allowanceOnlyEvent,
}) => {
  const isAOonly = allowanceOnlyEvent?.isAllowanceOnlyEventType,
    isAllRequiredFieldsValid = shouldUpdateName(getValues, formFields);
  return isAOonly && isAllRequiredFieldsValid
    ? updateAllowanceOnlyEventName({
        eventName,
        eventDetailsData,
        state,
        getValues,
        setValue,
      })
    : isAllRequiredFieldsValid &&
        updateEventName({
          eventName,
          eventDetailsData,
          state,
          getValues,
          setValue,
        });
};
export const updateAllowanceOnlyEventName = ({
  eventName,
  eventDetailsData,
  state,
  getValues,
  setValue,
}) => {
  const formEventName = getFieldNameValue(state, eventDetailsData, getValues);
  formEventName && setValue(eventName.registerField, formEventName);
};

export const updateEventName = ({
  eventName,
  eventDetailsData,
  state,
  getValues,
  setValue,
}) => {
  const name = getValues("name")?.trim();
  if (
    eventDetailsData?.name !== getValues("name") ||
    eventDetailsData?.dataFetchedFromPeriscope
  ) {
    const formEventName = getFieldNameValue(
      state,
      eventDetailsData,
      getValues
    )?.trim();
    setValue(eventName?.registerField, formEventName || name);
  }
};

export const isPeriscopeInputValid = ({
  formFields,
  setFormFields,
}): boolean => {
  return formFields?.periscopeIdState
    ? checkItContainsDigit(formFields?.periscopeIdState, setFormFields)
      ? true
      : false
    : true;
};

export const promoStatusList = ({ eventDetailsData }) => {
  const {
    promotionsLists: [{ promotionsList }],
  } = eventDetailsData || {};
  return promotionsList?.every(promotion =>
    excludeStatuses?.includes(promotion?.promotionWorkflowStatus)
  );
};
export const getPPGItemIds = items => {
  return items?.map(item => item?.itemId)?.join(", ");
};
export const gerneratePeriscopePPGError = periscopeData => {
  const {
    PERISCOPE_ERRORS: { PPG_NOT_FOUND, PPG_NOT_FOUND_INFO, CICS },
  } = efConstants;
  const { sourceProductGroupId, periscopeItems } =
    periscopeData?.[0]?.periscopePlanProductGroup ?? {};
  const ppgItemIds = getPPGItemIds(periscopeItems);
  const ppgInfo = `${PPG_NOT_FOUND_INFO} ${sourceProductGroupId}. ${CICS} ${ppgItemIds}`;
  return { ppgError: PPG_NOT_FOUND, ppgInfo };
};
export const isPeriscopeValidFromApi = periscopeData => {
  const {
    PERISCOPE_ERRORS: { INVALID_PID_KEY },
  } = efConstants;
  return !periscopeData?.[0]?.periscopeMessages?.includes(INVALID_PID_KEY);
};
export const periscopeFieldEnableStatus = getValues => {
  const { eventStatus, offerAllowances, promotionsList } = getValues();
  let piDButtonEnable = true;
  if (
    (eventStatus === EEVENT_STATUS.DRAFT &&
      (offerAllowances?.length || promotionsList?.length)) ||
    eventStatus !== EEVENT_STATUS.DRAFT
  ) {
    piDButtonEnable = false;
  }

  return { piDButtonEnable };
};

export const setNdVendorDivisions = () => {
  const userType = getLoggedInUserType();

  const vendorRolePermissions = JSON.parse(
    localStorage.getItem("USER_ROLE_PERMISSIONS") || "[]"
  );
  const result = {
    vendorNumbers: new Set(),
    divisions: new Set(),
  };

  vendorRolePermissions.forEach(vendor => {
    if (NationalVendorRoles.some(role => vendor.userRoles.includes(role))) {
      result.vendorNumbers.add(vendor.vendorNumber);
      vendor.userDivisions.forEach(division => {
        result.divisions.add(division.divisionId);
      });
    }
  });

  return {
    vendorNumbers:
      userType.toUpperCase() === "VENDOR"
        ? Array.from(result.vendorNumbers)?.length
          ? Array.from(result.vendorNumbers)
          : JSON.parse(localStorage.getItem("vendorNumList") || "[]")
        : [],
    divisions:
      userType.toUpperCase() === "VENDOR"
        ? Array.from(result.divisions)
        : ["98"],
  };
};
export const getIsValidDivisionIdForNationalEvent = (
  eventTypeName,
  divisionIds
) => {
  const isNationalEvent = ["NDP", "NAO"].includes(eventTypeName);
  const filteredArr = divisionIds?.filter(item =>
    isNationalEvent ? Number(item) === 98 : Number(item) !== 98
  );
  return !!filteredArr.length;
};
