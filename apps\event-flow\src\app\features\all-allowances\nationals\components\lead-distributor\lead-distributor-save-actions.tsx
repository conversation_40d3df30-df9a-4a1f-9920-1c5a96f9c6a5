import Button from "@albertsons/uds/molecule/Button";
import {
  leadDistributors<PERSON><PERSON><PERSON>,
  resetDivisionWiseLeadDistData,
  setInitialLeadDistData,
} from "../../../../create-event/service/slice/lead-distributors-slice";
import {
  onCloseDistPopup,
  leadDistError,
  isNationalType,
} from "../../../allowance-lead-distributors/billing-selection-utils";
import React from "react";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import { useDispatch } from "react-redux";
import { useSelectorWrap } from "@me/data-rtk";
import useLeadDistributors from "../../../../create-event/hooks/useLeadDistributors";

type Props = {
  isAllDivLeadsSelected: () => boolean;
  leadDistMode: string;
  setIsModelOpen: (val: boolean) => void;
  divisionWiseLeadDistData: any;
  currentDivisionId: string | number;
  isNdpType?: boolean;
};

function LeadDistributorSaveActions({
  isAllDivLeadsSelected,
  leadDistMode,
  setIsModelOpen,
  divisionWiseLeadDistData,
  currentDivisionId,
  isNdpType = false,
}: Props) {
  const dispatch = useDispatch();

  const {
    data: { isLeadDistributorError },
  } = useSelectorWrap("leadDistErrorslice_rn");
  const {
    data: {
      stepData: selectedSteps,
      allDivisionStepsData,
      leadOptions,
      allDivisionLeadOptions,
      leadSelectionType,
      leadDistMode: defaultModeSelection,
    },
  } = useSelectorWrap("leadDistributors_rn") || {};
  const isNational = isNationalType(isNdpType);
  const currentDivLeadOptions = isNational
    ? allDivisionLeadOptions?.[currentDivisionId] || []
    : leadOptions;
  const { selectedList, stepData } = useLeadDistributors({
    leadOptions: currentDivLeadOptions?.map(item => item?.vendorNbr),
    currentDivId: isNational ? currentDivisionId : "",
    isNdpType: isNational,
  });

  const currentDivisionSteps =
    allDivisionStepsData?.[currentDivisionId]?.stepData || [];

  const popperCloseArgs = {
    selectedSteps: isNational ? currentDivisionSteps : selectedSteps,
    defaultModeSelection,
    dispatch,
    setIsModelOpen,
    leadSelectionType,
  };
  const updatedInitialLeadDistData = () => {
    return leadDistMode && divisionWiseLeadDistData
      ? Object.keys(divisionWiseLeadDistData)?.reduce((acc, key) => {
          acc[key] = divisionWiseLeadDistData?.[key]?.stepData || [];
          return acc;
        }, {})
      : {};
  };

  return (
    <>
      <div className="mt-2">{leadDistError(isLeadDistributorError)}</div>
      <div className="flex items-center justify-center w-full my-4">
        <Button
          width={82}
          size="lg"
          className="mr-2 whitespace-nowrap"
          variant="secondary"
          onClick={() => {
            dispatch(resetDivisionWiseLeadDistData());
            onCloseDistPopup(popperCloseArgs);
          }}
        >
          Cancel
        </Button>
        <Button
          width={92}
          size="lg"
          className="ml-2 whitespace-nowrap"
          onClick={() => {
            dispatch(
              leadDistributorsHandler({
                stepData,
                leadDistMode,
                leadSelectionType: efConstants.LEAD_DIST_LABEL,
                allDivisionStepsData: isNational
                  ? divisionWiseLeadDistData
                  : {},
              })
            );
            dispatch(
              setInitialLeadDistData({
                leadDistData: leadDistMode
                  ? isNational
                    ? updatedInitialLeadDistData()
                    : stepData
                  : [],
              })
            );
            dispatch(resetDivisionWiseLeadDistData());
            setIsModelOpen(false);
          }}
          disabled={
            isNational
              ? !isAllDivLeadsSelected()
              : selectedList?.length < 1 || selectedList?.length > 2
          }
        >
          Confirm
        </Button>
      </div>
    </>
  );
}
export default React.memo(LeadDistributorSaveActions);
