import { useCallback, useEffect, useRef, useState } from "react";
import {
  doRefetchEventsPlanxAapi,
  eventDetailsDataHandler,
  setEventStatusChangedIndicators,
} from "../../../create-event/service/slice/event-detail-slice";
import { IEventAction } from "./event-action.model";
import { useNavigate } from "react-router-dom";
import {
  EEVENT_STATUS,
  EUSER_ROLES,
  PROMO_DETAILS_HIDDEN_PRICING_FEATURE,
  useGetAppBasePath,
  valid_promos,
} from "@me/util-helpers";
import DEFAULT_EVENT_DETAILS from "../../../create-event/constants/event-details/eventDetails";
import { RenderStates } from "@me/ui-render-states";
import { useSelectorWrap } from "@me/data-rtk";
import { getApiCall, getModalData, ctaKeyLabel } from "./helper-event";
import { validOfferList } from "../../../create-event/service/allowance/allowance-service";
import efConstants from "../../../../shared/ef-constants/ef-constants";
import { useDispatch } from "react-redux";
import EventActionModal from "./event-action-modal";
import renderEventActions from "./render-event-actions";
import EventActionService from "./event-action-services";
import { setIsFromCommentSection } from "../../slices/event-types-slice";
import { isAddPromoFn } from "../../../create-event/service/slice/promotion-details-slice";
import _ from "lodash";
import {
  allowanceFormReset,
  promoCardConfiguration,
  resetIsOfferSectionUpdated,
  resetOfferAmountsData,
  setPromoSubCardConfiguration,
} from "../../../create-event/service/slice/allowance-details-slice";
import { CTAs_Keys } from "../../../create-event/constants/event-status/contsants";
import { getLoggedInUserType } from "@me-upp-js/utilities";
import { useDeleteAllowanceTempWorkDataMutation } from "../../../create-event/service/apis/allowance-api";
import { allowanceTempWorkReset } from "../../../create-event/service/slice/allowance-temp-work-slice";
import { resetNationalDivisionsConfig } from "../../../all-allowances/nationals/slices/national-main-entry-slices";
import { EVENT_TYPE } from "../../../create-event/constants/constants";

const EventAction = ({
  userRole,
  eventStatus,
  eventStatusData,
  eventId: eId,
  setCommentOpen,
  modifyOrCancel,
}: IEventAction) => {
  const { hiddenPricingFeatureEnabled } = PROMO_DETAILS_HIDDEN_PRICING_FEATURE;
  const dispatch = useDispatch();
  const executeAction = useCallback((rEventStatus, ctaKey) => {
    const cta_key = ctaKey ? ctaKey : ctaKeyLabel;
    document
      .getElementById("event-header")
      ?.scrollIntoView({ behavior: "smooth" });
    dispatch(eventDetailsDataHandler({ eventStatus: rEventStatus }));
    dispatch(setEventStatusChangedIndicators({ isEventStatusChanged: true }));
    if (
      userRole === EUSER_ROLES.VENDOR &&
      rEventStatus === EEVENT_STATUS.AGREED_PENDING &&
      [
        CTAs_Keys.SEND_TO_MERCHANT,
        CTAs_Keys.CANCEL_EVENT,
        CTAs_Keys.RETURN,
      ].includes(cta_key)
    ) {
      dispatch(
        doRefetchEventsPlanxAapi({
          isRefetch: _.uniqueId("refetch"),
        })
      );
    } else if (
      userRole === EUSER_ROLES.MERCHANT &&
      rEventStatus === EEVENT_STATUS.AGREED_PENDING &&
      [CTAs_Keys.REJECT_PENDING_REQUEST].includes(cta_key)
    )
      dispatch(
        doRefetchEventsPlanxAapi({
          isRefetch: _.uniqueId("refetch"),
        })
      );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const dispatchPending = useCallback(() => {
    return {
      data: () => {
        dispatch(
          setIsFromCommentSection({
            isFromComment: false,
            currentEventStatus: eventStatus,
          })
        );
        setCommentOpen(true);
      },
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const [isModalPopupOpen, setModalPopupOpen] = useState<boolean>(false);
  const [modalType, setModalType] = useState<string>("");
  const modalPropsdefault = {
    title: getModalData?.modalTitle,
    warningMessage: getModalData?.warningMessage,
    confirmBtnTitle: getModalData?.confirmBtnTitle,
    cancelBtnTitle: getModalData?.cancelBtnTitle,
    showHideBtns: true,
  };
  const [deleteAllowanceTempWorkData] =
    useDeleteAllowanceTempWorkDataMutation();

  const [modalProps, setModalProps] = useState({});
  const { data: { editCardConfig } = { editCardConfig: {} } } =
    useSelectorWrap("offer_card_configutation_rn") || {};
  const { data: { isEditPromotion } = { isEditPromotion: {} } } =
    useSelectorWrap("promotion_edit_enable_configutation_rn") || {};
  const { data: { isAddAnotherOffer } = { isAddAnotherOffer: false } } =
    useSelectorWrap("allowance_new_card_configuration_rn") || {};
  const { data: eventDetailsData } =
    useSelectorWrap("event_details_data") || {};
  const { isEventCardOpen } =
    useSelectorWrap("is_event_edit_enable")?.data || {};
  const { isAddNewPromo: newPromoAdd = {} } =
      useSelectorWrap("promo_card_configutation_rn")?.data || {},
    allowanceTempWorkData = useSelectorWrap("allowance_temp_work")?.data;

  const {
    divisionIds = [],
    promotionsList = [],
    promotionsLists = [],
    planEvent = {},
    planEventHistory = [],
    eventType = "DP",
    startDate,
    endDate,
    negotiationSimsVendors = [],
  } = eventDetailsData;

  const eventDetailsDataRef = useRef(eventDetailsData);

  useEffect(() =>{
    eventDetailsDataRef.current = eventDetailsData;
  },[eventDetailsData])

  const eventActionService = EventActionService(
    startDate,
    endDate,
    executeAction,
    dispatchPending,
    setCommentOpen
  );
  const eventActionStatus = eventActionService.eventActionStatus;

  // Disabling workflows buttons if any allowance or promotion is in editing mode
  const doDisableCtsBtn = ctaKey => {
    const isAllowEditMode =
      isAddAnotherOffer ||
      (editCardConfig && Object.values(editCardConfig).some(e => e)) ||
      false;
    const isPromoEditMode = isEditPromotion?.isEdit || false;
    const isNewPromo = newPromoAdd && Object.values(newPromoAdd).some(e => e);
    return (
      isAllowEditMode ||
      isPromoEditMode ||
      (isNewPromo && promotionsList?.length) ||
      isEventCardOpen ||
      checkIsAllInvalidOfferOrPromo(ctaKey)
    );
  };

  const checkForHiddenPriceOrROG = ctaKey => {
    const isNotInEditMode = doDisableCtsBtn(ctaKey);
    if (isNotInEditMode) {
      return true;
    }
    const {
      allowanceEventInd,
      eventDetailsEventInd,
      promotionEventInd,
      otherDetailsChangedInd,
    } = eventDetailsData || {};

    if (
      hiddenPricingFeatureEnabled &&
      !isNotInEditMode &&
      getLoggedInUserType() === EUSER_ROLES?.MERCHANT &&
      ["SEND_TO_VENDOR"]?.includes(ctaKey)
    ) {
      const checkForDraftPromoAllow =
        eventActionService?.checkIsNewAdded(planEvent);
      if (
        !allowanceEventInd &&
        !checkForDraftPromoAllow &&
        !(eventDetailsEventInd && otherDetailsChangedInd) &&
        promotionEventInd
      ) {
        const validPromos = valid_promos(eventDetailsData?.promotionsList);
        return validPromos?.[0]?.hiddenPrice || validPromos?.[0]?.createPricing;
      }
    }
    return isNotInEditMode;
  };

  const checkIsAllInvalidOfferOrPromo = ctaKey => {
    const {
      SEND_CTS_BTNS,
      NEW_DIRECTION_FEATURES_FLAGS: { isDirectionalChangesEnable },
    } = efConstants;
    if (isDirectionalChangesEnable && SEND_CTS_BTNS.includes(ctaKey)) {
      const validOffers = validOfferList(eventDetailsData) || [],
        promoList = eventActionService.promoStatusList(promotionsLists) || [];
      return validOffers.length + promoList.length <= 0;
    }
    return false;
  };

  const navigate = useNavigate();
  const { basePath } = useGetAppBasePath();
  let apiCall;
  const onDispatchCommentSection = useCallback(() => {
    dispatch(
      setIsFromCommentSection({
        isFromComment: false,
        currentEventStatus: eventStatus,
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const handleChangeStatus = (e, key) => {
    e?.preventDefault();
    apiCall = eventActionService.getPostApiCall(key, userRole, eventStatus);
    eventActionService.handleStatus(
      key,
      setModalType,
      setModalPopupOpen,
      onDispatchCommentSection,
      eId,
      apiCall,
      eventType,
      eventDetailsDataRef.current?.promotionsLists || promotionsLists,
      eventStatus,
      userRole
    );
  };
  const isApiLoading = () => {
    return (
      Object.values(eventActionStatus).some(status => status?.isLoading) ||
      eventActionStatus.isDeleteLoading
    );
  };

  const modalConfirmHandler = useCallback(() => {
    setModalPopupOpen(false);
    document.body.style.overflow = "visible";
    eventActionService.executeApiCall(eId, getApiCall, "", setModalPopupOpen);
  }, [eId, eventActionService]);

  const deleteModalConfirm = () => {
    const isNcdpType = eventDetailsData?.eventType === EVENT_TYPE.NCDP;
    const parentEventId = eventDetailsData?.parentEvent?.eventId || null;
    eventActionStatus.deleteEvent({
      URL_PARAM: isNcdpType && parentEventId ? parentEventId : eId,
      customHeaders: {
        ...(isNcdpType && {
          divisionIds: eventDetailsData?.divisionIds?.join(",") || "",
        }),
      },
    });
  };

  const onCloseHandler = useCallback(() => {
    setModalPopupOpen(false);
    document.body.style.overflow = "visible";
  }, []);

  const onPromoWarningHandler = useCallback(() => {
    dispatch(isAddPromoFn({ isAddPromo: true }));
    dispatch(promoCardConfiguration({ openCardConfig: { 0: true } }));
    dispatch(
      setPromoSubCardConfiguration({ promoSubCardConfig: { 0: { 0: true } } })
    );
    setModalPopupOpen(false);
    document.body.style.overflow = "visible";
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onPromoWarningCloseHandler = useCallback(() => {
    setModalPopupOpen(false);
    document.body.style.overflow = "visible";
  }, []);

  const deleteTempWorkData = async () => {
    const tempWorkAllowanceId =
      allowanceTempWorkData?.allowanceData?.tempWorkAllowanceId || "";

    if (tempWorkAllowanceId) {
      const isDeleted = await deleteAllowanceTempWorkData({
        URL_PARAM: tempWorkAllowanceId,
      });

      if (isDeleted) {
        dispatch(allowanceTempWorkReset());
        dispatch(allowanceFormReset());
        dispatch(resetOfferAmountsData());
        dispatch(resetIsOfferSectionUpdated());
        dispatch(resetNationalDivisionsConfig());
      }
    }
  };

  useEffect(() => {
    // if delete call was successful, reset store and nav to Task View
    if (eventActionStatus.isDeleteSuccess) {
      dispatch(eventDetailsDataHandler({ ...DEFAULT_EVENT_DETAILS }));
      deleteTempWorkData();
      navigate(`${basePath}/`);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [eventActionStatus.isDeleteSuccess]);

  const modalHandlers = {
    promo: {
      onClose: onPromoWarningCloseHandler,
      cancelBtnHandler: onPromoWarningHandler,
      height: 316,
      width: 750,
      minBtnWidth: 400,
    },
    modal: {
      onClose: onCloseHandler,
      cancelBtnHandler: onCloseHandler,
    },
    delete: {
      deleteCardHandler: deleteModalConfirm,
      modalNameHandler: deleteModalConfirm,
      onClose: onCloseHandler,
      cancelBtnHandler: onCloseHandler,
      height: 272,
      width: 800,
      minHeight: 272,
      minBtnWidth: 92,
      minCancelBtnWidth: 82,
    },
  };

  useEffect(() => {
    setModalProps({
      ...modalPropsdefault,
      isModalPopupOpen: isModalPopupOpen,
      setModalPopupOpen: setModalPopupOpen,
      deleteCardHandler: modalConfirmHandler,
      modalNameHandler: modalConfirmHandler,
      ...modalHandlers[modalType],
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isModalPopupOpen]);

  const shouldRenderAction = label => {
    if ((eId === undefined || eId.length === 0) && label === "Delete Draft") {
      return false;
    } else {
      return true;
    }
  };
  const renderHtml = (
    <>
      <EventActionModal modalProps={modalProps} />
      {renderEventActions(
        eventActionService,
        eventStatusData,
        eventStatus,
        userRole,
        planEvent,
        shouldRenderAction,
        divisionIds,
        doDisableCtsBtn,
        checkForHiddenPriceOrROG,
        handleChangeStatus,
        planEventHistory,
        isApiLoading(),
        negotiationSimsVendors,
        eId,
        eventType
      )}
    </>
  );
  const renderDetails = {
    isApiLoading: isApiLoading(),
    isPageLevelSpinner: true,
    isRenderMainHtml: true,
    renderHtml,
  };
  return <RenderStates details={renderDetails} />;
};

export default EventAction;
