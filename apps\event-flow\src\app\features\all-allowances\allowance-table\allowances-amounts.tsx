/* eslint-disable react/jsx-no-useless-fragment */
import Input from "@albertsons/uds/molecule/Input";
import Select from "@albertsons/uds/molecule/Select";
import { useSelectorWrap } from "@me/data-rtk";
import classNames from "classnames";
import _ from "lodash";
import { ChangeEvent, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import efConstants from "../../../shared/ef-constants/ef-constants";
import useAllowanceAmountValidation from "../../create-event/hooks/allowance-amount-validations";
import {
  getAllowanceTypeFromTempWork,
  getMinListCostKey,
  getUOMBasedOnType,
} from "../../create-event/service/allowance/allowance-service";
import { checkIsDisplayOnSwitch } from "./allowance-table-data-utils";
import "./allowances-amounts-styles.scss";
import {
  updateIndividualItemAction,
  updateItemByDistributorAction,
  updateItemByItemAction,
} from "../../create-event/service/slice/table-data-slice";
import InValidAmountTooltip from "./invalid-amount-tooltip";
import { textMapping } from "../all-allowances-container/text-mapping";
import { isVendorLoggedIn } from "@me-upp-js/utilities";
import { isNationalType } from "../allowance-lead-distributors/billing-selection-utils";

export default function AllowancesAmounts({
  rowData,
  warehouseIndex,
  type,
  byItemVendDetails = [],
  byItemId = {},
  isHeader = false,
}) {
  const { itemId = {}, vendorDetails = {} } = rowData;
  const currentVendorObj = vendorDetails?.[warehouseIndex] || {};
  const {
      UOM_OPTIONS,
      ALLOWANCE_AMT_INPUT_KEY,
      ALLOWANCE_UOM_KEY,
      SWITCH_OPTIONS,
      ZERO_COST_TEXT,
    } = efConstants,
    { vendorNbr = -1, distCenter = -1 } = currentVendorObj,
    allowanceAmt = currentVendorObj[ALLOWANCE_AMT_INPUT_KEY] || "",
    uomValue = currentVendorObj[ALLOWANCE_UOM_KEY] || "";

  const [allowanceAmtVal, setAllowanceAmtVal] = useState(allowanceAmt);
  const [uomVal, setUomVal] = useState<any>({});
  const [minUnitListCost, setMinUnitListCost] = useState(0);
  const { data: excludedVendorData } = useSelectorWrap(
    "excludedVendorForAllowance_rn"
  );
  const { data: allDivisionsWarningData } =
    useSelectorWrap("all_divisions_warning_data") || {};
  const { selectedDivisionData = {}, isNdpType = false } =
    useSelectorWrap("national_divisions_config")?.data || {};
  const isNational = isNationalType(isNdpType);
  const { divisionErrObj = {} } = allDivisionsWarningData || {};
  const getInputTextClass = isAnyErrorInColumn => {
    return classNames({
      amtFieldWrap: true,
      "error-input-wrap": !isNational && isAnyErrorInColumn,
      "amount-input-error": isNational && isAnyErrorInColumn,
    });
  };

  const [inputTextClass, setInputTextClass] = useState(
    getInputTextClass(false)
  );

  const { data: emptyFieldsData } = useSelectorWrap("emptyFields_rn"),
    { data: switchValue } = useSelectorWrap("selectedSwitchValue_rn");
  const allowanceTempWorkData = useSelectorWrap("allowance_temp_work");
  const allowDataSet = allowanceTempWorkData?.data?.allowanceData;
  const allowanceData = isNational && Array.isArray(allowDataSet) ? allowDataSet?.[0] : allowDataSet;
  const allowanceType = allowanceData?.allowanceType || "";
  const {
    customAmtWidth,
    isShowUOMOption,
    isSkipLessThanAmtValidation = false,
    isDistCenter = false,
    showExclude = false,
  } = textMapping({
    allowanceTempWorkData: allowanceData || {},
  });
  const {
    data: { tableData },
  } = useSelectorWrap("allowanceTableData_rn");
  const dispatch = useDispatch();
  const { calcError, validateNewAmount, clearError } =
    useAllowanceAmountValidation({
      maxLimit: minUnitListCost,
      modCommand: currentVendorObj?.itemLevelModCommand,
    });
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Set the component as loaded once mounted
    setIsLoaded(true);
  }, []);

  useEffect(() => {
    calculateAndSaveMinUnitListCost();
  }, [tableData, allowanceType]);

  /**
   * When ever switch value changes, we need to calculate the min amount and amount again
   */
  useEffect(() => {
    checkAmtValidtnOnSwitch();
  }, [switchValue?.selectedSwitch]);

  useEffect(() => {
    updateValidationOnChange();
  }, [allowanceAmt, minUnitListCost]);

  useEffect(() => {
    !isNational && checkIsAnyFieldEmpty(emptyFieldsData);
  }, [emptyFieldsData]);

  useEffect(() => {
    if (!isNational || !selectedDivisionData?.divisionId) return;
    const currentDivErrorObj =
      divisionErrObj?.[selectedDivisionData?.divisionId];
    checkIsAnyFieldEmpty(currentDivErrorObj);
  }, [divisionErrObj, selectedDivisionData?.divisionId, distCenter, vendorNbr]);

useEffect(() => {
  handleDivChangeForValidation();
}, [
  selectedDivisionData?.divisionId,
  distCenter,
  vendorNbr,
]);

  useEffect(() => {
    allowanceType && getUomVal();
  }, [allowanceType, uomValue, switchValue?.selectedSwitch]);

  function updateAmounts(amountVal) {
    const { amount } = validateNewAmount(amountVal);
    setAllowanceAmtVal(amount);
  }
  const updateValidationOnChange = () => {
    updateAmounts(allowanceAmt);
    const amtValue =
      rowData?.vendorDetails?.[warehouseIndex]?.[allowListCostKey];
    if (parseFloat(allowanceAmt || 0) < parseFloat(amtValue || 0)) {
      clearError();
    }
  };
  const handleDivChangeForValidation = () => {
  // Clear amounts in the header for national if the division changes
  if (!isNational) return;
  const isByDistributorOrByItem = ["byDistributor", "byItem"].includes(type);
  if (isByDistributorOrByItem) {
    setAllowanceAmtVal("");
    clearError();
  } else {
    calculateAndSaveMinUnitListCost();
    updateValidationOnChange();
  }
};

  /**
   *
   * @returns This will return boolean value if switch selected shows only display only values
   * Ex = when we switch to unit, we need to display amt as display value and disable
   * input text box and UOM type
   */
  const isOnlyDisplayOnSwitch = () => {
    return checkIsDisplayOnSwitch(allowanceType, switchValue?.selectedSwitch);
  };
  const allowListCostKey = getMinListCostKey({
    allowanceType,
    SWITCH_OPTIONS,
    switchValue,
    allowanceTempWorkData,
  });

  const isDisabled = () => {
    return isOnlyDisplayOnSwitch() || isDisableFieldOnExclude();
  };

  const checkAmtValidtnOnSwitch = () => {
    if (isOnlyDisplayOnSwitch()) {
      setAllowanceAmtVal("");
      clearError();
    }
  };

  const checkIsAnyFieldEmpty = (emptyFieldsData) => {
    const emptyFieldsDetails = emptyFieldsData?.emptyFieldsDetails,
      facilitiesWithEmptyFields = emptyFieldsDetails?.[itemId];
    let isAnyErrorInColumn = false;
    if (!_.isEmpty(facilitiesWithEmptyFields)) {
      //Check if the current Column(Specific vendorNbr) is having the error field
      isAnyErrorInColumn = facilitiesWithEmptyFields.includes(
        isDistCenter ? distCenter : vendorNbr
      );
      setInputTextClass(getInputTextClass(isAnyErrorInColumn));
    } else {
      setInputTextClass(getInputTextClass(false));
    }
  };
  useEffect(() => {
    if (isHeader && allowanceType) {
      const uomOptions = UOM_OPTIONS[allowanceType]?.defaultOptions;
      const defaultUom = uomOptions?.find(uomObj => uomObj?.default);
      defaultUom && setUomVal(defaultUom);
    }
  }, [allowanceType]);

  const getUomVal = () => {
    const uomValueObj: any = getUOMBasedOnType(
      allowanceType,
      UOM_OPTIONS,
      switchValue?.selectedSwitch
    )?.filter(option => {
      //returns the value based on the response.
      return option.value === uomValue;
    });
    const val = uomValueObj?.[0];
    val && !isHeader && setUomVal(val);
  };

  const handler_allowanceAmt = e => {
    updateAmounts(e.target.value);
  };

  const handler_uom = e => {
    setUomVal(e);
  };

  const handler_blurUoM = () => {
    const e = uomVal;

    switch (type) {
      case "":
        dispatch(
          updateIndividualItemAction({
            itemId: itemId,
            vendorIndex: warehouseIndex,
            uom: e.value,
            amount: allowanceAmtVal,
            switchValue: switchValue?.selectedSwitch
          })
        );
        break;
      case "byItem":
        dispatch(
          updateItemByItemAction({
            itemId: byItemId,
            uom: e.value,
            switchValue: switchValue?.selectedSwitch
          })
        );
        break;
      case "byDistributor":
        dispatch(
          updateItemByDistributorAction({
            vendorIndex: warehouseIndex,
            uom: e.value,
            switchValue: switchValue?.selectedSwitch
          })
        );
        break;

      default:
        break;
    }
  };

  const isDisableFieldOnExclude = () => {
    const toCheckExcludedVendor = isNational
      ? (excludedVendorData?.divisionWiseExcludedVendors?.[
          selectedDivisionData?.divisionId
        ] ?? {})
      : excludedVendorData;
    return (
      showExclude &&
      toCheckExcludedVendor?.[warehouseIndex]?.isExclude &&
      type !== "byItem"
    );
  };
  const handler_blurAllowanceAmt = (e: ChangeEvent<HTMLInputElement>) => {
    // Commenting below as they want to show only warning message but user can still proceed
    // if (calcError) return;
    switch (type) {
      case "":
        dispatch(
          updateIndividualItemAction({
            itemId: itemId,
            vendorIndex: warehouseIndex,
            amount: allowanceAmtVal,
            switchValue: switchValue?.selectedSwitch
          })
        );
        break;
      case "byItem":
        dispatch(
          updateItemByItemAction({
            itemId: byItemId,
            amount: allowanceAmtVal,
            switchValue: switchValue?.selectedSwitch
          })
        );
        break;
      case "byDistributor":
        dispatch(
          updateItemByDistributorAction({
            vendorIndex: warehouseIndex,
            amount: allowanceAmtVal,
            switchValue: switchValue?.selectedSwitch
          })
        );
        break;

      default:
        break;
    }
  };

  return (
    <>
      {allowanceType && (
        <div
          className={`flex items-start px-3 ${efConstants.componentClassName.ALLOWANCES_AMOUNTS}`}
        >
          <span className="relative">
            <span className="absolute top-[10px] left-[10px]"> $ </span>
            <Input
              data-testid="uom-text"
              name="allowanceAmt"
              onWheel={event => event.currentTarget.blur()}
              className={`${inputTextClass} ${
                calcError ? "error-input-wrap" : ""
              }`}
              disabled={isDisabled()}
              value={allowanceAmtVal}
              onBlur={(e: ChangeEvent<HTMLInputElement>) =>
                handler_blurAllowanceAmt(e)
              }
              onChange={(e: ChangeEvent<HTMLInputElement>) =>
                handler_allowanceAmt(e)
              }
              width={customAmtWidth || 90}
            />
          </span>
          {isShowUOMOption ? (
            <div className="w-[76px]" data-testid="uom-select-container">
              <Select
                data-testid="uom-select"
                className="w-[76px] ml-1 mt-0.5"
                items={getUOMBasedOnType(
                  allowanceType,
                  UOM_OPTIONS,
                  switchValue?.selectedSwitch
                )}
                disabled={isDisabled()}
                itemText="name"
                size="md"
                value={uomVal}
                onChange={(e: any) => handler_uom(e)}
                // * this will be reuqired to make sure that in the powerlines, if we select same option again,
                // * it will trigger the powerline operation
                onClose={() => {
                  if (isLoaded) {
                    handler_blurUoM();
                  }
                }}
              />
            </div>
          ) : null}
          <InValidAmountTooltip calcError={calcError} dynamicDirection={true} />
        </div>
      )}
    </>
  );

  // find the minimum value for minUnitListCost
  function calculateAndSaveMinUnitListCost() {
    const allowListCostKey = getMinListCostKey({
      allowanceType,
      SWITCH_OPTIONS,
      switchValue,
      allowanceTempWorkData,
    });
    let minVal = 0;
    switch (type) {
      case "":
        minVal = isSkipLessThanAmtValidation
          ? Number.MAX_VALUE
          : rowData?.vendorDetails?.[warehouseIndex]?.[allowListCostKey];
        break;
      case "byItem":
        minVal = Math.min(
          ...(byItemVendDetails
            ? byItemVendDetails?.map((vendor: any) =>
                (isVendorLoggedIn() && vendor?.itemLevelModCommand === ZERO_COST_TEXT) || vendor?.isEmptyVendor
                  ? Number.MAX_VALUE
                  : vendor[allowListCostKey]
              )
            : [])
        );
        break;
      case "byDistributor": {
        minVal = Math.min(
          ...tableData?.map(row => {
            const vendorObj = row?.vendorDetails?.[warehouseIndex];
            return (isVendorLoggedIn() && vendorObj?.itemLevelModCommand === ZERO_COST_TEXT) ||
              vendorObj?.isEmptyVendor ||
              isSkipLessThanAmtValidation
              ? Number.MAX_VALUE
              : vendorObj?.[allowListCostKey];
          })
        );
        break;
      }

      default:
        break;
    }
    setMinUnitListCost(minVal);
  }
}
