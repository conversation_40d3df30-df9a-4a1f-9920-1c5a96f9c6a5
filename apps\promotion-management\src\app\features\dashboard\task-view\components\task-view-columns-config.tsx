import Button from "@albertsons/uds/molecule/Button";
import { Column } from "@albertsons/uds/molecule/Table/Table.types";
import { Tooltip } from "@albertsons/uds/molecule/Tooltip";
import classNames from "classnames";
import { formatDate } from "../../../../shared/utility/utility";
import { Task, TaskActionType } from "./task-view-model";
import {
  validateIfAnyChangesAreMade,
  validateIfSentBackWithCmt,
} from "./task-view-util";
import {
  EEVENT_STATUS,
  dateFormatWithZone,
  formatTime,
  formatTimeWithZone,
} from "@me/util-helpers";
import {
  isMerchantLoggedIn,
  isSortFeatureFlagEnabled,
  useGetAppBaseNationalIcon,
} from "@me-upp-js/utilities";
import { Trash, Calendar } from "lucide-react";
import useMarkTaskCompleted from "../../../../shared/hooks/useMarkTaskCompleted";
import { SortLabel } from "../../common/components/sort-label";
import { PROMOTION_MANAGEMENT_COLUMNS } from "apps/promotion-management/src/app/shared/pm-constants/pm-constants";
import { NavLink } from "react-router-dom";
import { EVENT_TYPE } from "apps/promotion-management/src/app/constants";
export const TASK_ACTIONS_LABELS = {
  AGREE: "Agree",
  SEND_BACK_WITH_COMMENT: "Send Back w/Cmt",
  REJECT: "Reject",
  AGREE_TO_PENDING_REQUEST: "Agree to Pending Request",
  REJECT_PENDING_REQUEST: "Reject Pending Request",
  SEND_TO_MERCHANT: "Send to Merchant",
  SEND_TO_VENDOR: "Send to Vendor",
  ADD_ITEM_SEND_TO_VENDOR: "Add Item & Send to Vendor",
  DELETE_ITEM: "Delete Item",
  SET_END_DATE: "Set End Date",
  DO_NOT_ADD_ITEM: "Do Not Add Item",
  ADD_ITEM: "Add Item",
  DO_NOT_DELETE_ITEM: "Do Not Delete Item",
  AGREE_TO_CANCEL_REQUEST: "Agree To Cancel Request",
  LAST_ITEM_CANCEL: "LAST_ITEM_CANCEL",
  LAST_ITEM_CANCEL_FOR_NDP: "LAST_ITEM_CANCEL_FOR_NDP",
};

export const WORKFLOW = "WORKFLOW";
export const ALLOWANCE_INQUIRY = "ALLOWANCE_INQUIRY";
export const NEW_ITEM = "NEW_ITEM";
export const IMMINENT_TASK = "ImminentTask";
export const DEFER_ACTION = "DEFER";
export const DROPPED_ITEM = "DROPPED_ITEM";
export const EVENT_UPDATED = "EVENT_UPDATED";
export const EVENT_CANCELED = "EVENT_CANCELED";
export const EVENT_PUBLISHED = "EVENT_PUBLISHED";
export const CHANGED_EVENT = "Change Request";
export const NEW_EVENT = "New Event";
export const COMMENT = "COMMENT";
export const addItemSendToVendorTooltipLabel =
  "Send this item to the vendor to add the amount";

export const BILLING_INQUIRY = "Billing Inquiry";
export const EXTERNAL = "External";

export const PPG_UNIT_TYPE_MAPPER = {
  1: {
    name: "Single",
    code: "S",
  },
  2: {
    name: "Multi",
    code: "M",
  },
  3: {
    name: "Case",
    code: "C",
  },
};

export const TASK_TYPE_MAPPER = {
  [WORKFLOW]: "Workflow",
  [ALLOWANCE_INQUIRY]: "Billing Inquiry",
  [NEW_ITEM]: "New Item",
  COMMENT: "New Comment",
  [DROPPED_ITEM]: "Item Removed",
  [EVENT_UPDATED]: "Event Updated",
  [EVENT_CANCELED]: "Event Canceled",
};

export const ALERTS_AGREED_MAPPER = {
  [WORKFLOW]: "Event Approved",
  [NEW_ITEM]: "New Item",
  [DROPPED_ITEM]: "Item Removed",
  [EVENT_UPDATED]: "Event Updated",
  [EVENT_CANCELED]: "Event Canceled",
  [EVENT_PUBLISHED]: "Event Published",
};

const ViewOpenEventInNewTab = ({ task }: { task: Task }) => {
  const markTaskCompleted = useMarkTaskCompleted();

  const handleClick = async () => {
    if (task?.subType === "COMMENT" && task?.taskStatus !== "COMPLETED") {
      await markTaskCompleted(task.taskId);
    }
  };

  return (
    <NavLink to={`events/edit/${task?.id}?taskType=${task?.subType}`}>
      <Button variant="secondary" width={54} onClick={handleClick}>
        View
      </Button>
    </NavLink>
  );
};

const ITEM_ADDITION_COMMON_CONFIG = {
  taskAlertStatus: "Completed",
  modificationType: "ITEM_ADDITION",
};
const ITEM_DROPPED_COMMON_CONFIG = {
  taskAlertStatus: "Completed",
  modificationType: "ITEM_DELETION",
};
export const PPG_ITEM_DECISION_CONFIG = {
  [TASK_ACTIONS_LABELS.DO_NOT_ADD_ITEM]: {
    ...ITEM_ADDITION_COMMON_CONFIG,
    selectedOption: "DECLINE",
  },
  [TASK_ACTIONS_LABELS.ADD_ITEM]: {
    ...ITEM_ADDITION_COMMON_CONFIG,
    selectedOption: "ACCEPT",
  },
  [TASK_ACTIONS_LABELS.DELETE_ITEM]: {
    ...ITEM_DROPPED_COMMON_CONFIG,
    selectedOption: "ACCEPT",
  },
  [TASK_ACTIONS_LABELS.SET_END_DATE]: {
    ...ITEM_DROPPED_COMMON_CONFIG,
    selectedOption: "TAG",
  },
  [TASK_ACTIONS_LABELS.DO_NOT_DELETE_ITEM]: {
    ...ITEM_DROPPED_COMMON_CONFIG,
    selectedOption: "DECLINE",
  },
  [TASK_ACTIONS_LABELS.SEND_TO_VENDOR]: {
    ...ITEM_ADDITION_COMMON_CONFIG,
    selectedOption: "DEFER",
  },
};
export const TASK_VIEW_ACTIONS_BUTTONS = [
  {
    name: "View",
    key: "view",
    width: 54,
    component: ViewOpenEventInNewTab,
  },
];

export const TASK_MODAL_CONFIG = {
  REJECT: {
    title: "Are you sure you want to Reject the event?",
    warningMessage: "Event will no longer be editable",
    confirmBtnTitle: "Yes, Reject Event",
    cancelBtnTitle: "No, Cancel",
  },
  AGREE: {
    title: "Are you sure you want to Agree to this Event?",
    confirmBtnTitle: "Yes, Agree to Event",
    cancelBtnTitle: "No, Cancel",
  },
  LAST_ITEM_CANCEL: {
    title: "Do you want to Cancel the Offer/Event?",
    infoMessage:
      "This is the final item in this PPG or the PPG has been deleted.\n Deleting this will mark the task as complete.",
    confirmBtnTitle: "Yes, Delete Item and Cancel Offer",
    cancelBtnTitle: "No, Leave Item and Do Not Cancel Offer",
    minCancelBtnWidth: "350",
    minBtnWidth: "350",
  },
  LAST_ITEM_CANCEL_FOR_NDP: {
    title: "Do you want to Cancel the Offer/Event?",
    infoMessage:
      "The item(s) will be deleted from all divisions in the event; however, it\n will be the final item in the Offer for the following divisions.\n Deleting this will mark the task as complete.",
    confirmBtnTitle: "Yes, Delete Item and Cancel Offer",
    cancelBtnTitle: "No, Leave Item and Do Not Cancel Offer",
    minCancelBtnWidth: "350",
    minBtnWidth: "350",
  },
};

const getDisplayTaskType = (taskSubType, eventStatus) => {
  return taskSubType === WORKFLOW
    ? getWrkFlowTaskType(eventStatus)
    : TASK_TYPE_MAPPER[taskSubType] || taskSubType;
};

const getWrkFlowTaskType = eventStatus => {
  return eventStatus === EEVENT_STATUS.AGREED_PENDING
    ? CHANGED_EVENT
    : NEW_EVENT;
};

const renderEventNameWithPopover = ({
  planEventIdNbr,
  eventName,
  eventType,
  parentEvent,
}) => {
  const nationalIcon = useGetAppBaseNationalIcon();

  return (
    <Tooltip zIndex={10} anchor="bottom">
      <Tooltip.Popover>
        <div className="m-1 w-fit overflow-auto rounded text-black text-sm font-normal leading-4">
          {eventName}
        </div>
      </Tooltip.Popover>
      <div
        className={classNames({
          "text-brand text-[#1B6EBB] cursor-pointer truncate w-full font-bold text-base flex":
            true,
        })}
      >
        {eventType === EVENT_TYPE.NDP && nationalIcon && (
          <img
            src={nationalIcon}
            alt="national-icon"
            className="h-6 w-6 mr-1"
          />
        )}
        {eventType === EVENT_TYPE.NCDP
          ? `${
              parentEvent?.planEventIdNbr
                ? parentEvent?.planEventIdNbr
                : planEventIdNbr
            } - ${eventName}`
          : `${planEventIdNbr} - ${eventName}`}
      </div>
    </Tooltip>
  );
};

const { Event, Date, Vendor, Event_Status, Task_Type, Created_On } =
  PROMOTION_MANAGEMENT_COLUMNS;

export const TASK_VIEW_GRID_COLUMNS: Column<Task>[] = [
  {
    id: "event",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel _key={"event"} label={Event} view="taskView" />
    ) : (
      Event
    ),
    width: "30vw",
    value: (task: Task) => (
      <NavLink
        to={`events/edit/${task?.id}?taskType=${task?.subType}`}
        className="flex flex-col p-3 gap-2"
        id={`abs-task-view-columns-config-event-container-${task.taskId}`}
      >
        {renderEventNameWithPopover(task)}
      </NavLink>
    ),
  },
  {
    id: "date",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel _key={"date"} label={Date} view="taskView" />
    ) : (
      Date
    ),
    width: "8vw",
    value: (task: Task) => (
      <div
        id={`abs-task-view-columns-config-event-date-${task.taskId}`}
        className="text-sm"
      >
        {`${formatDate(task?.startDate)} - ${formatDate(task?.endDate)}`}
      </div>
    ),
  },
  {
    id: "vendor",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel _key={"vendor"} label={Vendor} view="taskView" />
    ) : (
      Vendor
    ),
    width: "12vw",
    value: ({ simsTaskVendor, taskId }: Task) => {
      const uniquesimsTaskList: string[] = [];
      const label = simsTaskVendor?.reduce(
        (simsTaskLabel, { vendorId, vendorName }) => {
          if (!uniquesimsTaskList.includes(vendorId)) {
            uniquesimsTaskList.push(vendorId);
            simsTaskLabel =
              simsTaskLabel +
              `${simsTaskLabel && ", "}${vendorId} ${vendorName}`;
          }
          return simsTaskLabel;
        },
        ""
      );

      return (
        <div
          id={`abs-task-view-columns-config-vendor-container-${simsTaskVendor?.[0]?.vendorId}-${taskId}`}
          className="pl-3 flex gap-2 truncate whitespace-nowrap text-sm"
        >
          <Tooltip zIndex={10} anchor="top">
            <Tooltip.Popover>
              <span className="p-1">{label}</span>
            </Tooltip.Popover>
            <div
              id={`abs-task-view-columns-config-vendor-label-${simsTaskVendor?.[0]?.vendorId}-${taskId}`}
              className="w-full truncate font-semibold leading-6"
            >
              {label}
            </div>
          </Tooltip>
        </div>
      );
    },
  },
  {
    id: "type",
    width: "8vw",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel _key={"type"} label={Event_Status} view="taskView" />
    ) : (
      Event_Status
    ),
    value: (task: Task) => (
      <div
        id={`abs-task-view-columns-config-type-container-${task.taskId}`}
        className="p-2 flex flex-col gap-2"
      >
        <div
          id={`abs-task-view-columns-config-event-status-${task.taskId}`}
          className="text-sm"
        >
          {task?.eventStatus ? task?.eventStatus : task?.currentEventStatus}
        </div>
      </div>
    ),
  },
  {
    id: "taskType",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel _key={"taskType"} label={Task_Type} view="taskView" />
    ) : (
      Task_Type
    ),
    width: "6vw",
    value: (task: Task) => (
      <div
        id={`abs-task-view-columns-config-task-type-${task.taskId}`}
        className="text-[13px] font-semibold"
      >
        {getDisplayTaskType(task?.subType, task?.eventStatus)}
      </div>
    ),
  },
  {
    id: "createdOn",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel _key={"createdOn"} label={Created_On} view="taskView" />
    ) : (
      Created_On
    ),
    width: "8vw",
    value: (task: Task) => (
      <div
        id={`abs-task-view-columns-config-created-${task?.taskId}`}
        className="p-2 flex flex-col gap-2"
      >
        <div className="text-sm">{dateFormatWithZone(task?.createdOn)}</div>
        <div className="text-sm">
          {formatTime(task?.createdOn)}{" "}
          {formatTimeWithZone(task?.createdOn).replace(
            formatTime(task?.createdOn),
            ""
          )}
        </div>
      </div>
    ),
  },
  {
    id: "completedOn",
    label: "Completed On",
    width: "8vw",
    hidden: false,
    value: (task: Task) => {
      return (
        <div
          id={`abs-task-view-columns-config-completed-${task?.taskId}`}
          className="p-2 flex flex-col gap-2"
        >
          <div className="text-sm">
            {task?.completedOn && dateFormatWithZone(task.completedOn)}
          </div>
          <div className="text-sm">
            {task?.completedOn && formatTime(task.completedOn)}{" "}
            {task?.completedOn &&
              formatTimeWithZone(task?.completedOn).replace(
                formatTime(task?.completedOn),
                ""
              )}
          </div>
        </div>
      );
    },
  },
  {
    id: "completedBy",
    label: "Completed By",
    width: "10vw",
    hidden: false,
    value: (task: Task) => {
      return (
        <div
          id={`abs-task-view-columns-config-completed-by-${task?.taskId}`}
          className="p-2 flex flex-col gap-2 truncate whitespace-nowrap text-sm"
        >
          <Tooltip zIndex={10} anchor="bottom">
            <Tooltip.Popover>
              <span className="p-1">
                {task?.completedBy ? task?.completedBy : ""}
              </span>
            </Tooltip.Popover>
            <div className="w-full truncate font-semibold leading-6">
              {task?.completedBy ? task?.completedBy : ""}
            </div>
          </Tooltip>
        </div>
      );
    },
  },
  {
    id: "actions",
    label: "Actions",
    width: "5vw",
    value: (task: Task) => {
      return (
        <div
          id={`abs-task-view-columns-config-actions-container-${task.taskId}`}
          className="flex w-full gap-2 px-3 justify-start"
        >
          {TASK_VIEW_ACTIONS_BUTTONS?.map(buttonConfig => (
            <div key={buttonConfig.key}>
              {buttonConfig.component && <buttonConfig.component task={task} />}
            </div>
          ))}
        </div>
      );
    },
  },
];

const isValidItemAction = (taskDetails, taskAction) => {
  return (
    taskDetails.type === IMMINENT_TASK &&
    taskDetails?.subType === taskAction &&
    isMerchantLoggedIn()
  );
};
const DoNotTrashIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="#1B6EBB"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
      className="lucide lucide-trash-off"
    >
      <path d="M3 6h18" />
      <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
      <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
      <line x1="22" x2="2" y1="2" y2="22" />
    </svg>
  );
};

const TrashIcon = () => {
  return <Trash color="#1B6EBB" />;
};
const CalendarIcon = () => {
  return <Calendar color="#1B6EBB" />;
};
const PPG_ITEMS_ACTIONS = [
  {
    label: TASK_ACTIONS_LABELS.DO_NOT_ADD_ITEM,
    checkIfVisible: (eventDetails: any, taskDetails?: any) => {
      return isValidItemAction(taskDetails, NEW_ITEM);
    },
  },
  {
    label: TASK_ACTIONS_LABELS.DELETE_ITEM,
    icon: TrashIcon,
    toolipText: "Delete Item",
    checkIfVisible: (eventDetails: any, taskDetails?: any) => {
      return isValidItemAction(taskDetails, DROPPED_ITEM);
    },
  },
  {
    label: TASK_ACTIONS_LABELS.DO_NOT_DELETE_ITEM,
    icon: DoNotTrashIcon,
    toolipText: "Do Not Delete Item",
    checkIfVisible: (eventDetails: any, taskDetails?: any) => {
      return (
        isValidItemAction(taskDetails, DROPPED_ITEM) &&
        taskDetails?.options?.includes("DECLINE")
      );
    },
  },
  {
    label: TASK_ACTIONS_LABELS.SET_END_DATE,
    toolipText: "Set Item End Date",
    icon: CalendarIcon,
    checkIfVisible: (eventDetails: any, taskDetails?: any) => {
      return (
        isValidItemAction(taskDetails, DROPPED_ITEM) &&
        taskDetails?.options?.includes("TAG")
      );
    },
  },
  {
    label: TASK_ACTIONS_LABELS.ADD_ITEM,
    checkIfVisible: (eventDetails: any, taskDetails?: any) => {
      return (
        isValidItemAction(taskDetails, NEW_ITEM) &&
        taskDetails?.options?.includes("ACCEPT")
      );
    },
  },
  {
    label: TASK_ACTIONS_LABELS.SEND_TO_VENDOR,
    checkIfVisible: (eventDetails: any, taskDetails?: any) => {
      return (
        isValidItemAction(taskDetails, NEW_ITEM) &&
        taskDetails?.options?.includes("DEFER") &&
        taskDetails?.eventStatus !== EEVENT_STATUS.ACTIVE
      );
    },
  },
  {
    label: TASK_ACTIONS_LABELS.ADD_ITEM_SEND_TO_VENDOR,
    checkIfVisible: (eventDetails: any, taskDetails?: any) => {
      return (
        isValidItemAction(taskDetails, NEW_ITEM) &&
        taskDetails?.options?.includes("DEFER") &&
        taskDetails?.eventStatus === EEVENT_STATUS.ACTIVE
      );
    },
  },
];
export const TASK_ACTION_MAPPER: Record<string, TaskActionType[]> = {
  [EEVENT_STATUS.PENDING_WITH_MERCHANT]: [
    {
      label: TASK_ACTIONS_LABELS.AGREE,
      checkIfVisible: (eventDetails: any) => {
        return !validateIfAnyChangesAreMade(eventDetails);
      },
    },
    {
      label: TASK_ACTIONS_LABELS.SEND_TO_VENDOR,
      checkIfVisible: (eventDetails: any) => {
        return validateIfAnyChangesAreMade(eventDetails);
      },
    },
    {
      label: TASK_ACTIONS_LABELS.SEND_BACK_WITH_COMMENT,
      checkIfVisible: (eventDetails: any) => {
        return !validateIfAnyChangesAreMade(eventDetails);
      },
    },
    {
      label: TASK_ACTIONS_LABELS.REJECT,
      checkIfVisible: (eventDetails: any) => {
        return true;
      },
    },
  ],
  [EEVENT_STATUS.PENDING_WITH_VENDOR]: [
    {
      label: TASK_ACTIONS_LABELS.AGREE,
      checkIfVisible: (eventDetails: any) => {
        return (
          !validateIfAnyChangesAreMade(eventDetails) &&
          !validateIfSentBackWithCmt(eventDetails)
        );
      },
    },
    {
      label: TASK_ACTIONS_LABELS.SEND_TO_MERCHANT,
      checkIfVisible: (eventDetails: any) => {
        return validateIfAnyChangesAreMade(eventDetails);
      },
    },
    {
      label: TASK_ACTIONS_LABELS.REJECT,
      checkIfVisible: (eventDetails: any) => {
        return true;
      },
    },
  ],
  [EEVENT_STATUS.AGREED_PENDING]: [
    {
      label: TASK_ACTIONS_LABELS.AGREE_TO_CANCEL_REQUEST,
      checkIfVisible: (eventDetails: any) => {
        return eventDetails?.planEvent?.planEventWorkFlowType === "Cancel";
      },
    },
    {
      label: TASK_ACTIONS_LABELS.AGREE_TO_PENDING_REQUEST,
      checkIfVisible: (eventDetails: any) => {
        return eventDetails?.planEvent?.planEventWorkFlowType !== "Cancel";
      },
    },
    {
      label: TASK_ACTIONS_LABELS.REJECT_PENDING_REQUEST,
      checkIfVisible: (eventDetails: any) => {
        return true;
      },
    },
  ],
  [EEVENT_STATUS.AGREED]: [...PPG_ITEMS_ACTIONS],
  [EEVENT_STATUS.ACTIVE]: [...PPG_ITEMS_ACTIONS],
};
