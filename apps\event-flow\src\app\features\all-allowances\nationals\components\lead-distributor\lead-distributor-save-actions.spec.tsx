import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import { useDispatch } from "react-redux";
import LeadDistributorSaveActions from "./lead-distributor-save-actions";
import useLeadDistributors from "../../../../create-event/hooks/useLeadDistributors";

// Mock dependencies
jest.mock("react-redux", () => ({
  useDispatch: jest.fn(),
}));

jest.mock("../../../../create-event/service/slice/lead-distributors-slice", () => ({
  leadDistributorsHandler: jest.fn(),
  resetDivisionWiseLeadDistData: jest.fn(),
  setInitialLeadDistData: jest.fn(),
}));

jest.mock("@me/data-rtk", () => ({
  useSelectorWrap: jest.fn().mockImplementation((selector) => {
    if (selector === "leadDistErrorslice_rn") {
      return { data: { isLeadDistributorError: false } };
    }
    if (selector === "leadDistributors_rn") {
      return {
        data: {
          stepData: [],
          allDivisionStepsData: {},
          leadOptions: [],
          allDivisionLeadOptions: {},
          leadSelectionType: "LEAD_DIST_LABEL",
          leadDistMode: "LEAD_DIST_MODE",
        },
      };
    }
    return {};
  }),
}));

jest.mock("../../../../create-event/hooks/useLeadDistributors", () =>
  jest.fn().mockReturnValue({
    selectedList: ["vendor1"],
    stepData: [],
  })
);

jest.mock("../../../allowance-lead-distributors/billing-selection-utils", () => ({
  onCloseDistPopup: jest.fn(),
  leadDistError: jest.fn(() => <div>Error Message</div>),
  isNationalType: jest.fn((isNdpType) => isNdpType),
}));

describe("LeadDistributorSaveActions Component", () => {
  const mockDispatch = jest.fn();
  const mockSetIsModelOpen = jest.fn();
  const mockIsAllDivLeadsSelected = jest.fn();

  beforeEach(() => {
    (useDispatch as jest.Mock).mockReturnValue(mockDispatch);
    jest.clearAllMocks();
  });


  it("renders the component correctly", () => {
    render(
      <LeadDistributorSaveActions
        isAllDivLeadsSelected={mockIsAllDivLeadsSelected}
        leadDistMode="LEAD_DIST_MODE"
        setIsModelOpen={mockSetIsModelOpen}
        divisionWiseLeadDistData={{}}
        currentDivisionId="1"
        isNdpType={false}
      />
    );

    expect(screen.getByText("Cancel")).toBeInTheDocument();
    expect(screen.getByText("Confirm")).toBeInTheDocument();
    expect(screen.getByText("Error Message")).toBeInTheDocument();
  });

  it("calls dispatch and closes the modal on Cancel button click", () => {
    render(
      <LeadDistributorSaveActions
        isAllDivLeadsSelected={mockIsAllDivLeadsSelected}
        leadDistMode="LEAD_DIST_MODE"
        setIsModelOpen={mockSetIsModelOpen}
        divisionWiseLeadDistData={{}}
        currentDivisionId="1"
        isNdpType={false}
      />
    );

    const cancelButton = screen.getByText("Cancel");
    fireEvent.click(cancelButton);
    expect(mockDispatch).toHaveBeenCalledTimes(1);

  });

  it("calls dispatch and closes the modal on Confirm button click", () => {
    mockIsAllDivLeadsSelected.mockReturnValue(true);

    render(
      <LeadDistributorSaveActions
        isAllDivLeadsSelected={mockIsAllDivLeadsSelected}
        leadDistMode="LEAD_DIST_MODE"
        setIsModelOpen={mockSetIsModelOpen}
        divisionWiseLeadDistData={{}}
        currentDivisionId="1"
        isNdpType={true}
      />
    );

    const confirmButton = screen.getByText("Confirm");
    fireEvent.click(confirmButton);
    expect(mockDispatch).toHaveBeenCalledTimes(3);
    expect(mockSetIsModelOpen).toHaveBeenCalledWith(false);
  });

  it("disables the Confirm button when conditions are not met", () => {
    mockIsAllDivLeadsSelected.mockReturnValue(false);

    render(
      <LeadDistributorSaveActions
        isAllDivLeadsSelected={mockIsAllDivLeadsSelected}
        leadDistMode="LEAD_DIST_MODE"
        setIsModelOpen={mockSetIsModelOpen}
        divisionWiseLeadDistData={{}}
        currentDivisionId="1"
        isNdpType={true}
      />
    );
    const confirmButton = screen.getByText("Confirm");
    expect(confirmButton).toBeDisabled();
  });
});