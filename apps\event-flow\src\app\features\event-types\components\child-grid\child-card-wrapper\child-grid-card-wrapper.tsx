import React from "react";
import useGetChildGridData from "../hooks/useGetChildEventGridData";
import ChildGridCard from "../child-grid-card/child-grid-card";
import { Loader } from "../../../../create-event/constants/LoadingSpinner/Loader";
import SkeletonLoader from "@albertsons/uds/molecule/SkeletonLoader";

function ChildGridCardWrapper() {
  const { data: childGridData = [], isFetching } = useGetChildGridData();

  return (
    <>
      {childGridData?.length
        ? childGridData?.map((cardData, index) => (
            <section className="my-4" key={index}>
              <ChildGridCard
                key={index}
                cardIndex={index}
                isFetching={isFetching}
              />
            </section>
          ))
        : null}
      {isFetching && !childGridData?.length && (
        <>
          <SkeletonLoader height={30} />
          <SkeletonLoader height={30} />
          <SkeletonLoader height={30} />
          <SkeletonLoader height={30} />
        </>
      )}
    </>
  );
}

export default React.memo(ChildGridCardWrapper);
