import { EEVENT_STATUS, PROMOTION_TYPE_KEYS } from "@me/util-helpers";
import {
  getLoggedInUserType,
  getNationalOfferStatus,
} from "@me-upp-js/utilities";
import { DASHBOARD_CONSTANTS } from "../../../../constants";
import classNames from "classnames";
import {
  DEFAULT_FILTER_SEARCH_CONFIG,
  DEFAULT_PAGINATION_CONFIG,
  DEFAULT_SEARCH_CONFIG,
} from "apps/promotion-management/src/app/config/dashboard-config";
import { AnyAction } from "@reduxjs/toolkit";
import { SkeletonLoader } from "../../../../shared/ui/atoms";
import { getIsNationalEvent } from "apps/event-flow/src/app/features/event-types/event-types-helper";

const {
  ALLOWANCE_TYPES,
  UOM_KEY_VALUE_MAPPER,
  OFFER_AMOUNT_FIELDS,
  PROMOTION_AMOUNT_FIELDS,
  STATUS_USER_MAPPER,
  STRIKE_OFF_FIELDS,
  PROMOTION_TYPES_WITHOUT_AMOUNTS,
} = DASHBOARD_CONSTANTS;

export const getAllowanceTypeByPerformance = performance => {
  const { allowanceCd, perfCode1, perfCode2 } = performance || {};
  const { CASE, SCAN, SHIPTOSTORE, HEADERFLAT, ITEMFLAT } = ALLOWANCE_TYPES;
  switch (allowanceCd) {
    case CASE.allowanceCd:
      return CASE.label;

    case SCAN.allowanceCd:
      return SCAN.label;

    case SHIPTOSTORE.allowanceCd:
      return SHIPTOSTORE.label;

    case HEADERFLAT.allowanceCd:
      if (perfCode1?.trim()) return HEADERFLAT.label;
      else if (perfCode2?.trim()) return ITEMFLAT.label;
      return "";

    default:
      return "";
  }
};

export const getAmountItemsKey = (isCreate = false, isHeaderFlat = false) => {
  return isHeaderFlat ? "headerFlatAmt" : isCreate ? "allowanceAmount" : null;
};

export const checkForSendBackAction = (eventDetails, subType) => {
  const { planEventHistory = [] } = eventDetails || {};
  const lastEventHistory = planEventHistory?.[planEventHistory?.length - 1];
  return (
    subType === "WORKFLOW" &&
    lastEventHistory?.eventChanges?.find(
      changeObj =>
        changeObj?.labelFieldName === "planevents.isStatusUpdateWithComment" &&
        changeObj?.afterVal === "true"
    )
  );
};

export function formatAmount(val: any, decimalPlaces = 2) {
  if (isNaN(val) || val === null) return "";
  return parseFloat(val).toFixed(decimalPlaces);
}

export const getAllowanceMinMaxAmount = (
  allowanceItems,
  isCreate = false,
  isHeaderFlat = false
) => {
  const key = getAmountItemsKey(isCreate, isHeaderFlat);
  const allowanceAmountsData: number[] = allowanceItems?.map((item: any) =>
    key ? item?.[key] || 0 : item?.allowanceItemComps?.[0]?.allowAmount || 0
  );

  const maxAmount = Math.max(...allowanceAmountsData),
    minAmount = Math.min(...allowanceAmountsData);

  if (minAmount === maxAmount) return `$${formatAmount(maxAmount)}`;
  return `$${formatAmount(minAmount)} - $${formatAmount(maxAmount)}`;
};

export const getUniqueUomTypes = (allowanceItems, uomKey) => {
  return allowanceItems?.length
    ? allowanceItems?.reduce((uomItems, item) => {
        uomItems[item?.[uomKey]] = [...(uomItems[item?.[uomKey]] || []), item];
        return uomItems;
      }, {})
    : [];
};

export const getObjectKeys = (obj: object) => {
  return Object.keys(obj || {});
};

export const getAmountLevel = (amountInfo: string[] | [] = []) => {
  const amountLevel = amountInfo?.[0]?.split(" ");
  if (amountLevel?.length > 2) {
    amountLevel?.pop();
    return amountLevel?.join(" ");
  }
  return amountLevel?.[0];
};

export const getAllowanceAmountsSubHeaderValue = (
  allowanceType = "",
  allowanceItems,
  isCreate = false,
  isUomVisable = true
) => {
  if (!allowanceItems?.length) return "";
  const { HEADERFLAT, ITEMFLAT } = ALLOWANCE_TYPES;
  const isHeaderFlat = [HEADERFLAT.label, HEADERFLAT.key].includes(
    allowanceType
  );

  //Generates amount sub header for IF & HF
  if (isHeaderFlat || [ITEMFLAT.key, ITEMFLAT.label].includes(allowanceType)) {
    return getAllowanceMinMaxAmount(allowanceItems, isCreate, isHeaderFlat);
  }

  //Generates amount sub header for SCAN, CASE & S2S
  const uomKey = isCreate ? "allowUomType" : "uom";
  const uniqueItemsByUom = getUniqueUomTypes(allowanceItems, uomKey);

  const amountInfo = getObjectKeys(uniqueItemsByUom)?.map(uomKey => {
    return `${getAllowanceMinMaxAmount(
      uniqueItemsByUom?.[uomKey],
      isCreate,
      isHeaderFlat
    )} ${UOM_KEY_VALUE_MAPPER?.[uomKey]}`;
  });

  //concat and return the result based on isUomVisable and amountInfo
  const sublevelValue =
    amountInfo.length <= 1
      ? isUomVisable
        ? amountInfo?.[0]
        : getAmountLevel(amountInfo)
      : amountInfo.join(" and ");

  return sublevelValue;
};

export const getCombinedAllowanceItems = allowances => {
  return allowances?.length
    ? allowances.reduce((itemsData = [], item) => {
        return [...itemsData, ...(item?.allowanceItems || [])];
      }, [])
    : [];
};

export const getOfferAllowanceAmount = (
  allowances: { includeInd: boolean }[] | [],
  allowanceType: string
) => {
  if (!allowances?.length) return "";
  const isHeaderFlat = ALLOWANCE_TYPES.HEADERFLAT.label === allowanceType;
  const allowancesData = allowances?.filter(allowance => allowance?.includeInd);
  return getAllowanceAmountsSubHeaderValue(
    allowanceType,
    isHeaderFlat ? allowancesData : getCombinedAllowanceItems(allowancesData),
    false
  );
};

export const checkIsValidOffer = (modifiedOffer, offerId) => {
  return !!modifiedOffer?.offerAllowanceChanges?.filter(
    ({ offerNumber = "", changes = [] }) =>
      offerNumber === offerId &&
      changes.filter(item =>
        OFFER_AMOUNT_FIELDS.includes(item?.["labelFieldName"])
      ).length
  )?.length;
};

export const getModifiedOffer = (
  offerId: string | number,
  allowanceEventInd: boolean,
  planEventHistory,
  planEventPending,
  planEventPendingChanges,
  allowanceType: string,
  previousValue: string,
  allowanceStatus: string
) => {
  const { allowances: editedRecords, updateUser } =
    planEventPending?.offerAllowances?.find(
      item => item?.offerNumber === offerId
    ) || {};

  const isEdited =
    allowanceEventInd &&
    checkIsValidUser(allowanceStatus, updateUser) &&
    checkIsValidOffer(planEventPendingChanges, offerId);
  if (isEdited) {
    const editedValue = getOfferAllowanceAmount(editedRecords, allowanceType);
    const isEqual = editedValue === previousValue;
    return getUpdatedExpandRecord(
      offerId,
      allowanceType,
      allowanceType,
      previousValue,
      isEqual ? null : editedValue,
      !isEqual
    );
  }

  const isUpdated =
    allowanceStatus !== EEVENT_STATUS.AGREED &&
    checkIsValidOffer(planEventHistory?.slice(-1)?.[0], offerId);
  if (isUpdated) {
    const updatedValue = getOfferAllowanceAmount(editedRecords, allowanceType);
    const isEqual = updatedValue === previousValue;
    return getUpdatedExpandRecord(
      offerId,
      allowanceType,
      allowanceType,
      isEqual ? previousValue : updatedValue,
      isEqual ? null : previousValue,
      false,
      !isEqual
    );
  }

  return getUpdatedExpandRecord(
    offerId,
    allowanceType,
    allowanceType,
    previousValue
  );
};

const checkIsValidUser = (allowanceStatus, updateUser) => {
  const userType = getLoggedInUserType();
  return (
    STATUS_USER_MAPPER?.[userType] === allowanceStatus ||
    ([
      EEVENT_STATUS.EXECUTED,
      EEVENT_STATUS.AGREED,
      EEVENT_STATUS.ACTIVE,
    ].includes(allowanceStatus) &&
      updateUser?.type?.toUpperCase() === userType)
  );
};

export const getUpdatedExpandRecord = (
  id: string | number,
  previousType: string | null = null,
  currentType: string | null = null,
  previousValue: string | null = null,
  currentValue: string | null = null,
  isEdited = false,
  isUpdated = false
) => {
  return {
    id,
    value: {
      type: {
        previousType,
        currentType,
      },
      amount: {
        previousValue,
        currentValue,
      },
    },
    isEdited,
    isUpdated,
  };
};

export const payloadForViewDealSheetFromTaskView = (eventsData, offer) => {
  if (!eventsData?.planEvent?.offerAllowances?.length) return [];
  const { planEvent = {} } = eventsData || {};
  const {
    offerAllowances = [],
    eventStatus = "",
    id,
    divisionIds,
    eventType,
  } = planEvent || {};
  const selectedOffer = offerAllowances?.find(
    item => item?.offerNumber === offer?.id
  );
  return {
    planEvent: id,
    id: offer?.id,
    offerNumber: offer?.id,
    divisionIds,
    allowanceSummary: selectedOffer?.allowances,
    createInd: selectedOffer?.createInd,
    eventStatus,
    allowanceChangeStatus: selectedOffer?.allowanceChangeStatus,
    eventType,
  };
};

export const getModifiedOffersList = eventDetails => {
  if (!eventDetails?.planEvent?.offerAllowances?.length) return [];
  const {
    allowanceEventInd = false,
    planEvent = {},
    planEventHistory = [],
    planEventPending = {},
    planEventPendingChanges = {},
    eventType,
  } = eventDetails || {};
  const userType = getLoggedInUserType();
  const { isNDPAndFlagEnabled: isNational } = getIsNationalEvent(eventType);
  const { offerAllowances = [], eventStatus = "" } = planEvent;
  const offerAllowancesData = offerAllowances
    ?.filter(({ updateUser = {}, allowances = [] }) => {
      const allowanceStatusValue = isNational
        ? getNationalOfferStatus(allowances)
        : allowances?.[0]?.["allowanceStatus"];
      if (
        [EEVENT_STATUS.AGREED, EEVENT_STATUS.ACTIVE].includes(eventStatus) &&
        allowanceStatusValue === EEVENT_STATUS.DRAFT &&
        updateUser?.["type"]?.toUpperCase() !== userType
      )
        return false;
      return true;
    })
    ?.map(offer => {
      const { offerNumber, allowances } = offer || {};
      const allowanceStatusValue = isNational
        ? getNationalOfferStatus(allowances)
        : allowances?.[0]?.["allowanceStatus"];
      const { allowanceStatus, performance } = allowances?.[0] || {};
      const allowanceType = getAllowanceTypeByPerformance(performance);
      const previousValue = getOfferAllowanceAmount(allowances, allowanceType);
      if (
        [EEVENT_STATUS.DRAFT, EEVENT_STATUS.CANCELED].filter(status =>
          [
            eventStatus,
            isNational ? allowanceStatusValue : allowanceStatus,
          ].includes(status)
        ).length
      ) {
        return getUpdatedExpandRecord(
          offerNumber,
          allowanceType,
          allowanceType,
          previousValue
        );
      }

      return getModifiedOffer(
        offerNumber,
        allowanceEventInd,
        planEventHistory?.slice(-1),
        planEventPending,
        planEventPendingChanges,
        allowanceType,
        previousValue,
        isNational ? allowanceStatusValue : allowanceStatus
      );
    });
  return offerAllowancesData;
};

export function getPromoProductGroups(planProductGroups) {
  return planProductGroups?.map(ppg => ppg.name);
}

export const amountOrPercent = (amount, uom, percent) => {
  return `${percent ? "" : "$"}${amount}${percent ? "%" : ""} ${uom}`;
};

export const getPromoValue = promo => {
  const amount = formatAmount(promo?.promoDetails?.amount);
  const uom = promo?.promoDetails?.unitMeasure;
  if (
    PROMOTION_TYPES_WITHOUT_AMOUNTS.includes(promo?.promoDetails?.promotionType)
  )
    return "";

  return amountOrPercent(
    amount,
    uom,
    promo?.promoDetails?.promotionType === "PERCENT_OFF"
  );
};

export const checkIsValueUpdated = (changesObject, promo) => {
  const amountResponse = getPlanEventUpdatedValue(
    changesObject,
    promo?.promotionId,
    PROMOTION_AMOUNT_FIELDS[0],
    STRIKE_OFF_FIELDS.PROMO.itemName
  );
  const typeResponse = getPlanEventUpdatedValue(
    changesObject,
    promo?.promotionId,
    PROMOTION_AMOUNT_FIELDS[1],
    STRIKE_OFF_FIELDS.PROMO.itemName
  );

  return { amountResponse, typeResponse };
};

const getModifiedPromotion = (
  promo,
  promotionEventInd,
  planEvent,
  planEventHistory,
  planEventPendingChanges,
  planEventPending
) => {
  const {
    promotionId,
    promoDetails = {},
    promotionWorkflowStatus,
  } = promo || {};
  const { promotionType } = promoDetails;

  const updatedPromo =
    planEventPending?.promotionsList?.find(
      item => item?.promotionId === promo?.promotionId
    ) || {};
  const { updateUser } = updatedPromo || {};

  let { amountResponse, typeResponse } = checkIsValueUpdated(
    planEventPendingChanges,
    promo
  );

  const isEdited =
    promotionEventInd &&
    checkIsValidUser(promotionWorkflowStatus, updateUser) &&
    (amountResponse?.[0]?.afterVal || typeResponse?.[0]?.afterVal);

  if (isEdited) {
    return getUpdatedExpandRecord(
      promotionId,
      PROMOTION_TYPE_KEYS[promotionType],
      PROMOTION_TYPE_KEYS[updatedPromo?.promoDetails?.promotionType],
      getPromoValue(promo),
      getPromoValue(updatedPromo),
      true,
      false
    );
  }

  amountResponse = checkIsValueUpdated(
    planEventHistory?.[planEventHistory?.length - 1],
    promo
  )?.amountResponse;

  typeResponse = checkIsValueUpdated(
    planEventHistory?.[planEventHistory?.length - 1],
    promo
  )?.typeResponse;

  const isUpdated =
    !promotionEventInd &&
    (amountResponse?.[0]?.afterVal || typeResponse?.[0]?.afterVal);

  if (isUpdated) {
    return getUpdatedExpandRecord(
      promotionId,
      PROMOTION_TYPE_KEYS[updatedPromo?.promoDetails?.promotionType],
      PROMOTION_TYPE_KEYS[promotionType],
      getPromoValue(updatedPromo),
      getPromoValue(promo),
      false,
      true
    );
  }

  return getUpdatedExpandRecord(
    promotionId,
    PROMOTION_TYPE_KEYS[promotionType],
    PROMOTION_TYPE_KEYS[promotionType],
    getPromoValue(promo)
  );
};
export function getPromotionList(eventDetails) {
  if (!eventDetails?.planEvent?.promotionsList?.length) return [];
  const {
    promotionEventInd = false,
    planEvent = {},
    planEventHistory = [],
    planEventPendingChanges = {},
    planEventPending = {},
  } = eventDetails || {};

  const { promotionsList } = planEvent;
  const promotions = promotionsList.map(promo => {
    if (
      [
        EEVENT_STATUS.DRAFT,
        EEVENT_STATUS.CANCELED,
        EEVENT_STATUS.AGREED,
        EEVENT_STATUS.REJECTED,
      ].filter(status =>
        [planEvent?.eventStatus, promo?.promotionWorkflowStatus].includes(
          status
        )
      ).length
    ) {
      const { promotionId, promoDetails = {} } = promo || {};
      const { promotionType } = promoDetails;
      return getUpdatedExpandRecord(
        promotionId,
        PROMOTION_TYPE_KEYS[promotionType],
        PROMOTION_TYPE_KEYS[promotionType],
        getPromoValue(promo)
      );
    }

    return getModifiedPromotion(
      promo,
      promotionEventInd,
      planEvent,
      planEventHistory,
      planEventPendingChanges,
      planEventPending
    );
  });
  return promotions;
}

export const getPlanEventUpdatedValue = (
  planObject,
  itemNumber,
  fieldName,
  itemName
) => {
  const { OFFER, PROMO } = STRIKE_OFF_FIELDS;
  let itemField, itemNumberField;
  if (itemName === OFFER.itemName) {
    itemField = OFFER.itemField;
    itemNumberField = OFFER.itemNumberField;
  } else if (itemName === PROMO.itemName) {
    itemField = PROMO.itemField;
    itemNumberField = PROMO.itemNumberField;
  }
  return planObject?.[itemField]
    ?.find(item => item?.[itemNumberField] === itemNumber)
    ?.changes?.filter(history => history?.labelFieldName === fieldName);
};

export function renderOfferPromoSummary({ isOffer, id, values }) {
  return (
    <div
      id={`abs-task-service-${isOffer ? "offer" : "promo"}-${id}`}
      className="flex gap-2 items-start justify-start"
    >
      <div className="border-r-[1px] pr-2 border-[#C8DAEB] shrink-0">
        {isOffer ? `Offer #${id}` : `Promo #${id}`}
      </div>

      <div id="abs-task-service">{renderValues(values, isOffer)}</div>
    </div>
  );
}

export const renderValues = (values, isOffer) => {
  const { isEdited, isUpdated, value } = values || {};
  const { type, amount } = value || {};

  return (
    <>
      <div
        id="abs-task-service"
        className={classNames({ "line-through": isEdited || isUpdated })}
      >{`${type?.previousType} ${`${isOffer ? "of" : ""}`} ${
        amount?.previousValue && `(${amount?.previousValue})`
      }`}</div>
      <div
        id="abs-task-service"
        className={classNames(
          { "text-[#1B6EBB]": isEdited },
          { "text-[#AB4205]": isUpdated }
        )}
      >
        {(isUpdated || isEdited) &&
          `${type?.currentType} ${`${isOffer ? "of" : ""}`} ${
            amount?.currentValue && `(${amount?.currentValue})`
          }`}
      </div>
    </>
  );
};

export const sortItems = items => {
  return items.sort((a, b) => {
    return a?.id - b?.id;
  });
};
export const formatUPC = (upc = "") => {
  // Remove any non-digit characters from the input
  const digitsOnly = upc?.replace(/\D/g, "");

  // Determine the UPC format based on its length
  if (digitsOnly.length === 12) {
    // Scan UPC format: Country(1)-System(1)-Manuf(5)-Item(5)
    const country = digitsOnly.slice(0, 1);
    const system = digitsOnly.slice(1, 2);
    const manufacturer = digitsOnly.slice(2, 7);
    const item = digitsOnly.slice(7, 12);

    // Construct the formatted scan UPC string
    const formatted = `${country}-${system}-${manufacturer}-${item}`;

    return formatted;
  } else if (digitsOnly.length === 13) {
    // Case UPC format: (1)-(1)-(1)-(5)-(5)
    const section1 = digitsOnly.slice(0, 1);
    const country = digitsOnly.slice(1, 2);
    const system = digitsOnly.slice(2, 3);
    const manufacturer = digitsOnly.slice(3, 8);
    const item = digitsOnly.slice(8, 13);

    // Construct the formatted case UPC string
    const formatted = `${section1}-${country}-${system}-${manufacturer}-${item}`;

    return formatted;
  } else {
    // Invalid UPC format
    return "";
  }
};

export const updateHederFilterData = (
  setFilterData: (value: unknown) => AnyAction,
  currentValue = {},
  updatedValue = {},
  isDefault = false
) => {
  return setFilterData({
    ...currentValue,
    ...(isDefault && DEFAULT_FILTER_SEARCH_CONFIG),
    ...DEFAULT_PAGINATION_CONFIG,
    ...DEFAULT_SEARCH_CONFIG,
    ...updatedValue,
  });
};

export const getInitialLoader = () => {
  const columns = ["Event", "Type", "Status", "Actions"];
  return [...columns, ...columns]?.map(column => (
    <div
      id={`abs-task-service-${column.toLowerCase()}`}
      className="grid lg:grid-cols-11 md:grid-cols-6 gap-4 px-4 py-4 border-b border-[#c8daeb]"
    >
      {columns.map((column, index) => (
        <div
          id={`abs-task-service-${column.toLowerCase()}-${index}`}
          className={`col-span-${!index ? 5 : 2}`}
        >
          <SkeletonLoader />
        </div>
      ))}
    </div>
  ));
};

// funtion to return a unique number
export const getUniqueNumber = () => {
  return Math.floor(Math.random() * 1000000);
};
