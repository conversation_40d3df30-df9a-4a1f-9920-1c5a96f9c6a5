import { useCallback, useEffect, useState } from "react";
import { useSelectorWrap } from "@me/data-rtk";
import { CommonModal } from "../../../create-event/components/cards";
import efConstants from "../../../../shared/ef-constants/ef-constants";
import { useGetOfferAgreementPdfDataMutation } from "../../../create-event/service/apis/allowance-api";

export const ViewAgreementModal = ({
  setIsAllowanceAgreementVisible,
  eventId = "",
  divisionName = "",
  allowanceType = "",
  offerNumber = "",
  allowanceChangeStatus = "",
  eventStatus = "",
  allowances = [],
  eventType = "",
}) => {
  const {
    data: { allowanceEventInd },
  } = useSelectorWrap("plan_event_indicators");

  const [postOfferAgreementPdfData, { isLoading }] =
    useGetOfferAgreementPdfDataMutation();

  const [isModalPopupOpen, setModalPopupOpen] = useState(true);
  const [isWindows, setIsWindows] = useState(false);

  const [agreementUrl, setAgreementUrl] = useState<string>("");

  useEffect(() => {
    const userAgent = navigator.userAgent.toLowerCase();
    if (userAgent.includes("win")) {
      setIsWindows(true);
    }
    getAgreementData();
  }, []);

  const getAgreementData = async () => {
    const excludedAllowances = allowances?.reduce(
      (acc, { includeInd = null, excludedTag = false, allowanceIdNbr }) => {
        if (!includeInd && excludedTag && allowanceIdNbr)
          return [...acc, allowanceIdNbr];
        return acc;
      },
      []
    );
    try {
      const payload = {
        eventId,
        division: divisionName,
        allowanceType,
        offerNumber,
        phoneNumber: "",
        allowanceChangeStatus,
        eventStatus,
        excludedAllowances,
        allowanceEventInd,
        eventType,
      };
      await postOfferAgreementPdfData(JSON.stringify(payload))
        .then(async response => {
          if (response?.data) {
            const url = window.URL.createObjectURL(response?.data);
            setAgreementUrl(url);
          } else setAgreementUrl("");
        })
        .catch(err => {
          console.error(err);
          setAgreementUrl("");
        });
    } catch (error) {
      console.error(error);
      setAgreementUrl("");
    }
  };

  const handleAgreementCancel = useCallback(() => {
    setIsAllowanceAgreementVisible(false);
    document.body.style.overflow = "visible";
  }, []);

  const getAgreementContent = () => {
    return (
      <div
        className={`flex flex-col gap-[16px] mx-[56px] pt-6 ${efConstants.componentClassName.GET_AGREEMENT_CONTENT}`}
      >
        <div className="text-center select-none font-bold text-[28px] text-[#2B303C]">
          Allowance Agreement
        </div>
        <div
          className={`fixed top-[100px] w-[90%] h-[80%] flex justify-center items-center ${
            isWindows ? "pb-[20px]" : ""
          }`}
        >
          {agreementUrl && (
            <iframe
              src={`${agreementUrl}#navpanes=0`}
              className="w-full h-full max-w-full max-h-full border-0"
              title="Allowance_Agreement_Offer"
            />
          )}
        </div>
        <div className={`absolute bottom-3 ${isWindows ? "pt-[10px]" : ""}`}>
          <a
            href={agreementUrl}
            download={`Allowance_Agreement_Offer_${offerNumber}`}
            target="_blank"
            rel="noreferrer"
            className="text-[#1B6EBB]"
          >
            Download PDF{" "}
          </a>
        </div>
      </div>
    );
  };

  return (
    <div className={`${efConstants.componentClassName.VIEW_AGREEMENT_MODAL}`}>
      <CommonModal
        isModalPopupOpen={isModalPopupOpen}
        setModalPopupOpen={setModalPopupOpen}
        modalContent={getAgreementContent()}
        onClose={handleAgreementCancel}
        // minHeight={agreementUrl ? "auto" : 650}
        minHeight={"95%"}
        minWidth={"95%"}
        isLoading={isLoading}
        cancelBtnHandler={handleAgreementCancel}
        modalContainerClass="pdf-viewer-modal"
      />
    </div>
  );
};
