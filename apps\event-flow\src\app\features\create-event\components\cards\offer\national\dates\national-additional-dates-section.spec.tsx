import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import NationalAdditionalDatesSection from "./national-additional-dates-section";
import { getAllowanceKey } from "../../../../../service/allowance/allowance-service";

// --- Mock the allowance service ---
jest.mock("../../../../../service/allowance/allowance-service", () => ({
  getAllowanceKey: jest.fn(),
}));

// --- Mock the child components ---
jest.mock("./national-scan-additional-dates-section", () => {
  return function MockNationalScanAdditionalDatesSection(props) {
    return (
      <div data-testid="national-scan-additional-dates-section">
        National Scan Additional Dates Section
      </div>
    );
  };
});

jest.mock("./national-case-additional-dates-section", () => {
  return function MockNationalCaseAdditionalDatesSection(props) {
    return (
      <div data-testid="national-case-additional-dates-section">
        National Case Additional Dates Section
      </div>
    );
  };
});

describe("NationalAdditionalDatesSection - formControls fallback", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders nothing when formControls is undefined", () => {
    // When formControls is undefined, getValues defaults to () => null,
    // so formData becomes {} and allowanceType is "".
    // We'll have getAllowanceKey return an empty string.
    getAllowanceKey.mockReturnValue("");
    
    render(
      <NationalAdditionalDatesSection
        cardIndex={0}
        cardItemIndex={0}
        // formControls is omitted (undefined)
        sectionConfiguration={{}}
        isEditEnable={false}
        offerMapKey="DSD_WHSE_RETAIL_DIVISION"
        vehicleFields={{}}
        sectionKey="test-section"
      />
    );

    // Since allowanceType is "", no mapping matches, so no child is rendered.
    expect(
      screen.queryByTestId("national-scan-additional-dates-section")
    ).not.toBeInTheDocument();
    expect(
      screen.queryByTestId("national-case-additional-dates-section")
    ).not.toBeInTheDocument();
  });

  it("renders nothing when formControls.getValues is undefined", () => {
    // Pass a formControls object that does not include getValues.
    const formControls = {}; // getValues is missing
    // For consistency, let getAllowanceKey return an empty string.
    getAllowanceKey.mockReturnValue("");

    render(
      <NationalAdditionalDatesSection
        cardIndex={1}
        cardItemIndex={1}
        formControls={formControls}
        sectionConfiguration={{}}
        isEditEnable={false}
        offerMapKey="DSD_WHSE_RETAIL_DIVISION"
        vehicleFields={{}}
        sectionKey="test-section-2"
      />
    );

    // With getValues undefined, the default function returns null, so no child is rendered.
    expect(
      screen.queryByTestId("national-scan-additional-dates-section")
    ).not.toBeInTheDocument();
    expect(
      screen.queryByTestId("national-case-additional-dates-section")
    ).not.toBeInTheDocument();
  });
});
