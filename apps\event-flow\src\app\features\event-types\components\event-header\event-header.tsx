import Breadcrumb from "@albertsons/uds/molecule/Breadcrumb";
import _ from "lodash";
import Divider from "@albertsons/uds/molecule/Divider";
import React, { useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import { NavLink, useLocation, useNavigate } from "react-router-dom";
import EventStatusBadge from "./event-status-badge";
import { useDispatch } from "react-redux";
import {
  editEventStatusHandler,
  eventDetailsDataHandler,
  setIsProductSourcesEmptyHandler,
} from "../../../create-event/service/slice/event-detail-slice";
import DEFAULT_EVENT_DETAILS from "../../../../features/create-event/constants/event-details/eventDetails";
import { useGetAppBasePath } from "@me/util-helpers";
import { allowanceTempWorkReset } from "../../../create-event/service/slice/allowance-temp-work-slice";
import { resetEventPlanCardUpdateDataSlice } from "../../../create-event/service/slice/event-plan-slice";
import {
  addRemoveNewAllowance,
  allowanceFormReset,
  allowanceNewCardConfiguration,
  offerCardConfiguration,
  resetIsOfferSectionUpdated,
  resetOfferAmountsData,
  setAllowTypeChange,
  setOfferSubCardConfiguration,
  setPromoSubCardConfiguration,
} from "../../../create-event/service/slice/allowance-details-slice";
import {
  useDeleteAllowancePendingCancelDataMutation,
  useDeleteAllowanceTempWorkDataMutation,
  useDeleteNationalAllowanceTempWorkDataMutation,
  useNationalDeleteAllowancePendingCancelDataMutation,
} from "../../../create-event/service/apis/allowance-api";
import { useSelectorWrap } from "@me/data-rtk";
import efConstants from "../../../../shared/ef-constants/ef-constants";
import EventUnsavedWarningModal from "./event-unsaved-warning-modal";
import { UNSAVED_WARNING_MODAL_PROPS } from "../../../create-event/constants/event-status/contsants";
import { appConstants } from "@me/utils-root-props";

import EventTypeSection from "./event-type-section";
import { RBAC } from "albertsons-react-rbac";
import EventHistorySection from "../event-histroy/event-history-section";

import { EEVENT_STATUS } from "../event-action/event-action.model";
import { setAllowancesDeletePendingCancel } from "../../../create-event/service/slice/allowances-response-slice";
import {
  getNationalOfferStatus,
  isAllowanceFeatureEnabled,
  useGetAppBaseNationalIcon,
} from "@me-upp-js/utilities";
import { getIsNationalEvent } from "../../event-types-helper";
import {
  resetDivisionWiseShrdWhseData,
  resetNationalDivisionsConfig,
} from "../../../all-allowances/nationals/slices/national-main-entry-slices";
import { EVENT_TYPE } from "../../../create-event/constants/constants";
interface IEventHeaderProps {
  eventId?: string;
  eventStatus?: string;
  promoProductGroup?: string;
  eventType?: string | undefined;
  eventTypeName?: string;
  isChangeEventTypeVisible?: boolean;
  divisionIds?: string[] | undefined;
  negotiationSimsVendors?: [] | undefined;
  state?: any;
  scrolledMore?: boolean;
  parentEvent?: string;
}

export default function EventHeader({
  eventId,
  eventStatus,
  promoProductGroup,
  eventType = "",
  eventTypeName,
  isChangeEventTypeVisible,
  divisionIds,
  negotiationSimsVendors,
  state,
  scrolledMore,
  parentEvent,
}: IEventHeaderProps) {
  const location = useLocation();
  const formContext = useFormContext();
  const [isOpen, setOpen] = useState<boolean>(false);

  interface NavigationDetails {
    node: {
      uri: string;
      label: string;
    };
    navigate: () => void;
    handleSelect: (uri: string, navigate: () => void) => void;
  }

  const [navigationDetials, setNavigationDetails] = useState<NavigationDetails>(
    {
      node: {
        uri: "",
        label: "",
      },
      navigate: () => null,
      handleSelect: () => null,
    }
  );
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [modalType, setModalType] = useState("RETURN");
  const [modalProps, setModalProps] = useState({});
  const { basePath } = useGetAppBasePath();
  const { allowanceData } = useSelectorWrap("allowance_temp_work")?.data;
  const [deleteAllowanceTempWorkData] =
    useDeleteAllowanceTempWorkDataMutation();
  const [deleteNationalAllowanceTempWorkData] =
    useDeleteNationalAllowanceTempWorkDataMutation();
  const { isNDPAndFlagEnabled: isNational } = getIsNationalEvent(eventType);
  const nationalIcon = useGetAppBaseNationalIcon();
  const deleteTempMutation = isNational
    ? deleteNationalAllowanceTempWorkData
    : deleteAllowanceTempWorkData;
  const backToDefaultTaskViewHandler = (e: React.MouseEvent) => {
    e?.preventDefault();
    // dispatch(setClearSearchInput(false));
    dispatch(
      setIsProductSourcesEmptyHandler({
        isProductSourcesEmpty: false,
      })
    );
    localStorage.setItem("EVENT_ID", "");
    localStorage.setItem("DIVISION_ID", "");
    localStorage.setItem("PLAN_EVENT_ID_NBR", "");
    if (
      formContext?.getValues("isEventChanged") ||
      formContext?.getValues("isPromoChanged") ||
      formContext?.getValues("isAllowanceChanged")
    ) {
      setOpen(true);
      setModalType("RETURN");
    } else {
      dispatch(eventDetailsDataHandler({ ...DEFAULT_EVENT_DETAILS }));
      dispatch(allowanceTempWorkReset());
      dispatch(allowanceFormReset());
      dispatch(resetEventPlanCardUpdateDataSlice());
      dispatch(addRemoveNewAllowance({}));
      dispatch(
        allowanceNewCardConfiguration({
          isNewAllowance: true,
          stepperType: "offerAllowances",
          isAddAnotherOffer: false,
        })
      );
      isNational ? deleteNationalTempData() : deleteTempWorkData();
      resetCardSlices();
      dispatch(editEventStatusHandler({ isEventCardOpen: false }));
      navigate(`${basePath}/`);
    }
  };

  const {
    data: { allowanceData: allowanceTempWorkData },
  } = useSelectorWrap("allowance_temp_work");

  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const [deleteAllowancePendingCancelData] =
    useDeleteAllowancePendingCancelDataMutation();
  const [deleteNationalAllowancePendingCancelData] =
    useNationalDeleteAllowancePendingCancelDataMutation();
  const getAllowData = () =>
    isNational && Array.isArray(allowanceData)
      ? allowanceData?.[0]
      : allowanceData;
  const onChange = async () => {
    setOpen(false);
    dispatch(eventDetailsDataHandler({ ...DEFAULT_EVENT_DETAILS }));
    dispatch(allowanceTempWorkReset());
    dispatch(allowanceFormReset());
    dispatch(resetEventPlanCardUpdateDataSlice());
    if (
      navigationDetials?.node?.uri &&
      localStorage.getItem(
        `${appConstants.RESTRICT_NAV_STORAGE_NAME}${sessionStorage.getItem(
          appConstants.SESSION_STORAGE_RESTRICTED_APP_KEY_NAME
        )}`
      ) === "true"
    ) {
      localStorage.removeItem(
        `${appConstants.RESTRICT_NAV_STORAGE_NAME}${sessionStorage.getItem(
          appConstants.SESSION_STORAGE_RESTRICTED_APP_KEY_NAME
        )}`
      );
      sessionStorage.removeItem(
        appConstants.SESSION_STORAGE_RESTRICTED_APP_KEY_NAME
      );
      sessionStorage.removeItem("currentApp");
      isNational ? deleteNationalTempData() : deleteTempWorkData();
      navigationDetials.handleSelect(
        navigationDetials?.node?.uri,
        navigationDetials?.navigate
      );
    } else {
      const shouldDeleteTemp =
        isNational && Array.isArray(allowanceTempWorkData)
          ? allowanceTempWorkData?.[0]?.tempWorkAllowanceId
          : allowanceTempWorkData?.tempWorkAllowanceId;
      shouldDeleteTemp &&
        (await deleteTempMutation({
          URL_PARAM: isNational
            ? eventDetailsData?.id
            : allowanceTempWorkData?.tempWorkAllowanceId,
        }));

      const allowanceNo = getAllowData()?.offerAllowancesId;
      const offerNumber = getAllowData()?.offerNumber;
      if (allowanceNo) {
        const allowance = eventDetailsData?.planEvent?.offerAllowances.find(
          allowance => allowance.id === allowanceNo
        );

        const allowStatusToCheck = isNational
          ? getNationalOfferStatus(allowance?.allowances)
          : allowance?.allowances?.[0]?.allowanceStatus;
        if (
          allowance &&
          [
            EEVENT_STATUS.AGREED,
            EEVENT_STATUS.ACTIVE,
            EEVENT_STATUS.EXECUTED,
          ].includes(allowStatusToCheck)
        ) {
          const deleteMutationHandler = isNational
            ? deleteNationalAllowancePendingCancelData
            : deleteAllowancePendingCancelData;
          await deleteMutationHandler({
            URL_PARAM: isNational ? offerNumber : allowanceNo,
          });
        }
      }
      resetCardSlices();
      dispatch(
        setAllowancesDeletePendingCancel({ isAllowanceCancelled: true })
      );
      navigate(`${basePath}/`);
    }
  };
  const resetCardSlices = () => {
    dispatch(
      offerCardConfiguration({
        editCardConfig: {},
        openCardConfig: {},
      })
    );
    dispatch(
      setOfferSubCardConfiguration({
        offerSubCardConfig: {},
      })
    );
    dispatch(
      setPromoSubCardConfiguration({
        promoSubCardConfig: {},
      })
    );
    dispatch(resetOfferAmountsData());
    dispatch(resetIsOfferSectionUpdated());
    dispatch(resetNationalDivisionsConfig());
    dispatch(resetDivisionWiseShrdWhseData());
  };
  const deleteNationalTempData = () => {
    if (allowanceData?.length) {
      const tempWorkAllowanceId =
        sessionStorage.getItem("TEMPWORK_ID") ||
        allowanceData?.[0]?.tempWorkAllowanceId ||
        "";
      const tempWorkLastUpdatedUser =
        sessionStorage.getItem("TEMPWORK_USER") ||
        allowanceData?.[0]?.lastUpdUserId ||
        allowanceData?.[0]?.createUserId;
      ("");
      deleteTempAction({ tempWorkAllowanceId, tempWorkLastUpdatedUser });
    }
  };
  const deleteTempAction = ({
    tempWorkAllowanceId,
    tempWorkLastUpdatedUser,
  }) => {
    const { eventStatus } = eventDetailsData;
    const loggedInUser = JSON.parse(
      localStorage.getItem("OAM_REMOTE_USER") || ""
    )?.toUpperCase();
    if (
      tempWorkAllowanceId &&
      tempWorkLastUpdatedUser?.toUpperCase() === loggedInUser
    ) {
      const isDeleted = deleteTempMutation({
        URL_PARAM: isNational ? eventDetailsData?.id : tempWorkAllowanceId,
      });
      if (
        sessionStorage.getItem("OFFER_ID") &&
        [
          EEVENT_STATUS.AGREED,
          EEVENT_STATUS.ACTIVE,
          EEVENT_STATUS.EXECUTED,
        ].includes(eventStatus)
      ) {
        deleteAllowancePendingCancelData({
          URL_PARAM: sessionStorage.getItem("OFFER_ID"),
        });
      }
      if (isDeleted) {
        sessionStorage.removeItem("TEMPWORK_USER");
        sessionStorage.removeItem("TEMPWORK_ID");
        sessionStorage.removeItem("OFFER_ID");
        resetCardSlices();
        dispatch(allowanceTempWorkReset());
        dispatch(allowanceFormReset());
      }
    }
  };
  const deleteTempWorkData = () => {
    const tempWorkAllowanceId =
      sessionStorage.getItem("TEMPWORK_ID") ||
      allowanceData?.tempWorkAllowanceId ||
      "";
    const tempWorkLastUpdatedUser =
      sessionStorage.getItem("TEMPWORK_USER") ||
      allowanceData?.lastUpdUserId ||
      allowanceData?.createUserId;
    ("");
    deleteTempAction({ tempWorkAllowanceId, tempWorkLastUpdatedUser });
  };

  const handleBeforeUnload = () => {
    if (window.location.pathname.includes("/events/edit/")) {
      isNational ? deleteNationalTempData() : deleteTempWorkData();
      if (isAllowanceFeatureEnabled) {
        dispatch(
          setAllowTypeChange({
            isAllowanceTypeChanged: {},
          })
        );
      }
    }
  };

  useEffect(() => {
    const allowData = getAllowData();
    !_.isEmpty(allowData) &&
      sessionStorage.setItem(
        "TEMPWORK_USER",
        allowData?.lastUpdUserId || allowData?.createUserId
      );
  }, [JSON.stringify(allowanceData)]);

  React.useEffect(() => {
    if (
      !!formContext?.getValues("isEventChanged") ||
      !!formContext?.getValues("isPromoChanged") ||
      !!formContext?.getValues("isAllowanceChanged")
    ) {
      const allowData = getAllowData();
      localStorage.setItem(
        `${appConstants.RESTRICT_NAV_STORAGE_NAME}${sessionStorage.getItem(
          appConstants.SESSION_STORAGE_RESTRICTED_APP_KEY_NAME
        )}`,
        "true"
      );
      window.addEventListener("beforeunload", handleBeforeUnload);
      sessionStorage.setItem(
        "TEMPWORK_ID",
        allowData?.tempWorkAllowanceId || ""
      );
    } else {
      localStorage.removeItem(
        `${appConstants.RESTRICT_NAV_STORAGE_NAME}${sessionStorage.getItem(
          appConstants.SESSION_STORAGE_RESTRICTED_APP_KEY_NAME
        )}`
      );
    }
  }, [
    formContext?.getValues("isEventChanged"),
    formContext?.getValues("isPromoChanged"),
    formContext?.getValues("isAllowanceChanged"),
    JSON.stringify(allowanceData),
  ]);

  useEffect(() => {
    const handleLeftNav = (event: Event) => {
      const leftNavEvent = event as CustomEvent<{
        node: {
          uri: string;
          label: string;
        };
        navigate: () => void;
        handleSelect: () => void;
      }>;

      // Stop the event from propagating further
      event.stopPropagation();
      // Prevent any default action associated with the event
      event.preventDefault();

      setNavigationDetails({
        node: leftNavEvent.detail.node,
        navigate: leftNavEvent.detail.navigate,
        handleSelect: leftNavEvent.detail.handleSelect,
      });
      setModalType("LEFT_NAV");
      setOpen(true);
    };

    window.addEventListener(
      `${appConstants.RESTRICT_NAV_EVENT_NAME}${sessionStorage.getItem(
        appConstants.SESSION_STORAGE_RESTRICTED_APP_KEY_NAME
      )}`,
      handleLeftNav as EventListener
    );

    // Cleanup the event listener on component unmount
    return () => {
      window.removeEventListener(
        `${appConstants.RESTRICT_NAV_EVENT_NAME}${sessionStorage.getItem(
          appConstants.SESSION_STORAGE_RESTRICTED_APP_KEY_NAME
        )}`,
        handleLeftNav as EventListener
      );
    };
  }, []);

  useEffect(() => {
    setModalProps({
      ...modalProps,
      isOpen,
      onChange,
      setOpen,
      warningTitle: UNSAVED_WARNING_MODAL_PROPS[modalType].WARNING_TITLE,
      warningBodyText: UNSAVED_WARNING_MODAL_PROPS[modalType].WARNING_BODY_TEXT,
      confirmButtonLabel:
        UNSAVED_WARNING_MODAL_PROPS[modalType].CONFIRM_BUTTON_LABEL,
      cancelButtonLabel:
        UNSAVED_WARNING_MODAL_PROPS[modalType].CANCEL_BUTTON_LABEL,
      warningLabel: UNSAVED_WARNING_MODAL_PROPS[modalType].WARNING_LABEL,
    });
  }, [isOpen]);

  useEffect(() => {
    return () => {
      if (
        !!formContext?.getValues("isEventChanged") ||
        !!formContext?.getValues("isPromoChanged") ||
        !!formContext?.getValues("isAllowanceChanged")
      ) {
        if (window.location.pathname.includes("/events/edit/"))
          window.removeEventListener("beforeunload", handleBeforeUnload);
      }
    };
  }, []);
  return (
    <div
      className={`${efConstants.componentClassName.EVENT_HEADER} ${
        scrolledMore && eventId ? "inline-flex items-center" : ""
      }`}
      id="event-header"
    >
      {scrolledMore && eventId ? (
        <div className="inline-flex items-center gap-1">
          <Breadcrumb onClick={backToDefaultTaskViewHandler} label="BACK" />
          {<span className="mr-1">/</span>}
        </div>
      ) : (
        <Breadcrumb onClick={backToDefaultTaskViewHandler} label="BACK" />
      )}

      <div
        className={`flex gap-[16px] items-center ${
          scrolledMore && eventId ? "py-2" : "mt-1 pb-[12px]"
        }`}
      >
        <span
          className={`flex items-center gap-2 leading-[32px] whitespace-nowrap
           ${
             scrolledMore && eventId
               ? "text-[18px] font-normal pt-[0px]"
               : "text-[24px] font-bold pt-[4px]"
           }`}
        >
          {!eventId ? (
            <>New Event</>
          ) : (
            <>
              {eventType === EVENT_TYPE.NDP && nationalIcon && (
                <span>
                  <img
                    src={nationalIcon}
                    className="h-6 w-6 mr-1"
                    alt="national-icon"
                  />
                </span>
              )}
              Event
              <span className="text-[18px] font-normal">
                {eventType !== EVENT_TYPE.NCDP
                  ? `ID #${eventId}`
                  : `ID #${parentEvent?.["planEventIdNbr"]}-${divisionIds}`}
              </span>
            </>
          )}
        </span>

        <Divider height={24} width={1} color="#C8DAEB" className="" />
        <EventTypeSection
          eventType={eventType}
          eventTypeName={eventTypeName}
          isChangeEventTypeVisible={isChangeEventTypeVisible}
          divisionIds={divisionIds}
          negotiationSimsVendors={negotiationSimsVendors}
          state={state}
        />
        {eventType === EVENT_TYPE.NCDP && (
          <div className="cursor-pointer">
            <NavLink to={`${basePath}/events/edit/${parentEvent?.["eventId"]}`}>
              <button className="text-sm text-[#1b6ebb]">
                View National Event
              </button>
            </NavLink>
          </div>
        )}

        {location?.pathname !== "/" && (
          <>
            <Divider height={24} width={1} color="#C8DAEB" className="" />
            {eventStatus ? <EventStatusBadge event={eventStatus} /> : null}
          </>
        )}
      </div>
      <EventUnsavedWarningModal modalProps={modalProps} />
    </div>
  );
}
