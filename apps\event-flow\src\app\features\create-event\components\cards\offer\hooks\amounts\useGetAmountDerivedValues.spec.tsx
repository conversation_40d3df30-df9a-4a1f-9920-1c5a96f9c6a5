import { renderHook } from "@testing-library/react";
import { useGetAmountDerivedValues } from "./useGetAmountDerivedValues";

describe("useGetAmountDerivedValues Hook", () => {
  const mockProps = {
    sectionKey: "Allowance Amounts",
    allowanceTempWorkData: {
      tempWorkAllowanceId: "67bc3efc4d69215b66479f6a",
      planEventId: "67b736792a5a6650b2c813b5",
      createUserId: "PJAIN03",
      lastUpdUserId: "PJAIN03",
      createTs: 1740390140136,
      lastUpdTs: 1740390145101,
      allowanceType: "SCAN",
      allowanceTypeSpecification: {
        scanAllow: {
          allowanceType: "SCAN",
          createInd: "TC",
          allowancePerformanceId: "63a3a12743a6cee87995b834",
          vehicleDatesMap: {
            DSD_WHSE_RETAIL_DIVISION: {
              vehicleId: "66f1eded1d792055ff84c77c",
              vehicleRef: {
                vehicleId: "66f1eded1d792055ff84c77c",
                vehicleNm: "27 Week 19 Insert 2025",
                sourceVehicleSk: 0,
                vehicleType: {
                  vehicleTypeId: "66a3f061900b0b47a182376f",
                  sourceVehicleTypeSk: 198,
                  vehicleTypNm: "insrt",
                  vehicleTypDesc: "Weekly Insert",
                },
              },
              dateRange: {
                startDate: "2025-05-07",
                endDate: "2025-05-13",
              },
            },
          },
          allowancesMap: {
            DSD_WHSE_RETAIL_DIVISION: [
              {
                allowanceIdNbr: 1,
                vendorNbr: "",
                costAreaDesc: "",
                defaultAllowanceDates: {
                  allowanceStartDate: "2025-05-07",
                  allowanceEndDate: "2025-05-13",
                  performStartDate: "2025-05-07",
                  performEndDate: "2025-05-13",
                  orderStartDate: "0001-01-01",
                  orderEndDate: "0001-01-01",
                  shipStartDate: "0001-01-01",
                  shipEndDate: "0001-01-01",
                  arrivalStartDate: "0001-01-01",
                  arrivalEndDate: "0001-01-01",
                  overrideInd: true,
                  notPreSaved: false,
                },
                allowanceStartDate: "2025-05-07",
                allowanceEndDate: "2025-05-13",
                performStartDate: "2025-05-07",
                performEndDate: "2025-05-13",
                orderStartDate: "2025-05-07",
                orderEndDate: "2025-05-13",
                shipStartDate: "2025-05-07",
                shipEndDate: "2025-05-13",
                arrivalStartDate: "2025-05-07",
                arrivalEndDate: "2025-05-13",
                vehicleId: "66f1eded1d792055ff84c77c",
                createInd: "TC",
                locationId: "639033d538196056762e6e28",
                locationName: "27 - Seattle",
                distCenter: "DDSE",
                locationTypeCd: "D",
                allowanceBillingInfo: {
                  vendorIds: [
                    {
                      vendorNbr: "006446",
                      vendorSubAccount: "013",
                      costArea: "1",
                      fullVendorNbr: "006446-013-1",
                    },
                  ],
                  absMerchVendor: "006446-013",
                  absVendorName: "KEHE DISTRIBUTORS {JBG WHLSALE}",
                  absVendorPaymentType: "I",
                  acPayableVendorNbr: "",
                  acReceivableVendorNbr: "184870",
                  billingName: "1031 MARKETING {ALLOWANCE ONLY}",
                  vendorBillingList: [],
                  vendorItemCount: 8,
                  vendorItemCountsSet: [
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "006446",
                        vendorSubAccount: "013",
                        costArea: "1",
                        vendorRank: "B.DSD",
                        fullVendorNbr: "006446-013-1",
                        valid: true,
                      },
                      itemIdSet: [
                        "2020113",
                        "2020201",
                        "2020143",
                        "2020197",
                        "2020393",
                        "2021581",
                        "2020118",
                        "2021027",
                      ],
                      vendorDsdWhseItemCount: 8,
                    },
                  ],
                  source: "SIMS_VENDOR",
                  matched: "SIMS_ITEM_VENDOR",
                  billingControls: [
                    {
                      controlName: "UPP-22",
                      controlLevel: "Vendor Outlet",
                      merchVendor: "006446",
                      merchVendorNm: "KEHE DISTRIBUTORS",
                      partnerVendors: [],
                      legacyFinVendNm: "1031 MARKETING {ALLOWANCE ONLY}",
                      legacyFinVendNbr: "184870",
                      location: {
                        locationType: "Distribution Center",
                        divisionIds: ["05"],
                        stores: [],
                        rogs: [],
                        sectionIds: [],
                        distributionCenterIds: ["DDSE"],
                      },
                      effectiveStartDate: "2025-02-16",
                      createUser: "VBAND07",
                      createTs: 1739940340608,
                      lastUpdatedUser: "VBAND07",
                      lastUpdatedTs: 1739940340608,
                      partnerId: "10014568",
                      partnerNm: "1846Testing",
                      partnerCtrlType: "",
                      merchVendOutlet: ["001", "013"],
                      billingType: "I",
                      inclusion: "Out",
                      effectiveEndDate: "9999-12-31",
                      billingControlId: "8f3d932e-8f07-4b86-9b7d-be3035b0a5ea",
                      versionId: "1",
                    },
                  ],
                  orgAbsVendorPaymentType: "I",
                  orgAcPayableVendorNbr: "110056",
                  orgAcReceivableVendorNbr: "      ",
                },
                allowanceBillingInfos: [
                  {
                    vendorIds: [
                      {
                        vendorNbr: "006446",
                        vendorSubAccount: "013",
                        costArea: "1",
                        fullVendorNbr: "006446-013-1",
                      },
                    ],
                    absMerchVendor: "006446-013",
                    absVendorName: "KEHE DISTRIBUTORS {JBG WHLSALE}",
                    absVendorPaymentType: "I",
                    acPayableVendorNbr: "",
                    acReceivableVendorNbr: "184870",
                    billingName: "1031 MARKETING {ALLOWANCE ONLY}",
                    vendorBillingList: [],
                    vendorItemCount: 8,
                    vendorItemCountsSet: [
                      {
                        vendorDsdWhseId: {
                          vendorNbr: "006446",
                          vendorSubAccount: "013",
                          costArea: "1",
                          vendorRank: "B.DSD",
                          fullVendorNbr: "006446-013-1",
                          valid: true,
                        },
                        itemIdSet: [
                          "2020113",
                          "2020201",
                          "2020143",
                          "2020197",
                          "2020393",
                          "2021581",
                          "2020118",
                          "2021027",
                        ],
                        vendorDsdWhseItemCount: 8,
                      },
                    ],
                    source: "SIMS_VENDOR",
                    billingControls: [
                      {
                        controlName: "UPP-22",
                        controlLevel: "Vendor Outlet",
                        merchVendor: "006446",
                        merchVendorNm: "KEHE DISTRIBUTORS",
                        partnerVendors: [],
                        legacyFinVendNm: "1031 MARKETING {ALLOWANCE ONLY}",
                        legacyFinVendNbr: "184870",
                        location: {
                          locationType: "Distribution Center",
                          divisionIds: ["05"],
                          stores: [],
                          rogs: [],
                          sectionIds: [],
                          distributionCenterIds: ["DDSE"],
                        },
                        effectiveStartDate: "2025-02-16",
                        createUser: "VBAND07",
                        createTs: 1739940340608,
                        lastUpdatedUser: "VBAND07",
                        lastUpdatedTs: 1739940340608,
                        partnerId: "10014568",
                        partnerNm: "1846Testing",
                        partnerCtrlType: "",
                        merchVendOutlet: ["001", "013"],
                        billingType: "I",
                        inclusion: "Out",
                        effectiveEndDate: "9999-12-31",
                        billingControlId:
                          "8f3d932e-8f07-4b86-9b7d-be3035b0a5ea",
                        versionId: "1",
                      },
                    ],
                    orgAbsVendorPaymentType: "I",
                    orgAcPayableVendorNbr: "110056",
                    orgAcReceivableVendorNbr: "      ",
                  },
                  {
                    vendorIds: [
                      {
                        vendorNbr: "006446",
                        vendorSubAccount: "001",
                        costArea: "4",
                        fullVendorNbr: "006446-001-4",
                      },
                      {
                        vendorNbr: "006446",
                        vendorSubAccount: "001",
                        costArea: "2",
                        fullVendorNbr: "006446-001-2",
                      },
                      {
                        vendorNbr: "006446",
                        vendorSubAccount: "001",
                        costArea: "1",
                        fullVendorNbr: "006446-001-1",
                      },
                    ],
                    absMerchVendor: "006446-001",
                    absVendorName: "KEHE DISTRIBUTORS",
                    absVendorPaymentType: "I",
                    acPayableVendorNbr: "",
                    acReceivableVendorNbr: "184870",
                    billingName: "1031 MARKETING {ALLOWANCE ONLY}",
                    billingContactName: "<EMAIL>     ",
                    billingContactEmail: "<EMAIL>",
                    vendorBillingList: [
                      {
                        billingContactName: "<EMAIL>     ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "<EMAIL>        ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "BRANDON SWEET            ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "<EMAIL>        ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "<EMAIL>     ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "<EMAIL>        ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "BRANDON SWEET            ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "<EMAIL>        ",
                        billingContactEmail: "<EMAIL>",
                      },
                    ],
                    vendorItemCount: 7,
                    vendorItemCountsSet: [
                      {
                        vendorDsdWhseId: {
                          vendorNbr: "006446",
                          vendorSubAccount: "001",
                          costArea: "4",
                          vendorRank: "B.DSD",
                          fullVendorNbr: "006446-001-4",
                          valid: true,
                        },
                        itemIdSet: [
                          "2020113",
                          "2020143",
                          "2020197",
                          "2020393",
                          "2020118",
                          "2010289",
                          "2021027",
                        ],
                        vendorDsdWhseItemCount: 7,
                      },
                      {
                        vendorDsdWhseId: {
                          vendorNbr: "006446",
                          vendorSubAccount: "001",
                          costArea: "2",
                          vendorRank: "B.DSD",
                          fullVendorNbr: "006446-001-2",
                          valid: true,
                        },
                        itemIdSet: [
                          "2020113",
                          "2020143",
                          "2020197",
                          "2020393",
                          "2020118",
                          "2010289",
                          "2021027",
                        ],
                        vendorDsdWhseItemCount: 7,
                      },
                      {
                        vendorDsdWhseId: {
                          vendorNbr: "006446",
                          vendorSubAccount: "001",
                          costArea: "1",
                          vendorRank: "B.DSD",
                          fullVendorNbr: "006446-001-1",
                          valid: true,
                        },
                        itemIdSet: [
                          "2020113",
                          "2020143",
                          "2020197",
                          "2020393",
                          "2020118",
                          "2010289",
                          "2021027",
                        ],
                        vendorDsdWhseItemCount: 7,
                      },
                    ],
                    source: "SIMS_VENDOR",
                    billingControls: [
                      {
                        controlName: "UPP-22",
                        controlLevel: "Vendor Outlet",
                        merchVendor: "006446",
                        merchVendorNm: "KEHE DISTRIBUTORS",
                        partnerVendors: [],
                        legacyFinVendNm: "1031 MARKETING {ALLOWANCE ONLY}",
                        legacyFinVendNbr: "184870",
                        location: {
                          locationType: "Distribution Center",
                          divisionIds: ["05"],
                          stores: [],
                          rogs: [],
                          sectionIds: [],
                          distributionCenterIds: ["DDSE"],
                        },
                        effectiveStartDate: "2025-02-16",
                        createUser: "VBAND07",
                        createTs: 1739940340608,
                        lastUpdatedUser: "VBAND07",
                        lastUpdatedTs: 1739940340608,
                        partnerId: "10014568",
                        partnerNm: "1846Testing",
                        partnerCtrlType: "",
                        merchVendOutlet: ["001", "013"],
                        billingType: "I",
                        inclusion: "Out",
                        effectiveEndDate: "9999-12-31",
                        billingControlId:
                          "8f3d932e-8f07-4b86-9b7d-be3035b0a5ea",
                        versionId: "1",
                      },
                    ],
                    orgAbsVendorPaymentType: "D",
                    orgAcPayableVendorNbr: "110056",
                    orgAcReceivableVendorNbr: "082099",
                  },
                  {
                    vendorIds: [
                      {
                        vendorNbr: "000895",
                        vendorSubAccount: "028",
                        costArea: "2",
                        fullVendorNbr: "000895-028-2",
                      },
                      {
                        vendorNbr: "000895",
                        vendorSubAccount: "028",
                        costArea: "1",
                        fullVendorNbr: "000895-028-1",
                      },
                    ],
                    absMerchVendor: "000895-028",
                    absVendorName: "MONDELEZ GLOBAL LLC",
                    absVendorPaymentType: "D",
                    acPayableVendorNbr: "045375",
                    acReceivableVendorNbr: "158846",
                    billingContactName: "<EMAIL>       ",
                    billingContactEmail: "<EMAIL>",
                    vendorBillingList: [
                      {
                        billingContactName: "<EMAIL>       ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "<EMAIL>       ",
                        billingContactEmail: "<EMAIL>",
                      },
                    ],
                    vendorItemCount: 2,
                    vendorItemCountsSet: [
                      {
                        vendorDsdWhseId: {
                          vendorNbr: "000895",
                          vendorSubAccount: "028",
                          costArea: "1",
                          vendorRank: "B.DSD",
                          fullVendorNbr: "000895-028-1",
                          valid: true,
                        },
                        itemIdSet: ["2020338", "2020337"],
                        vendorDsdWhseItemCount: 2,
                      },
                      {
                        vendorDsdWhseId: {
                          vendorNbr: "000895",
                          vendorSubAccount: "028",
                          costArea: "2",
                          vendorRank: "B.DSD",
                          fullVendorNbr: "000895-028-2",
                          valid: true,
                        },
                        itemIdSet: ["2020338", "2020337"],
                        vendorDsdWhseItemCount: 2,
                      },
                    ],
                    source: "SIMS_VENDOR",
                    orgAbsVendorPaymentType: "D",
                    orgAcPayableVendorNbr: "045375",
                    orgAcReceivableVendorNbr: "158846",
                  },
                ],
                allowanceDateOffsets: {
                  allowanceTypes: ["SCAN", "HEADER_FLAT", "ITEM_FLAT"],
                  startDateOffset: 0,
                  endDateOffset: 0,
                  defaultOrderLeadTimeDays: 0,
                  defaultShipTransitDays: 0,
                  resolvedLeadTimeDays: 0,
                  resolvedShipTransitDays: 0,
                },
                leadDistributorInfos: [],
                createAllowInd: true,
                allowanceItems: [
                  {
                    itemId: "2010289",
                    itemDescription: "ANNIES BUNNY GRAHAMS CHOC CHIP          ",
                    primaryUpc: "001356200018",
                    consumerUpc: "001356200018",
                    caseUpc: "0000000000000",
                    itemUpcs: ["0000000000000", "001356200018"],
                    consumerUpcs: [
                      {
                        upc: "001356200018",
                        rog: "SSEA",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356200018",
                        rog: "SSPK",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356200018",
                        rog: "SACG",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                    ],
                    effectiveStartDate: "2024-08-29",
                    effectiveEndDate: "9999-01-01",
                    allowanceType: "SCAN",
                    packWhse: 12,
                    ringType: 0,
                    size: "7.5 OZ",
                    vendorPackConversionFactor: 1,
                    unitNetCosts: {
                      netCostType: "UNIT",
                      cost: 3.77,
                      costAllow: 3.1784,
                      initialAllowAmt: 0,
                      newCostAllow: 3.1784,
                    },
                    masterCaseNetCosts: {
                      netCostType: "MASTER_CASE",
                      cost: 45.24,
                      costAllow: 38.14,
                      initialAllowAmt: 0,
                      newCostAllow: 38.14,
                    },
                    shipCaseNetCosts: {
                      netCostType: "SHIP_CASE",
                      cost: 45.24,
                      costAllow: 38.14,
                      initialAllowAmt: 0,
                      newCostAllow: 38.14,
                    },
                    allowanceAmount: 0,
                    allowUomType: "EA",
                    allowUomTypes: ["EA"],
                    overlaps: {
                      offerAllowAmounts: [
                        {
                          offer: 7021834,
                          rog: "SACG",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSEA",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSPK",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021364,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021829,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7020147,
                          rog: "SACG",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSEA",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSPK",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                      ],
                      unitizedOverlaps: {
                        netCostType: "UNIT",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 0.0833,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.075,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 0.3333,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 0.1,
                          },
                        ],
                        scanAllow: 0.1,
                        caseAllow: 0.0833,
                        shipAllow: 0.4083,
                        allowSum: 0.5916,
                      },
                      shipCaseOverlaps: {
                        netCostType: "SHIP_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 7.1,
                      },
                      masterCaseOverlaps: {
                        netCostType: "MASTER_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 7.1,
                      },
                    },
                    modCommand: "NONE",
                  },
                  {
                    itemId: "2020113",
                    itemDescription: "ANNIES BUNNY GRAHAMS HONEY              ",
                    primaryUpc: "001356200015",
                    consumerUpc: "001356200015",
                    caseUpc: "0001356200015",
                    itemUpcs: ["0001356200015", "001356200015"],
                    consumerUpcs: [
                      {
                        upc: "001356200015",
                        rog: "SACG",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356200015",
                        rog: "SSEA",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356200015",
                        rog: "SSPK",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                    ],
                    effectiveStartDate: "2024-08-29",
                    effectiveEndDate: "9999-01-01",
                    allowanceType: "SCAN",
                    packWhse: 12,
                    ringType: 0,
                    size: "7.5 OZ",
                    vendorPackConversionFactor: 1,
                    unitNetCosts: {
                      netCostType: "UNIT",
                      cost: 3.79,
                      costAllow: 3.0317,
                      initialAllowAmt: 0,
                      newCostAllow: 3.0317,
                    },
                    masterCaseNetCosts: {
                      netCostType: "MASTER_CASE",
                      cost: 45.48,
                      costAllow: 36.38,
                      initialAllowAmt: 0,
                      newCostAllow: 36.38,
                    },
                    shipCaseNetCosts: {
                      netCostType: "SHIP_CASE",
                      cost: 45.48,
                      costAllow: 36.38,
                      initialAllowAmt: 0,
                      newCostAllow: 36.38,
                    },
                    allowanceAmount: 0,
                    allowUomType: "EA",
                    allowUomTypes: ["EA"],
                    overlaps: {
                      offerAllowAmounts: [
                        {
                          offer: 7021834,
                          rog: "SACG",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSEA",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSPK",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021364,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021829,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 6,
                        },
                        {
                          offer: 7021829,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 6,
                        },
                        {
                          offer: 7021829,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 6,
                        },
                        {
                          offer: 7020147,
                          rog: "SACG",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSEA",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSPK",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                      ],
                      unitizedOverlaps: {
                        netCostType: "UNIT",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 0.0833,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.075,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 6,
                            convertedAmount: 0.5,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 0.1,
                          },
                        ],
                        scanAllow: 0.1,
                        caseAllow: 0.0833,
                        shipAllow: 0.575,
                        allowSum: 0.7583,
                      },
                      shipCaseOverlaps: {
                        netCostType: "SHIP_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 6,
                            convertedAmount: 6,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 6.9,
                        allowSum: 9.1,
                      },
                      masterCaseOverlaps: {
                        netCostType: "MASTER_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 6,
                            convertedAmount: 6,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 6.9,
                        allowSum: 9.1,
                      },
                    },
                    modCommand: "NONE",
                  },
                  {
                    itemId: "2020118",
                    itemDescription: "ANNIES CHEDDAR BUNNIES ORIG             ",
                    primaryUpc: "001356230215",
                    consumerUpc: "001356230215",
                    caseUpc: "0001356230215",
                    itemUpcs: ["0001356230215", "001356230215"],
                    consumerUpcs: [
                      {
                        upc: "001356230215",
                        rog: "SACG",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356230215",
                        rog: "SSEA",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356230215",
                        rog: "SSPK",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                    ],
                    effectiveStartDate: "2024-08-29",
                    effectiveEndDate: "9999-01-01",
                    allowanceType: "SCAN",
                    packWhse: 12,
                    ringType: 0,
                    size: "7.5 OZ",
                    vendorPackConversionFactor: 1,
                    unitNetCosts: {
                      netCostType: "UNIT",
                      cost: 3.77,
                      costAllow: 3.1784,
                      initialAllowAmt: 0,
                      newCostAllow: 3.1784,
                    },
                    masterCaseNetCosts: {
                      netCostType: "MASTER_CASE",
                      cost: 45.24,
                      costAllow: 38.14,
                      initialAllowAmt: 0,
                      newCostAllow: 38.14,
                    },
                    shipCaseNetCosts: {
                      netCostType: "SHIP_CASE",
                      cost: 45.24,
                      costAllow: 38.14,
                      initialAllowAmt: 0,
                      newCostAllow: 38.14,
                    },
                    allowanceAmount: 0,
                    allowUomType: "EA",
                    allowUomTypes: ["EA"],
                    overlaps: {
                      offerAllowAmounts: [
                        {
                          offer: 7021834,
                          rog: "SACG",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSEA",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSPK",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021364,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021829,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7020147,
                          rog: "SACG",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSEA",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSPK",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                      ],
                      unitizedOverlaps: {
                        netCostType: "UNIT",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 0.0833,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.075,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 0.3333,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 0.1,
                          },
                        ],
                        scanAllow: 0.1,
                        caseAllow: 0.0833,
                        shipAllow: 0.4083,
                        allowSum: 0.5916,
                      },
                      shipCaseOverlaps: {
                        netCostType: "SHIP_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 7.1,
                      },
                      masterCaseOverlaps: {
                        netCostType: "MASTER_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 7.1,
                      },
                    },
                    modCommand: "NONE",
                  },
                  {
                    itemId: "2020201",
                    itemDescription: "ANNIES BIRTHDAY CAKE BUNNY GRAHAMS      ",
                    primaryUpc: "001356210923",
                    consumerUpc: "001356210923",
                    caseUpc: "0001356210923",
                    itemUpcs: ["0001356210923", "001356210923"],
                    consumerUpcs: [
                      {
                        upc: "001356210923",
                        rog: "SACG",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356210923",
                        rog: "SSEA",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                    ],
                    effectiveStartDate: "2024-08-29",
                    effectiveEndDate: "9999-01-01",
                    allowanceType: "SCAN",
                    packWhse: 12,
                    ringType: 0,
                    size: "7.5 OZ",
                    vendorPackConversionFactor: 1,
                    unitNetCosts: {
                      netCostType: "UNIT",
                      cost: 3.33,
                      costAllow: 2.7384,
                      initialAllowAmt: 0,
                      newCostAllow: 2.7384,
                    },
                    masterCaseNetCosts: {
                      netCostType: "MASTER_CASE",
                      cost: 39.96,
                      costAllow: 32.86,
                      initialAllowAmt: 0,
                      newCostAllow: 32.86,
                    },
                    shipCaseNetCosts: {
                      netCostType: "SHIP_CASE",
                      cost: 39.96,
                      costAllow: 32.86,
                      initialAllowAmt: 0,
                      newCostAllow: 32.86,
                    },
                    allowanceAmount: 0,
                    allowUomType: "EA",
                    allowUomTypes: ["EA"],
                    overlaps: {
                      offerAllowAmounts: [
                        {
                          offer: 7021834,
                          rog: "SACG",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSEA",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSPK",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021364,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021829,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7020147,
                          rog: "SACG",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSEA",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSPK",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                      ],
                      unitizedOverlaps: {
                        netCostType: "UNIT",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 0.0833,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.075,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 0.3333,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 0.1,
                          },
                        ],
                        scanAllow: 0.1,
                        caseAllow: 0.0833,
                        shipAllow: 0.4083,
                        allowSum: 0.5916,
                      },
                      shipCaseOverlaps: {
                        netCostType: "SHIP_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 7.1,
                      },
                      masterCaseOverlaps: {
                        netCostType: "MASTER_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 7.1,
                      },
                    },
                    modCommand: "NONE",
                  },
                  {
                    itemId: "2020338",
                    itemDescription: "NBC EASY CHEESE AMERICAN                ",
                    primaryUpc: "004400004552",
                    consumerUpc: "004400004552",
                    caseUpc: "0000000000000",
                    itemUpcs: ["0000000000000", "004400004552"],
                    consumerUpcs: [
                      {
                        upc: "004400004552",
                        rog: "SSPK",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "8 OZ   ",
                      },
                      {
                        upc: "004400004552",
                        rog: "SACG",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "8 OZ   ",
                      },
                      {
                        upc: "004400004552",
                        rog: "SSEA",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "8 OZ   ",
                      },
                    ],
                    effectiveStartDate: "2024-11-14",
                    effectiveEndDate: "9999-01-01",
                    allowanceType: "SCAN",
                    packWhse: 12,
                    ringType: 0,
                    size: "8.0 OZ",
                    vendorPackConversionFactor: 1,
                    unitNetCosts: {
                      netCostType: "UNIT",
                      cost: 4.49,
                      costAllow: 3.9984,
                      initialAllowAmt: 0,
                      newCostAllow: 3.9984,
                    },
                    masterCaseNetCosts: {
                      netCostType: "MASTER_CASE",
                      cost: 53.88,
                      costAllow: 47.98,
                      initialAllowAmt: 0,
                      newCostAllow: 47.98,
                    },
                    shipCaseNetCosts: {
                      netCostType: "SHIP_CASE",
                      cost: 53.88,
                      costAllow: 47.98,
                      initialAllowAmt: 0,
                      newCostAllow: 47.98,
                    },
                    allowanceAmount: 0,
                    allowUomType: "EA",
                    allowUomTypes: ["EA"],
                    overlaps: {
                      offerAllowAmounts: [
                        {
                          offer: 7021834,
                          rog: "SACG",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSEA",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSPK",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021364,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021829,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 4,
                        },
                      ],
                      unitizedOverlaps: {
                        netCostType: "UNIT",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 0.0833,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.075,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 0.3333,
                          },
                        ],
                        scanAllow: 0,
                        caseAllow: 0.0833,
                        shipAllow: 0.4083,
                        allowSum: 0.4916,
                      },
                      shipCaseOverlaps: {
                        netCostType: "SHIP_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                        ],
                        scanAllow: 0,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 5.9,
                      },
                      masterCaseOverlaps: {
                        netCostType: "MASTER_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                        ],
                        scanAllow: 0,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 5.9,
                      },
                    },
                    modCommand: "NONE",
                  },
                  {
                    itemId: "2020393",
                    itemDescription: "ANNIES CRACKER HOMEGROWN CHEDDAR SQUARES",
                    primaryUpc: "001356200053",
                    consumerUpc: "001356200053",
                    caseUpc: "0000000000000",
                    itemUpcs: ["0000000000000", "001356200053"],
                    consumerUpcs: [
                      {
                        upc: "001356200053",
                        rog: "SACG",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356200053",
                        rog: "SSPK",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356200053",
                        rog: "SSEA",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                    ],
                    effectiveStartDate: "2024-08-29",
                    effectiveEndDate: "9999-01-01",
                    allowanceType: "SCAN",
                    packWhse: 12,
                    ringType: 0,
                    size: "7.5 OZ",
                    vendorPackConversionFactor: 1,
                    unitNetCosts: {
                      netCostType: "UNIT",
                      cost: 3.77,
                      costAllow: 3.1784,
                      initialAllowAmt: 0,
                      newCostAllow: 3.1784,
                    },
                    masterCaseNetCosts: {
                      netCostType: "MASTER_CASE",
                      cost: 45.24,
                      costAllow: 38.14,
                      initialAllowAmt: 0,
                      newCostAllow: 38.14,
                    },
                    shipCaseNetCosts: {
                      netCostType: "SHIP_CASE",
                      cost: 45.24,
                      costAllow: 38.14,
                      initialAllowAmt: 0,
                      newCostAllow: 38.14,
                    },
                    allowanceAmount: 0,
                    allowUomType: "EA",
                    allowUomTypes: ["EA"],
                    overlaps: {
                      offerAllowAmounts: [
                        {
                          offer: 7021834,
                          rog: "SACG",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSEA",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSPK",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021364,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021829,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7020147,
                          rog: "SACG",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSEA",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSPK",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                      ],
                      unitizedOverlaps: {
                        netCostType: "UNIT",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 0.0833,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.075,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 0.3333,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 0.1,
                          },
                        ],
                        scanAllow: 0.1,
                        caseAllow: 0.0833,
                        shipAllow: 0.4083,
                        allowSum: 0.5916,
                      },
                      shipCaseOverlaps: {
                        netCostType: "SHIP_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 7.1,
                      },
                      masterCaseOverlaps: {
                        netCostType: "MASTER_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 7.1,
                      },
                    },
                    modCommand: "NONE",
                  },
                  {
                    itemId: "2021027",
                    itemDescription: "ANNIES CRACKERS CHEDDAR BUNNIES WHITE   ",
                    primaryUpc: "001356230228",
                    consumerUpc: "001356230228",
                    caseUpc: "0001356230228",
                    itemUpcs: ["0001356230228", "001356230228"],
                    consumerUpcs: [
                      {
                        upc: "001356230228",
                        rog: "SSEA",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356230228",
                        rog: "SACG",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356230228",
                        rog: "SSPK",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                    ],
                    effectiveStartDate: "2024-08-29",
                    effectiveEndDate: "9999-01-01",
                    allowanceType: "SCAN",
                    packWhse: 12,
                    ringType: 0,
                    size: "7.5 OZ",
                    vendorPackConversionFactor: 1,
                    unitNetCosts: {
                      netCostType: "UNIT",
                      cost: 3.78,
                      costAllow: 3.1884,
                      initialAllowAmt: 0,
                      newCostAllow: 3.1884,
                    },
                    masterCaseNetCosts: {
                      netCostType: "MASTER_CASE",
                      cost: 45.36,
                      costAllow: 38.26,
                      initialAllowAmt: 0,
                      newCostAllow: 38.26,
                    },
                    shipCaseNetCosts: {
                      netCostType: "SHIP_CASE",
                      cost: 45.36,
                      costAllow: 38.26,
                      initialAllowAmt: 0,
                      newCostAllow: 38.26,
                    },
                    allowanceAmount: 0,
                    allowUomType: "EA",
                    allowUomTypes: ["EA"],
                    overlaps: {
                      offerAllowAmounts: [
                        {
                          offer: 7021834,
                          rog: "SACG",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSEA",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSPK",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021364,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021829,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7020147,
                          rog: "SACG",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSEA",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSPK",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                      ],
                      unitizedOverlaps: {
                        netCostType: "UNIT",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 0.0833,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.075,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 0.3333,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 0.1,
                          },
                        ],
                        scanAllow: 0.1,
                        caseAllow: 0.0833,
                        shipAllow: 0.4083,
                        allowSum: 0.5916,
                      },
                      shipCaseOverlaps: {
                        netCostType: "SHIP_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 7.1,
                      },
                      masterCaseOverlaps: {
                        netCostType: "MASTER_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 7.1,
                      },
                    },
                    modCommand: "NONE",
                  },
                  {
                    itemId: "2021581",
                    itemDescription: "ANNIES HOMEGROWN SNACK MIX CHEDDAR ORG  ",
                    primaryUpc: "001356230057",
                    consumerUpc: "001356230057",
                    caseUpc: "0001356230057",
                    itemUpcs: ["0001356230057", "001356230057"],
                    consumerUpcs: [
                      {
                        upc: "001356230057",
                        rog: "SACG",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "9 OZ   ",
                      },
                      {
                        upc: "001356230057",
                        rog: "SSEA",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "9 OZ   ",
                      },
                    ],
                    effectiveStartDate: "2024-09-11",
                    effectiveEndDate: "9999-01-01",
                    allowanceType: "SCAN",
                    packWhse: 12,
                    ringType: 0,
                    size: "9.0 OZ",
                    vendorPackConversionFactor: 1,
                    unitNetCosts: {
                      netCostType: "UNIT",
                      cost: 5.01,
                      costAllow: 4.5184,
                      initialAllowAmt: 0,
                      newCostAllow: 4.5184,
                    },
                    masterCaseNetCosts: {
                      netCostType: "MASTER_CASE",
                      cost: 60.12,
                      costAllow: 54.22,
                      initialAllowAmt: 0,
                      newCostAllow: 54.22,
                    },
                    shipCaseNetCosts: {
                      netCostType: "SHIP_CASE",
                      cost: 60.12,
                      costAllow: 54.22,
                      initialAllowAmt: 0,
                      newCostAllow: 54.22,
                    },
                    allowanceAmount: 0,
                    allowUomType: "EA",
                    allowUomTypes: ["EA"],
                    overlaps: {
                      offerAllowAmounts: [
                        {
                          offer: 7021834,
                          rog: "SACG",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSEA",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSPK",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021364,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021829,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 4,
                        },
                      ],
                      unitizedOverlaps: {
                        netCostType: "UNIT",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 0.0833,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.075,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 0.3333,
                          },
                        ],
                        scanAllow: 0,
                        caseAllow: 0.0833,
                        shipAllow: 0.4083,
                        allowSum: 0.4916,
                      },
                      shipCaseOverlaps: {
                        netCostType: "SHIP_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                        ],
                        scanAllow: 0,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 5.9,
                      },
                      masterCaseOverlaps: {
                        netCostType: "MASTER_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                        ],
                        scanAllow: 0,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 5.9,
                      },
                    },
                    modCommand: "NONE",
                  },
                ],
                headerFlatAmt: 0,
                allowanceStatus: "Draft",
                storeGroups: [],
                leadDistributorInd: false,
                includeInd: true,
                finalizedAmountsInd: false,
                excludedItems: [
                  {
                    itemId: "2020143",
                    excludeInd: "DEACTIVATED_ITEM",
                  },
                  {
                    itemId: "2020197",
                    excludeInd: "DEACTIVATED_ITEM",
                  },
                  {
                    itemId: "2020337",
                    excludeInd: "DEACTIVATED_ITEM",
                  },
                ],
                allowanceProcessStatus: "",
                allowDownstreamStatus: {},
              },
            ],
          },
          productSources: ["DSD"],
          allowancePerformances: {
            id: "63a3a12743a6cee87995b834",
            performance: "4U Event (52)",
            allowanceType: "Scan",
            allowanceCd: "T",
            perfCode1: "20",
            perfCode2: "52",
            performanceConfig: {
              defaultCreateInd: "TC",
              allowOnlyOverrideStoreGroupsInd: false,
              changeableOnEditInd: true,
            },
          },
          offerAllowancesGroupInfoMap: {},
        },
      },
      divisionId: "27",
      planEventType: "DP",
      offerPersistMode: "CREATE",
    },
    allowanceForm: {
      allowanceFormData: {
        "offerAllowances[0].allowances[0]": {
          allowancePrimeData: {
            allowanceType: "Scan",
            createInd: "TC",
            performance: "4U Event (52)",
            perfConfig: {
              allowanceType: "Scan",
              allowanceCd: "T",
              perfCode1: "20",
              perfCode2: "52",
              id: "63a3a12743a6cee87995b834",
              performance: "4U Event (52)",
              performanceConfig: {
                __typename: "PerformanceConfig",
                defaultCreateInd: "TC",
                allowOnlyOverrideStoreGroupsInd: false,
                allowancePerformanceChildId: null,
              },
            },
            allowanceToBeCreated: "DSD_WHSE_RETAIL_DIVISION",
            allowanceToBeCreatedOption: {
              name: "One Allowance: Warehouse, DSD, or Combined",
              key: "DSD_WHSE_RETAIL_DIVISION",
              routingKey: "DSD_WHSE_RETAIL_DIVISION",
              allowanceMap: {
                DSD_WHSE_RETAIL_DIVISION: [],
              },
              default: false,
              createIndex: "TC",
            },
            isAdditionalDatesChanged: false,
          },
          allowanceCreationVehicle: {
            DSD_WHSE_RETAIL_DIVISION: {
              vehicleId: "66f1eded1d792055ff84c77c",
              vehicleRef: {
                vehicleTypeId: "66a3f061900b0b47a182376f",
                customStartDate: "2025-05-07",
                customEndDate: "2025-05-13",
                vehicleType: {
                  vehicleTypeId: "66a3f061900b0b47a182376f",
                  sourceVehicleTypeSk: 198,
                  vehicleTypNm: "insrt",
                  vehicleTypDesc: "Weekly Insert",
                },
                vehicleTabType: "Weekly Insert",
                vehicleId: "66f1eded1d792055ff84c77c",
                vehicleNm: "27 Week 19 Insert 2025",
                vehicleTypeDesc: "Weekly Insert",
                year: 2025,
              },
              dateRange: {
                startDate: "2025-05-07",
                endDate: "2025-05-13",
              },
            },
          },
          additionalDates: {
            DSD_WHSE_RETAIL_DIVISION: [
              {
                allowanceIdNbr: 1,
                vendorNbr: "",
                costAreaDesc: "",
                defaultAllowanceDates: {
                  allowanceStartDate: "2025-05-07",
                  allowanceEndDate: "2025-05-13",
                  performStartDate: "2025-05-07",
                  performEndDate: "2025-05-13",
                  orderStartDate: "0001-01-01",
                  orderEndDate: "0001-01-01",
                  shipStartDate: "0001-01-01",
                  shipEndDate: "0001-01-01",
                  arrivalStartDate: "0001-01-01",
                  arrivalEndDate: "0001-01-01",
                  overrideInd: true,
                  notPreSaved: false,
                },
                allowanceStartDate: "2025-05-07",
                allowanceEndDate: "2025-05-13",
                performStartDate: "2025-05-07",
                performEndDate: "2025-05-13",
                orderStartDate: "2025-05-07",
                orderEndDate: "2025-05-13",
                shipStartDate: "2025-05-07",
                shipEndDate: "2025-05-13",
                arrivalStartDate: "2025-05-07",
                arrivalEndDate: "2025-05-13",
                vehicleId: "66f1eded1d792055ff84c77c",
                createInd: "TC",
                locationId: "639033d538196056762e6e28",
                locationName: "27 - Seattle",
                distCenter: "DDSE",
                locationTypeCd: "D",
                allowanceBillingInfo: {
                  vendorIds: [
                    {
                      vendorNbr: "006446",
                      vendorSubAccount: "013",
                      costArea: "1",
                      fullVendorNbr: "006446-013-1",
                    },
                  ],
                  absMerchVendor: "006446-013",
                  absVendorName: "KEHE DISTRIBUTORS {JBG WHLSALE}",
                  absVendorPaymentType: "I",
                  acPayableVendorNbr: "",
                  acReceivableVendorNbr: "184870",
                  billingName: "1031 MARKETING {ALLOWANCE ONLY}",
                  vendorBillingList: [],
                  vendorItemCount: 8,
                  vendorItemCountsSet: [
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "006446",
                        vendorSubAccount: "013",
                        costArea: "1",
                        vendorRank: "B.DSD",
                        fullVendorNbr: "006446-013-1",
                        valid: true,
                      },
                      itemIdSet: [
                        "2020113",
                        "2020118",
                        "2020143",
                        "2020197",
                        "2020201",
                        "2020393",
                        "2021027",
                        "2021581",
                      ],
                      vendorDsdWhseItemCount: 8,
                    },
                  ],
                  source: "SIMS_VENDOR",
                  matched: "SIMS_ITEM_VENDOR",
                  billingControls: [
                    {
                      controlName: "UPP-22",
                      controlLevel: "Vendor Outlet",
                      merchVendor: "006446",
                      merchVendorNm: "KEHE DISTRIBUTORS",
                      partnerVendors: [],
                      legacyFinVendNm: "1031 MARKETING {ALLOWANCE ONLY}",
                      legacyFinVendNbr: "184870",
                      location: {
                        locationType: "Distribution Center",
                        divisionIds: ["05"],
                        stores: [],
                        rogs: [],
                        sectionIds: [],
                        distributionCenterIds: ["DDSE"],
                      },
                      effectiveStartDate: "2025-02-16",
                      createUser: "VBAND07",
                      createTs: 1739940340608,
                      lastUpdatedUser: "VBAND07",
                      lastUpdatedTs: 1739940340608,
                      partnerId: "10014568",
                      partnerNm: "1846Testing",
                      partnerCtrlType: "",
                      merchVendOutlet: ["001", "013"],
                      billingType: "I",
                      inclusion: "Out",
                      effectiveEndDate: "9999-12-31",
                      billingControlId: "8f3d932e-8f07-4b86-9b7d-be3035b0a5ea",
                      versionId: "1",
                    },
                  ],
                  orgAbsVendorPaymentType: "I",
                  orgAcPayableVendorNbr: "110056",
                  orgAcReceivableVendorNbr: "      ",
                },
                allowanceBillingInfos: [
                  {
                    vendorIds: [
                      {
                        vendorNbr: "006446",
                        vendorSubAccount: "013",
                        costArea: "1",
                        fullVendorNbr: "006446-013-1",
                      },
                    ],
                    absMerchVendor: "006446-013",
                    absVendorName: "KEHE DISTRIBUTORS {JBG WHLSALE}",
                    absVendorPaymentType: "I",
                    acPayableVendorNbr: "",
                    acReceivableVendorNbr: "184870",
                    billingName: "1031 MARKETING {ALLOWANCE ONLY}",
                    vendorBillingList: [],
                    vendorItemCount: 8,
                    vendorItemCountsSet: [
                      {
                        vendorDsdWhseId: {
                          vendorNbr: "006446",
                          vendorSubAccount: "013",
                          costArea: "1",
                          vendorRank: "B.DSD",
                          fullVendorNbr: "006446-013-1",
                          valid: true,
                        },
                        itemIdSet: [
                          "2020113",
                          "2020118",
                          "2020143",
                          "2020197",
                          "2020201",
                          "2020393",
                          "2021027",
                          "2021581",
                        ],
                        vendorDsdWhseItemCount: 8,
                      },
                    ],
                    source: "SIMS_VENDOR",
                    billingControls: [
                      {
                        controlName: "UPP-22",
                        controlLevel: "Vendor Outlet",
                        merchVendor: "006446",
                        merchVendorNm: "KEHE DISTRIBUTORS",
                        partnerVendors: [],
                        legacyFinVendNm: "1031 MARKETING {ALLOWANCE ONLY}",
                        legacyFinVendNbr: "184870",
                        location: {
                          locationType: "Distribution Center",
                          divisionIds: ["05"],
                          stores: [],
                          rogs: [],
                          sectionIds: [],
                          distributionCenterIds: ["DDSE"],
                        },
                        effectiveStartDate: "2025-02-16",
                        createUser: "VBAND07",
                        createTs: 1739940340608,
                        lastUpdatedUser: "VBAND07",
                        lastUpdatedTs: 1739940340608,
                        partnerId: "10014568",
                        partnerNm: "1846Testing",
                        partnerCtrlType: "",
                        merchVendOutlet: ["001", "013"],
                        billingType: "I",
                        inclusion: "Out",
                        effectiveEndDate: "9999-12-31",
                        billingControlId:
                          "8f3d932e-8f07-4b86-9b7d-be3035b0a5ea",
                        versionId: "1",
                      },
                    ],
                    orgAbsVendorPaymentType: "I",
                    orgAcPayableVendorNbr: "110056",
                    orgAcReceivableVendorNbr: "      ",
                  },
                  {
                    vendorIds: [
                      {
                        vendorNbr: "006446",
                        vendorSubAccount: "001",
                        costArea: "1",
                        fullVendorNbr: "006446-001-1",
                      },
                      {
                        vendorNbr: "006446",
                        vendorSubAccount: "001",
                        costArea: "2",
                        fullVendorNbr: "006446-001-2",
                      },
                      {
                        vendorNbr: "006446",
                        vendorSubAccount: "001",
                        costArea: "4",
                        fullVendorNbr: "006446-001-4",
                      },
                    ],
                    absMerchVendor: "006446-001",
                    absVendorName: "KEHE DISTRIBUTORS",
                    absVendorPaymentType: "I",
                    acPayableVendorNbr: "",
                    acReceivableVendorNbr: "184870",
                    billingName: "1031 MARKETING {ALLOWANCE ONLY}",
                    billingContactName: "<EMAIL>     ",
                    billingContactEmail: "<EMAIL>",
                    vendorBillingList: [
                      {
                        billingContactName: "<EMAIL>     ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "<EMAIL>        ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "BRANDON SWEET            ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "<EMAIL>        ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "<EMAIL>     ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "<EMAIL>        ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "BRANDON SWEET            ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "<EMAIL>        ",
                        billingContactEmail: "<EMAIL>",
                      },
                    ],
                    vendorItemCount: 7,
                    vendorItemCountsSet: [
                      {
                        vendorDsdWhseId: {
                          vendorNbr: "006446",
                          vendorSubAccount: "001",
                          costArea: "1",
                          vendorRank: "B.DSD",
                          fullVendorNbr: "006446-001-1",
                          valid: true,
                        },
                        itemIdSet: [
                          "2010289",
                          "2020113",
                          "2020118",
                          "2020143",
                          "2020197",
                          "2020393",
                          "2021027",
                        ],
                        vendorDsdWhseItemCount: 7,
                      },
                      {
                        vendorDsdWhseId: {
                          vendorNbr: "006446",
                          vendorSubAccount: "001",
                          costArea: "2",
                          vendorRank: "B.DSD",
                          fullVendorNbr: "006446-001-2",
                          valid: true,
                        },
                        itemIdSet: [
                          "2010289",
                          "2020113",
                          "2020118",
                          "2020143",
                          "2020197",
                          "2020393",
                          "2021027",
                        ],
                        vendorDsdWhseItemCount: 7,
                      },
                      {
                        vendorDsdWhseId: {
                          vendorNbr: "006446",
                          vendorSubAccount: "001",
                          costArea: "4",
                          vendorRank: "B.DSD",
                          fullVendorNbr: "006446-001-4",
                          valid: true,
                        },
                        itemIdSet: [
                          "2010289",
                          "2020113",
                          "2020118",
                          "2020143",
                          "2020197",
                          "2020393",
                          "2021027",
                        ],
                        vendorDsdWhseItemCount: 7,
                      },
                    ],
                    source: "SIMS_VENDOR",
                    billingControls: [
                      {
                        controlName: "UPP-22",
                        controlLevel: "Vendor Outlet",
                        merchVendor: "006446",
                        merchVendorNm: "KEHE DISTRIBUTORS",
                        partnerVendors: [],
                        legacyFinVendNm: "1031 MARKETING {ALLOWANCE ONLY}",
                        legacyFinVendNbr: "184870",
                        location: {
                          locationType: "Distribution Center",
                          divisionIds: ["05"],
                          stores: [],
                          rogs: [],
                          sectionIds: [],
                          distributionCenterIds: ["DDSE"],
                        },
                        effectiveStartDate: "2025-02-16",
                        createUser: "VBAND07",
                        createTs: 1739940340608,
                        lastUpdatedUser: "VBAND07",
                        lastUpdatedTs: 1739940340608,
                        partnerId: "10014568",
                        partnerNm: "1846Testing",
                        partnerCtrlType: "",
                        merchVendOutlet: ["001", "013"],
                        billingType: "I",
                        inclusion: "Out",
                        effectiveEndDate: "9999-12-31",
                        billingControlId:
                          "8f3d932e-8f07-4b86-9b7d-be3035b0a5ea",
                        versionId: "1",
                      },
                    ],
                    orgAbsVendorPaymentType: "D",
                    orgAcPayableVendorNbr: "110056",
                    orgAcReceivableVendorNbr: "082099",
                  },
                  {
                    vendorIds: [
                      {
                        vendorNbr: "000895",
                        vendorSubAccount: "028",
                        costArea: "1",
                        fullVendorNbr: "000895-028-1",
                      },
                      {
                        vendorNbr: "000895",
                        vendorSubAccount: "028",
                        costArea: "2",
                        fullVendorNbr: "000895-028-2",
                      },
                    ],
                    absMerchVendor: "000895-028",
                    absVendorName: "MONDELEZ GLOBAL LLC",
                    absVendorPaymentType: "D",
                    acPayableVendorNbr: "045375",
                    acReceivableVendorNbr: "158846",
                    billingContactName: "<EMAIL>       ",
                    billingContactEmail: "<EMAIL>",
                    vendorBillingList: [
                      {
                        billingContactName: "<EMAIL>       ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "<EMAIL>       ",
                        billingContactEmail: "<EMAIL>",
                      },
                    ],
                    vendorItemCount: 2,
                    vendorItemCountsSet: [
                      {
                        vendorDsdWhseId: {
                          vendorNbr: "000895",
                          vendorSubAccount: "028",
                          costArea: "1",
                          vendorRank: "B.DSD",
                          fullVendorNbr: "000895-028-1",
                          valid: true,
                        },
                        itemIdSet: ["2020337", "2020338"],
                        vendorDsdWhseItemCount: 2,
                      },
                      {
                        vendorDsdWhseId: {
                          vendorNbr: "000895",
                          vendorSubAccount: "028",
                          costArea: "2",
                          vendorRank: "B.DSD",
                          fullVendorNbr: "000895-028-2",
                          valid: true,
                        },
                        itemIdSet: ["2020337", "2020338"],
                        vendorDsdWhseItemCount: 2,
                      },
                    ],
                    source: "SIMS_VENDOR",
                    orgAbsVendorPaymentType: "D",
                    orgAcPayableVendorNbr: "045375",
                    orgAcReceivableVendorNbr: "158846",
                  },
                ],
                allowanceDateOffsets: {
                  allowanceTypes: ["SCAN", "HEADER_FLAT", "ITEM_FLAT"],
                  startDateOffset: 0,
                  endDateOffset: 0,
                  defaultOrderLeadTimeDays: 0,
                  defaultShipTransitDays: 0,
                  resolvedLeadTimeDays: 0,
                  resolvedShipTransitDays: 0,
                },
                leadDistributorInfos: [],
                createAllowInd: true,
                allowanceItems: [
                  {
                    itemId: "2010289",
                    itemDescription: "ANNIES BUNNY GRAHAMS CHOC CHIP          ",
                    primaryUpc: "001356200018",
                    consumerUpc: "001356200018",
                    caseUpc: "0000000000000",
                    itemUpcs: ["0000000000000", "001356200018"],
                    consumerUpcs: [
                      {
                        upc: "001356200018",
                        rog: "SSEA",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356200018",
                        rog: "SSPK",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356200018",
                        rog: "SACG",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                    ],
                    effectiveStartDate: "2024-08-29",
                    effectiveEndDate: "9999-01-01",
                    allowanceType: "SCAN",
                    packWhse: 12,
                    ringType: 0,
                    size: "7.5 OZ",
                    vendorPackConversionFactor: 1,
                    unitNetCosts: {
                      netCostType: "UNIT",
                      cost: 3.77,
                      costAllow: 3.1784,
                      initialAllowAmt: 0,
                      newCostAllow: 3.1784,
                    },
                    masterCaseNetCosts: {
                      netCostType: "MASTER_CASE",
                      cost: 45.24,
                      costAllow: 38.14,
                      initialAllowAmt: 0,
                      newCostAllow: 38.14,
                    },
                    shipCaseNetCosts: {
                      netCostType: "SHIP_CASE",
                      cost: 45.24,
                      costAllow: 38.14,
                      initialAllowAmt: 0,
                      newCostAllow: 38.14,
                    },
                    allowanceAmount: 0,
                    allowUomType: "EA",
                    allowUomTypes: ["EA"],
                    overlaps: {
                      offerAllowAmounts: [
                        {
                          offer: 7021834,
                          rog: "SACG",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSEA",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSPK",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021364,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021829,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7020147,
                          rog: "SACG",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSEA",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSPK",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                      ],
                      unitizedOverlaps: {
                        netCostType: "UNIT",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 0.0833,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.075,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 0.3333,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 0.1,
                          },
                        ],
                        scanAllow: 0.1,
                        caseAllow: 0.0833,
                        shipAllow: 0.4083,
                        allowSum: 0.5916,
                      },
                      shipCaseOverlaps: {
                        netCostType: "SHIP_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 7.1,
                      },
                      masterCaseOverlaps: {
                        netCostType: "MASTER_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 7.1,
                      },
                    },
                    modCommand: "NONE",
                  },
                  {
                    itemId: "2020113",
                    itemDescription: "ANNIES BUNNY GRAHAMS HONEY              ",
                    primaryUpc: "001356200015",
                    consumerUpc: "001356200015",
                    caseUpc: "0001356200015",
                    itemUpcs: ["0001356200015", "001356200015"],
                    consumerUpcs: [
                      {
                        upc: "001356200015",
                        rog: "SACG",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356200015",
                        rog: "SSEA",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356200015",
                        rog: "SSPK",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                    ],
                    effectiveStartDate: "2024-08-29",
                    effectiveEndDate: "9999-01-01",
                    allowanceType: "SCAN",
                    packWhse: 12,
                    ringType: 0,
                    size: "7.5 OZ",
                    vendorPackConversionFactor: 1,
                    unitNetCosts: {
                      netCostType: "UNIT",
                      cost: 3.79,
                      costAllow: 3.0317,
                      initialAllowAmt: 0,
                      newCostAllow: 3.0317,
                    },
                    masterCaseNetCosts: {
                      netCostType: "MASTER_CASE",
                      cost: 45.48,
                      costAllow: 36.38,
                      initialAllowAmt: 0,
                      newCostAllow: 36.38,
                    },
                    shipCaseNetCosts: {
                      netCostType: "SHIP_CASE",
                      cost: 45.48,
                      costAllow: 36.38,
                      initialAllowAmt: 0,
                      newCostAllow: 36.38,
                    },
                    allowanceAmount: 0,
                    allowUomType: "EA",
                    allowUomTypes: ["EA"],
                    overlaps: {
                      offerAllowAmounts: [
                        {
                          offer: 7021834,
                          rog: "SACG",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSEA",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSPK",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021364,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021829,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 6,
                        },
                        {
                          offer: 7021829,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 6,
                        },
                        {
                          offer: 7021829,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 6,
                        },
                        {
                          offer: 7020147,
                          rog: "SACG",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSEA",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSPK",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                      ],
                      unitizedOverlaps: {
                        netCostType: "UNIT",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 0.0833,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.075,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 6,
                            convertedAmount: 0.5,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 0.1,
                          },
                        ],
                        scanAllow: 0.1,
                        caseAllow: 0.0833,
                        shipAllow: 0.575,
                        allowSum: 0.7583,
                      },
                      shipCaseOverlaps: {
                        netCostType: "SHIP_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 6,
                            convertedAmount: 6,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 6.9,
                        allowSum: 9.1,
                      },
                      masterCaseOverlaps: {
                        netCostType: "MASTER_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 6,
                            convertedAmount: 6,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 6.9,
                        allowSum: 9.1,
                      },
                    },
                    modCommand: "NONE",
                  },
                  {
                    itemId: "2020118",
                    itemDescription: "ANNIES CHEDDAR BUNNIES ORIG             ",
                    primaryUpc: "001356230215",
                    consumerUpc: "001356230215",
                    caseUpc: "0001356230215",
                    itemUpcs: ["0001356230215", "001356230215"],
                    consumerUpcs: [
                      {
                        upc: "001356230215",
                        rog: "SACG",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356230215",
                        rog: "SSEA",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356230215",
                        rog: "SSPK",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                    ],
                    effectiveStartDate: "2024-08-29",
                    effectiveEndDate: "9999-01-01",
                    allowanceType: "SCAN",
                    packWhse: 12,
                    ringType: 0,
                    size: "7.5 OZ",
                    vendorPackConversionFactor: 1,
                    unitNetCosts: {
                      netCostType: "UNIT",
                      cost: 3.77,
                      costAllow: 3.1784,
                      initialAllowAmt: 0,
                      newCostAllow: 3.1784,
                    },
                    masterCaseNetCosts: {
                      netCostType: "MASTER_CASE",
                      cost: 45.24,
                      costAllow: 38.14,
                      initialAllowAmt: 0,
                      newCostAllow: 38.14,
                    },
                    shipCaseNetCosts: {
                      netCostType: "SHIP_CASE",
                      cost: 45.24,
                      costAllow: 38.14,
                      initialAllowAmt: 0,
                      newCostAllow: 38.14,
                    },
                    allowanceAmount: 0,
                    allowUomType: "EA",
                    allowUomTypes: ["EA"],
                    overlaps: {
                      offerAllowAmounts: [
                        {
                          offer: 7021834,
                          rog: "SACG",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSEA",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSPK",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021364,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021829,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7020147,
                          rog: "SACG",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSEA",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSPK",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                      ],
                      unitizedOverlaps: {
                        netCostType: "UNIT",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 0.0833,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.075,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 0.3333,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 0.1,
                          },
                        ],
                        scanAllow: 0.1,
                        caseAllow: 0.0833,
                        shipAllow: 0.4083,
                        allowSum: 0.5916,
                      },
                      shipCaseOverlaps: {
                        netCostType: "SHIP_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 7.1,
                      },
                      masterCaseOverlaps: {
                        netCostType: "MASTER_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 7.1,
                      },
                    },
                    modCommand: "NONE",
                  },
                  {
                    itemId: "2020201",
                    itemDescription: "ANNIES BIRTHDAY CAKE BUNNY GRAHAMS      ",
                    primaryUpc: "001356210923",
                    consumerUpc: "001356210923",
                    caseUpc: "0001356210923",
                    itemUpcs: ["0001356210923", "001356210923"],
                    consumerUpcs: [
                      {
                        upc: "001356210923",
                        rog: "SACG",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356210923",
                        rog: "SSEA",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                    ],
                    effectiveStartDate: "2024-08-29",
                    effectiveEndDate: "9999-01-01",
                    allowanceType: "SCAN",
                    packWhse: 12,
                    ringType: 0,
                    size: "7.5 OZ",
                    vendorPackConversionFactor: 1,
                    unitNetCosts: {
                      netCostType: "UNIT",
                      cost: 3.33,
                      costAllow: 2.7384,
                      initialAllowAmt: 0,
                      newCostAllow: 2.7384,
                    },
                    masterCaseNetCosts: {
                      netCostType: "MASTER_CASE",
                      cost: 39.96,
                      costAllow: 32.86,
                      initialAllowAmt: 0,
                      newCostAllow: 32.86,
                    },
                    shipCaseNetCosts: {
                      netCostType: "SHIP_CASE",
                      cost: 39.96,
                      costAllow: 32.86,
                      initialAllowAmt: 0,
                      newCostAllow: 32.86,
                    },
                    allowanceAmount: 0,
                    allowUomType: "EA",
                    allowUomTypes: ["EA"],
                    overlaps: {
                      offerAllowAmounts: [
                        {
                          offer: 7021834,
                          rog: "SACG",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSEA",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSPK",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021364,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021829,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7020147,
                          rog: "SACG",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSEA",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSPK",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                      ],
                      unitizedOverlaps: {
                        netCostType: "UNIT",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 0.0833,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.075,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 0.3333,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 0.1,
                          },
                        ],
                        scanAllow: 0.1,
                        caseAllow: 0.0833,
                        shipAllow: 0.4083,
                        allowSum: 0.5916,
                      },
                      shipCaseOverlaps: {
                        netCostType: "SHIP_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 7.1,
                      },
                      masterCaseOverlaps: {
                        netCostType: "MASTER_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 7.1,
                      },
                    },
                    modCommand: "NONE",
                  },
                  {
                    itemId: "2020338",
                    itemDescription: "NBC EASY CHEESE AMERICAN                ",
                    primaryUpc: "004400004552",
                    consumerUpc: "004400004552",
                    caseUpc: "0000000000000",
                    itemUpcs: ["0000000000000", "004400004552"],
                    consumerUpcs: [
                      {
                        upc: "004400004552",
                        rog: "SSPK",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "8 OZ   ",
                      },
                      {
                        upc: "004400004552",
                        rog: "SACG",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "8 OZ   ",
                      },
                      {
                        upc: "004400004552",
                        rog: "SSEA",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "8 OZ   ",
                      },
                    ],
                    effectiveStartDate: "2024-11-14",
                    effectiveEndDate: "9999-01-01",
                    allowanceType: "SCAN",
                    packWhse: 12,
                    ringType: 0,
                    size: "8.0 OZ",
                    vendorPackConversionFactor: 1,
                    unitNetCosts: {
                      netCostType: "UNIT",
                      cost: 4.49,
                      costAllow: 3.9984,
                      initialAllowAmt: 0,
                      newCostAllow: 3.9984,
                    },
                    masterCaseNetCosts: {
                      netCostType: "MASTER_CASE",
                      cost: 53.88,
                      costAllow: 47.98,
                      initialAllowAmt: 0,
                      newCostAllow: 47.98,
                    },
                    shipCaseNetCosts: {
                      netCostType: "SHIP_CASE",
                      cost: 53.88,
                      costAllow: 47.98,
                      initialAllowAmt: 0,
                      newCostAllow: 47.98,
                    },
                    allowanceAmount: 0,
                    allowUomType: "EA",
                    allowUomTypes: ["EA"],
                    overlaps: {
                      offerAllowAmounts: [
                        {
                          offer: 7021834,
                          rog: "SACG",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSEA",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSPK",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021364,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021829,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 4,
                        },
                      ],
                      unitizedOverlaps: {
                        netCostType: "UNIT",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 0.0833,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.075,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 0.3333,
                          },
                        ],
                        scanAllow: 0,
                        caseAllow: 0.0833,
                        shipAllow: 0.4083,
                        allowSum: 0.4916,
                      },
                      shipCaseOverlaps: {
                        netCostType: "SHIP_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                        ],
                        scanAllow: 0,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 5.9,
                      },
                      masterCaseOverlaps: {
                        netCostType: "MASTER_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                        ],
                        scanAllow: 0,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 5.9,
                      },
                    },
                    modCommand: "NONE",
                  },
                  {
                    itemId: "2020393",
                    itemDescription: "ANNIES CRACKER HOMEGROWN CHEDDAR SQUARES",
                    primaryUpc: "001356200053",
                    consumerUpc: "001356200053",
                    caseUpc: "0000000000000",
                    itemUpcs: ["0000000000000", "001356200053"],
                    consumerUpcs: [
                      {
                        upc: "001356200053",
                        rog: "SACG",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356200053",
                        rog: "SSPK",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356200053",
                        rog: "SSEA",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                    ],
                    effectiveStartDate: "2024-08-29",
                    effectiveEndDate: "9999-01-01",
                    allowanceType: "SCAN",
                    packWhse: 12,
                    ringType: 0,
                    size: "7.5 OZ",
                    vendorPackConversionFactor: 1,
                    unitNetCosts: {
                      netCostType: "UNIT",
                      cost: 3.77,
                      costAllow: 3.1784,
                      initialAllowAmt: 0,
                      newCostAllow: 3.1784,
                    },
                    masterCaseNetCosts: {
                      netCostType: "MASTER_CASE",
                      cost: 45.24,
                      costAllow: 38.14,
                      initialAllowAmt: 0,
                      newCostAllow: 38.14,
                    },
                    shipCaseNetCosts: {
                      netCostType: "SHIP_CASE",
                      cost: 45.24,
                      costAllow: 38.14,
                      initialAllowAmt: 0,
                      newCostAllow: 38.14,
                    },
                    allowanceAmount: 0,
                    allowUomType: "EA",
                    allowUomTypes: ["EA"],
                    overlaps: {
                      offerAllowAmounts: [
                        {
                          offer: 7021834,
                          rog: "SACG",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSEA",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSPK",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021364,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021829,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7020147,
                          rog: "SACG",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSEA",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSPK",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                      ],
                      unitizedOverlaps: {
                        netCostType: "UNIT",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 0.0833,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.075,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 0.3333,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 0.1,
                          },
                        ],
                        scanAllow: 0.1,
                        caseAllow: 0.0833,
                        shipAllow: 0.4083,
                        allowSum: 0.5916,
                      },
                      shipCaseOverlaps: {
                        netCostType: "SHIP_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 7.1,
                      },
                      masterCaseOverlaps: {
                        netCostType: "MASTER_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 7.1,
                      },
                    },
                    modCommand: "NONE",
                  },
                  {
                    itemId: "2021027",
                    itemDescription: "ANNIES CRACKERS CHEDDAR BUNNIES WHITE   ",
                    primaryUpc: "001356230228",
                    consumerUpc: "001356230228",
                    caseUpc: "0001356230228",
                    itemUpcs: ["0001356230228", "001356230228"],
                    consumerUpcs: [
                      {
                        upc: "001356230228",
                        rog: "SSEA",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356230228",
                        rog: "SACG",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                      {
                        upc: "001356230228",
                        rog: "SSPK",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "7.5 OZ ",
                      },
                    ],
                    effectiveStartDate: "2024-08-29",
                    effectiveEndDate: "9999-01-01",
                    allowanceType: "SCAN",
                    packWhse: 12,
                    ringType: 0,
                    size: "7.5 OZ",
                    vendorPackConversionFactor: 1,
                    unitNetCosts: {
                      netCostType: "UNIT",
                      cost: 3.78,
                      costAllow: 3.1884,
                      initialAllowAmt: 0,
                      newCostAllow: 3.1884,
                    },
                    masterCaseNetCosts: {
                      netCostType: "MASTER_CASE",
                      cost: 45.36,
                      costAllow: 38.26,
                      initialAllowAmt: 0,
                      newCostAllow: 38.26,
                    },
                    shipCaseNetCosts: {
                      netCostType: "SHIP_CASE",
                      cost: 45.36,
                      costAllow: 38.26,
                      initialAllowAmt: 0,
                      newCostAllow: 38.26,
                    },
                    allowanceAmount: 0,
                    allowUomType: "EA",
                    allowUomTypes: ["EA"],
                    overlaps: {
                      offerAllowAmounts: [
                        {
                          offer: 7021834,
                          rog: "SACG",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSEA",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSPK",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021364,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021829,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7020147,
                          rog: "SACG",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSEA",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                        {
                          offer: 7020147,
                          rog: "SSPK",
                          basisDsc: "T",
                          amount: 0.1,
                        },
                      ],
                      unitizedOverlaps: {
                        netCostType: "UNIT",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 0.0833,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.075,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 0.3333,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 0.1,
                          },
                        ],
                        scanAllow: 0.1,
                        caseAllow: 0.0833,
                        shipAllow: 0.4083,
                        allowSum: 0.5916,
                      },
                      shipCaseOverlaps: {
                        netCostType: "SHIP_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 7.1,
                      },
                      masterCaseOverlaps: {
                        netCostType: "MASTER_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                          {
                            offer: 7020147,
                            basisDsc: "T",
                            amount: 0.1,
                            convertedAmount: 1.2,
                          },
                        ],
                        scanAllow: 1.2,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 7.1,
                      },
                    },
                    modCommand: "NONE",
                  },
                  {
                    itemId: "2021581",
                    itemDescription: "ANNIES HOMEGROWN SNACK MIX CHEDDAR ORG  ",
                    primaryUpc: "001356230057",
                    consumerUpc: "001356230057",
                    caseUpc: "0001356230057",
                    itemUpcs: ["0001356230057", "001356230057"],
                    consumerUpcs: [
                      {
                        upc: "001356230057",
                        rog: "SACG",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "9 OZ   ",
                      },
                      {
                        upc: "001356230057",
                        rog: "SSEA",
                        primaryInd: true,
                        labelSize: "M",
                        packDesc: "012",
                        sizeDesc: "9 OZ   ",
                      },
                    ],
                    effectiveStartDate: "2024-09-11",
                    effectiveEndDate: "9999-01-01",
                    allowanceType: "SCAN",
                    packWhse: 12,
                    ringType: 0,
                    size: "9.0 OZ",
                    vendorPackConversionFactor: 1,
                    unitNetCosts: {
                      netCostType: "UNIT",
                      cost: 5.01,
                      costAllow: 4.5184,
                      initialAllowAmt: 0,
                      newCostAllow: 4.5184,
                    },
                    masterCaseNetCosts: {
                      netCostType: "MASTER_CASE",
                      cost: 60.12,
                      costAllow: 54.22,
                      initialAllowAmt: 0,
                      newCostAllow: 54.22,
                    },
                    shipCaseNetCosts: {
                      netCostType: "SHIP_CASE",
                      cost: 60.12,
                      costAllow: 54.22,
                      initialAllowAmt: 0,
                      newCostAllow: 54.22,
                    },
                    allowanceAmount: 0,
                    allowUomType: "EA",
                    allowUomTypes: ["EA"],
                    overlaps: {
                      offerAllowAmounts: [
                        {
                          offer: 7021834,
                          rog: "SACG",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSEA",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021834,
                          rog: "SSPK",
                          basisDsc: "C",
                          amount: 1,
                        },
                        {
                          offer: 7021364,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021364,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 0.9,
                        },
                        {
                          offer: 7021829,
                          rog: "SACG",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSEA",
                          basisDsc: "S",
                          amount: 4,
                        },
                        {
                          offer: 7021829,
                          rog: "SSPK",
                          basisDsc: "S",
                          amount: 4,
                        },
                      ],
                      unitizedOverlaps: {
                        netCostType: "UNIT",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 0.0833,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.075,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 0.3333,
                          },
                        ],
                        scanAllow: 0,
                        caseAllow: 0.0833,
                        shipAllow: 0.4083,
                        allowSum: 0.4916,
                      },
                      shipCaseOverlaps: {
                        netCostType: "SHIP_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                        ],
                        scanAllow: 0,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 5.9,
                      },
                      masterCaseOverlaps: {
                        netCostType: "MASTER_CASE",
                        convertedAllowanceAmounts: [
                          {
                            offer: 7021834,
                            basisDsc: "C",
                            amount: 1,
                            convertedAmount: 1,
                          },
                          {
                            offer: 7021364,
                            basisDsc: "S",
                            amount: 0.9,
                            convertedAmount: 0.9,
                          },
                          {
                            offer: 7021829,
                            basisDsc: "S",
                            amount: 4,
                            convertedAmount: 4,
                          },
                        ],
                        scanAllow: 0,
                        caseAllow: 1,
                        shipAllow: 4.9,
                        allowSum: 5.9,
                      },
                    },
                    modCommand: "NONE",
                  },
                ],
                headerFlatAmt: 0,
                allowanceStatus: "Draft",
                storeGroups: [],
                leadDistributorInd: false,
                includeInd: true,
                finalizedAmountsInd: false,
                excludedItems: [
                  {
                    itemId: "2020143",
                    excludeInd: "DEACTIVATED_ITEM",
                  },
                  {
                    itemId: "2020197",
                    excludeInd: "DEACTIVATED_ITEM",
                  },
                  {
                    itemId: "2020337",
                    excludeInd: "DEACTIVATED_ITEM",
                  },
                ],
                allowanceProcessStatus: "",
                allowDownstreamStatus: {},
              },
            ],
          },
        },
      },
    },
    cardIndex: 0,
    cardItemIndex: 0,
    isEditEnable: false,
    isNational: false,
  };

  test.skip("should return expected derived values", () => {
    const { result } = renderHook(() => useGetAmountDerivedValues(mockProps));

    expect(result.current.sectionConfiguration).toBeDefined();
    expect(result.current.mapperKey).toBe("DSD_WHSE_RETAIL_DIVISION");
    expect(result.current.allowanceTypeKey).toBe("SCAN");
    expect(result.current.isHfOrIf).toBe(false);
    expect(result.current.isHfIfWhseCase).toBe(false);
    expect(result.current.offerNumber).toBe(undefined);
    expect(result.current.allowanceRegField).toBe(
      "offerAllowances[0].allowances[0]"
    );
    expect(result.current.amountsInitialValueOnLoad).toEqual({});
    expect(result.current.allowName).toBe("scanAllow");
  });

  test("should return defaultValues if edit is true", () => {
    const newMockProps = {
      ...mockProps,
      isEditEnable: true,
      isNational: false,
      allowanceTempWorkData: {
        ...mockProps.allowanceTempWorkData,
        allowanceTypeSpecification: {
          ...mockProps.allowanceTempWorkData.allowanceTypeSpecification,
          scanAllow: {
            ...mockProps.allowanceTempWorkData.allowanceTypeSpecification
              .scanAllow,
            offerNumber: "12345",
          },
        },
      },
      allowanceForm: {
        allowanceFormData: {
          "offerAllowances[0].allowances[0]": {
            allowancePrimeData: {
              allowanceType: "Scan",
              createInd: "TC",
              performance: "4U Event (52)",
              perfConfig: {
                allowanceType: "Scan",
                allowanceCd: "T",
                perfCode1: "20",
                perfCode2: "52",
                id: "63a3a12743a6cee87995b834",
                performance: "4U Event (52)",
                performanceConfig: {
                  __typename: "PerformanceConfig",
                  defaultCreateInd: "TC",
                  allowOnlyOverrideStoreGroupsInd: false,
                  allowancePerformanceChildId: null,
                },
              },
              allowanceToBeCreated: "DSD_WHSE_RETAIL_DIVISION",
              allowanceToBeCreatedOption: {
                name: "One Allowance: Warehouse, DSD, or Combined",
                key: "DSD_WHSE_RETAIL_DIVISION",
                routingKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceMap: {
                  DSD_WHSE_RETAIL_DIVISION: [],
                },
                default: false,
                createIndex: "TC",
              },
              isAdditionalDatesChanged: false,
            },
            allowanceAmountsData: {
              DSD_WHSE_RETAIL_DIVISION: {
                uom: "EA",
                allowanceAmount: "4",
              },
            },
          },
        },
      },
    };

    const { result } = renderHook(() =>
      useGetAmountDerivedValues(newMockProps)
    );
    expect(result.current.amountsInitialValueOnLoad).toEqual({
      uom: "EA",
      allowanceAmount: "4",
    });
  });
});
