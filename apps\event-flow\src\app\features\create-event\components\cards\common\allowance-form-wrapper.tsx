import Button from "@albertsons/uds/molecule/Button";
import efConstants from "../../../../../shared/ef-constants/ef-constants";
import { ReactElement, useEffect } from "react";
import {
  Control,
  useForm,
  UseFormSetError,
  UseFormClearErrors,
  UseFormGetValues,
  UseFormRegister,
  UseFormReset,
  UseFormSetValue,
  UseFormTrigger,
  useWatch,
  UseFormGetFieldState,
} from "react-hook-form";

export interface IFormControls {
  register: UseFormRegister<any>;
  control: Control<any, any>;
  trigger: UseFormTrigger<any>;
  reset: UseFormReset<any>;
  getValues: UseFormGetValues<any>;
  setValue: UseFormSetValue<any>;
  setError: UseFormSetError<any>;
  clearErrors: UseFormClearErrors<any>;
  getFieldState: UseFormGetFieldState<any>;
  formState: any
}

interface IAllowanceFormWrapper {
  defaultValues: any;
  handleSave: (formValues: any) => void;
  getFormControls: (controls: IFormControls) => void;
  children: ReactElement | null;
  footerProps?: IFooterProps;
  setValues?: any;
  variant?: "secondary" | "primary" | "tertiary" | undefined;
  watchFields?: string[];
}

interface IFooterProps {
  label: string;
  type?: "reset" | "submit" | "button";
  className?: string;
  visable?: boolean;
  disabled?: boolean;
  FooterChildren?: any;
  style?: object;
}

const AllowanceFormWrapper = ({
  defaultValues,
  handleSave,
  getFormControls,
  children,
  footerProps,
  setValues,
  variant = "primary",
  watchFields = [],
}: IAllowanceFormWrapper) => {
  const {
    register,
    control,
    getValues,
    setValue,
    handleSubmit,
    reset,
    trigger,
    setError,
    clearErrors,
    getFieldState,
    formState,
  } = useForm({
    defaultValues,
  });

  const values = useWatch({ control, name: watchFields });

  useEffect(() => {
    control &&
      getFormControls({
        register,
        control,
        trigger,
        getValues,
        setValue,
        reset,
        setError,
        clearErrors,
        getFieldState,
        formState
      });
  }, [formState?.isDirty]);

  useEffect(() => {
    setValues?.(values);
  }, [values]);

  const getFooterContent = ({
    label,
    type = "submit",
    className = "py-3 pl-[0px]",
    visable = true,
    disabled = false,
    FooterChildren,
    style = {},
  }: IFooterProps) => {
    return visable ? (
      <div className={className} id="abs-allowance-form-wrapper-label-cont">
        <Button
          {...style}
          className="w-fit p-3"
          type={type}
          disabled={disabled}
          variant={variant}
        >
          {label}
        </Button>
        {FooterChildren}
      </div>
    ) : null;
  };

  return (
    <form
      onSubmit={handleSubmit(handleSave)}
      noValidate
      id="abs-allowance-form-wrapper-form"
      data-testid="abs-data-allowance-form-wrapper-form"
      className={`${efConstants.componentClassName.ALLOWANCE_FORM_WRAPPER}`}
    >
      {children}
      {footerProps && getFooterContent(footerProps)}
    </form>
  );
};

export default AllowanceFormWrapper;
