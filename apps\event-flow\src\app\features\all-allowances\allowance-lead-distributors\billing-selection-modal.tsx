import Button from "@albertsons/uds/molecule/Button";
import Checkbox from "@albertsons/uds/molecule/Checkbox";
import Modal from "@albertsons/uds/molecule/Modal";
import { Radio } from "@albertsons/uds/molecule/Radio";
import { useSelectorWrap } from "@me/data-rtk";
import { ChevronDown, ChevronUp } from "lucide-react";
import efConstants from "../../../shared/ef-constants/ef-constants";
import useBillingSelection from "../../create-event/hooks/useBillingSelection";
import "./lead-distributor-modal.scss";
import {
  leadDistributorsHandler,
  setInitialLeadDistData,
} from "../../create-event/service/slice/lead-distributors-slice";
import { useDispatch } from "react-redux";
import classNames from "classnames";
import { onCloseDistPopup } from "./billing-selection-utils";

type Props = {
  isModelOpen: boolean;
  setIsModelOpen: any;
};

export default function BillingSelectionModal({
  isModelOpen = false,
  setIsModelOpen,
}: Props) {
  const {
      data: {
        leadOptions,
        stepData: selectedSteps,
        leadDistMode: defaultModeSelection,
        leadSelectionType
      },
    } = useSelectorWrap("leadDistributors_rn") || {},
    { data: allowGroupConfigObj } =
      useSelectorWrap("allowanceTableColsGroupConfig_rn") || {};
  const leadDistMode = efConstants.BILL_LEAD_DIST;
  const { billingInfoConfig = {} } = allowGroupConfigObj || {};
  const dispatch = useDispatch();

  const { moveDown, moveUp, mainVendors, mapping } = useBillingSelection({
    vendors: leadOptions,
  });

  let maxLength;
  mainVendors?.forEach(vendorData => {
    if (vendorData) {
      const vendor = leadOptions?.find(item => item?.vendorNbr === vendorData);
      if (vendor) {
        const length =
          vendor?.vendorName?.length +
          vendor?.vendorNbr?.length +
          vendor?.costAreaDesc?.length;
        if (length > maxLength?.length) {
          maxLength = length;
        }
      }
    }
  });

  const vendorLists = (
    <div className="pl-[10rem] pr-4 abs-ef-billing-selection-modal-vendor-lists">
      <section
        className="flex flex-col w-[100%] p-2 modal-cls border border-gray-500  overflow-scroll lead-dist-vendor-section"
        style={{ height: "12vw" }}
      >
        {mainVendors.map((vendorNbr, index) => {
          const { child } = mapping[index];
          return (
            <div className="flex flex-col gap-1">
              <div className="flex">
                <Checkbox checked disabled className="py-[2px]">
                  {getDistributorsNameSummary(vendorNbr, leadOptions)}
                </Checkbox>
              </div>

              {child.length ? (
                <>
                  {child.map(vendor => {
                    return (
                      <div
                        className="flex pl-4 justify-between w-full"
                        key={vendor}
                      >
                        <Checkbox
                          disabled
                          className={classNames({
                            "py-[2px]": true,
                            "[&>label]:w-full": true,
                          })}
                        >
                          {getDistributorsNameSummary(vendor, leadOptions)}
                        </Checkbox>
                        <div className="flex text-[#1B6EBB] hover:cursor-pointer">
                          {index === 0 ? null : (
                            <div
                              className="flex"
                              onClick={e => {
                                moveUp(vendor);
                              }}
                            >
                              Move
                              <ChevronUp fill="white" />
                            </div>
                          )}
                          {index === mainVendors.length - 1 ? null : (
                            <div
                              className="flex"
                              onClick={e => {
                                moveDown(vendor);
                              }}
                            >
                              Move
                              <ChevronDown fill="white" />
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </>
              ) : null}
            </div>
          );
        })}
      </section>
    </div>
  );
  const popperCloseArgs = {
    selectedSteps,
    defaultModeSelection,
    dispatch,
    setIsModelOpen,
    leadSelectionType
  };
  const saveButton = (
    <div className="flex items-center justify-center w-full my-4 abs-ef-billing-selection-modal-save-btn">
      <Button
        width={82}
        size="lg"
        className="mr-2 whitespace-nowrap"
        variant="secondary"
        onClick={() => onCloseDistPopup(popperCloseArgs)}
      >
        Cancel
      </Button>
      <Button
        width={92}
        size="lg"
        className="ml-2 whitespace-nowrap"
        onClick={() => {
          dispatch(
            leadDistributorsHandler({
              stepData: mapping,
              leadDistMode,
              leadSelectionType: efConstants.BILL_DIST_LABEL,
            })
          );
          dispatch(
            setInitialLeadDistData({
              leadDistData: leadDistMode ? mapping : [],
            })
          );
          setIsModelOpen(false);
        }}
      >
        Confirm
      </Button>
    </div>
  );

  return (
    <Modal
      isOpen={isModelOpen}
      onClose={() => onCloseDistPopup(popperCloseArgs)}
      height={630}
      width={800 + maxLength * mainVendors?.length}
      className="overflow-y-auto display-scroll abs-billing-section-modal"
    >
      {leadDistStaticHtml}
      {/* it will be non edit-able */}
      {leadDistModeOptions(leadDistMode, billingInfoConfig)}
      {subLabelText}
      {vendorLists}

      {saveButton}
    </Modal>
  );
}

function getDistributorsNameSummary(id, leadOptions) {
  const vendor = leadOptions.find(item => item.vendorNbr === id);
  return (
    <span className="abs-ef-get-distributors-name-summary">
      <span className="font-bold">{vendor?.vendorName} </span>- {id}{" "}
      {vendor?.costAreaDesc}
    </span>
  );
}

const leadDistStaticHtml = (
  <div className="text-center select-none font-bold text-[28px] mt-8 abs-ef-lead-dist-static-html">
    Added Vendor Billing Selections
  </div>
);

const subLabelText = (
  <div className="flex flex-col mx-[70px] my-[24px] text-center text-sm leading-4 text-[#5A697B] gap-2 abs-ef-sub-label-text">
    <p className="">
      Select the Distributor(s) that you wish to have the Additional Cost Areas
      Billed to
    </p>
  </div>
);

const leadDistModeOptions = (leadDistMode, billingInfoConfig) => (
  <div
    className={`eet py-6 border-t border-b mx-5 my-8 ${efConstants.componentClassName.LEAD_DIST_MODE_OPTIONS}`}
  >
    <div className="flex flex-col">
      <Radio.Group
        horizontal={true}
        value={leadDistMode}
        onChange={e => null}
        className="lead-distributor-radio-group"
      >
        {Object?.keys(billingInfoConfig)?.map(leadDisOption => (
          <Radio
            label={billingInfoConfig[leadDisOption]?.displayLabel}
            value={billingInfoConfig[leadDisOption]?.value}
            key={billingInfoConfig[leadDisOption]?.key}
            disabled={billingInfoConfig[leadDisOption].disable}
          />
        ))}
      </Radio.Group>
      <div className="flex justify-around">
        <div style={{ width: "41%" }} className="text-[#5A697B]">
          {billingInfoConfig?.LEAD_DIST_ONLY?.subText}
        </div>
        <div className="pl-[3.2rem] text-[#5A697B]" style={{ width: "43%" }}>
          {billingInfoConfig?.BILL_LEAD_DIST?.subText}
        </div>
      </div>
    </div>
  </div>
);
