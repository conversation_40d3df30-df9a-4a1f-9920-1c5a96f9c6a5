import { fireEvent, render, screen } from "@testing-library/react";
import EventCommentsSection from "./event-comments-section";
import { Provider } from "react-redux";
import { app_store } from "@me/data-rtk";
import { SetStateAction } from "react";
import { IPlanEventCommentDTO } from "./interfaces/plan-event-comment-interface";
import userEvent from "@testing-library/user-event";
import { EVENT_COMMENTS } from "./constants/event-comments-constants";
import {
  mockAllCommentsList,
  mockAllCommentsListShortened,
} from "./event-comments-mock";
import * as MEUPP_UTILS from "@me-upp-js/utilities";
import "@testing-library/jest-dom";

import * as COMMENTS_API from "./event-comments-section-service";
import * as rtk_tools from "@me/data-rtk";
import { promo10003136 } from "../../shared/event-flow-mocks/promotions-mock";

jest.mock("@me/util-helpers", () => ({
  getOAMRemoteUser: () => ({}),
  formatDateTimeWithZone:() => ({}),
  useGetQueryParams: () => ({ queryParamValue: { isCommentOpen: true } }),
}));

describe("Event Comments Section Component Test Suite", () => {
  class ResizeObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
  }
  const mockEventDetailsData = {
    id: "6448ee5453eea24e88fba49b",
    planEventIdNbr: 10006146,
    name: "Knudsen Cottage Cheese 16 OZ - Norcal California All Stores  -Teste5",
    divisionIds: ["25"],
    startDate: 1690351200000,
    endDate: 1690869600000,
    eventType: "DP",
    sourceEventType: "ECP",
    eventStatus: "Draft",
    promotionsList: [promo10003136],
    promotions: ["6448ee8d4440742b1218498f"],
    createUser: {
      userId: "SPOTH03",
      name: "Sridhar pothanaveni",
      type: "Vendor",
      userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
      createTs: "2023-05-10T06:55:55.355Z",
    },
    updateUser: {
      userId: "SPOTH03",
      name: "Sridhar pothanaveni",
      type: "Vendor",
      userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
      createTs: "2023-05-10T06:55:55.355Z",
    },
    planEventWorkFlowType: "NOT FOUND",
    eventTypeEnum: "DP",
  };
  beforeEach(() => {
    window.ResizeObserver = ResizeObserver as any;
    window["OAM_REMOTE_USER"] = "TESTUSER";

    // IntersectionObserver isn't available in test environment
    const mockIntersectionObserver = jest.fn();
    mockIntersectionObserver.mockReturnValue({
      observe: () => null,
      unobserve: () => null,
      disconnect: () => null,
    });
    window.IntersectionObserver = mockIntersectionObserver;
    jest
      .spyOn(rtk_tools, "useSelectorWrap")
      .mockImplementation(useSelectorName => {
        if (useSelectorName === "event_details_data") {
          return { data: mockEventDetailsData };
        } else if (useSelectorName === "plan_event_indicators") {
          return { data: {} };
        } else if (useSelectorName === "isFromCommentSection_rn") {
          return { data: {} };
        }
      });
  });

  it("should render the event comments section component", async () => {
    const user = userEvent.setup();

    render(
      <Provider store={app_store}>
        <EventCommentsSection
          eventId={"6446d376fac61d52c3700e8a"}
          isEventCommentsOpen={true}
          setIsEventCommentsOpen={jest.fn()}
          allCommentsList={mockAllCommentsList}
          isEventCommentsLoading={false}
          parentCommentUpdater={undefined}
          setCommentOpen={jest.fn()}
        />
      </Provider>
    );
    const comment = await screen.findByText("test 2".trim(), { exact: false });
    const comment_div = await (
      await screen.findByText("test 2".trim(), { exact: false })
    ).closest("div");
    user.hover(comment);
  });

  xit("should be able to hover a comment", async () => {
    render(
      <Provider store={app_store}>
        <EventCommentsSection
          eventId={"6446d376fac61d52c3700e8a"}
          isEventCommentsOpen={true}
          setIsEventCommentsOpen={jest.fn()}
          allCommentsList={mockAllCommentsListShortened}
          isEventCommentsLoading={false}
          parentCommentUpdater={undefined}
          setCommentOpen={jest.fn()}
        />
      </Provider>
    );
    const comment = await screen.findByText("test 2".trim(), { exact: false });
    fireEvent.mouseEnter(comment);
    const editIcon = await screen.findAllByText("Edit");
    expect(editIcon?.[0]).toBeTruthy();
  });

  xit("should be able to click edit on a hovered a comment", async () => {
    // skipped due to product design change. No longer showing edit on hover
    render(
      <Provider store={app_store}>
        <EventCommentsSection
          eventId={"6446d376fac61d52c3700e8a"}
          isEventCommentsOpen={true}
          setIsEventCommentsOpen={jest.fn()}
          allCommentsList={mockAllCommentsListShortened}
          isEventCommentsLoading={false}
          parentCommentUpdater={undefined}
          setCommentOpen={jest.fn()}
        />
      </Provider>
    );
    const comment = await screen.findByText("test 2".trim(), { exact: false });
    fireEvent.mouseEnter(comment);
    const editIcon = await screen.findAllByText("Edit".trim(), {
      exact: false,
    });
    fireEvent.click(editIcon?.[0]);
    expect(await screen.findByText("Cancel")).toBeTruthy();
  });
  xit("should be able to enter a comment in the edit text area", async () => {
    render(
      <Provider store={app_store}>
        <EventCommentsSection
          eventId={"6446d376fac61d52c3700e8a"}
          isEventCommentsOpen={true}
          setIsEventCommentsOpen={jest.fn()}
          allCommentsList={mockAllCommentsListShortened}
          isEventCommentsLoading={false}
          parentCommentUpdater={undefined}
          setCommentOpen={jest.fn()}
        />
      </Provider>
    );
    const comment = await screen.findByText("test 2".trim(), { exact: false });
    fireEvent.mouseEnter(comment);
    const editIcon = await screen.findAllByText("Edit".trim(), {
      exact: false,
    });
    fireEvent.click(editIcon?.[0]);

    // confirm if edit was successful

    const test2TextArea = await screen.findByDisplayValue("test 2");
    const user = userEvent.setup();
    await user.type(test2TextArea, "TEST 2 Is Here");
    expect(await screen.findByText("test 2TEST 2 Is Here")).toBeTruthy();
  });

  xit("should be able to enter a comment in the new comment text area", async () => {
    render(
      <Provider store={app_store}>
        <EventCommentsSection
          eventId={"6446d376fac61d52c3700e8a"}
          isEventCommentsOpen={true}
          setIsEventCommentsOpen={jest.fn()}
          allCommentsList={mockAllCommentsListShortened}
          isEventCommentsLoading={false}
          parentCommentUpdater={undefined}
          setCommentOpen={jest.fn()}
        />
      </Provider>
    );
    // FIGMA is  not sure on the billing inquiry and the other two tabs so made exact false for now as they're substrings
    const newCommentTextArea = await screen.findByPlaceholderText(
      "Please add details",
      { exact: false }
    );
    const user = userEvent.setup();
    const myCommentText = "MY NEW COMMENT TO SUBMIT. WHY CAPS? I dont know";
    await user.type(newCommentTextArea, myCommentText);

    expect(screen.findByText(myCommentText)).toBeTruthy();
  });

  it("should be able to render MERCHANT VIEW ", async () => {
    jest.spyOn(MEUPP_UTILS, "getLoggedInUserType").mockReturnValue("MERCHANT");
    render(
      <Provider store={app_store}>
        <EventCommentsSection
          eventId={"6446d376fac61d52c3700e8a"}
          isEventCommentsOpen={true}
          setIsEventCommentsOpen={jest.fn()}
          allCommentsList={mockAllCommentsListShortened}
          isEventCommentsLoading={false}
          parentCommentUpdater={undefined}
          setCommentOpen={jest.fn()}
        />
      </Provider>
    );

    expect(screen.findByText("Billing Inquiry")).toBeTruthy();
    expect(screen.findByText("Internal")).toBeTruthy();
    expect(screen.findByText("External")).toBeTruthy();
  });

  xit("should show the correct text label in MERCHANT VIEW for each tabs", async () => {
    jest.spyOn(MEUPP_UTILS, "getLoggedInUserType").mockReturnValue("MERCHANT");
    render(
      <Provider store={app_store}>
        <EventCommentsSection
          eventId={"6446d376fac61d52c3700e8a"}
          isEventCommentsOpen={true}
          setIsEventCommentsOpen={jest.fn()}
          allCommentsList={mockAllCommentsListShortened}
          isEventCommentsLoading={false}
          parentCommentUpdater={undefined}
          setCommentOpen={jest.fn()}
        />
      </Provider>
    );
    const user = userEvent.setup();
    const externalTab = await screen.findByText("External");
    user.click(externalTab);
    expect(
      await screen.findByText(EVENT_COMMENTS["VENDOR_MERCHANT_TEXT_AREA_LABEL"])
    ).toBeTruthy();
    const billingTab = await screen.findByText("Billing Inquiry");
    user.click(billingTab);
    expect(
      await screen.findByText(EVENT_COMMENTS["BILLING_INQUIRY"])
    ).toBeTruthy();
    const internalTab = await screen.findByText("Internal");
    user.click(internalTab);
    // TODO: Arian please look into it
    // expect(await screen.findByText(EVENT_COMMENTS["INTERNAL_TEXT_AREA_LABEL"])).toBeTruthy();
  });

  xit("should show disabled Add Comment button when comment is greater than 300 character", async () => {
    jest.spyOn(MEUPP_UTILS, "getLoggedInUserType").mockReturnValue("MERCHANT");
    render(
      <Provider store={app_store}>
        <EventCommentsSection
          eventId={"6446d376fac61d52c3700e8a"}
          isEventCommentsOpen={true}
          setIsEventCommentsOpen={jest.fn()}
          allCommentsList={mockAllCommentsListShortened}
          isEventCommentsLoading={false}
          parentCommentUpdater={undefined}
          setCommentOpen={jest.fn()}
        />
      </Provider>
    );
    const user = userEvent.setup();
    const newCommentTextArea = await screen.findByPlaceholderText(
      "Please add details",
      { exact: false }
    );
    const myCommentText = "My Comment Length";
    await user.type(newCommentTextArea, myCommentText);
    expect(await screen.findByText(`${myCommentText.length}/300`)).toBeTruthy();
    const longComment = `${"a".repeat(302)}`;
    await user.clear(newCommentTextArea);
    // TODO: Arian please look into it
    await user.type(newCommentTextArea, longComment);
    expect(await screen.findByText(`302/300`)).toBeTruthy();
    // expect(screen.findByText(myCommentText)).toBeTruthy();
  });

  xit("should be able to click cancel while editing a comment", async () => {
    render(
      <Provider store={app_store}>
        <EventCommentsSection
          eventId={"6446d376fac61d52c3700e8a"}
          isEventCommentsOpen={true}
          setIsEventCommentsOpen={jest.fn()}
          allCommentsList={mockAllCommentsListShortened}
          isEventCommentsLoading={false}
          parentCommentUpdater={undefined}
          setCommentOpen={jest.fn()}
        />
      </Provider>
    );
    const comment = await screen.findByText("test 2".trim(), { exact: false });
    fireEvent.mouseEnter(comment);
    const editIcon = await screen.findAllByText("Edit".trim(), {
      exact: false,
    });
    fireEvent.click(editIcon?.[0]);
    const cancelButton = await screen.findByText("Cancel");
    expect(cancelButton).toBeTruthy();
    const user = userEvent.setup();

    await user.click(cancelButton);
    expect(cancelButton).not.toBeInTheDocument();
  });

  xit("should be able to click update while editing a comment", async () => {
    render(
      <Provider store={app_store}>
        <EventCommentsSection
          eventId={"6446d376fac61d52c3700e8a"}
          isEventCommentsOpen={true}
          setIsEventCommentsOpen={jest.fn()}
          allCommentsList={mockAllCommentsListShortened}
          isEventCommentsLoading={false}
          parentCommentUpdater={undefined}
          setCommentOpen={jest.fn()}
        />
      </Provider>
    );
    const comment = await screen.findByText("test 2".trim(), { exact: false });
    fireEvent.mouseEnter(comment);
    const editIcon = await screen.findAllByText("Edit".trim(), {
      exact: false,
    });
    fireEvent.click(editIcon?.[0]);
    const updateButton = await screen.findByText("Update");
    expect(updateButton).toBeTruthy();
    const user = userEvent.setup();

    await user.click(updateButton);
  });

  xit("should show warning Modal if event comment is not submitted and close without saving", async () => {
    render(
      <Provider store={app_store}>
        <EventCommentsSection
          eventId={"6446d376fac61d52c3700e8a"}
          isEventCommentsOpen={true}
          setIsEventCommentsOpen={jest.fn()}
          allCommentsList={mockAllCommentsListShortened}
          isEventCommentsLoading={false}
          parentCommentUpdater={undefined}
          setCommentOpen={jest.fn()}
        />
      </Provider>
    );

    const user = userEvent.setup();
    const newCommentTextArea = await screen.findByPlaceholderText(
      "Please add details",
      { exact: false }
    );
    const myCommentText = "My Comment Length";
    await user.type(newCommentTextArea, myCommentText);
    const CommentsHeader = await screen.findByText("Event Comments");
    await user.click(CommentsHeader);
    const WarningTitleModal = await screen.findByText(
      "You have unsaved comments!",
      { exact: false }
    );
    expect(WarningTitleModal).toBeInTheDocument();
    const closeWithoutSaving = await screen.findAllByText(
      "Close without saving",
      { exact: false }
    );

    await user.click(closeWithoutSaving[0]);
  });

  xit("should show warning Modal if event comment is not submitted and SAVE AND CLOSE", async () => {
    render(
      <Provider store={app_store}>
        <EventCommentsSection
          eventId={"6446d376fac61d52c3700e8a"}
          isEventCommentsOpen={true}
          setIsEventCommentsOpen={jest.fn()}
          allCommentsList={mockAllCommentsListShortened}
          isEventCommentsLoading={false}
          parentCommentUpdater={undefined}
          setCommentOpen={jest.fn()}
        />
      </Provider>
    );

    const user = userEvent.setup();
    const newCommentTextArea = await screen.findByPlaceholderText(
      "Please add details",
      { exact: false }
    );
    const myCommentText = "My Comment Length";
    await user.type(newCommentTextArea, myCommentText);
    const CommentsHeader = await screen.findByText("Event Comments");
    await user.click(CommentsHeader);
    const WarningTitleModal = await screen.findByText(
      "You have unsaved comments!",
      { exact: false }
    );
    expect(WarningTitleModal).toBeInTheDocument();
    const saveButton = await screen.findByText("Save and Close", {
      exact: false,
    });
    await user.click(saveButton);
  });

  xit("should handle tab changes for different comment categories", async () => {
    jest.spyOn(MEUPP_UTILS, "getLoggedInUserType").mockReturnValue("MERCHANT");

    render(
      <Provider store={app_store}>
        <EventCommentsSection
          eventId={"6446d376fac61d52c3700e8a"}
          isEventCommentsOpen={true}
          setIsEventCommentsOpen={jest.fn()}
          allCommentsList={mockAllCommentsListShortened}
          isEventCommentsLoading={false}
          parentCommentUpdater={undefined}
          setCommentOpen={jest.fn()}
        />
      </Provider>
    );

    const user = userEvent.setup();
    await user.click(
      await screen.findByText("Billing Inquiry", { exact: false })
    );
    await user.click(await screen.findByText("Internal", { exact: false }));
    await user.click(await screen.findByText("External", { exact: false }));
  });

  xit("should show submit a new comment in the text area", async () => {
    // jest
    // .spyOn(MEUPP_UTILS, "getLoggedInUserType")
    // .mockReturnValue("MERCHANT")
    // render(
    //     <Provider store={app_store}>
    //         <EventCommentsSection
    //         eventId={"6446d376fac61d52c3700e8a"}
    //         isEventCommentsOpen={true}
    //         setIsEventCommentsOpen={jest.fn()}
    //         allCommentsList={mockAllCommentsListShortened}
    //         isEventCommentsLoading={false}
    //         parentCommentUpdater={undefined}
    //         />
    //     </Provider>
    // )
    // const mockAPIResult = [
    //     jest.fn(),
    //     {"status":"uninitialized","isUninitialized":true,"isLoading":false,"isSuccess":false,"isError":false, "data":"My Comment Length"},
    // ];
    // jest
    // .spyOn(COMMENTS_API, "usePostEventCommentsDataMutation")
    // .mockReturnValue(
    //     mockAPIResult
    // )
    // const user = userEvent.setup();
    // const addCommentTextArea = await screen.findByText(EVENT_COMMENTS["ADD_COMMENT_BUTTON"]);
    // const newCommentTextArea = await screen.findByPlaceholderText("Please add details", {exact:false})
    // const myCommentText = "My Comment Length"
    // await user.type(newCommentTextArea, myCommentText);
    // // expect(await screen.findByText(`${myCommentText.length}/300`)).toBeTruthy();
    // // await user.click(addCommentTextArea)
  });
});
