import {
  getAllowanceFormReg<PERSON><PERSON><PERSON>,
  getAllowance<PERSON>ey,
  getAllowance<PERSON>ap<PERSON>ey,
  getOffer<PERSON>ey,
  getUOMFromNameOrValue,
  isHfOrIfType,
} from "../../../../../service/allowance/allowance-service";
import { OFFER_ALLOWANCE } from "../../offer-flow-config";
import { getOfferMapKey } from "../../offer-service";

export const useGetAmountDerivedValues = ({
  sectionKey,
  allowanceTempWorkData,
  allowanceForm,
  cardIndex,
  cardItemIndex,
  isEditEnable,
  isNational
}) => {
  allowanceTempWorkData =
    isNational && Array.isArray(allowanceTempWorkData)
      ? allowanceTempWorkData?.[0]
      : allowanceTempWorkData;
  const allowanceRegField = getAllowanceFormRegisterKey(
    cardIndex,
    cardItemIndex
  );
  const allowanceFormData =
    allowanceForm?.allowanceFormData?.[allowanceRegField];
  const { allowanceType = "", createInd = "" } =
    allowanceFormData?.allowancePrimeData || {};
  const sectionConfiguration = OFFER_ALLOWANCE?.[sectionKey] || {};
  const allowanceTypeKey = getAllowanceKey(allowanceType?.toUpperCase());
  const isHfOrIf = isHfOrIfType(allowanceTypeKey);
  const isHfIfWhseCase =
    isHfOrIf && sectionConfiguration?.offerTypeKey === "WAREHOUSE";

  const mapperKey = getOfferMapKey(
    createInd,
    sectionConfiguration?.offerTypeKey
  );
  const amountsInitialValueOnLoad =
    allowanceFormData?.["allowanceAmountsData"]?.[mapperKey] || {};
  const allowName = getAllowanceMapKey(allowanceTypeKey) || "",
    offerNumber =
      allowanceTempWorkData?.allowanceTypeSpecification?.[allowName]
        ?.offerNumber || allowanceTempWorkData?.offerNumber || "";
  const tempAllowItems =
    allowanceTempWorkData?.allowanceTypeSpecification?.[allowName]
      ?.allowancesMap?.[mapperKey];
  const isAmtSavedInTemp = tempAllowItems?.[0]?.finalizedAmountsInd;
  let updatedAmountsInitialValueOnLoad = {
    ...amountsInitialValueOnLoad,
    ...(!isHfOrIf && !isHfIfWhseCase && amountsInitialValueOnLoad?.uom
      ? {
          uom: amountsInitialValueOnLoad?.uom
            ? getUOMFromNameOrValue({ value: amountsInitialValueOnLoad.uom })
                .name
            : undefined,
        }
      : {}),
  };
  if (!isEditEnable && !isAmtSavedInTemp) {
    updatedAmountsInitialValueOnLoad = {};
  }
  return {
    sectionConfiguration,
    mapperKey,
    allowanceTypeKey,
    isHfOrIf,
    isHfIfWhseCase,
    tempAllowItems,
    offerNumber,
    allowanceRegField,
    allowanceFormData,
    amountsInitialValueOnLoad: updatedAmountsInitialValueOnLoad,
    allowName
  };
};
