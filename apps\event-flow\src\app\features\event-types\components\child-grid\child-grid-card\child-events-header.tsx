import React from "react";
import { childEventConstants } from "../child-events-constants";
import { useSelectorWrap } from "@me/data-rtk";
import { EEVENT_STATUS } from "@me/util-helpers";

function ChildEventsHeader({ selectedTabConfig, cardIndex }) {
  const { key = "", gridDataSliceKey = "" } = selectedTabConfig || {};
  const { data: gridData = [] } = useSelectorWrap(gridDataSliceKey) || {};
  const { data: selectedChildDivIds = [] } = useSelectorWrap(
    "selected_child_events_data"
  );
  const currentEventData = gridData?.[cardIndex]?.[key] || [];
  const { DIVISIONS } = childEventConstants;
  const getValidOffers = currentEventData?.filter(
    e =>
      ![EEVENT_STATUS.CANCELED, EEVENT_STATUS.REJECTED].includes(e?.eventStatus)
  );
  const getValidLength = () =>
    getValidOffers?.length
      ? `${selectedChildDivIds?.length || 0}/${getValidOffers?.length || 0}`
      : "";
  return (
    <div className="flex items-center">
      <span className="font-bold">{`${getValidLength()} ${DIVISIONS}`}</span>
    </div>
  );
}

export default React.memo(ChildEventsHeader);
