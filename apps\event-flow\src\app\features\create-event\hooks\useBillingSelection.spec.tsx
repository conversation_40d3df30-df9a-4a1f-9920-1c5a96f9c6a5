import useBillingSelection from "./useBillingSelection";
import * as utils from "../../all-allowances/allowance-lead-distributors/billing-selection-utils";
import * as selectors from "@me/data-rtk";
import { act, renderHook } from "@testing-library/react";

// jest.mock("@me/data-rtk", () => ({
//   useSelectorWrap: jest.fn(),
// }));

jest.mock("@me/data-rtk", () => ({
  ...jest.requireActual("@me/data-rtk"),
  useSelectorWrap: jest.fn(),
}));

jest.mock(
  "../../all-allowances/allowance-lead-distributors/billing-selection-utils",
  () => ({
    checkIfExternal: jest.fn(),
    getDefaultBillingModalState: jest.fn(),
    tableDataToVendorsArray: jest.fn(),
  })
);
jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
  switch (sel_name) {
    case "allowanceTableData_rn":
      return {
        data: {
          filteredAndSortedIds: ["36200012", "36200011"],
          tableData: [
            {
              rowIndex: 0,
              itemId: "36200011",
              itemDesc: "LUCERNE SOUR CREAM LIGHT",
              primaryUpc: "002113007305",
              caseUpc: "1002113007305",
              itemUpcs: ["002113007305", "1002113007305"],
              effectiveEndDate: "0001-01-01",
              allowanceType: "SCAN",
              packWhse: 12,
              ringType: 0,
              size: "8.0 OZ",
              vendorPackConversionFactor: 1,
              caseListCost: 9.84,
              unitListCost: 0.82,
              unitCostAllow: 0.7617,
              caseCostAllow: 9.14,
              allowanceAmount: 0,
              allowUomType: "EA",
              allowUomTypes: ["EA"],
              newUnitCostAllow: 0.7617,
              newCaseCostAllow: 9.14,
              overlaps: {
                caseAllow: 0.1,
                caseUnitizedAllow: 0.0083,
                shipAllow: 0.6,
                shipUnitizedAllow: 0.05,
                scanAllow: 0,
                scanCaseAllow: 0,
                unitizedAllowanceOverlapsSum: 0.0583,
                caseAllowanceOverlapsSum: 0.7,
              },
              distCenter: "DDNC",
              vendorDetails: [
                {
                  vendorNbr: "",
                  costAreaDesc: "",
                  createAllowInd: true,
                  headerFlatAmt: 0,
                  distCenter: "DDNC",
                  caseListCost: 9.84,
                  unitListCost: 0.82,
                  unitCostAllow: "0.76",
                  allowanceAmount: "",
                  allowUomType: "EA",
                  newUnitCostAllow: 0.7617,
                  caseCostAllow: "9.14",
                  newCaseCostAllow: 9.14,
                  shippingCost: "9.84",
                },
              ],
            },
          ],
        },
      };

    default:
      return null;
  }
});
describe("useBillingSelection", () => {
  const mockVendors = [
    { vendorNbr: "1" },
    { vendorNbr: "2" },
    { vendorNbr: "3" },
  ];

  const mockTableData = [
    { vendorNbr: "1", name: "Vendor 1" },
    { vendorNbr: "2", name: "Vendor 2" },
    { vendorNbr: "3", name: "Vendor 3" },
  ];

  const mockExcludedVendorData = {
    excludedVendors: ["3"],
  };

  beforeEach(() => {
    (utils.tableDataToVendorsArray as jest.Mock).mockReturnValue(mockTableData);
    (utils.checkIfExternal as jest.Mock).mockImplementation(
      vendor => vendor.vendorNbr === "3"
    );
    (utils.getDefaultBillingModalState as jest.Mock).mockReturnValue({});
  });

  xit("should initialize correctly", () => {
    const { result } = renderHook(() =>
      useBillingSelection({ vendors: mockVendors })
    );

    expect(result.current.mainVendors).toEqual(["1", "2"]);
    expect(result.current.mapping).toEqual([
      { id: "1", child: [] },
      { id: "2", child: [] },
    ]);
  });

  xit("should move child vendor down", () => {
    const { result } = renderHook(() =>
      useBillingSelection({ vendors: mockVendors })
    );

    act(() => {
      result.current.moveDown("3");
    });

    expect(result.current.mapping).toEqual([
      { id: "1", child: ["3"] },
      { id: "2", child: [] },
    ]);
  });
});
