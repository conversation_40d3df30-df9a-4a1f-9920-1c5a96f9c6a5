import { memo } from "react";
import {
  dashedVerticalDivider,
  renderField,
} from "../../../../../allowance/stepper/common-stepper/allowance-amount/allowance-amounts-services";
import { IHeaderItemInfoProps } from "../props-types";

const HeaderItemInfo = ({
  itemCount,
  totalAmount,
  wareHouseCount,
  hasItemCount = true,
  isHfIfWhseCase = false,
}: IHeaderItemInfoProps) => (
  <>
    {renderField({
      label: "Item Count",
      value: itemCount,
      prefix: "",
      formatValue: false,
    })}
    {isHfIfWhseCase ? (
      <>
        {dashedVerticalDivider}

        {renderField({
          label: "Warehouse Count",
          value: wareHouseCount,
          prefix: "",
          formatValue: false,
        })}
      </>
    ) : null}
    {hasItemCount ? (
      <>
        {dashedVerticalDivider}

        {renderField({
          label: "Total Allowance Amount",
          value: totalAmount,
        })}
      </>
    ) : null}
  </>
);
export default memo(HeaderItemInfo);
