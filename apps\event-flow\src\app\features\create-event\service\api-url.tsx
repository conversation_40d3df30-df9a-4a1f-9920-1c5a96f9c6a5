export const API_EVENT_URL = {
  // event
  EVENT_API: `/meupp/plan/event`,
  PLAN_EVENT_API: `/meupp/plan-xapi/event`,
  PLAN_POST_EVENT_API: `/meupp/plan-xapi/event/$eventId`,
  GET_DIVISION_API: `/meupp/plan/filter/division`,
  GET_CICS_PPG_ITEMS_API:
    "/meupp/plan-xapi/planeventitems/cics?responseFilter=itemMin",
  PLAN_PPG_RESOLVE_API: "/meupp/plan-xapi/planProductGroups/resolve",
  ITEM_MIN_API:
    "/meupp/plan-xapi/planeventitems/event/URL_PARAM?responseFilter=itemMin",

  EVENT_PERISCOPE: "/meupp/plan/event/periscope/URL_PARAM",

  CHILD_EVENT_DETAILS_API: "/meupp/plan/nationalEvents/URL_PARAM",
  // allowance
  ALLOWANCE_API: `/meupp/allowance/event/allowance`,
  VENDOR_DATA: `/meupp/plan-xapi/event/URL_PARAM/vendorsinfo`,
  ALLOWANCE_TEMP_WORK_API: `/meupp/allowance/event/URL_PARAM/allowancetempwork`,
  ALLOWANCE_EDIT_TEMPWORK_POST:
    "/meupp/allowance/offerallowances/URL_PARAM/allowancetempwork",
  ALLOWANCE_TEMPWORK_DELETE: "/meupp/allowance/allowancetempwork/URL_PARAM",
  ALLOWANCE_UPDATE: "/meupp/allowance/event/URL_PARAM_0/offerallowancespending",
  ALLOWANCE_START_DATE_POST_API: `/meupp/plan-xapi/planeventitems/event/URL_PARAM`,
  OVERLAPPING_ALLOWACE_DATA_GET:
    "/meupp/allowance/event/URL_PARAM_0/allowance/overlaps/URL_PARAM_1",
  ALLOWANCE_ITEMS:
    "/meupp/allowance/event/URL_PARAM_0/allowance/URL_PARAM_1/item/amounts",
  OFFER_ALLOWANCE: `/meupp/allowance/event/URL_PARAM/offerallowances`,
  ALLOWANCE_PUT_REJECT_CANCEL_API: `/meupp/allowance/event/URL_PARAM_0/URL_PARAM_1`,
  ALLOWANCE_DELETE: "/meupp/allowance/event/offerallowances/URL_PARAM",
  ALLOWANCE_DELETE_BY_OFFER: "/meupp/allowance/event/offerallowances/offer/URL_PARAM",
  OFFER_RESEND: "/meupp/allowance/event/offerallowances/resendOffer",
  OFFER_CREATE_UPDTAE:
    "/meupp/allowance/offerallowance/URL_PARAM_0/allowancetype/URL_PARAM_1/allowancetempwork",

  OFFER_AGREEMENT_API: "/meupp/allowance/offer-report",

  // National APIS
  NATIONAL_ALLOWANCE_TEMP_WORK_DELETE: `/meupp/allowance/allowancetempworks/event/URL_PARAM`,
  NATIONAL_ALLOWANCE_TEMP_WORK_API: `/meupp/allowance/event/URL_PARAM/allowancetempworks`,
  NATIONAL_ALLOWANCE_ITEMS: `/meupp/allowance/event/URL_PARAM_0/allowance/URL_PARAM_1/div/item/amounts`,
  NATIONAL_ALLOWANCE_ITEMS_DIFFERENCE: `/meupp/allowance/event/URL_PARAM_0/allowance/URL_PARAM_1/div/item/amounts/differences`,
  NATIONAL_TEMPWORK_DELETE: `/meupp/allowance/allowancetempworks/event/URL_PARAM`,
  NATIONAL_CREATE_OFFER_ALLOWANCE: `/meupp/allowance/event/URL_PARAM/offerallowances/all`,
  NATIONAL_UPDATE_OFFER_ALLOWANCE: `/meupp/allowance/event/URL_PARAM_0/offerallowancespendings`,
  NATIONAL_ALLOWANCE_EDIT_TEMPWORK_POST: `/meupp/allowance/offerallowances/offer/URL_PARAM/allowancetempworks`,

  // promotion
  PROMOTION_API: `/meupp/plan/event/promotion`,
  PROMOTION_POST_API: `/meupp/promotions/event/$eventId/promotion`,
  PROMOTION_PUT_API: `/meupp/promotions/event/promotion`,
  PROMOTION_DELETE_API: `/meupp/promotions/promotion/URL_PARAM`,
  PROMOTION_PRICING_API: `/meupp/plan-xapi/regularPriceCostData/event/URL_PARAM`,
  GET_FORECAST: "/meupp/promotions/forecast/URL_PARAM",
  PROMOTION_PUT_REJECT_CANCEL_API:
    "/meupp/promotions/event/promotion/URL_PARAM_0/status/URL_PARAM_1",
  ALLOWANCE_API_FOR_UOM_IN_PROMO:
    "/meupp/allowance/event/URL_PARAM/allowance/overlaps",
  PROMOTION_ROG_LEVEL_PRICING_API: `/meupp/promotions/promotion/URL_PARAM/pricing/ROG`,
  PROMOTION_ROG_LEVEL_PRICING_POST_API: `/meupp/promotions/promotion/pricing/ROG`,

  // Promo Resend
  PROMO_RESEND: "/meupp/promotions/event/promotion/resend",
  // performance
  PERFORMANCE_API: `/meupp/plan/event/performance`,

  // related info
  RELATED_INFO_API: `/meupp/plan/event/related/info`,

  // comment
  ALL_EVENT_COMMENTS: "/meupp/plan/event/URL_PARAM/comment/all",
  VIEW_PLAN_PROMO_PRODUCT_GROUP: "/meupp/plan-xapi/view/planproductgroup",
  VIEW_PLAN_ITEMS: "/meupp/plan-xapi/planeventitems/event/URL_PARAM/item-data",
  VIEW_STORE_GROUP_STORES: "/meupp/org/view/storegroup",
  ALLOWANCE_PENDING_CANCEL:
    "/meupp/allowance/event/offerallowancespending/URL_PARAM",
  NATIONAL_ALLOWANCE_PENDING_CANCEL: "/meupp/allowance/event/offerallowancespending/offer/URL_PARAM",
  PRODUCT_LOCATION_VALIDATION: `/meupp/plan-xapi/planEventPromoGroup`,
  ALLOWANCE_ITEMS_DIFFERENCE:
    "/meupp/allowance/event/offerallowances/URL_PARAM/item/amounts/differences",
  OVERLAPPING_PROMOTION_DATA_GET: "/meupp/promotions/event/promotion/overlaps",
  OFFER_ATTACHMENT_UPLOAD: "allowance/offer/URL_PARAM/blob/upload",
  OFFER_ATTACHMENT_DOWNLOAD:
    "allowance/offer/URL_PARAM1/blob/download/URL_PARAM2",
  OFFER_ATTACHMENT_DELETE: "allowance/offer/URL_PARAM1/blob/URL_PARAM2",
};
