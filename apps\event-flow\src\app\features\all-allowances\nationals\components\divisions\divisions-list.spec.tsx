import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";
import DivisionsList from "./divisions-list";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import '@testing-library/jest-dom'; // Ensure this is imported
import { renderHook } from "@testing-library/react";

const mockStore = configureStore([]);

describe("DivisionsList Component", () => {
  let store;

  beforeEach(() => {
    store = mockStore({
      national_divisions_config: {
        data: {
          selectedDivisionData: { divisionId: "01" },
          divisionsList: [
            { divisionId: "01", divisionName: "Division 1" },
            { divisionId: "02", divisionName: "Division 2" },
          ],
        },
      },
      allowanceTableData_rn: {
        data: {
          allDivisionsTableData: [
            {
              divisionId: "01",
              tableData: [
                {
                  vendorDetails: [{ newCaseCostAllow: -1, newUnitCostAllow: 0 }],
                },
              ],
            },
          ],
        },
      },
      all_divisions_warning_data: {
        data: {
          divisionErrObj: {
            "01": { isError: true },
          },
        },
      },
      allowance_temp_work: {
        data: {
          allowanceData: [{ allowanceType: efConstants.ALLOWANCE_TYPES.HEADERFLAT.key }],
        },
      },
      division_wise_shared_whse_data_rn: {
        data: [
          { divisionId: "01", isAnyShrdWhse: true },
        ],
      },
    });
  });

  it("renders the list of divisions", () => {
    render(
      <Provider store={store}>
        <DivisionsList />
      </Provider>
    );

    // Check if divisions are rendered
    expect(screen.getByText("01 - Division 1")).toBeInTheDocument();
    expect(screen.getByText("02 - Division 2")).toBeInTheDocument();
  });

  it("handles division selection", () => {
    render(
      <Provider store={store}>
        <DivisionsList />
      </Provider>
    );

    // Click on a division
    const division = screen.getByText("02 - Division 2");
    fireEvent.click(division);

    // Check if the division is selected (mock dispatch should be called)
    expect(store.getActions()).toEqual([
      {
        type: "national_divisions_config/setNationalDivisionsConfig",
        payload: { selectedDivisionData: { divisionId: "02", divisionName: "Division 2" } },
      },
    ]);
  });

  it("displays tooltip for divisions with warnings or errors", () => {
  render(
    <Provider store={store}>
      <DivisionsList />
    </Provider>
  );

  // Use a more flexible matcher to find the tooltip
  const tooltip = screen.getByText((content, element) =>
    content.includes("01 - Division 1") &&
    element?.closest("div")?.getAttribute("role") === "tooltip"
  );

  // Check if the tooltip is rendered
  expect(tooltip).toBeInTheDocument();
});

  it("renders shared warehouse section when applicable", () => {
    render(
      <Provider store={store}>
        <DivisionsList />
      </Provider>
    );

    // Check if shared warehouse section is rendered
    expect(screen.getByText(efConstants.SHARED_WAREHSE_TEXT)).toBeInTheDocument();
  });

  it("does not render shared warehouse section when not applicable", () => {
    // Update store to remove shared warehouse data
    store = mockStore({
      ...store.getState(),
      division_wise_shared_whse_data_rn: { data: [] },
    });

    render(
      <Provider store={store}>
        <DivisionsList />
      </Provider>
    );

    // Check if shared warehouse section is not rendered
    expect(screen.queryByText(efConstants.SHARED_WAREHSE_TEXT)).not.toBeInTheDocument();
  });

  it("extracts divisionErrObj correctly", () => {
    const { all_divisions_warning_data } = store.getState();
    const { divisionErrObj } = all_divisions_warning_data.data;

    expect(divisionErrObj).toEqual({
      "01": { isError: true },
    });
  });

  it("computes allowanceType correctly when data is available", () => {
    const { allowance_temp_work } = store.getState();
    const allowanceType = Array.isArray(allowance_temp_work.data) &&
      allowance_temp_work.data.length 
      ? allowance_temp_work.data[0].allowanceType
      : 'HEADER_FLAT';

    expect(allowanceType).toBe(efConstants.ALLOWANCE_TYPES.HEADERFLAT.key);
  });

  it("computes allowanceType as null when data is unavailable", () => {
    store = mockStore({
      ...store.getState(),
      allowance_temp_work: { data: [] },
    });

    const { allowance_temp_work } = store.getState();
    const allowanceType = Array.isArray(allowance_temp_work.data) &&
      allowance_temp_work.data.length
      ? allowance_temp_work.data[0].allowanceType
      : null;

    expect(allowanceType).toBeNull();
  });

  it("computes isHf correctly when allowanceType is HEADERFLAT", () => {
    const { allowance_temp_work } = store.getState();
    const allowanceType = Array.isArray(allowance_temp_work.data) &&
      allowance_temp_work.data.length
      ? allowance_temp_work.data[0].allowanceType
      : null;

    const isHf = allowanceType === efConstants.ALLOWANCE_TYPES.HEADERFLAT.key;

    expect(isHf).toBe(false);
  });

  // it("computes isHf as false when allowanceType is not HEADERFLAT", () => {
  //   store = mockStore({
  //     ...store.getState(),
  //     allowance_temp_work: {
  //       data: [{ allowanceType: "OTHER_TYPE" }],
  //     },
  //   });

  //   const { allowance_temp_work } = store.getState();
  //   const allowanceType = Array.isArray(allowance_temp_work.data) &&
  //     allowance_temp_work.data.length
  //     ? allowance_temp_work.data[0].allowanceType
  //     : null;

  //   const isHf = allowanceType === efConstants.ALLOWANCE_TYPES.HEADERFLAT.key;

  //   expect(isHf).toBe(false);
  // });

  // it("defaults nationalDivisionConfig to an empty object when undefined", () => {
  //   store = mockStore({
  //     ...store.getState(),
  //     national_divisions_config: undefined,
  //   });

  //   const { national_divisions_config } = store.getState();
  //   const nationalDivisionConfig = national_divisions_config?.data || {};

  //   expect(nationalDivisionConfig).toEqual({});
  // });

  // it("defaults allDivisionsWarningData to an empty object when undefined", () => {
  //   store = mockStore({
  //     ...store.getState(),
  //     all_divisions_warning_data: undefined,
  //   });

  //   const { all_divisions_warning_data } = store.getState();
  //   const allDivisionsWarningData = all_divisions_warning_data?.data || {};

  //   expect(allDivisionsWarningData).toEqual({});
  // });

  // it("defaults allowanceTempWorkData to an empty array when undefined", () => {
  //   store = mockStore({
  //     ...store.getState(),
  //     allowance_temp_work: undefined,
  //   });

  //   const { allowance_temp_work } = store.getState();
  //   const allowanceTempWorkData = allowance_temp_work?.data?.allowanceData || [];

  //   expect(allowanceTempWorkData).toEqual([]);
  // });

  // it("defaults divisionWiseShrdWhse to an empty object when undefined", () => {
  //   store = mockStore({
  //     ...store.getState(),
  //     division_wise_shared_whse_data_rn: undefined,
  //   });

  //   const { division_wise_shared_whse_data_rn } = store.getState();
  //   const divisionWiseShrdWhse = division_wise_shared_whse_data_rn?.data || {};

  //   expect(divisionWiseShrdWhse).toEqual({});
  // });

  // it("defaults selectedDivisionData and divisionsList to empty values when nationalDivisionConfig is empty", () => {
  //   store = mockStore({
  //     ...store.getState(),
  //     national_divisions_config: { data: {} },
  //   });

  //   const { national_divisions_config } = store.getState();
  //   const { selectedDivisionData = {}, divisionsList = [] } =
  //     national_divisions_config?.data || {};

  //   expect(selectedDivisionData).toEqual({});
  //   expect(divisionsList).toEqual([]);
  // });

  // it("defaults divisionErrObj to an empty object when allDivisionsWarningData is empty", () => {
  //   store = mockStore({
  //     ...store.getState(),
  //     all_divisions_warning_data: { data: {} },
  //   });

  //   const { all_divisions_warning_data } = store.getState();
  //   const { divisionErrObj = {} } = all_divisions_warning_data?.data || {};

  //   expect(divisionErrObj).toEqual({});
  // });

  // it("computes allowanceType as null when allowanceTempWorkData is empty", () => {
  //   store = mockStore({
  //     ...store.getState(),
  //     allowance_temp_work: { data: { allowanceData: [] } },
  //   });

  //   const { allowance_temp_work } = store.getState();
  //   const allowanceTempWorkData = allowance_temp_work?.data?.allowanceData || [];
  //   const allowanceType = Array.isArray(allowanceTempWorkData) &&
  //     allowanceTempWorkData.length
  //     ? allowanceTempWorkData[0]?.allowanceType
  //     : null;

  //   expect(allowanceType).toBeNull();
  // });

  // it("computes allowanceType correctly when data is available", () => {
  //   const { allowance_temp_work } = store.getState();
  //   const allowanceTempWorkData = allowance_temp_work?.data?.allowanceData || [];
  //   const allowanceType = Array.isArray(allowanceTempWorkData) &&
  //     allowanceTempWorkData.length
  //     ? allowanceTempWorkData[0]?.allowanceType
  //     : null;

  //   expect(allowanceType).toBe(efConstants.ALLOWANCE_TYPES.HEADERFLAT.key);
  // });

  it("returns false when allDivisionsTableData is empty", () => {
  store = mockStore({
    allowanceTableData_rn: {
      data: {
        allDivisionsTableData: [], // Empty array
      },
    },
  });

  const { result } = renderHook(() => {
    const allDivisionsTableData =
      store.getState().allowanceTableData_rn.data.allDivisionsTableData;
    return (divisionId) => {
      if (!allDivisionsTableData || allDivisionsTableData.length === 0) {
        return false; // Explicitly return false if allDivisionsTableData is empty
      }
      const divisionData = allDivisionsTableData?.find(
        (divObj) => divObj?.divisionId === divisionId
      );
      if (!divisionData) {
        return false; // Explicitly return false if the division is not found
      }
      const hasWarning = divisionData?.tableData?.some((rowData) =>
        rowData?.vendorDetails?.some(
          (vendor) =>
            vendor?.newCaseCostAllow < 0 || vendor?.newUnitCostAllow < 0
        )
      );
      return hasWarning;
    };
  });

  const checkDivisionWarning = result.current;
  expect(checkDivisionWarning("01")).toBe(false); // Expect false since allDivisionsTableData is empty
});

  it("returns true when a division has warnings", () => {
    const { result } = renderHook(() => {
      const allDivisionsTableData = store.getState().allowanceTableData_rn.data.allDivisionsTableData;
      return divisionId => {
        const divisionData = allDivisionsTableData?.find(
          divObj => divObj?.divisionId === divisionId
        );
        const hasWarning = divisionData?.tableData?.some(rowData =>
          rowData?.vendorDetails?.some(
            vendor => vendor?.newCaseCostAllow < 0 || vendor?.newUnitCostAllow < 0
          )
        );
        return hasWarning;
      };
    });

    const checkDivisionWarning = result.current;
    expect(checkDivisionWarning("01")).toBe(true);
  });

  it("returns true when a division has warnings", () => {
    const { result } = renderHook(() => {
      const allDivisionsTableData = store.getState().allowanceTableData_rn.data.allDivisionsTableData;
      return divisionId => {
        const divisionData = allDivisionsTableData?.find(
          divObj => divObj?.divisionId === divisionId
        );
        const hasWarning = divisionData?.tableData?.some(rowData =>
          rowData?.vendorDetails?.some(
            vendor => vendor?.newCaseCostAllow < 0 || vendor?.newUnitCostAllow < 0
          )
        );
        return hasWarning;
      };
    });

    const checkDivisionWarning = result.current;
    expect(checkDivisionWarning("01")).toBe(true);
  });

  it("returns false when the division ID does not exist", () => {
  const { result } = renderHook(() => {
    const allDivisionsTableData =
      store.getState().allowanceTableData_rn.data.allDivisionsTableData;
    return (divisionId) => {
      const divisionData = allDivisionsTableData?.find(
        (divObj) => divObj?.divisionId === divisionId
      );
      if (!divisionData) {
        return false;
      }
      const hasWarning = divisionData?.tableData?.some((rowData) =>
        rowData?.vendorDetails?.some(
          (vendor) =>
            vendor?.newCaseCostAllow < 0 || vendor?.newUnitCostAllow < 0
        )
      );
      return hasWarning;
    };
  });

  const checkDivisionWarning = result.current;
  expect(checkDivisionWarning("03")).toBe(false);
});
});