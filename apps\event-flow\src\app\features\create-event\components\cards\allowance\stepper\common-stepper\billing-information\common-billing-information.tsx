import Button from "@albertsons/uds/molecule/Button";
import { useSelectorWrap } from "@me/data-rtk";
import { InputTextAtom } from "../../../../../fields";
import { RenderStates } from "@me/ui-render-states";
import { EEVENT_STATUS, editFieldHighlight } from "@me/util-helpers";
import LoadingSpinner from "../../../../../../../create-event/constants/LoadingSpinner/LoadingSpinner";
import efConstants from "../../../../../../../../shared/ef-constants/ef-constants";
import { isEmpty } from "lodash";
import { useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import useAllowanceTempUpdate from "../../../../../../hooks/useAllowanceTempUpdate";
import {
  checkIsPaymentTypeDeduct,
  getAllowance<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  getAllow<PERSON><PERSON><PERSON><PERSON><PERSON>,
  get<PERSON><PERSON><PERSON><PERSON>,
  removeP<PERSON><PERSON>,
  saveAllowanceFormData,
} from "../../../../../../service/allowance/allowance-service";
import {
  useDeleteAllowanceTempWorkDataMutation,
  usePostOfferAllowanceMutation,
  usePutOfferAllowanceMutation,
} from "../../../../../../service/apis/allowance-api";
import {
  allowanceNewCardConfiguration,
  allowanceOfferDetailsConfiguration,
  offerCardConfiguration,
  resetAllowanceStepSkipInfo,
  setAllowTypeChange,
  setAllowanceFormInfo,
} from "../../../../../../service/slice/allowance-details-slice";
import { allowanceTempWorkReset } from "../../../../../../service/slice/allowance-temp-work-slice";
import { setAllowancesResponse } from "../../../../../../service/slice/allowances-response-slice";
import AllowanceFormWrapper, {
  IFormControls,
} from "../../../../common/allowance-form-wrapper";
import BillingCommentAllSection from "./billing-comment-all-section";
import BillingInfoForAllowance, {
  displayData,
  getRegKeyForBilling,
} from "./billing-info-allowance";
import { IBillingInformationProps } from "./billing-information.model";
import _ from "lodash";
import classNames from "classnames";
import { appConstants } from "@me/utils-root-props";
import { isAllowanceFeatureEnabled } from "@me-upp-js/utilities";
import { VENDOR_COMMENT_CHAR_LIMIT } from "../../../../../../constants/fields/allowance/field-billing-information";

export default function CommonBillingInformation({
  saveAndContinueHanlder,
  stepperElement,
  offerIndex = 0,
  allowanceIndex = 0,
  step,
  isLastStep,
  isEditEnable,
}: IBillingInformationProps) {
  const {
    fields: {
      allowanceSpecificFields,
      billingInformationData,
      suggestedVendorPaymentType,
      acApOrArNumber,
      vendorComment,
      vendorOfferTrackingNbr,
      commonComment,
    },
  } = stepperElement;
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { data } = useSelectorWrap("allowance_form_data");
  const dispatch = useDispatch();
  const { allowanceData } = useSelectorWrap("allowance_temp_work").data;
  const { editCardConfig = {}, openCardConfig = {} } =
    useSelectorWrap("offer_card_configutation_rn").data || {};
  const { isAllowanceTypeChanged } =
    useSelectorWrap("allow_type_change_rn")?.data || {};

  const allowanceRegField = getAllowanceFormRegisterKey(
    offerIndex,
    allowanceIndex
  );

  const { addAllowancesBillingInformation, isLoading: isSaveBillingLoading } =
    useAllowanceTempUpdate();

  const allowanceFormData = data?.allowanceFormData?.[allowanceRegField],
    { allowanceType, createInd, performance } =
      allowanceFormData?.allowanceTypeAndPerformance || {},
    billingInfoRegFieldsData =
      allowanceSpecificFields?.[allowanceType]?.[
        getOfferKey(stepperElement?.offerTypeKey, allowanceType, createInd)
      ] || allowanceSpecificFields?.DEFAULT,
    { key } = billingInfoRegFieldsData,
    billingRegFieldKey = billingInformationData?.registerKeyName,
    billingInitialValueOnFormLoad =
      allowanceFormData?.[billingRegFieldKey]?.[key],
    billingInitialValueOnLoad =
      allowanceData?.allowanceTypeSpecification?.[
        getAllowanceMapKey(allowanceType) || ""
      ]?.allowancesMap?.[key],
    searchId = eventDetailsData?.offerAllowances?.[offerIndex]?.offerNumber,
    eventStatus = eventDetailsData?.eventStatus,
    allowanceStatus =
      eventDetailsData?.offerAllowances?.[offerIndex]?.allowances[
        allowanceIndex
      ]?.allowanceStatus;

  const [putOfferAllowance, { isLoading: isAllowancePutLoading }] =
    usePutOfferAllowanceMutation();
  const [postOfferAllowance, { isLoading: isAllowancePostLoading }] =
    usePostOfferAllowanceMutation();

  const isCancelled = useRef(false);
  const [
    deleteAllowanceTempWorkData,
    { isLoading: isDeleteAllowanceTempDataloading, isError: isDeleteError },
  ] = useDeleteAllowanceTempWorkDataMutation();

  const isCommentRequired = performance === "Other (99)";

  const [formControls, setFormControls] = useState<IFormControls>();

  const [errors, setErrors] = useState<any>([]);
  const [vendorTrackingNumberChanged, setVendorTrackingNumberChanged] =
    useState(false);
  const defaultTrackNumber = useMemo(() => {
    let trackingNumber = "";

    billingInitialValueOnFormLoad?.allowanceBillingInfo?.forEach(item => {
      if (item?.vendorOfferTrackingNbr) {
        trackingNumber = item?.vendorOfferTrackingNbr;
      }
    });

    return trackingNumber;
  }, [billingInitialValueOnFormLoad]);

  const {
    register = () => null,
    control,
    setValue = () => null,
    getValues = (key: string) => null,
    setError = () => null,
    clearErrors = () => null,
  } = formControls || {};

  const getEventData = () => {
    return eventDetailsData;
  };

  const getHighletedClassName = (fieldProps, fieldChanged: boolean) => {
    return isEditEnable && fieldChanged
      ? "border-2 border-blue-500 "
      : editFieldHighlight(
          fieldProps.mapperKey,
          getEventData,
          searchId,
          "allowance"
        );
  };

  const handleCancel = () => {
    isCancelled.current = true;
    allowanceData?.tempWorkAllowanceId &&
      deleteAllowanceTempWorkData({
        URL_PARAM: allowanceData?.tempWorkAllowanceId,
      });
    if (eventDetailsData?.inValidAllowances) {
      dispatch(
        offerCardConfiguration({
          ...editCardConfig,
          ...openCardConfig,
          offerData: `${
            eventDetailsData?.offerAllowances?.[offerIndex]?.id
          }_${_.uniqueId()}`,
        })
      );
    }
    changeStoreDataAfterUpdate(null);
  };

  const cancelButton = isEditEnable && (
    <Button onClick={handleCancel} width={140} variant="secondary">
      Cancel
    </Button>
  );

  const getSubmitBtnLabel = () => {
    if (isEditEnable && isLastStep) return efConstants.UPDATE_ALLOWANCE;
    else if (!isEditEnable && isLastStep) return efConstants.CREATE_ALLOWANCE;
    return stepperElement.create.label;
  };

  const getOfferStatus = () => {
    if (eventStatus === EEVENT_STATUS.CANCELED) return true;
    else if (isEditEnable && allowanceStatus === EEVENT_STATUS.CANCELED)
      return true;
    return false;
  };

  const getFormControls = (controls: IFormControls) => {
    setFormControls(controls);
  };

  const changeStoreDataAfterUpdate = data => {
    dispatch(allowanceTempWorkReset());
    dispatch(
      setAllowanceFormInfo({
        allowanceFormData: {
          [allowanceRegField]: {},
        },
      })
    );
    dispatch(
      offerCardConfiguration({
        editCardConfig: {},
        openCardConfig: {},
      })
    );
    dispatch(resetAllowanceStepSkipInfo());
    data && dispatch(setAllowancesResponse(data));
  };

  const handleUpdateOrCreateOffer = async (data: object = {}) => {
    data = !isEditEnable
      ? data
      : {
          ...data,
          offerNumber: searchId,
          allowanceChangeStatus: allowanceStatus,
        };
    if (isEditEnable && !isAllowanceTypeChanged?.[offerIndex]) {
      const putOfferData = await putOfferAllowance({
        URL_PARAMS: [searchId],
        ...data,
      });
      putOfferData?.data && changeStoreDataAfterUpdate(putOfferData?.data);
    } else {
      const postOfferData = await postOfferAllowance(data);
      postOfferData?.data && handleDataAfterCreate(postOfferData?.data);
    }
    //remove temp work id from session storage after update or save
    sessionStorage.removeItem("TEMPWORK_ID");
    sessionStorage.removeItem("OFFER_ID");
  };

  const handleDataAfterCreate = postOfferData => {
    dispatch(
      allowanceNewCardConfiguration({
        isNewAllowance: true,
        stepperType: "offerAllowances",
        isAddAnotherOffer: false,
      })
    );
    if (isAllowanceFeatureEnabled) {
      dispatch(
        setAllowTypeChange({
          isAllowanceTypeChanged: {},
        })
      );
    }
    changeStoreDataAfterUpdate(postOfferData);
    dispatch(allowanceOfferDetailsConfiguration(postOfferData));
  };

  const handleSave = async formValues => {
    if (!isCancelled.current) {
      const { ...restFormValues } = formValues;
      // TODO: earlier it was using the allowanceFormData but object is empty there
      let allowances = billingInitialValueOnLoad;

      let invalidFormData = false;
      const newErrors: any[] = [];

      allowances = allowances?.map((allowance, index) => {
        const formAllow = restFormValues?.allowanceBillingInfo?.[index] || {};
        const isDeduction = formAllow?.suggestedVendorPaymentType === "Deduct";

        formAllow.suggestedVendorPaymentType =
          formAllow?.suggestedVendorPaymentType === "Select"
            ? ""
            : formAllow?.suggestedVendorPaymentType;

        const error: any = {};

        if (isCommentRequired && !formAllow?.vendorComment) {
          error.vendorComment = "Vendor Comment is required";
        }

        if (
          Boolean(formAllow?.suggestedVendorPaymentType) !==
          Boolean(formAllow?.acApOrArNumber)
        ) {
          if (formAllow?.suggestedVendorPaymentType) {
            error.acApOrArNumber = "Suggested A/P or A/R Number is required";
          } else {
            error.suggestedVendorPaymentType =
              "Suggested Payment Type is required";
          }
        }

        invalidFormData = invalidFormData || !isEmpty(error);
        newErrors.push(error);

        return {
          ...allowance,
          allowanceBillingInfo: {
            ...allowance?.allowanceBillingInfo,
            absVendorPaymentType: checkIsPaymentTypeDeduct(
              performance,
              allowance
            )
              ? "Deduct"
              : "Invoice",
            suggestedVendorPaymentType:
              formAllow?.suggestedVendorPaymentType || "",
            vendorComment: formAllow?.vendorComment || "",
            vendorOfferTrackingNbr: formAllow?.vendorOfferTrackingNbr || "",
            suggestedAcPayableVendorNbr:
              (isDeduction ? formAllow?.acApOrArNumber : "") || "",
            suggestedAcReceivableVendorNbr:
              (!isDeduction ? formAllow?.acApOrArNumber : "") || "",
          },
        };
      });

      setErrors(newErrors);
      if (invalidFormData) return;

      if (isLastStep) {
        let result = await addAllowancesBillingInformation({
          allowances,
          createAllowInd: false,
          offerAllowanceGroup: key,
          isLastStep,
        });
        setValue("isAllowanceChanged", false);
        if (isAllowanceTypeChanged?.[offerIndex]) {
          result = {
            ...result,
            offerNumber: allowanceData?.offerNumber,
            overrideOfferNumbers: [allowanceData?.offerNumber],
          };
        }
        await handleUpdateOrCreateOffer(result);
        removeParams();
        localStorage.removeItem(
          `${appConstants.RESTRICT_NAV_STORAGE_NAME}${sessionStorage.getItem(
            appConstants.SESSION_STORAGE_RESTRICTED_APP_KEY_NAME
          )}`
        );
      } else {
        const result = await addAllowancesBillingInformation({
          allowances,
          createAllowInd: false,
          offerAllowanceGroup: key,
          isLastStep,
        });
        if (result?.data) {
          saveAllowanceFormData({
            dispatch,
            key: billingRegFieldKey,
            allowanceRegField,
            value: {
              ...allowanceFormData?.[billingRegFieldKey],
              [key]: {
                ...restFormValues,
                //  changedFields.length !== 0,
              },
            },
            isPreviousDataRequired: true,
            previousData: allowanceFormData,
          });
          saveAndContinueHanlder(step);
        }
      }
      analyticsLinkClickEvent();
    }
  };

  const analyticsLinkClickEvent = () => {
    if (
      window["AB"] &&
      window["AB"]["DATALAYER"] &&
      typeof window["AB"]["DATALAYER"].setLinkClickEvents === "function" &&
      getSubmitBtnLabel() === efConstants.CREATE_ALLOWANCE
    ) {
      window["AB"]["DATALAYER"].setLinkClickEvents("promotions:create-offer");
    }
  };

  function onUpdateAllCommentSubmit(e) {
    if (
      getValues(commonComment.registerField)?.trimStart().length >
      VENDOR_COMMENT_CHAR_LIMIT
    ) {
      setError(commonComment.registerField, {
        message: `Maximum ${VENDOR_COMMENT_CHAR_LIMIT} characters allowed`,
      });
      return;
    } else {
      clearErrors(commonComment.registerField);
    }

    billingInitialValueOnLoad?.forEach((element, index) => {
      if (billingInitialValueOnLoad?.[index]?.includeInd) {
        setValue(
          getRegKeyForBilling(vendorComment, index),
          getValues(commonComment.registerField)?.trimStart()
        );
      }
    });
  }

  function onPaymentTypeChange(e, allowanceIndex: number) {
    updateAllRelatedAllowances(allowanceIndex, index => {
      setValue(getRegKeyForBilling(suggestedVendorPaymentType, index), e.id);
    });
  }

  function onApArNumberChange(e, allowanceIndex: number) {
    updateAllRelatedAllowances(allowanceIndex, index => {
      setValue(getRegKeyForBilling(acApOrArNumber, index), e);
    });
  }

  function onVendorCommentChange(e, allowanceIndex: number) {
    updateAllRelatedAllowances(allowanceIndex, index => {
      setValue(getRegKeyForBilling(vendorComment, index), e?.trimStart());
    });
  }

  function onVendorTrackingNumberChange(e) {
    const value = e?.trim();
    billingInitialValueOnLoad?.forEach((element, index) => {
      if (billingInitialValueOnLoad?.[index]?.includeInd) {
        setValue(getRegKeyForBilling(vendorOfferTrackingNbr, index), value);
      }
    });
    setVendorTrackingNumberChanged(true);
    setValue(vendorOfferTrackingNbr.registerField, value);
  }

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  function updateAllRelatedAllowances(index, callBackFunction = i => {}) {
    callBackFunction(index);

    const allowance = billingInitialValueOnLoad?.[index];

    if (efConstants.LEAD_DIST_ONLY !== allowance?.leadDistributorMode) return;

    allowance?.leadDistributorInfos?.forEach((element, index) => {
      // TODO check if vendorNbr is only option
      const subAllowIndex = billingInitialValueOnLoad?.findIndex(
        allow => element?.vendorNbr === allow?.vendorNbr
      );
      if (billingInitialValueOnLoad?.[subAllowIndex]?.includeInd) {
        callBackFunction(subAllowIndex);
      }
    });
  }

  function getErrorForVendorTrackingNumber() {
    let errorMsg = "";

    for (let i = 0; i < errors.length; ++i) {
      const item = errors[i];
      if (item?.vendorOfferTrackingNbr) {
        errorMsg = item?.vendorOfferTrackingNbr;
        break;
      }
    }
    return errorMsg;
  }

  const uniqueVendorNames = billingInitialValueOnLoad?.filter((obj, index) => {
    return (
      index ===
      billingInitialValueOnLoad?.findIndex(
        o =>
          obj?.allowanceBillingInfo?.absMerchVendor ===
          o?.allowanceBillingInfo?.absMerchVendor
      )
    );
  });

  const billingContent = (
    <div
      className="flex flex-col gap-4"
      id="abs-common-billing-information-text-container"
    >
      <p id="abs-common-billing-information-text1">
        For reference, the items in this Offer are setup under the following
        Merchandising System Vendors:
      </p>

      {uniqueVendorNames?.map(vendor => {
        return (
          <div
            className="flex gap-4"
            id="abs-common-billing-information-merch-vendor"
          >
            {displayData(
              "ABS Merch Vendor",
              vendor?.allowanceBillingInfo?.absMerchVendor,
              true
            )}
            {displayData(
              "ABS Vendor Name",
              vendor?.allowanceBillingInfo?.absVendorName
            )}
          </div>
        );
      })}

      <p id="abs-common-billing-information-merch-vendor-text">
        Vendor may enter any Comments that they wish to have displayed on
        Allowance Agreements or any other information they deem needed. Comments
        entered in this box will be for the entire Offer and will be propagated
        onto each Allowance within it below. If you wish to have different
        Comments on each Allowance enter them on each one and do not use this
        entry box.
      </p>

      <BillingCommentAllSection
        formControls={formControls}
        commonComment={commonComment}
        onUpdateAllCommentSubmit={onUpdateAllCommentSubmit}
        isCommentRequired={isCommentRequired}
      />

      <p id="abs-common-billing-information-merch-vendor-text1">
        Suggested Billing Information - use ONLY if you think that Default
        Billing is incorrect or if you have an unusual one-time circumstance
        where you would like to request to be Billed differently. Your
        suggestion will be visible to the Albertsons Allowance Billing
        department.
      </p>

      {billingInitialValueOnLoad?.map((allowance, index) => {
        if (!allowance?.includeInd) return null;

        if (
          efConstants.LEAD_DIST_ONLY === allowance?.leadDistributorMode &&
          !allowance?.leadDistributorInd
        )
          return null;

        return (
          <BillingInfoForAllowance
            control={control}
            register={register}
            stepperElement={stepperElement}
            allowance={allowance}
            allowancesResp={billingInitialValueOnLoad}
            index={index}
            onPaymentTypeChange={onPaymentTypeChange}
            onApArNumberChange={onApArNumberChange}
            onVendorCommentChange={onVendorCommentChange}
            isCommentRequired={isCommentRequired}
            getHighletedClassName={getHighletedClassName}
            error={errors[index]}
            getValues={getValues}
            keyValue={key}
            allowanceType={allowanceType}
            isEditEnable={isEditEnable}
            performance={performance}
            key={`${allowanceType} - ${index}`}
          />
        );
      })}

      <p id="abs-common-billing-information-optional-text">
        Optional - if Vendor wishes to enter a Tracking Number to be displayed
        on all Allowances for the Offer enter it here
      </p>

      <div
        className="flex gap-3 items-center"
        id="abs-common-billing-information-vendor-tracking-sec"
      >
        <div
          className="shrink-0 flex font-bold"
          id="abs-common-billing-information-vendor-tracking-sec2"
        >
          Vendor Tracking Number
        </div>
        <InputTextAtom
          control={control}
          register={register}
          fieldProps={vendorOfferTrackingNbr}
          onChange={onVendorTrackingNumberChange}
          className={classNames(
            getHighletedClassName(
              vendorOfferTrackingNbr,
              vendorTrackingNumberChanged
            ),
            "w-[300px]"
          )}
          error={{
            message: getErrorForVendorTrackingNumber(),
          }}
        />
      </div>
    </div>
  );

  const renderHtml = (
    <AllowanceFormWrapper
      defaultValues={{
        ...billingInitialValueOnFormLoad,
        vendorOfferTrackingNbr: defaultTrackNumber,
      }}
      handleSave={handleSave}
      getFormControls={getFormControls}
      footerProps={{
        label: getSubmitBtnLabel(),
        className: "flex gap-2 p-3 pl-[0px]",
        FooterChildren: cancelButton,
        style: {
          width:
            getSubmitBtnLabel() === efConstants.UPDATE_ALLOWANCE ? 180 : 240,
        },
        disabled: getOfferStatus(),
      }}
    >
      <>
        <LoadingSpinner
          isLoading={
            isDeleteAllowanceTempDataloading ||
            isAllowancePostLoading ||
            isAllowancePutLoading
          }
          classname="!h-full !w-full rounded-md"
        />
        {control && billingContent}
      </>
    </AllowanceFormWrapper>
  );

  const renderDetails = {
    isApiLoading: false,
    isPageLevelSpinner: true,
    isRenderMainHtml: true,
    renderHtml,
  };

  return <RenderStates details={renderDetails} />;
}
