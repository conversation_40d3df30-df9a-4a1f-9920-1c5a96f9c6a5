import { warningIconMaroon } from "@me-upp-js/utilities";
import React, { memo } from "react";

function MultiCostMessage() {
  return (
    <div
      className="flex mt-4 w-full text-[#9D2210] font-bold text-sm gap-3"
      id="abs-basic-allowance-amount-render-bottom-section-warning-text"
      data-testid="multi-cost-message-container"
    >
      <span>{warningIconMaroon}</span>
      <span>
        Multiple costs associated with the items. Click 'Edit/View All Items' to
        enter allowance amounts.
      </span>
    </div>
  );
}

export default memo(MultiCostMessage);
