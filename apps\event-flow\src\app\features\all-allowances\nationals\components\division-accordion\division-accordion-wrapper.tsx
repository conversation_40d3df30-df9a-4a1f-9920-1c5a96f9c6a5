import React, { useState } from "react";
import Accordion<PERSON>tom from "./accordion-atom";
import VendorSelections from "../lead-distributor/vendor-selection";
import Button from "@albertsons/uds/molecule/Button";
import { useSelectorWrap } from "@me/data-rtk";
import "./division-accordion-wrapper.scss";
import BillingVendorSelection from "../billing-section/billing-vendor-selection";

function DivisionAccordion({ isBilling = false }) {
  const { divisionsList = [] } =
    useSelectorWrap("national_divisions_config")?.data || {};
  const { data: leadDistData = {} } =
    useSelectorWrap("leadDistributors_rn") || {};
  const [expandAll, setExpandAll] = useState(true);
  const {
    allDivisionLeadOptions = {},
  } = leadDistData;
  const handleExpandAll = () => setExpandAll(!expandAll);
  const VendorSelectionComp = isBilling
    ? BillingVendorSelection
    : VendorSelections;

  const renderDivisionItem = division => {
    if (!division?.divisionId || !allDivisionLeadOptions?.[division?.divisionId]) return null; // Ensuring divisionId exists before rendering

    return (
      <AccordionAtom
        key={division?.divisionId}
        division={division}
        isExpandAll={expandAll}
        // setExpandAll={setExpandAll}
      >
        <VendorSelectionComp currentDivision={division} isNdpType={true} />
      </AccordionAtom>
    );
  };

  return (
    <section id="abs-national-division-accordion-wrapper" className="mx-6 division-accordion-wrapper">
      <div className="border-t mb-5 mt-5 pt-4 flex items-center">
        <div className="mr-2">Edit Division Info</div>
        <Button
          width={120}
          size="md"
          className="mr-2 whitespace-nowrap"
          variant="secondary"
          onClick={handleExpandAll}
        >
          {expandAll ? "Collapse All" : "Expand All"}
        </Button>
      </div>

      {Array.isArray(divisionsList) && divisionsList?.length > 0 ? (
        divisionsList?.map(renderDivisionItem)
      ) : (
        <p className="text-gray-500">No divisions available.</p>
      )}
    </section>
  );
}

export default React.memo(DivisionAccordion);
