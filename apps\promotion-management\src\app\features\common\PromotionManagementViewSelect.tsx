import { isEmpty } from "lodash";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  ALLOWVIEW_DRAGGABLE_OPTIONS_MAPPER,
  DASHBOARD_MAPPING_ITEMS,
  DASHBOARD_VIEW_KEY_MAPPER,
  DASHBOARD_SLICE_CONFIG,
  SEARCH_FILTER_OPTIONS,
  getOptionsForView,
  OFFER_ID,
} from "../../config/dashboard-config";
import {
  setAllowancesColumnsData,
  setAllowancesViewDefalutColumnsConfig,
  setDashboardConfigValue,
  setDsdFundingFilterData,
  setSelectedTab,
} from "../../library/data-access/dashboard";
import { useGetSelectedDashboardTab, usePageRefresh } from "../../shared/hooks";
import pmConstants from "../../shared/pm-constants/pm-constants";
import { SkeletonLoader } from "../../shared/ui/atoms";
import DataGrid from "../dashboard/data-grid";
import {
  getDefaultTab,
  getFromSessionStorage,
  getSelectedTab,
  isMerchantUser,
  removeParams,
  saveToSessionStorage,
} from "./helpers";
import DsdFundingView from "../dashboard/dsd-funding-view/components/dsd-funding-view";
import { useSelectorWrap } from "@me/data-rtk";

import { appConstants } from "@me/utils-root-props";
import { useGetDefaultAllowanceColsPrefQuery } from "../../library/data-service/dashboard/dashboard-service";

export default function PromotionManagementViewSelect({
  showSortAndFilter,
  selectedTab,
  setSelectedTabValue,
}) {
  const dispatch = useDispatch();
  const queryString = window.location.search;
  const urlParams = new URLSearchParams(queryString);
  const isDsdView: boolean = urlParams.get("isDsdView") === "true";
  const isHavingDsdAccess = isMerchantUser();
  const { data: dsdFundingFilterData } = useSelectorWrap(
    DASHBOARD_SLICE_CONFIG.dsdFundingView.FILTER_SLICE_KEY
  );
  const {
    TASK_VIEW_KEY,
    ALERT_VIEW_KEY,
    PLANNING_VIEW_KEY,
    ALLOWANCE_VIEW_KEY,
    DSD_FUNDING_VIEW_KEY,
  } = DASHBOARD_VIEW_KEY_MAPPER;
  const viewTypeParam = getFromSessionStorage("viewType");
  const { dashboardConfig } = useGetSelectedDashboardTab();
  const isPageRefreshed = usePageRefresh();
  const defaultTab = getDefaultTab();

  const { data: allowanceDefaultColsResp, isFetching } =
    useGetDefaultAllowanceColsPrefQuery(
      {},
      {
        skip: selectedTab?.key !== ALLOWANCE_VIEW_KEY,
        refetchOnMountOrArgChange: true,
      }
    );

  const setDashboardDefaultConfiguration = () => {
    const configuration = DASHBOARD_MAPPING_ITEMS.reduce((acc, item) => {
      const shouldIncludeItem =
        item.key === DSD_FUNDING_VIEW_KEY
          ? !item?.isDisabled && isHavingDsdAccess
          : !item?.isDisabled;
      if (shouldIncludeItem) {
        const { component, ...rest } = item;
        acc[item?.name] = rest;
      }
      return acc;
    }, {});
    dispatch(setDashboardConfigValue(configuration));
  };

  const setAllowanceViewDefalutColumnsConfig = () => {
    const defaultApiPreference: {
      name: string;
      visible: boolean;
    }[] = allowanceDefaultColsResp?.offerColumns || [];
    const updatedColumnsPreference = defaultApiPreference?.length
      ? defaultApiPreference?.map(item => {
          const { name, visible } = item;
          const columnConfig = ALLOWVIEW_DRAGGABLE_OPTIONS_MAPPER?.[name] || {};
          return {
            ...columnConfig,
            visible,
            label: name,
          };
        })
      : Object.values(ALLOWVIEW_DRAGGABLE_OPTIONS_MAPPER);
    dispatch(setAllowancesViewDefalutColumnsConfig(updatedColumnsPreference));
    dispatch(
      setAllowancesColumnsData({
        expandedColumns: updatedColumnsPreference,
        isUserChangedCols: false,
      })
    );
  };

  useEffect(() => {
    setDashboardDefaultConfiguration();
  }, []);

  useEffect(() => {
    setDsdViewAndSearchOnRedirect();
  }, [isDsdView]);

  useEffect(() => {
    if (viewTypeParam) {
      const selectedTab = getSelectedTab(viewTypeParam);
      setSelectedTabValue(selectedTab || defaultTab);
    } else {
      !isDsdView &&
        saveToSessionStorage("viewType", selectedTab.key || defaultTab.key);
    }

    const saveSelectedTabConfiguration = () => {
      dispatch(
        setSelectedTab({ key: selectedTab.key, name: selectedTab.name })
      );
    };
    saveSelectedTabConfiguration();
    if (selectedTab?.key === ALLOWANCE_VIEW_KEY) {
      setAllowanceViewDefalutColumnsConfig();
    }
  }, [allowanceDefaultColsResp, isFetching, viewTypeParam]);

  useEffect(() => {
    // If page is refreshed, persist the selected tab for task and alert view
    if (isPageRefreshed) {
      const tabToPersist =
        viewTypeParam === ALERT_VIEW_KEY ? selectedTab : defaultTab;
      saveToSessionStorage("viewType", tabToPersist.key);
      setSelectedTabValue(tabToPersist);
    }
  }, [isPageRefreshed]);

  const getSearchParams = (
    isDsdView: boolean,
    storedSearchKey: string,
    storedSearchValue: string,
    urlParams: URLSearchParams
  ) => {
    const offerId = isDsdView
      ? urlParams.get("offerId") || ""
      : storedSearchKey === "Offer ID#"
      ? storedSearchValue || ""
      : "";
    const promoId = isDsdView
      ? urlParams.get("promoId") || ""
      : storedSearchKey === "Promo ID#"
      ? storedSearchValue || ""
      : "";

    return {
      key: offerId ? "offerNumbers" : "promotionIds",
      value: offerId || promoId,
    };
  };

  const handleDsdView = (dsdSearchParams: { key: string; value: string }) => {
    sessionStorage.setItem(
      "searchKey",
      SEARCH_FILTER_OPTIONS.find(
        option => option.eventKey === dsdSearchParams.key
      )?.name || ""
    );
    sessionStorage.setItem("searchValue", dsdSearchParams.value);
    removeParams();
    const tabSelected = isHavingDsdAccess
      ? DASHBOARD_MAPPING_ITEMS.find(ele => ele.key === DSD_FUNDING_VIEW_KEY)
      : defaultTab;
    saveToSessionStorage("viewType", tabSelected?.key as string);
    setSelectedTabValue(tabSelected);
  };

  const setDsdViewAndSearchOnRedirect = () => {
    const dsdKillSwitch = appConstants.DSD_FUNDING_KILL_SWITCH;
    const storedSearchValue = sessionStorage.getItem("searchValue") || "";
    const storedSearchKey = sessionStorage.getItem("searchKey") || "";
    const isDsdViewType =
      sessionStorage.getItem("viewType") === "dsdFundingView";

    if (dsdKillSwitch && (isDsdView || (isDsdViewType && storedSearchValue))) {
      const dsdSearchParams = getSearchParams(
        isDsdView,
        storedSearchKey,
        storedSearchValue,
        urlParams
      );

      if (isDsdView) handleDsdView(dsdSearchParams);
      else
        isHavingDsdAccess &&
          dispatch(
            setDsdFundingFilterData({
              ...dsdFundingFilterData,
              searchObj: {
                key: dsdSearchParams.key,
                value: [dsdSearchParams.value],
                rawString: dsdSearchParams.value,
              },
            })
          );
    }
  };

  return (
    <div
      id="abs-promotionManagementViewSelect"
      className={`overflow-hidden ${
        selectedTab.name !== "DSD Funding" ? "border border-[#c8daeb]" : ""
      } rounded-[8px] w-full ${
        pmConstants.componentClassName.PROMOTION_MANAGEMENT_VIEW_SELECT
      } abs-pm-task-alert-select`}
    >
      <Suspense fallback={<SkeletonLoader height={500} />}>
        <div
          id="abs-promotionManagementViewSelect-alertView"
          className={`min-h-full ${pmConstants.componentClassName.ALERT_VIEW}`}
          data-testid="alert-view"
        >
          <div
            id="abs-promotionManagementViewSelect-eventContentWrapper"
            className={`${
              selectedTab.name !== "DSD Funding" ? "bg-slate-100" : ""
            } ${pmConstants.componentClassName.EVENT_CONTENT_WRAPPER} rounded-[8px]`}
          >
            {isEmpty(dashboardConfig) ? null : (
              <>
                {/* {selectedTab.name === "Planning" && <selectedTab.component />} */}
                {[
                  TASK_VIEW_KEY,
                  ALERT_VIEW_KEY,
                  PLANNING_VIEW_KEY,
                  ALLOWANCE_VIEW_KEY,
                  DSD_FUNDING_VIEW_KEY,
                ].includes(selectedTab.key) && (
                  <Suspense fallback={<SkeletonLoader height={500} />}>
                    {selectedTab.name === "DSD Funding" ? (
                      <DsdFundingView />
                    ) : (
                      <DataGrid
                        selectedTab={selectedTab.name}
                        showSortAndFilter={showSortAndFilter}
                      />
                    )}
                  </Suspense>
                )}
              </>
            )}
          </div>
        </div>
      </Suspense>
    </div>
  );
}
