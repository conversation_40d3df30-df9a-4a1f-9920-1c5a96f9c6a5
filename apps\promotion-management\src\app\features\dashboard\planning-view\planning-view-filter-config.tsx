import Button from "@albertsons/uds/molecule/Button";
import CalendarIcon from "@me/promotion-management/event-calendar/event-calendar-elements/event-status/panel-content/event-content/calendar-icon";
import TimeScaleCalendarView from "@me/promotion-management/event-calendar/timescale-calendar-view";
import {
  EEVENT_STATUS,
  EUSER_ROLES,
  formPromoTypeAmountDisplay,
  formatDateTimeWithZone,
  getEventStatusTextClasses,
  getLoggedInUserType,
  getQuadrantColors,
  getSelectedStoreGroup,
  replaceMeuppuserToSystem,
  roundToDecimalPlaces,
} from "@me/util-helpers";
import {
  DASHBOARD_SLICE_CONFIG,
  DASHBOARD_VIEW_KEY_MAPPER,
  EXPORTABLE_COLUMNS_ARRAY,
} from "apps/promotion-management/src/app/config/dashboard-config";
import { setPlanningFilterData } from "apps/promotion-management/src/app/library/data-access/dashboard/planning-view-slice";
import { Copy, Skull } from "lucide-react";
import DashboardSearch from "../common/components/dashboard-search";
import { HeaderItem } from "../common/components/render-header-item";
import { Column } from "@albertsons/uds/molecule/Table/Table.types";
import Tooltip from "@albertsons/uds/molecule/Tooltip";
import classNames from "classnames";
import {
  getAllowanceType,
  getPromoType,
} from "../../../shared/utility/utility";
import pmConstants, {
  PROMOTION_MANAGEMENT_COLUMNS,
} from "../../../shared/pm-constants/pm-constants";
import { NavLink } from "react-router-dom";
import {
  formatTimestamp,
  getCurrentDateAndTime,
  isSortFeatureFlagEnabled,
  showAddEventCTA,
  useGetAppBaseNationalIcon,
} from "@me-upp-js/utilities";
import { format } from "date-fns";
import EventOverviewColumn from "./event-overview-coulmn";
import { useSelectorWrap } from "@me/data-rtk";
import { useState } from "react";
import { useGetPlanningExportDataMutation } from "../../../library/data-service/dashboard/dashboard-service";
import { ExportModal } from "../../../shared/ui/molecules";
import { ExportBtn, SkeletonLoader } from "../../../shared/ui/atoms";
import { generateCommonPayLoadForDashBoard } from "../common/utils/common-view-api-service";
import { appConstants } from "@me/utils-root-props";
import { getDefaultExportOptionsByView } from "@me/promotion-management/common/helpers";
import { SortLabel } from "../common/components/sort-label";
import { EVENT_TYPE } from "../../../constants";
import { is } from "date-fns/locale";

const loggedInUser = getLoggedInUserType(),
  isUserMerchant = loggedInUser === EUSER_ROLES.MERCHANT;

const ExportContent = () => {
  const dashboardKey = DASHBOARD_VIEW_KEY_MAPPER.PLANNING_VIEW_KEY;
  const { planningView } = DASHBOARD_SLICE_CONFIG;
  const { data: planningData } = useSelectorWrap(planningView.SLICE_KEY) || {};
  const { data: planningFilterData } =
    useSelectorWrap(planningView.FILTER_SLICE_KEY) || {};
  const { data: globalHeaderData } = useSelectorWrap("eventCalendarPayload_rn");
  const { data: performanceApiData } = useSelectorWrap(
    "performance_api_data_rn"
  );
  const [options, setOptions] = useState(
    getDefaultExportOptionsByView(dashboardKey)
  );
  const [isOpened, setIsOpened] = useState(false);
  const [error, setError] = useState("");

  const [postPlanningExportData, { isLoading }] =
    useGetPlanningExportDataMutation();

  const selectedOptions = options?.filter(({ visible }) => visible);

  const onExport = () => {
    setIsOpened(isOpened => !isOpened);
  };

  const handleExport = async () => {
    try {
      setError("");
      const payload = {
        planEventFilter: generateCommonPayLoadForDashBoard({
          globalHeaderData,
          viewLevelFilterData: planningFilterData,
          paging: { pageNumber: 0, pageSize: 500 },
          propsFromGlobal: PLANNING_PROPS_FROM_GLOBAL_HEADER,
          view: "Planning",
          performanceApiData,
        }),
        exportColumns: selectedOptions?.map(({ key }) => key),
      };
      delete payload.planEventFilter?.paging;

      await postPlanningExportData(JSON.stringify(payload))
        .then(async response => {
          if (response?.data) {
            const fileURL = window.URL.createObjectURL(response?.data);
            const link = document.createElement("a");
            link.href = fileURL;
            link.setAttribute(
              "download",
              `planning_export_${getCurrentDateAndTime()}.xlsx`
            );
            document.body.appendChild(link);
            link.click();
            link?.parentNode?.removeChild(link);
            URL.revokeObjectURL(fileURL);
            setIsOpened(false);
          } else {
            setError(appConstants.EXPORT_ERROR.message);
          }
        })
        .catch(err => {
          setError(err);
        });
    } catch (error) {
      setError(error as string);
    }
  };

  return (
    <div className="flex gap-3 items-center mr-4">
      <ExportModal
        options={options}
        setOptions={setOptions}
        selectedOptions={selectedOptions}
        isOpened={isOpened}
        setIsOpened={setIsOpened}
        headerTitle={"Export Events"}
        error={error}
        setError={setError}
        handleExport={handleExport}
        isLoading={isLoading}
        view={DASHBOARD_VIEW_KEY_MAPPER.PLANNING_VIEW_KEY}
      />
      <ExportBtn
        onExportClick={onExport}
        disabled={!planningData?.eventTaskProductGroups?.length}
      />
    </div>
  );
};

export const SUB_HEADER_CONFIG: HeaderItem[] = [
  {
    key: "duplicate-event",
    label: (
      <div className="flex gap-2">
        <Copy /> Copy
      </div>
    ),
    type: "button",
    extraConfig: {
      width: 244,
      variant: "secondary",
    },
  },
  {
    key: "divider-1",
    type: "divider",
  },
  {
    key: "search-box",
    type: "custom-component",
    customComponent: (
      <DashboardSearch
        viewKey={DASHBOARD_VIEW_KEY_MAPPER.PLANNING_VIEW_KEY}
        action={setPlanningFilterData}
        sliceKey={DASHBOARD_SLICE_CONFIG.planningView.FILTER_SLICE_KEY}
        viewType={DASHBOARD_VIEW_KEY_MAPPER.PLANNING_VIEW_KEY}
      />
    ),
  },
  {
    key: "divider-2",
    type: "divider",
  },
  {
    key: "event-status",
    type: "multi-select",
    extraConfig: {
      renderText: renderOptionText,
    },
  },
  {
    key: "white-space",
    type: "white-space",
  },
  {
    key: "export-btn",
    type: "custom-component",
    customComponent: <ExportContent />,
  },
  {
    key: "timescale-and-calender-view",
    type: "custom-component",
    customComponent: <TimeScaleCalendarView />,
  },
  {
    key: "divider-3",
    type: "divider",
  },
];

function renderOptionText(value, statusCount) {
  return (
    <div className="flex items-center gap-1">
      <CalendarIcon event={{ workflowState: value?.name }} className="h-2" />

      <span
        className={`font-semibold ${getEventStatusTextClasses(value?.name)}`}
      >
        {value?.name} {`(${statusCount?.eventStatusCount || 0})`}
      </span>
    </div>
  );
}

export type PlanningPropsFromGlobalHeader = {
  required: string[];
  optional: string[];
};

export const PLANNING_PROPS_FROM_GLOBAL_HEADER: PlanningPropsFromGlobalHeader =
  {
    required: ["startDate", "endDate", "divisionIds", "timeScale"],
    optional: [
      "asmNamesOrCategorGroupCodes",
      "smicCategoryCodes",
      "smicGroupCodes",
      "isAllGrpCatSelected",
      // "allGrpCatSelected",
      "productSelectionType",
      "planEventSortType",
      "supplierCodes",
      "promoProductGroups",
      "banners",
      "allowanceTypes",
      "promotionTypes",
      "vehicleTypes",
      "eventTypes",
    ],
  };

export const dateRange = (props, isLoading) => (
  <p
    className={`text-[13px] leading-4 text-left text-[#2b303c] ${pmConstants.componentClassName.DATE_RANGE}`}
  >
    {!isLoading ? (
      <SkeletonLoader height={15} width={100} />
    ) : (
      `${formatTimestamp({ timestamp: props.startDate, pattern: "MM/DD/YY" })} - ${formatTimestamp({ timestamp: props.endDate, pattern: "MM/DD/YY" })}`
    )}

  </p>
);

export const storeGrpFormedList = props => {
  return (
    props?.storeGroupDTO?.length > 1 &&
    props?.storeGroupDTO?.map((storeGroup, index) => {
      return {
        ...storeGroup,
      };
    })
  );
};

export const getStoreGroupName = props => {
  const { storeGroupDTO } = props;
  let displayStoreGroup = `${storeGroupDTO?.[0]?.storeGroupName} ${
    storeGroupDTO?.[0]?.storeCount ? `(${storeGroupDTO?.[0]?.storeCount})` : ""
  }`;

  if (storeGroupDTO?.length > 1) {
    const selectedStoreGroup = getSelectedStoreGroup(storeGrpFormedList(props));
    displayStoreGroup = `${selectedStoreGroup?.storeGroupName} (${selectedStoreGroup?.storeCount})`;
  }
  return displayStoreGroup;
};

export const onPopperBlur = (ev: any, ref, cb) => {
  if (ev.relatedTarget !== ref.current?.firstChild) {
    cb(false);
  }
};

const eventTypeTextClass = classNames({
  "text-left text-[#2b303c]": true,
});

const eventTypeWrapClass = classNames({
  "flex justify-start items-center relative gap-1": true,
});

const appliedIcon = (
  <svg
    width="9"
    height="8"
    viewBox="0 0 9 8"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="flex-grow-0 flex-shrink-0 w-2 h-2"
    preserveAspectRatio="none"
  >
    <circle cx="4.33334" cy="4" r="3.5" fill="#5FBFA8" />
    <path
      d="M3.58334 5.0499L2.53334 3.9999L2.18334 4.3499L3.58334 5.7499L6.58334 2.7499L6.23334 2.3999L3.58334 5.0499Z"
      fill="white"
    />
  </svg>
);

const notApplicable = (
  <svg
    width="9"
    height="8"
    viewBox="0 0 9 8"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="flex-grow-0 flex-shrink-0 w-2 h-2"
    preserveAspectRatio="none"
  >
    <circle cx="4.33334" cy="4" r="3.5" fill="#98A1BE" />
  </svg>
);

const displayEventType = (props, isLoading) => {
  const isDisplayPromoOption = ![EVENT_TYPE.NDP, EVENT_TYPE?.NCDP].includes(
    props?.eventType
  );
  return (
    <div
      className={`flex justify-start items-start gap-2 text-[11px] flex-wrap ${pmConstants.componentClassName.DISPLAY_EVENT_TYPE}`}
    >
      <div className={eventTypeWrapClass}>
        {!isLoading ? (
          <SkeletonLoader height={15} width={100} />
        ) : (
          <>
            {props.allowancesInd ? appliedIcon : notApplicable}
            <p className={eventTypeTextClass}>
              {`Allowance ${
                props?.allowances?.length > 1
                  ? `(${props?.allowances?.length})`
                  : ""
              }`}
            </p>
          </>
        )}
      </div>
      {props?.eventType !== "AO" && isDisplayPromoOption && (
        <div className={eventTypeWrapClass}>
          {!isLoading ? (
            <SkeletonLoader height={15} width={100} />
          ) : (
            <>
              {props.promotionsInd ? appliedIcon : notApplicable}
              <p className={eventTypeTextClass}>{`Promo ${
                props?.promotions?.length > 1
                  ? `(${props?.promotions?.length})`
                  : ""
              }`}</p>
            </>
          )}
        </div>
      )}
      {/* <div className={eventTypeWrapClass}>
        {props.adPlansInd ? appliedIcon : notApplicable}
        <p className={eventTypeTextClass}>{`Ad ${props?.adPlans?.length > 1 ? `(${props?.adPlans?.length})` : ""
          }`}</p>
      </div> */}
    </div>
  );
};

const eventTypeColumn = (props, isLoading) => (
  <div
    className={`pl-3 text-[13px] leading-4 text-left ${pmConstants.componentClassName.EVENT_TYPE_COLUMN}`}
  >
    {!isLoading ? (
      <SkeletonLoader height={15} width={100} />
    ) : (
    props?.allowanceType?.length ? (
      <p className="text-[#2b303c]">
        Allowance Type: &nbsp;<b>{getAllowanceType(props.allowanceType)}</b>
        {/* {(props.allowanceAmount as string[]).length
            ? `($${getAllowanceAmount(props.allowanceAmount)})`
            : null} */}
      </p>
    ) : null)}
    {props?.promotionTypes?.length ? (
      !isLoading ? <SkeletonLoader height={15} width={100} /> :
      <p className="text-[#2b303c]">
        Promo Type:
        <span className="text-[13px] text-[#2b303c]">
          &nbsp;<b>{getPromoType(props.promotionTypes)}</b>
          <span className="ml-1">
            {formPromoTypeAmountDisplay(
              props,
              props?.promotionAmounts?.reduce(
                (total, amount) => (total += +amount),
                0
              )
            )}
          </span>
        </span>
      </p>
    ) : null}
    {pmConstants.FORECAST_DISPLAY[loggedInUser] ? (
      !isLoading ? <SkeletonLoader height={15} width={100} /> :
      <p className="text-[#2b303c]">
        Forecast :
        <span className="font-semibold text-[#2b303c] pl-1">
          $
          <span>
            {props.forecastSalesAmt
              ? roundToDecimalPlaces(+props.forecastSalesAmt, 0)
              : 0}
          </span>
          {/* check if forecastSalesAmt to be displayed here */}
          {/* $ <span>{props.forecastSalesAmt}</span> */}
        </span>
      </p>
    ) : null}

    {displayEventType(props, isLoading)}
  </div>
);

const vendorNameColumn = (props, isLoading) => {
  const uniquesimsList: string[] = [];
  const label = props.planSimsVendor?.reduce(
    (simsTaskLabel, { vendorId, vendorName }) => {
      if (!uniquesimsList.includes(vendorId)) {
        uniquesimsList.push(vendorId);
        simsTaskLabel =
          !isLoading ? <SkeletonLoader height={15} width={200} />:
          simsTaskLabel + `${simsTaskLabel && ", "}${vendorId} ${vendorName}`;
      }
      return simsTaskLabel;
    },
    ""
  );

  return (
    <div
      id={`abs-planning-view-columns-config-vendor-container-${props.planSimsVendor?.[0]?.vendorId}-${props.id}`}
      className="pl-3 flex gap-2 truncate whitespace-nowrap text-sm"
    >
      <Tooltip zIndex={10} anchor="bottom">
        <Tooltip.Popover>
          <div
            id={`abs-planning-view-columns-config-vendor-name-tooltip--${props.planSimsVendor?.[0]?.vendorId}-${props.id}`}
            className="m-1 w-fit overflow-auto rounded text-black text-sm font-normal leading-4"
          >
            {label}
          </div>
        </Tooltip.Popover>
        <div
          id={`abs-planning-view-columns-config-vendor-label-${props.planSimsVendor?.[0]?.vendorId}-${props.id}`}
          className="w-[17vw] truncate font-semibold leading-6"
        >
          {label}
        </div>
      </Tooltip>
    </div>
  );
};

const eventStatusColumn = (props, isLoading) => {
  const eventStatusClass = classNames({
    "text-[14px] leading-4 font-semibold text-left ": true,
    "text-[#ab4205]": ["Pending With Merchant", "Pending With Vendor"].includes(
      props?.eventStatus
    ),
    "text-[#105f0e]": ["Ready"].includes(props?.eventStatus),
    "text-[#033b69]": ["Draft", "Agreed", "Rejected"].includes(
      props?.eventStatus
    ),
  });
  return (
    <div
      className={`pl-3 ${pmConstants.componentClassName.EVENT_STATUS_COLUMN}`}
    >
      {
        !isLoading ? <SkeletonLoader height={15} width={50} /> :
      <p className={eventStatusClass}>{props.eventStatus}</p>
      }

      <div className="flex flex-col justify-center items-start gap-1">
        <div className="flex justify-start items-start relative gap-1">
          <p className="text-[13px] font-semibold text-left text-[#2b303c]">
            { !isLoading ? <SkeletonLoader height={15} width={150} /> :
              <div className="w-full">
                {replaceMeuppuserToSystem(props?.updateUser?.name)}
            <span
              className={`text-[13px] font-normal leading-4 ${
                props?.updateUser?.name ? "px-1" : ""
              }`}
            >
              updated event
            </span>
            </div>}
          </p>
        </div>
        <div className="flex justify-start items-start relative gap-1 text-xs items-center">
          {
            !isLoading ? <SkeletonLoader className="mb-1" height={15} width={100} /> :
            <p className="text-xs text-left text-[#5a697b]">
            {formatDateTimeWithZone(
              props?.updateUser?.createTs
                ? props?.updateUser?.createTs
                : new Date()
            )}
          </p>}

          {props.eventStatus === "Ready" && (
            <>
              <svg
                width={2}
                height={16}
                viewBox="0 0 2 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className=" "
                preserveAspectRatio="xMidYMid meet"
              >
                <line
                  x1="1.16669"
                  y1="-8.28172e-9"
                  x2="1.16669"
                  y2={16}
                  stroke="#C8DAEB"
                />
              </svg>
              <Button
                variant="tertiary"
                width={80}
                size="xs"
                onClick={() =>
                  props.handleViewHistory(props.id, props.planEventIdNbr)
                }
                id="abs-events-row-container-view-history-button"
              >
                View History
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};
const renderApproveButton = () => {
  return (
    <div
      className={`${pmConstants.componentClassName.RENDER_APPROVE_BUTTON}`}
      id="abs-events-row-container-approve-button"
    >
      <div className="flex flex-col justify-center items-center   h-8 overflow-hidden gap-2.5 px-2 py-0.5 rounded-lg bg-white border-2 border-[#1b6ebb]">
        <div className="flex justify-center items-center   relative gap-2">
          <p className="  text-base font-semibold text-center text-[#1b6ebb]">
            Approve
          </p>
        </div>
      </div>
    </div>
  );
};

const renderReviewButton = props => {
  return (
    <div
      className={`flex flex-col justify-start items-start ${pmConstants.componentClassName.RENDER_REVIEW_BUTTON}`}
      id="abs-events-row-container-review-button"
    >
      <div className="flex flex-col justify-center items-center   h-8 overflow-hidden gap-2.5 px-2 py-0.5 rounded-lg">
        <div className="flex justify-center items-center   relative gap-2">
          <NavLink
            to={`events/edit/${props.id}`}
            // onClick={(e) => e?.preventDefault()}
            className="flex font-bold text-left text-[#1b6ebb]"
            data-testid="link-by-product-d"
          >
            <p className="  text-base font-semibold text-center text-[#1b6ebb]">
              Review
            </p>
          </NavLink>
        </div>
      </div>
    </div>
  );
};

const renderNoActionsNeeded = ids => {
  return (
    <div
      className={`flex pl-1 items-center w-[151px] ${pmConstants.componentClassName.RENDER_NO_ACTION_NEEDED}`}
      id={`abs-events-row-container-no-actions-needed-${ids}`}
    >
      <div className="flex justify-start items-start   relative gap-2.5 pr-2">
        <p className="  text-sm text-[#5a697b]">No actions needed</p>
      </div>
    </div>
  );
};
const eventActionsColumn = props => {
  const isDisplayActionBtns =
    ["PENDING WITH VENDOR", "PENDING WITH MERCHANT", "AGREED PENDING"].includes(
      props?.eventStatus.toUpperCase().replace(/-/g, " ")
    ) &&
    localStorage.getItem("USER_TYPE") !==
      props?.updateUser?.type?.toUpperCase();
  return (
    <div
      className={`pl-3 flex flex-shrink ${pmConstants.componentClassName.EVENT_ACTION_COLUMN}`}
    >
      {isDisplayActionBtns
        ? renderApproveButton() && renderReviewButton(props)
        : renderNoActionsNeeded(props?.id)}
    </div>
  );
};
const { Event, Vendor, Status } = PROMOTION_MANAGEMENT_COLUMNS;
export const PLANNING_VIEW_GRID_COLUMNS = (isLoading: boolean): Column<any>[] => {
  return[
  {
    id: "eventName",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel _key={"event"} label={Event} view="planningView" />
    ) : (
      Event
    ),
    width: "33vw",
    value: (task: any) => <EventOverviewColumn task={task} isLoading={isLoading} />,
  },
  {
    id: "dateRange",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel
        _key={"date"}
        label={PROMOTION_MANAGEMENT_COLUMNS?.Date}
        view="planningView"
      />
    ) : (
      PROMOTION_MANAGEMENT_COLUMNS?.Date
    ),
    width: "8vw",
    value: (task: any) => dateRange(task, isLoading),
  },
  {
    id: "VendorName",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel _key={"vendor"} label={Vendor} view="planningView" />
    ) : (
      Vendor
    ),
    width: "20vw",
    value: (task: any) => vendorNameColumn(task, isLoading)
  },
  {
    id: "eventType",
    label: "Type",
    width: "15vw",
    value: (task: any) => eventTypeColumn(task, isLoading),
  },
  {
    id: "eventStatus",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel _key={"eventStatus"} label={Status} view="planningView" />
    ) : (
      Status
    ),
    value: (task: any) => eventStatusColumn(task, isLoading),
  },
];}
