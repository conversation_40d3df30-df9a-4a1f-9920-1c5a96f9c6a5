import { useSelectorWrap } from "@me/data-rtk";
import React from "react";
import EventDetailsColumnConfig from "../child-data-grid/columns-config/event-details-column-config";
import OfferDetailsColumnConfig from "../child-data-grid/columns-config/offer-details-column-config";

function useGetGridConfigData({ cardIndex }) {
  // Fetch selected tab configuration from the store
  const { data: selectedTabConfig } = useSelectorWrap(
    "child_events_tab_config_data"
  );

  const { gridConfig, key, gridDataSliceKey, childDataKey } = selectedTabConfig || {};

  const { data: allGridData = [] } = useSelectorWrap(gridDataSliceKey);

  // Retrieve the grid data for the current card and childDataKey
  const currentCardGridData = allGridData?.[cardIndex]?.[childDataKey] || [];

const Mapper = {
  events: EventDetailsColumnConfig({
    gridConfig,
    gridData: allGridData,
    cardIndex,
  }),
  offers: OfferDetailsColumnConfig({
    gridConfig,
    gridData: allGridData,
    cardIndex,
  }),
};

  return {
    gridConfig,
    columns: Mapper?.[key] || [],
    gridData: currentCardGridData,
    key
  };
}

export default useGetGridConfigData;
