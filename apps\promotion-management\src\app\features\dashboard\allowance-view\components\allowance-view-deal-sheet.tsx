import Button from "@albertsons/uds/molecule/Button";
import { useState } from "react";
import { OfferViewModal } from "./allowance-view-model";
import { ViewAgreementModal } from "../../../../shared/components/view-agreement-modal";
import { ClipboardList } from "lucide-react";

type Props = {
  offer: OfferViewModal;
  isCopyShow?: boolean;
};

export default function AllowanceViewDealSheet({ offer, isCopyShow }: Props) {
  const {
    planEvent,
    offerNumber,
    divisionIds,
    allowanceSummary,
    createInd,
    eventStatus,
    allowanceChangeStatus,
    eventType,
  } = offer;
  const [isModalOpen, setIsModalOpen] = useState(false);

  const getExcludedAllowances = () =>
    allowanceSummary?.reduce((acc, { includeInd = null, allowanceIdNbr }) => {
      if (!includeInd && allowanceIdNbr) return [...acc, allowanceIdNbr];
      return acc;
    }, []);

  return (
    <div id="abs-allowance-view-deal-sheet">
      {isCopyShow ? (
        <ClipboardList width={'16px'} height={'16px'} onClick={() => setIsModalOpen(true)} color="#1B6EBB" />
      ) : (
        <Button
          id="abs-allowance-view-deal-sheet-button"
          // width={140}
          variant="secondary"
          className="!h-auto px-2 py-1"
          onClick={() => setIsModalOpen(true)}
        >
          View Deal Sheet
        </Button>
      )}

      {isModalOpen && (
        <ViewAgreementModal
          setIsAllowanceAgreementVisible={() => setIsModalOpen(false)}
          eventId={planEvent}
          divisionName={divisionIds?.[0]}
          allowanceType={createInd}
          offerNumber={offerNumber}
          allowanceChangeStatus={allowanceChangeStatus}
          eventStatus={eventStatus}
          allowances={allowanceSummary}
          exludedAllowances={getExcludedAllowances()}
          eventType={eventType}
        />
      )}
    </div>
  );
}
