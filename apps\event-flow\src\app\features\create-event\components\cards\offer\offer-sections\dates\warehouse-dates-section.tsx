import { useEffect, useRef, useState, memo, useMemo } from "react";
import LoadingSpinner from "../../../../../constants/LoadingSpinner/LoadingSpinner";
import {
  getAllowanceKey,
  getAllowanceMapKey,
} from "../../../../../service/allowance/allowance-service";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";
import { useSelectorWrap } from "@me/data-rtk";
import { cloneDeep } from "lodash";
import useAllowTempworkUpdate from "../../../../../hooks/useAllowTempworkUpdate";
import AllowanceDatesInitialText from "../../../allowance/stepper/common-stepper/allowance-dates/allowance-dates-initial-text";
import BaseAllowanceDates from "./base-allowance-dates";
import {
  checkIsNationalEvent,
  getDatesVehicleForTemp,
  getNationalVehicleForTemp,
  getOfferMapKey,
} from "../../offer-service";
import AdditionalDatesSection from "./additional-dates-section";
import { getObjectKeys } from "../../../../../service/allowance/allowance-stepper-service";
import useNationalAllowTempUpdate from "../../../../../hooks/useNationalAllowTempUpdate";
import NationalAdditionalDatesSection from "../../national/dates/national-additional-dates-section";

const WarehouseAllowanceDatesSection = ({
  cardIndex,
  cardItemIndex,
  formControls,
  sectionConfiguration,
  isEditEnable = false,
  allowanceRegField,
  sectionKey,
}) => {
  // read data from redux store
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { isAllowConvEnable } = useSelectorWrap("allow_conv_status_rn").data;
  const { data: allowanceForm } = useSelectorWrap("allowance_form_data");
  const { allowanceData } = useSelectorWrap("allowance_temp_work")?.data || {};
  const { offerDivisions = [] } =
    useSelectorWrap("national_offer_divisions")?.data || {};

  // API call to save data in temp
  const { saveAllowancePrimeSectionData, isLoading } = useAllowTempworkUpdate();
  const {
    saveNationalAllowancePrimeSectionData,
    isLoading: isNationalTempLoading,
  } = useNationalAllowTempUpdate();

  // read data from configuration
  const { initialText } = sectionConfiguration?.fields || {};
  const { ALLOWANCE_TYPES, NEW_DIRECTION_FEATURES_FLAGS } = efConstants,
    { CASE } = ALLOWANCE_TYPES,
    { isDirectionalChangesEnable } = NEW_DIRECTION_FEATURES_FLAGS;

  // local state
  const [vehicleFields, setVehicleFields] = useState({
    vehicleTypeId: "",
    startDate: "",
    endDate: "",
    vehicleType: {},
    vehicleTabType: "",
    vehicleId: "",
    vehicleNm: "",
    vehicleTypeDesc: "",
    year: "",
  });
  const isFirstLoad = useRef(true);
  const [enableAddintionalDates, setEnableAdditionalDates] = useState(false);
  const { setValue = () => null } = formControls || {};

  // get data from form
  const allowanceFormData =
    allowanceForm?.allowanceFormData?.[allowanceRegField];
  const { allowanceType = "", createInd } =
    allowanceFormData?.allowancePrimeData || {};
  const allowanceTypeKey = getAllowanceKey(allowanceType?.toUpperCase());
  const allowName = getAllowanceMapKey(allowanceTypeKey) || "";
  const offerMapKey = getOfferMapKey(
    createInd,
    sectionConfiguration?.offerTypeKey
  );
  const isNationalEvent = checkIsNationalEvent(eventDetailsData?.eventType);
  const nationalVehicles = eventDetailsData?.nationalInfos?.nationalDivs || [];

  const saveNationalVehicleDates = () => {
    if (
      allowanceData?.[0]?.allowanceTypeSpecification?.[allowName]
        ?.allowancesMap?.[offerMapKey]?.length
    ) {
      setEnableAdditionalDates(true);
      isFirstLoad.current = true;
      return;
    }

    const nationalVehicleMapData = getNationalVehicleForTemp(
      offerDivisions,
      nationalVehicles,
      offerMapKey
    );
    saveDatesInToNationalTemp(nationalVehicleMapData);
    return nationalVehicleMapData;
  };

  async function saveDatesInToNationalTemp(nationalVehicleMaps) {
    const vehicleKeys = getObjectKeys(nationalVehicleMaps);
    try {
      // Initial TEMPWORK construction when there is no temp data or dependent fields are changed related to date.
      const updatedTempwork = vehicleKeys?.map((divId: string) => {
        const tempData: any = allowanceData?.find(
          tempWork => tempWork?.divisionId === divId
        );
        return {
          ...tempData,
          allowanceTypeSpecification: {
            ...tempData?.allowanceTypeSpecification,
            [allowName]: {
              ...tempData?.allowanceTypeSpecification?.[allowName],
              vehicleDatesMap: {
                ...tempData?.allowanceTypeSpecification?.[allowName]
                  ?.vehicleDatesMap,
                [offerMapKey]: nationalVehicleMaps?.[divId]?.[offerMapKey],
              },
            },
          },
        };
      });

      const result = await saveNationalAllowancePrimeSectionData(
        updatedTempwork
      );
      if (result?.data?.[0]?.allowanceType) {
        isFirstLoad.current = true;
        setEnableAdditionalDates(true);
      }
    } catch (e) {
      console.log(e);
    }
  }

  // Save allowance dates in to temp
  async function saveDatesInToTemp(vehicleDatesMap) {
    const formVehicle =
      allowanceFormData?.allowanceCreationVehicle?.[offerMapKey];
    if (formVehicle?.vehicleId) {
      setEnableAdditionalDates(true);
      isFirstLoad.current = true;
      return;
    }

    try {
      const updatedTempwork = {
        ...allowanceData,
        allowanceTypeSpecification: {
          ...allowanceData?.allowanceTypeSpecification,
          [allowName]: {
            ...allowanceData?.allowanceTypeSpecification?.[allowName],
            vehicleDatesMap,
          },
        },
      };
      const result = await saveAllowancePrimeSectionData(updatedTempwork);
      if (result?.data?.allowanceType) {
        isFirstLoad.current = true;
        setEnableAdditionalDates(true);
      }
    } catch (e) {
      console.log(e);
    }
  }

  // Initial load of the component
  const handleInitialStepLoad = async () => {
    setEnableAdditionalDates(false);
    if (allowanceTypeKey !== CASE.key) return;
    else if (!isDirectionalChangesEnable && !isAllowConvEnable) {
      setEnableAdditionalDates(true);
      return;
    }
    isFirstLoad.current = false;
    return isNationalEvent
      ? await saveNationalVehicleDates()
      : await saveDatesInToTemp(
          getDatesVehicleForTemp(
            cloneDeep(eventDetailsData?.eventCreationVehicle),
            offerMapKey,
            allowanceData?.allowanceTypeSpecification?.[allowName]
              ?.vehicleDatesMap
          )
        );
  };

  useEffect(() => {
    setValue("allowanceType", allowanceType);
  }, [allowanceType]);

  useEffect(() => {
    isFirstLoad.current && handleInitialStepLoad();
  }, []);

  const AdditionalDatesComponent = useMemo(
    () => (
      <AdditionalDatesSection
        cardIndex={cardIndex}
        cardItemIndex={cardItemIndex}
        formControls={formControls}
        sectionConfiguration={sectionConfiguration}
        isEditEnable={isEditEnable}
        offerMapKey={offerMapKey}
        vehicleFields={vehicleFields}
        sectionKey={sectionKey}
      />
    ),
    [enableAddintionalDates]
  );

  const NationalAdditionalDatesComponent = useMemo(
    () => (
      <NationalAdditionalDatesSection
        cardIndex={cardIndex}
        cardItemIndex={cardItemIndex}
        formControls={formControls}
        sectionConfiguration={sectionConfiguration}
        isEditEnable={isEditEnable}
        offerMapKey={offerMapKey}
        vehicleFields={vehicleFields}
        sectionKey={sectionKey}
      />
    ),
    [enableAddintionalDates]
  );

  return (
    <div>
      <div className="text-xl text-[#3997EF] font-bold my-3">
        {sectionConfiguration?.label || ""}
      </div>

      <LoadingSpinner
        isLoading={isLoading || isNationalTempLoading}
        classname="!h-full !w-full rounded-md"
      />
      <AllowanceDatesInitialText
        allowanceType={allowanceTypeKey}
        option={offerMapKey}
        stepperElement={{ fields: { initialText } }}
        className="py-4 px-3 bg-[#F3F4F6]"
      />
      <BaseAllowanceDates
        formControls={formControls}
        allowanceRegField={allowanceRegField}
        sectionConfiguration={sectionConfiguration}
        isEditEnable={isEditEnable}
        offerMapKey={offerMapKey}
        vehicleFields={vehicleFields}
        setVehicleFields={setVehicleFields}
      />
      {vehicleFields?.vehicleId && (enableAddintionalDates || isAllowConvEnable)
        ? !isNationalEvent
          ? AdditionalDatesComponent
          : NationalAdditionalDatesComponent
        : null}
    </div>
  );
};

export default memo(WarehouseAllowanceDatesSection);
