import { replaceMeuppuserToSystem, timeDifference } from "@me/util-helpers";
import efConstants from "../../../../shared/ef-constants/ef-constants";
import { addWeeks, format } from "date-fns";

const {
  DISPLAY_TYPE_PPG_CD,
  PPG_UNIT_TYPE_MAPPER,
  DISPLAY_ITEM_FLAG_MIXED,
  VENDOR_NOT_LINKED,
  SMIC_NOT_IN_ROLLOUT,
  ALLOWANCE_TYPES,
} = efConstants;

export const getTitle = title => {
  return `${title}`;
};

export const getEventName = (eventName = "Event Name") => {
  return `${eventName}`;
};
export const getSubTitle = (updatedObject: {
  name: string;
  createTs: string;
}) => {
  return `Updated ${
    updatedObject?.["name"]
      ? `by ${replaceMeuppuserToSystem(updatedObject?.["name"])}`
      : ""
  } ${timeDifference(updatedObject?.["createTs"])} ago`;
};

export const getProductGroups = productGroups => {
  return productGroups?.map(productGroup => {
    const productGroupName = formPpgName(productGroup, efConstants);
    return {
      productGroupName,
      id: productGroup.planProductGroupId,
      supplier: productGroup?.supplier?.supplierName,
      itemCount: productGroup?.itemCount,
    };
  });
};

export const getStoreGroups = storeGroups => {
  return storeGroups.map(storeGroup => {
    return {
      storeGroupName: storeGroup.storeGroupName,
      storeGroupId: storeGroup.storeGroupId,
      storeCount: storeGroup.storeCount,
    };
  });
};

export const getStoreGroupsListFromOffers = eventDetailsData => {
  let storeGroups: object[] = [];
  const storeGroupIds = new Set();

  eventDetailsData?.offerAllowances?.forEach(offer => {
    offer?.allowances?.[0]?.storeGroups?.forEach(storeGroup => {
      if (
        !checkForCaseAllowance(offer) &&
        !storeGroupIds.has(storeGroup?.storeGroupId)
      ) {
        storeGroups = [...storeGroups, storeGroup];
        storeGroupIds.add(storeGroup?.storeGroupId);
      }
    });
  });
  return storeGroups;
};

export const checkForCaseAllowance = offer => {
  return ALLOWANCE_TYPES.CASE.createInd.includes(offer?.createInd);
};

export const formPpgNameBasedOnUnitTypeAndCount = ppgList => {
  if (!ppgList) return [];
  const ppgDataCopied = JSON.parse(JSON.stringify(ppgList));
  return ppgDataCopied?.map(ppgObj => {
    if (ppgObj?.productGroupType !== "CIC") {
      const updatedPpgName = formPpgName(ppgObj, efConstants);
      ppgObj.ppgName = ppgObj.name;
      ppgObj.name = updatedPpgName;
      ppgObj.planProductGroupId = ppgObj.planProductGroupId || ppgObj.id;
    }
    return ppgObj;
  });
};

export const formPpgName = (ppgObj, efConstants) => {
  if (!ppgObj) return "";

  const { unitType, displayInd, itemCount, name, sourceProductGroupId, planProductGroupName = "" } =
    ppgObj;

  const unitTypeCd = displayInd
    ? DISPLAY_TYPE_PPG_CD
    : PPG_UNIT_TYPE_MAPPER?.[unitType]?.code || "";

  const typeCdWithItemString = `- ${unitTypeCd} - ${itemCount}`;
  let updatedName = sourceProductGroupId ? `${sourceProductGroupId} - ` : "";
  updatedName += (name || planProductGroupName)?.includes(typeCdWithItemString)
    ? (name || planProductGroupName)
    : `${name || planProductGroupName} ${typeCdWithItemString}`;

  return updatedName;
};

export const getEventDetailsDates = eventDetails => {
  if (
    !eventDetails?.eventCreationVehicle?.startDate ||
    !eventDetails.eventCreationVehicle?.endDate
  )
    return "";
  if (
    eventDetails?.eventCreationVehicle?.vehicleType?.vehicleTypDesc ===
    "Custom Date"
  )
    return `${format(
      new Date(
        typeof eventDetails?.startDate === "string"
          ? eventDetails?.startDate.replace(/-/g, "/")
          : eventDetails?.startDate
      ),
      "MM/dd/yy"
    )} - ${format(
      new Date(
        typeof eventDetails?.endDate === "string"
          ? eventDetails?.endDate.replace(/-/g, "/")
          : eventDetails?.endDate
      ),
      "MM/dd/yy"
    )}`;
  return `${format(
    new Date(
      typeof eventDetails?.eventCreationVehicle?.startDate === "string"
        ? eventDetails?.eventCreationVehicle?.startDate?.replace(/-/g, "/")
        : eventDetails?.eventCreationVehicle?.startDate
    ),
    "MM/dd/yy"
  )} - ${format(
    new Date(
      typeof eventDetails?.eventCreationVehicle?.endDate === "string"
        ? eventDetails?.eventCreationVehicle?.endDate?.replace(/-/g, "/")
        : eventDetails?.eventCreationVehicle?.endDate
    ),
    "MM/dd/yy"
  )}`;
};

export const getEventCreationVehicleName = eventCreationVehicle => {
  return `${eventCreationVehicle.vehicleNm}`;
};

export const arraivalDateMapper = {
  arrivalStart: "allowanceStartDate",
  arrivalEnd: "allowanceEndDate",
};
export const arrivalDateField = {
  arrivalStart: "arrivalStartDate",
  arrivalEnd: "arrivalEndDate",
};

export const formateVehicleDate = (
  date: string,
  isCurrent?: boolean,
  formValues?: any
) => {
  const { key, planValue, searchId, planEvent, planPendingObj } =
    formValues || {};
  let updatedDate = date;
  const planPendingDateVal = planPendingObj?.offerAllowances?.filter(
    offer => offer?.offerNumber === searchId
  )?.[0]?.allowances?.[0]?.[arrivalDateField?.[key]];
  if (
    [planValue, planPendingDateVal]?.includes("0001-01-01") &&
    !isCurrent &&
    arraivalDateMapper?.[key]
  ) {
    const objToCheck = [planValue]?.includes("0001-01-01")
      ? planEvent
      : planPendingObj;
    updatedDate =
      [planValue, planPendingDateVal]?.includes("0001-01-01") &&
      !isCurrent &&
      arraivalDateMapper?.[key]
        ? objToCheck?.offerAllowances?.filter(
            offer => offer?.offerNumber === searchId
          )?.[0]?.allowances?.[0]?.[arraivalDateMapper?.[key]]
        : date;
  }
  const dateObj = new Date(
    typeof updatedDate === "string"
      ? updatedDate?.replace(/-/g, "/")
      : updatedDate
  );

  const month = (dateObj?.getMonth() + 1)?.toString()?.padStart(2, "0");
  const day = dateObj?.getDate()?.toString()?.padStart(2, "0");
  const year = dateObj?.getFullYear()?.toString()?.slice(-2);
  return `${month}/${day}/${year}`;
};

/**
 * Generates a list of years centered around the current year, based on a given date.
 *
 * @param {Date} date - The input date from which to calculate the current year.
 * @param {number} count - (Optional) The number of years to include on each side of the current year. Default is 2.
 * @returns {number[]} An array of years centered around the current year.
 */
export const getUpdatedYearBasedOnCurrentDate = (date: any, count = 2) => {
  // Calculate the current year by adding 2 weeks to the provided date and formatting it.
  const dateVal = format(addWeeks(date, 2), "yyyy");
  const yearsList: number[] = [];
  const currentYear = parseInt(dateVal);

  // Generate a list of years, including `count` years before and after the current year.
  for (let i = currentYear - count; i <= currentYear + count; i++) {
    yearsList.push(i);
  }

  return yearsList;
};

export const getYearFromFormDate = (date: any) => {
  const formYear = date?.getFullYear();
  return formYear;
};

export const getSelectedStoreGroup = storeGroups => {
  const selectedStoreGroup = storeGroups.reduce((prev, curr) => {
    return +prev?.storeCount > +curr?.storeCount ? prev : curr;
  }, {});
  return selectedStoreGroup;
};

export const getDateInTimeFormat = (date: string) => {
  return new Date(date)?.getTime();
};

export const getPromoType = eventDetailsData => {
  if (Array.isArray(eventDetailsData?.pricing)) {
    const str = eventDetailsData?.pricing[0]?.promoType;
    return str?.replace(/_/g, " ");
  } else {
    return "";
  }
};

export const getRegularPrice = eventDetailsData => {
  if (Array.isArray(eventDetailsData?.promotionsList)) {
    return eventDetailsData?.promotionsList[0]?.promoDetails?.regularPrice;
  } else {
    return "";
  }
};

export const removeObjectsById = ({ formattedPpgData, selectedPPGs }) => {
  const commonIds = selectedPPGs.map(obj => obj.sourceProductGroupId);
  const filteredPPGs = formattedPpgData?.filter(
    obj => !commonIds.includes(obj.sourceProductGroupId)
  );
  return filteredPPGs;
};

export const checkForKey = obj => {
  return Object.prototype.hasOwnProperty.call(obj, "ppgName")
    ? obj?.ppgName
    : obj?.name;
};
/** format ppg name which added through CIC Ids */
export const formatCicPpgDisplayName = ppg => {
  let ppgNameToDisplay = "";
  if (ppg?.productGroupType === "CIC" && !ppg?.name?.includes("CIC")) {
    const ppgName = `CIC #${ppg.name}`;
    ppgNameToDisplay = ppgName.trim();
  } else if (ppg?.productGroupType !== "CIC") {
    ppgNameToDisplay = checkForKey(ppg);
  }
  return ppgNameToDisplay;
};
/** Persist CIC ids for edit mode in comma seperated values */
export const getCicValuesInEditMode = ppgs => {
  let cicInputValues: string[] = [];
  ppgs.forEach(ppg => {
    if (ppg?.productGroupType === "CIC") {
      const cicId = ppg.name.split("-")[0].trim();
      cicInputValues = [...cicInputValues, cicId];
    }
  });
  return cicInputValues.join(",");
};
export const removeInvalidCICIds = (cicIds, invalidItems) => {
  return cicIds.filter(cic => !invalidItems.includes(cic));
};
export const disablePPGorCICField = (ppgs, field = "cic") => {
  if (ppgs?.length) {
    let isCic = field === "cic";
    if (ppgs?.[0].productGroupType === "CIC") {
      isCic = !isCic;
    }
    return isCic;
  }
  return false;
};
export const removePPGs = (ppg, totalPPGs) => {
  const filteredPPGs = totalPPGs?.filter(p => {
    return ppg.sourceProductGroupId !== p.sourceProductGroupId;
  });
  return { filteredPPGs };
};
export const isCICTypePPGsAvailable = ppgs => {
  if (ppgs?.length) {
    return ppgs.filter(ppg => ppg.productGroupType === "CIC")?.length > 0;
  }
  return false;
};
export const getItemIds = (items, error) =>
  items
    .filter(item => item.resolveValidInd === error)
    .map(item => item.itemId)
    ?.join(",");

export const getUnResolvedCICItemIds = unresolvedItems => {
  const invalidReason = unresolvedItems[0]?.resolveValidInd;
  let unresolvedError = "";
  let unresolvedIds = [];
  if (invalidReason === DISPLAY_ITEM_FLAG_MIXED) {
    return { unresolvedError: DISPLAY_ITEM_FLAG_MIXED };
  } else if (invalidReason === VENDOR_NOT_LINKED) {
    unresolvedIds = getItemIds(unresolvedItems, VENDOR_NOT_LINKED);
    unresolvedError = VENDOR_NOT_LINKED;
  } else {
    unresolvedIds = getItemIds(unresolvedItems, SMIC_NOT_IN_ROLLOUT);
    unresolvedError = SMIC_NOT_IN_ROLLOUT;
  }
  return {
    unresolvedError,
    unresolvedIds,
  };
};

export const getUnitTypesOfAddedCICs = ppgs => {
  const unitType = ppgs?.filter(ppg => ppg.productGroupType === "CIC")?.[0]
    ?.unitType;
  return PPG_UNIT_TYPE_MAPPER?.[unitType]?.code;
};
