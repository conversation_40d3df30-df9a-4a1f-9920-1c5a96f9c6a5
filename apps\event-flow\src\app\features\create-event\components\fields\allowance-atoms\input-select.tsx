import Select, { Option } from "@albertsons/uds/molecule/Select";
import { FormFieldError } from "@me/util-form-wrapper";
import classNames from "classnames";
import { Control, useController, UseFormRegister } from "react-hook-form";

export interface IOption {
  [key: string]: string | object | undefined | null | number;
}

interface IFieldProps {
  registerField: string;
  label: string;
  required: boolean;
  errors: object;
}

interface IInputSelectProps {
  fieldProps: IFieldProps;
  register: UseFormRegister<any> | (() => void);
  control: Control<any, any> | undefined;
  options: IOption[];
  displayLabel: keyof IOption;
  error?: { message: string };
  placeholder?: string;
  disabled?: boolean;
  size?: "sm" | "md" | "lg";
  className?: string;
  parentClassName?: string;
  onChange: (option: IOption) => void;
  onClick?: any;
  isInvalidDate?: boolean;
  isSearchable?: boolean;
  displayLabelName?: string;
}

const InputSelect = ({
  fieldProps,
  register,
  control,
  options,
  displayLabel,
  placeholder,
  disabled,
  size,
  className,
  parentClassName,
  onChange,
  error,
  onClick,
  isInvalidDate,
  isSearchable = false,
  displayLabelName = "",
}: IInputSelectProps) => {
  const { registerField, label, required, errors: errorMessages } = fieldProps;
  const { field, formState } = useController({ name: registerField, control });
  const fieldErrorData = formState.errors?.[registerField] || error;

  const handleChange = (option: IOption) => {
    field.onChange(option?.[displayLabel]);
    onChange(option);
  };

  const isDuplicateStartWeek = () => {
    return registerField === "duplicate.startWeekVehicle" || false;
  };

  return (
    <>
      <div>
        <div className="flex font-bold gap-1" id="abs-input-select-div">
          <p id="abs-input-select-label">{label}</p>
          {required && <p className="text-sm text-left text-[#bf2912]">*</p>}
        </div>
      </div>
      <div
        className={`${isInvalidDate ? "border border-[red]" : ""} ${
          disabled ? "bg-gray-205" : ""
        } ml-[3px] ${parentClassName}
        `}
        onClick={e => (onClick && !disabled ? onClick(e, options) : null)}
        id={`abs-input-select-container`}
      >
        <Select
          {...register(registerField, {
            required: required ? errorMessages?.["required"]?.text : required,
          })}
          value={options?.find(
            (option: IOption) => option?.[displayLabel] === field.value
          )}
          placeholder={placeholder || "Select"}
          disabled={disabled}
          search={isSearchable}
          size={size || "md"}
          className={classNames(
            { [className as string]: !fieldErrorData?.message },
            "rounded periodScroll w-full inputSelect",
            { className },
            {
              "rounded border-[#BF2912] border-error border-2 w-full":
                !field.value && fieldErrorData?.message,
            }
          )}
          menuWidth={isDuplicateStartWeek() ? 500 : 0}
          itemText={isDuplicateStartWeek() ? displayLabelName : displayLabel}
          onChange={handleChange}
          multiple={false}
        >
          {options?.map((item, idx) => (
            <Option
              key={idx}
              item={item}
              disabled={item?.["disabled"] as undefined}
            />
          ))}
        </Select>
      </div>

      {!field.value && fieldErrorData?.message && (
        <FormFieldError
          error={fieldErrorData}
          classes={registerField.includes("suggestedVendorPaymentType")}
        />
      )}
    </>
  );
};

export default InputSelect;
