import { getDateByFormat, getLoggedInUserType } from "@me-upp-js/utilities";
import { has, isEmpty, pick } from "lodash";
import { TASK_EVENT_STATUS_TYPES } from "../../task-view/task-view-filter-config";
import { ALERT_EVENT_STATUS_TYPES } from "../../alert-view/alert-view-filter-config";
import {
  ALLOWANCES_EVENT_STATUS_TYPES,
  ALLOWANCE_DEFAULT_DATE_TYPE,
} from "../../allowance-view/allowance-view-filter-config";
import { PlanningPropsFromGlobalHeader } from "../../planning-view/planning-view-filter-config";
import { SEARCH_FILTER_OPTIONS } from "apps/promotion-management/src/app/config/dashboard-config";
import { processSearchText } from "./planning-sub-header-service";
import { getSearchDopdown } from "@me/promotion-management/common/helpers";
import { appendZeroes } from "@me/util-helpers";
import { getIsNationalEvent } from "apps/event-flow/src/app/features/event-types/event-types-helper";

export function generateCommonPayLoadForDashBoard({
  globalHeaderData,
  viewLevelFilterData,
  paging,
  view,
  propsFromGlobal,
  performanceApiData,
}: {
  globalHeaderData: any;
  viewLevelFilterData: any;
  paging: any;
  view: string;
  propsFromGlobal: PlanningPropsFromGlobalHeader;
  performanceApiData: any[];
}) {
  const { isNationalEvent, nationalRolesAvailable, nationalEventsFlagEnabled } = getIsNationalEvent("NDP");


  if (
    !globalHeaderData ||
    !propsFromGlobal?.required?.every(property =>
      has(globalHeaderData, property)
    )
  ) {
    return null;
  }
  const views = ["Tasks", "Alerts", "Allowances"];
  if (views?.includes(view) && globalHeaderData?.planEventSortType) {
    globalHeaderData = { ...globalHeaderData, planEventSortType: null };
  }
  let payload: any = pick(globalHeaderData, propsFromGlobal.required);
  if (view === "Planning") {
    // add custom planning data here
    payload.startDate = dateToISO(payload?.startDate);
    payload.endDate = dateToISO(payload?.endDate);

    payload.eventStatusTypes =
      viewLevelFilterData["event-status"]
        ?.filter(status => status.checked)
        .map(status => status.name) || [];
    payload.planEventSortType =
      viewLevelFilterData?.["planEventSortType"]?.eventKey;
  } else if (view === "Tasks" || view === "Alerts") {
    // add custom task/alert data here

    payload.taskType = view === "Tasks" ? "Task" : "Alert";
    payload.eventStatusTypes =
      view === "Tasks" ? TASK_EVENT_STATUS_TYPES : ALERT_EVENT_STATUS_TYPES;
    payload.taskStatusType =
      view === "Alerts"
        ? viewLevelFilterData?.taskStatusType?.eventKey
        : payload?.taskStatusType;
    payload.taskAlertStatusType = viewLevelFilterData?.taskAlertStatusType
      ? "Completed"
      : "Pending";
    payload.tasksSortType = viewLevelFilterData?.tasksSortType?.eventKey;
  } else if (view === "Allowances") {
    // add custom allowances data here
    payload.dateFilterType =
      payload.dateFilterType || ALLOWANCE_DEFAULT_DATE_TYPE;
    payload.offerPlanEventProjectionType =
      viewLevelFilterData?.offerPlanEventProjectionType?.eventKey;
    payload.eventStatusTypes =
      viewLevelFilterData["allowanceStatus"]
        ?.filter(status => status.isChecked)
        .map(status => status.label) || [];
    payload.startDate = dateToISO(payload?.startDate);
    payload.endDate = dateToISO(payload?.endDate);
  }
  if (!["Tasks", "Alerts"].includes(view)) {
    payload.timeScaleFilterEnum = globalHeaderData?.timeScaleFilterEnum;
  }

  propsFromGlobal.optional?.forEach(element => {
    if (globalHeaderData?.[element]) {
      payload[element] = globalHeaderData[element];
    }
  });
  payload.isAllGrpCatSelected = globalHeaderData?.isAllGrpCatSelected;
  payload.paging = paging;
  payload.userType = getLoggedInUserType();
  const vendNo = localStorage.getItem("vendorNum");
  const searchVal = sessionStorage.getItem("searchValue");
  const searchKey = sessionStorage.getItem("searchKey");

  const getDropdownKey = SEARCH_FILTER_OPTIONS.find(
    item => item?.name === searchKey
  );
  const validatedSearchData = searchVal
    ? processSearchText({
      searchFilterOption: getSearchDopdown(searchKey),
      searchText: searchVal,
    })
    : "";
  viewLevelFilterData = {
    ...viewLevelFilterData,
    ...(searchVal && {
      searchObj: {
        key: getDropdownKey?.eventKey,
        rawString: searchVal,
        value: validatedSearchData,
      },
    }),
  };

  const modifiedValue = appendZeroes(
    viewLevelFilterData?.searchObj?.value,
    viewLevelFilterData?.searchObj?.key
  );

  const searchValues = modifiedValue
    ? {
      [viewLevelFilterData?.searchObj?.key]: modifiedValue,
    }
    : {};

  const performanceData = getPerformanceDataForApi({
    globalHeaderData,
    performanceApiData,
  });

  if (payload?.isAllGrpCatSelected) {
    payload["smicCategoryCodes"] = [];
    payload["smicGroupCodes"] = [];
  }

  const nationalPattern = /^(?=.*-).*/;
  const matchResult = searchVal?.match(nationalPattern);
  const isNationalFormat = !!matchResult;
  const nationalEventSearchFilters = new Map();

  createNCDPID(
    searchKey,
    searchVal,
    nationalEventSearchFilters
  );
  if (!payload.isSearchEvent || !nationalEventsFlagEnabled) {
    delete payload.nationalEventSearchFilters
  }
  if (payload?.eventTypes) {
    payload.eventTypes = payload?.eventTypes?.some(item => ["NDP", "DP"].includes(item)) ? [...payload.eventTypes, "NCDP"] : payload.eventTypes;
  }
  payload.divisionIds = isNationalEvent && nationalRolesAvailable && nationalEventsFlagEnabled && (payload.eventTypes?.includes("NDP") || !payload?.eventTypes?.length) ? [...payload.divisionIds, "98"] : payload.divisionIds;
  payload = {
    ...payload,
    ...searchValues,
    isSearchEvent: !isEmpty(searchValues),
    ...(vendNo && { vendors: JSON.parse(vendNo) }),
    ...performanceData,
    ...(nationalEventSearchFilters?.size
      ? {
        nationalEventSearchFilters: Array.from(
          nationalEventSearchFilters.values()
        ),
      }
      : []),
  };
  return payload;
}

const createNCDPID = (
  sessionSearchKey,
  sessionSearchValue,
  nationalEventSearchFilters
) => {
  const selectedKeyConfig = SEARCH_FILTER_OPTIONS.find(
    option => option?.name === sessionSearchKey
  );

  const nationalKey: any = selectedKeyConfig?.nationalSearchKey;
  const valueSplit = sessionSearchValue.split(",").map(entry => entry?.trim());
  for (const value of valueSplit) {
    let splitWithDiv, name, divisionId;
    if (sessionSearchKey !== "Event Name") {
      splitWithDiv = value?.split("-").map(entry => entry?.trim());
      [name, divisionId] = splitWithDiv;
    } else {
      name = value, divisionId = null;
    }
    if (nationalEventSearchFilters?.has(name.trim())) {
      const nationalEvent = nationalEventSearchFilters?.get(name);
      nationalEvent?.childDivisionIds?.push(divisionId);
    } else {
      const childDivisionIds: any = [];
      if (divisionId) {
        childDivisionIds?.push(divisionId);
      }
      const nationaSearch: any = {
        [nationalKey?.trim()]: name?.trim(),
        ...(childDivisionIds?.length > 0 && { childDivisionIds }),
      };
      nationalEventSearchFilters?.set(name?.trim(), nationaSearch);
    }
  }
};

function dateToISO(date) {
  return getDateByFormat(date, "YYYY-MM-DD");
}

export function getPerformanceDataForApi({
  globalHeaderData,
  performanceApiData,
}) {
  let perf: any[] = [];

  const { performanceTypes, allowanceTypes } = globalHeaderData;

  if (allowanceTypes && allowanceTypes.length) {
    if (performanceTypes && performanceTypes.length) {
      perf = performanceApiData?.filter(
        performance =>
          performanceTypes?.includes(performance?.performance) &&
          allowanceTypes?.includes(performance?.allowanceType)
      );
    } else {
      perf = performanceApiData?.filter(performance =>
        allowanceTypes?.includes(performance?.allowanceType)
      );
    }
  } else {
    if (performanceTypes && performanceTypes.length) {
      perf = performanceApiData?.filter(performance =>
        performanceTypes?.includes(performance?.performance)
      );
    }
  }

  return {
    performanceTypes: perf?.map(performance => performance?.id),
  };
}
