import { useSelectorWrap } from "@me/data-rtk";
import { useDispatch } from "react-redux";
import {
  formatAmount,
  getAllowanceMapKey,
} from "../service/allowance/allowance-service";
import { usePostNationalAllowanceTempWorkDataMutation } from "../service/apis/allowance-api";
import { allowanceTempWorkHandler } from "../service/slice/allowance-temp-work-slice";
import { getObjectKeys } from "../service/allowance/allowance-stepper-service";
import { postBatchPayloadByDivisions } from "../components/cards/offer/offer-service";
import usePostApiCombinedData from "../components/cards/offer/national/hooks/usePostApiCombinedData";

interface AllowanceTempWorPayload {
  divisionIds?: string[];
  eventId?: string;
}
const useNationalAllowTempUpdate = () => {
  const dispatch = useDispatch();
  const { allowanceData: allowanceTempWorkInfo } =
    useSelectorWrap("allowance_temp_work")?.data || {};
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  // const { isAllowConvEnable } = useSelectorWrap("allow_conv_status_rn").data;
  const {
    data: { offerDivisions = [] },
  } = useSelectorWrap("national_offer_divisions") || {};

  const [postNationalAllowanceTempWork] =
    usePostNationalAllowanceTempWorkDataMutation();
  const { postCombinedData, state } = usePostApiCombinedData();

  const { id: eventId = "" } = eventDetailsData || {};
  
  async function saveTempWorkData(
    allowancesTempData,
    payload = { divisionIds: [], eventId: "" }
  ) {
    const {
      divisionIds: payloadDivIds = [],
      eventId: payloadEventId,
    }: AllowanceTempWorPayload = payload || {};
    try {
      const payload = postBatchPayloadByDivisions(
        allowancesTempData,
        allowancesTempData?.length
          ? allowancesTempData?.map(item => item?.divisionId)
          : offerDivisions?.length
          ? offerDivisions
          : payloadDivIds,
        {
          URL_PARAM:
            eventId || payloadEventId || allowancesTempData?.[0]?.planEventId,
          queryParams: {},
        }
      );

      const updatedTempData = await postCombinedData(
        postNationalAllowanceTempWork,
        payload
      );
      const { data: updatedData, error } = updatedTempData;

      !error &&
        dispatch(
          allowanceTempWorkHandler({
            allowanceData: updatedData,
          })
        );
      return updatedTempData;
    } catch (e) {
      console.error(e);
      return { data: [], error: e };
    }
  }

  const getNationalTempworkObject = primeData => {
    const {
      allowanceTypeKey,
      allowancePerformanceId,
      allowancePerformances,
      offerAllowancesGroupInfoMap = {},
      createInd,
      allowanceToBeCreatedOption,
      amountRefreshCheck,
      overrideHeaderFlatAmt,
      productSources,
      showStoreSelection,
      overrideStoreGroups,
      vehicleDatesMap,
      additionalDates,
      allowKey,
      isEditEnable,
      offerMapKey,
      divisionId,
      planEventId,
      planEventType,
    } = primeData;

    let tempWorkData: any = {};
    if (allowanceTempWorkInfo?.length) {
      tempWorkData = allowanceTempWorkInfo?.find(
        tempWork => tempWork?.divisionId === divisionId
      );
    }

    const allowancesMap = getNationalAllowanceMapData(
      additionalDates || [],
      vehicleDatesMap,
      isEditEnable,
      offerMapKey,
      amountRefreshCheck,
      isEditEnable,
      tempWorkData?.allowanceTypeSpecification?.[allowKey]?.allowancesMap
    );

    return {
      ...tempWorkData,
      allowanceType: allowanceTypeKey,
      allowanceTypeSpecification: {
        [allowKey]: {
          ...tempWorkData?.allowanceTypeSpecification?.[allowKey],
          allowanceType: allowanceTypeKey,
          allowancePerformanceId,
          allowancePerformances,
          offerAllowancesGroupInfoMap,
          createInd: allowanceToBeCreatedOption?.createIndex || createInd,
          allowancesMap,
          ...(amountRefreshCheck && {
            overrideHeaderFlatAmt: formatAmount(overrideHeaderFlatAmt),
          }),
          ...(showStoreSelection && {
            overrideStoreGroups,
          }),
          productSources,
          vehicleDatesMap,
        },
      },
      planEventType,
      tempWorkAllowanceId: tempWorkData?.tempWorkAllowanceId,
      divisionId,
      planEventId: planEventId || allowanceTempWorkInfo?.[0]?.planEventId,
    };
  };

  const getNationalAllowanceMapData = (
    datesArray,
    vehicleDatesMap,
    isEditEnable = false,
    offerMapKey: string,
    isAmountsIncluded = false,
    isSavePrevious = false,
    previousData = {}
  ) => {
    const defaultValuesForAllowance = {
      leadDistributorMode: null,
      leadDistributorInfos: [],
      leadDistributorInd: false,
      includeInd: true,
      createAllowInd: true,
    };

    const allowMapData = isSavePrevious ? previousData : {};

    const allowanesOffsetDates =
      isEditEnable && allowMapData?.[offerMapKey]?.length // Edit All
        ? allowMapData?.[offerMapKey]?.map((values, index) => {
            return {
              ...values,
              ...datesArray?.[index],
              vehicleId: vehicleDatesMap?.[offerMapKey]?.vehicleId,
              finalizedAmountsInd: isAmountsIncluded,
              allowanceBillingInfo: values?.allowanceBillingInfo,
            };
          })
        : datesArray?.length
        ? datesArray.map(values => {
            // Case Scan and S2S Create
            return {
              ...values,
              finalizedAmountsInd: isAmountsIncluded,
              ...defaultValuesForAllowance,
            };
          })
        : allowMapData?.[offerMapKey]?.map(values => {
            //  HF IF Create
            return {
              ...values,
              vehicleId: vehicleDatesMap?.[offerMapKey]?.vehicleId,
              finalizedAmountsInd: isAmountsIncluded,
              ...defaultValuesForAllowance,
            };
          });

    return {
      ...allowMapData,
      [offerMapKey]: allowanesOffsetDates || [],
    };
  };

  async function saveNationalAllowancePrimeSectionData(tempWorks) {
    return await saveTempWorkData(tempWorks);
  }

  async function saveAllowanceAmountSectionData({
    updatedTempwork,
    divisionIds,
    eventId,
  }) {
    return await saveTempWorkData(updatedTempwork, { divisionIds, eventId });
  }

  function checkTempDefaultDataAvailablity() {
    if (
      allowanceTempWorkInfo === undefined ||
      !getObjectKeys(allowanceTempWorkInfo?.[0])?.length
    ) {
      return;
    }
  }

  async function addNationalAllowancesBillingInformation({
    allowances,
    offerAllowanceGroup,
    isLastStep,
  }) {
    checkTempDefaultDataAvailablity();

    const allowancesData = setNationalAllowancesInfo(
      allowances,
      offerAllowanceGroup
    );
    return await saveTempWorkData(allowancesData);
  }

  const setNationalAllowancesInfo = (
    allowances,
    offerAllowanceGroup,
    isDatesStep = false,
    vehicleDatesMap = null
  ) => {
    const allowancesData = allowanceTempWorkInfo || [];

    return allowancesData?.map(item => {
      const { allowanceTypeSpecification: allowanceTypeSpecificationValue } =
        item || {};

      const allowanceTypeSpecification = {
        ...allowanceTypeSpecificationValue,
      };
      const allowanceName = getAllowanceMapKey(item?.allowanceType) || "";
      const selectedAllowances = allowances?.filter(
        allowance => allowance?.divisionId === item?.divisionId
      );
      const allowancesMap = {
        ...allowanceTypeSpecification?.[allowanceName]?.allowancesMap,
        [offerAllowanceGroup]: [...(selectedAllowances || [])],
      };

      if (isDatesStep) return allowancesMap;

      const updatedItem = {
        ...item,
        allowanceTypeSpecification: {
          ...allowanceTypeSpecification,
          [allowanceName]: {
            ...allowanceTypeSpecification?.[allowanceName],
            allowancesMap,
            ...(vehicleDatesMap ? { vehicleDatesMap } : {}),
          },
        },
      };
      return updatedItem;
    });
  };

  return {
    saveNationalAllowancePrimeSectionData,
    getNationalTempworkObject,
    getNationalAllowanceMapData,
    saveAllowanceAmountSectionData,
    addNationalAllowancesBillingInformation,
    isSuccess: !state?.error && !!state?.data?.length,
    isError: !!state?.error,
    error: state?.error,
    isLoading: state?.loading,
  };
};

export default useNationalAllowTempUpdate;
