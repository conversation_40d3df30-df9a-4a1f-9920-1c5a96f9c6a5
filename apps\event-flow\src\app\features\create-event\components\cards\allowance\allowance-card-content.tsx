/* eslint-disable no-extra-boolean-cast */
import Button from "@albertsons/uds/molecule/Button";
import Card from "@albertsons/uds/molecule/Card";
import Stepper from "@albertsons/uds/molecule/Stepper";
import { useSelectorWrap } from "@me/data-rtk";
import { RBAC } from "albertsons-react-rbac";
import { Edit2 as EditIcon, Eye } from "lucide-react";
import {
  FunctionComponent,
  Suspense,
  lazy,
  useEffect,
  useState,
  useRef,
} from "react";
import { useFormContext } from "react-hook-form";
import { useDispatch } from "react-redux";
import efConstants from "../../../../../shared/ef-constants/ef-constants";
import { EVENT_ALLOWANCE } from "../../../constants/fields/allowance/allowance-steps-config";
import { useGetAllowancePerformanceById } from "../../../hooks/useGetAllowancePerformaceById";
import useSetOfferAllowanceGroup from "../../../hooks/useSetOfferAllowanceGroup";
import {
  checkIsPerfChangable,
  checkObjectHasKeys,
  getAllowanceFormRegisterKey,
  getAllowanceMapKey,
  getDefaultAllowanceType,
  getOfferFormRegisterKey,
  getOfferKey,
  getProductSourceByOfferKey,
  getQueryParams,
  getSteppers,
  isHfOrIfType,
  removeParams,
} from "../../../service/allowance/allowance-service";
import {
  getObjectKeys,
  saveFormAndGetStepperValue,
} from "../../../service/allowance/allowance-stepper-service";
import { useGetAllowanceTempWorkDataQuery } from "../../../service/apis/allowance-api";
import {
  setAllowConvEnable,
  setAllowTypeChange,
  setAllowanceFormInfo,
} from "../../../service/slice/allowance-details-slice";
import { allowanceTempWorkHandler } from "../../../service/slice/allowance-temp-work-slice";
import "./allowance-card-content.scss";
import { RenderStates } from "@me/ui-render-states";
import { byPassOfferAllowanceHandler } from "../../../service/slice/event-detail-slice";
import { eventProgressDataHandler } from "../../../service/slice/event-progress-slice";
import { Info } from "lucide-react";
import Tooltip from "@albertsons/uds/molecule/Tooltip";
import {
  isAllowanceFeatureEnabled,
  isComingFromtask,
} from "@me-upp-js/utilities";
import { checkIsAllowConversionEnable } from "@me/util-helpers";
import { saveToSessionStorage } from "../../../../../shared/helpers/event-flow-helpers";

const {
    ALLOWANCE_SCREEN_TYPES,
    BYPASS_ALLOWANCE_LABEL,
    DEFAULT_ALLOWANCE_TYPE,
    ITEM_AMOUNTS_SUMMARIZED_KEY,
    ALLOWANCE_TYPES: { CASE },
  } = efConstants,
  { DP, AO } = ALLOWANCE_SCREEN_TYPES;

interface IAllowanceCardContentProps {
  cardConfiguration?: any;
  route?: any;
  isNew?: any;
  cardItemIndex: number;
  cardIndex: number;
  control?: any;
  getValues?: any;
}

const AllowanceCardContent: FunctionComponent<IAllowanceCardContentProps> = ({
  control,
  cardIndex,
  cardItemIndex,
  isNew,
  route,
  cardConfiguration,
  getValues,
}) => {
  const { setValue } = useFormContext();
  const dispatch = useDispatch(),
    { isCancelled, isSaved, offerKey, taskType, isAllowTypeChange } =
      getQueryParams();

  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { allowanceData: tempAllowanceData, isNewAllowance = false } =
    useSelectorWrap("allowance_temp_work").data;
  const { data: allowanceFormDetails } = useSelectorWrap("allowance_form_data");
  const { data: allowanceAdditionalInfo } = useSelectorWrap(
    "allowance_stepper_additional_details"
  );
  const { editCardConfig: isEditEnableValue } = useSelectorWrap(
    "offer_card_configutation_rn"
  ).data;

  const { data: allowancePerfData } = useSelectorWrap(
    "allowance_type_performance_data"
  );

  const { data: productSourceData } = useSelectorWrap(
    "allowance_product_sources_rn"
  );
  const { isEventVehicleChangedInPending, isEventVehicleChangedInHistory } =
    useSelectorWrap("check_event_vehicledates_rn").data;
  const isEditEnable = isEditEnableValue?.[cardIndex] || false;
  const allowanceRegisterField = getAllowanceFormRegisterKey(
    cardIndex,
    cardItemIndex
  );
  const editKey = `${allowanceRegisterField}.edit`;
  const offerRegisterField = getOfferFormRegisterKey(cardIndex);
  const {
    offerAllowances = [],
    divisionIds = [],
    id = "",
    eventType = "",
    inValidAllowances = [],
    negotiationSimsVendors,
  } = eventDetailsData || {};
  const TIMEOUT_INTERVAL = 15000;

  const [step, setStep] = useState<number>(0);
  const [steppersShow, setSteppersShow] = useState<any>([]);
  const isAllowanceTempLoaded = useRef(false);
  const [allowanceTypeValue, setAllowanceType] = useState(
    getDefaultAllowanceType()
  );
  const [allowanceToBeCreated, setAllowanceToBeCreated] = useState("");
  const isTempGetTriggered = useRef(false);

  const allowanceFormData =
      allowanceFormDetails?.allowanceFormData?.[allowanceRegisterField],
    { createInd, allowanceType = DEFAULT_ALLOWANCE_TYPE } =
      allowanceFormData?.allowanceTypeAndPerformance || {};
  const skipStep = allowanceAdditionalInfo?.skipStep?.[allowanceRegisterField];
  const allowanceId = getValues(`${allowanceRegisterField}.id`);
  const offerId = getValues(`${offerRegisterField}.id`);

  const editAllowanceName = tempAllowanceData?.allowanceType,
    allowName = getAllowanceMapKey(editAllowanceName) || "",
    offerGroupkeys =
      tempAllowanceData?.allowanceTypeSpecification?.[allowName]?.allowancesMap,
    defaultCreateInd =
      allowancePerfData?.performanceConfig?.[
        isEditEnable ? `${allowanceRegisterField}.edit` : allowanceRegisterField
      ]?.defaultCreateInd ||
      allowancePerfData?.performanceConfig?.[allowanceRegisterField]
        ?.defaultCreateInd;

  const { getPerformanceById } = useGetAllowancePerformanceById(
    getValues,
    setValue,
    isEditEnable ? editKey : allowanceRegisterField,
    isEditEnable
  );

  const { fetchAllowanceToBeCreated, isLoading: isProductGroupsLoading } =
    useSetOfferAllowanceGroup({
      allowanceRegisterField: isEditEnable ? editKey : allowanceRegisterField,
      getValues,
      setValue,
      allowanceTypeHandler: allowanceTypeAndAllowToBeCreatedHandler,
      saveAndContinueHanlder,
      isEdit: isEditEnable,
    });

  const {
    data: allowanceTempDataFromGet = null,
    isFetching: isTempworkGetCreateLoaded,
  } = useGetAllowanceTempWorkDataQuery(
    {
      URL_PARAM: id,
      queryParams: {},
    },
    {
      skip: isNewAllowance || allowanceId || !id || isEditEnable,
    }
  );

  useEffect(() => {
    const isAllowConvEnable =
      checkIsAllowConversionEnable(
        getValues(offerRegisterField)?.origOfferNumber
      ) && !(isEventVehicleChangedInPending || isEventVehicleChangedInHistory);
    dispatch(
      setAllowConvEnable({
        isAllowConvEnable,
      })
    );
  }, [isEventVehicleChangedInPending, isEventVehicleChangedInHistory]);

  useEffect(() => {
    allowanceType && setAllowanceType(allowanceType);
  }, [allowanceType]);

  useEffect(() => {
    if (!getObjectKeys(tempAllowanceData).length) {
      setStep(0);
    }
  }, [tempAllowanceData]);

  const saveAllowanceFormData = (
    formInitialData: object | null | undefined
  ) => {
    dispatch(
      setAllowanceFormInfo({
        allowanceFormData: {
          [allowanceRegisterField]: {
            ...allowanceFormData,
            ...formInitialData,
          },
        },
      })
    );
  };

  useEffect(() => {
    if (isTempworkGetCreateLoaded || checkObjectHasKeys(tempAllowanceData))
      isTempGetTriggered.current = true;
  }, [isTempworkGetCreateLoaded]);

  useEffect(() => {
    const allowanceFormCreateInitialLoad = async () => {
      const tempData = checkObjectHasKeys(tempAllowanceData)
        ? tempAllowanceData
        : allowanceTempDataFromGet || {};

      if (
        !isNewAllowance &&
        checkObjectHasKeys(tempData) &&
        !isAllowanceTempLoaded.current &&
        isTempGetTriggered.current &&
        !tempData?.offerNumber &&
        (step === 0 || isCancelled || isSaved)
      ) {
        dispatch(
          allowanceTempWorkHandler({
            allowanceData: tempData,
          })
        );
        isTempGetTriggered.current = false;
        if (!allowanceId) {
          isAllowanceTempLoaded.current = true;
          const reloadInfo = {
            cancelState: { isCancelled, offerKey },
            allowanceRegisterField,
            getValues,
            setValue,
            getPerformanceById,
            fetchAllowanceToBeCreated,
            eventId: id,
          };
          setStep(
            await saveFormAndGetStepperValue(
              eventType,
              tempData,
              reloadInfo,
              false
            )
          );
          saveAllowanceFormData(getValues(allowanceRegisterField));
          if (isCancelled || isSaved) {
            setTimeout(() => {
              removeParams();
              saveToSessionStorage(ITEM_AMOUNTS_SUMMARIZED_KEY, false);
            }, TIMEOUT_INTERVAL);
          }
        }
      }
    };
    allowanceFormCreateInitialLoad();
  }, [allowanceTempDataFromGet, isTempGetTriggered.current]);

  useEffect(() => {
    const allowanceFormEditInitialLoad = async () => {
      if (
        !!allowanceId &&
        productSourceData?.productSources?.length &&
        tempAllowanceData !== undefined &&
        Object.keys(tempAllowanceData)?.length &&
        isEditEnable &&
        !isAllowanceTempLoaded.current
      ) {
        isAllowanceTempLoaded.current = true;
        setValue(editKey, null);
        const reloadInfo = {
          cancelState: { isCancelled, offerKey },
          saveState: { isSaved, offerKey },
          allowanceRegisterField: editKey,
          getValues,
          setValue,
          getPerformanceById,
          fetchAllowanceToBeCreated,
          eventId: id,
        };
        setStep(
          await saveFormAndGetStepperValue(
            eventType,
            tempAllowanceData,
            reloadInfo,
            true,
            inValidAllowances?.includes(offerId),
            isComingFromtask(taskType, eventDetailsData) && isEditEnable
          )
        );
        saveAllowanceFormData(getValues(editKey));
        if (isCancelled || isSaved) {
          if (isAllowTypeChange && isAllowanceFeatureEnabled) {
            dispatch(
              setAllowTypeChange({
                isAllowanceTypeChanged: { [cardIndex]: true },
              })
            );
          }
          setTimeout(() => {
            removeParams();
            saveToSessionStorage(ITEM_AMOUNTS_SUMMARIZED_KEY, false);
          }, TIMEOUT_INTERVAL);
        }
      }
    };
    allowanceFormEditInitialLoad();
  }, [allowanceId, tempAllowanceData, productSourceData?.productSources]);

  useEffect(() => {
    let editInfo = { productGroups: [], allowanceToBeCraetedLabel: "" };
    if (isEditEnable) {
      editInfo = getProductSourceByOfferKey(
        getObjectKeys(offerGroupkeys)?.[0],
        editAllowanceName
      );
    }

    const steppersShowData: string[] = getSteppers(
      eventType,
      isEditEnable ? editAllowanceName : allowanceTypeValue,
      isEditEnable ? editInfo?.allowanceToBeCraetedLabel : allowanceToBeCreated,
      isEditEnable
        ? isHfOrIfType(editAllowanceName) ||
          defaultCreateInd === CASE.createInd[3]
          ? productSourceData?.productSources
          : editInfo?.productGroups
        : productSourceData?.productSources,
      defaultCreateInd
    );
    setSteppersShow(steppersShowData);
  }, [
    allowanceTypeValue,
    allowanceToBeCreated,
    productSourceData,
    eventType,
    isEditEnable,
    offerGroupkeys,
    editAllowanceName,
    defaultCreateInd,
  ]);

  function saveAndContinueHanlder(stepper: number) {
    setStep(step => stepper + 1);
  }

  function allowanceTypeAndAllowToBeCreatedHandler(type, toBeCreated) {
    type && setAllowanceType(type?.toUpperCase());
    setAllowanceToBeCreated(toBeCreated);
  }

  const isStepEditable = (stepperElement, stepper) => {
    if (stepper === "Allowance Type & Performance")
      return !checkIsPerfChangable(createInd);
    return stepperElement.edit.isView;
  };

  const editTag = (stepperElement, stepper) => {
    return (
      <div
        className="flex items-center justify-end abs-allowance-card-content-edit-tag"
        id="abs-allowance-card-content-view-edit"
      >
        <span className="mr-5 font-semibold text-[#1B6EBB]">
          {isEditEnable && isStepEditable(stepperElement, stepper) ? (
            <>
              {" "}
              <Eye
                width={16}
                height={16}
                color="#1B6EBB"
                className="inline-block mb-[3px] mr-[8px]"
              />{" "}
              View
            </>
          ) : (
            <>
              <EditIcon
                width={16}
                height={16}
                color="#1B6EBB"
                className="inline-block mb-[3px] mr-[8px]"
              />
              Edit
            </>
          )}
        </span>
      </div>
    );
  };

  const getSubLevelInput = (fields: any, offerTypeKey: string) => {
    const subLevelInput = {
      subLevelInputValues: allowanceFormData,
      fields,
    };
    if (offerTypeKey) {
      subLevelInput.fields =
        fields?.allowanceSpecificFields?.[allowanceTypeValue]?.[
          getOfferKey(offerTypeKey, allowanceTypeValue, createInd)
        ] || fields?.allowanceSpecificFields?.DEFAULT;
    }

    return subLevelInput;
  };
  const handleByPassAllowanceClick = () => {
    dispatch(
      byPassOfferAllowanceHandler({
        isOfferBypassed: true,
      })
    );
    dispatch(
      eventProgressDataHandler({
        selected: "Promotion",
        byPassedSections: ["Allowance"],
      })
    );
  };

  const showByPassAllowanceButton = () => {
    return (
      !getObjectKeys(tempAllowanceData?.allowanceTypeSpecification).length &&
      !isEditEnable &&
      eventType === DP.key &&
      !offerAllowances?.length
    );
  };

  const renderHtml = (
    <Card.Content>
      <RBAC
        divisionIds={divisionIds}
        permissionsOnly={["PROMOTION_ALLOWANCE_MGMT_EDIT"]}
        simsVendors={negotiationSimsVendors}
      >
        <Stepper
          step={step}
          onChange={setStep}
          className={`${
            skipStep ? `skipStep-${skipStep + 1}` : ""
          } allowance-stepper-${step}`}
          skippable={isEditEnable}
        >
          {steppersShow?.map((stepper: any, index) => {
            const stepperElement = EVENT_ALLOWANCE[stepper];
            const container = stepper
              .replace(/[&]/g, "")
              .replace(/ /g, "-")
              .replace(/--/g, "-")
              .toLowerCase();
            const Component = lazy(
              () => import(`./stepper/${container}/${container}`)
            );
            const subLabel =
              stepperElement?.sublabel(
                getSubLevelInput(
                  stepperElement?.fields,
                  stepperElement?.offerTypeKey
                )
              ) || "";
            return (
              <Stepper.Step
                key={`${cardIndex}${cardItemIndex}`}
                label={stepperElement.label}
                tag={editTag(stepperElement, stepper)}
                sublabel={step !== index ? subLabel : ""}
              >
                <div
                  className="flex justify-start items-start flex-grow-0 flex-shrink-0 relative gap-2.5 pt-1.5 pr-[0px] bottom-[39px] abs-allowance-card-content-stepper-step"
                  id="abs-allowance-card-content-flex"
                >
                  <div
                    className="flex-grow-0 flex-shrink-0 w-full h-4 relative"
                    id="abs-allowance-card-content-flex-grow"
                  >
                    <p
                      className="absolute right-0 top-0 text-sm italic"
                      id="abs-allowance-card-content-text"
                    >
                      <span className="text-sm italic text-right text-[#bf2912]">
                        *
                      </span>
                      <span className="text-sm italic text-right text-black">
                        required fields
                      </span>
                    </p>
                  </div>
                </div>
                <Suspense fallback={null}>
                  <Component
                    step={step}
                    control={control}
                    stepperElement={stepperElement}
                    offerIndex={cardIndex}
                    allowanceIndex={cardItemIndex}
                    allowanceTypeHandler={
                      allowanceTypeAndAllowToBeCreatedHandler
                    }
                    saveAndContinueHanlder={saveAndContinueHanlder}
                    isLastStep={steppersShow?.length - 1 === step}
                    isEditEnable={isEditEnable}
                    isInvalidDate={inValidAllowances?.includes(offerId)}
                  />
                </Suspense>
              </Stepper.Step>
            );
          })}
        </Stepper>
        <div
          className="flex-grow-0 flex-shrink-0 w-full h-px  bg-[#fdfdff] border-t border-r border-b-0 border-l border-[#c8daeb]"
          id="abs-allowance-card-content-save-event"
        ></div>
        {!isEditEnable && eventType !== AO.key ? (
          isNew ? null : (
            <Button
              width={340}
              variant="tertiary"
              className="whitespace-nowrap lg:w-[302px]"
            >
              "Save Event Details & Add Allowance"
            </Button>
          )
        ) : null}
        {showByPassAllowanceButton() ? (
          <div
            className="flex justify-start items-center abs-allowance-card-content-bypass-allowance"
            id="abs-allowance-card-content-bypass-allowance"
          >
            <Button
              onClick={handleByPassAllowanceClick}
              variant="tertiary"
              width={150}
            >
              <span className="font-medium">Bypass Allowance</span>
            </Button>
            <span>
              <Tooltip zIndex={10} anchor={"top"} variant="light">
                <Tooltip.Popover>
                  <span className="flex flex-col m-4 text-xs text-[#5a697b] whitespace-nowrap">
                    {BYPASS_ALLOWANCE_LABEL}
                  </span>
                </Tooltip.Popover>
                <Info color="#1B6EBB" size={15} />
              </Tooltip>
            </span>
          </div>
        ) : null}
      </RBAC>
    </Card.Content>
  );

  const isApiLoading = () => {
    return isTempworkGetCreateLoaded || isProductGroupsLoading;
  };

  const renderDetails = {
    isApiLoading: isApiLoading(),
    isPageLevelSpinner: true,
    isRenderMainHtml: true,
    renderHtml,
  };
  return <RenderStates details={renderDetails} />;
};

export default AllowanceCardContent;
