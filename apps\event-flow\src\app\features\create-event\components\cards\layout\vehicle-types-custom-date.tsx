import Select from "@albertsons/uds/molecule/Select";
import { Controller, useFormContext, useWatch } from "react-hook-form";
import { useState, useEffect } from "react";
import VehicleMenu from "./vehicle-menu";
import { FormFieldError } from "@me/util-form-wrapper";
import { DateObject } from "albertsons-react-multi-date-picker";
import { DateFormat, dateFormat } from "@me/util-helpers";
import InfoGraphics from "../../../../event-types/components/info-graphics/info-graphics";
import { INFO_GRAPHICS } from "@me-upp-js/utilities";

interface IVehicleTypesProps {
  fieldProps?: any;
  onChange?: any;
  setVehicle?: any;
  vehicleTypeProps?: any;
  isEditEvent?: any;
  isDivisionChanged?: any;
  control?: any;
  dynamicRegField?: any;
  minDate?: Date | string | number | DateObject;
  maxDate?: Date | string | number | DateObject;
  isInvalid?: boolean;
  vehicleTypeError?: boolean;
  isAllowanceForm?: boolean;
  disabled?: boolean;
  show?: boolean;
  hideVehicles?: boolean;
  setShow?: (value: boolean) => void;
  isNCDP?: boolean;
}

const VehicleTypes = ({
  onChange,
  fieldProps,
  setVehicle,
  vehicleTypeProps,
  isEditEvent,
  isDivisionChanged,
  control,
  dynamicRegField,
  minDate,
  maxDate,
  isInvalid,
  vehicleTypeError,
  isAllowanceForm = false,
  disabled,
  show = false,
  hideVehicles,
  setShow = value => value,
  isNCDP = false,
}: IVehicleTypesProps) => {
  const items = [];

  const {
    formState: { errors },
  } = useFormContext();
  const [optionValue, setOptionValue] = useState(
    vehicleTypeProps || { name: "" }
  );

  useEffect(() => {
    if (vehicleTypeError) {
      setOptionValue(vehicleTypeProps || { name: "" });
    }
  }, [vehicleTypeProps, vehicleTypeError]);

  const watchVehicleType = useWatch({ name: "vehicleType" });

  const onChangeHandler = (element, type, customDateType) => {
    const data =
      type === "Vehicle"
        ? {
            name: element?.vehicleTypDesc,
            id: element.id ? element.id : element.vehicleTypeId,
            type,
          }
        : {
            name: "Custom Date",
            customDates:
              dateFormat(element[0], DateFormat["YYYY-MM-DD"]) +
              " - " +
              dateFormat(element[1], DateFormat["YYYY-MM-DD"]),

            id: customDateType?.id,
            type,
          };
    setSelectedValue(data);
    onChange(data);
  };

  const setSelectedValue = element => {
    setOptionValue({
      name: element?.name,
      customDates: element?.customDates ? element?.customDates : null,
      value: element?.id,
      selectedType: element?.type,
    });
  };

  const { getValues, setValue, control: formControl } = useFormContext();
  const [timeframeStart, setTimeframeStart] = useState("");
  const [timeframeEnd, setTimeframeEnd] = useState("");
  const dataFetchedFromPeriscope = getValues("dataFetchedFromPeriscope");
  useEffect(() => {
    if (getValues("vehicleType") === "") {
      setOptionValue({ name: "" });
    }
  }, [watchVehicleType]);

  useEffect(() => {
    const vehicleType = isAllowanceForm
      ? vehicleTypeProps
      : getValues(`${dynamicRegField}.vehicleType`) ||
        getValues("eventCreationVehicle.vehicleType");

    if (vehicleType) {
      setOptionValue({
        ...vehicleType,
        name:
          vehicleType?.type === "CustomDate"
            ? "Custom Date"
            : vehicleType?.vehicleTypDesc,
        value:
          vehicleType?.type === "CustomDate"
            ? vehicleType?.id
            : vehicleType?.vehicleTypeId,
        customDates:
          vehicleType?.type === "CustomDate"
            ? customDateName(vehicleType)
            : null,
      });
    }
  }, [
    isEditEvent,
    dataFetchedFromPeriscope,
    isAllowanceForm,
    vehicleTypeProps,
    getValues,
    dynamicRegField,
  ]);

  const customDateName = vehicleType => {
    if (vehicleType.customDates) return vehicleType.customDates;
    const vehicle = isAllowanceForm
      ? vehicleTypeProps
      : getValues(dynamicRegField);
    if (vehicle.vehicleType.customDates) return vehicle.vehicleType.customDates;
    const customDates =
      new Date(vehicle.startDate).toLocaleDateString("en-US") +
      " - " +
      new Date(vehicle.endDate).toLocaleDateString("en-US");
    return customDates;
  };

  const onVehicleMenuCloseHandler = event => {
    const elm = (event.target as HTMLElement).closest(".z-index-dynamic-class");
    !elm?.classList?.contains("z-[9]") &&
      elm?.classList?.add("z-[9]", "z-index-added");
    setShow(false);
  };

  return (
    <div className="w-full" id="abs-vehicle-types-custom-date-container">
      <div id="abs-vehicle-types-custom-date-cont1">
        <div
          className="flex font-bold gap-1"
          id="abs-vehicle-types-custom-date-label-cont"
        >
          <label
            className={"whitespace-nowrap"}
            htmlFor={`${fieldProps.label}`}
            id="abs-vehicle-types-custom-date-label"
          >
            {fieldProps.label}
          </label>
          {fieldProps.required ? (
            <p
              className="text-sm text-left text-[#bf2912]"
              id="abs-vehicle-types-custom-date-required"
            >
              *
            </p>
          ) : null}
          <span className="flex items-center">
            <InfoGraphics
              anchor="top"
              variant="light"
              registerField={
                fieldProps?.registerField ===
                "eventCreationVehicle.vehicleType.vehicleTypeId"
              }
              classNameInfo="flex-col m-4 text-xs text-[#5a697b]"
              hoverContent={
                INFO_GRAPHICS[`${fieldProps?.label}`]?.INFO_GRAPHICS_LABEL
              }
              size="14"
              id="abs-vehicle-types-custom-date-info-graphics"
            />
          </span>
        </div>
      </div>
      <div
        className="min-w-[200px]"
        id="abs-vehicle-types-custom-date-vehicle-label-cont"
      >
        <VehicleLabel
          show={show}
          setShow={setShow}
          errors={errors}
          control={control}
          formControl={formControl}
          fieldProps={fieldProps}
          isInvalid={isInvalid}
          items={items}
          optionValue={optionValue}
          setSelectedValue={setSelectedValue}
          disabled={isNCDP || disabled}
        />
        {show && (
          <div
            onClick={e => e.stopPropagation()}
            id="abs-vehicle-types-custom-date-vehicle-menu"
            className="relative z-[100]"
          >
            <VehicleMenu
              onClose={event => onVehicleMenuCloseHandler(event)}
              show={show}
              setTimeframeStart={setTimeframeStart}
              setTimeframeEnd={setTimeframeEnd}
              minDate={minDate}
              maxDate={maxDate}
              optionValue={optionValue}
              dynamicRegField={dynamicRegField}
              // setVehicle={setVehicle}
              fieldProps={fieldProps}
              onChange={onChangeHandler}
              hideVehicles={hideVehicles}
            />
          </div>
        )}
      </div>
    </div>
  );
};

function VehicleLabel({
  // props,
  errors,
  show,
  setShow,
  control,
  formControl,
  fieldProps,
  isInvalid,
  items,
  optionValue,
  setSelectedValue,
  disabled,
}) {
  const onCliCkAddIndex = event => {
    event?.stopPropagation();
    (event.target as HTMLElement)
      .closest(".z-index-dynamic-class")
      ?.classList.add("z-[9]");
    setShow(!show);
  };
  const closeAndRemoveIndex = () => {
    document
      .querySelectorAll(".z-index-dynamic-class")
      ?.forEach(e => e.classList.remove("z-[9]"));
    setShow(false);
    document
      .querySelectorAll(".truncate")
      ?.forEach(elm => elm.removeEventListener("click", closeAndRemoveIndex));
    window.removeEventListener("click", closeAndRemoveIndex);
  };
  window.addEventListener("click", closeAndRemoveIndex);

  return (
    <div
      id="abs-vehicle-types-custom-date-vehicle-type-message"
      className={`flex flex-col justify-center items-center ${
        errors?.["vehicleType"]?.message ? "h-auto" : "h-10"
      }`}
    >
      <div
        id="abs-vehicle-types-custom-date-is-invalid-sec1"
        className={`w-full ${isInvalid ? "rounded border border-[red]" : ""}${
          disabled ? "bg-gray-205" : ""
        }`}
        onClick={onCliCkAddIndex}
      >
        <Controller
          name="vehicleType"
          control={control || formControl}
          rules={{
            required: {
              value: fieldProps?.required,
              message: fieldProps?.error?.required?.text,
            },
          }}
          render={({ field, fieldState: { error } }) => (
            <>
              <Select
                id={`${fieldProps.label}`}
                className={`w-full inputSelect h-10 !min-h-[40px]${
                  error?.message || isInvalid
                    ? "rounded border !border-[red]"
                    : ""
                }`}
                items={items}
                placeholder={
                  optionValue?.name
                    ? optionValue?.name
                    : "Select Vehicle Type/Custom Date"
                }
                itemText="name"
                value={optionValue}
                onChange={e => {
                  setSelectedValue(e);
                }}
                disabled={disabled}
              />
              {error && <FormFieldError error={error}></FormFieldError>}
            </>
          )}
        />
      </div>
    </div>
  );
}

export default VehicleTypes;
