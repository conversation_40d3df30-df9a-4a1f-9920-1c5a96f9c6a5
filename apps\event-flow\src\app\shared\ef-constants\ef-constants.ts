import { isFeatureFlagEnabled } from "@me-upp-js/utilities";
import { useGetAppBaseUrl } from "@me/util-helpers";
import { appConstants } from "@me/utils-root-props";

/* Have section/feature/page based configs  in seperate vairables and include them in pmConstants.
Dont include constants directly in pmConstants*/
export enum EEVENT_STATUS {
  READY = "Ready",
  DRAFT = "Draft",
  PENDING_WITH_VENDOR = "Pending With Vendor",
  PENDING_WITH_MERCHANT = "Pending With Merchant",
  REJECTED = "Rejected",
  AGREED = "Agreed",
  AGREED_PENDING = "Agreed-Pending",
  CANCELED = "Canceled",
  ACTIVE = "Active",
  EXECUTED = "Executed",
  NOT_FOUND = "NOT FOUND",
  REMOVED = "Removed",
}
const allowancesEntry_constants = {
  ALLOWANCE_TYPES: {
    CASE: {
      key: "CASE",
      value: "CASE",
      label: "Case",
      allowMapKey: "caseAllow",
      allowanceCd: "C",
      createInd: ["CW", "CD", "CB", "CC"],
      defaultPerfOption: {
        DP: {
          CB: "Off Invoice (01)",
          CW: "Off Invoice (01)",
          CD: "DSD Off Invoice (01)",
          CC: "DSD Off Invoice (01)",
        },
        AO: {
          CB: "Auto-Deduct (01)",
          CW: "Auto-Deduct (01)",
          CD: "DSD Off Invoice (01)",
          CC: "DSD Off Invoice (01)",
        },
        NDP: {
          CB: "Price / Ad / Display (88)",
          CW: "Price / Ad / Display (88)",
          CD: "DSD Off Invoice (01)",
          CC: "DSD Off Invoice (01)",
        },
        NCDP: {
          CB: "Price / Ad / Display (88)",
          CW: "Price / Ad / Display (88)",
          CD: "DSD Off Invoice (01)",
          CC: "DSD Off Invoice (01)",
        },
      },
    },
    SCAN: {
      key: "SCAN",
      value: "SCAN",
      label: "Scan",
      allowMapKey: "scanAllow",
      allowanceCd: "T",
      createInd: ["TC", "TS"],
      defaultPerfOption: {
        DP: { TC: "", TS: "" },
        AO: { TC: "Non-Performance (01)", TS: "Non-Performance (01)" },
        NDP: { TC: "", TS: "" },
        NCDP: { TC: "", TS: "" },
      },
    },
    SHIPTOSTORE: {
      key: "SHIP_TO_STORE",
      value: "SHIP TO STORE",
      label: "Ship To Store",
      allowMapKey: "shipAllow",
      allowanceCd: "S",
      createInd: ["SB", "SD"],
      defaultPerfOption: {
        DP: {
          SB: "Price / Ad / Display (88)",
          SD: "Price / Ad / Display (88)",
        },
        AO: {
          SB: "Non-Performance (01)",
          SD: "Non-Performance (01)",
        },
        NDP: {
          SB: "Price / Ad / Display (88)",
          SD: "Price / Ad / Display (88)",
        },
        NCDP: {
          SB: "Price / Ad / Display (88)",
          SD: "Price / Ad / Display (88)",
        },
      },
    },
    HEADERFLAT: {
      key: "HEADER_FLAT",
      value: "HEADER FLAT",
      label: "Header Flat",
      allowMapKey: "headerFlatAllow",
      allowanceCd: "A",
      createInd: ["LC", "LW"],
      defaultPerfOption: {
        DP: {
          LC: "Price / Ad / Display (88)",
          LW: "Price / Ad / Display (88)",
        },
        AO: {},
        NDP: {
          LC: "Price / Ad / Display (88)",
          LW: "Price / Ad / Display (88)",
        },
        NCDP: {
          LC: "Price / Ad / Display (88)",
          LW: "Price / Ad / Display (88)",
        },
      },
    },
    ITEMFLAT: {
      key: "ITEM_FLAT",
      value: "ITEM FLAT",
      label: "Item Flat",
      allowMapKey: "itemFlatAllow",
      allowanceCd: "A",
      createInd: ["AC", "AW"],
      defaultPerfOption: {
        DP: {
          AC: "Price / Ad / Display (88)",
          AW: "Price / Ad / Display (88)",
        },
        AO: {
          AC: "Placement (04)",
          AW: "Placement (04)",
        },
        NDP: {
          AC: "Price / Ad / Display (88)",
          AW: "Price / Ad / Display (88)",
        },
        NCDP: {
          AC: "Price / Ad / Display (88)",
          AW: "Price / Ad / Display (88)",
        },
      },
    },
  },
  LEAD_DIST_CONFIG: {
    LEAD_DIST_ONLY: {
      displayLabel: "Create Lead Distributor Allowance(s) Only",
      subText:
        "-Allowance(s) will only be created for the Lead Distributor(s) and will include all stores serviced by the grouped Distributor(s).",
      value: "LEAD_DIST_ONLY",
      key: "lead_dist_only",
    },
    BILL_LEAD_DIST: {
      displayLabel: "Separate Allowances, Bill Lead Distributor(s)",
      subText:
        "-Allowance(s) will only be created for each Distributor, but only Lead Distributor(s) will be billed.",
      value: "BILL_LEAD_DIST",
      key: "bill_lead_dist",
    },
  },
  BILLING_SELECTION_CONFIG: {
    LEAD_DIST_ONLY: {
      displayLabel: "Create Lead Distributor Allowance(s) Only",
      subText:
        "-Allowance(s) will only be created for the Lead Distributor(s) and will include all stores serviced by the grouped Distributor(s).",
      value: "LEAD_DIST_ONLY",
      key: "lead_dist_only",
      disable: true,
    },
    BILL_LEAD_DIST: {
      displayLabel: "Separate Allowances, Bill Lead Distributor(s)",
      subText:
        "-Allowance(s) will only be created for each Distributor, but only Lead Distributor(s) will be billed.",
      value: "BILL_LEAD_DIST",
      key: "bill_lead_dist",
    },
  },
  ALLOWANCE_SCREEN_TYPES: {
    DP: {
      key: "DP",
      label: "division_promotion",
    },
    AO: {
      key: "AO",
      label: "allowance_only",
    },
    NDP: {
      key: "NDP",
      label: "national_division_promotion",
    },
    NCDP: {
      key: "NCDP",
      label: "national_child_division_promotion",
    },
  },

  CREATEIND_PRODUCTSOURCE_MAPPER: {},
  OFFER_EDITING_MODE_ERROR_MSG:
    "Offer actions are disabled/hidden as USER_ID is either editing an existing offer or adding a new offer.",
  UOM_OPTIONS: {
    SCAN: {
      isSwitching: false,
      defaultOptions: [
        {
          name: "EA",
          value: "EA",
          default: true,
        },
        {
          name: "LB",
          value: "WE",
        },
      ],
      switchCaseOptions: null,
    },
    CASE: {
      isSwitching: true,
      defaultOptions: [
        {
          name: "CA",
          value: "CA",
          default: true,
        },
        {
          name: "LB",
          value: "WE",
        },
      ],
      switchCaseOptions: {
        Case: [
          {
            name: "CA",
            value: "CA",
            default: true,
          },
          {
            name: "LB",
            value: "WE",
          },
        ],
        Unit: [
          {
            name: "EA",
            value: "EA",
            default: true,
          },
          {
            name: "LB",
            value: "WE",
          },
        ],
      },
    },
    SHIP_TO_STORE: {
      isSwitching: true,
      defaultOptions: [
        {
          name: "CA",
          value: "CA",
          default: true,
        },
        {
          name: "LB",
          value: "WE",
        },
      ],
      switchCaseOptions: {
        Case: [
          {
            name: "CA",
            value: "CA",
            default: true,
          },
          {
            name: "LB",
            value: "WE",
          },
        ],
        Unit: [
          {
            name: "EA",
            value: "EA",
            default: true,
          },
          {
            name: "LB",
            value: "WE",
          },
        ],
      },
    },
  },

  UOM_KEY_VALUE_MAPPER: {
    CA: "CA",
    EA: "EA",
    WE: "LB",
    RW: "LB",
    LB: "LB",
  },

  SWITCH_OPTIONS: {
    SCAN: {
      isSwitching: true,
      defaultToggleValue: "Unit",
      switchOptions: {
        Case: {
          isOnlyDisplay: true,
          displayAmtKey: "displayUnitAmt",
          displayUOMKey: "displayUnitUOM",
          allowanceNewCostKey: "newCaseCostAllow",
          allowanceAllowKey: "caseCostAllow",
        },
        Unit: {
          isOnlyDisplay: false,
          allowanceNewCostKey: "newUnitCostAllow",
          allowanceAllowKey: "unitCostAllow",
          displayAmtKey: "displayUnitAmt",
          displayUOMKey: "displayUnitUOM",
        },
      },
      dataMapping: {
        Unit: {
          key: "unitNetCosts",
          labels: {
            costLabel: "Unit List Cost",
            costAllowLabel: "Unit Cost w/Included Allow",
            newCostAllowLabel: "Final Unit Cost",
          },
        },
        Case: {
          key: "shipCaseNetCosts",
          labels: {
            costLabel: "Shipping Case Cost",
            costAllowLabel: "Case Cost w/Included Allow",
            newCostAllowLabel: "Final Case Cost",
          },
        },
      },
    },
    CASE: {
      isSwitching: true,
      defaultToggleValue: "Case",
      switchOptions: {
        Case: {
          isOnlyDisplay: false,
          allowanceNewCostKey: "newCaseCostAllow",
          allowanceAllowKey: "caseCostAllow",
        },

        Unit: {
          isOnlyDisplay: true,
          displayAmtKey: "displayUnitAmt",
          displayUOMKey: "displayUnitUOM",
          allowanceNewCostKey: "newUnitCostAllow",
          allowanceAllowKey: "unitCostAllow",
        },
      },
      dataMapping: {
        Unit: {
          key: "unitNetCosts",
          labels: {
            costLabel: "Unit List Cost",
            costAllowLabel: "Unit Cost w/Included Allow",
            newCostAllowLabel: "Final Unit Cost",
          },
        },
        Case: {
          key: "masterCaseNetCosts",
          labels: {
            costLabel: "Master Case Cost",
            costAllowLabel: "Case Cost w/Included Allow",
            newCostAllowLabel: "Final Case Cost",
          },
        },
      },
    },
    SHIP_TO_STORE: {
      isSwitching: true,
      defaultToggleValue: "Case",
      switchOptions: {
        Case: {
          isOnlyDisplay: false,
          allowanceNewCostKey: "newCaseCostAllow",
          allowanceAllowKey: "caseCostAllow",
        },

        Unit: {
          isOnlyDisplay: true,
          displayAmtKey: "displayUnitAmt",
          displayUOMKey: "displayUnitUOM",
          allowanceNewCostKey: "newUnitCostAllow",
          allowanceAllowKey: "unitCostAllow",
        },
      },
      dataMapping: {
        Unit: {
          key: "unitNetCosts",
          labels: {
            costLabel: "Unit List Cost",
            costAllowLabel: "Unit Cost w/Included Allow",
            newCostAllowLabel: "Final Unit Cost",
          },
        },
        Case: {
          key: "shipCaseNetCosts",
          labels: {
            costLabel: "Shipping Case Cost",
            costAllowLabel: "Case Cost w/Included Allow",
            newCostAllowLabel: "Final Case Cost",
          },
        },
      },
    },
    HEADER_FLAT: {
      isSwitching: false,
      dataMapping: {
        Unit: {
          key: "unitNetCosts",
          labels: {
            costLabel: "Unit List Cost",
            costAllowLabel: "Unit Cost w/Included Allow",
            newCostAllowLabel: "Final Unit Cost",
          },
        },
        Case: {
          key: "masterCaseNetCosts",
          labels: {
            costLabel: "Master Case Cost",
            costAllowLabel: "Case Cost w/Included Allow",
            newCostAllowLabel: "Final Case Cost",
          },
        },
      },
    },
    ITEM_FLAT: {
      isSwitching: false,
      dataMapping: {
        Unit: {
          key: "unitNetCosts",
          labels: {
            costLabel: "Unit List Cost",
            costAllowLabel: "Unit Cost w/Included Allow",
            newCostAllowLabel: "Final Unit Cost",
          },
        },
        Case: {
          key: "masterCaseNetCosts",
          labels: {
            costLabel: "Master Case Cost",
            costAllowLabel: "Case Cost w/Included Allow",
            newCostAllowLabel: "Final Case Cost",
          },
        },
      },
    },
  },
  DEFAULT_ALLOWANCE_TYPE: "SCAN",
  ALLOWANCE_HF_AMT_KEY: "headerFlatAmt",
  ALLOWANCE_AMT_INPUT_KEY: "allowanceAmount",
  ALLOWANCE_UOM_KEY: "allowUomType",
  ALLOWANCE_ITEM_DESC_KEY: "itemDescription",
  ALLOWANCE_NEW_UNIT_COST_ALLOW_KEY: "newUnitCostAllow",
  ALLOWANCE_UNIT_ALLOW_KEY: "unitCostAllow",
  ALLOWANCES_DASHBOARD_ROUTE: "/all-allowances",
  VENDOR_DIST_CENTER_KEY: "distCenter",
  CA_UOM_TYPE: "CA",
  EA_UOM_TYPE: "EA",
  LEAD_DIST_ONLY: "LEAD_DIST_ONLY",
  BILL_LEAD_DIST: "BILL_LEAD_DIST",
  S2S_WAREHOUSE_ALLOWANCE_OPTION: "One Allowance",
  CASE_DSD_ALLOWANCE_OPTION: "DSD Only",
  REINCLUDE_EXTERNAL_DIST_WARN_MSG: `You have re-Included an Allowance and its Billing Information has now
          been defaulted. Please review Billing information in the Billing
          Selection section to ensure that it is correct.`,
  BYPASS_ALLOWANCE_LABEL:
    "Bypass Allowance: Add allowance at a later time or Date.",
  INCLUDEIND_LABEL: "offerallowances.allowances.includeInd",
  NORECORDS_FOUND_LABEL: "No Records Found",
  ALLOWANCES_TYPE_MAPPNG: {
    SCAN: "scanAllow",
    CASE: "caseAllow",
    SHIP_TO_STORE: "shipAllow",
    ITEM_FLAT: "itemFlatAllow",
    HEADER_FLAT: "headerFlatAllow",
  },
  STATUS_USER_MAPPER: {
    VENDOR: "Pending With Vendor",
    MERCHANT: "Pending With Merchant",
  },

  //This is used to render the filer. Keys would match with the json data
  FILTER_DISPLAY_OBJ: {
    packWhse: {
      filterValues: [] as number[],
    },
    size: {
      filterValues: [] as string[],
    },
    caseListCost: {
      filterValues: [] as number[],
    },
    unitListCost: {
      filterValues: [] as number[],
    },
    unitCostAllow: {
      filterValues: [] as number[],
    },
    caseCostAllow: {
      filterValues: [] as number[],
    },
    shippingCost: {
      filterValues: [] as number[],
    },
  },

  SEARCH_FIELDS: ["itemDescription", "itemId", "primaryUpc"], //These columns can be filtered from search input

  OFFER_ALLOWANCE_KEY_MAPPING: {
    DSD_WHSE_RETAIL_DIVISION: {
      productGroups: ["WAREHOUSE"],
      allowanceToBeCraetedOption: {
        SCAN: "One Allowance: Warehouse, DSD, or Combined",
        CASE: "One Allowance: DSD Combined",
        SHIP_TO_STORE: "Combined DSD",
      },
    },
    DSD_LEAD_DISTRIBUTORS: {
      productGroups: ["DSD"],
      allowanceToBeCraetedOption: {
        SCAN: "Separate Allowances By DSD Distributor",
        CASE: "Separate Allowances By DSD Distributor",
        SHIP_TO_STORE: "Separate Allowances By DSD Distributor",
      },
    },
    WAREHOUSE_DIST_CENTERS: {
      productGroups: ["WAREHOUSE"],
      allowanceToBeCraetedOption: {
        SCAN: "One Allowance: Warehouse, DSD, or Combined",
        CASE: "Warehouse Only",
        SHIP_TO_STORE: "Combined DSD",
      },
    },
  },

  OFFER_ALLOWANCE_GROUP: {
    SCAN: {
      SEPERATE: "DSD_LEAD_DISTRIBUTORS",
      COMBINED: "DSD_WHSE_RETAIL_DIVISION",
    },
    CASE: {
      WAREHOUSE: "WAREHOUSE_DIST_CENTERS",
      DSD: "DSD_LEAD_DISTRIBUTORS",
      COMBINED: "DSD_WHSE_RETAIL_DIVISION",
    },
    SHIP_TO_STORE: {
      SEPERATE: "DSD_LEAD_DISTRIBUTORS",
      COMBINED: "DSD_WHSE_RETAIL_DIVISION",
    },
  },

  VENDOR_KEYS_ARR: [
    "caseListCost",
    "unitListCost",
    "unitCostAllow",
    "allowanceAmount",
    "allowUomType",
    "newUnitCostAllow",
    "caseCostAllow",
    "newCaseCostAllow",
    "shippingCost",
    "prevAllowAmtValue",
    "updateUser",
    "prevHeaderAmtValue",
    "currentHeaderAmtValue",
    "prevUomValue",
    "pendingChangeFlag",
  ],
  ALLOWANCE_DASHBOARD_BASE_URL: "allowances-entry",
  TRANSFORMED_VENDOR_KEY: "vendorNbr",
  TRANSFORMED_VENDOR_NUMBER: "vendorNbr",
  TRANSFORMED_VENDOR_NAME: "vendorName",
  CREATE_ALLOW_IND: "createAllowInd",
  INCLUDE_IND: "includeInd",
  LEAD_DIST_IND: "leadDistributorInd",
  COST_AREA_DESC: "costAreaDesc",
  BOTH_KEY: "BOTH",
  WAREHOUSE_DIST_CENTERS_KEY: "WAREHOUSE_DIST_CENTERS",
  ADD_ANOTHER_ALLOWANCE: "Add Another Allowance",
  UPDATE_ALLOWANCE: "Update Offer",
  CREATE_ALLOWANCE: "Save & Create Allowance(s)",
  VIEW_OFFER_DEAL_SHEET_BUTTON: "View Offer Deal Sheet",
  OFFER_UPLOAD_LABEL: "Upload",
  OFFER_MANAGE_LABEL: "Manage",
  VIEW_OFFER_SUMMARY: "View Summary",
  LEAD_DISTRIBUTOR_SUBLABEL_HEADER_TEXT:
    "If there is a single lead Distributor, all other Distributors are grouped by default.",
  LEAD_DISTRIBUTOR_SUBLABEL_CONTENT_TEXT:
    "If 2 lead Distributors are selected, you must move the other distributors under the respective Lead Distributor that you want them grouped with.",
  PRODUCT_SOURCE_DATA: {
    WAREHOUSE: {
      key: "WAREHOUSE",
      value: "One Allowance: Warehouse, DSD, or Combined",
    },
    DSD: {
      key: "DSD",
      value: "Separate Allowances By DSD Distributor",
    },
    BOTH: {
      key: "BOTH",
      value: "Both",
    },
  },

  PRODUCT_SOURCE_INFO: {
    CASE: {
      WAREHOUSE: {
        key: "WAREHOUSE",
        value: "Warehouse Only",
        createIndex: "CW",
      },
      DSD: {
        key: "DSD",
        value: "DSD Only",
        createIndex: "CD",
      },
      BOTH: {
        key: "BOTH",
        value: "Both",
        createIndex: "CB",
      },
    },
    SCAN: {
      WAREHOUSE: {
        key: "WAREHOUSE",
        value: "One Allowance: Warehouse, DSD, or Combined",
        createIndex: "TC",
      },
      DSD: {
        key: "DSD",
        value: "Separate Allowances By DSD Distributor",
        createIndex: "TS",
      },
      BOTH: {
        key: "BOTH",
        value: "Both",
        createIndex: "TS",
      },
    },
    SHIP_TO_STORE: {
      WAREHOUSE: {
        key: "WAREHOUSE",
        value: "Combined DSD",
        createIndex: "SB",
      },
      DSD: {
        key: "DSD",
        value: "Separate Allowances By DSD Distributor",
        createIndex: "SD",
      },
      BOTH: {
        key: "BOTH",
        value: "Both",
        createIndex: "SB",
      },
    },
    HEADER_FLAT: {
      WAREHOUSE: {
        key: "WAREHOUSE",
        createIndex: "LW",
      },
      DSD: {
        key: "DSD",
        createIndex: "LC",
      },
    },
    ITEM_FLAT: {
      WAREHOUSE: {
        key: "WAREHOUSE",
        createIndex: "AW",
      },
      DSD: {
        key: "DSD",
        createIndex: "AC",
      },
    },
  },

  DEFAULT_ALLOWANCE_CREATED_TYPE: {
    CASE: "Both",
    SCAN: "One Allowance: Warehouse, DSD, or Combined",
    SHIP_TO_STORE: "Combined DSD",
  },
  DEFAULT_PRODUCT_SOURCE: {
    CASE: "BOTH",
    SCAN: "WAREHOUSE",
    SHIP_TO_STORE: "WAREHOUSE",
  },
  UOM_VALUES: {
    EACH: "EA",
    WEIGHT: "WE",
    DEFAULT_LABEL: "LB",
    CASE: "CA",
  },
  ALLOWANCE_TYPE_NAME_MAPPER: {
    Case: "CASE",
    Scan: "SCAN",
    "Ship To Store": "SHIP_TO_STORE",
    "Header Flat": "HEADER_FLAT",
    "Item Flat": "ITEM_FLAT",
  },

  DISTRIBUTORS_DATA: {
    label: "",
    required: true,
    registerField: "distributorGroup",
    registerKeyName: "distributorGroup",
    optionUrl: "",
    default: "ALL",
    leadOption: "LEAD",
    options: [
      {
        name: "All Distributors",
        default: true,
        key: "ALL",
        hideIfSameVendor: false,
      },
      {
        name: "Lead Distributor(s) Only",
        default: false,
        key: "LEAD",
        hideIfSameVendor: false,
      },
      {
        name: "Billing Selection",
        default: false,
        key: "BILLING",
        hideIfSameVendor: true,
      },
    ],
    type: "radio",
    apiUrl: "",
    slice: "",
    errors: {
      required: {
        backgroundColor: "",
        text: "Distributors Required",
      },
    },
  },

  DATE_FORMATS: {
    ISO_DATE_FORMAT: "YYYY-MM-DDTHH:mm:ss.SSS[Z]",
  },

  REGEX_PATTERN: {
    NUMBERS_WITH_ASTERISK_ONLY: "^[\\d*]+$",
    NUMBERS_ONLY: "^[0-9]+$",
    NUBERS_WITH_DECIMALS: "^[0-9]*(.[0-9]{0,2})?$",
  },
};

const promotionDates_constants = {
  PROMOTION_CUSTOM_DATE: "Custom Date",
};

const groupInd_constants = {
  DEFAULT_GROUP_IND: "D",
};
const event_constants = {
  PPG_UNIT_TYPE_MAPPER: {
    1: {
      name: "Single",
      code: "S",
    },
    2: {
      name: "Multi",
      code: "M",
    },
    3: {
      name: "Case",
      code: "C",
    },
  },
  DISPLAY_TYPE_PPG_CD: "D",
  EXCLUDED_OFFER_PROMO_STATUS: [
    EEVENT_STATUS.REJECTED,
    EEVENT_STATUS.CANCELED,
    EEVENT_STATUS.REMOVED,
  ],
  SEND_CTS_BTNS: ["SEND_TO_MERCHANT", "SEND_TO_VENDOR"],
};

const product_validation_constants = {
  DIVISION_PROMO:
    "Invalid combination of Promo Product Groups and Store Groups. Please update Promo Product Groups or Store Groups to continue.",
  NATIONAL_DIVISION_PROMO:
    "The following divisions do not carry any items in the selected PPG(s):",
  AO_ONLY:
    "Invalid combination of Promo Product Groups and Division. Please update Promo Product Groups or Division to continue.",
};

const EDIT_EVENT_WARNING_MESSAGE = {
  DP: {
    header: "Division, Promo Product Group, or Store Group cannot be changed!",
    message:
      "Please cancel and create new event for a Division, Promo Product Group, or Store Group change.",
  },
  AO: {
    header: "Division or Promo Product Group cannot be changed!",
    message:
      "Please cancel and create new event for a Division or Promo Product Group change.",
  },
  NDP: {
    header: "Division, Promo Product Group, or Store Group cannot be changed!",
    message:
      "Please cancel and create new event for a Division, Promo Product Group, or Store Group change.",
  },
  NCDP: {
    header: "Division, Promo Product Group, or Store Group cannot be changed!",
    message:
      "Please cancel and create new event for a Division, Promo Product Group, or Store Group change.",
  },
};

const DISPLAY_PERISCOPE_ERRORS = {
  ITEMS_NOT_MATCH: {
    label: "Item(s) are not matching with associated PID details.",
  },
  DATES_NOT_MATCH: {
    label: "Dates are not matching with associated PID details.",
  },
  INVALID_PID: {
    label: "This PID is invalid",
  },
  PID_NOT_VALIDATED: {
    label: "This PID could not be validated",
  },
};

const NEW_DIRECTION_FEATURES_FLAGS = {
  isDirectionalChangesEnable: true,
  ONE_ALLOWANCE: {
    enableAddOfferNewLogic: true,
    Scan: {
      showPeriscopeId: false,
      disableVehicleStepper: true,
    },
    Case: {
      showPeriscopeId: false,
      disableVehicleStepper: true,
    },
    "Ship To Store": {
      showPeriscopeId: false,
      disableVehicleStepper: true,
    },
    "Header Flat": {
      showPeriscopeId: false,
      disableVehicleStepper: true,
    },
    "Item Flat": {
      showPeriscopeId: false,
      disableVehicleStepper: true,
    },
  },
  isPromoDirectionalChangesEnable: true,
  ONE_PROMOTION: {
    showPeriscopeId: false,
    disableVehicleStepper: true,
    enableAddPromoNewLogic: true,
  },
};

const CREATEIND_OFFER_GROUP_MAPPER = {
  CW: "WAREHOUSE_DIST_CENTERS",
  CD: "DSD_LEAD_DISTRIBUTORS",
  CB: {
    DSD: "DSD_LEAD_DISTRIBUTORS",
    WAREHOUSE: "WAREHOUSE_DIST_CENTERS",
  },
  CC: "DSD_WHSE_RETAIL_DIVISION",
  TC: "DSD_WHSE_RETAIL_DIVISION",
  TS: "DSD_LEAD_DISTRIBUTORS",
  SB: "DSD_WHSE_RETAIL_DIVISION",
  SD: "DSD_LEAD_DISTRIBUTORS",
  LC: "DSD_WHSE_RETAIL_DIVISION",
  LW: "WAREHOUSE_DIST_CENTERS",
  AC: "DSD_WHSE_RETAIL_DIVISION",
  AW: "WAREHOUSE_DIST_CENTERS",
};

const CREATEIND_ALLOWANCE_TO_BE_CREATED_OPTION_MAPPER = {
  CW: "WAREHOUSE_DIST_CENTERS",
  CD: "DSD_LEAD_DISTRIBUTORS",
  CB: "BOTH",
  CC: "DSD_WHSE_RETAIL_DIVISION",
  TC: "DSD_WHSE_RETAIL_DIVISION",
  TS: "DSD_LEAD_DISTRIBUTORS",
  SB: "DSD_WHSE_RETAIL_DIVISION",
  SD: "DSD_LEAD_DISTRIBUTORS",
  LC: "DSD_WHSE_RETAIL_DIVISION",
  LW: "WAREHOUSE_DIST_CENTERS",
  AC: "DSD_WHSE_RETAIL_DIVISION",
  AW: "WAREHOUSE_DIST_CENTERS",
};

const OFFER_POPUP_MESSAGES = {
  TITLE:
    "You have not completed entering the needed allowance amounts to complete this New Item task",
  CONFIRM_BTN_TITLE: "Cancel and Leave on Task list",
  CANCEL_BTN_TITLE: "Go Back and Finish",
};
const PERISCOPE_ERRORS = {
  INVALID_PID: "This PID is invalid",
  INVALID_PID_KEY: "INVALID_PID",
  PID_NOT_VALIDATED: "This PID could not be validated",
  EXTRA_FIELD: "Please review and correct them before continuing.",
  ITEM_DATE_INVALID_MESSAGE:
    "Item and/or Dates are not matching with associated PID details.",
  PPG_NOT_FOUND:
    "Promo Product Group match cannot be found for entered PID. Please review the details below.",
  PPG_NOT_FOUND_INFO: "Please Enter CIC IDs in the box below for PPG ",
  PPG_NOT_FOUND_KEY: "PPG_NOT_FOUND",
  CICS: "CICs",
};

const ALLOWANCE_TYPE_EDIT_MESSAGES = {
  INFO: "Are you certain you want to change the Allowance Type?",
  CONFIRM_BTN_TITLE: "Ok",
  CANCEL_BTN_TITLE: "Cancel",
};

const ALLOWANCE_TYPE_EDIT_ON_CANCEL_MESSAGES = {
  TITLE: "Canceling will delete the offer. You'll need to create a new one?",
  CONFIRM_BTN_TITLE: "Ok",
  CANCEL_BTN_TITLE: "Cacel",
};

const EVENT_DATE_WARNING = {
  PAST_DATE_WARNING:
    "Event Start and End date cannot be changed to past if Event contains Promotion!",
  HELPER_TEXT:
    "Please cancel offer and create new event for an Offer date change to past date.",
};
const VALIDATION_PPGS = "has 0 valid items. Please select a new group.";

const DYNAMIC_PPGS_MESSAGES = {
  INVALID_CICS_ERROR: "Please remove invalid CICs to proceed.",
  REMOVE_INVALID_CIC_IDS: "Remove Invalid CIC IDs",
  CIC_SUBMIT_INFO:
    "You can only submit CICs for markdowns, new item placement, and supply changes. Please reach out to merchant team if new PPG is needed for regular promotion.",
  REMOVE_PPG_ERROR: "Remove added PPGs before entering CIC IDs",
  MIXED_UPC_TYPE_ERROR_FOR_MODAL:
    "You're trying to add mixed cic types. Please select similat types.",
  MIXED_UPC_TYPE_ERROR: "You're trying to add mixed cic types. Please remove.",
  ALPHA_NUMERIC_ERROR:
    "Entered CIC cannot contain more than 8 digits or special characters.",
  DISPLAY_ITEM_FLAG_MIXED: "DISPLAY_ITEM_FLAG_MIXED",
  VENDOR_NOT_LINKED: "VENDOR_NOT_LINKED",
  SMIC_NOT_IN_ROLLOUT: "SMIC_NOT_IN_ROLLOUT",
  UNRESOLVED_ITEMS_MAPPER: {
    DISPLAY_ITEM_FLAG_MIXED:
      "Display type CICs cannot be mixed with non-Display CICs within an Event. Please change CICs chosen.",
    VENDOR_NOT_LINKED: ` "Unable to add CIC to event due to access error". For assistance, please submit a case via Case Management `,
    SMIC_NOT_IN_ROLLOUT: ` "Unable to add CIC to event due to error". For assistance, please submit a case via Case Management `,
    ANCHOR_LINK: "<EMAIL>",
  },
  ADD_ITEMS_LABEL: "Add Items",
  ENTER_CIC_IDS_LABEL: "Or Enter CIC IDs",
  ENTER_CICS_PLACEHOLDER: "Enter CICs",
};

export const PERISCOPE_LABELS = {
  BUTTON_LABEL: "Fetch Event Details",
  ENTER_DETAILS_LABEL: "Or enter details manually",
};

export const EVENT_DETAILS_ERROR_LABELS = {
  DIVISION_ERROR: "Error fetching Divisions.",
  PROMO_PRODUCT_GROUPS_ERROR:
    "Promo Product Group match cannot be found for entered PID. Please add items manually.",
  STORE_GROUPS_ERROR:
    "Store Group Group match cannot be found for entered PID. Please select Store Groups manually.",
  VEHICLE_TYPE_GROUPS_ERROR:
    "Vehicle match cannot be found for entered PID. Please select a vehicle or custom date manually.",
};

const S2S_WAREHOUSE_DSD_COMBINED_KEY = "Combined";
const FORECAST_DISPLAY = {
  MERCHANT: false,
  VENDOR: false,
};
const NO_PPGS_ADDED_LABEL = "No Promo Products Groups added yet.";
const INVALID_DIVISIONS_WITHOUT_ITEMS =
  "The following divisions do not carry any items in the selected PPG(s):"
const CIC_FEATURE_FLAGS = {
  isCICFeatureEnabled: true,
};
const PROTION_ROG_LEVEL_PRICING_FEATURE_FLAGS = {
  isEnabled: isFeatureFlagEnabled(
    appConstants?.FEATURE_FLAGS?.ROG_HIDDEN_PRICE
  ),
  PROMOTION_PRICING_BASE_URL: "promotion-pricing",
};
const ZERO_COST_TEXT = "ZERO_COST";
const SKIP_COST_TEXT = "SKIP_ZERO_COST";
const MOD_COMMAND_NONE = "NONE";
const MOVE_BTNS_DIRECTIONS = ["UP", "DOWN"];
const MOVE_BTNS_LABELS = {
  UP: "UP",
  DOWN: "DOWN",
};
const ALL_DIST = "ALL";
const LEAD_DIST_LABEL = "LEAD";
const BILL_DIST_LABEL = "BILLING";
const LEAD_DIST_ERR_MSG =
  "There are more than 2 lead distributors please combined to two or less.";
const ITEM_AMOUNTS_SUMMARIZED_KEY = "itemAmountsCouldBeSummarized";
const WAREHOUSE_HEADER_DISABLE_DISTCENTER_KEY = "DDSE";
const CLOSE_INQUIRY = "Close Inquiry";
const REQUEST_FOR_CHANGE = "Request For Change";
const CONTINUE_EDITING = "Continue Editing";
const POPUP_TEXT_FOR_REQUEST_CHANGE =
  "Submit the request for this change, as this event already been agreed.";
const NATIONAL_EVENTS_ROLES = [
  "PROMOTION_NATIONAL_EVENT_DIV_PROMO_EDIT",
  "PROMOTION_NATIONAL_EVENT_ALLOWANCE_ONLY_EDIT",
  "PROMOTION_NATIONAL_EVENT_DIV_PROMO_VIEW",
  "PROMOTION_NATIONAL_EVENT_ALLOWANCE_ONLY_VIEW",
];
const NATIONAL_DIVISIONS_FOR_DIFFERENT_DATES = ["33", "34"];
const OFFER_ITEM_ERRORS = {
  NO_ITEMS_FOR_ALL_DIVISIONS:
    "None of the divisions have valid items or eligible locations. Offer cannot be created.",
  MISSING_ITEMS_FOR_SELECTED_DIVISIONS:
    "These division were excluded as no valid amounts were found for all items in the PPG:",
  NO_ITEMS_FOR_NEW_OFFER:
    "This division has no active items available for any of the allowances.",
  NO_WHSE_ALLOW_LOCS:
    "This division’s warehouse locations do not match any valid allowance locations.",
  NO_DSD_AUTH_VENDORS:
    "This division has no valid DSD authorizations for the selected items.",
  DEFAULT:
    "This division has no active items available for any of the allowances.",
};
const CASE_WHSE_CREATE_IND = "CW";
const CASE_WHSE_OFFER_GROUP = "WAREHOUSE_DIST_CENTERS";
const SHARED_WAREHSE_TEXT =
  "For shared warehouses, the same item should have the same allowance amount across all divisions. Any changes will reflect in all divisions. For non-shared warehouses, allowance can vary by division.";
const POWERLINE_TEXT = "Applying Powerline will overwrite existing dates.";
const AMOUNTS_POWERLINE_TEXT =
  "Applying Powerline will overwrite existing allowance amounts.";
const BILLING_POWERLINE_TEXT =
  "Applying Powerline will overwrite existing billing information.";
const efConstants = {
  ...allowancesEntry_constants,
  ...promotionDates_constants,
  ...groupInd_constants,
  ...event_constants,
  ...product_validation_constants,
  ...EVENT_DATE_WARNING,
  ...DYNAMIC_PPGS_MESSAGES,
  NEW_DIRECTION_FEATURES_FLAGS,
  PERISCOPE_LABELS,
  PERISCOPE_ERRORS,
  DISPLAY_PERISCOPE_ERRORS,
  EVENT_DETAILS_ERROR_LABELS,
  EDIT_EVENT_WARNING_MESSAGE,
  VALIDATION_PPGS,
  FORECAST_DISPLAY,
  OFFER_POPUP_MESSAGES,
  ALLOWANCE_TYPE_EDIT_MESSAGES,
  ALLOWANCE_TYPE_EDIT_ON_CANCEL_MESSAGES,
  CIC_FEATURE_FLAGS,
  INVALID_DIVISIONS_WITHOUT_ITEMS,
  PROTION_ROG_LEVEL_PRICING_FEATURE_FLAGS,
  NO_PPGS_ADDED_LABEL,
  ZERO_COST_TEXT,
  SKIP_COST_TEXT,
  MOD_COMMAND_NONE,
  MOVE_BTNS_LABELS,
  MOVE_BTNS_DIRECTIONS,
  ALL_DIST,
  LEAD_DIST_LABEL,
  BILL_DIST_LABEL,
  LEAD_DIST_ERR_MSG,
  NATIONAL_EVENTS_ROLES,
  CREATEIND_OFFER_GROUP_MAPPER,
  CREATEIND_ALLOWANCE_TO_BE_CREATED_OPTION_MAPPER,
  componentClassName: {
    EVENT_TYPES_CONTAINER: "abs-ef-event-types-container",
    EVENT_HEADER_CONTAINER: "abs-ef-event-header-container",
    EVENT_HEADER: "abs-ef-event-header",
    EVENT_STATUS_BADGE: "abs-ef-event-status-badge",
    WARNING_MODAL: "abs-ef-warning-modal",
    EVENT_CARDS: "abs-ef-event-cards",
    GET_ALLOWANCE_INFO_VIEW: "abs-ef-get-allowance-view",
    ALLOWANCE_INFO_VIEW: "abs-ef-allowance-info-view",
    EVENT_CREATION_CONTAINER: "abs-ef-event-creation-container",
    EVENT_CREATION_LAYOUT: "abs-ef-event-creation-layout",
    EVENT_DETAILS_CARD_HEADER: "abs-ef-event-details-card-header",
    RENDER_CANCEL_BUTTON: "abs-ef-render-cancel-button",
    GET_CANCEL_LABEL: "abs-ef-get-cancel-label",
    RENDER_RESEND_BUTTON: "abs-ef-render-resend-button",
    GET_AGREEMENT_CONTENT: "abs-ef-get-agreement-content",
    VIEW_AGREEMENT_MODAL: "abs-ef-view-agreement-modal",
    GET_COMMON_CONTENT: "abs-ef-get-common-content",
    COMMON_MODAL: "abs-ef-common-modal",
    RENDER_HTML: "abs-ef-render-html",
    RENDER_HTML_CARD_HEADER: "abs-ef-render-html-card-header",
    RENDER_HTML_CARD_CONTENT: "abs-ef-render-html-card-content",
    EVENT_DETAILS_PREVIEW: "abs-ef-event-details-preview",
    NO_PPGS_ADDED: "abs-ef-no-ppgs-added",
    PLAN_PRODUCT_GROUPS_SUMMARY: "abs-ef-plan-product-groups-summary",
    MULTI_VENDOR_PPG_INFO: "abs-ef-multi-vendor-ppg-info",
    GET_INVALID_PPGS_COUNT: "abs-ef-get-invalid-ppgs-count",
    SELECT_UPC_TYPE_COMP: "abs-ef-select-upc-type-comp",
    RENDER_PERISCOPE_ERRORS: "abs-ef-render-periscope-errors",
    RENDER_PAST_EVENT_DATE_WARNING: "abs-ef-render-past-event-date-warning",
    EVENT_DETAILS_CARD_CONTENT: "abs-ef-event-details-card-content",
    EVENT_VIEW_ITEMS_MODAL: "abs-ef-event-view-items-modal",
    VIEW_ITEMS_MODAL_HEADER: "abs-view-items-modal-header",
    VIEW_ITEMS_MODAL_CONTENT: "abs-ef-view-items-modal-content",
    GET_VIEW_ITEMS: "abs-ef-get-view-items",
    EVENT_DETAILS_PROMO_PRODUCT_HOVER_CONTENT_CONTAINER:
      "abs-ef-event-details-promo-product-hover-content-container",
    EVENT_HISTORY_ALLOWANCE_AMOUNTS: "abs-ef-event-history-allowance-amounts",
    ALL_ALLOWANCES_CONTAINER: "abs-ef-all-allowances-container",
    ALLOWANCE_EXCLUDE_COMPONENT: "abs-ef-allowance-exclude-component",
    LEAD_DIST_MODE_OPTIONS: "abs-ef-lead-dist-mode-options",
    LEAD_DISTRIBUTOR_SUBLABEL_TEXT: "abs-ef-lead-distributor-sublabel-text",
    ALLOWANCES_AMOUNTS: "abs-ef-allowances-amounts",
    ALLOWANCES_UNIT_COST_DETAILS: "abs-ef-allowances-unit-cost-details",
    AMOUNT_ONLYIN_HEADER: "abs-ef-amount-onlyin-header",
    INVALID_AMOUNT_TOOLTIP: "abs-ef-invalid-amount-tooltip",
    UPDATE_ALL_ALLOWANCES: "abs-ef-update-all-allowances",
    ALLOWANCES_FILTER_SELECTION: "abs-ef-allowances-filter-selection",
    ALLOWANCE_CARD_CONTENT_PREVIEW: "abs-ef-allowance-card-content-preview",
    ALLOWANCE_FORM_WRAPPER: "abs-ef-allowance-form-wrapper-form",
  },
  S2S_WAREHOUSE_DSD_COMBINED_KEY,
  ITEM_AMOUNTS_SUMMARIZED_KEY,
  WAREHOUSE_HEADER_DISABLE_DISTCENTER_KEY,
  CLOSE_INQUIRY,
  REQUEST_FOR_CHANGE,
  CONTINUE_EDITING,
  POPUP_TEXT_FOR_REQUEST_CHANGE,
  NATIONAL_DIVISIONS_FOR_DIFFERENT_DATES,
  OFFER_ITEM_ERRORS,
  SHARED_WAREHSE_TEXT,
  CASE_WHSE_CREATE_IND,
  CASE_WHSE_OFFER_GROUP,
  POWERLINE_TEXT,
  AMOUNTS_POWERLINE_TEXT,
  BILLING_POWERLINE_TEXT,
};
export const billingInformationMessages = [
  {
    preContent:
      "Vendor may enter any Comments that they wish to have displayed on Allowance Agreements or any other information they deem needed. Comments entered in this box will be for the entire Offer and will be propagated onto each Allowance within it below. If you wish to have different Comments on each Allowance enter them on each one and do not use this entry box.",
    id: "abs-common-billing-information-merch-vendor-text-comments",
    isShowCommentField: true,
  },
  {
    preContent:
      "Vendors: if you are not doing a one-time exception and your Default Billing Information is incorrect, please go to ",
    postContent:
      "  and open a Case so that Albertsons can resolve it for you and have your Billing information default correctly ongoing.  When submitting your Case, please choose the Invoice Repository option so that it is routed to our Allowance Billing department for resolution.",
    urlLink: `${window.location.origin}${appConstants.ME_MSP_BASE_PATH_NAME}case-management`,
    isShowCommentField: false,
    css: "text-blue-500 font-bold",
    id: "abs-common-billing-information-merch-vendor-case-management",
    urlText: "Case Management",
  },
  {
    preContent:
      "Suggested Billing Information - use ONLY if you think that Default Billing is incorrect or if you have an unusual one-time circumstance where you would like to request to be Billed differently. Your suggestion will be visible to the Albertsons Allowance Billing department.",
    id: "abs-common-billing-information-merch-vendor-text",
    isShowCommentField: false,
  },
];

export default efConstants;
