import { useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import efConstants from "../../../../../../shared/ef-constants/ef-constants";
import { useSearchParams } from "react-router-dom";
import {
  getInvalidAllowanceAndPromotions,
  GetInvalidAllowanceKeys,
  GetInvalidPromoKeys,
  getLoggedInUserType,
} from "@me/util-helpers";
import { getInvalidDataObject } from "../../../../service/allowance/allowance-service";
import { useSelectorWrap } from "@me/data-rtk";

type InvalidIdsData = {
  inValidPromotionIds: string[];
  inValidAllowanceIds: string[];
};

function InvalidAllowPromoFieldMessage() {
  const { getValues } = useFormContext();
  const eventCreationVehicle = getValues("eventCreationVehicle");
  const [invalidIdsData, setInvalidIdsData] = useState<InvalidIdsData>({
    inValidPromotionIds: [],
    inValidAllowanceIds: [],
  });
  const [searchParams] = useSearchParams();
  const eventTypeName = searchParams.get("eventType");
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { isEventVehicleChangedInPending } = useSelectorWrap(
    "check_event_vehicledates_rn"
  ).data;
  const eventType = eventTypeName || eventDetailsData?.eventType;

  const getInvalidAllowanceAndPromo = (state, isAllowance = false) => {
    return state?.map(item => {
      return (
        <span key={item}>
          <span className="font-bold">#{item}, </span>
        </span>
      );
    });
  };

  const getInvalidAllowancePromos = () => {
    if (getValues("id")) {
      const userType = getLoggedInUserType();
      const invalidPromo = getInvalidAllowanceAndPromotions(
        eventDetailsData,
        eventCreationVehicle,
        GetInvalidPromoKeys.iterateKey1,
        GetInvalidPromoKeys.iterateKey2,
        GetInvalidPromoKeys.startDateKey,
        GetInvalidPromoKeys.endDateKey,
        "promotionId",
        userType,
        eventDetailsData,
        isEventVehicleChangedInPending
      );
      setInvalidIdsData(prevState => ({
        ...prevState,
        inValidPromotionIds: invalidPromo,
      }));
    }
  };

  const getInvalidOfferIds = () => {
    if (getValues("id")) {
      const userType = getLoggedInUserType();
      const inValidOffer = getInvalidAllowanceAndPromotions(
        eventDetailsData,
        eventCreationVehicle,
        GetInvalidAllowanceKeys.iterateKey1,
        GetInvalidAllowanceKeys.iterateKey2,
        GetInvalidAllowanceKeys.startDateKey,
        GetInvalidAllowanceKeys.endDateKey,
        "id",
        userType,
        eventDetailsData,
        isEventVehicleChangedInPending
      );
      if (inValidOffer) {
        const inValidOffers = getInvalidDataObject(
          eventDetailsData,
          inValidOffer,
          eventDetailsData?.["inValidPromotions"]
        );
        setInvalidIdsData(prevState => ({
          ...prevState,
          inValidAllowanceIds: inValidOffers?.offerAllowances?.map(
            item => item?.offerNumber
          ),
        }));
      }
    }
  };

  useEffect(() => {
    getInvalidAllowancePromos();
    getInvalidOfferIds();
  }, [eventCreationVehicle]);

  return (
    <div className="text-[#9d2210] text-base">
      {!efConstants.NEW_DIRECTION_FEATURES_FLAGS.isDirectionalChangesEnable &&
      (invalidIdsData?.inValidAllowanceIds?.length ||
        invalidIdsData?.inValidPromotionIds?.length) ? (
        <>
          <p>
            Below <span className="font-bold">Allowance(s)</span>
            {eventType === efConstants?.ALLOWANCE_SCREEN_TYPES["DP"].key ? (
              <span>
                and/or
                <span className="font-bold">Promotion(s)</span>
              </span>
            ) : null}
            will be impacted if you update the Event Dates.
          </p>
          {invalidIdsData?.inValidAllowanceIds?.length ? (
            <p className="ml-6">
              <span>
                {invalidIdsData?.inValidAllowanceIds?.length
                  ? "Allowance "
                  : ""}
                <span>
                  {getInvalidAllowanceAndPromo(
                    invalidIdsData?.inValidAllowanceIds
                  )}{" "}
                  needs to be updated
                </span>{" "}
              </span>
            </p>
          ) : null}
          {invalidIdsData?.inValidPromotionIds?.length ? (
            <p className="ml-6">
              {eventType === efConstants?.ALLOWANCE_SCREEN_TYPES["DP"].key ? (
                <span>
                  {" "}
                  {invalidIdsData?.inValidPromotionIds.length
                    ? "Promotion "
                    : ""}
                  <span>
                    {getInvalidAllowanceAndPromo(
                      invalidIdsData?.inValidPromotionIds
                    )}{" "}
                    needs to be updated
                  </span>{" "}
                </span>
              ) : null}
            </p>
          ) : null}
        </>
      ) : null}
    </div>
  );
}

export default InvalidAllowPromoFieldMessage;
