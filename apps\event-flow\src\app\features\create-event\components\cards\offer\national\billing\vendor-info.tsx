import { displayData } from "../../offer-sections/billing/billing-info-allowance";

export const VendorInfo = ({ uniqueVendorNames }) => {
  return (
    <>
      <div className="py-4 px-3 bg-[#F3F4F6] text-sm text-[#5a697b]">
        <p id="abs-common-billing-information-text1">
          For reference, the items in this Offer are setup under the following
          Merchandising System Vendors:
        </p>
      </div>

      {uniqueVendorNames?.map(vendor => {
        return (
          <div
            className="flex gap-4"
            id="abs-common-billing-information-merch-vendor"
          >
            {displayData(
              "ABS Merch Vendor",
              vendor?.allowanceBillingInfo?.absMerchVendor,
              true
            )}
            {displayData(
              "ABS Vendor Name",
              vendor?.allowanceBillingInfo?.absVendorName
            )}
          </div>
        );
      })}
    </>
  );
};
