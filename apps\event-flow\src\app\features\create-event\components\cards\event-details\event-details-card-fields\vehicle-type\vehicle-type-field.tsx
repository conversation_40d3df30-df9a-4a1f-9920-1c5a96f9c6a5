import VehicleTypes from "../../../layout/vehicle-types-custom-date";
import { add, sub } from "date-fns";
import { useFormContext } from "react-hook-form";
import { EEVENT_STATUS } from "@me/util-helpers";
import { useSelectorWrap } from "@me/data-rtk";
import { useLocation, useParams } from "react-router-dom";
import _ from "lodash";
import { memo } from "react";
import { getFieldNameValue } from "../../utility/utility";
import { addYearIfNotExists, getYearFromDate } from "@me-upp-js/utilities";
import { validateNCDP } from "../../event-details-card-service";

function VehicleTypeField({
  vehicleType,
  formFields,
  setFormFields,
  isEditEvent,
  startWeek,
  eventName,
  show,
  setShow,
  setVehicleTypeFieldChanged,
  year,
}) {
  const { setValue, getValues, clearErrors } = useFormContext();
  const { id: eventId } = useParams();
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const minDateForVehicleType = sub(new Date(), { years: 2 });
  const maxDateForVehicleType = add(new Date(), { years: 2 });

  const location = useLocation();
  const state: any = location.state || {};
  const { data: eventTypeAndDivisionsData } = useSelectorWrap(
    "event_type_and_divisions"
  );
  const eventType = eventTypeAndDivisionsData?.eventType || "";

  const promoActiveStatus = () => {
    const promotionsList =
      eventDetailsData?.promotionsLists?.[0]?.promotionsList;
    return promotionsList?.some(
      promotion => promotion?.promotionWorkflowStatus === EEVENT_STATUS?.ACTIVE
    );
  };
  const setVehicleTypeValues = ele => {
    setValue("eventCreationVehicle.vehicleNm", ele.name);
    setValue("eventCreationVehicle.year", formFields?.yearVal.toString());
    setValue("vehicleType", ele?.name);
    setValue("startDate", "");
    setValue("endDate", "");
    setValue("startWeekVehicle", "");
    setFormFields(prevState => ({
      ...prevState,
      vehicleTypeId: getValues("eventCreationVehicle.vehicleType.id"),
      customStartDate: "",
      customEndDate: "",
      enableStartWeekVehicle: true,
    }));
  };
  const updateEventName = () => {
    const name = getValues("name");
    if (eventDetailsData?.name !== getValues("name")) {
      const formEventName = getFieldNameValue(
        state,
        eventDetailsData,
        getValues
      );
      setValue(eventName.registerField, formEventName || name);
    }
  };
  const onVehicleTypeHandler = ele => {
    clearErrors("vehicleType");
    setVehicleTypeFieldChanged(true);
    if (ele?.type === "CustomDate") {
      const date = ele?.customDates?.split(" - ");
      setValue(
        "startDate",
        !formFields?.isPastDate && promoActiveStatus()
          ? eventDetailsData?.startDate
          : date?.[0]
      );
      setValue("eventCreationVehicle.year", formFields?.yearVal.toString());
      setValue("endDate", date[1]);
      setFormFields(prevState => ({
        ...prevState,
        vehicleTypeId: ele.id,
        customStartDate: date?.[0],
        customEndDate: date?.[1],
        vehicleTypeProps: ele.type,
        enableStartWeekVehicle: true,
      }));
      setValue("vehicleType", date);
      setValue("startWeekVehicle", "");
      setValue(startWeek.registerField, "");
      if (
        getValues("promoProductGroup") &&
        getValues("storeGroupName") &&
        getValues("vehicleType") &&
        !eventId &&
        formFields?.count === 0
      ) {
        updateEventName();
      }
      year.options = addYearIfNotExists(
        year.options,
        getYearFromDate(date?.[0])
      );

      // setEnableStartWeekVehicle(true);
    } else if (ele?.type === "Vehicle") {
      setVehicleTypeValues(ele);
    }
    setFormFields(prevState => ({
      ...prevState,
      dataFetchedFromPeriscope: false,
      vehicleTypeProps: ele.type,
    }));
    setValue("dataFetchedFromPeriscope", false);
  };
  return (
    <div className="!min-h-[40px]">
      {vehicleType ? (
        <VehicleTypes
          onChange={onVehicleTypeHandler}
          fieldProps={vehicleType}
          vehicleTypeProps={formFields?.vehicleTypeProps}
          isEditEvent={isEditEvent}
          isDivisionChanged={formFields?.isDivisionChanged}
          minDate={
            !formFields?.isPastDate && promoActiveStatus()
              ? eventDetailsData?.startDate
              : minDateForVehicleType
          }
          maxDate={maxDateForVehicleType}
          show={show}
          setShow={setShow}
          hideVehicles={!formFields?.isPastDate && promoActiveStatus()}
          // setVehicle={setVehicle}
          isNCDP={validateNCDP(eventType)}
        />
      ) : null}
    </div>
  );
}

export default memo(VehicleTypeField);
