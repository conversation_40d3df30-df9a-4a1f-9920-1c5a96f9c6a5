import { InputSelect } from "@me/input-fields";
import { useFormContext } from "react-hook-form";
import {
  batchSetValueFields,
  resetRegisterFields,
} from "../../utility/utility";
import { memo } from "react";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import { periscopeIdChangesEnabled } from "@me-upp-js/utilities";
import { useSelectorWrap } from "@me/data-rtk";
import { validateNCDP } from "../../event-details-card-service";

function EventDivisionFields({
  setFormFields,
  cardFields,
  isAllowanceOrPromotion,
}) {
  const { setValue, clearErrors } = useFormContext();
  const { division } = cardFields;
  const {
    PERISCOPE_LABELS: { ENTER_DETAILS_LABEL },
  } = efConstants;

  const onDivisionSelect = element => {
    setFormValuesOnChange(element);
    setValue(division.registerField, [element?.id]);
    // setValue(storeGroups?.registerField, []);
    // setValue(planProductGroups?.registerField, []);
    resetRegisterFields(cardFields, setValue);
    batchSetValueFields(division?.batchSetValueFields, "", setValue);
    clearErrors(division?.clearErrorFieldsOnChange);
  };
  const setFormValuesOnChange = element => {
    setFormFields(prevState => ({
      ...prevState,
      isDivisionChanged: element?.id ? true : prevState?.isDivisionChanged,
      groupInd: "",
      divisionId: element?.id,
      customStartDate: "",
      customEndDate: "",
      vehicleTypeId: "",
      vehicleTypeProps: "",
      isMultiVendorPPG: false,
      canCreateEvent: true,
      enableStartWeekVehicle: false,
      enableStoreGroupType: false,
      count: 0,
    }));
  };
  const { data: eventTypeAndDivisionsData } = useSelectorWrap(
    "event_type_and_divisions"
  );
  const eventType = eventTypeAndDivisionsData?.eventType;
  return (
    <>
      {periscopeIdChangesEnabled() ? (
        <div className="text-[#898989] italic">{ENTER_DETAILS_LABEL}</div>
      ) : null}
      <div className="grid grid-cols-4 w-full component-scroll">
        {division ? (
          <InputSelect
            onChange={onDivisionSelect}
            fieldProps={division}
            disabled={validateNCDP(eventType) || isAllowanceOrPromotion}
          ></InputSelect>
        ) : null}
      </div>
      {periscopeIdChangesEnabled() ? (
        <div className="flex-grow-0 flex-shrink-0 w-full h-px  bg-[#fdfdff] border-t border-r border-b-0 border-l border-[#c8daeb]"></div>
      ) : null}
    </>
  );
}

export default memo(EventDivisionFields);
