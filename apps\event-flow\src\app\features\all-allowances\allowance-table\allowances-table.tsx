import { useSelectorWrap } from "@me/data-rtk";
import { RenderStates } from "@me/ui-render-states";
import _ from "lodash";
import { useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import efConstants from "../../../shared/ef-constants/ef-constants";
import {
  getAllowanceMapKey,
  payload_AllowancesItemsQuery_entry,
  sortAllowanceById,
} from "../../create-event/service/allowance/allowance-service";
import {
  useGetAllowanceItemsDifferenceQuery,
  useGetAllowancesItemsQuery,
} from "../../create-event/service/apis/allowance-api";
import { setAllowancesRespCopy } from "../../create-event/service/slice/allowances-dashboard-slices";
import {
  leadDistributorsHandler,
  leadDistributorsModeHandler,
  setInitialLeadDistData,
  setIsLeadDataChange,
} from "../../create-event/service/slice/lead-distributors-slice";
import {
  setFilteredAndSortedIdsAction,
  setTableDataAction,
  updateCaseAmountOnSwitch,
  updateLeadDistributorsInfoAction,
} from "../../create-event/service/slice/table-data-slice";
import { AllowanceTableComponent } from "./allowance-table-component";
import { getFilteredData } from "./allowances-table-filter-data";
import "./allowances-table-styles.scss";
import { AllowancesUnitCostDetails } from "./allowances-unit-cost-details";
import { AllowancesUpcDetails } from "./allowances-upc-details";
import { transformAllowancesData } from "./transformer";
import classNames from "classnames";
import { useGetQueryParams } from "@me/util-helpers";
import { getLeadDistributorInitialState } from "../allowance-lead-distributors/billing-selection-utils";
import { saveToSessionStorage } from "../../../shared/helpers/event-flow-helpers";
import { isAllowanceFeatureEnabled } from "@me-upp-js/utilities";

export const AllowancesTable = () => {
  const [tableWrapClass, setTableWrapClass] = useState("");
  const isTableEmpty = useRef(false);
  const {
    ALLOWANCE_ITEM_DESC_KEY,
    SWITCH_OPTIONS,
    NORECORDS_FOUND_LABEL,
    ITEM_AMOUNTS_SUMMARIZED_KEY,
  } = efConstants;
  const dispatch = useDispatch();
  const {
    queryParamValue: {
      isSummary,
      isEdit: isEditEnable,
      offerAllowancesId,
      group: allowGroup,
      isAllowTypeChange = false,
    },
  } = useGetQueryParams([
    "isEdit",
    "isSummary",
    "offerAllowancesId",
    "group",
    "isAllowTypeChange",
  ]);
  const isEditMode = isEditEnable === "true";
  const isSummaryScreen = isSummary === "true";
  const allowTypeChange =
    isAllowanceFeatureEnabled && isAllowTypeChange === "true";
  const { data: switchValue } = useSelectorWrap("selectedSwitchValue_rn");
  const { data: selectedFiltersData } = useSelectorWrap("selectedFilters_rn");
  const {
    data: { stepData: leadAllowanceData, leadDistMode },
  } = useSelectorWrap("leadDistributors_rn");
  const {
    data: { isLeadChange },
  } = useSelectorWrap("leadDistributorsChanged_rn");

  const {
    data: { tableData, filteredAndSortedIds },
  } = useSelectorWrap("allowanceTableData_rn");
  const {
    data: { allowanceData: allowanceTempWorkData },
  } = useSelectorWrap("allowance_temp_work");
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { data: allowGroupConfigObj } = useSelectorWrap(
    "allowanceTableColsGroupConfig_rn"
  );
  const { headerOnlyAmt, isHideColGroupHeader, showLeadDistributorSection } =
    allowGroupConfigObj || {};

  const allowancetempDataRes =
    allowanceTempWorkData?.allowanceTypeSpecification?.[
      getAllowanceMapKey(allowanceTempWorkData?.allowanceType)
    ]?.allowancesMap?.[allowGroup || ""];

  const skipCalculateNewFinalCostInTable =
    !isEditMode &&
    isSummary !== "true" &&
    !allowancetempDataRes?.[0]?.finalizedAmountsInd;

  const {
    data: allowancesResp,
    error: itemsApiError,
    isFetching: isAllowanceRespFetched,
  } = useGetAllowancesItemsQuery(
    ...payload_AllowancesItemsQuery_entry(
      isSummaryScreen,
      allowanceTempWorkData?.tempWorkAllowanceId
    )
  ); //API Data

  const {
    data: allowItemDiffResp,
    error: allowItemDiffRespError,
    isFetching: allowItemDiffRespFetched,
  } = useGetAllowanceItemsDifferenceQuery(
    {
      URL_PARAM: offerAllowancesId,
    },
    {
      skip: !offerAllowancesId || !isSummary,
      refetchOnMountOrArgChange: true,
    }
  ); //API Data
  // on lead distributor data changes, update the tableData
  useEffect(() => {
    dispatch(
      updateLeadDistributorsInfoAction({
        leadDistMapping: leadAllowanceData,
        leadDistMode,
      })
    );
    dispatch(
      setIsLeadDataChange({
        isLeadChange: !isLeadChange,
      })
    );
  }, [leadAllowanceData]);

  useEffect(() => {
    setTableDataOnPgLoad_Filter();
  }, [selectedFiltersData, tableData]);

  /**
   * This useEffect is used to update the allowance amount when switch value changes.
   *
   */
  useEffect(() => {
    updateAmtOnSwitch();
  }, [switchValue?.selectedSwitch]);

  useEffect(() => {
    if (allowGroupConfigObj && Object.keys(allowGroupConfigObj)?.length) {
      setTableContainerClasses();
    }
  }, [JSON.stringify(allowGroupConfigObj)]);

  // on getting data from amounts api, fill data in form
  useEffect(() => {
    allowancesResp &&
      !isAllowanceRespFetched &&
      allowancetempDataRes &&
      Object.keys(allowGroupConfigObj || {})?.length &&
      executeOnlyOnce();
    if (allowancesResp) {
      saveToSessionStorage(
        ITEM_AMOUNTS_SUMMARIZED_KEY,
        allowancesResp?.summary?.itemAmountsCouldBeSummarized || false
      );
      isTableEmpty.current = !allowancesResp.allowances?.length;
    }
  }, [
    allowancesResp,
    allowancetempDataRes,
    isAllowanceRespFetched,
    allowGroupConfigObj,
  ]);

  useEffect(() => {
    isSummary &&
      allowItemDiffResp &&
      !allowItemDiffRespFetched &&
      eventDetailsData?.id &&
      setTableDataForHighlightSummary();
    if (isSummary && allowItemDiffResp) {
      isTableEmpty.current = !allowItemDiffResp.allowances?.length;
    }
  }, [allowItemDiffResp, allowItemDiffRespFetched, eventDetailsData]);

  // on table filter changes update the filterAndSortedIds
  function setTableDataOnPgLoad_Filter() {
    const { isFilterApplied, isFilterReset } = selectedFiltersData;
    let table_Data = tableData;
    if (isFilterApplied || isFilterReset) {
      table_Data = filterData();
    }
    const data: any = sortSaveData(table_Data);
    dispatch(setFilteredAndSortedIdsAction(data.map(item => item.itemId)));
  }

  //By default, sort Items By Descriptions before populating it in form
  const sortSaveData = data => {
    const sortItemsByDescriptions = obj => {
      return obj[ALLOWANCE_ITEM_DESC_KEY];
    };

    const sorted_plannedProductGroupItems = _.sortBy(
      data,
      sortItemsByDescriptions
    );
    return sorted_plannedProductGroupItems;
  };

  const updateAmtOnSwitch = () => {
    const isSwitching =
      SWITCH_OPTIONS[allowanceTempWorkData?.allowanceType]?.["isSwitching"];
    if (!isSwitching) return;
    dispatch(
      updateCaseAmountOnSwitch({
        switchValue: switchValue?.selectedSwitch,
      })
    );
  };

  const setTableContainerClasses = () => {
    //By setting the container class to table, style the table

    const wrapperClassName = classNames({
      tableWrapper: true,
      isHideColGroupHeader: isHideColGroupHeader,
    });

    setTableWrapClass(wrapperClassName);
  };

  const executeOnlyOnce = () => {
    const isFromTemp =
      !!allowancetempDataRes.length &&
      allowancetempDataRes[0]?.finalizedAmountsInd;
    const sortedAllowances =
      (allowancesResp?.allowances?.length &&
        sortAllowanceById([...allowancesResp?.allowances])) ||
      allowancesResp?.allowances;
    let transformed_data = transformAllowancesData(
      _.cloneDeep(
        isEditMode
          ? sortedAllowances
          : isFromTemp
          ? allowancetempDataRes
          : sortedAllowances
      ),
      headerOnlyAmt,
      (isEditMode && !allowTypeChange) || isFromTemp,
      isEditMode,
      allowanceTempWorkData?.allowanceType,
      false,
      false,
      skipCalculateNewFinalCostInTable,
      showLeadDistributorSection,
      isAllowanceFeatureEnabled && allowTypeChange
    );

    //Adding index information to get the index of the updated text fields when validating for empty fields
    transformed_data = transformed_data.map((item, rowIndex) => ({
      rowIndex,
      ...item,
    }));
    dispatch(setTableDataAction(transformed_data));

    const leadOptions = sortedAllowances?.map(item => {
      return _.pick(item, ["vendorNbr", "costAreaDesc", "vendorName"]);
    });
    const leadDistMode = sortedAllowances?.[0]?.leadDistributorMode || null;
    const leadRadioMode = getLeadDistributorInitialState(transformed_data);
    dispatch(
      leadDistributorsHandler({
        leadOptions,
        leadDistMode,
        leadSelectionType: leadRadioMode,
      })
    );
    dispatch(
      leadDistributorsModeHandler({
        leadDistributorsMode: leadRadioMode,
      })
    );
    const leadDistInitialData = getLeadDistributorsSelected(
      sortedAllowances,
      leadDistMode
    );
    dispatch(
      setInitialLeadDistData({
        leadDistData: leadDistMode ? leadDistInitialData : [],
      })
    );

    dispatch(
      setAllowancesRespCopy({
        ...allowancesResp,
        allowances: sortedAllowances,
      })
    );
  };

  const setTableDataForHighlightSummary = () => {
    let transformed_data = transformAllowancesData(
      _.cloneDeep(allowItemDiffResp?.allowances),
      headerOnlyAmt,
      true,
      isEditMode,
      eventDetailsData?.allowanceType,
      true,
      allowItemDiffResp?.pendingChangesFlag,
      skipCalculateNewFinalCostInTable,
      showLeadDistributorSection
    );

    //Adding index information to get the index of the updated text fields when validating for empty fields
    transformed_data = transformed_data.map((item, rowIndex) => ({
      rowIndex,
      ...item,
    }));
    dispatch(setTableDataAction(transformed_data));
  };
  const getLeadDistributorsSelected = (allowances, leadDistMode) => {
    const selectedAllowances = leadDistMode
      ? allowances?.filter(allowObj => allowObj?.leadDistributorInd)
      : [];
    return selectedAllowances?.map(({ vendorNbr, leadDistributorInfos }) => ({
      id: vendorNbr,
      child: leadDistributorInfos?.map(distObj => distObj?.vendorNbr) || [],
    }));
  };
  const filterData = () => {
    let data;
    if (selectedFiltersData?.isFilterReset) {
      //If no filter applied / filter is reset, display the entire data from the response.
      data = tableData;
    } else if (selectedFiltersData?.filteredOptions) {
      //If filter is applied, display the filtered data
      data = getFilteredData({
        selectedFiltersData,
        transformed_data: tableData,
      });
    }
    return data;
  };

  // created the data for table according to the filtered and sorted Data
  const filteredAndSortedTableData = filteredAndSortedIds.map(itemId => {
    return tableData.find(item => item.itemId === itemId);
  });

  const renderDetails = {
    isApiLoading: isAllowanceRespFetched || allowItemDiffRespFetched,
    apiError: { msg: (itemsApiError || allowItemDiffRespError)?.data?.message },
    isPageLevelSpinner: true,
    isRenderMainHtml: isSummary
      ? !allowItemDiffRespFetched && allowItemDiffResp && tableData?.[0]
      : tableData && tableData[0],
    noRecordsMessage: isTableEmpty.current ? NORECORDS_FOUND_LABEL : "",
    renderHtml: (
      <>
        <div
          className={tableWrapClass}
          id="abs-allowances-table-allowance-table-component-div"
        >
          <AllowanceTableComponent items={filteredAndSortedTableData} />
        </div>

        <AllowancesUpcDetails />
        <AllowancesUnitCostDetails
          isSummary={isSummaryScreen}
          offerAllowancesId={offerAllowancesId}
        />
      </>
    ),
  };
  return <RenderStates details={renderDetails} />;
};
