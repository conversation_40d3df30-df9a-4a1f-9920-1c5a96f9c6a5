import { useEffect, useRef, useState } from "react";
import { useSelectorWrap } from "@me/data-rtk";
import LoadingSpinner from "../../../../../../../constants/LoadingSpinner/LoadingSpinner";
import {
  addDaysHelper,
  checkInvalidScanDates,
  createDateKeyMapperObject,
  getFieldWithHighletedWrapper,
  getUpdatedDatesArray,
  renderVendorDetails,
  renderWarehouseDetails,
} from "../allowance-dates-service";
import useAmountsAPIcallInAllowanceDates from "../useAmountsAPIcallInAllowanceDates";
import { InputDatePickerUDS } from "../../../../../../fields/index";
import { isEmpty } from "lodash";
import { FormFieldError } from "@me/util-form-wrapper";
import efConstants from "../../../../../../../../../shared/ef-constants/ef-constants";

export default function ScanAllowanceDates({
  offerIndex,
  allowanceIndex,
  vehicleFields,
  stepperElement,
  datesArray,
  setDatesArray,
  keyVal,
  makeAmountsCallOnDateChange,
  setMakeAmountsCallOnDateChange,
  setIsInvalidDates,
  setIsAmountsApiLoading,
  setIsAmountsApiInvalid,
  errors,
  tempAllowItems,
}) {
  const {
    ALLOWANCE_TYPES,
    OFFER_ALLOWANCE_GROUP,
    WAREHOUSE_HEADER_DISABLE_DISTCENTER_KEY,
  } = efConstants;
  const {
    fields: { startDate, endDate },
  } = stepperElement;

  const vehicleStart = vehicleFields.customStartDate;
  const vehicleEnd = vehicleFields.customEndDate;

  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { isAllowConvEnable } = useSelectorWrap("allow_conv_status_rn").data;

  const isWarehouseOffer = keyVal === OFFER_ALLOWANCE_GROUP.SCAN.COMBINED;
  const typeKey = isWarehouseOffer ? "distCenter" : "vendorNbr";

  const searchId =
    eventDetailsData?.offerAllowances?.[offerIndex || 0]?.offerNumber;
  const offerId = eventDetailsData?.offerAllowances?.[offerIndex || 0]?.id;

  const dates = useRef<any>([]);
  const [count, setCount] = useState(0);
  const [invalidDatesVendor, setInvalidDatesVendor] = useState<any>([]);
  const [dateType, setDateType] = useState<any>([]);

  const {
    vendorsArray: dataFromAmountsAPI,
    isAmountsDataLoading,
    refetch,
  } = useAmountsAPIcallInAllowanceDates({
    offerIndex,
    allowanceIndex,
    stepperElement,
  });

  useEffect(() => {
    setIsAmountsApiLoading(isAmountsDataLoading);
  }, [isAmountsDataLoading]);

  useEffect(() => {
    if (dataFromAmountsAPI?.length || isAllowConvEnable) {
      const vendorsArray = isAllowConvEnable
        ? tempAllowItems
        : dataFromAmountsAPI;
      vendorsArray?.length &&
        setDatesArray(() =>
          getUpdatedDatesArray(
            vendorsArray,
            {
              vehicleStart,
              vehicleEnd,
            },
            !eventDetailsData?.inValidAllowances?.includes(offerId)
          )
        );
    } else {
      setIsAmountsApiInvalid(true);
    }
  }, [dataFromAmountsAPI?.length]);

  useEffect(() => {
    makeAmountsCallOnDateChange && refetch();
    setMakeAmountsCallOnDateChange(false);
  }, [makeAmountsCallOnDateChange]);

  useEffect(() => {
    if (!count) {
      dates.current = datesArray;
    }
    const { inValidVendors, type } = checkInvalidScanDates(
      datesArray,
      vehicleStart,
      vehicleEnd,
      typeKey
    );
    setInvalidDatesVendor(inValidVendors);
    setDateType(type);
  }, [JSON.stringify(datesArray)]);

  useEffect(() => {
    setIsInvalidDates(!isEmpty(invalidDatesVendor));
  }, [invalidDatesVendor]);

  useEffect(() => {
    count && setDatesArray(() => dates.current);
  }, [count]);

  const dateKeyMapper = (key: string, date: string) => {
    return createDateKeyMapperObject(key === "Start Date", date);
  };

  const onVehicleDateChange = (
    vendorKey: string,
    dateType: string,
    date: string,
    index: number
  ) => {
    dates.current = dates.current.map((vendor, i) => {
      return i === index
        ? {
            ...vendor,
            ...(vendorKey && vendor?.[typeKey] === vendorKey
              ? dateKeyMapper(dateType, date)
              : {}),
          }
        : vendor;
    });
    setCount(count => count + 1);
  };

  const getEventData = () => {
    return eventDetailsData;
  };

  const StartDateField = ({ orderStartDate = "", ...rest }, index: number) => {
    const _key = rest?.[typeKey];
    return startDate
      ? getFieldWithHighletedWrapper(
          <InputDatePickerUDS
            fieldProps={startDate}
            customDate={orderStartDate}
            inValid={
              invalidDatesVendor?.includes(_key) &&
              (dateType?.[_key]?.all || dateType?.[_key]?.vehicle)
            }
            onChange={e => {
              onVehicleDateChange(_key, startDate?.label, e, index);
            }}
            minimumDate={addDaysHelper(vehicleStart, -30)}
            maximumDate={addDaysHelper(vehicleEnd, 30)}
            isAllowFlow={true}
          />,
          startDate,
          getEventData,
          searchId
        )
      : null;
  };

  const EndDateField = ({ orderEndDate = "", ...rest }, index: number) => {
    const _key = rest?.[typeKey];
    return endDate
      ? getFieldWithHighletedWrapper(
          <InputDatePickerUDS
            fieldProps={endDate}
            customDate={orderEndDate}
            inValid={
              invalidDatesVendor?.includes(_key) &&
              (dateType?.[_key]?.all || dateType?.[_key]?.vehicle)
            }
            onChange={e => {
              onVehicleDateChange(_key, endDate?.label, e, index);
            }}
            minimumDate={addDaysHelper(vehicleStart, -30)}
            maximumDate={addDaysHelper(vehicleEnd, 30)}
            isAllowFlow={true}
          />,
          endDate,
          getEventData,
          searchId
        )
      : null;
  };

  const renderScanDateFields = (index: number) => {
    return (
      <>
        {[StartDateField, EndDateField].map(field =>
          field(dates?.current?.[index], index)
        )}
      </>
    );
  };

  return (
    <div className="pb-2">
      <LoadingSpinner
        isLoading={isAmountsDataLoading}
        classname="!h-full !w-full rounded-md"
      />

      {dates.current?.length
        ? datesArray?.map((vendor: any, index: number) => (
            <>
              {isWarehouseOffer
                ? datesArray?.length === 1 &&
                  vendor?.distCenter === WAREHOUSE_HEADER_DISABLE_DISTCENTER_KEY
                  ? null
                  : renderWarehouseDetails(vendor, false, true)
                : renderVendorDetails(vendor)}
              <div>
                <div className="grid grid-cols-4 gap-2 mt-4">
                  {renderScanDateFields(index)}
                </div>
                {invalidDatesVendor?.includes(vendor?.[typeKey]) && (
                  <div className="flex flex-col gap-1 my-1">
                    {Object.keys(dateType?.[vendor?.[typeKey]] || {})?.map(
                      (type: string) =>
                        dateType?.[vendor?.[typeKey]]?.[type] && (
                          <FormFieldError
                            error={
                              errors.INVALID_DATES?.[
                                ALLOWANCE_TYPES.SCAN.key
                              ]?.[type]
                            }
                          />
                        )
                    )}
                  </div>
                )}
              </div>
            </>
          ))
        : null}
    </div>
  );
}
