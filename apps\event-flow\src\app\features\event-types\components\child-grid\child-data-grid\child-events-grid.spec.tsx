import React from "react";
import { render } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import ChildEventsGrid from "./child-data-grid";
import useGetGridConfigData from "../hooks/useGetGridConfigData";
import Datagrid from "./datagrid";

jest.mock("../hooks/useGetGridConfigData");
jest.mock("./datagrid", () => jest.fn(() => <div data-testid="datagrid" />));

describe("ChildEventsGrid", () => {
  const mockGridData = [{ id: 1, name: "Event 1" }];
  const mockGridConfig = { sortable: true };
  const mockColumns = [{ field: "name", headerName: "Name" }];

  beforeEach(() => {
    (useGetGridConfigData as jest.Mock).mockReturnValue({
      gridData: mockGridData,
      gridConfig: mockGridConfig,
      columns: mockColumns,
    });
  });

  it("renders the Datagrid component with correct props", () => {
    const { getByTestId } = render(
      <ChildEventsGrid cardIndex={0} isFetching={false} />
    );
    expect(getByTestId("datagrid")).toBeInTheDocument();
    expect(Datagrid).toHaveBeenCalledWith(
      {
        cardIndex: 0,
        gridData: mockGridData,
        gridConfig: mockGridConfig,
        columns: mockColumns,
        isFetching: false,
      },
      {}
    );
  });

  it("calls useGetGridConfigData with correct parameters", () => {
    render(<ChildEventsGrid cardIndex={1} isFetching={false} />);
    expect(useGetGridConfigData).toHaveBeenCalledWith({ cardIndex: 1 });
  });
});
