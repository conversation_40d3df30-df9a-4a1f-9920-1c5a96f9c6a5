import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import { FormProvider, useForm } from "react-hook-form";
import { Provider } from "react-redux";
import { app_store } from "@me/data-rtk";
import CardContainerLayout from "./common-card";
import * as selectors from "@me/data-rtk";
import {
  CardConfigMock,
  EventDetailsDataSelectorMock,
} from "../event-details/event-details-mocks";
import { MockedProvider } from "@apollo/client/testing";
import { mockCardConfiguration } from "./card-container-data-mock";
import * as ALLOWANCE_SERVICE from "../../../service/apis/allowance-api";

const Wrapper = props => {
  const formMethods = useForm<any>({
    defaultValues: {
      name: "Daelmans Stroopwafels - 148569 - S - 2 - 27 GMHBC Sep Feature 2023",
      startDate: "2023-09-27",
      endDate: "2023-10-31",
      divisionIds: ["27"],
      divisions: [],
      offerAllowances: [
        {
          divisionIds: ["27"],
          periscopeIds: ["897205"],
        },
      ],
      promotionsList: [
        {
          periscopePromoId: "897198",
          forecast: {
            id: "64eceb49740df07e415b013e",
            quadrant: "3",
            forecastSales: 6111,
            forecastUnits: 1574,
            forecastAgp: -24,
            forecastAgpPercent: -0.39,
            markdown: 3972,
            markdownPercent: 50,
            incrementalUnits: 322,
            incrementalPercent: 25.77,
            incrementalSales: -1905,
            incrementalSalesPercent: -23.87,
            incrementalAgp: -2604,
            incrementalAgpPercent: -101.43,
            vendorFunding: 689,
            coverage: 17.35,
            coveragePercent: 17.35,
            periscopePromotionId: "897198",
            promotionObjectId: "64eceb0b752c0072378515f0",
            planEvent: "64eceaa21e739b08a1678161",
            lastUpdated: "2023-08-28T18:45:29.980Z",
            lastUpdateBy: "meupp-periscope-bk-azr",
          },
        },
      ],
      promotionsLists: [
        {
          promotionsList: [
            {
              periscopePromoId: "897198",
              forecast: {
                id: "64eceb49740df07e415b013e",
                quadrant: "3",
                forecastSales: 6111,
                forecastUnits: 1574,
                forecastAgp: -24,
                forecastAgpPercent: -0.39,
                markdown: 3972,
                markdownPercent: 50,
                incrementalUnits: 322,
                incrementalPercent: 25.77,
                incrementalSales: -1905,
                incrementalSalesPercent: -23.87,
                incrementalAgp: -2604,
                incrementalAgpPercent: -101.43,
                vendorFunding: 689,
                coverage: 17.35,
                coveragePercent: 17.35,
                periscopePromotionId: "897198",
                promotionObjectId: "64eceb0b752c0072378515f0",
                planEvent: "64eceaa21e739b08a1678161",
                lastUpdated: "2023-08-28T18:45:29.980Z",
                lastUpdateBy: "meupp-periscope-bk-azr",
              },
            },
          ],
        },
      ],
    },
  });
  return (
    <Provider store={app_store}>
      <MockedProvider>
        <FormProvider {...formMethods}>{props.children}</FormProvider>
      </MockedProvider>
    </Provider>
  );
};

jest.mock("./pdf-viewer", () => ({
  PdfViewer: () => <></>,
}));

describe("Common card", () => {
  class ResizeObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
  }
  beforeEach(() => {
    window.ResizeObserver = ResizeObserver as any;
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      switch (sel_name) {
        case "event_plan_card_update_details":
          return {
            data: {
              EVENT_PLAN_CARD: 0,
              isPromotionAdded: false,
            },
          };
        case "allowance_new_card_configuration_rn":
          return {
            data: {
              isNewAllowanceDetails: true,
            },
          };
        case "apiError_rn":
          return {
            data: {
              apiErrorsMsgs: [],
              showTokenError: false,
            },
          };
        case "event_details_data": {
          return {
            data: {
              ...EventDetailsDataSelectorMock?.data,
              offerAllowances: [],
            },
          };
        }
        case "event_status_change_indicators":
          return {
            data: { isEventStatusChanged: false },
          };
        case "is_event_edit_enable": {
          return {
            data: {
              isEventCardOpen: {},
            },
          };
        }
        case "offer_card_configutation_rn": {
          return {
            data: {
              editCardConfig: {},
              openCardConfig: {},
            },
          };
        }
        case "promo_card_configutation_rn":
          return {
            data: {
              openCardConfig: {},
              editCardConfig: {},
              isAddNewPromo: {},
            },
          };
        case "by_pass_offer_allowance": {
          return {
            data: { isByPassOfferAllowanceSelected: false },
          };
        }
        case "promotion_edit_enable_configutation_rn":
          return {
            data: { isEditPromotion: {} },
          };
        case "promotion_regular_price_data":
          return {
            data: {
              minRegularPrice: 0,
              maxRegularPrice: 0,
              maxListCost: 0,
              minListCost: 0,
              maxListAGP: 0,
              minListAGP: 0,
            },
          };

        case "allowance_temp_work":
          return {
            data: { id: "hh" },
          };
        case "lastPromoStep_rn":
          return {
            data: { isPromoCardOnLastStep: false },
          };
        case "isAddingPromoConfig_rn":
          return {
            data: { isAddingPromoStore: false },
          };
        case "offer_sub_card_configutation_rn": {
          return {
            data: {
              offerSubCardConfig: {},
            },
          };
        }
        case "promo_sub_card_configutation_rn": {
          return {
            data: {
              promoSubCardConfig: {},
            },
          };
        }
        case "eventProgressConfigData_rn":
          return {
            data: {},
          };
        case "allow_type_change_rn": {
          return {
            data: {},
          };
        }
        case "national_offer_divisions": {
          return {
            data: { offerDivisions :[]},
          };
        }
        default:
          break;
      }
    });
    jest
      .spyOn(ALLOWANCE_SERVICE, "usePostAllowanceToBeCreatedMutation")
      .mockReturnValue([
        jest.fn,
        {
          data: [{ productSources: [{}] }],
        },
      ]);
  });
  it("should load event card", () => {
    jest
      .spyOn(ALLOWANCE_SERVICE, "usePostAllowanceToBeCreatedMutation")
      .mockReturnValue([
        jest.fn,
        {
          data: [{ productSources: [{}] }],
        },
      ]);
    render(
      <Wrapper>
        <CardContainerLayout
          cardConfiguration={CardConfigMock}
          sectionIndex={0}
          step={0}
          cardFieldProp={null}
        />
      </Wrapper>
    );
    expect(screen.queryByText("Event Details ")).toBeDefined();
  });
  it("should load allowance card", () => {
    jest
      .spyOn(ALLOWANCE_SERVICE, "usePostAllowanceToBeCreatedMutation")
      .mockReturnValue([
        jest.fn,
        {
          data: [{ productSources: [] }],
        },
      ]);
    const { baseElement } = render(
      <Wrapper>
        <CardContainerLayout
          cardConfiguration={mockCardConfiguration}
          sectionIndex={0}
          step={0}
          cardFieldProp={null}
        />
      </Wrapper>
    );
    expect(baseElement).toBeDefined();
  });
});
