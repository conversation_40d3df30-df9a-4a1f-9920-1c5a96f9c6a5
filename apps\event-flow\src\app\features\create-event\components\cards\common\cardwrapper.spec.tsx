import "@testing-library/jest-dom";
import React from "react";
import { render, screen } from "@testing-library/react";
import { useFormContext } from "react-hook-form";
import CardWrapper from "./CardWrapper";
import AllowanceCardContent from "../allowance/allowance-card-content";
import PromotionDetailsCardContent from "../promotion-details/promotion-details-card-content";
import OfferCard from "../offer/offer-card";
import { appConstants } from "@me/utils-root-props";

// Mock dependencies
jest.mock("react-hook-form", () => ({
  useFormContext: jest.fn(),
}));

jest.mock("../allowance/allowance-card-content", () => jest.fn(() => <div>AllowanceCardContent</div>));
jest.mock("../promotion-details/promotion-details-card-content", () =>
  jest.fn(() => <div>PromotionDetailsCardContent</div>)
);
jest.mock("../offer/offer-card", () => jest.fn(() => <div>OfferCard</div>));

describe("CardWrapper", () => {
  const mockControl = {};
  const mockGetValues = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useFormContext as jest.Mock).mockReturnValue({
      control: mockControl,
      getValues: mockGetValues,
    });
  });

  it("renders AllowanceCardContent when cardConfiguration.key is 'Allowance' and IS_ENABLE_REDESIGN_ALLOWANCE_CHANGES is false", () => {
    appConstants.IS_ENABLE_REDESIGN_ALLOWANCE_CHANGES = false;

    render(
      <CardWrapper
        cardConfiguration={{ key: "Allowance" }}
        cardIndex={0}
        cardItemIndex={1}
        isNew={true}
        handleCancel={jest.fn()}
        isEditMode={false}
      />
    );

    expect(screen.getByText("AllowanceCardContent")).toBeInTheDocument();
    expect(screen.queryByText("OfferCard")).not.toBeInTheDocument();
    expect(screen.queryByText("PromotionDetailsCardContent")).not.toBeInTheDocument();
  });

  it("renders OfferCard when cardConfiguration.key is 'Allowance' and IS_ENABLE_REDESIGN_ALLOWANCE_CHANGES is true", () => {
    appConstants.IS_ENABLE_REDESIGN_ALLOWANCE_CHANGES = true;

    render(
      <CardWrapper
        cardConfiguration={{ key: "Allowance" }}
        cardIndex={0}
        cardItemIndex={1}
        isNew={true}
        handleCancel={jest.fn()}
        isEditMode={false}
      />
    );

    expect(screen.getByText("OfferCard")).toBeInTheDocument();
    expect(screen.queryByText("AllowanceCardContent")).not.toBeInTheDocument();
    expect(screen.queryByText("PromotionDetailsCardContent")).not.toBeInTheDocument();
  });

  it("renders PromotionDetailsCardContent when cardConfiguration.key is not 'Allowance'", () => {
    render(
      <CardWrapper
        cardConfiguration={{ key: "Promotion" }}
        cardIndex={0}
        cardItemIndex={1}
        isNew={true}
        handleCancel={jest.fn()}
        isEditMode={false}
      />
    );

    expect(screen.getByText("PromotionDetailsCardContent")).toBeInTheDocument();
    expect(screen.queryByText("AllowanceCardContent")).not.toBeInTheDocument();
    expect(screen.queryByText("OfferCard")).not.toBeInTheDocument();
  });

  it("passes the correct props to PromotionDetailsCardContent", () => {
    const handleCancelMock = jest.fn();

    render(
      <CardWrapper
        cardConfiguration={{ key: "Promotion" }}
        cardIndex={0}
        cardItemIndex={1}
        isNew={true}
        handleCancel={handleCancelMock}
        isEditMode={true}
      />
    );

    expect(PromotionDetailsCardContent).toHaveBeenCalledWith(
      expect.objectContaining({
        cardIndex: 0,
        cardItemIndex: 1,
        cardConfiguration: { key: "Promotion" },
        control: mockControl,
        getValues: mockGetValues,
        handleCancel: handleCancelMock,
        isEditMode: true,
      }),
      {}
    );
  });
});