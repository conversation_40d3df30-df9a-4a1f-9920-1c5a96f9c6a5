/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { RootState, useSelectorWrap } from "@me/data-rtk";
import { MouseEventHandler, useState, useEffect, useRef } from "react";
import {
  TypedUseSelectorHook,
  useSelector as useReduxSelector,
} from "react-redux";
import "../../../../../features/create-event/components/select-dropdown.scss";
import {
  useGetVehicleTypesByDivisionIdAndStartEndDateQuery,
  useGetVehicleTypesByDivisionIdQuery,
  useGetVehicleTypesByDivisionIdAndStartDateQuery,
} from "../../../../../graphql/generated/schema";
import { useFormContext } from "react-hook-form";
import CustomDate from "./custom-date";
import { DateObject } from "albertsons-react-multi-date-picker";
import { isPast, lightFormat } from "date-fns";

export const useSelector: TypedUseSelectorHook<RootState> = useReduxSelector;

const VehicleMenu = (props: {
  show: boolean;
  setTimeframeStart: React.Dispatch<React.SetStateAction<string>>;
  setTimeframeEnd: React.Dispatch<React.SetStateAction<string>>;
  minDate?: Date | string | number | DateObject;
  maxDate?: Date | string | number | DateObject;
  optionValue: { name: string; value: string; selectedType: string };
  onClose: MouseEventHandler<HTMLDivElement>;
  // setVehicle: Function;
  fieldProps: any;
  onChange: any;
  dynamicRegField?: any;
  hideVehicles?: boolean;
}) => {
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { data: eventTypeAndDivisionsData } = useSelectorWrap(
    "event_type_and_divisions"
  );

  const eventType =
    eventTypeAndDivisionsData?.eventType || eventDetailsData?.eventType;
  const { getValues, setValue } = useFormContext();
  const divisionID = eventType === "NDP" ? "98" : getValues("divisionIds")?.[0];
  const startDate = getValues("startDate");
  const endDate = getValues("endDate");
  const [gqlQuery, setGqlQuery] = useState<any>();

  const buildQueryVariablesforAPI = () => {
    switch (props.fieldProps.gqlQueryConst) {
      case "events":
        return {
          variables: {
            divId: divisionID,
            startDate: eventDetailsData?.startDate,
            endDate: eventDetailsData?.endDate,
          },
        };
      case "allowance":
        return {
          variables: {
            divId: divisionID,
            startDate: startDate,
            endDate: endDate,
          },
        };
      case "promotions": {
        const didEventStartInPast = isPast(new Date(startDate));
        const currentDate = lightFormat(new Date(), "yyyy-MM-dd");
        return {
          variables: {
            divId: divisionID,
            startDate: didEventStartInPast ? currentDate : startDate,
            endDate: endDate,
          },
        };
      }
      default:
        return {};
    }
  };

  const getQuery = () => {
    if (props.fieldProps.gqlQueryConst === "events") {
      return props.hideVehicles
        ? useGetVehicleTypesByDivisionIdAndStartDateQuery
        : useGetVehicleTypesByDivisionIdQuery;
    } else if (
      props.fieldProps.gqlQueryConst === "allowance" ||
      props.fieldProps.gqlQueryConst === "promotions"
    ) {
      return useGetVehicleTypesByDivisionIdAndStartEndDateQuery;
    }
    return params => {
      const data =
        props.fieldProps?.options?.map(ele => {
          return ele;
        }) || [];
      return {
        data,
      };
    };
  };

  const getQueryParams = buildQueryVariablesforAPI();
  const applyQuery = getQuery();
  const { data: vehicleTypesData } = applyQuery(getQueryParams);

  useEffect(() => {
    props.fieldProps.gqlQueryConst === "events" && !props.hideVehicles
      ? setGqlQuery(vehicleTypesData?.getVehicleTypesByDivisionId)
      : setGqlQuery(vehicleTypesData?.getVehicleTypesByDivisionIdAndStartDate);
  }, [props.fieldProps, vehicleTypesData]);

  const [periodStart, setPeriodStart] = useState(""); // to store the period start date and show the date range when user selects a period
  const [periodEnd, setPeriodEnd] = useState(""); // to store the period end date and show the date range when user selects a period

  const applyVehicleScrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    applyVehicleScrollRef.current?.scrollIntoView({
      behavior: "smooth",
      block: "center",
    });
  }, [applyVehicleScrollRef]);

  const [activeTab, setActiveTab] = useState(
    props?.optionValue?.selectedType === "CustomDate" ? "CustomDate" : "Vehicle"
  ); //to toggle between the tabs

  const [isApplyBtnDisplay, setApplyBtnDisplay] = useState(false);
  const [isVehicleSelected, setIsVehicleSelected] = useState(
    props?.optionValue?.selectedType === "Vehicle"
      ? props?.optionValue?.value
      : ""
  );

  useEffect(() => {
    const defaultTab =
      props?.optionValue?.selectedType === "CustomDate" ||
      gqlQuery?.length === 1
        ? "CustomDate"
        : "Vehicle";
    handleActiveTab(defaultTab);
  }, [vehicleTypesData, gqlQuery]);

  function handleActiveTab(tab) {
    setActiveTab(tab);
    setApplyBtnDisplay(false);
  }

  const handleApply = () => {
    const changeParams =
      activeTab === "Vehicle"
        ? getValues(
            props.dynamicRegField
              ? `${props.dynamicRegField}.vehicleType`
              : "eventCreationVehicle.vehicleType"
          )
        : getValues([
            props.dynamicRegField
              ? `${props.dynamicRegField}.startDate`
              : "eventCreationVehicle.startDate",
            props.dynamicRegField
              ? `${props.dynamicRegField}.endDate`
              : "eventCreationVehicle.endDate",
          ]);
    const customDateType = gqlQuery?.find(
      ele => ele?.vehicleTypDesc === "Custom Date"
    );

    props.onChange(changeParams, activeTab, customDateType);
  };

  const selectVehicle = (e, event) => {
    setValue(
      props.dynamicRegField
        ? `${props.dynamicRegField}.vehicleType`
        : "eventCreationVehicle.vehicleType",
      e
    );
    setIsVehicleSelected(e?.id);
    props.onClose(event);
    handleApply();
  };
  const handleApplyButtonCustDate = value => {
    setApplyBtnDisplay(value);
  };

  const currentTab = "CustomDate";

  return (
    <div id="abs-vehicle-menu-container">
      <div
        id="abs-vehicle-menu"
        className="w-[560.41px] h-[350px] absolute overflow-hidden rounded-[7.77px] mt-[5px] bg-white z-60 display:block "
        style={{
          boxShadow: "0px 0px 9.329999923706055px 0 rgba(3,59,105,0.2)",
        }}
      >
        {renderTabContent(props.fieldProps, props.onChange)}

        <section
          className="relative"
          id="abs-vehicle-menu-render-close-btn-sec"
        >
          {renderCloseBtn()}
          {activeTab !== "Vehicle" ? renderApplyBtn() : null}
        </section>

        <div
          className="w-[0.78px] h-[285.02px] absolute left-[151.61px] top-[-0.78px] bg-[#c8daeb] "
          id="abs-vehicle-menu-render-tabs-sec"
        />

        {renderTabs()}
        {renderBottomBorder()}
        {renderSeperatorLine()}
        {/* {currentTab === "CustomDate" && renderLegends()} */}
      </div>
    </div>
  );

  function renderCloseBtn() {
    return (
      <div
        id="abs-vehicle-menu-render-close-btn-cont2"
        className="flex flex-col justify-start items-start absolute right-24 top-[303px] cursor-pointer"
        onClick={props.onClose}
      >
        <div
          className="flex flex-col justify-center items-center flex-grow-0 flex-shrink-0 h-[31.1px] overflow-hidden gap-[7.77px] px-[9.33px] py-[7.77px] rounded-[7px] bg-white border-[2.22px] border-[#1b6ebb] btn-cancel w-[60px]"
          id="abs-vehicle-menu-cancel-sec1"
        >
          <div
            className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-[6.21999979019165px]"
            id="abs-vehicle-menu-cancel-sec2"
          >
            <p
              className="timefram-dropdown__footer-button flex-grow-0 flex-shrink-0  text-center font-bold text-[#1b6ebb]"
              id="abs-vehicle-menu-cancel-text"
            >
              Cancel
            </p>
          </div>
        </div>
      </div>
    );
  }

  function renderTabContent(fieldProps, onChangeHandler) {
    return (
      <section className="w-[560px]" id="abs-vehicle-menu-active-tab-sec1">
        {activeTab === "Vehicle" ? (
          <div
            className="absolute h-[280px] w-[560px] left-[152px] p-0 overflow-y-scroll component-scroll"
            id="abs-vehicle-menu-component-scroll-sec1"
          >
            {gqlQuery?.length ? (
              gqlQuery
                ?.filter(ele => ele?.vehicleTypDesc !== "Custom Date")
                .map((vehicleRefElem, i) => (
                  <div
                    id="abs-vehicle-menu-active-tab-sec1-1"
                    key={vehicleRefElem?.id}
                    className={`flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-2.5 p-2 ${
                      isVehicleSelected === vehicleRefElem?.id
                        ? "bg-[#e0edfb]"
                        : ""
                    }`}
                    onClick={event => selectVehicle(vehicleRefElem, event)}
                  >
                    <p
                      className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative rounded cursor-pointer text-left text-[#121213] "
                      id="abs-vehicle-menu-vehicle-ref-elem-sec1"
                    >
                      {vehicleRefElem?.vehicleTypDesc}
                    </p>
                  </div>
                ))
            ) : (
              <span className="flex justify-start items-center p-9 [#121213]">
                {divisionID
                  ? "Please wait, loading Vehicle Type options"
                  : "Please select Division"}
              </span>
            )}
          </div>
        ) : (
          ""
        )}
        {activeTab === "CustomDate" ? (
          <div
            className="absolute  left-[152px] p-0"
            id="abs-vehicle-menu-custom-date-sec1"
          >
            <CustomDate
              setPeriodStart={setPeriodStart}
              setPeriodEnd={setPeriodEnd}
              minDate={props?.minDate}
              maxDate={props?.maxDate}
              fieldProps={fieldProps}
              handleApplyButton={handleApplyButtonCustDate}
              dynamicRegField={props.dynamicRegField}
              hideVehicles={props?.hideVehicles}
            />
          </div>
        ) : (
          ""
        )}
      </section>
    );
  }

  function renderSeperatorLine() {
    return (
      <div
        className="flex justify-start items-start w-[12.44px] h-[190.49px] absolute left-[139.95px] top-[12.44px] gap-[7.774999618530273px] p-[3.109999895095825px]"
        id="abs-vehicle-menu-justify-start-sec1"
      >
        <div
          className="self-stretch flex-grow w-[6.22px] h-[184.27px] relative"
          id="abs-vehicle-menu-self-stretch-sec1"
        />
      </div>
    );
  }

  function renderBottomBorder() {
    return (
      <svg
        id="abs-vehicle-menu-svg-sec1"
        width={712}
        height={2}
        viewBox="0 0 712 2"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="absolute left-[-0.78px] top-[284.24px]"
        preserveAspectRatio="xMidYMid meet"
      >
        <line
          x1="-3.39856e-8"
          y1="0.63127"
          x2="711.412"
          y2="0.631207"
          stroke="#7296B8"
          widthstroke-="0.7775"
        />
      </svg>
    );
  }

  function renderLegends() {
    return (
      <div
        className="flex justify-start items-start absolute left-3.5 bottom-[24px] gap-3"
        id="abs-vehicle-menu-render-period-enddt-sec1"
      >
        {renderPeriodEndDt()}
        {renderQuarterEndDt()}
        {renderHolidays()}
      </div>
    );

    function renderHolidays() {
      return (
        <div
          className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-1"
          id="abs-vehicle-menu-holidays-cont"
        >
          <svg
            id="abs-vehicle-menu-holidays-cont-svg1"
            width={8}
            height={9}
            viewBox="0 0 8 9"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="flex-grow-0 flex-shrink-0"
            preserveAspectRatio="xMidYMid meet"
          >
            <circle cx={4} cy="4.5" r="3.5" fill="#C74E4E" stroke="#C74E4E" />
          </svg>
          <p
            className="flex-grow-0 flex-shrink-0 text-[10.88499927520752px] text-center text-[#2b303c]"
            id="abs-vehicle-menu-holidays-text"
          >
            Holidays
          </p>
        </div>
      );
    }

    function renderQuarterEndDt() {
      return (
        <div
          className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-1"
          id="abs-vehicle-menu-quarter-cont"
        >
          <svg
            id="abs-vehicle-menu-quarter-cont-svg"
            width={8}
            height={9}
            viewBox="0 0 8 9"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="flex-grow-0 flex-shrink-0"
            preserveAspectRatio="xMidYMid meet"
          >
            <circle cx={4} cy="4.5" r="3.5" fill="black" stroke="black" />
          </svg>
          <p
            className="flex-grow-0 flex-shrink-0 text-[10.88499927520752px] text-center text-[#2b303c]"
            id="abs-vehicle-menu-quarter-end-date-cont"
          >
            Quarter End Date
          </p>
        </div>
      );
    }
  }

  function renderPeriodEndDt() {
    return (
      <div
        className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-1"
        id="abs-vehicle-menu-period-end-date-cont"
      >
        <svg
          id="abs-vehicle-menu-period-end-date-cont-svg12"
          width={8}
          height={9}
          viewBox="0 0 8 9"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="flex-grow-0 flex-shrink-0"
          preserveAspectRatio="xMidYMid meet"
        >
          <circle cx={4} cy="4.5" r="3.5" stroke="black" />
        </svg>
        <p
          className="flex-grow-0 flex-shrink-0 text-[10.88499927520752px] text-center text-[#2b303c]"
          id="abs-vehicle-menu-period-end-date-sec1"
        >
          Period End Date
        </p>
      </div>
    );
  }

  function renderTabs() {
    return (
      <div
        className="flex flex-col justify-start items-start absolute left-0 top-2 gap-[3.109999895095825px] pb-[9.329999923706055px]"
        id="abs-vehicle-menu-vehicle-sec2"
      >
        {gqlQuery?.length > 1 ? (
          <div
            id="abs-vehicle-menu-vehicle-sec3"
            className={`flex justify-start items-center flex-grow-0 flex-shrink-0 w-[151px] h-12 relative rounded cursor-pointer ${
              activeTab === "Vehicle" ? "bg-[#F5F9FD]" : ""
            }`}
            onClick={() => handleActiveTab("Vehicle")}
          >
            <div
              id="abs-vehicle-menu-vehicle-sec4"
              className={
                activeTab === "Vehicle"
                  ? "flex-grow-0 flex-shrink-0 w-1 h-12 bg-[#3997ef]"
                  : "flex-grow-0 flex-shrink-0 w-1 h-12 bg-white"
              }
            />
            <div
              className="flex flex-col justify-center items-center self-stretch flex-grow-0 flex-shrink-0 overflow-hidden px-4 rounded-tl rounded-tr"
              id="abs-vehicle-menu-vehicle-sec5"
            >
              <div
                className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-2"
                id="abs-vehicle-menu-vehicle-sec6"
              >
                <p
                  className="timefram-dropdown__tabs flex-grow-0 flex-shrink-0  font-semibold text-left text-[#2b303c]"
                  id="abs-vehicle-menu-vehicle-text-sec6"
                >
                  Vehicle
                </p>
              </div>
            </div>
          </div>
        ) : null}

        <div
          id="abs-vehicle-menu-vehicle-customdate-sec6"
          className={`flex justify-start items-center flex-grow-0 flex-shrink-0 w-[151px] h-12 relative rounded cursor-pointer ${
            activeTab === "CustomDate" ? "bg-[#F5F9FD]" : ""
          }`}
          onClick={() => handleActiveTab("CustomDate")}
        >
          <div
            id="abs-vehicle-menu-vehicle-customdate-sec7"
            className={
              activeTab === "CustomDate"
                ? "flex-grow-0 flex-shrink-0 w-1 h-12 bg-[#3997ef]"
                : "flex-grow-0 flex-shrink-0 w-1 h-12 bg-white"
            }
          />
          <div
            className="flex flex-col justify-center items-center self-stretch flex-grow-0 flex-shrink-0 overflow-hidden px-4 rounded-tl rounded-tr"
            id="abs-vehicle-menu-vehicle-customdate-sec8"
          >
            <div
              className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-2"
              id="abs-vehicle-menu-vehicle-customdate-sec9"
            >
              <p
                className="timefram-dropdown__tabs flex-grow-0 flex-shrink-0 font-semibold text-left text-[#2b303c]"
                id="abs-vehicle-menu-vehicle-customdate-text-sec10"
              >
                Custom Date
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  function renderApplyBtn() {
    return (
      <div
        id="apply-btn"
        className={`flex flex-col justify-start items-start absolute top-[303px] right-6 cursor-pointer rounded-[7px] ${
          isApplyBtnDisplay
            ? "pointer-events-auto cursor-pointer bg-[#1b6ebb]"
            : "opacity-25 pointer-events-none cursor-not-allowed bg-[#ccc]"
        } `}
        onClick={() => {
          handleApply();
        }}
      >
        <div
          id="abs-vehicle-menu-vehicle-apply-text-cont"
          className="flex flex-col justify-center items-center flex-grow-0 flex-shrink-0 h-[31.1px] overflow-hidden gap-[9.774999618530273px] px-[9.329999923706055px] py-[4.664999961853027px]  w-[60px] btn-apply"
          onClick={props.onClose}
        >
          <div
            id="abs-vehicle-menu-vehicle-apply-text-sec1"
            className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-[6.21999979019165px]"
            ref={applyVehicleScrollRef}
          >
            <p
              className="timefram-dropdown__footer-button flex-grow-0 flex-shrink-0  text-center font-semibold text-white"
              id="abs-vehicle-menu-vehicle-apply-text-sec2"
            >
              Apply
            </p>
          </div>
        </div>
      </div>
    );
  }
};

export default VehicleMenu;
