export interface IEventCommentsSectionProps {
  eventId: string;
  isEventCommentsOpen: boolean;
  setIsEventCommentsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  allCommentsList: Array<IPlanEventCommentDTO>;
  isEventCommentsLoading: boolean;
  parentCommentUpdater: any;
  setCommentOpen: any;
}

export interface IPlanEventCommentProps {
  id?: IObjectIdProps | string;
  messageText: string;
  createUserNm: string;
  createUserId: string;
  lastUpdUserId: string;
  userType?: string;
  notifiedUsers: Array<string>;
  createTs: string | number;
  lastUpdTs: string | number;
  sendBackWithComment?: boolean;
  eventStatus?: string;
  taskId?: IObjectIdProps | string;
  subType?: string;
  planEventCommentId?: IObjectIdProps | string;
  taskAlertStatus?: string;
  requestForChange?: boolean;
}

export interface IObjectIdProps {
  timestamp: number;
  date: string;
}

export enum CommentCategory {
  EXTERNAL = "External",
  INTERNAL = "Internal",
  BILLING_INQUIRY = "Billing Inquiry",
  NOT_FOUND = "Not Found",
}

export enum CommentPermissions {
  VIEW_EXTERNAL = "PROMOTION_COMMENTS_VIEW_EXTERNAL",
  VIEW_INTERNAL = "PROMOTION_COMMENTS_VIEW_INTERNAL",
  VIEW_BILLING = "PROMOTION_COMMENTS_VIEW_BILL_INQUIRY",
  EDIT_EXTERNAL = "PROMOTION_COMMENTS_EDIT_EXTERNAL",
  EDIT_INTERNAL = "PROMOTION_COMMENTS_EDIT_INTERNAL",
  EDIT_BILLING = "PROMOTION_COMMENTS_EDIT_BILL_INQUIRY",
}
interface IOfferPromoList {
  id?: string;
  type?: string;
}

export interface ISendBackWithCommentsDtls {
  offers?: Array<IOfferPromoList>;
  promos?: Array<IOfferPromoList>;
}

export interface IPlanEventCommentDTO {
  id?: string;
  planEvent: string;
  commentCategory: CommentCategory | string;
  eventComments: Array<IPlanEventCommentProps>;
  createUserId: string;
  createTs: string | number;
  sendBackWithComment?: boolean;
  eventStatus?: string;
  sendBackWithCommentsDtls?: ISendBackWithCommentsDtls;
  requestForChange: boolean;
}
