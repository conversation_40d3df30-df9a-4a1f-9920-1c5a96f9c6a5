import { Column } from "@albertsons/uds/molecule/Table/Table.types";
import { useSelectorWrap } from "@me/data-rtk";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import Tooltip from "@albertsons/uds/molecule/Tooltip";
import { DASHBOARD_SLICE_CONFIG } from "apps/promotion-management/src/app/config/dashboard-config";
import { DASHBOARD_CONSTANTS } from "apps/promotion-management/src/app/constants";
import { onModalToggle } from "apps/promotion-management/src/app/library/data-access/dashboard/dsd-funding-slice";
import { useUpcFormatter } from "@me/util-helpers";
import { sortUpcsByLength, sortSize } from "@me-upp-js/utilities";

type DSDFunding = {
  index: number;
  itemDescription: string;
  itemId: string;
  primaryUpc: string;
  itemUpcs: any;
  packWhse: number;
  size: string;
  vendorDetails: any;
  allowanceAmount: any;
};

export const DSDColumns = (excludedOffers: number[], itemHeaderHeight = 40) => {
  const dispatch = useDispatch();
  const { dsdFundingView } = DASHBOARD_SLICE_CONFIG;
  const { data: dsdFundingData } = useSelectorWrap(dsdFundingView.SLICE_KEY);
  const {
    data: { viewItemDetailsColumns },
  } = useSelectorWrap(dsdFundingView.TOGGLE_ITEM_DETAILS_COLS_SLICE_KEY);
  const {
    data: { viewOnlyPotentialFundingOmission },
  } = useSelectorWrap(dsdFundingView.TOGGLE_POTENTIAL_FUNDING_SLICE_KEY);

  const [columns, setColumns] = useState<Column<DSDFunding>[]>([]);

  const [formattedUPC, formatUPC] = useUpcFormatter();

  const RenderUpcs = rowData => {
    const caseUpc = rowData?.caseUpc,
      caseUpcs = rowData?.itemUpcs?.filter(itemUpc => itemUpc == caseUpc);
    const caseUpcsLength =
      typeof rowData?.caseUpc == "object"
        ? rowData?.caseUpc?.length
        : caseUpcs?.length;
    const displayUpcDetails = (e, rowData) => {
      e?.preventDefault();
      dispatch(onModalToggle({ isOpen: true, modalData: rowData }));
    };

    return (
      <>
        <a
          href="#"
          className="text-[#1b6ebb] px-3"
          title={rowData?.itemUpcs}
          onClick={e => displayUpcDetails(e, rowData)}
        >
          {caseUpcsLength}
        </a>
      </>
    );
  };

  //This is to set the height of the unpinned table header
  const rederCicLabel = label => {
    return (
      <div
        style={{ height: `${itemHeaderHeight}px` }}
        className="content-center font-bold text-sm leading-4 text-dark-text select-none whitespace-nowrap truncate"
      >
        {label}
      </div>
    );
  };

  const getFormatedUpcValue = (upcValue = "") => {
    return (
      <div className="px-3 text-sm leading-4 text-dark-text select-none whitespace-nowrap truncate">
        {formatUPC(upcValue)}
      </div>
    );
  };

  const mainColumns: Column<DSDFunding>[] = [
    {
      id: "itemId",
      label: () => rederCicLabel("CIC"),
      align: "center",
      value: "itemId",
      sortable: (i1: any, i2: any) => i1?.itemId - i2?.itemId,
      pinned: false,
    },
    {
      id: "itemDescription",
      label: "Item Description",
      value: "itemDescription",
      sortable: (i1, i2) =>
        i1?.itemDescription?.localeCompare(i2?.itemDescription),
      pinned: false,
    },
    {
      id: "primaryUpc",
      label: "Primary UPC",
      value: rowData => getFormatedUpcValue(rowData?.primaryUpc),
      sortable: (i1, i2) => i1?.primaryUpc?.localeCompare(i2?.primaryUpc),
      hidden: !viewItemDetailsColumns,
      align: "center",
    },
    {
      id: "UPCs",
      label: "UPCs",
      value: rowData => RenderUpcs(rowData),
      sortable: (i1, i2) => sortUpcsByLength(i1, i2),
      hidden: !viewItemDetailsColumns,
      align: "center",
    },
    {
      id: "packWhse",
      label: "Pack",
      value: "packWhse",
      hidden: !viewItemDetailsColumns,
      align: "center",
      sortable: (i1, i2) => i1?.packWhse - i2?.packWhse,
    },
    {
      id: "size",
      label: "Size",
      value: "size",
      hidden: !viewItemDetailsColumns,
      align: "center",
      sortable: (i1, i2) => sortSize(i1?.size, i2?.size),
    },
  ];

  const getVendorColsList = () => {
    const vendorCols: Column<any>[] = getVenderCols();
    return vendorCols;
  };

  const getVendorCols = () => {
    const newObject = {};
    dsdFundingData?.dsdVendorFunding?.dsdVendors?.forEach(vendor => {
      const _vendorId = vendor?.vendorId;
      newObject[_vendorId] = {
        vendorName: vendor.vendorName,
        costAreaDescList: [],
        costAreaList: [],
      };

      dsdFundingData?.dsdVendorFunding?.dsdFundingItems?.forEach(item => {
        item.dsdOffersFundings.forEach(funding => {
          const {
            vendorId = {},
            costAreaDesc = "",
            dsdOfferOverlapFunding = [],
            itemDsdVendorAuthorized = true,
          } = funding || {};
          const itemVendorId = `${vendorId?.vendorNbr}-${vendorId?.vendorSubAccount}`;
          const costAreaNumber = vendorId?.costArea;
          if (itemVendorId === _vendorId) {
            const isCostAreaAvailable =
              newObject?.[_vendorId]?.costAreaDescList?.includes(costAreaDesc);
            if (!isCostAreaAvailable) {
              newObject[_vendorId].costAreaDescList.push(costAreaDesc);
              newObject[_vendorId].costAreaList.push(costAreaNumber);
              newObject[_vendorId].vendorId = _vendorId;
            }
            if (!newObject?.[_vendorId]?.omissionsInfo?.[costAreaDesc])
              newObject[_vendorId].omissionsInfo = {
                ...(newObject?.[_vendorId]?.omissionsInfo || {}),
                [costAreaDesc]:
                  (!dsdOfferOverlapFunding?.length &&
                    itemDsdVendorAuthorized) ||
                  !!dsdOfferOverlapFunding?.filter(
                    item =>
                      !excludedOffers.includes(item?.offer) &&
                      (item?.convertedAmount === 0 || !item?.includeInd)
                  )?.length,
              };
          }
        });
      });
    });
    return newObject;
  };

  useEffect(() => {
    setColumns([...mainColumns, ...getVendorColsList()]);
  }, [
    viewOnlyPotentialFundingOmission,
    viewItemDetailsColumns,
    excludedOffers,
    itemHeaderHeight,
  ]);

  const getOverlapOfferAmount = overlapItems => {
    const result = overlapItems?.reduce(
      (res, item) => {
        if (!excludedOffers.includes(item?.offer)) {
          res.offers.push({
            offer: item?.offer,
            basisDsc: item?.basisDsc,
            convertedAmount: item?.convertedAmount,
            includeInd: item?.includeInd,
          });
          if (item?.includeInd) {
            res.amount += item?.convertedAmount;
          }
        }
        return res;
      },
      { amount: 0, offers: [] }
    );
    return result;
  };

  const omissionSvg = (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="lucide lucide-octagon-alert"
      color="#bf2812"
    >
      <polygon points="7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2" />
      <line x1="12" x2="12" y1="8" y2="12" />
      <line x1="12" x2="12.01" y1="16" y2="16" />
    </svg>
  );

  const getColumnLabel = (costAreaDesc: any, costArea: any) => {
    return (
      <div className="px-3 text-[14px] leading-[14px] text-[#2B303C]">
        <div>CA {costArea}</div>
        <div className="font-normal capitalize mt-[5px]">{costAreaDesc}</div>
      </div>
    );
  };

  const getGroupColumns = vendorObj => {
    const columns: Column<any>[] = vendorObj?.costAreaDescList
      ?.filter(
        item =>
          !viewOnlyPotentialFundingOmission || vendorObj?.omissionsInfo?.[item]
      )
      ?.map((costAreaDesc, index) => {
        const costArea =
          vendorObj?.costAreaList?.[
            Number(vendorObj?.costAreaDescList?.indexOf(costAreaDesc) || 0)
          ];
        const column: Column<any> = {
          id: `allowamt-${index}`,
          label: getColumnLabel(costAreaDesc, costArea),
          value: rowData => {
            const vendorInfo = rowData?.dsdOffersFundings.find(vendor => {
              const itemVendorId = `${vendor.vendorId.vendorNbr}-${vendor.vendorId.vendorSubAccount}`;
              return (
                vendor?.costAreaDesc === costAreaDesc &&
                itemVendorId === vendorObj?.vendorId
              );
            });
            if (
              vendorInfo?.itemDsdVendorAuthorized &&
              vendorInfo?.dsdOfferOverlapFunding?.length
            ) {
              const overlapsData = getOverlapOfferAmount(
                vendorInfo?.dsdOfferOverlapFunding
              );
              const offers = overlapsData?.offers || [];

              return (
                <div className="flex items-center justify-center">
                  <span className="leading-[16px] text-[14px] text-[#2B303C]">
                    {offers?.length &&
                    (offers?.every(item => !item.includeInd) ||
                      offers?.every(item => item?.convertedAmount === 0)) ? (
                      <span>{omissionSvg}</span>
                    ) : (
                      `$${(overlapsData?.amount).toFixed(2)}`
                    )}
                  </span>

                  {offers?.length > 1 ? (
                    <Tooltip zIndex={10} anchor="right">
                      <Tooltip.Popover>
                        <ul className="p-[12px]">
                          {offers.map((offer, index) => (
                            <li
                              key={index}
                              className="cursor-pointer flex items-center leading-6 text-[12px] text-left text-[#2B303C]"
                            >
                              Offer #{offer?.offer} :&nbsp;{" "}
                              {offer?.includeInd ? (
                                <>
                                  <b>
                                    ${(offer?.convertedAmount).toFixed(2)}{" "}
                                    {
                                      DASHBOARD_CONSTANTS.ALLOWANCE_CD_MAPPER?.[
                                        offer?.basisDsc
                                      ]
                                    }
                                  </b>
                                  {offer?.convertedAmount === 0 ? (
                                    <>&nbsp; {omissionSvg}</>
                                  ) : (
                                    ""
                                  )}
                                </>
                              ) : (
                                <b className="flex items-center justify-center">
                                  {omissionSvg} &nbsp; Excluded
                                </b>
                              )}
                            </li>
                          ))}
                        </ul>
                      </Tooltip.Popover>

                      <span className="flex items-center justify-center">
                        <span className="mx-[5px] rounded abs-ofr-ovrlap-length py-[4px] px-[8px] bg-[#e0fbec] text-[14px] leading-[16px] text-[#2D7207]">
                          {offers?.length || 0}
                        </span>

                        {offers?.some(
                          offer =>
                            !offer.includeInd || offer?.convertedAmount === 0
                        ) &&
                        offers?.some(
                          offer => offer.includeInd || offer?.convertedAmount
                        ) ? (
                          <span>{omissionSvg}</span>
                        ) : (
                          ""
                        )}
                      </span>
                    </Tooltip>
                  ) : null}
                </div>
              );
            } else if (
              vendorInfo?.itemDsdVendorAuthorized &&
              !vendorInfo?.dsdOfferOverlapFunding?.length
            ) {
              return <div>{omissionSvg}</div>;
            }
            return (
              <div className="text-center w-full h-full bg-[#f7f8fa]"></div>
            );
          },
          align: "center",
        };
        return column;
      });
    return columns;
  };

  const getVendorLabel = (vendorObj: any) => {
    return (
      <div className="px-3 text-[14px] leading-[14px] text-[#2B303C]">
        <div>{vendorObj?.vendorName}</div>
        <div>{vendorObj?.vendorId}</div>
      </div>
    );
  };
  const getVenderCols = () => {
    const newObject = getVendorCols();
    const vendorCols = Object.keys(newObject)
      ?.filter(vendorId => {
        const vendorObj = newObject?.[vendorId];
        return !!vendorObj?.costAreaDescList?.filter(
          item =>
            !viewOnlyPotentialFundingOmission ||
            vendorObj?.omissionsInfo?.[item]
        )?.length;
      })
      ?.map((vendorId, index) => {
        const vendorObj = newObject?.[vendorId];
        const vendorColumn: Column<any> = {
          id: `vendorDetails-${index}`,
          label: getVendorLabel(vendorObj),
          align: "center",
          columns: getGroupColumns(vendorObj),
          hidden: vendorObj?.details?.length <= 0,
          className: "dsd-vendor-group-column",
        };
        return vendorColumn;
      });
    return vendorCols;
  };
  return columns;
};
