import { fireEvent, render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import { <PERSON>rowserRouter } from "react-router-dom";
import { app_store } from "@me/data-rtk";
import RequestForChangeModal from "./request-for-change-modal";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import { CTAs_label } from "../../../create-event/constants/event-status/contsants";
import "@testing-library/jest-dom";

jest.mock("../../../create-event/components/cards", () => ({
  PdfViewer: () => <></>,
  CommonModal: ({ modalContent }) => <>{modalContent}</>,
}));

function Wrapper({ children }) {
  return (
    <BrowserRouter>
      <Provider store={app_store}>{children}</Provider>
    </BrowserRouter>
  );
}

describe("render for change modal component", () => {
  let setIsRequestForChangePopupOpen;
  let confrimBtnHandler;

  beforeEach(() => {
    setIsRequestForChangePopupOpen = jest.fn();
    confrimBtnHandler = jest.fn();
  });

  test("renders correctly with given props", () => {
    const { getByText, getByRole } = render(
      <Wrapper>
        <RequestForChangeModal
          isRequestForChangePopupOpen={true}
          setIsRequestForChangePopupOpen={setIsRequestForChangePopupOpen}
          confrimBtnHandler={confrimBtnHandler}
        />
      </Wrapper>
    );

    expect(getByText(efConstants.REQUEST_FOR_CHANGE)).toBeTruthy();
    expect(getByText(CTAs_label.SEND_TO_MERCHANT)).toBeTruthy();
    expect(getByText(efConstants.CONTINUE_EDITING)).toBeTruthy();
    expect(getByRole("textbox")).toBeTruthy();
  });

  test("handles comment input correctly", () => {
    const { getByRole } = render(
      <Wrapper>
        <RequestForChangeModal
          isRequestForChangePopupOpen={true}
          setIsRequestForChangePopupOpen={setIsRequestForChangePopupOpen}
          confrimBtnHandler={confrimBtnHandler}
        />
      </Wrapper>
    );

    const textArea = getByRole("textbox");
    fireEvent.change(textArea, { target: { value: "Test comment" } });

    expect(textArea.innerHTML).toBe("Test comment");
  });
  test("calls setIsRequestForChangePopupOpen with false when modal is closed", () => {
    const { getByText } = render(
      <Wrapper>
        <RequestForChangeModal
          isRequestForChangePopupOpen={true}
          setIsRequestForChangePopupOpen={setIsRequestForChangePopupOpen}
          confrimBtnHandler={confrimBtnHandler}
        />
      </Wrapper>
    );

    const cancelButton = getByText(efConstants.CONTINUE_EDITING);
    fireEvent.click(cancelButton);

    expect(setIsRequestForChangePopupOpen).toHaveBeenCalledWith(false);
  });
  test("disables the confirm button when the comment is empty", () => {
    const { getByText } = render(
      <Wrapper>
        <RequestForChangeModal
          isRequestForChangePopupOpen={true}
          setIsRequestForChangePopupOpen={setIsRequestForChangePopupOpen}
          confrimBtnHandler={confrimBtnHandler}
        />
      </Wrapper>
    );

    const confirmButton = getByText(CTAs_label.SEND_TO_MERCHANT);
    expect(confirmButton).toBeDisabled();
  });
});
