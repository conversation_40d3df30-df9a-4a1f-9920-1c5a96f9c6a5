import { IPlanEventCommentDTO } from "./interfaces/plan-event-comment-interface";

export const mockAllCommentsList: IPlanEventCommentDTO[] = [
  {
    id: "644766f553eea24e88fba2f8",
    planEvent: "6446d376fac61d52c3700e8a",
    commentCategory: "External",
    eventComments: [
      {
        id: "6448229953eea24e88fba413",
        messageText: "",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682449039591,
        lastUpdTs: 1682449039591,
      },
      {
        id: "6448227553eea24e88fba412",
        messageText: "F",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682448984663,
        lastUpdTs: 1682448984663,
      },
      {
        id: "6448224253eea24e88fba411",
        messageText: "",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682448961582,
        lastUpdTs: 1682448961582,
      },
      {
        id: "644811a553eea24e88fba40a",
        messageText: "",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682444699989,
        lastUpdTs: 1682444699989,
      },
      {
        id: "6448116c53eea24e88fba409",
        messageText: "",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682444651727,
        lastUpdTs: 1682444651727,
      },
      {
        id: "64480fc753eea24e88fba403",
        messageText: "aa",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682444230087,
        lastUpdTs: 1682444230087,
      },
      {
        id: "644804bb53eea24e88fba3fe",
        messageText: "aaa",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682441402508,
        lastUpdTs: 1682441402508,
      },
      {
        id: "6448049a53eea24e88fba3fd",
        messageText: "",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682441368984,
        lastUpdTs: 1682441368984,
      },
      {
        id: "6448035f53eea24e88fba3fc",
        messageText: "",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682441054717,
        lastUpdTs: 1682441054717,
      },
      {
        id: "644768f253eea24e88fba2fd",
        messageText: "asdf",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682401519897,
        lastUpdTs: 1682401519897,
      },
      {
        id: "6447672553eea24e88fba2fa",
        messageText: "test 2",
        createUserNm: "ANAMA05",
        createUserId: "ANAMA05",
        lastUpdUserId: "ANAMA05",
        userType: "Vendor",
        notifiedUsers: [],
        createTs: 1682401059356,
        lastUpdTs: 1682401059356,
      },
      {
        id: "644766f553eea24e88fba2f9",
        messageText: "test",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682400990126,
        lastUpdTs: 1682400990126,
      },
    ],
    createUserId: "RROUT04",
    createTs: 1682449049181,
    requestForChange: true,
  },
  {
    id: "644767e153eea24e88fba2fb",
    planEvent: "6446d376fac61d52c3700e8a",
    commentCategory: "Internal",
    eventComments: [
      {
        id: "64483ab053eea24e88fba416",
        messageText: "",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682455208330,
        lastUpdTs: 1682455208330,
      },
      {
        id: "644819f353eea24e88fba40e",
        messageText: "Int",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682446823906,
        lastUpdTs: 1682446823906,
      },
      {
        id: "644818c853eea24e88fba40d",
        messageText: "INT Eger",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682446531595,
        lastUpdTs: 1682446531595,
      },
      {
        id: "6448174f53eea24e88fba40c",
        messageText: "Fry",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682446155227,
        lastUpdTs: 1682446155227,
      },
      {
        id: "64480fcd53eea24e88fba404",
        messageText: "hello",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682444236219,
        lastUpdTs: 1682444236219,
      },
      {
        id: "644804d053eea24e88fba400",
        messageText: "",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682441421713,
        lastUpdTs: 1682441421713,
      },
      {
        id: "644804c353eea24e88fba3ff",
        messageText: "asdf",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682441410238,
        lastUpdTs: 1682441410238,
      },
      {
        id: "6447fc8953eea24e88fba3f9",
        messageText: "",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682439297492,
        lastUpdTs: 1682439297492,
      },
      {
        id: "64476af853eea24e88fba307",
        messageText: "screen",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682402037444,
        lastUpdTs: 1682402037444,
      },
      {
        id: "644767e153eea24e88fba2fc",
        messageText: "rr",
        createUserNm: "ANAMA05",
        createUserId: "ANAMA05",
        lastUpdUserId: "ANAMA05",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682401246497,
        lastUpdTs: 1682401246497,
      },
    ],
    createUserId: "RROUT04",
    createTs: 1682455216780,
    requestForChange: true,
  },
  {
    id: "64476ace53eea24e88fba305",
    planEvent: "6446d376fac61d52c3700e8a",
    commentCategory: "Billing Inquiry",
    eventComments: [
      {
        id: "644844bf53eea24e88fba418",
        messageText: "asdf",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682457789378,
        lastUpdTs: 1682457789378,
      },
      {
        id: "6448368653eea24e88fba415",
        messageText: "443",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682454148384,
        lastUpdTs: 1682454148384,
      },
      {
        id: "6448364653eea24e88fba414",
        messageText: "331",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682454018374,
        lastUpdTs: 1682454018374,
      },
      {
        id: "64481f7953eea24e88fba410",
        messageText: "WW",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682448248623,
        lastUpdTs: 1682448248623,
      },
      {
        id: "64481f6453eea24e88fba40f",
        messageText: "EE",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682448226968,
        lastUpdTs: 1682448226968,
      },
      {
        id: "644811c253eea24e88fba40b",
        messageText: "FF",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682444730086,
        lastUpdTs: 1682444730086,
      },
      {
        id: "6448113053eea24e88fba408",
        messageText: "asdf",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682444589551,
        lastUpdTs: 1682444589551,
      },
      {
        id: "6448106853eea24e88fba407",
        messageText: "cc",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682444389259,
        lastUpdTs: 1682444389259,
      },
      {
        id: "6448105953eea24e88fba406",
        messageText: "qq",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682444375529,
        lastUpdTs: 1682444375529,
      },
      {
        id: "64480fd153eea24e88fba405",
        messageText: "cc Edit",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682444240550,
        lastUpdTs: 1682444245990,
      },
      {
        id: "6448059953eea24e88fba402",
        messageText: "Hee. Well edit",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682441623196,
        lastUpdTs: 1682441643107,
      },
      {
        id: "6448058a53eea24e88fba401",
        messageText: "aa",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682441609294,
        lastUpdTs: 1682441609294,
      },
      {
        id: "64476ace53eea24e88fba306",
        messageText: "hello there",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682401996089,
        lastUpdTs: 1682401996089,
      },
    ],
    createUserId: "RROUT04",
    createTs: 1682457791330,
    requestForChange: true,
  },
];

export const mockAllCommentsListShortened: IPlanEventCommentDTO[] = [
  {
    id: "644766f553eea24e88fba2f8",
    planEvent: "6446d376fac61d52c3700e8a",
    commentCategory: "External",
    eventComments: [
      {
        id: "6447672553eea24e88fba2fa",
        messageText: "test 2",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682401059356,
        lastUpdTs: 1682401059356,
      },
      {
        id: "644766f553eea24e88fba2f9",
        messageText: "test",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682400990126,
        lastUpdTs: 1682400990126,
      },
    ],
    createUserId: "RROUT04",
    createTs: 1682449049181,
    requestForChange: true,
  },
  {
    id: "644767e153eea24e88fba2fb",
    planEvent: "6446d376fac61d52c3700e8a",
    commentCategory: "Internal",
    eventComments: [
      {
        id: "644767e153eea24e88fba2fc",
        messageText: "rr",
        createUserNm: "ANAMA05",
        createUserId: "ANAMA05",
        lastUpdUserId: "ANAMA05",
        userType: "Vendor",
        notifiedUsers: [],
        createTs: 1682401246497,
        lastUpdTs: 1682401246497,
      },
    ],
    createUserId: "RROUT04",
    createTs: 1682455216780,
    requestForChange: true,
  },
  {
    id: "64476ace53eea24e88fba305",
    planEvent: "6446d376fac61d52c3700e8a",
    commentCategory: "Billing Inquiry",
    eventComments: [
      {
        id: "64476ace53eea24e88fba306",
        messageText: "hello there",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        userType: "Merchant",
        notifiedUsers: [],
        createTs: 1682401996089,
        lastUpdTs: 1682401996089,
      },
    ],
    createUserId: "RROUT04",
    createTs: 1682457791330,
    requestForChange: true,
  },
];
