import { render } from "@testing-library/react";
import { Provider } from "react-redux";
import { <PERSON>rowserRouter } from "react-router-dom";
import { app_store } from "@me/data-rtk";
import { EEVENT_STATUS } from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import { CTAs_Keys } from "../../../create-event/constants/event-status/contsants";
import "@testing-library/jest-dom";
import { EUSER_ROLES } from "./event-action.model";
import RenderEventActions from "./render-event-actions";

const mockEventActionService = {
  checkIsValid: jest.fn(),
};

const mockHandleChangeStatus = jest.fn();
const mockRefetch = jest.fn();
const mockPostEventCommentsData = jest.fn().mockResolvedValue({});

jest.mock("../../event-comments-section-service", () => ({
  usePostEventCommentsDataMutation: () => [
    mockPostEventCommentsData,
    { isSuccess: true },
  ],
  useGetCommentsDataQuery: () => ({
    isFetching: false,
    data: [],
    refetch: mockRefetch,
  }),
}));

function Wrapper({ children }) {
  return (
    <BrowserRouter>
      <Provider store={app_store}>{children}</Provider>
    </BrowserRouter>
  );
}

describe("render for change modal component", () => {
  const defaultProps = {
    eventActionService: mockEventActionService,
    eventStatusData: {
      [EEVENT_STATUS.AGREED]: {
        USERS: {
          [EUSER_ROLES.VENDOR]: {
            buttons: [
              {
                label: "Send to Merchant",
                variant: "primary",
                disabled: false,
                key: CTAs_Keys.SEND_TO_MERCHANT,
                validate: {},
              },
            ],
          },
        },
      },
    },
    eventStatus: EEVENT_STATUS.AGREED,
    userRole: EUSER_ROLES.VENDOR,
    planEvent: {},
    shouldRenderAction: jest.fn().mockReturnValue(true),
    divisionIds: [],
    doDisableCtsBtn: false,
    checkForHiddenPriceOrROG: jest.fn().mockReturnValue(false),
    handleChangeStatus: mockHandleChangeStatus,
    planEventHistory: [],
    isApiLoading: false,
    negotiationSimsVendors: [],
    eventId: "123",
  };
  beforeEach(() => {
    jest.clearAllMocks();
  });
  test.skip("renders correctly with given props", () => {
    const { baseElement } = render(
      <Wrapper>
        <RenderEventActions {...defaultProps} />
      </Wrapper>
    );

    expect(baseElement).toBeTruthy();
  });
});
