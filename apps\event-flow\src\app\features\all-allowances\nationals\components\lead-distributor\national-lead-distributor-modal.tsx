import React, { useCallback, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useSelectorWrap } from "@me/data-rtk";
import { isFeatureFlagEnabled } from "@me-upp-js/utilities";
import { appConstants } from "@me/utils-root-props";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";

import Modal from "@albertsons/uds/molecule/Modal";
import DivisionAccordion from "../division-accordion/division-accordion-wrapper";
import LeadDistributorSaveActions from "./lead-distributor-save-actions";
import LeadDistributorHeader from "./lead-distributor-header";
import LeadDistributorSubLabel from "../../../allowance-lead-distributors/lead-distributor-sublabel-text";

import { getMainAndOtherVendors, onCloseDistPopup } from "../../../allowance-lead-distributors/billing-selection-utils";
import { resetDivisionWiseLeadDistData, setIsLeadDistributorError } from "../../../../create-event/service/slice/lead-distributors-slice";

import "./national-lead-distributor-modal.scss";
import DistributorModeSelection from "../common/distributor-mode-selection";

type Props = {
  isModelOpen: boolean;
  setIsModelOpen: (value: boolean) => void;
};

function NationalLeadDistributorsModal({
  isModelOpen = false,
  setIsModelOpen,
}: Props) {
  const dispatch = useDispatch();

  // 🟢 Fetching data from store
  const { data: leadDistData = {} } =
    useSelectorWrap("leadDistributors_rn") || {};
  const { data: divisionWiseLeadDistData } = useSelectorWrap(
    "division_wise_lead_dist_data_rn"
  );
  const { data: { tableData = [], allDivisionsTableData = [] } = {} } =
    useSelectorWrap("allowanceTableData_rn") ?? {};
  const { data: nationalDivisionsConfig = {} } =
    useSelectorWrap("national_divisions_config") || {};
  const { data: allowGroupConfigObj = {} } =
    useSelectorWrap("allowanceTableColsGroupConfig_rn") || {};

  // 🟢 Extracting necessary values
  const {
    leadDistMode: defaultModeSelection,
    allDivisionLeadOptions,
    stepData: selectedSteps,
    leadSelectionType,
  } = leadDistData;
  const { selectedDivisionData = {}, divisionsList = [] } =
    nationalDivisionsConfig;

  // 🟢 Feature flag check
  const isMultiVendorFeatureEnabled = isFeatureFlagEnabled(
    appConstants.FEATURE_FLAGS.MULTI_VENDOR
  );

  const isAnyExternalPresentAcrossDiv = isMultiVendorFeatureEnabled
    ? allDivisionsTableData?.find(currentDivData => {
        const { otherVendors = [] } =
          getMainAndOtherVendors(
            currentDivData?.tableData || [],
            allDivisionLeadOptions?.[currentDivData?.divisionId]?.map(
              item => item?.vendorNbr
            ) || []
          ) || {};
        return otherVendors?.length > 0;
      })
    : false;
  // 🟢 State for lead distribution mode
  const [leadDistMode, setLeadDistMode] = useState(
    defaultModeSelection || efConstants.LEAD_DIST_ONLY
  );

  // 🟢 Effect: Set leadDistMode if `isExternalVendor` is true
  useEffect(() => {
    if (isAnyExternalPresentAcrossDiv) {
      setLeadDistMode(efConstants.BILL_LEAD_DIST);
    }
  }, [isAnyExternalPresentAcrossDiv]);

  // 🟢 Effect: Check if there are more than 2 selected leads and dispatch error
  useEffect(() => {
    const isInvalidSelection = divisionsList?.some(({ divisionId }) => {
      const selectedList = divisionWiseLeadDistData?.[divisionId]?.selectedList;
      return selectedList?.length > 2;
    });
    dispatch(
      setIsLeadDistributorError({ isLeadDistributorError: isInvalidSelection })
    );
  }, [JSON.stringify(divisionWiseLeadDistData)]);

  // 🟢 Check if all divisions have valid lead selections (1 or 2 per division)
  const isAllDivLeadsSelected = useCallback(() => {
    return divisionsList?.every(({ divisionId }) => {
      if(!allDivisionLeadOptions?.[divisionId]) return true;
      const selectedList = divisionWiseLeadDistData?.[divisionId]?.selectedList;
      return selectedList?.length >= 1 && selectedList?.length <= 2;
    });
  }, [JSON.stringify(divisionsList), JSON.stringify(divisionWiseLeadDistData)]);

  const popperCloseArgs = {
    selectedSteps,
    defaultModeSelection,
    dispatch,
    setIsModelOpen,
    leadSelectionType,
  };

  return (
    <Modal
      isOpen={isModelOpen}
      onClose={() => {
        dispatch(resetDivisionWiseLeadDistData());
        onCloseDistPopup(popperCloseArgs);
      }}
      height={720}
      className="overflow-hidden national-lead-distributor"
      width="65vw"
    >
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex-shrink-0 mb-4">
          <LeadDistributorHeader />
        </div>

        {/* Main Content */}
        <div className="flex-grow overflow-y-auto display-scroll">
          <DistributorModeSelection
            distMode={leadDistMode}
            setDistMode={setLeadDistMode}
            distConfig={allowGroupConfigObj?.leadDistConfig}
            isExternalVendor={isAnyExternalPresentAcrossDiv}
            isBillingSection={false}
          />
          <LeadDistributorSubLabel />
          <DivisionAccordion />
        </div>

        {/* Actions */}
        <div className="flex-shrink-0">
          <LeadDistributorSaveActions
            isAllDivLeadsSelected={isAllDivLeadsSelected}
            leadDistMode={leadDistMode}
            setIsModelOpen={setIsModelOpen}
            divisionWiseLeadDistData={divisionWiseLeadDistData}
            currentDivisionId={selectedDivisionData?.divisionId}
            isNdpType={true}
          />
        </div>
      </div>
    </Modal>
  );
}
export default React.memo(NationalLeadDistributorsModal);
