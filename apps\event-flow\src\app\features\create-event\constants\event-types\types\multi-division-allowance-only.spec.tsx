import { NATIONAL_DIVISION_ALLOWANCE_ONLY } from "./multi-division-allowance-only";
import { EVENT_ALLOWANCE } from "../../fields/allowance/allowance-steps-config";
import { EVENT_DETAILS_ALLOWANCE_ONLY_FIELDS } from "../../fields/event-details/event-details-allowance-only";

describe("NATIONAL_DIVISION_ALLOWANCE_ONLY Config", () => {
  it("should have correct top-level properties", () => {
    expect(NATIONAL_DIVISION_ALLOWANCE_ONLY).toHaveProperty("section", "Allowance Only");
    expect(NATIONAL_DIVISION_ALLOWANCE_ONLY).toHaveProperty("sections", ["Event Details", "Allowance"]);
    expect(NATIONAL_DIVISION_ALLOWANCE_ONLY).toHaveProperty("generate_event_name", ["promoProductGroup", "startWeekVehicle"]);
    expect(NATIONAL_DIVISION_ALLOWANCE_ONLY).toHaveProperty("showField", false);
    expect(NATIONAL_DIVISION_ALLOWANCE_ONLY).toHaveProperty("progress_types", ["Event Details", "Allowance"]);
  });

  it("should have correct Event Details section", () => {
    const eventDetails = NATIONAL_DIVISION_ALLOWANCE_ONLY["Event Details"];
    expect(eventDetails).toHaveProperty("section", "Event Details");
    expect(eventDetails).toHaveProperty("fields", EVENT_DETAILS_ALLOWANCE_ONLY_FIELDS);
    expect(eventDetails).toHaveProperty("saveLabel", "Save Event Details & Add Allowance");
    expect(eventDetails).toHaveProperty("eventTypeName", "AO");
    expect(eventDetails).toHaveProperty("showField", false);
  });

  it("should have correct Allowance section", () => {
    const allowance = NATIONAL_DIVISION_ALLOWANCE_ONLY["Allowance"];
    expect(allowance).toHaveProperty("section", "Allowance");
    expect(allowance).toHaveProperty("fields", EVENT_ALLOWANCE);
    expect(allowance).toHaveProperty("offerTitle", "New Offer");
    expect(allowance).toHaveProperty("allowanceTye", "Case");
    expect(allowance).toHaveProperty("allowanceToBeCreated", "Both");
    expect(allowance).toHaveProperty("nextLevelLabel", "Skip to Promotion Details");
    expect(allowance).toHaveProperty("saveAndCreateAllowance", "Save & Create Allowance");
  });
});
