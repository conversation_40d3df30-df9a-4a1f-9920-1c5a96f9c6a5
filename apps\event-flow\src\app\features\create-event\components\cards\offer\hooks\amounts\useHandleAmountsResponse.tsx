import { useSelectorWrap } from "@me/data-rtk";
import { useDeleteNationalAllowanceTempWorkDataMutation } from "../../../../../service/apis/allowance-api";
import { useDispatch } from "react-redux";
import { allowanceTempWorkHandler } from "../../../../../service/slice/allowance-temp-work-slice";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";

export const useHandleAmountsResponse = () => {
  const dispatch = useDispatch();
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const {
    data: { allowanceData: allowanceTempWorkData },
  } = useSelectorWrap("allowance_temp_work");
  const {
    data: { deletedDivisions = [], offerDivisions = [] },
  } = useSelectorWrap("national_offer_divisions") || {};

  const [
    deleteNationalAllowanceTempWorkData,
    { isLoading: isTempDelInprogress },
  ] = useDeleteNationalAllowanceTempWorkDataMutation();

  const { id: eventId = "", eventType = "" } = eventDetailsData || {};

  const handleTempDeleteForInvalidItems = async (
    amountsResponse,
    tempResponse = []
  ) => {
    if (eventType !== efConstants.ALLOWANCE_SCREEN_TYPES.NDP.key)
      return amountsResponse;
    const validDivList = amountsResponse?.map(item => item?.divisionId);
    const availableDivsInTemp: any[] = [];
    let tempData = tempResponse?.length ? tempResponse : allowanceTempWorkData;
    const delDivList = tempResponse?.length
      ? offerDivisions?.filter(divId => !validDivList?.includes(divId))
      : deletedDivisions;

    if (delDivList?.length && eventId) {
      tempData = tempData?.filter(item => {
        const isDivdeleted = delDivList?.includes(item?.divisionId);
        isDivdeleted && availableDivsInTemp.push(item?.divisionId);
        return !isDivdeleted;
      });

      if (availableDivsInTemp?.length) {
        const isDeleteSuccess = await deleteNationalAllowanceTempWorkData(
          {
            URL_PARAM: eventId,
            queryParams: { divisionIds: delDivList?.join() },
          },
          {
            skip: !eventId,
          }
        );

        if (isDeleteSuccess) {
          dispatch(
            allowanceTempWorkHandler({
              allowanceData: tempData,
            })
          );
        }
      }
    }

    return { amountsResp: amountsResponse, tempList: tempData };
  };

  return { handleTempDeleteForInvalidItems, isTempDelInprogress };
};
