import efConstants from "../../../../../shared/ef-constants/ef-constants";
import {
  getAllowanceAmountsSubHeader,
  getAllowanceBillingInfoSubHeader,
  getAllowanceCaseDatesSubHeader,
  getAllowanceDates<PERSON><PERSON><PERSON>eader,
  getAllowanceStoreSelectionSubHeader,
  getAllowanceToBeCreatedSubHeader,
  getAllowanceTypePerformanceSubHeader,
  getDSDAllowanceDatesSubHeader,
} from "../../../service/allowance/allowance-sub-level-header";

import { ALLOWANCE_DATES } from "./field-allowance-dates";
import { ALLOWANCE_TO_BE_CREATED } from "./field-allowance-to-be-created";
import { ALLOWANCE_TYPE_PERFORMANCE } from "./field-allowance-type-performance";
import { STORE_SELECTION } from "./field-store-selection";
import { ALLOWANCE_AMOUNT } from "./field-allowance-amount";
import { BILLING_INFORMATION } from "./field-billing-information";
import {
  ALLOWANCE_AMNTS_HISTORY_KEYS,
  ALLOWANCE_AMOUNTS_PREVIEW_FIELDS,
  ALLOWANCE_DATES_HISTORY_KEYS,
  ALLOWANCE_DATES_PREVIEW_FIELDS,
  ALLOWANCE_DEFAULT_BILLING_PREVIEW_FIELDS,
  ALLOW_AMOUNT_HEADER_FIELDS,
  ALLOW_DATE_HEADER_FIELDS,
  DEFAULT_BILLING_INFO_HIST_KEYS,
  PROMOTION_DATES_PREVIEW_FIELDS,
  PROMO_DATE_HEADER_FIELDS,
  PROMO_DETAILS_PREVIEW_FIELDS,
  WHSE_ALLOW_DATES_HISTORY_KEYS,
  PROMO_DETAILS_HISTORY_KEYS,
  PROMOTION_DATES_HISTORY_KEYS,
  ALLOWANCE_HEADER_FLAT_AMOUNT,
  ALLOW_HEADERFLAT_HEADER_FIELD,
  ALLOW_HEADERFLAT_HEADER_HISTORY_KEYS,
} from "./allowance-preview-fields-config";
import { appConstants } from "@me/utils-root-props";

const { ALLOWANCE_SCREEN_TYPES, PRODUCT_SOURCE_INFO } = efConstants;
const { DP, AO, NDP, NCDP } = ALLOWANCE_SCREEN_TYPES;
const { HEADER_FLAT, ITEM_FLAT } = PRODUCT_SOURCE_INFO;


const ALLOWANCE_STEPPERS = {
  ALLOWANCE_TYPE_PERFORMANCE: "Allowance Type & Performance",
  STORE_SELECTION: "Store Selection",
  ALLOWANCE_TO_BE_CREATED: "Allowance to be Created",
  ALLOWANCE_DATES: "Allowance Dates",
  DSD_ALLOWANCE_DATES: "DSD Allowance Dates",
  ALLOWANCE_AMOUNT: "Allowance Amounts",
  DSD_CASE_ALLOWANCE_DATES: "DSD Case Allowance Dates",
  DSD_CASE_ALLOWANCE_AMOUNT: "DSD Case Allowance Amount",
  DSD_SCAN_ALLOWANCE_AMOUNT: "DSD Scan Allowance Amount",
  DSD_SHIP_TO_STORE_ALLOWANCE_AMOUNT: "DSD Ship To Store Allowance Amount",
  DEFAULT_BILLING_INFO: "Default Billing Information",
  DSD_DEFAULT_BILLING_INFO: "DSD Default Billing Information",
  WAREHOUSE_CASE_ALLOWANCE_DATES: "Warehouse Case Allowance Dates",
  WAREHOUSE_CASE_ALLOWANCE_AMOUNT: "Warehouse Case Allowance Amount",
  WAREHOUSE_SCAN_ALLOWANCE_AMOUNT: "Warehouse Scan Allowance Amount",
  WAREHOUSE_DEFAULT_BILLING_INFO: "Warehouse Default Billing Information",
};

export const ALLOWANCE_TO_BE_CREATED_CD_MAPPER = {
  CW: {
    value: "Warehouse Only",
    key: "WAREHOUSE_DIST_CENTERS",
    offerKey: "WAREHOUSE",
  },
  CD: {
    value: "Separate Allowances By DSD Distributor",
    key: "DSD_LEAD_DISTRIBUTORS",
    offerKey: "DSD",
  },
  CC: {
    value: "One Allowance: DSD Combined",
    key: "DSD_WHSE_RETAIL_DIVISION",
    offerKey: "COMBINED",
  },
  CB: {
    value: "Both",
    key: "",
  },
  TC: {
    value: "One Allowance: Warehouse, DSD, or Combined",
    key: "DSD_WHSE_RETAIL_DIVISION",
    offerKey: "WAREHOUSE",
  },
  TS: {
    value: "Separate Allowances By DSD Distributor",
    key: "DSD_LEAD_DISTRIBUTORS",
    offerKey: "DSD",
  },
  SB: {
    value: "Combined DSD",
    key: "DSD_WHSE_RETAIL_DIVISION",
    offerKey: "WAREHOUSE",
  },
  SD: {
    value: "Separate Allowances By DSD Distributor",
    key: "DSD_LEAD_DISTRIBUTORS",
    offerKey: "DSD",
  },
  LC: {
    value: "LC",
    key: "DSD_WHSE_RETAIL_DIVISION",
    offerKey: "DSD",
  },
  LW: {
    value: "LW",
    key: "WAREHOUSE_DIST_CENTERS",
    offerKey: "WAREHOUSE",
  },
  AC: {
    value: "AC",
    key: "DSD_WHSE_RETAIL_DIVISION",
    offerKey: "DSD",
  },
  AW: {
    value: "AW",
    key: "WAREHOUSE_DIST_CENTERS",
    offerKey: "WAREHOUSE",
  },
};

export const DISTRIBUTORS_IND_KEYS = ["CD", "TS", "SD"];

export const EVENT_ALLOWANCE: {
  steppers: string[];
} = {
  steppers: Object.values(ALLOWANCE_STEPPERS),
  [ALLOWANCE_STEPPERS.ALLOWANCE_TYPE_PERFORMANCE]: {
    fields: ALLOWANCE_TYPE_PERFORMANCE,
    allowanceTypeAndPerformance: [
      "CASE",
      "SCAN",
      "SHIP_TO_STORE",
      "HEADER_FLAT",
      "ITEM_FLAT",
    ],
    allowanceToBeCreated: [
      "Both",
      "One Allowance: Warehouse, DSD, or Combined",
      "One Allowance: DSD Combined",
      "Combined DSD",
      "Separate Allowances By DSD Distributor",
      "DSD Only",
      "Warehouse Only",
    ],
    isOpen: true,
    label: "Allowance Type & Performance",
    sublabel: getAllowanceTypePerformanceSubHeader,
    disable: {
      allowanceType: {
        CASE: {
          productSources: ["WAREHOUSE"],
        },
        SCAN: {
          productSources: ["WAREHOUSE", "BOTH"],
        },
        SHIP_TO_STORE: {
          productSources: ["WAREHOUSE", "BOTH"],
        },
      },
    },
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save & Continue",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Update & Continue",
    },
  },
  [ALLOWANCE_STEPPERS.STORE_SELECTION]: {
    fields: STORE_SELECTION,
    allowanceTypeAndPerformance: [
      "SCAN",
      "SHIP_TO_STORE",
      "HEADER_FLAT",
      "ITEM_FLAT",
    ],
    allowanceToBeCreated: [
      "One Allowance: Warehouse, DSD, or Combined",
      "Combined DSD",
      "Separate Allowances By DSD Distributor",
    ],
    isOpen: true,
    label: "Store Selection",
    notFoundText: "No Store Groups Found",
    sublabel: getAllowanceStoreSelectionSubHeader,
    disable: {
      allowanceType: {
        CASE: {
          productSources: ["DSD", "WAREHOUSE"],
        },
        SCAN: {
          productSources: ["WAREHOUSE", "BOTH"],
        },
        SHIP_TO_STORE: {
          productSources: ["WAREHOUSE", "BOTH"],
        },
      },
      createInd: [
        ITEM_FLAT.WAREHOUSE.createIndex,
        HEADER_FLAT.WAREHOUSE.createIndex,
      ],
      allowanceScreenTypes: [AO.key],
    },
    allowanceScreenTypes: [AO.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save & Continue",
    },
    edit: {
      isEdit: true,
      isView: true,
      label: "Update & Continue",
    },
  },
  [ALLOWANCE_STEPPERS.ALLOWANCE_TO_BE_CREATED]: {
    fields: ALLOWANCE_TO_BE_CREATED,
    isOpen: false,
    allowanceTypeAndPerformance: ["CASE", "SCAN", "SHIP_TO_STORE"],
    allowanceToBeCreated: [
      "Both",
      "One Allowance: Warehouse, DSD, or Combined",
      "One Allowance: DSD Combined",
      "Combined DSD",
      "Separate Allowances By DSD Distributor",
      "DSD Only",
      "Warehouse Only",
    ],
    label: "Allowance to be Created",
    sublabel: getAllowanceToBeCreatedSubHeader,
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save & Continue",
    },
    edit: {
      isEdit: true,
      isView: true,
      label: "Update & Continue",
    },
  },
  [ALLOWANCE_STEPPERS.ALLOWANCE_DATES]: {
    fields: ALLOWANCE_DATES,
    isOpen: false,
    offerTypeKey: "WAREHOUSE",
    allowanceTypeAndPerformance: [
      "SCAN",
      "CASE",
      "SHIP_TO_STORE",
      "HEADER_FLAT",
      "ITEM_FLAT",
    ],
    allowanceToBeCreated: [
      "Combined DSD",
      "One Allowance: Warehouse, DSD, or Combined",
      "One Allowance: DSD Combined",
    ],
    label: "Allowance Dates",
    sublabel: getAllowanceDatesSubHeader,
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save & Continue",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Update & Continue",
    },
  },
  [ALLOWANCE_STEPPERS.DSD_ALLOWANCE_DATES]: {
    fields: ALLOWANCE_DATES,
    isOpen: false,
    offerTypeKey: "DSD",
    allowanceTypeAndPerformance: ["SCAN", "SHIP_TO_STORE"],
    allowanceToBeCreated: [
      "Separate Allowances By DSD Distributor",
      "DSD Only",
    ],
    label: "Allowance Dates",
    sublabel: getAllowanceDatesSubHeader,
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save & Continue",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Update & Continue",
    },
  },
  [ALLOWANCE_STEPPERS.DSD_CASE_ALLOWANCE_DATES]: {
    fields: ALLOWANCE_DATES,
    isOpen: false,
    allowanceTypeAndPerformance: ["CASE"],
    allowanceToBeCreated: [
      "Both",
      "DSD Only",
      "Separate Allowances By DSD Distributor",
    ],
    label: "DSD Case Allowance Dates",
    sublabel: getDSDAllowanceDatesSubHeader,
    offerTypeKey: "DSD",
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save & Continue",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Update & Continue",
    },
  },
  [ALLOWANCE_STEPPERS.WAREHOUSE_CASE_ALLOWANCE_DATES]: {
    fields: ALLOWANCE_DATES,
    isOpen: false,
    allowanceTypeAndPerformance: ["CASE"],
    allowanceToBeCreated: ["Both", "Warehouse Only"],
    label: "Warehouse Case Allowance Dates",
    offerTypeKey: "WAREHOUSE",
    sublabel: getAllowanceCaseDatesSubHeader,
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save & Continue",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Update & Continue",
    },
  },
  [ALLOWANCE_STEPPERS.ALLOWANCE_AMOUNT]: {
    fields: ALLOWANCE_AMOUNT,
    isOpen: false,
    allowanceTypeAndPerformance: [
      "SCAN",
      "CASE",
      "SHIP_TO_STORE",
      "HEADER_FLAT",
      "ITEM_FLAT",
    ],
    allowanceToBeCreated: [
      "One Allowance: Warehouse, DSD, or Combined",
      "One Allowance: DSD Combined",
      "Combined DSD",
    ],
    label: "Allowance Amounts",
    offerTypeKey: "WAREHOUSE",
    sublabel: getAllowanceAmountsSubHeader,
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save & Continue",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Update & Continue",
    },
    disable: {
      createInd: [HEADER_FLAT.DSD.createIndex],
      allowanceScreenType: [DP.key],
    },
  },
  [ALLOWANCE_STEPPERS.DSD_CASE_ALLOWANCE_AMOUNT]: {
    fields: ALLOWANCE_AMOUNT,
    isOpen: false,
    allowanceTypeAndPerformance: ["CASE"],
    allowanceToBeCreated: [
      "Both",
      "DSD Only",
      "Separate Allowances By DSD Distributor",
    ],
    label: "DSD Case Allowance Amount",
    offerTypeKey: "DSD",
    sublabel: getAllowanceAmountsSubHeader,
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save & Continue",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Update & Continue",
    },
  },
  [ALLOWANCE_STEPPERS.DSD_SCAN_ALLOWANCE_AMOUNT]: {
    fields: ALLOWANCE_AMOUNT,
    isOpen: false,
    allowanceTypeAndPerformance: ["SCAN"],
    allowanceToBeCreated: ["Both", "Separate Allowances By DSD Distributor"],
    label: "DSD Scan Allowance Amounts",
    offerTypeKey: "DSD",
    sublabel: getAllowanceAmountsSubHeader,
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save & Continue",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Update & Continue",
    },
  },
  [ALLOWANCE_STEPPERS.DSD_SHIP_TO_STORE_ALLOWANCE_AMOUNT]: {
    fields: ALLOWANCE_AMOUNT,
    isOpen: false,
    allowanceTypeAndPerformance: ["SHIP_TO_STORE"],
    allowanceToBeCreated: ["Both", "Separate Allowances By DSD Distributor"],
    label: "DSD Ship To Store Allowance Amounts",
    offerTypeKey: "DSD",
    sublabel: getAllowanceAmountsSubHeader,
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save & Continue",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Update & Continue",
    },
  },
  [ALLOWANCE_STEPPERS.WAREHOUSE_CASE_ALLOWANCE_AMOUNT]: {
    fields: ALLOWANCE_AMOUNT,
    isOpen: false,
    allowanceTypeAndPerformance: ["CASE"],
    allowanceToBeCreated: ["Both", "Warehouse Only"],
    label: "Warehouse Case Allowance Amount",
    offerTypeKey: "WAREHOUSE",
    sublabel: getAllowanceAmountsSubHeader,
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save & Continue",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Update & Continue",
    },
  },
  [ALLOWANCE_STEPPERS.WAREHOUSE_SCAN_ALLOWANCE_AMOUNT]: {
    fields: ALLOWANCE_AMOUNT,
    isOpen: false,
    allowanceTypeAndPerformance: [],
    allowanceToBeCreated: ["Both"],
    label: "Warehouse Scan Allowance Amounts",
    offerTypeKey: "WAREHOUSE",
    sublabel: getAllowanceAmountsSubHeader,
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save & Continue",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Update & Continue",
    },
  },
  [ALLOWANCE_STEPPERS.DEFAULT_BILLING_INFO]: {
    fields: BILLING_INFORMATION,
    isOpen: false,
    allowanceTypeAndPerformance: [
      "SCAN",
      "CASE",
      "SHIP_TO_STORE",
      "HEADER_FLAT",
      "ITEM_FLAT",
    ],
    allowanceToBeCreated: [
      "One Allowance: Warehouse, DSD, or Combined",
      "One Allowance: DSD Combined",
      "Combined DSD",
    ],
    label: "Default Billing Information",
    offerTypeKey: "WAREHOUSE",
    sublabel: getAllowanceBillingInfoSubHeader,
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save & Continue",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Update & Continue",
    },
  },
  [ALLOWANCE_STEPPERS.DSD_DEFAULT_BILLING_INFO]: {
    fields: BILLING_INFORMATION,
    isOpen: false,
    allowanceTypeAndPerformance: ["CASE", "SCAN", "SHIP_TO_STORE"],
    allowanceToBeCreated: [
      "Both",
      "Separate Allowances By DSD Distributor",
      "DSD Only",
    ],
    label: "DSD Default Billing Information",
    offerTypeKey: "DSD",
    sublabel: getAllowanceBillingInfoSubHeader,
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save & Continue",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Update & Continue",
    },
  },
  [ALLOWANCE_STEPPERS.WAREHOUSE_DEFAULT_BILLING_INFO]: {
    fields: BILLING_INFORMATION,
    isOpen: false,
    allowanceTypeAndPerformance: ["CASE"],
    allowanceToBeCreated: ["Both", "Warehouse Only"],
    label: "Warehouse Default Billing Information",
    offerTypeKey: "WAREHOUSE",
    sublabel: getAllowanceBillingInfoSubHeader,
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save & Continue",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Update & Continue",
    },
  },
};

export const ALLOWANCE_PREVIEW_DEFAULT_STEPS = ["Allowance Id Information"];

export const PREVIEW_WORKFLOW_RULES = {
  ALLOWANCE: {
    [ALLOWANCE_STEPPERS.ALLOWANCE_TYPE_PERFORMANCE]: {
      previewCard: "allowance-type-performance",
      redesignPreviewTitle: ALLOWANCE_STEPPERS.ALLOWANCE_TYPE_PERFORMANCE,
      isDividerRequired: false,
      isShowDirectField: true,
      fields: ALLOWANCE_HEADER_FLAT_AMOUNT,
      stepperHeaderFields: ALLOW_HEADERFLAT_HEADER_FIELD,
      fieldsHistoryKeys: ALLOW_HEADERFLAT_HEADER_HISTORY_KEYS,
    },
    [ALLOWANCE_STEPPERS.STORE_SELECTION]: {
      fields: [],
      redesignPreviewTitle: ALLOWANCE_STEPPERS.STORE_SELECTION,
      previewCard: "allowance-store-selection",
      isDividerRequired: false,
      isShowDirectField: true,
    },
    [ALLOWANCE_STEPPERS.ALLOWANCE_TO_BE_CREATED]: {
      previewCard: "allowance-to-be-created",
      redesignPreviewTitle: ALLOWANCE_STEPPERS.ALLOWANCE_TO_BE_CREATED,
      isDividerRequired: false,
      isShowDirectField: true,
      fields: [],
    },
    [ALLOWANCE_STEPPERS.ALLOWANCE_DATES]: {
      previewCard: "allowance-dates",
      isDividerRequired: true,
      isShowDirectField: false,
      redesignPreviewTitle: ALLOWANCE_STEPPERS.ALLOWANCE_DATES,
      sliceCount: 3,
      fields: ALLOWANCE_DATES_PREVIEW_FIELDS,
      stepperHeaderFields: ALLOW_DATE_HEADER_FIELDS,
      fieldsHistoryKeys: ALLOWANCE_DATES_HISTORY_KEYS,
    },
    [ALLOWANCE_STEPPERS.DSD_ALLOWANCE_DATES]: {
      previewCard: "allowance-dates",
      isDividerRequired: true,
      isShowDirectField: false,
      sliceCount: 3,
      redesignPreviewTitle: ALLOWANCE_STEPPERS.DSD_ALLOWANCE_DATES,
      fields: ALLOWANCE_DATES_PREVIEW_FIELDS,
      stepperHeaderFields: ALLOW_DATE_HEADER_FIELDS,
      fieldsHistoryKeys: ALLOWANCE_DATES_HISTORY_KEYS,
    },
    [ALLOWANCE_STEPPERS.DSD_CASE_ALLOWANCE_DATES]: {
      previewCard: "allowance-dates",
      isDividerRequired: true,
      isShowDirectField: false,
      sliceCount: 4,
      redesignPreviewTitle: ALLOWANCE_STEPPERS.DSD_ALLOWANCE_DATES,
      fields: ALLOWANCE_DATES_PREVIEW_FIELDS,
      stepperHeaderFields: ALLOW_DATE_HEADER_FIELDS,
      fieldsHistoryKeys: ALLOWANCE_DATES_HISTORY_KEYS,
    },
    [ALLOWANCE_STEPPERS.WAREHOUSE_CASE_ALLOWANCE_DATES]: {
      previewCard: "allowance-dates",
      isDividerRequired: true,
      isShowDirectField: false,
      sliceCount: 5,
      redesignPreviewTitle: "Warehouse Allowance Dates",
      fields: ALLOWANCE_DATES_PREVIEW_FIELDS,
      stepperHeaderFields: ALLOW_DATE_HEADER_FIELDS,
      fieldsHistoryKeys: WHSE_ALLOW_DATES_HISTORY_KEYS,
    },
    [ALLOWANCE_STEPPERS.ALLOWANCE_AMOUNT]: {
      previewCard: "allowance-amounts",
      isDividerRequired: true,
      isShowDirectField: false,
      redesignPreviewTitle: ALLOWANCE_STEPPERS.ALLOWANCE_AMOUNT,
      allowTypeFields: {
        uom: {
          hideForAllowType: [
            efConstants.ALLOWANCE_TYPES.ITEMFLAT.label,
            efConstants.ALLOWANCE_TYPES.HEADERFLAT.label,
          ],
          isShow: true,
        },
      },
      fields: ALLOWANCE_AMOUNTS_PREVIEW_FIELDS,
      stepperHeaderFields: ALLOW_AMOUNT_HEADER_FIELDS,
      fieldsHistoryKeys: ALLOWANCE_AMNTS_HISTORY_KEYS,
    },
    [ALLOWANCE_STEPPERS.DSD_CASE_ALLOWANCE_AMOUNT]: {
      previewCard: "allowance-amounts",
      isDividerRequired: true,
      isShowDirectField: false,
      redesignPreviewTitle: "DSD Allowance Amount",
      allowTypeFields: {
        uom: {
          hideForAllowType: [efConstants.ALLOWANCE_TYPES.ITEMFLAT.label],
          isShow: true,
        },
      },
      fields: ALLOWANCE_AMOUNTS_PREVIEW_FIELDS,
      stepperHeaderFields: ALLOW_AMOUNT_HEADER_FIELDS,
      fieldsHistoryKeys: ALLOWANCE_AMNTS_HISTORY_KEYS,
    },
    [ALLOWANCE_STEPPERS.DSD_SCAN_ALLOWANCE_AMOUNT]: {
      previewCard: "allowance-amounts",
      isDividerRequired: true,
      isShowDirectField: false,
      redesignPreviewTitle: "DSD Allowance Amount",
      allowTypeFields: {
        uom: {
          hideForAllowType: [efConstants.ALLOWANCE_TYPES.ITEMFLAT.label],
          isShow: true,
        },
      },
      fields: ALLOWANCE_AMOUNTS_PREVIEW_FIELDS,
      stepperHeaderFields: ALLOW_AMOUNT_HEADER_FIELDS,
      fieldsHistoryKeys: ALLOWANCE_AMNTS_HISTORY_KEYS,
    },
    [ALLOWANCE_STEPPERS.DSD_SHIP_TO_STORE_ALLOWANCE_AMOUNT]: {
      previewCard: "allowance-amounts",
      isDividerRequired: true,
      isShowDirectField: false,
      redesignPreviewTitle: ALLOWANCE_STEPPERS.DSD_SHIP_TO_STORE_ALLOWANCE_AMOUNT,
      allowTypeFields: {
        uom: {
          hideForAllowType: [efConstants.ALLOWANCE_TYPES.ITEMFLAT.label],
          isShow: true,
        },
      },
      fields: ALLOWANCE_AMOUNTS_PREVIEW_FIELDS,
      stepperHeaderFields: ALLOW_AMOUNT_HEADER_FIELDS,
      fieldsHistoryKeys: ALLOWANCE_AMNTS_HISTORY_KEYS,
    },
    [ALLOWANCE_STEPPERS.WAREHOUSE_CASE_ALLOWANCE_AMOUNT]: {
      previewCard: "allowance-amounts",
      isDividerRequired: true,
      isShowDirectField: false,
      redesignPreviewTitle: "Warehouse Allowance Amount",
      allowTypeFields: {
        uom: {
          hideForAllowType: [efConstants.ALLOWANCE_TYPES.ITEMFLAT.label],
          isShow: true,
        },
      },
      fields: ALLOWANCE_AMOUNTS_PREVIEW_FIELDS,
      stepperHeaderFields: ALLOW_AMOUNT_HEADER_FIELDS,
      fieldsHistoryKeys: ALLOWANCE_AMNTS_HISTORY_KEYS,
    },
    [ALLOWANCE_STEPPERS.WAREHOUSE_SCAN_ALLOWANCE_AMOUNT]: {
      previewCard: "allowance-amounts",
      isDividerRequired: true,
      isShowDirectField: false,
      redesignPreviewTitle: "Warehouse Allowance Amount",
      allowTypeFields: {
        uom: {
          hideForAllowType: [efConstants.ALLOWANCE_TYPES.ITEMFLAT.label],
          isShow: true,
        },
      },
      fields: ALLOWANCE_AMOUNTS_PREVIEW_FIELDS,
      stepperHeaderFields: ALLOW_AMOUNT_HEADER_FIELDS,
      fieldsHistoryKeys: ALLOWANCE_AMNTS_HISTORY_KEYS,
    },
    [ALLOWANCE_STEPPERS.DEFAULT_BILLING_INFO]: {
      previewCard: "allowance-default-billing-info",
      isDividerRequired: true,
      isShowDirectField: false,
      redesignPreviewTitle: "Billing Details",
      sliceCount: 4,
      fields: ALLOWANCE_DEFAULT_BILLING_PREVIEW_FIELDS,
      fieldsHistoryKeys: DEFAULT_BILLING_INFO_HIST_KEYS,
    },
    [ALLOWANCE_STEPPERS.DSD_DEFAULT_BILLING_INFO]: {
      previewCard: "allowance-default-billing-info",
      isDividerRequired: true,
      isShowDirectField: false,
      redesignPreviewTitle: "DSD Billing Details",
      sliceCount: 4,
      fields: ALLOWANCE_DEFAULT_BILLING_PREVIEW_FIELDS,
      fieldsHistoryKeys: DEFAULT_BILLING_INFO_HIST_KEYS,
    },
    [ALLOWANCE_STEPPERS.WAREHOUSE_DEFAULT_BILLING_INFO]: {
      previewCard: "allowance-default-billing-info",
      isDividerRequired: true,
      isShowDirectField: false,
      redesignPreviewTitle: "Warehouse Billing Details",
      sliceCount: 4,
      fields: ALLOWANCE_DEFAULT_BILLING_PREVIEW_FIELDS,
      fieldsHistoryKeys: DEFAULT_BILLING_INFO_HIST_KEYS,
    },
    [ALLOWANCE_PREVIEW_DEFAULT_STEPS[0]]: {
      previewCard: "allowance-id",
      isDividerRequired: false,
      isShowDirectField: true,
    },
  },
  PROMOTION: {
    "Promotion Customer": {
      previewCard: "promotion",
      title: "Promotion",
      isDividerRequired: false,
      isShowDirectField: true,
      fields: [],
    },

    "Promotion Dates": {
      previewCard: "promotion-dates",
      isDividerRequired: true,
      isShowDirectField: false,
      fields: PROMOTION_DATES_PREVIEW_FIELDS,
      sliceCount: 3,
      stepperHeaderFields: PROMO_DATE_HEADER_FIELDS,
      fieldsHistoryKeys: PROMOTION_DATES_HISTORY_KEYS,
    },
    "Promotion Details": {
      previewCard: "promotion-details",
      title: "Promo Details",
      isDividerRequired: false,
      isShowDirectField: true,
      fields: PROMO_DETAILS_PREVIEW_FIELDS,
      customPrefixText: "$",
      sliceCount: 3,
      fieldsHistoryKeys: PROMO_DETAILS_HISTORY_KEYS,
    },
  },
};
