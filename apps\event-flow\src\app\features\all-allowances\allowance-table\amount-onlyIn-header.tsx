import Input from "@albertsons/uds/molecule/Input";
import { ChangeEvent, useEffect, useState } from "react";
import "./amount-onlyIn-header.scss";
import { useDispatch } from "react-redux";
import { updateItemByDistributorAction } from "../../create-event/service/slice/table-data-slice";
import { useSelectorWrap } from "@me/data-rtk";
import classNames from "classnames";
import { setHeaderOnlyAmt } from "../../create-event/service/slice/allowances-dashboard-slices";
import efConstants from "../../../shared/ef-constants/ef-constants";
import { isNationalType } from "../allowance-lead-distributors/billing-selection-utils";
import { setNationalHeaderOnlyAmt } from "../nationals/slices/national-main-entry-slices";
import { calculateErrorClass } from "../nationals/service/table-service";

/* In HF, we have amount field only at the header and not at item level. */
export default function AmountOnlyInHeader({
  propsData: { vendorDetails, warehouseIndex },
}) {
  const { selectedDivisionData = {}, isNdpType = false } =
    useSelectorWrap("national_divisions_config")?.data || {};
  const { data: allDivisionsWarningData } =
    useSelectorWrap("all_divisions_warning_data") || {};
  const isNational = isNationalType(isNdpType);
  const { divisionErrObj = {} } =
    allDivisionsWarningData || {};
  const getInputTextClass = isAnyErrorInColumn => {
    return classNames({
      amtFieldWrap: true,
      "error-input-wrap": !isNational && isAnyErrorInColumn,
      "amount-input-error": isNational && isAnyErrorInColumn,
    });
  };
  const [amtVal, setAmtVal] = useState(vendorDetails?.headerFlatAmt),
    [inputTextClass, setInputTextClass] = useState(getInputTextClass(false)),
    dispatch = useDispatch(),
    { data: emptyFieldsData } = useSelectorWrap("emptyFields_rn"),
    { data: switchValue } = useSelectorWrap("selectedSwitchValue_rn"),
    { data: excludedVendorData } = useSelectorWrap(
      "excludedVendorForAllowance_rn"
    );

  const excludedDataToCheck = isNational
    ? excludedVendorData?.divisionWiseExcludedVendors?.[
        selectedDivisionData?.divisionId
      ] ?? {}
    : excludedVendorData;

  const isExclude = excludedDataToCheck?.[warehouseIndex]?.isExclude || false;

  useEffect(() => {
    setHeaderAmtInSlice(amtVal);
  }, [amtVal]);

  useEffect(() => {
    setAmtVal(vendorDetails.headerFlatAmt);
    setHeaderAmtInSlice(vendorDetails?.headerFlatAmt);
  }, [vendorDetails]);

  useEffect(() => {
    !isNational && checkIsAnyFieldEmpty();
  }, [emptyFieldsData]);

  useEffect(() => {
    isNational && setErrorOnFieldEmpty();
  }, [
    JSON.stringify(divisionErrObj),
    selectedDivisionData?.divisionId,
    vendorDetails?.distCenter,
    inputTextClass
  ]);

  useEffect(() => {
    if (isExclude) {
      setAmtVal(0);
    }
  }, [isExclude]);

  const setErrorOnFieldEmpty = () => {
    const errorClass = calculateErrorClass({
      divisionErrObj,
      selectedDivisionData,
      distCenter: vendorDetails?.distCenter,
      inputTextClass,
      getInputTextClass
    }
    );
    // Only update if the class has changed to avoid unnecessary re-renders
    if (errorClass !== inputTextClass) {
      setInputTextClass(errorClass);
    }
  };
  const setHeaderAmtInSlice = amtValue => {
    const disptacherMethod = isNational
      ? setNationalHeaderOnlyAmt
      : setHeaderOnlyAmt;
    dispatch(
      disptacherMethod({
        warehouseIndex,
        val: amtValue,
        ...(isNational && { divisionId: selectedDivisionData?.divisionId }),
      })
    );
  };
  const checkIsAnyFieldEmpty = () => {
    let inputClass = getInputTextClass(false);
    if (
      Object.keys(emptyFieldsData)?.[0] &&
      (amtVal === undefined || amtVal === "")
    ) {
      //Error
      inputClass = getInputTextClass(true);
    }
    setInputTextClass(inputClass);
  };

  const handler_allowanceAmt = e => {
    setAmtVal(e.target.value);
  };
  const handler_blurAllowanceAmt = (e: ChangeEvent<HTMLInputElement>) => {
    dispatch(
      updateItemByDistributorAction({
        vendorIndex: warehouseIndex,
        amount: e.target.value,
        switchValue: switchValue?.selectedSwitch,
      })
    );
  };

  return (
    <>
      <div
        className={`flex items-start px-3 ${efConstants.componentClassName.AMOUNT_ONLYIN_HEADER}`}
      >
        <span className="relative">
          <span className="absolute top-[10px] left-[10px]"> $ </span>
          <Input
            data-testid="uom-text"
            name="allowanceAmt"
            type="number"
            value={amtVal}
            disabled={isExclude}
            onWheel={event => event.currentTarget.blur()}
            onChange={(e: ChangeEvent<HTMLInputElement>) =>
              handler_allowanceAmt(e)
            }
            onBlur={(e: ChangeEvent<HTMLInputElement>) =>
              handler_blurAllowanceAmt(e)
            }
            className={inputTextClass}
          />
        </span>
      </div>
    </>
  );
}
