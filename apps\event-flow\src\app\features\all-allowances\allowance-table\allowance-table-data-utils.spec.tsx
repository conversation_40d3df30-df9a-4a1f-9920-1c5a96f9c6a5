import {
  caseUnitSwitchConversion,
  checkIsDisplayOnSwitch,
  getSwitchOptions,
  getUpdatedVendorOnSwitch,
  getUpdateVendorObj,
  searchAndUpdateTableData,
  updateKeysOnSwitch,
  updateTableOnSwtich,
} from "./allowance-table-data-utils";
import * as TableDataUtils from "./allowance-table-data-utils";

jest.mock("../all-allowances-container/text-mapping", () => ({
  __esModule: true,
  textMapping: jest.fn().mockReturnValue({}),
}));

describe("allowance table data utils", () => {
  it("should test getUpdateVendorObj", () => {
    const result = getUpdateVendorObj({
      vendor: {
        allowanceAmount: "",
        newUnitCostAllow: "0",
        unitListCost: 10,
        unitCostAllow: 10,
      },
      amount: "2",
      uom: "LB",
      allowType: "CASE",
      packWhse: 1,
      vendorPackConversionFactor: 1,
    });
    expect(result).toEqual({
      allowanceAmount: "",
      newUnitCostAllow: "0",
      unitListCost: 10,
      unitCostAllow: 10,
      allowUomType: "LB",
    });
  });

  it("should test getUpdateVendorObj with 0 amount", () => {
    const result = getUpdateVendorObj({
      vendor: {
        allowanceAmount: "",
        newUnitCostAllow: "0",
        unitListCost: 10,
        unitCostAllow: 10,
        includeInd: true,
      },
      amount: "",
      uom: "LB",
      allowType: "CASE",
      packWhse: 1,
      vendorPackConversionFactor: 1,
    });
    expect(result).toEqual({
      allowanceAmount: "",
      includeInd: true,
      newCaseCostAllow: NaN,
      newUnitCostAllow: 10,
      unitListCost: 10,
      unitCostAllow: 10,
      allowUomType: "LB",
    });
  });

  //   empty list for test
  it("should test searchAndUpdateTableData", () => {
    const result = searchAndUpdateTableData({
      currentTableData: [],
      filteredAndSortedIds: ["1"],
      amount: "0.2",
    });

    expect(result).toEqual([]);
  });
  it("should test searchAndUpdateTableData with current table data", () => {
    const result = searchAndUpdateTableData({
      currentTableData: [
        {
          itemId: "1",
          vendorDetails: [],
          allowanceType: "",
        },
      ],
      filteredAndSortedIds: ["1"],
      amount: "0.2",
      itemId: "1",
    });

    expect(result).toEqual([
      { allowanceType: "", itemId: "1", vendorDetails: [] },
    ]);
  });

  it("returns null when allowType is not present in SWITCH_OPTIONS", () => {
    const result = getSwitchOptions("invalidType", "someValue");
    expect(Object.keys(result).length).toBe(0);
  });

  it("returns the correct switchOptions when isSwitching is true", () => {
    const result = getSwitchOptions("CASE", "Unit");
    expect(result.isOnlyDisplay).toEqual(true);
  });
  test("returns false when switchOptions is null", () => {
    const allowType = "foo";
    const switchValue = "bar";
    const result = checkIsDisplayOnSwitch(allowType, switchValue);
    expect(result).toBe(false);
  });

  test("returns false when switchValue is not in switchOptions", () => {
    const allowType = "SCAN";
    // added invalid switchValue
    const switchValue = "Case__";
    const result = checkIsDisplayOnSwitch(allowType, switchValue);
    expect(result).toBe(false);
  });

  test("returns isOnlyDisplay value when switchValue is in switchOptions", () => {
    const allowType = "CASE";
    const switchValue = "Unit";
    jest.spyOn(TableDataUtils, "getSwitchOptions").mockReturnValue({
      bar: {
        isOnlyDisplay: true,
      },
    });
    const result = checkIsDisplayOnSwitch(allowType, switchValue);
    expect(result).toBe(true);
  });
  it("should return an empty object if allowanceAmount is not provided", () => {
    expect(updateKeysOnSwitch({})).toEqual({});
  });

  it("should return a new object with allowUomType updated to CA_UOM_TYPE if allowUomType is EA_UOM_TYPE", () => {
    const itemObj = { allowanceAmount: 100, allowUomType: "EA" };
    const expected = { allowUomType: "CA" };
    expect(updateKeysOnSwitch(itemObj)).toEqual(expected);
  });

  it("should return the original object if allowUomType is not EA_UOM_TYPE", () => {
    const itemObj = { allowanceAmount: 100, allowUomType: "LB" };
    expect(updateKeysOnSwitch(itemObj)).toEqual({ allowUomType: "LB" });
  });

  test('switchSelected is "Case"', () => {
    const input = {
      switchValue: "Case",
      allowanceAmount: 10,
      vendorPackConversionFactor: 2,
      packWhse: 3,
      allowUomType: "EA",
    };
    const expectedOutput = {
      amount: 10,
      displayAmt: "60.00",
      displayPk: 1,
      uom: "EA",
    };
    expect(caseUnitSwitchConversion(input)).toEqual(expectedOutput);
  });

  test('switchSelected is "Unit"', () => {
    const input = {
      switchValue: "Unit",
      allowanceAmount: "20",
      vendorPackConversionFactor: 2,
      packWhse: 3,
      allowUomType: "CA",
    };
    const expectedOutput = {
      amount: "20",
      uom: "CA",
      displayPk: 1,
      displayAmt: "3.33",
    };
    expect(caseUnitSwitchConversion(input)).toEqual(expectedOutput);
  });

  test('switchSelected is not "Case" or "Unit"', () => {
    const input = {
      switchValue: "Other",
      allowanceAmount: 30,
      vendorPackConversionFactor: 2,
      packWhse: 3,
      allowUomType: "EA",
    };
    const expectedOutput = {
      amount: 30,
      uom: "EA",
      displayPk: 3,
      displayAmt: "",
    };
    expect(caseUnitSwitchConversion(input)).toEqual(expectedOutput);
  });
  xit("should not update vendor details when switch is not selected", () => {
    const currentTableData = [
      {
        itemId: "1",
        vendorPackConversionFactor: 1,
        packWhse: 6,
        allowanceType: "CASE",
        vendorDetails: [
          { id: "a", name: "Vendor A", isEnabled: false, allowanceAmount: 10 },
          { id: "b", name: "Vendor B", isEnabled: true, allowanceAmount: 20 },
        ],
      },
      {
        itemId: "2",
        vendorPackConversionFactor: 1,
        packWhse: 6,
        allowanceType: "CASE",
        vendorDetails: [
          { id: "c", name: "Vendor C", isEnabled: false, allowanceAmount: 20 },
          { id: "d", name: "Vendor D", isEnabled: true, allowanceAmount: 10 },
        ],
      },
    ];

    const filteredAndSortedIds = ["1", "2"];

    const switchSelected = "Unit";
    const result = updateTableOnSwtich({
      currentTableData,
      filteredAndSortedIds,
      switchSelected,
    });
    expect(result[0].vendorDetails[0].displayUnitAmt).toEqual("1.67");
  });
});

describe("searchAndUpdateTableData", () => {
  const currentTableData = [
    {
      itemId: "1",
      allowanceType: "A",
      packWhse: 1,
      vendorPackConversionFactor: 2,
      vendorDetails: [
        { vendorId: "1", amount: 10, uom: "kg" },
        { vendorId: "2", amount: 20, uom: "kg" },
      ],
    },
    {
      itemId: "2",
      allowanceType: "B",
      packWhse: 3,
      vendorPackConversionFactor: 4,
      vendorDetails: [
        { vendorId: "1", amount: 30, uom: "kg" },
        { vendorId: "2", amount: 40, uom: "kg" },
      ],
    },
  ];

  const filteredAndSortedIds = ["1", "2"];

  // Test cases for different scenarios
  test("updates the amount for all vendors", () => {
    const result = searchAndUpdateTableData({
      currentTableData,
      filteredAndSortedIds,
      amount: 50,
    });

    expect(result[0].vendorDetails[0].amount).toBe(10);
    expect(result[0].vendorDetails[1].amount).toBe(20);
    expect(result[1].vendorDetails[0].amount).toBe(30);
    expect(result[1].vendorDetails[1].amount).toBe(40);
  });

  test("updates the amount for specific itemId", () => {
    const result = searchAndUpdateTableData({
      currentTableData,
      filteredAndSortedIds,
      itemId: "2",
      amount: 60,
    });

    expect(result[0].vendorDetails[0].amount).toBe(10);
    expect(result[0].vendorDetails[1].amount).toBe(20);
    expect(result[1].vendorDetails[0].amount).toBe(30);
    expect(result[1].vendorDetails[1].amount).toBe(40);
  });

  test("updates the amount for specific vendorIndex", () => {
    const result = searchAndUpdateTableData({
      currentTableData,
      filteredAndSortedIds,
      vendorIndex: "1",
      amount: 70,
    });

    expect(result[0].vendorDetails[0].amount).toBe(10);
    expect(result[0].vendorDetails[1].amount).toBe(20);
    expect(result[1].vendorDetails[0].amount).toBe(30);
    expect(result[1].vendorDetails[1].amount).toBe(40);
  });

  test("updates the amount only for empty values", () => {
    const modifiedTableData = JSON.parse(JSON.stringify(currentTableData));
    modifiedTableData[0].vendorDetails[1].amount = 0;

    const result = searchAndUpdateTableData({
      currentTableData: modifiedTableData,
      filteredAndSortedIds,
      amount: 80,
      onlyUpdateEmptyAmount: true,
    });

    expect(result[0].vendorDetails[0].amount).toBe(10);
    expect(result[0].vendorDetails[1].amount).toBe(0);
    expect(result[1].vendorDetails[0].amount).toBe(30);
  });
});
