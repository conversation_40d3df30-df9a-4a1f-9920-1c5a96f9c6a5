import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { Provider } from "react-redux"; // Adjust the import path
import { FormProvider, useForm } from "react-hook-form";
import { app_store } from "@me/data-rtk";
import userEvent from "@testing-library/user-event";
import AllowanceCardContent from "./allowance-card-content";
import "@testing-library/jest-dom";
import * as selectors from "@me/data-rtk";

const mockDispatch = jest.fn();
jest.mock("react-redux", () => ({
  ...jest.requireActual("react-redux"),
  useDispatch: () => mockDispatch,
}));

const Wrapper = props => {
  const formMethods = useForm<any>();
  return (
    <Provider store={app_store}>
      <FormProvider {...formMethods}>{props.children}</FormProvider>
    </Provider>
  );
};

describe("AllowanceCardContent", () => {
  const mockTempWorkFromGetApi = {
    tempWorkAllowanceId: "6752d256b2ddfa25af83007f",
    planEventId: "6752cb5c6574417c422a84f1",
    createUserId: "PJAIN03",
    lastUpdUserId: "PJAIN03",
    createTs: 1733481046806,
    lastUpdTs: 1733481071812,
    allowanceType: "SCAN",
    allowanceTypeSpecification: {
      scanAllow: {
        allowanceType: "SCAN",
        createInd: "TC",
        allowancePerformanceId: "63a3a12743a6cee87995b834",
        vehicleDatesMap: {
          DSD_WHSE_RETAIL_DIVISION: {
            vehicleId: "66f1eded1d792055ff84c763",
            vehicleRef: {
              vehicleId: "66f1eded1d792055ff84c763",
              vehicleNm: "27 Week 07 Insert 2025",
              sourceVehicleSk: 0,
              startDate: "2025-02-12",
              endDate: "2025-02-18",
              vehicleType: {
                vehicleTypeId: "66a3f061900b0b47a182376f",
                sourceVehicleTypeSk: 0,
                vehicleTypDesc: "Weekly Insert",
              },
            },
            dateRange: {
              startDate: "2025-02-12",
              endDate: "2025-02-18",
            },
          },
        },
        allowancesMap: {
          DSD_WHSE_RETAIL_DIVISION: [
            {
              allowanceIdNbr: 1,
              vendorNbr: "",
              costAreaDesc: "",
              defaultAllowanceDates: {
                allowanceStartDate: "2025-02-12",
                allowanceEndDate: "2025-02-18",
                performStartDate: "2025-02-12",
                performEndDate: "2025-02-18",
                orderStartDate: "0001-01-01",
                orderEndDate: "0001-01-01",
                shipStartDate: "0001-01-01",
                shipEndDate: "0001-01-01",
                arrivalStartDate: "0001-01-01",
                arrivalEndDate: "0001-01-01",
                overrideInd: false,
                notPreSaved: false,
              },
              allowanceStartDate: "2025-02-12",
              allowanceEndDate: "2025-02-18",
              performStartDate: "2025-02-12",
              performEndDate: "2025-02-18",
              orderStartDate: "2025-02-12",
              orderEndDate: "2025-02-18",
              shipStartDate: "2025-02-12",
              shipEndDate: "2025-02-18",
              arrivalStartDate: "2025-02-12",
              arrivalEndDate: "2025-02-18",
              vehicleId: "66f1eded1d792055ff84c763",
              createInd: "TC",
              locationId: "639033d538196056762e6e28",
              locationName: "27 - Seattle",
              distCenter: "DDSE",
              locationTypeCd: "D",
              allowanceBillingInfo: {
                vendorIds: [
                  {
                    vendorNbr: "006446",
                    vendorSubAccount: "013",
                    costArea: "1",
                    fullVendorNbr: "006446-013-1",
                  },
                ],
                absMerchVendor: "006446-013",
                absVendorName: "KEHE DISTRIBUTORS {JBG WHLSALE}",
                absVendorPaymentType: "I",
                acPayableVendorNbr: "110056",
                acReceivableVendorNbr: "      ",
                vendorComment: "",
                vendorOfferTrackingNbr: "",
                vendorBillingList: [],
                vendorItemCount: 5,
                vendorItemCountsSet: [
                  {
                    vendorDsdWhseId: {
                      vendorNbr: "006446",
                      vendorSubAccount: "013",
                      costArea: "1",
                      vendorRank: "B.DSD",
                      fullVendorNbr: "006446-013-1",
                      valid: true,
                    },
                    itemIdSet: [
                      "2020113",
                      "2021581",
                      "2022453",
                      "2021601",
                      "2021027",
                    ],
                    vendorDsdWhseItemCount: 5,
                  },
                ],
                source: "SIMS_VENDOR",
                matched: "SIMS_ITEM_VENDOR",
              },
              allowanceBillingInfos: [
                {
                  vendorIds: [
                    {
                      vendorNbr: "006446",
                      vendorSubAccount: "013",
                      costArea: "1",
                      fullVendorNbr: "006446-013-1",
                    },
                  ],
                  absMerchVendor: "006446-013",
                  absVendorName: "KEHE DISTRIBUTORS {JBG WHLSALE}",
                  absVendorPaymentType: "I",
                  acPayableVendorNbr: "110056",
                  acReceivableVendorNbr: "      ",
                  vendorComment: "",
                  vendorOfferTrackingNbr: "",
                  vendorBillingList: [],
                  vendorItemCount: 5,
                  vendorItemCountsSet: [
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "006446",
                        vendorSubAccount: "013",
                        costArea: "1",
                        vendorRank: "B.DSD",
                        fullVendorNbr: "006446-013-1",
                        valid: true,
                      },
                      itemIdSet: [
                        "2020113",
                        "2021581",
                        "2022453",
                        "2021601",
                        "2021027",
                      ],
                      vendorDsdWhseItemCount: 5,
                    },
                  ],
                  source: "SIMS_VENDOR",
                  matched: "SIMS_ITEM_VENDOR",
                },
                {
                  vendorIds: [
                    {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "4",
                      fullVendorNbr: "006446-001-4",
                    },
                    {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "2",
                      fullVendorNbr: "006446-001-2",
                    },
                    {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "1",
                      fullVendorNbr: "006446-001-1",
                    },
                  ],
                  absMerchVendor: "006446-001",
                  absVendorName: "KEHE DISTRIBUTORS",
                  absVendorPaymentType: "D",
                  acPayableVendorNbr: "110056",
                  acReceivableVendorNbr: "082099",
                  billingContactName: "<EMAIL>     ",
                  billingContactEmail: "<EMAIL>",
                  vendorBillingList: [
                    {
                      billingContactName: "<EMAIL>     ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>        ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "BRANDON SWEET            ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>        ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>     ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>        ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "BRANDON SWEET            ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>        ",
                      billingContactEmail: "<EMAIL>",
                    },
                  ],
                  vendorItemCount: 4,
                  vendorItemCountsSet: [
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "006446",
                        vendorSubAccount: "001",
                        costArea: "4",
                        vendorRank: "B.DSD",
                        fullVendorNbr: "006446-001-4",
                        valid: true,
                      },
                      itemIdSet: ["2020113", "2022453", "2021601", "2021027"],
                      vendorDsdWhseItemCount: 4,
                    },
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "006446",
                        vendorSubAccount: "001",
                        costArea: "2",
                        vendorRank: "B.DSD",
                        fullVendorNbr: "006446-001-2",
                        valid: true,
                      },
                      itemIdSet: ["2020113", "2022453", "2021601", "2021027"],
                      vendorDsdWhseItemCount: 4,
                    },
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "006446",
                        vendorSubAccount: "001",
                        costArea: "1",
                        vendorRank: "B.DSD",
                        fullVendorNbr: "006446-001-1",
                        valid: true,
                      },
                      itemIdSet: ["2020113", "2022453", "2021601", "2021027"],
                      vendorDsdWhseItemCount: 4,
                    },
                  ],
                  source: "SIMS_VENDOR",
                },
                {
                  vendorIds: [
                    {
                      vendorNbr: "000895",
                      vendorSubAccount: "028",
                      costArea: "2",
                      fullVendorNbr: "000895-028-2",
                    },
                    {
                      vendorNbr: "000895",
                      vendorSubAccount: "028",
                      costArea: "1",
                      fullVendorNbr: "000895-028-1",
                    },
                  ],
                  absMerchVendor: "000895-028",
                  absVendorName: "MONDELEZ GLOBAL LLC",
                  absVendorPaymentType: "D",
                  acPayableVendorNbr: "045375",
                  acReceivableVendorNbr: "158846",
                  billingContactName: "<EMAIL>       ",
                  billingContactEmail: "<EMAIL>",
                  vendorBillingList: [
                    {
                      billingContactName: "<EMAIL>       ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>       ",
                      billingContactEmail: "<EMAIL>",
                    },
                  ],
                  vendorItemCount: 1,
                  vendorItemCountsSet: [
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "000895",
                        vendorSubAccount: "028",
                        costArea: "1",
                        vendorRank: "B.DSD",
                        fullVendorNbr: "000895-028-1",
                        valid: true,
                      },
                      itemIdSet: ["2020337"],
                      vendorDsdWhseItemCount: 1,
                    },
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "000895",
                        vendorSubAccount: "028",
                        costArea: "2",
                        vendorRank: "B.DSD",
                        fullVendorNbr: "000895-028-2",
                        valid: true,
                      },
                      itemIdSet: ["2020337"],
                      vendorDsdWhseItemCount: 1,
                    },
                  ],
                  source: "SIMS_VENDOR",
                },
              ],
              allowanceDateOffsets: {
                allowanceTypes: ["SCAN", "HEADER_FLAT", "ITEM_FLAT"],
                startDateOffset: 0,
                endDateOffset: 0,
                defaultOrderLeadTimeDays: 0,
                defaultShipTransitDays: 0,
                resolvedLeadTimeDays: 0,
                resolvedShipTransitDays: 0,
              },
              leadDistributorInfos: [],
              createAllowInd: true,
              allowanceItems: [
                {
                  itemId: "2020113",
                  itemDescription: "ANNIES BUNNY GRAHAMS HONEY              ",
                  primaryUpc: "001356200015",
                  consumerUpc: "001356200015",
                  caseUpc: "0001356200015",
                  itemUpcs: ["0001356200015", "001356200015"],
                  consumerUpcs: [
                    {
                      upc: "001356200015",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356200015",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356200015",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "2024-11-14",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 3.79,
                    costAllow: 3.79,
                    initialAllowAmt: 0,
                    newCostAllow: 3.79,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 45.48,
                    costAllow: 45.48,
                    initialAllowAmt: 0,
                    newCostAllow: 45.48,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 45.48,
                    costAllow: 45.48,
                    initialAllowAmt: 0,
                    newCostAllow: 45.48,
                  },
                  allowanceAmount: 1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0,
                      allowSum: 0,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0,
                      allowSum: 0,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0,
                      allowSum: 0,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2021027",
                  itemDescription: "ANNIES CRACKERS CHEDDAR BUNNIES WHITE   ",
                  primaryUpc: "001356230228",
                  consumerUpc: "001356230228",
                  caseUpc: "0001356230228",
                  itemUpcs: ["0001356230228", "001356230228"],
                  consumerUpcs: [
                    {
                      upc: "001356230228",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356230228",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356230228",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "2024-08-29",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 3.78,
                    costAllow: 3.78,
                    initialAllowAmt: 0,
                    newCostAllow: 3.78,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 45.36,
                    costAllow: 45.36,
                    initialAllowAmt: 0,
                    newCostAllow: 45.36,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 45.36,
                    costAllow: 45.36,
                    initialAllowAmt: 0,
                    newCostAllow: 45.36,
                  },
                  allowanceAmount: 1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0,
                      allowSum: 0,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0,
                      allowSum: 0,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0,
                      allowSum: 0,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2021581",
                  itemDescription: "ANNIES HOMEGROWN SNACK MIX CHEDDAR ORG  ",
                  primaryUpc: "001356230057",
                  consumerUpc: "001356230057",
                  caseUpc: "0001356230057",
                  itemUpcs: ["0001356230057", "001356230057"],
                  consumerUpcs: [
                    {
                      upc: "001356230057",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "9 OZ   ",
                    },
                    {
                      upc: "001356230057",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "9 OZ   ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "9.0 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 5.01,
                    costAllow: 5.01,
                    initialAllowAmt: 0,
                    newCostAllow: 5.01,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 60.12,
                    costAllow: 60.12,
                    initialAllowAmt: 0,
                    newCostAllow: 60.12,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 60.12,
                    costAllow: 60.12,
                    initialAllowAmt: 0,
                    newCostAllow: 60.12,
                  },
                  allowanceAmount: 1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0,
                      allowSum: 0,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0,
                      allowSum: 0,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0,
                      allowSum: 0,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2021601",
                  itemDescription: "ANNIES HOM SNACK MIX BUNNY O            ",
                  primaryUpc: "001356230055",
                  consumerUpc: "001356230055",
                  caseUpc: "0000000000000",
                  itemUpcs: ["0000000000000", "001356230055"],
                  consumerUpcs: [
                    {
                      upc: "001356230055",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "9 OZ   ",
                    },
                    {
                      upc: "001356230055",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "9 OZ   ",
                    },
                    {
                      upc: "001356230055",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "9 OZ   ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "9.0 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 4.65,
                    costAllow: 4.65,
                    initialAllowAmt: 0,
                    newCostAllow: 4.65,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 55.8,
                    costAllow: 55.8,
                    initialAllowAmt: 0,
                    newCostAllow: 55.8,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 55.8,
                    costAllow: 55.8,
                    initialAllowAmt: 0,
                    newCostAllow: 55.8,
                  },
                  allowanceAmount: 1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0,
                      allowSum: 0,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0,
                      allowSum: 0,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0,
                      allowSum: 0,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2022453",
                  itemDescription: "SCHAR CRACKERS TABLE GLTN FREE          ",
                  primaryUpc: "081075701006",
                  consumerUpc: "081075701006",
                  caseUpc: "0081075701006",
                  itemUpcs: ["0081075701006", "081075701006"],
                  consumerUpcs: [
                    {
                      upc: "081075701006",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "005",
                      sizeDesc: "7.4 OZ ",
                    },
                    {
                      upc: "081075701006",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "005",
                      sizeDesc: "7.4 OZ ",
                    },
                    {
                      upc: "081075701006",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "005",
                      sizeDesc: "7.4 OZ ",
                    },
                  ],
                  effectiveStartDate: "2024-08-06",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 5,
                  ringType: 0,
                  size: "7.4 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 4.31,
                    costAllow: 4.31,
                    initialAllowAmt: 0,
                    newCostAllow: 4.31,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 21.55,
                    costAllow: 21.55,
                    initialAllowAmt: 0,
                    newCostAllow: 21.55,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 21.55,
                    costAllow: 21.55,
                    initialAllowAmt: 0,
                    newCostAllow: 21.55,
                  },
                  allowanceAmount: 1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0,
                      allowSum: 0,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0,
                      allowSum: 0,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0,
                      allowSum: 0,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
              ],
              headerFlatAmt: 0,
              allowanceStatus: "Draft",
              storeGroups: [],
              leadDistributorInd: false,
              includeInd: true,
              finalizedAmountsInd: true,
              dateBindMetrics: {
                group: "DATES",
                mode: "DATES_DEFAULT_BIND",
                bindValues: {
                  srcDates: {
                    allow: {
                      startDate: "2025-02-12",
                      endDate: "2025-02-18",
                    },
                    order: {
                      startDate: "2025-02-12",
                      endDate: "2025-02-18",
                    },
                    ship: {
                      startDate: "2025-02-12",
                      endDate: "2025-02-18",
                    },
                    arrival: {
                      startDate: "2025-02-12",
                      endDate: "2025-02-18",
                    },
                  },
                  dstDates: {
                    allow: {
                      startDate: "2025-02-12",
                      endDate: "2025-02-18",
                    },
                    order: {
                      startDate: "0001-01-01",
                      endDate: "0001-01-01",
                    },
                    ship: {
                      startDate: "0001-01-01",
                      endDate: "0001-01-01",
                    },
                    arrival: {
                      startDate: "0001-01-01",
                      endDate: "0001-01-01",
                    },
                  },
                },
                bind: true,
              },
              excludedItems: [
                {
                  itemId: "2020337",
                  excludeInd: "DEACTIVATED_ITEM",
                },
              ],
              allowanceProcessStatus: "",
              allowDownstreamStatus: {},
            },
          ],
        },
        productSources: ["DSD"],
        allowancePerformances: {
          id: "63a3a12743a6cee87995b834",
          performance: "4U Event (52)",
          allowanceType: "Scan",
          allowanceCd: "T",
          perfCode1: "20",
          perfCode2: "52",
          performanceConfig: {
            defaultCreateInd: "TC",
            allowOnlyOverrideStoreGroupsInd: false,
            changeableOnEditInd: true,
          },
        },
        offerAllowancesGroupInfoMap: {},
      },
    },
  };
  const cardConfiguration = {
    section: "Allowance",
    key: "Allowance",
    title: "New Offer",
    offerTitle: "New Offer",
    isOpenCard: true,
    multiple: true,
    subtitle: "Not Started",
    stepper: 0,
    allowanceTye: "Case",
    allowanceToBeCreated: "Both",
    fields: {
      steppers: [
        "Allowance Type & Performance",
        "Store Selection",
        "Allowance to be Created",
        "Allowance Dates",
        "DSD Allowance Dates",
        "Allowance Amounts",
        "DSD Case Allowance Dates",
        "DSD Case Allowance Amount",
        "DSD Scan Allowance Amount",
        "DSD Ship To Store Allowance Amount",
        "Default Billing Information",
        "DSD Default Billing Information",
        "Warehouse Case Allowance Dates",
        "Warehouse Case Allowance Amount",
        "Warehouse Scan Allowance Amount",
        "Warehouse Default Billing Information",
      ],
      "Allowance Type & Performance": {
        fields: {
          allowanceType: {
            label: "Allowance Type",
            required: true,
            registerField: "allowanceType",
            registerKeyName: "performance.allowanceCd",
            default: "Scan",
            optionUrl: "allowanceType",
            type: "select",
            options: [],
            displayLabel: "name",
            apiUrl: "",
            slice: "",
            errors: {
              required: {
                text: "AllowanceType is Required",
              },
            },
          },
          performance: {
            label: "Performance",
            required: true,
            default: "Price/Ad/Display",
            registerField: "performance",
            registerKeyName: "performance",
            optionUrl: "performance",
            options: [],
            displayLabel: "performance",
            type: "select",
            apiUrl: "",
            slice: "",
            errors: {
              required: {
                text: "Performance is Required",
              },
            },
          },
          allowanceAmount: {
            label: "Allowance Amount",
            required: true,
            registerField: "overrideHeaderFlatAmt",
            registerKeyName: "overrideHeaderFlatAmt",
            type: "number",
            errors: {
              required: {
                text: "Allowance Amount is Required",
              },
              maxLength: {
                length: 10,
                text: "Max Length Reached",
              },
              formatError: {
                pattern: {},
                text: "Please enter valid Allowance Amount",
              },
              digitError: {
                text: "Allowance amount cannot be zero.",
              },
            },
          },
          allowanceCreationVehicle: {
            label: "AllowanceCreationVehicle",
            required: true,
            registerKeyName: "allowanceCreationVehicle",
            registerField: "allowanceCreationVehicle",
            options: [],
          },
          allowanceTypeAndPerformance: {
            registerKeyName: "allowanceTypeAndPerformance",
          },
        },
        allowanceTypeAndPerformance: [
          "CASE",
          "SCAN",
          "SHIP_TO_STORE",
          "HEADER_FLAT",
          "ITEM_FLAT",
        ],
        allowanceToBeCreated: [
          "Both",
          "One Allowance: Warehouse, DSD, or Combined",
          "Combined DSD",
          "Separate Allowances By DSD Distributor",
          "DSD Only",
          "Warehouse Only",
        ],
        isOpen: true,
        label: "Allowance Type & Performance",
        disable: {
          allowanceType: {
            CASE: {
              productSources: ["DSD", "WAREHOUSE"],
            },
            SCAN: {
              productSources: ["WAREHOUSE", "BOTH"],
            },
            SHIP_TO_STORE: {
              productSources: ["WAREHOUSE", "BOTH"],
            },
          },
        },
        allowanceScreenTypes: ["DP", "AO"],
        create: {
          isEdit: false,
          isView: false,
          label: "Save & Continue",
        },
        edit: {
          isEdit: true,
          isView: false,
          label: "Update & Continue",
        },
      },
      "Store Selection": {
        fields: {
          planStoreGroupType: {
            label: "Store Group Type",
            required: true,
            registerField: "planStoreGroupType",
            registerKeyName: "performance.allowanceCd",
            default: "Scan",
            optionUrl: "planStoreGroupType",
            type: "select",
            options: [],
            displayLabel: "name",
            apiUrl: "",
            slice: "",
            errors: {
              required: {
                text: "Store Group Type is Required",
              },
            },
          },
          storeGroups: {
            label: "Store Groups",
            required: true,
            default: "Price/Ad/Display",
            registerField: "storeGroups",
            registerKeyName: "performance",
            optionUrl: "performance",
            options: [],
            displayLabel: "name",
            type: "select",
            apiUrl: "",
            slice: "",
            errors: {
              required: {
                text: "Store Group is Required",
              },
            },
          },
          allowanceStoreSelection: {
            registerKeyName: "allowanceStoreSelection",
          },
        },
        allowanceTypeAndPerformance: [
          "SCAN",
          "SHIP_TO_STORE",
          "HEADER_FLAT",
          "ITEM_FLAT",
        ],
        allowanceToBeCreated: [
          "Both",
          "One Allowance: Warehouse, DSD, or Combined",
          "Combined DSD",
          "Separate Allowances By DSD Distributor",
        ],
        isOpen: true,
        label: "Store Selection",
        notFoundText: "No Store Groups Found",
        disable: {
          allowanceType: {
            CASE: {
              productSources: ["DSD", "WAREHOUSE"],
            },
            SCAN: {
              productSources: ["WAREHOUSE", "BOTH"],
            },
            SHIP_TO_STORE: {
              productSources: ["WAREHOUSE", "BOTH"],
            },
          },
          createInd: ["AW", "LW"],
          allowanceScreenTypes: ["AO"],
        },
        allowanceScreenTypes: ["AO"],
        create: {
          isEdit: false,
          isView: false,
          label: "Save & Continue",
        },
        edit: {
          isEdit: true,
          isView: true,
          label: "Update & Continue",
        },
      },
      "Allowance to be Created": {
        fields: {
          allowanceToBeCreated: {
            name: "",
            key: "",
            required: true,
            registerField: "allowanceToBeCreated",
            registerKeyName: "allowanceToBeCreated",
            createIndex: "",
            disable: true,
            text: "Your selected Products and Store Group(s) are serviced by both DSD and Warehouse Items, if you wish to not create both Allowances change the default from Both. Be advised that this will result in some Store/Product promotions not being funded.",
            options: [],
            errors: {
              required: {
                backgroundColor: "",
                text: "Allowance To Be Created is required",
              },
            },
          },
          allowanceSpecificFields: {
            CASE: {
              WAREHOUSE: {
                name: "Warehouse Only",
                key: "WAREHOUSE_DIST_CENTERS",
                registerField: "allowanceToBeCreated",
                createIndex: "CW",
                disable: true,
                text: "Your selected Products and Store Group(s) are serviced by both DSD and Warehouse Items, if you wish to not create both Allowances change the default from Both. Be advised that this will result in some Store/Product promotions not being funded.",
                options: [
                  {
                    name: "Warehouse Only",
                    key: "WAREHOUSE_DIST_CENTERS",
                    routingKey: "WAREHOUSE_DIST_CENTERS",
                    default: true,
                    allowanceMap: {
                      WAREHOUSE_DIST_CENTERS: [],
                    },
                    createIndex: "CW",
                  },
                ],
              },
              DSD: {
                name: "DSD Only",
                key: "DSD_LEAD_DISTRIBUTORS",
                registerField: "allowanceToBeCreated",
                disable: true,
                createIndex: "CD",
                text: "Your selected Products and Store Group(s) are serviced by both DSD and Warehouse Items, if you wish to not create both Allowances change the default from Both. Be advised that this will result in some Store/Product promotions not being funded.",
                options: [
                  {
                    name: "DSD Only",
                    key: "DSD_LEAD_DISTRIBUTORS",
                    routingKey: "DSD_LEAD_DISTRIBUTORS",
                    allowanceMap: {
                      DSD_LEAD_DISTRIBUTORS: [],
                    },
                    default: true,
                    createIndex: "CD",
                  },
                ],
              },
              BOTH: {
                name: "Both",
                key: "BOTH",
                registerField: "allowanceToBeCreated",
                disable: false,
                text: "Your selected Products and Store Group(s) are serviced by both DSD and Warehouse Items, if you wish to not create both Allowances change the default from Both. Be advised that this will result in some Store/Product promotions not being funded.",
                options: [
                  {
                    name: "Both",
                    key: "BOTH",
                    routingKey: "WAREHOUSE_DIST_CENTERS",
                    default: true,
                    allowanceMap: {
                      WAREHOUSE_DIST_CENTERS: [],
                      DSD_LEAD_DISTRIBUTORS: [],
                    },
                    createIndex: "CB",
                  },
                  {
                    name: "Warehouse Only",
                    key: "WAREHOUSE_DIST_CENTERS",
                    routingKey: "WAREHOUSE_DIST_CENTERS",
                    default: false,
                    allowanceMap: {
                      WAREHOUSE_DIST_CENTERS: [],
                    },
                    createIndex: "CW",
                  },
                  {
                    name: "DSD Only",
                    key: "DSD_LEAD_DISTRIBUTORS",
                    routingKey: "DSD_LEAD_DISTRIBUTORS",
                    allowanceMap: {
                      DSD_LEAD_DISTRIBUTORS: [],
                    },
                    default: false,
                    createIndex: "CD",
                  },
                ],
              },
            },
            SCAN: {
              WAREHOUSE: {
                name: "One Allowance: Warehouse, DSD, or Combined",
                key: "DSD_WHSE_RETAIL_DIVISION",
                registerField: "allowanceToBeCreated",
                createIndex: "TC",
                disable: true,
                text: "Select DSD Separate to create separate allowances that can be Billed for each Distributor or choose Combined to create only one Allowance.",
                options: [
                  {
                    name: "One Allowance: Warehouse, DSD, or Combined",
                    key: "DSD_WHSE_RETAIL_DIVISION",
                    routingKey: "DSD_WHSE_RETAIL_DIVISION",
                    allowanceMap: {
                      DSD_WHSE_RETAIL_DIVISION: [],
                    },
                    default: false,
                    createIndex: "TC",
                  },
                  {
                    name: "Separate Allowances By DSD Distributor",
                    key: "DSD_LEAD_DISTRIBUTORS",
                    routingKey: "DSD_LEAD_DISTRIBUTORS",
                    allowanceMap: {
                      DSD_LEAD_DISTRIBUTORS: [],
                    },
                    default: false,
                    createIndex: "TS",
                  },
                ],
              },
              DSD: {
                name: "Separate Allowances By DSD Distributor",
                key: "DSD_LEAD_DISTRIBUTORS",
                registerField: "allowanceToBeCreated",
                disable: false,
                createIndex: "TS",
                text: "Select DSD Separate to create separate allowances that can be Billed for each Distributor or choose Combined to create only one Allowance.",
                options: [
                  {
                    name: "One Allowance: Warehouse, DSD, or Combined",
                    key: "DSD_WHSE_RETAIL_DIVISION",
                    routingKey: "DSD_WHSE_RETAIL_DIVISION",
                    allowanceMap: {
                      DSD_WHSE_RETAIL_DIVISION: [],
                    },
                    default: true,
                    createIndex: "TC",
                  },
                  {
                    name: "Separate Allowances By DSD Distributor",
                    key: "DSD_LEAD_DISTRIBUTORS",
                    routingKey: "DSD_LEAD_DISTRIBUTORS",
                    allowanceMap: {
                      DSD_LEAD_DISTRIBUTORS: [],
                    },
                    default: false,
                    createIndex: "TS",
                  },
                ],
              },
              BOTH: {
                name: "Both",
                key: "BOTH",
                registerField: "allowanceToBeCreated",
                disable: false,
                text: "Select DSD Separate to create separate allowances that can be Billed for each Distributor or choose Combined to create only one Allowance.",
                options: [
                  {
                    name: "One Allowance: Warehouse, DSD, or Combined",
                    key: "DSD_WHSE_RETAIL_DIVISION",
                    routingKey: "DSD_WHSE_RETAIL_DIVISION",
                    default: false,
                    allowanceMap: {
                      DSD_WHSE_RETAIL_DIVISION: [],
                    },
                    createIndex: "TC",
                  },
                  {
                    name: "Separate Allowances By DSD Distributor",
                    key: "DSD_LEAD_DISTRIBUTORS",
                    routingKey: "DSD_LEAD_DISTRIBUTORS",
                    allowanceMap: {
                      DSD_WHSE_RETAIL_DIVISION: [],
                      DSD_LEAD_DISTRIBUTORS: [],
                    },
                    default: false,
                    createIndex: "TS",
                  },
                ],
              },
            },
            SHIP_TO_STORE: {
              WAREHOUSE: {
                name: "Combined DSD",
                key: "DSD_WHSE_RETAIL_DIVISION",
                registerField: "allowanceToBeCreated",
                createIndex: "SB",
                disable: true,
                text: "Select Separate Allowances By DSD Distributor to create separate Allowances that can be billed by Distributor, or choose Combined to create only one Allowance.",
                options: [
                  {
                    name: "Combined DSD",
                    key: "DSD_WHSE_RETAIL_DIVISION",
                    routingKey: "DSD_WHSE_RETAIL_DIVISION",
                    allowanceMap: {
                      DSD_WHSE_RETAIL_DIVISION: [],
                    },
                    default: false,
                    createIndex: "SB",
                  },
                  {
                    name: "Separate Allowances By DSD Distributor",
                    key: "DSD_LEAD_DISTRIBUTORS",
                    routingKey: "DSD_LEAD_DISTRIBUTORS",
                    allowanceMap: {
                      DSD_LEAD_DISTRIBUTORS: [],
                    },
                    default: false,
                    createIndex: "SD",
                  },
                ],
              },
              DSD: {
                name: "Separate Allowances By DSD Distributor",
                key: "DSD_LEAD_DISTRIBUTORS",
                registerField: "allowanceToBeCreated",
                disable: false,
                createIndex: "SD",
                text: "Select Separate Allowances By DSD Distributor to create separate Allowances that can be billed by Distributor, or choose Combined to create only one Allowance.",
                options: [
                  {
                    name: "Combined DSD",
                    key: "DSD_WHSE_RETAIL_DIVISION",
                    routingKey: "DSD_WHSE_RETAIL_DIVISION",
                    allowanceMap: {
                      DSD_WHSE_RETAIL_DIVISION: [],
                    },
                    default: true,
                    createIndex: "SB",
                  },
                  {
                    name: "Separate Allowances By DSD Distributor",
                    key: "DSD_LEAD_DISTRIBUTORS",
                    routingKey: "DSD_LEAD_DISTRIBUTORS",
                    allowanceMap: {
                      DSD_LEAD_DISTRIBUTORS: [],
                    },
                    default: false,
                    createIndex: "SD",
                  },
                ],
              },
              BOTH: {
                name: "Both",
                key: "BOTH",
                registerField: "allowanceToBeCreated",
                disable: false,
                text: "Select Separate Allowances By DSD Distributor to create separate Allowances that can be billed by Distributor, or choose Combined to create only one Allowance.",
                options: [
                  {
                    name: "Combined DSD",
                    key: "DSD_WHSE_RETAIL_DIVISION",
                    routingKey: "DSD_WHSE_RETAIL_DIVISION",
                    default: false,
                    allowanceMap: {
                      DSD_WHSE_RETAIL_DIVISION: [],
                    },
                    createIndex: "SB",
                  },
                  {
                    name: "Separate Allowances By DSD Distributor",
                    key: "DSD_LEAD_DISTRIBUTORS",
                    routingKey: "DSD_LEAD_DISTRIBUTORS",
                    allowanceMap: {
                      DSD_WHSE_RETAIL_DIVISION: [],
                      DSD_LEAD_DISTRIBUTORS: [],
                    },
                    default: false,
                    createIndex: "SD",
                  },
                ],
              },
            },
            "HEADER FLAT": [],
            "ITEM FLAT": [],
          },
        },
        isOpen: false,
        allowanceTypeAndPerformance: ["CASE", "SCAN", "SHIP_TO_STORE"],
        allowanceToBeCreated: [
          "Both",
          "One Allowance: Warehouse, DSD, or Combined",
          "Combined DSD",
          "Separate Allowances By DSD Distributor",
          "DSD Only",
          "Warehouse Only",
        ],
        label: "Allowance to be Created",
        allowanceScreenTypes: ["DP", "AO"],
        create: {
          isEdit: false,
          isView: false,
          label: "Save & Continue",
        },
        edit: {
          isEdit: true,
          isView: true,
          label: "Update & Continue",
        },
      },
      "Allowance Dates": {
        fields: {
          allowanceSpecificFields: {
            CASE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            SCAN: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            SHIP_TO_STORE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            HEADER_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            ITEM_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            DEFAULT: {
              key: "DSD_WHSE_RETAIL_DIVISION",
              routeKey: "DSD_WHSE_RETAIL_DIVISION",
              allowanceDatesData: {
                registerKeyName:
                  "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
              },
            },
          },
          vehicleTypeOrCustomDate: {
            label: "Vehicle Type/Custom Date",
            required: false,
            registerField: "allowance.vehicleTypeOrCustomDate",
            gqlQueryConst: "allowance",
            default: "Weekly Insert",
            type: "select",
            apiUrl: "",
            slice: "",
            displayLabel: "name",
            options: [
              {
                id: 1,
                name: "Weekly Insert",
              },
              {
                id: 2,
                name: "Monthly Insert",
              },
              {
                id: 3,
                name: "Yearly Insert",
              },
            ],
            errors: {
              required: {
                backgroundColor: "",
                text: "Vehicle Type/Custom Date is Required",
              },
            },
            mapperKey:
              "offerallowances.allowances.vehicle.vehicleType.vehicleTypDesc",
          },
          year: {
            label: "Year",
            required: false,
            registerField: "year",
            default: "2023",
            type: "select",
            apiUrl: "",
            slice: "",
            displayLabel: "name",
            options: [
              {
                name: 2022,
                id: 1,
              },
              {
                name: 2023,
                id: 2,
              },
              {
                name: 2024,
                id: 3,
              },
              {
                name: 2025,
                id: 4,
              },
            ],
            errors: {
              required: {
                backgroundColor: "",
                text: "Year is Required",
              },
            },
          },
          startWeekOrVehicle: {
            label: "Start Week/Vehicle",
            required: false,
            registerField: "startWeekOrVehicle",
            optionUrl: "allowance.startWeek",
            default: "Week 02 Insert 2023",
            type: "select",
            apiUrl: "",
            slice: "",
            displayLabel: "vehicleNm",
            options: [
              {
                id: "",
                vehicleNm: "",
              },
            ],
            errors: {
              required: {
                backgroundColor: "",
                text: "Start Week/Vehicle is Required",
              },
            },
            mapperKey: "offerallowances.allowances.vehicle.vehicleNm",
          },
          vehicleStart: {
            label: "Vehicle Start",
            required: false,
            disable: true,
            registerField: "allowance.vehicleStart",
            type: "date",
            displayLabel: "name",
            error: {
              required: {
                backgroundColor: "",
                text: "Allowance Start Required",
              },
            },
            mapperKey: "offerallowances.allowances.vehicle.startDate",
          },
          vehicleEnd: {
            label: "Vehicle End",
            required: false,
            disable: true,
            registerField: "allowance.vehicleEnd",
            type: "date",
            displayLabel: "name",
            error: {
              required: {
                backgroundColor: "",
                text: "Allowance End Required",
              },
            },
            mapperKey: "offerallowances.allowances.vehicle.endDate",
          },
          orderStart: {
            label: "Order Start",
            required: true,
            disable: true,
            registerField: "allowance.orderStart",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Order start Required",
              },
            },
            mapperKey: "offerallowances.allowances.orderStartDate",
          },
          orderEnd: {
            label: "Order End",
            required: true,
            disable: true,
            registerField: "allowance.orderEnd",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Order End Required",
              },
            },
            mapperKey: "offerallowances.allowances.orderEndDate",
          },
          arrivalStart: {
            label: "Arrival Start",
            required: true,
            disable: true,
            registerField: "allowance.arrivalStart",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Arrival Start Required",
              },
            },
            mapperKey: "offerallowances.allowances.arrivalStartDate",
          },
          arrivalEnd: {
            label: "Arrival End",
            required: true,
            disable: true,
            registerField: "allowance.arrivalEnd",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Arrival End Required",
              },
            },
            mapperKey: "offerallowances.allowances.arrivalEndDate",
          },
          storeReceivingStart: {
            label: "Shipment Start",
            required: true,
            disable: true,
            registerField: "allowance.storeReceivingStart",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Shipment Start Required",
              },
            },
            mapperKey: "offerallowances.allowances.storeReceivingStartDate",
          },
          storeReceivingEnd: {
            label: "Shipment End",
            required: true,
            disable: true,
            registerField: "allowance.storeReceivingEnd",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Shipment End Required",
              },
            },
            mapperKey: "offerallowances.allowances.storeReceivingEndDate",
          },
          startDate: {
            label: "Start Date",
            required: true,
            disable: true,
            registerField: "allowance.startDate",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Start Date Required",
              },
            },
            mapperKey: "offerallowances.allowances.orderStartDate",
          },
          endDate: {
            label: "End Date",
            required: true,
            disable: true,
            registerField: "allowance.endDate",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "End Date Required",
              },
            },
            mapperKey: "offerallowances.allowances.orderEndDate",
          },
          errors: {
            EMPTY_ALLOWANCES:
              "Invalid combination of Promo Product Groups and Store Groups. Please update Promo Product Groups or Store Groups to continue.",
            INVALID_DATES: {
              SCAN: {
                all: "Invalid Date combination, End date must be >= Start date",
                vehicle:
                  "Allowance Dates must overlap Vehicle Dates by at least 1 day",
              },
              CASE: {
                orderStart:
                  "Invalid Date combination, Arrival Start Date must be >= Order Start date",
                orderEnd:
                  "Invalid Date combination, Arrival End Date must be >= Order End date",
                order:
                  "Invalid Date combination, Order End date must be >= Order Start date",
                arrival:
                  "Invalid Date combination, Arrival End date must be >= Arrival Start date",
                vehicle:
                  "Allowance Dates must overlap Vehicle Dates by at least 1 day",
              },
              SHIP_TO_STORE: {
                ship: "Invalid Date combination, Shipment End date must be >= Shipment Start date",
                vehicle:
                  "Allowance Dates must overlap Vehicle Dates by at least 1 day",
              },
              OTHER: "Invalid Date combination, End date must be >= Start date",
            },
          },
          allowanceCreationVehicle: {
            registerKeyName: "allowanceCreationVehicle",
          },
          initialText: {
            SCAN: {
              DSD_WHSE_RETAIL_DIVISION: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
              DSD_LEAD_DISTRIBUTORS: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
            },
            CASE: {
              DSD_LEAD_DISTRIBUTORS: {
                header:
                  "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than that of the full Event. These dates will be used for O/I and/or Billing.",
                bulletPointsText:
                  "Based on ((Albertsons Accounting Policies,)) the dates for DSD Case Allowances will be pre-calculated as:",
                bulletPoints: [
                  "DSD Shipment Start: set to be 2 days prior to Vehicle Start",
                  "DSD Shipment End: set to match the Vehicle End",
                  "If an exception to these default dates is necessary due to either other Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
                ],
              },
              WAREHOUSE_DIST_CENTERS: {
                header:
                  "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for O/I and/or Billing.",
                bulletPointsText:
                  "Based on ((Albertsons Accounting Policies,)) the dates for Warehouse Case Allowances will be pre-calculated as:",
                bulletPoints: [
                  "Order Start: Arrival Start Date - Lead Time* ",
                  "Order End: Arrival End Date - Lead Time*",
                  "Arrival Start: 10 days prior to Vehicle Start. 16 days in Northern California due to Hawaii, 21 days in Alaska.",
                  "Arrival End: Vehicle End date",
                  "For Displayer items only, an additional 14 days will be incorporated into the Order Start and Arrival Start date calculations",
                ],
                footer:
                  "* As setup in the Albertsons buying system to represent total time in days from P.O. generation through warehouse receiving. If an exception to these default dates is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
              },
            },
            SHIP_TO_STORE: {
              DSD_WHSE_RETAIL_DIVISION: {
                header:
                  "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period shorter than the full Event.",
                subHeader:
                  "Based on ((Albertsons Accounting Policies,)) the dates for Ship To Store Allowances are not tied only to the Vehicle / Performance.  These allowances are earned upon Shipment and have no less than 7 days prior to the Performance start and 3 days after the end date.  14 Days prior for Displays.",
                footer:
                  "If an exception to these defaults is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
              },
              DSD_LEAD_DISTRIBUTORS: {
                header:
                  "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period shorter than the full Event.",
                subHeader:
                  "Based on ((Albertsons Accounting Policies,)) the dates for Ship To Store Allowances are not tied only to the Vehicle / Performance.  These allowances are earned upon Shipment and have no less than 7 days prior to the Performance start and 3 days after the end date.  14 Days prior for Displays.",
                footer:
                  "If an exception to these defaults is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
              },
            },
            HEADER_FLAT: {
              DSD_WHSE_RETAIL_DIVISION: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
              WAREHOUSE_DIST_CENTERS: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
            },
            ITEM_FLAT: {
              DSD_WHSE_RETAIL_DIVISION: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
              WAREHOUSE_DIST_CENTERS: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
            },
          },
        },
        isOpen: false,
        offerTypeKey: "WAREHOUSE",
        allowanceTypeAndPerformance: [
          "SCAN",
          "SHIP_TO_STORE",
          "HEADER_FLAT",
          "ITEM_FLAT",
        ],
        allowanceToBeCreated: [
          "Combined DSD",
          "Both",
          "One Allowance: Warehouse, DSD, or Combined",
          "Warehouse Only",
        ],
        label: "Allowance Dates",
        allowanceScreenTypes: ["DP", "AO"],
        create: {
          isEdit: false,
          isView: false,
          label: "Save & Continue",
        },
        edit: {
          isEdit: true,
          isView: false,
          label: "Update & Continue",
        },
      },
      "DSD Allowance Dates": {
        fields: {
          allowanceSpecificFields: {
            CASE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            SCAN: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            SHIP_TO_STORE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            HEADER_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            ITEM_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            DEFAULT: {
              key: "DSD_WHSE_RETAIL_DIVISION",
              routeKey: "DSD_WHSE_RETAIL_DIVISION",
              allowanceDatesData: {
                registerKeyName:
                  "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
              },
            },
          },
          vehicleTypeOrCustomDate: {
            label: "Vehicle Type/Custom Date",
            required: false,
            registerField: "allowance.vehicleTypeOrCustomDate",
            gqlQueryConst: "allowance",
            default: "Weekly Insert",
            type: "select",
            apiUrl: "",
            slice: "",
            displayLabel: "name",
            options: [
              {
                id: 1,
                name: "Weekly Insert",
              },
              {
                id: 2,
                name: "Monthly Insert",
              },
              {
                id: 3,
                name: "Yearly Insert",
              },
            ],
            errors: {
              required: {
                backgroundColor: "",
                text: "Vehicle Type/Custom Date is Required",
              },
            },
            mapperKey:
              "offerallowances.allowances.vehicle.vehicleType.vehicleTypDesc",
          },
          year: {
            label: "Year",
            required: false,
            registerField: "year",
            default: "2023",
            type: "select",
            apiUrl: "",
            slice: "",
            displayLabel: "name",
            options: [
              {
                name: 2022,
                id: 1,
              },
              {
                name: 2023,
                id: 2,
              },
              {
                name: 2024,
                id: 3,
              },
              {
                name: 2025,
                id: 4,
              },
            ],
            errors: {
              required: {
                backgroundColor: "",
                text: "Year is Required",
              },
            },
          },
          startWeekOrVehicle: {
            label: "Start Week/Vehicle",
            required: false,
            registerField: "startWeekOrVehicle",
            optionUrl: "allowance.startWeek",
            default: "Week 02 Insert 2023",
            type: "select",
            apiUrl: "",
            slice: "",
            displayLabel: "vehicleNm",
            options: [
              {
                id: "",
                vehicleNm: "",
              },
            ],
            errors: {
              required: {
                backgroundColor: "",
                text: "Start Week/Vehicle is Required",
              },
            },
            mapperKey: "offerallowances.allowances.vehicle.vehicleNm",
          },
          vehicleStart: {
            label: "Vehicle Start",
            required: false,
            disable: true,
            registerField: "allowance.vehicleStart",
            type: "date",
            displayLabel: "name",
            error: {
              required: {
                backgroundColor: "",
                text: "Allowance Start Required",
              },
            },
            mapperKey: "offerallowances.allowances.vehicle.startDate",
          },
          vehicleEnd: {
            label: "Vehicle End",
            required: false,
            disable: true,
            registerField: "allowance.vehicleEnd",
            type: "date",
            displayLabel: "name",
            error: {
              required: {
                backgroundColor: "",
                text: "Allowance End Required",
              },
            },
            mapperKey: "offerallowances.allowances.vehicle.endDate",
          },
          orderStart: {
            label: "Order Start",
            required: true,
            disable: true,
            registerField: "allowance.orderStart",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Order start Required",
              },
            },
            mapperKey: "offerallowances.allowances.orderStartDate",
          },
          orderEnd: {
            label: "Order End",
            required: true,
            disable: true,
            registerField: "allowance.orderEnd",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Order End Required",
              },
            },
            mapperKey: "offerallowances.allowances.orderEndDate",
          },
          arrivalStart: {
            label: "Arrival Start",
            required: true,
            disable: true,
            registerField: "allowance.arrivalStart",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Arrival Start Required",
              },
            },
            mapperKey: "offerallowances.allowances.arrivalStartDate",
          },
          arrivalEnd: {
            label: "Arrival End",
            required: true,
            disable: true,
            registerField: "allowance.arrivalEnd",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Arrival End Required",
              },
            },
            mapperKey: "offerallowances.allowances.arrivalEndDate",
          },
          storeReceivingStart: {
            label: "Shipment Start",
            required: true,
            disable: true,
            registerField: "allowance.storeReceivingStart",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Shipment Start Required",
              },
            },
            mapperKey: "offerallowances.allowances.storeReceivingStartDate",
          },
          storeReceivingEnd: {
            label: "Shipment End",
            required: true,
            disable: true,
            registerField: "allowance.storeReceivingEnd",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Shipment End Required",
              },
            },
            mapperKey: "offerallowances.allowances.storeReceivingEndDate",
          },
          startDate: {
            label: "Start Date",
            required: true,
            disable: true,
            registerField: "allowance.startDate",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Start Date Required",
              },
            },
            mapperKey: "offerallowances.allowances.orderStartDate",
          },
          endDate: {
            label: "End Date",
            required: true,
            disable: true,
            registerField: "allowance.endDate",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "End Date Required",
              },
            },
            mapperKey: "offerallowances.allowances.orderEndDate",
          },
          errors: {
            EMPTY_ALLOWANCES:
              "Invalid combination of Promo Product Groups and Store Groups. Please update Promo Product Groups or Store Groups to continue.",
            INVALID_DATES: {
              SCAN: {
                all: "Invalid Date combination, End date must be >= Start date",
                vehicle:
                  "Allowance Dates must overlap Vehicle Dates by at least 1 day",
              },
              CASE: {
                orderStart:
                  "Invalid Date combination, Arrival Start Date must be >= Order Start date",
                orderEnd:
                  "Invalid Date combination, Arrival End Date must be >= Order End date",
                order:
                  "Invalid Date combination, Order End date must be >= Order Start date",
                arrival:
                  "Invalid Date combination, Arrival End date must be >= Arrival Start date",
                vehicle:
                  "Allowance Dates must overlap Vehicle Dates by at least 1 day",
              },
              SHIP_TO_STORE: {
                ship: "Invalid Date combination, Shipment End date must be >= Shipment Start date",
                vehicle:
                  "Allowance Dates must overlap Vehicle Dates by at least 1 day",
              },
              OTHER: "Invalid Date combination, End date must be >= Start date",
            },
          },
          allowanceCreationVehicle: {
            registerKeyName: "allowanceCreationVehicle",
          },
          initialText: {
            SCAN: {
              DSD_WHSE_RETAIL_DIVISION: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
              DSD_LEAD_DISTRIBUTORS: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
            },
            CASE: {
              DSD_LEAD_DISTRIBUTORS: {
                header:
                  "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than that of the full Event. These dates will be used for O/I and/or Billing.",
                bulletPointsText:
                  "Based on ((Albertsons Accounting Policies,)) the dates for DSD Case Allowances will be pre-calculated as:",
                bulletPoints: [
                  "DSD Shipment Start: set to be 2 days prior to Vehicle Start",
                  "DSD Shipment End: set to match the Vehicle End",
                  "If an exception to these default dates is necessary due to either other Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
                ],
              },
              WAREHOUSE_DIST_CENTERS: {
                header:
                  "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for O/I and/or Billing.",
                bulletPointsText:
                  "Based on ((Albertsons Accounting Policies,)) the dates for Warehouse Case Allowances will be pre-calculated as:",
                bulletPoints: [
                  "Order Start: Arrival Start Date - Lead Time* ",
                  "Order End: Arrival End Date - Lead Time*",
                  "Arrival Start: 10 days prior to Vehicle Start. 16 days in Northern California due to Hawaii, 21 days in Alaska.",
                  "Arrival End: Vehicle End date",
                  "For Displayer items only, an additional 14 days will be incorporated into the Order Start and Arrival Start date calculations",
                ],
                footer:
                  "* As setup in the Albertsons buying system to represent total time in days from P.O. generation through warehouse receiving. If an exception to these default dates is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
              },
            },
            SHIP_TO_STORE: {
              DSD_WHSE_RETAIL_DIVISION: {
                header:
                  "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period shorter than the full Event.",
                subHeader:
                  "Based on ((Albertsons Accounting Policies,)) the dates for Ship To Store Allowances are not tied only to the Vehicle / Performance.  These allowances are earned upon Shipment and have no less than 7 days prior to the Performance start and 3 days after the end date.  14 Days prior for Displays.",
                footer:
                  "If an exception to these defaults is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
              },
              DSD_LEAD_DISTRIBUTORS: {
                header:
                  "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period shorter than the full Event.",
                subHeader:
                  "Based on ((Albertsons Accounting Policies,)) the dates for Ship To Store Allowances are not tied only to the Vehicle / Performance.  These allowances are earned upon Shipment and have no less than 7 days prior to the Performance start and 3 days after the end date.  14 Days prior for Displays.",
                footer:
                  "If an exception to these defaults is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
              },
            },
            HEADER_FLAT: {
              DSD_WHSE_RETAIL_DIVISION: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
              WAREHOUSE_DIST_CENTERS: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
            },
            ITEM_FLAT: {
              DSD_WHSE_RETAIL_DIVISION: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
              WAREHOUSE_DIST_CENTERS: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
            },
          },
        },
        isOpen: false,
        offerTypeKey: "DSD",
        allowanceTypeAndPerformance: ["SCAN", "SHIP_TO_STORE"],
        allowanceToBeCreated: [
          "Separate Allowances By DSD Distributor",
          "DSD Only",
        ],
        label: "Allowance Dates",
        allowanceScreenTypes: ["DP", "AO"],
        create: {
          isEdit: false,
          isView: false,
          label: "Save & Continue",
        },
        edit: {
          isEdit: true,
          isView: false,
          label: "Update & Continue",
        },
      },
      "DSD Case Allowance Dates": {
        fields: {
          allowanceSpecificFields: {
            CASE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            SCAN: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            SHIP_TO_STORE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            HEADER_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            ITEM_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            DEFAULT: {
              key: "DSD_WHSE_RETAIL_DIVISION",
              routeKey: "DSD_WHSE_RETAIL_DIVISION",
              allowanceDatesData: {
                registerKeyName:
                  "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
              },
            },
          },
          vehicleTypeOrCustomDate: {
            label: "Vehicle Type/Custom Date",
            required: false,
            registerField: "allowance.vehicleTypeOrCustomDate",
            gqlQueryConst: "allowance",
            default: "Weekly Insert",
            type: "select",
            apiUrl: "",
            slice: "",
            displayLabel: "name",
            options: [
              {
                id: 1,
                name: "Weekly Insert",
              },
              {
                id: 2,
                name: "Monthly Insert",
              },
              {
                id: 3,
                name: "Yearly Insert",
              },
            ],
            errors: {
              required: {
                backgroundColor: "",
                text: "Vehicle Type/Custom Date is Required",
              },
            },
            mapperKey:
              "offerallowances.allowances.vehicle.vehicleType.vehicleTypDesc",
          },
          year: {
            label: "Year",
            required: false,
            registerField: "year",
            default: "2023",
            type: "select",
            apiUrl: "",
            slice: "",
            displayLabel: "name",
            options: [
              {
                name: 2022,
                id: 1,
              },
              {
                name: 2023,
                id: 2,
              },
              {
                name: 2024,
                id: 3,
              },
              {
                name: 2025,
                id: 4,
              },
            ],
            errors: {
              required: {
                backgroundColor: "",
                text: "Year is Required",
              },
            },
          },
          startWeekOrVehicle: {
            label: "Start Week/Vehicle",
            required: false,
            registerField: "startWeekOrVehicle",
            optionUrl: "allowance.startWeek",
            default: "Week 02 Insert 2023",
            type: "select",
            apiUrl: "",
            slice: "",
            displayLabel: "vehicleNm",
            options: [
              {
                id: "",
                vehicleNm: "",
              },
            ],
            errors: {
              required: {
                backgroundColor: "",
                text: "Start Week/Vehicle is Required",
              },
            },
            mapperKey: "offerallowances.allowances.vehicle.vehicleNm",
          },
          vehicleStart: {
            label: "Vehicle Start",
            required: false,
            disable: true,
            registerField: "allowance.vehicleStart",
            type: "date",
            displayLabel: "name",
            error: {
              required: {
                backgroundColor: "",
                text: "Allowance Start Required",
              },
            },
            mapperKey: "offerallowances.allowances.vehicle.startDate",
          },
          vehicleEnd: {
            label: "Vehicle End",
            required: false,
            disable: true,
            registerField: "allowance.vehicleEnd",
            type: "date",
            displayLabel: "name",
            error: {
              required: {
                backgroundColor: "",
                text: "Allowance End Required",
              },
            },
            mapperKey: "offerallowances.allowances.vehicle.endDate",
          },
          orderStart: {
            label: "Order Start",
            required: true,
            disable: true,
            registerField: "allowance.orderStart",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Order start Required",
              },
            },
            mapperKey: "offerallowances.allowances.orderStartDate",
          },
          orderEnd: {
            label: "Order End",
            required: true,
            disable: true,
            registerField: "allowance.orderEnd",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Order End Required",
              },
            },
            mapperKey: "offerallowances.allowances.orderEndDate",
          },
          arrivalStart: {
            label: "Arrival Start",
            required: true,
            disable: true,
            registerField: "allowance.arrivalStart",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Arrival Start Required",
              },
            },
            mapperKey: "offerallowances.allowances.arrivalStartDate",
          },
          arrivalEnd: {
            label: "Arrival End",
            required: true,
            disable: true,
            registerField: "allowance.arrivalEnd",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Arrival End Required",
              },
            },
            mapperKey: "offerallowances.allowances.arrivalEndDate",
          },
          storeReceivingStart: {
            label: "Shipment Start",
            required: true,
            disable: true,
            registerField: "allowance.storeReceivingStart",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Shipment Start Required",
              },
            },
            mapperKey: "offerallowances.allowances.storeReceivingStartDate",
          },
          storeReceivingEnd: {
            label: "Shipment End",
            required: true,
            disable: true,
            registerField: "allowance.storeReceivingEnd",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Shipment End Required",
              },
            },
            mapperKey: "offerallowances.allowances.storeReceivingEndDate",
          },
          startDate: {
            label: "Start Date",
            required: true,
            disable: true,
            registerField: "allowance.startDate",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Start Date Required",
              },
            },
            mapperKey: "offerallowances.allowances.orderStartDate",
          },
          endDate: {
            label: "End Date",
            required: true,
            disable: true,
            registerField: "allowance.endDate",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "End Date Required",
              },
            },
            mapperKey: "offerallowances.allowances.orderEndDate",
          },
          errors: {
            EMPTY_ALLOWANCES:
              "Invalid combination of Promo Product Groups and Store Groups. Please update Promo Product Groups or Store Groups to continue.",
            INVALID_DATES: {
              SCAN: {
                all: "Invalid Date combination, End date must be >= Start date",
                vehicle:
                  "Allowance Dates must overlap Vehicle Dates by at least 1 day",
              },
              CASE: {
                orderStart:
                  "Invalid Date combination, Arrival Start Date must be >= Order Start date",
                orderEnd:
                  "Invalid Date combination, Arrival End Date must be >= Order End date",
                order:
                  "Invalid Date combination, Order End date must be >= Order Start date",
                arrival:
                  "Invalid Date combination, Arrival End date must be >= Arrival Start date",
                vehicle:
                  "Allowance Dates must overlap Vehicle Dates by at least 1 day",
              },
              SHIP_TO_STORE: {
                ship: "Invalid Date combination, Shipment End date must be >= Shipment Start date",
                vehicle:
                  "Allowance Dates must overlap Vehicle Dates by at least 1 day",
              },
              OTHER: "Invalid Date combination, End date must be >= Start date",
            },
          },
          allowanceCreationVehicle: {
            registerKeyName: "allowanceCreationVehicle",
          },
          initialText: {
            SCAN: {
              DSD_WHSE_RETAIL_DIVISION: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
              DSD_LEAD_DISTRIBUTORS: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
            },
            CASE: {
              DSD_LEAD_DISTRIBUTORS: {
                header:
                  "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than that of the full Event. These dates will be used for O/I and/or Billing.",
                bulletPointsText:
                  "Based on ((Albertsons Accounting Policies,)) the dates for DSD Case Allowances will be pre-calculated as:",
                bulletPoints: [
                  "DSD Shipment Start: set to be 2 days prior to Vehicle Start",
                  "DSD Shipment End: set to match the Vehicle End",
                  "If an exception to these default dates is necessary due to either other Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
                ],
              },
              WAREHOUSE_DIST_CENTERS: {
                header:
                  "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for O/I and/or Billing.",
                bulletPointsText:
                  "Based on ((Albertsons Accounting Policies,)) the dates for Warehouse Case Allowances will be pre-calculated as:",
                bulletPoints: [
                  "Order Start: Arrival Start Date - Lead Time* ",
                  "Order End: Arrival End Date - Lead Time*",
                  "Arrival Start: 10 days prior to Vehicle Start. 16 days in Northern California due to Hawaii, 21 days in Alaska.",
                  "Arrival End: Vehicle End date",
                  "For Displayer items only, an additional 14 days will be incorporated into the Order Start and Arrival Start date calculations",
                ],
                footer:
                  "* As setup in the Albertsons buying system to represent total time in days from P.O. generation through warehouse receiving. If an exception to these default dates is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
              },
            },
            SHIP_TO_STORE: {
              DSD_WHSE_RETAIL_DIVISION: {
                header:
                  "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period shorter than the full Event.",
                subHeader:
                  "Based on ((Albertsons Accounting Policies,)) the dates for Ship To Store Allowances are not tied only to the Vehicle / Performance.  These allowances are earned upon Shipment and have no less than 7 days prior to the Performance start and 3 days after the end date.  14 Days prior for Displays.",
                footer:
                  "If an exception to these defaults is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
              },
              DSD_LEAD_DISTRIBUTORS: {
                header:
                  "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period shorter than the full Event.",
                subHeader:
                  "Based on ((Albertsons Accounting Policies,)) the dates for Ship To Store Allowances are not tied only to the Vehicle / Performance.  These allowances are earned upon Shipment and have no less than 7 days prior to the Performance start and 3 days after the end date.  14 Days prior for Displays.",
                footer:
                  "If an exception to these defaults is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
              },
            },
            HEADER_FLAT: {
              DSD_WHSE_RETAIL_DIVISION: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
              WAREHOUSE_DIST_CENTERS: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
            },
            ITEM_FLAT: {
              DSD_WHSE_RETAIL_DIVISION: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
              WAREHOUSE_DIST_CENTERS: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
            },
          },
        },
        isOpen: false,
        allowanceTypeAndPerformance: ["CASE"],
        allowanceToBeCreated: ["Both", "DSD Only"],
        label: "DSD Case Allowance Dates",
        offerTypeKey: "DSD",
        allowanceScreenTypes: ["DP", "AO"],
        create: {
          isEdit: false,
          isView: false,
          label: "Save & Continue",
        },
        edit: {
          isEdit: true,
          isView: false,
          label: "Update & Continue",
        },
      },
      "Warehouse Case Allowance Dates": {
        fields: {
          allowanceSpecificFields: {
            CASE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            SCAN: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            SHIP_TO_STORE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            HEADER_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            ITEM_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceDatesData: {
                  registerKeyName:
                    "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            DEFAULT: {
              key: "DSD_WHSE_RETAIL_DIVISION",
              routeKey: "DSD_WHSE_RETAIL_DIVISION",
              allowanceDatesData: {
                registerKeyName:
                  "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
              },
            },
          },
          vehicleTypeOrCustomDate: {
            label: "Vehicle Type/Custom Date",
            required: false,
            registerField: "allowance.vehicleTypeOrCustomDate",
            gqlQueryConst: "allowance",
            default: "Weekly Insert",
            type: "select",
            apiUrl: "",
            slice: "",
            displayLabel: "name",
            options: [
              {
                id: 1,
                name: "Weekly Insert",
              },
              {
                id: 2,
                name: "Monthly Insert",
              },
              {
                id: 3,
                name: "Yearly Insert",
              },
            ],
            errors: {
              required: {
                backgroundColor: "",
                text: "Vehicle Type/Custom Date is Required",
              },
            },
            mapperKey:
              "offerallowances.allowances.vehicle.vehicleType.vehicleTypDesc",
          },
          year: {
            label: "Year",
            required: false,
            registerField: "year",
            default: "2023",
            type: "select",
            apiUrl: "",
            slice: "",
            displayLabel: "name",
            options: [
              {
                name: 2022,
                id: 1,
              },
              {
                name: 2023,
                id: 2,
              },
              {
                name: 2024,
                id: 3,
              },
              {
                name: 2025,
                id: 4,
              },
            ],
            errors: {
              required: {
                backgroundColor: "",
                text: "Year is Required",
              },
            },
          },
          startWeekOrVehicle: {
            label: "Start Week/Vehicle",
            required: false,
            registerField: "startWeekOrVehicle",
            optionUrl: "allowance.startWeek",
            default: "Week 02 Insert 2023",
            type: "select",
            apiUrl: "",
            slice: "",
            displayLabel: "vehicleNm",
            options: [
              {
                id: "",
                vehicleNm: "",
              },
            ],
            errors: {
              required: {
                backgroundColor: "",
                text: "Start Week/Vehicle is Required",
              },
            },
            mapperKey: "offerallowances.allowances.vehicle.vehicleNm",
          },
          vehicleStart: {
            label: "Vehicle Start",
            required: false,
            disable: true,
            registerField: "allowance.vehicleStart",
            type: "date",
            displayLabel: "name",
            error: {
              required: {
                backgroundColor: "",
                text: "Allowance Start Required",
              },
            },
            mapperKey: "offerallowances.allowances.vehicle.startDate",
          },
          vehicleEnd: {
            label: "Vehicle End",
            required: false,
            disable: true,
            registerField: "allowance.vehicleEnd",
            type: "date",
            displayLabel: "name",
            error: {
              required: {
                backgroundColor: "",
                text: "Allowance End Required",
              },
            },
            mapperKey: "offerallowances.allowances.vehicle.endDate",
          },
          orderStart: {
            label: "Order Start",
            required: true,
            disable: true,
            registerField: "allowance.orderStart",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Order start Required",
              },
            },
            mapperKey: "offerallowances.allowances.orderStartDate",
          },
          orderEnd: {
            label: "Order End",
            required: true,
            disable: true,
            registerField: "allowance.orderEnd",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Order End Required",
              },
            },
            mapperKey: "offerallowances.allowances.orderEndDate",
          },
          arrivalStart: {
            label: "Arrival Start",
            required: true,
            disable: true,
            registerField: "allowance.arrivalStart",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Arrival Start Required",
              },
            },
            mapperKey: "offerallowances.allowances.arrivalStartDate",
          },
          arrivalEnd: {
            label: "Arrival End",
            required: true,
            disable: true,
            registerField: "allowance.arrivalEnd",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Arrival End Required",
              },
            },
            mapperKey: "offerallowances.allowances.arrivalEndDate",
          },
          storeReceivingStart: {
            label: "Shipment Start",
            required: true,
            disable: true,
            registerField: "allowance.storeReceivingStart",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Shipment Start Required",
              },
            },
            mapperKey: "offerallowances.allowances.storeReceivingStartDate",
          },
          storeReceivingEnd: {
            label: "Shipment End",
            required: true,
            disable: true,
            registerField: "allowance.storeReceivingEnd",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Shipment End Required",
              },
            },
            mapperKey: "offerallowances.allowances.storeReceivingEndDate",
          },
          startDate: {
            label: "Start Date",
            required: true,
            disable: true,
            registerField: "allowance.startDate",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "Start Date Required",
              },
            },
            mapperKey: "offerallowances.allowances.orderStartDate",
          },
          endDate: {
            label: "End Date",
            required: true,
            disable: true,
            registerField: "allowance.endDate",
            type: "date",
            error: {
              required: {
                backgroundColor: "",
                text: "End Date Required",
              },
            },
            mapperKey: "offerallowances.allowances.orderEndDate",
          },
          errors: {
            EMPTY_ALLOWANCES:
              "Invalid combination of Promo Product Groups and Store Groups. Please update Promo Product Groups or Store Groups to continue.",
            INVALID_DATES: {
              SCAN: {
                all: "Invalid Date combination, End date must be >= Start date",
                vehicle:
                  "Allowance Dates must overlap Vehicle Dates by at least 1 day",
              },
              CASE: {
                orderStart:
                  "Invalid Date combination, Arrival Start Date must be >= Order Start date",
                orderEnd:
                  "Invalid Date combination, Arrival End Date must be >= Order End date",
                order:
                  "Invalid Date combination, Order End date must be >= Order Start date",
                arrival:
                  "Invalid Date combination, Arrival End date must be >= Arrival Start date",
                vehicle:
                  "Allowance Dates must overlap Vehicle Dates by at least 1 day",
              },
              SHIP_TO_STORE: {
                ship: "Invalid Date combination, Shipment End date must be >= Shipment Start date",
                vehicle:
                  "Allowance Dates must overlap Vehicle Dates by at least 1 day",
              },
              OTHER: "Invalid Date combination, End date must be >= Start date",
            },
          },
          allowanceCreationVehicle: {
            registerKeyName: "allowanceCreationVehicle",
          },
          initialText: {
            SCAN: {
              DSD_WHSE_RETAIL_DIVISION: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
              DSD_LEAD_DISTRIBUTORS: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
            },
            CASE: {
              DSD_LEAD_DISTRIBUTORS: {
                header:
                  "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than that of the full Event. These dates will be used for O/I and/or Billing.",
                bulletPointsText:
                  "Based on ((Albertsons Accounting Policies,)) the dates for DSD Case Allowances will be pre-calculated as:",
                bulletPoints: [
                  "DSD Shipment Start: set to be 2 days prior to Vehicle Start",
                  "DSD Shipment End: set to match the Vehicle End",
                  "If an exception to these default dates is necessary due to either other Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
                ],
              },
              WAREHOUSE_DIST_CENTERS: {
                header:
                  "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for O/I and/or Billing.",
                bulletPointsText:
                  "Based on ((Albertsons Accounting Policies,)) the dates for Warehouse Case Allowances will be pre-calculated as:",
                bulletPoints: [
                  "Order Start: Arrival Start Date - Lead Time* ",
                  "Order End: Arrival End Date - Lead Time*",
                  "Arrival Start: 10 days prior to Vehicle Start. 16 days in Northern California due to Hawaii, 21 days in Alaska.",
                  "Arrival End: Vehicle End date",
                  "For Displayer items only, an additional 14 days will be incorporated into the Order Start and Arrival Start date calculations",
                ],
                footer:
                  "* As setup in the Albertsons buying system to represent total time in days from P.O. generation through warehouse receiving. If an exception to these default dates is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
              },
            },
            SHIP_TO_STORE: {
              DSD_WHSE_RETAIL_DIVISION: {
                header:
                  "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period shorter than the full Event.",
                subHeader:
                  "Based on ((Albertsons Accounting Policies,)) the dates for Ship To Store Allowances are not tied only to the Vehicle / Performance.  These allowances are earned upon Shipment and have no less than 7 days prior to the Performance start and 3 days after the end date.  14 Days prior for Displays.",
                footer:
                  "If an exception to these defaults is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
              },
              DSD_LEAD_DISTRIBUTORS: {
                header:
                  "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period shorter than the full Event.",
                subHeader:
                  "Based on ((Albertsons Accounting Policies,)) the dates for Ship To Store Allowances are not tied only to the Vehicle / Performance.  These allowances are earned upon Shipment and have no less than 7 days prior to the Performance start and 3 days after the end date.  14 Days prior for Displays.",
                footer:
                  "If an exception to these defaults is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
              },
            },
            HEADER_FLAT: {
              DSD_WHSE_RETAIL_DIVISION: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
              WAREHOUSE_DIST_CENTERS: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
            },
            ITEM_FLAT: {
              DSD_WHSE_RETAIL_DIVISION: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
              WAREHOUSE_DIST_CENTERS: {
                header:
                  "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
              },
            },
          },
        },
        isOpen: false,
        allowanceTypeAndPerformance: ["CASE"],
        allowanceToBeCreated: ["Both", "Warehouse Only"],
        label: "Warehouse Case Allowance Dates",
        offerTypeKey: "WAREHOUSE",
        allowanceScreenTypes: ["DP", "AO"],
        create: {
          isEdit: false,
          isView: false,
          label: "Save & Continue",
        },
        edit: {
          isEdit: true,
          isView: false,
          label: "Update & Continue",
        },
      },
      "Allowance Amounts": {
        fields: {
          allowanceSpecificFields: {
            CASE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceAmountsData: {
                  registerKeyName: "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            SCAN: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceAmountsData: {
                  registerKeyName: "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            SHIP_TO_STORE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceAmountsData: {
                  registerKeyName: "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            HEADER_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            ITEM_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            DEFAULT: {
              key: "DSD_WHSE_RETAIL_DIVISION",
              routeKey: "DSD_WHSE_RETAIL_DIVISION",
              allowanceAmountsData: {
                registerKeyName:
                  "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
              },
            },
          },
          allowanceAmount: {
            label: "Allowance Amount",
            required: true,
            registerField: "allowanceAmount",
            type: "text",
            errors: {
              required: {
                text: "Allowance Amount Required",
                errorMessages: "Allowance Amount Required",
              },
              maxLength: {
                length: 10,
                text: "Max Length Reached",
              },
              digitError: {
                text: "Allowance amount cannot be zero",
              },
            },
            tooltip:
              "This is auto generated based on your selections.\n You can edit this if you want.",
            mapperKey:
              "offerallowances.allowances.allowanceItems.allowanceItemComps.allowAmount",
          },
          allowanceAmountPerItem: {
            label: "Allowance Amount per Item",
            required: true,
            registerField: "allowanceAmount",
            type: "text",
            errors: {
              required: {
                text: "Allowance Amount Required per Item",
                errorMessages: "Allowance Amount Required per Item",
              },
              maxLength: {
                length: 10,
                text: "Max Length Reached",
              },
              formatError: {
                pattern: {},
                text: "Please enter valid Allowance Amount per Item",
              },
              digitError: {
                text: "Allowance amount cannot be zero",
              },
            },
            tooltip:
              "This is auto generated based on your selections.\n You can edit this if you want.",
            mapperKey:
              "offerallowances.allowances.allowanceItems.allowanceItemComps.allowanceAmount",
          },
          uom: {
            label: "UOM",
            required: true,
            registerField: "uom",
            type: "select",
            apiUrl: "",
            slice: "",
            displayLabel: "name",
            errors: {
              required: {
                backgroundColor: "",
                text: "UOM Required",
                errorMessages: "UOM Required",
              },
            },
            mapperKey: "offerallowances.allowances.allowanceItems.uom",
          },
          allowanceAmountsData: {
            registerKeyName: "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
          },
        },
        isOpen: false,
        allowanceTypeAndPerformance: [
          "SCAN",
          "SHIP_TO_STORE",
          "HEADER_FLAT",
          "ITEM_FLAT",
        ],
        allowanceToBeCreated: [
          "One Allowance: Warehouse, DSD, or Combined",
          "Combined DSD",
        ],
        label: "Allowance Amounts",
        offerTypeKey: "WAREHOUSE",
        allowanceScreenTypes: ["DP", "AO"],
        create: {
          isEdit: false,
          isView: false,
          label: "Save & Continue",
        },
        edit: {
          isEdit: true,
          isView: false,
          label: "Update & Continue",
        },
        disable: {
          createInd: ["LC"],
          allowanceScreenType: ["DP"],
        },
      },
      "DSD Case Allowance Amount": {
        fields: {
          allowanceSpecificFields: {
            CASE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceAmountsData: {
                  registerKeyName: "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            SCAN: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceAmountsData: {
                  registerKeyName: "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            SHIP_TO_STORE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceAmountsData: {
                  registerKeyName: "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            HEADER_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            ITEM_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            DEFAULT: {
              key: "DSD_WHSE_RETAIL_DIVISION",
              routeKey: "DSD_WHSE_RETAIL_DIVISION",
              allowanceAmountsData: {
                registerKeyName:
                  "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
              },
            },
          },
          allowanceAmount: {
            label: "Allowance Amount",
            required: true,
            registerField: "allowanceAmount",
            type: "text",
            errors: {
              required: {
                text: "Allowance Amount Required",
                errorMessages: "Allowance Amount Required",
              },
              maxLength: {
                length: 10,
                text: "Max Length Reached",
              },
              digitError: {
                text: "Allowance amount cannot be zero",
              },
            },
            tooltip:
              "This is auto generated based on your selections.\n You can edit this if you want.",
            mapperKey:
              "offerallowances.allowances.allowanceItems.allowanceItemComps.allowAmount",
          },
          allowanceAmountPerItem: {
            label: "Allowance Amount per Item",
            required: true,
            registerField: "allowanceAmount",
            type: "text",
            errors: {
              required: {
                text: "Allowance Amount Required per Item",
                errorMessages: "Allowance Amount Required per Item",
              },
              maxLength: {
                length: 10,
                text: "Max Length Reached",
              },
              formatError: {
                pattern: {},
                text: "Please enter valid Allowance Amount per Item",
              },
              digitError: {
                text: "Allowance amount cannot be zero",
              },
            },
            tooltip:
              "This is auto generated based on your selections.\n You can edit this if you want.",
            mapperKey:
              "offerallowances.allowances.allowanceItems.allowanceItemComps.allowanceAmount",
          },
          uom: {
            label: "UOM",
            required: true,
            registerField: "uom",
            type: "select",
            apiUrl: "",
            slice: "",
            displayLabel: "name",
            errors: {
              required: {
                backgroundColor: "",
                text: "UOM Required",
                errorMessages: "UOM Required",
              },
            },
            mapperKey: "offerallowances.allowances.allowanceItems.uom",
          },
          allowanceAmountsData: {
            registerKeyName: "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
          },
        },
        isOpen: false,
        allowanceTypeAndPerformance: ["CASE"],
        allowanceToBeCreated: ["Both", "DSD Only"],
        label: "DSD Case Allowance Amount",
        offerTypeKey: "DSD",
        allowanceScreenTypes: ["DP", "AO"],
        create: {
          isEdit: false,
          isView: false,
          label: "Save & Continue",
        },
        edit: {
          isEdit: true,
          isView: false,
          label: "Update & Continue",
        },
      },
      "DSD Scan Allowance Amount": {
        fields: {
          allowanceSpecificFields: {
            CASE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceAmountsData: {
                  registerKeyName: "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            SCAN: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceAmountsData: {
                  registerKeyName: "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            SHIP_TO_STORE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceAmountsData: {
                  registerKeyName: "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            HEADER_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            ITEM_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            DEFAULT: {
              key: "DSD_WHSE_RETAIL_DIVISION",
              routeKey: "DSD_WHSE_RETAIL_DIVISION",
              allowanceAmountsData: {
                registerKeyName:
                  "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
              },
            },
          },
          allowanceAmount: {
            label: "Allowance Amount",
            required: true,
            registerField: "allowanceAmount",
            type: "text",
            errors: {
              required: {
                text: "Allowance Amount Required",
                errorMessages: "Allowance Amount Required",
              },
              maxLength: {
                length: 10,
                text: "Max Length Reached",
              },
              digitError: {
                text: "Allowance amount cannot be zero",
              },
            },
            tooltip:
              "This is auto generated based on your selections.\n You can edit this if you want.",
            mapperKey:
              "offerallowances.allowances.allowanceItems.allowanceItemComps.allowAmount",
          },
          allowanceAmountPerItem: {
            label: "Allowance Amount per Item",
            required: true,
            registerField: "allowanceAmount",
            type: "text",
            errors: {
              required: {
                text: "Allowance Amount Required per Item",
                errorMessages: "Allowance Amount Required per Item",
              },
              maxLength: {
                length: 10,
                text: "Max Length Reached",
              },
              formatError: {
                pattern: {},
                text: "Please enter valid Allowance Amount per Item",
              },
              digitError: {
                text: "Allowance amount cannot be zero",
              },
            },
            tooltip:
              "This is auto generated based on your selections.\n You can edit this if you want.",
            mapperKey:
              "offerallowances.allowances.allowanceItems.allowanceItemComps.allowanceAmount",
          },
          uom: {
            label: "UOM",
            required: true,
            registerField: "uom",
            type: "select",
            apiUrl: "",
            slice: "",
            displayLabel: "name",
            errors: {
              required: {
                backgroundColor: "",
                text: "UOM Required",
                errorMessages: "UOM Required",
              },
            },
            mapperKey: "offerallowances.allowances.allowanceItems.uom",
          },
          allowanceAmountsData: {
            registerKeyName: "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
          },
        },
        isOpen: false,
        allowanceTypeAndPerformance: ["SCAN"],
        allowanceToBeCreated: [
          "Both",
          "Separate Allowances By DSD Distributor",
        ],
        label: "DSD Scan Allowance Amounts",
        offerTypeKey: "DSD",
        allowanceScreenTypes: ["DP", "AO"],
        create: {
          isEdit: false,
          isView: false,
          label: "Save & Continue",
        },
        edit: {
          isEdit: true,
          isView: false,
          label: "Update & Continue",
        },
      },
      "DSD Ship To Store Allowance Amount": {
        fields: {
          allowanceSpecificFields: {
            CASE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceAmountsData: {
                  registerKeyName: "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            SCAN: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceAmountsData: {
                  registerKeyName: "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            SHIP_TO_STORE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceAmountsData: {
                  registerKeyName: "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            HEADER_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            ITEM_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            DEFAULT: {
              key: "DSD_WHSE_RETAIL_DIVISION",
              routeKey: "DSD_WHSE_RETAIL_DIVISION",
              allowanceAmountsData: {
                registerKeyName:
                  "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
              },
            },
          },
          allowanceAmount: {
            label: "Allowance Amount",
            required: true,
            registerField: "allowanceAmount",
            type: "text",
            errors: {
              required: {
                text: "Allowance Amount Required",
                errorMessages: "Allowance Amount Required",
              },
              maxLength: {
                length: 10,
                text: "Max Length Reached",
              },
              digitError: {
                text: "Allowance amount cannot be zero",
              },
            },
            tooltip:
              "This is auto generated based on your selections.\n You can edit this if you want.",
            mapperKey:
              "offerallowances.allowances.allowanceItems.allowanceItemComps.allowAmount",
          },
          allowanceAmountPerItem: {
            label: "Allowance Amount per Item",
            required: true,
            registerField: "allowanceAmount",
            type: "text",
            errors: {
              required: {
                text: "Allowance Amount Required per Item",
                errorMessages: "Allowance Amount Required per Item",
              },
              maxLength: {
                length: 10,
                text: "Max Length Reached",
              },
              formatError: {
                pattern: {},
                text: "Please enter valid Allowance Amount per Item",
              },
              digitError: {
                text: "Allowance amount cannot be zero",
              },
            },
            tooltip:
              "This is auto generated based on your selections.\n You can edit this if you want.",
            mapperKey:
              "offerallowances.allowances.allowanceItems.allowanceItemComps.allowanceAmount",
          },
          uom: {
            label: "UOM",
            required: true,
            registerField: "uom",
            type: "select",
            apiUrl: "",
            slice: "",
            displayLabel: "name",
            errors: {
              required: {
                backgroundColor: "",
                text: "UOM Required",
                errorMessages: "UOM Required",
              },
            },
            mapperKey: "offerallowances.allowances.allowanceItems.uom",
          },
          allowanceAmountsData: {
            registerKeyName: "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
          },
        },
        isOpen: false,
        allowanceTypeAndPerformance: ["SHIP_TO_STORE"],
        allowanceToBeCreated: [
          "Both",
          "Separate Allowances By DSD Distributor",
        ],
        label: "DSD Ship To Store Allowance Amounts",
        offerTypeKey: "DSD",
        allowanceScreenTypes: ["DP", "AO"],
        create: {
          isEdit: false,
          isView: false,
          label: "Save & Continue",
        },
        edit: {
          isEdit: true,
          isView: false,
          label: "Update & Continue",
        },
      },
      "Warehouse Case Allowance Amount": {
        fields: {
          allowanceSpecificFields: {
            CASE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceAmountsData: {
                  registerKeyName: "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            SCAN: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceAmountsData: {
                  registerKeyName: "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            SHIP_TO_STORE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceAmountsData: {
                  registerKeyName: "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            HEADER_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            ITEM_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            DEFAULT: {
              key: "DSD_WHSE_RETAIL_DIVISION",
              routeKey: "DSD_WHSE_RETAIL_DIVISION",
              allowanceAmountsData: {
                registerKeyName:
                  "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
              },
            },
          },
          allowanceAmount: {
            label: "Allowance Amount",
            required: true,
            registerField: "allowanceAmount",
            type: "text",
            errors: {
              required: {
                text: "Allowance Amount Required",
                errorMessages: "Allowance Amount Required",
              },
              maxLength: {
                length: 10,
                text: "Max Length Reached",
              },
              digitError: {
                text: "Allowance amount cannot be zero",
              },
            },
            tooltip:
              "This is auto generated based on your selections.\n You can edit this if you want.",
            mapperKey:
              "offerallowances.allowances.allowanceItems.allowanceItemComps.allowAmount",
          },
          allowanceAmountPerItem: {
            label: "Allowance Amount per Item",
            required: true,
            registerField: "allowanceAmount",
            type: "text",
            errors: {
              required: {
                text: "Allowance Amount Required per Item",
                errorMessages: "Allowance Amount Required per Item",
              },
              maxLength: {
                length: 10,
                text: "Max Length Reached",
              },
              formatError: {
                pattern: {},
                text: "Please enter valid Allowance Amount per Item",
              },
              digitError: {
                text: "Allowance amount cannot be zero",
              },
            },
            tooltip:
              "This is auto generated based on your selections.\n You can edit this if you want.",
            mapperKey:
              "offerallowances.allowances.allowanceItems.allowanceItemComps.allowanceAmount",
          },
          uom: {
            label: "UOM",
            required: true,
            registerField: "uom",
            type: "select",
            apiUrl: "",
            slice: "",
            displayLabel: "name",
            errors: {
              required: {
                backgroundColor: "",
                text: "UOM Required",
                errorMessages: "UOM Required",
              },
            },
            mapperKey: "offerallowances.allowances.allowanceItems.uom",
          },
          allowanceAmountsData: {
            registerKeyName: "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
          },
        },
        isOpen: false,
        allowanceTypeAndPerformance: ["CASE"],
        allowanceToBeCreated: ["Both", "Warehouse Only"],
        label: "Warehouse Case Allowance Amount",
        offerTypeKey: "WAREHOUSE",
        allowanceScreenTypes: ["DP", "AO"],
        create: {
          isEdit: false,
          isView: false,
          label: "Save & Continue",
        },
        edit: {
          isEdit: true,
          isView: false,
          label: "Update & Continue",
        },
      },
      "Warehouse Scan Allowance Amount": {
        fields: {
          allowanceSpecificFields: {
            CASE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceAmountsData: {
                  registerKeyName: "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            SCAN: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceAmountsData: {
                  registerKeyName: "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            SHIP_TO_STORE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                allowanceAmountsData: {
                  registerKeyName: "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            HEADER_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            ITEM_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                allowanceAmountsData: {
                  registerKeyName:
                    "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            DEFAULT: {
              key: "DSD_WHSE_RETAIL_DIVISION",
              routeKey: "DSD_WHSE_RETAIL_DIVISION",
              allowanceAmountsData: {
                registerKeyName:
                  "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
              },
            },
          },
          allowanceAmount: {
            label: "Allowance Amount",
            required: true,
            registerField: "allowanceAmount",
            type: "text",
            errors: {
              required: {
                text: "Allowance Amount Required",
                errorMessages: "Allowance Amount Required",
              },
              maxLength: {
                length: 10,
                text: "Max Length Reached",
              },
              digitError: {
                text: "Allowance amount cannot be zero",
              },
            },
            tooltip:
              "This is auto generated based on your selections.\n You can edit this if you want.",
            mapperKey:
              "offerallowances.allowances.allowanceItems.allowanceItemComps.allowAmount",
          },
          allowanceAmountPerItem: {
            label: "Allowance Amount per Item",
            required: true,
            registerField: "allowanceAmount",
            type: "text",
            errors: {
              required: {
                text: "Allowance Amount Required per Item",
                errorMessages: "Allowance Amount Required per Item",
              },
              maxLength: {
                length: 10,
                text: "Max Length Reached",
              },
              formatError: {
                pattern: {},
                text: "Please enter valid Allowance Amount per Item",
              },
              digitError: {
                text: "Allowance amount cannot be zero",
              },
            },
            tooltip:
              "This is auto generated based on your selections.\n You can edit this if you want.",
            mapperKey:
              "offerallowances.allowances.allowanceItems.allowanceItemComps.allowanceAmount",
          },
          uom: {
            label: "UOM",
            required: true,
            registerField: "uom",
            type: "select",
            apiUrl: "",
            slice: "",
            displayLabel: "name",
            errors: {
              required: {
                backgroundColor: "",
                text: "UOM Required",
                errorMessages: "UOM Required",
              },
            },
            mapperKey: "offerallowances.allowances.allowanceItems.uom",
          },
          allowanceAmountsData: {
            registerKeyName: "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
          },
        },
        isOpen: false,
        allowanceTypeAndPerformance: [],
        allowanceToBeCreated: ["Both"],
        label: "Warehouse Scan Allowance Amounts",
        offerTypeKey: "WAREHOUSE",
        allowanceScreenTypes: ["DP", "AO"],
        create: {
          isEdit: false,
          isView: false,
          label: "Save & Continue",
        },
        edit: {
          isEdit: true,
          isView: false,
          label: "Update & Continue",
        },
      },
      "Default Billing Information": {
        fields: {
          allowanceSpecificFields: {
            CASE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            SCAN: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            SHIP_TO_STORE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            HEADER_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            ITEM_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            DEFAULT: {
              key: "DSD_WHSE_RETAIL_DIVISION",
              routeKey: "DSD_WHSE_RETAIL_DIVISION",
              billingInformationData: {
                registerKeyName:
                  "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
              },
            },
          },
          suggestedVendorPaymentType: {
            label: "Suggested Payment Type",
            required: false,
            registerField: "suggestedVendorPaymentType",
            type: "select",
            default: "Deduct",
            displayLabel: "name",
            options: [
              {
                name: "Select",
                id: "",
              },
              {
                name: "Deduct",
                id: "Deduct",
              },
              {
                name: "Invoice",
                id: "Invoice",
              },
            ],
            apiUrl: "",
            slice: "",
            errors: {
              required: {
                backgroundColor: "",
                text: "ABS Vendor Payment Type is Required",
              },
            },
            tooltip:
              "This is auto generated based on your selections.\n You can edit this if you want.",
            mapperKey:
              "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
          },
          acApOrArNumber: {
            label: "Suggested A/P or A/R Number",
            required: false,
            registerField: "acApOrArNumber",
            type: "number",
            errors: {
              required: {
                backgroundColor: "",
                text: "A/P or A/R Vendor Number is Required",
              },
              formatError: {
                pattern: {},
                text: "Please enter valid A/P or A/R Vendor Number",
              },
              maxLength: {
                length: 8,
                text: "Maximum 8 characters allowed",
              },
            },
            mapperKey:
              "offerallowances.allowances.allowanceBillingInfo.acApOrArNumber",
          },
          vendorComment: {
            label: "Vendor Comment",
            required: false,
            registerField: "vendorComment",
            type: "text",
            errors: {
              required: {
                backgroundColor: "",
                text: "Vendor Comment is Required",
              },
              maxLength: {
                length: 1000,
                text: "Maximum 1000 characters allowed",
              },
            },
            mapperKey:
              "offerallowances.allowances.allowanceBillingInfo.vendorComment",
          },
          vendorOfferTrackingNbr: {
            label: "",
            required: false,
            registerField: "vendorOfferTrackingNbr",
            type: "text",
            errors: {
              required: {
                backgroundColor: "",
                text: "Vendor Tracking Number is Required",
              },
              maxLength: {
                length: 30,
                text: "Maximum 30 characters allowed",
              },
            },
            mapperKey:
              "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
          },
          commonComment: {
            label: "Vendor Comment",
            required: false,
            registerField: "allComment",
            type: "text",
            errors: {
              required: {
                backgroundColor: "",
                text: "Vendor Comment is Required",
              },
              maxLength: {
                length: 1000,
                text: "Maximum 1000 characters allowed",
              },
            },
          },
          billingInformationData: {
            registerKeyName: "billingInformationData",
          },
        },
        isOpen: false,
        allowanceTypeAndPerformance: [
          "SCAN",
          "SHIP_TO_STORE",
          "HEADER_FLAT",
          "ITEM_FLAT",
        ],
        allowanceToBeCreated: [
          "One Allowance: Warehouse, DSD, or Combined",
          "Combined DSD",
        ],
        label: "Default Billing Information",
        offerTypeKey: "WAREHOUSE",
        allowanceScreenTypes: ["DP", "AO"],
        create: {
          isEdit: false,
          isView: false,
          label: "Save & Continue",
        },
        edit: {
          isEdit: true,
          isView: false,
          label: "Update & Continue",
        },
      },
      "DSD Default Billing Information": {
        fields: {
          allowanceSpecificFields: {
            CASE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            SCAN: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            SHIP_TO_STORE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            HEADER_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            ITEM_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            DEFAULT: {
              key: "DSD_WHSE_RETAIL_DIVISION",
              routeKey: "DSD_WHSE_RETAIL_DIVISION",
              billingInformationData: {
                registerKeyName:
                  "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
              },
            },
          },
          suggestedVendorPaymentType: {
            label: "Suggested Payment Type",
            required: false,
            registerField: "suggestedVendorPaymentType",
            type: "select",
            default: "Deduct",
            displayLabel: "name",
            options: [
              {
                name: "Select",
                id: "",
              },
              {
                name: "Deduct",
                id: "Deduct",
              },
              {
                name: "Invoice",
                id: "Invoice",
              },
            ],
            apiUrl: "",
            slice: "",
            errors: {
              required: {
                backgroundColor: "",
                text: "ABS Vendor Payment Type is Required",
              },
            },
            tooltip:
              "This is auto generated based on your selections.\n You can edit this if you want.",
            mapperKey:
              "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
          },
          acApOrArNumber: {
            label: "Suggested A/P or A/R Number",
            required: false,
            registerField: "acApOrArNumber",
            type: "number",
            errors: {
              required: {
                backgroundColor: "",
                text: "A/P or A/R Vendor Number is Required",
              },
              formatError: {
                pattern: {},
                text: "Please enter valid A/P or A/R Vendor Number",
              },
              maxLength: {
                length: 8,
                text: "Maximum 8 characters allowed",
              },
            },
            mapperKey:
              "offerallowances.allowances.allowanceBillingInfo.acApOrArNumber",
          },
          vendorComment: {
            label: "Vendor Comment",
            required: false,
            registerField: "vendorComment",
            type: "text",
            errors: {
              required: {
                backgroundColor: "",
                text: "Vendor Comment is Required",
              },
              maxLength: {
                length: 1000,
                text: "Maximum 1000 characters allowed",
              },
            },
            mapperKey:
              "offerallowances.allowances.allowanceBillingInfo.vendorComment",
          },
          vendorOfferTrackingNbr: {
            label: "",
            required: false,
            registerField: "vendorOfferTrackingNbr",
            type: "text",
            errors: {
              required: {
                backgroundColor: "",
                text: "Vendor Tracking Number is Required",
              },
              maxLength: {
                length: 30,
                text: "Maximum 30 characters allowed",
              },
            },
            mapperKey:
              "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
          },
          commonComment: {
            label: "Vendor Comment",
            required: false,
            registerField: "allComment",
            type: "text",
            errors: {
              required: {
                backgroundColor: "",
                text: "Vendor Comment is Required",
              },
              maxLength: {
                length: 1000,
                text: "Maximum 1000 characters allowed",
              },
            },
          },
          billingInformationData: {
            registerKeyName: "billingInformationData",
          },
        },
        isOpen: false,
        allowanceTypeAndPerformance: ["CASE", "SCAN", "SHIP_TO_STORE"],
        allowanceToBeCreated: [
          "Both",
          "Separate Allowances By DSD Distributor",
          "DSD Only",
        ],
        label: "DSD Default Billing Information",
        offerTypeKey: "DSD",
        allowanceScreenTypes: ["DP", "AO"],
        create: {
          isEdit: false,
          isView: false,
          label: "Save & Continue",
        },
        edit: {
          isEdit: true,
          isView: false,
          label: "Update & Continue",
        },
      },
      "Warehouse Default Billing Information": {
        fields: {
          allowanceSpecificFields: {
            CASE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            SCAN: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            SHIP_TO_STORE: {
              DSD: {
                key: "DSD_LEAD_DISTRIBUTORS",
                routeKey: "DSD_LEAD_DISTRIBUTORS",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                },
              },
              WAREHOUSE: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
            },
            HEADER_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            ITEM_FLAT: {
              DSD: {
                key: "DSD_WHSE_RETAIL_DIVISION",
                routeKey: "DSD_WHSE_RETAIL_DIVISION",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                },
              },
              WAREHOUSE: {
                key: "WAREHOUSE_DIST_CENTERS",
                routeKey: "WAREHOUSE_DIST_CENTERS",
                billingInformationData: {
                  registerKeyName:
                    "billingInformationData.WAREHOUSE_DIST_CENTERS",
                },
              },
            },
            DEFAULT: {
              key: "DSD_WHSE_RETAIL_DIVISION",
              routeKey: "DSD_WHSE_RETAIL_DIVISION",
              billingInformationData: {
                registerKeyName:
                  "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
              },
            },
          },
          suggestedVendorPaymentType: {
            label: "Suggested Payment Type",
            required: false,
            registerField: "suggestedVendorPaymentType",
            type: "select",
            default: "Deduct",
            displayLabel: "name",
            options: [
              {
                name: "Select",
                id: "",
              },
              {
                name: "Deduct",
                id: "Deduct",
              },
              {
                name: "Invoice",
                id: "Invoice",
              },
            ],
            apiUrl: "",
            slice: "",
            errors: {
              required: {
                backgroundColor: "",
                text: "ABS Vendor Payment Type is Required",
              },
            },
            tooltip:
              "This is auto generated based on your selections.\n You can edit this if you want.",
            mapperKey:
              "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
          },
          acApOrArNumber: {
            label: "Suggested A/P or A/R Number",
            required: false,
            registerField: "acApOrArNumber",
            type: "number",
            errors: {
              required: {
                backgroundColor: "",
                text: "A/P or A/R Vendor Number is Required",
              },
              formatError: {
                pattern: {},
                text: "Please enter valid A/P or A/R Vendor Number",
              },
              maxLength: {
                length: 8,
                text: "Maximum 8 characters allowed",
              },
            },
            mapperKey:
              "offerallowances.allowances.allowanceBillingInfo.acApOrArNumber",
          },
          vendorComment: {
            label: "Vendor Comment",
            required: false,
            registerField: "vendorComment",
            type: "text",
            errors: {
              required: {
                backgroundColor: "",
                text: "Vendor Comment is Required",
              },
              maxLength: {
                length: 1000,
                text: "Maximum 1000 characters allowed",
              },
            },
            mapperKey:
              "offerallowances.allowances.allowanceBillingInfo.vendorComment",
          },
          vendorOfferTrackingNbr: {
            label: "",
            required: false,
            registerField: "vendorOfferTrackingNbr",
            type: "text",
            errors: {
              required: {
                backgroundColor: "",
                text: "Vendor Tracking Number is Required",
              },
              maxLength: {
                length: 30,
                text: "Maximum 30 characters allowed",
              },
            },
            mapperKey:
              "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
          },
          commonComment: {
            label: "Vendor Comment",
            required: false,
            registerField: "allComment",
            type: "text",
            errors: {
              required: {
                backgroundColor: "",
                text: "Vendor Comment is Required",
              },
              maxLength: {
                length: 1000,
                text: "Maximum 1000 characters allowed",
              },
            },
          },
          billingInformationData: {
            registerKeyName: "billingInformationData",
          },
        },
        isOpen: false,
        allowanceTypeAndPerformance: ["CASE"],
        allowanceToBeCreated: ["Both", "Warehouse Only"],
        label: "Warehouse Default Billing Information",
        offerTypeKey: "WAREHOUSE",
        allowanceScreenTypes: ["DP", "AO"],
        create: {
          isEdit: false,
          isView: false,
          label: "Save & Continue",
        },
        edit: {
          isEdit: true,
          isView: false,
          label: "Update & Continue",
        },
      },
    },
    headerButton: "New",
    nextLevel: true,
    cardLayout: true,
    cardFieldProp: "offerAllowances",
    cardFieldSubProp: "allowances",
    nextLevelLabel: "Skip to Promotion Details",
    allAnotherItem: "Add Another Allowance",
    addAnotherOfferAllowance: "Add Another Offer & Allowance",
    saveAndContinue: "Save & Continue",
    saveAndCreateAllowance: "Save & Create Allowance",
    permission: "PROMOTION_ALLOWANCE_MGMT_EDIT",
    module: "ALLOWANCE",
    previewModuleKey: "allowance",
  };
  const eventDetailsData = {
    name: "Annies Homegrown Snacks Box - 84882 - 27 Week 05 Insert 2025",
    startDate: "2025-01-29",
    endDate: "2025-02-04",
    divisionIds: ["27"],
    divisions: [],
    planProductGroups: [
      {
        planProductGroupId: "636bde8d9665d0440e006df8",
        sourceProductGroupId: 184779,
        productGroupType: "PPG",
        name: "Annies Homegrown Snacks Box - 84882",
        divisionId: "27",
        smicGroupCode: 2,
        smicCategoryCode: "0210",
        supplier: {
          supplierId: "515",
          supplierName: "GENERAL MILLS",
        },
        itemCount: 11,
        displayInd: false,
        simsVendors: ["000895", "006446"],
        simsSubAccntVendors: ["000895-028", "006446-001", "006446-013"],
        negotiationSimsVendors: ["000895", "006446"],
        unitType: 1,
        smicDescription: "CRACKERS",
        sect: "338",
        sectName: "SNACKS",
        dept: "301",
        deptName: "GROCERY",
        dsM: "JONATHAN HUNTER",
        cicCount: 389,
        cigCount: 53,
        dvp: "DENNIS SCHWARZ",
        dasm: "NICK WELLIVER",
        nvp: "",
        ncd: "",
        nacm: "",
      },
    ],
    storeGroups: [
      {
        storeGroupId: "6614614a5996dcc293a6b7bd",
        sourceStoreGroupId: "EDM",
        storeGroupName: "Seattle All Stores",
        storeGroupType: {
          groupType: "S",
          storeGrpTypeName: "Division",
          groupInd: "D",
        },
        divisionIds: ["27"],
        storeCount: 221,
        storeGrpNbr: 1713327680,
      },
    ],
    storeGroupType: "",
    allowance: [],
    isEventChanged: false,
    id: "6750115173c4fe063cf85eea",
    forecast: {
      forecastSales: 0,
      forecastUnits: 0,
      quadrant: "",
    },
    eventCreationVehicle: {
      vehicleId: "66f1eded1d792055ff84c75e",
      vehicleNm: "27 Week 05 Insert 2025",
      sourceVehicleSk: 64746,
      startDate: "2025-01-29",
      endDate: "2025-02-04",
      vehicleType: {
        vehicleTypeId: "66a3f061900b0b47a182376f",
        sourceVehicleTypeSk: 198,
        vehicleTypNm: "insrt",
        vehicleTypDesc: "Weekly Insert",
      },
    },
    planProductGroupPricing: {
      planProductGroup: {
        planProductGroupId: "",
        sourceProductGroupId: "",
        name: "",
        divisionId: "",
        smicGroupCode: "",
        smicCategoryCode: "",
        supplier: {
          id: "",
          supplierId: "",
          supplierName: "",
        },
      },
      quadrant: "",
      priceAmount: "",
      priceFactor: "",
      priceUnit: "",
      priceLimitQty: "",
      forecastAmt: "",
      userName: "",
      supplier: "",
    },
    startWeekVehicle: "27 Week 05 Insert 2025",
    planStoreGroupType: {
      groupType: "S",
      storeGrpTypeName: "Division",
      groupInd: "D",
    },
    vehicleType: "Weekly Insert",
    eventStatus: "Draft",
    eventType: "DP",
    inValidPromotions: [],
    inValidAllowances: [],
    isChangeEventTypeVisible: false,
    planEventIdNbr: 10030229,
    sourceEventType: "ECP",
    pricing: [
      {
        planProductGroup: {
          planProductGroupId: "636bde8d9665d0440e006df8",
          sourceProductGroupId: 184779,
          productGroupType: "PPG",
          name: "Annies Homegrown Snacks Box - 84882",
          divisionId: "27",
          smicGroupCode: 2,
          smicCategoryCode: "0210",
          supplier: {
            supplierId: "515",
            supplierName: "GENERAL MILLS",
          },
          itemCount: 11,
          displayInd: false,
          simsVendors: ["000895", "006446"],
          simsSubAccntVendors: ["000895-028", "006446-001", "006446-013"],
          negotiationSimsVendors: ["000895", "006446"],
          unitType: 1,
          smicDescription: "CRACKERS",
          sect: "338",
          sectName: "SNACKS",
          dept: "301",
          deptName: "GROCERY",
          dsM: "JONATHAN HUNTER",
          cicCount: 389,
          cigCount: 53,
          dvp: "DENNIS SCHWARZ",
          dasm: "NICK WELLIVER",
          nvp: "",
          ncd: "",
          nacm: "",
        },
        quadrant: "",
        priceAmount: "",
        priceFactor: "",
        priceUnit: "",
        priceLimitQty: "",
        prcMtd: "",
        promoType: "",
      },
    ],
    offerAllowances: [],
    promotionsList: [],
    createUser: {
      userId: "PJAIN03",
      name: "Prayas Jain (Contractor)",
      type: "Merchant",
      userRoles: ["az-meupp-nonprod-promointeditor"],
      createTs: "2024-12-04T08:22:41.073Z",
    },
    updateUser: {
      userId: "PJAIN03",
      name: "Prayas Jain (Contractor)",
      type: "Merchant",
      userRoles: ["az-meupp-nonprod-promointeditor"],
      createTs: "2024-12-04T08:22:41.073Z",
    },
    planEventWorkFlowType: "NOT FOUND",
    simsVendors: ["000895", "006446"],
    manufacturerSimsVendors: [],
    negotiationSimsVendors: ["000895", "006446"],
    eventNegotiationVendor: {
      source: "NONE",
      vendorIds: [],
      eventNegotiationUsers: {
        merchantUser: {
          userId: "PJAIN03",
          name: "Prayas Jain (Contractor)",
          type: "Merchant",
          userRoles: ["az-meupp-nonprod-promointeditor"],
          createTs: "2024-12-04T08:22:41.079Z",
        },
        vendorUserContacts: [],
      },
      userVendorNumbers: [],
    },
    simsVendorList: [
      {
        id: "64dbf5a323a9672237a83d39",
        supplierId: "000895",
        supplierName: "MONDELEZ GLOBAL LLC DSD USE ONLY",
        vendorNumSubAccount: "000895-028",
        vendorSubAccountNumber: "028",
        vendorSubAccountName: "MONDELEZ GLOBAL LLC                     ",
      },
      {
        id: "64dbf5a723a9672237a86a2e",
        supplierId: "006446",
        supplierName: "KEHE DISTRIBUTORS",
        vendorNumSubAccount: "006446-001",
        vendorSubAccountNumber: "001",
        vendorSubAccountName: "KEHE DISTRIBUTORS                       ",
      },
      {
        id: "64dd0e2b2db5059ed7715d7e",
        supplierId: "006446",
        supplierName: "KEHE DISTRIBUTORS",
        vendorNumSubAccount: "006446-013",
        vendorSubAccountNumber: "013",
        vendorSubAccountName: "KEHE DISTRIBUTORS {JBG WHLSALE}         ",
      },
    ],
    planEventTasks: [],
    submitForOthersInd: false,
    otherDetailsChangedInd: false,
    eventTypeEnum: "DP",
    pidDetailsEventInd: false,
    promotionsLists: [],
    promoProductGroup: "Annies Homegrown Snacks Box - 84882",
    storeGroupName: "Seattle All Stores",
  };
  const mockAllowTypePerfData = {
    getAllowancePerformanceTypes: [
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b81a",
        allowanceType: "Case",
        performance: "Off Invoice (01)",
        eventTypes: ["AO", "MD", "DP"],
        allowanceCd: "C",
        perfCode1: "01",
        perfCode2: "01",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "CB",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: "63a3a12743a6cee87995b81b",
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b81b",
        allowanceType: "Case",
        performance: "DSD Off Invoice (01)",
        eventTypes: ["DP", "AO", "MD"],
        allowanceCd: "C",
        perfCode1: "51",
        perfCode2: "01",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "CD",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b81c",
        allowanceType: "Case",
        performance: "Billback Liquor (85)",
        eventTypes: ["DP", "MD"],
        allowanceCd: "C",
        perfCode1: "48",
        perfCode2: "85",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "CB",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: "63a3a12743a6cee87995b81b",
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b81d",
        allowanceType: "Case",
        performance: "Price / Ad / Display (88)",
        eventTypes: ["DP", "MD"],
        allowanceCd: "C",
        perfCode1: "48",
        perfCode2: "88",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "CB",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: "63a3a12743a6cee87995b81b",
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b81e",
        allowanceType: "Case",
        performance: "Deduct (01)",
        eventTypes: ["AO", "MD", "DP"],
        allowanceCd: "C",
        perfCode1: "01",
        perfCode2: "01",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "CB",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: "63a3a12743a6cee87995b81b",
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b81f",
        allowanceType: "Header Flat",
        performance: "Contract Allow (03)",
        eventTypes: ["AO", "MD"],
        allowanceCd: "A",
        perfCode1: "03",
        perfCode2: " ",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "LC",
          allowOnlyOverrideStoreGroupsInd: true,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b820",
        allowanceType: "Header Flat",
        performance: "Logistics (08)",
        eventTypes: ["AO"],
        allowanceCd: "A",
        perfCode1: "08",
        perfCode2: " ",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "LW",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b821",
        allowanceType: "Header Flat",
        performance: "Swell Allowance (14)",
        eventTypes: ["AO"],
        allowanceCd: "A",
        perfCode1: "14",
        perfCode2: " ",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "LW",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b822",
        allowanceType: "Header Flat",
        performance: "Coupon/Complex (20)",
        eventTypes: ["DP", "MD"],
        allowanceCd: "A",
        perfCode1: "20",
        perfCode2: " ",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "LC",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b823",
        allowanceType: "Header Flat",
        performance: "ABS Foundation (29)",
        eventTypes: ["AO", "MD"],
        allowanceCd: "A",
        perfCode1: "29",
        perfCode2: " ",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "LC",
          allowOnlyOverrideStoreGroupsInd: true,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b825",
        allowanceType: "Header Flat",
        performance: "BBS/4U Tags (32)",
        eventTypes: ["DP", "MD"],
        allowanceCd: "A",
        perfCode1: "32",
        perfCode2: " ",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "LC",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b827",
        allowanceType: "Header Flat",
        performance: "Boise/Corp Contract (70)",
        eventTypes: ["AO", "MD"],
        allowanceCd: "A",
        perfCode1: "70",
        perfCode2: " ",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "LC",
          allowOnlyOverrideStoreGroupsInd: true,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b824",
        allowanceType: "Header Flat",
        performance: "Store Specific (30)",
        eventTypes: ["DP"],
        allowanceCd: "A",
        perfCode1: "30",
        perfCode2: " ",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "LC",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: null,
        },
        deleteInd: "Y",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b826",
        allowanceType: "Header Flat",
        performance: "4U Event (52)",
        eventTypes: ["DP", "MD"],
        allowanceCd: "A",
        perfCode1: "52",
        perfCode2: " ",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "LC",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b828",
        allowanceType: "Header Flat",
        performance: "Fuel Rewards (77)",
        eventTypes: ["DP"],
        allowanceCd: "A",
        perfCode1: "77",
        perfCode2: " ",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "LC",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b829",
        allowanceType: "Header Flat",
        performance: "Price / Ad / Display (88)",
        eventTypes: ["DP", "MD"],
        allowanceCd: "A",
        perfCode1: "88",
        perfCode2: " ",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "LC",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b82a",
        allowanceType: "Header Flat",
        performance: "Special Major Events (92)",
        eventTypes: ["AO", "MD"],
        allowanceCd: "A",
        perfCode1: "92",
        perfCode2: " ",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "LC",
          allowOnlyOverrideStoreGroupsInd: true,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b82b",
        allowanceType: "Header Flat",
        performance: "Other (99)",
        eventTypes: ["DP", "AO", "MD"],
        allowanceCd: "A",
        perfCode1: "99",
        perfCode2: " ",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "LC",
          allowOnlyOverrideStoreGroupsInd: true,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b82c",
        allowanceType: "Item Flat",
        performance: "Placement (04)",
        eventTypes: ["AO"],
        allowanceCd: "A",
        perfCode1: " ",
        perfCode2: "04",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "AC",
          allowOnlyOverrideStoreGroupsInd: true,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b82d",
        allowanceType: "Item Flat",
        performance: "Price / Ad / Display (88)",
        eventTypes: ["DP", "MD"],
        allowanceCd: "A",
        perfCode1: " ",
        perfCode2: "88",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "AC",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b830",
        allowanceType: "Item Flat",
        performance: "Other (99)",
        eventTypes: ["DP", "AO", "MD"],
        allowanceCd: "A",
        perfCode1: " ",
        perfCode2: "99",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "AC",
          allowOnlyOverrideStoreGroupsInd: true,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b82e",
        allowanceType: "Item Flat",
        performance: "Warehouse Logistics (08)",
        eventTypes: ["AO"],
        allowanceCd: "A",
        perfCode1: " ",
        perfCode2: "08",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "AW",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b82f",
        allowanceType: "Item Flat",
        performance: "Warehouse Price Protection (96)",
        eventTypes: ["AO"],
        allowanceCd: "A",
        perfCode1: " ",
        perfCode2: "96",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "AW",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b831",
        allowanceType: "Scan",
        performance: "Non-Performance (01)",
        eventTypes: ["AO", "DP"],
        allowanceCd: "T",
        perfCode1: "05",
        perfCode2: "01",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "TC",
          allowOnlyOverrideStoreGroupsInd: true,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b833",
        allowanceType: "Scan",
        performance: "Liquor Only (38)",
        eventTypes: ["DP"],
        allowanceCd: "T",
        perfCode1: "38",
        perfCode2: "88",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "TC",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b834",
        allowanceType: "Scan",
        performance: "4U Event (52)",
        eventTypes: ["DP", "MD"],
        allowanceCd: "T",
        perfCode1: "20",
        perfCode2: "52",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "TC",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b832",
        allowanceType: "Scan",
        performance: "Price / Ad / Display (88)",
        eventTypes: ["DP", "MD"],
        allowanceCd: "T",
        perfCode1: "05",
        perfCode2: "88",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "TC",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b835",
        allowanceType: "Scan",
        performance: "Coupon/OMS (75)",
        eventTypes: ["DP"],
        allowanceCd: "T",
        perfCode1: "20",
        perfCode2: "75",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "TC",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b836",
        allowanceType: "Scan",
        performance: "Fuel Rewards (77)",
        eventTypes: ["DP"],
        allowanceCd: "T",
        perfCode1: "20",
        perfCode2: "77",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "TC",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b837",
        allowanceType: "Ship To Store",
        performance: "Non-Performance (01)",
        eventTypes: ["AO", "DP"],
        allowanceCd: "S",
        perfCode1: "06",
        perfCode2: "01",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "SB",
          allowOnlyOverrideStoreGroupsInd: true,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "63a3a12743a6cee87995b838",
        allowanceType: "Ship To Store",
        performance: "Price / Ad / Display (88)",
        eventTypes: ["DP", "MD"],
        allowanceCd: "S",
        perfCode1: "06",
        perfCode2: "88",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "SB",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
      {
        __typename: "AllowancePerformances",
        id: "64c0357572d0b2e265f2357c",
        allowanceType: "Scan",
        performance: "Discontinued (66)",
        eventTypes: ["DP"],
        allowanceCd: "T",
        perfCode1: "05",
        perfCode2: "66",
        performanceConfig: {
          __typename: "PerformanceConfig",
          defaultCreateInd: "TC",
          allowOnlyOverrideStoreGroupsInd: false,
          allowancePerformanceChildId: null,
        },
        deleteInd: "",
      },
    ],
    performanceConfig: {
      "offerAllowances[0].allowances[0].edit": {
        defaultCreateInd: "TC",
      },
    },
  };
    class ResizeObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
  }
  beforeEach(() => {
    window.ResizeObserver = ResizeObserver as any;

  });
  afterEach(() => {
    jest.clearAllMocks();
  });

  xit("should render without crashing", () => {
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      switch (sel_name) {
        case "allowance_temp_work":
          return {
            data: { allowanceType: "default", allowanceTypeSpecification: {}},
          };
        case "allowance_form_data":
          return { data: { allowanceFormData: {} } };
        case "allowance_stepper_additional_details":
          return { skipStep: {} };
        case "offer_card_configutation_rn":
          return { data: { editCardConfig: {} } };
        case "check_event_vehicledates_rn": {
          return {
            data: {
              isEventVehicleChangedInPending: false,
              isEventVehicleChangedInHistory: false,
            },
          };
        }
        case "apiError_rn":
          return {
            data: {
              apiErrorsMsgs: [],
              showTokenError: false,
            },
          };
        case "event_details_data": {
          return {
            data: eventDetailsData,
          };
        }
        default:
          return {};
      }
    });
    render(
      <Wrapper>
        <AllowanceCardContent
          cardIndex={0}
          cardItemIndex={0}
          isNew={true}
          route={{}}
          cardConfiguration={cardConfiguration}
          getValues={jest.fn().mockReturnValue({})}
        />
      </Wrapper>
    );
    screen.debug();
    expect(
      screen.queryByText("Save Event Details & Add Allowance")
    ).not.toBeInTheDocument();
    expect(
      screen.getByText("Allowance Type & Performance")
    ).toBeInTheDocument();
    expect(screen.getByText("Bypass Allowance")).toBeInTheDocument();
  });

  xit('should trigger the handleByPassAllowanceClick function when "Bypass Allowance" is clicked', async () => {
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      switch (sel_name) {
        case "allowance_temp_work":
          return {
            data: { allowanceType: "default", allowanceTypeSpecification: {}},
          };
        case "allowance_form_data":
          return { data: {} };
        case "offer_card_configutation_rn":
          return { data: { editCardConfig: {} } };
        case "check_event_vehicledates_rn": {
          return {
            data: {
              isEventVehicleChangedInPending: false,
              isEventVehicleChangedInHistory: false,
            },
          };
        }
        case "apiError_rn":
          return {
            data: {
              apiErrorsMsgs: [],
              showTokenError: false,
            },
          };
                  case "event_details_data": {
          return {
            data: eventDetailsData,
          };
        }
        default:
          return {};
      }
    });
    jest.mock("../../../service/apis/allowance-api", () => ({
      ...jest.requireActual("../../../service/apis/allowance-api"),
      useGetAllowanceTempWorkDataQuery: jest.fn().mockReturnValue({
        data: mockTempWorkFromGetApi,
        isFetching: false,
      }),
    }));

    render(
      <Wrapper>
        <AllowanceCardContent
          cardIndex={0}
          cardItemIndex={0}
          isNew={true}
          route={{}}
          cardConfiguration={cardConfiguration}
          getValues={jest.fn().mockReturnValue({})}
        />
      </Wrapper>
    );
    screen.debug();
    // Click the "Bypass Allowance" button
    userEvent.click(screen.getByText("Bypass Allowance"));

    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalled();
    });
  });

  it("Handle edit allowance", async () => {

    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      switch (sel_name) {
        case "allowance_temp_work":
          return {
            data: {
              allowanceData: mockTempWorkFromGetApi,
              isNewAllowance: false,
            },
          };
        case "allowance_form_data":
          return { data: {} };
        case "offer_card_configutation_rn":
          return { data: { editCardConfig: { 0: true } } };
        case "check_event_vehicledates_rn": {
          return {
            data: {
              isEventVehicleChangedInPending: false,
              isEventVehicleChangedInHistory: false,
            },
          };
        }
        case "allowance_type_performance_data": {
          return {
            data: mockAllowTypePerfData,
          };
        }
      case "apiError_rn":
          return {
            data: {
              apiErrorsMsgs: [],
              showTokenError: false,
            },
          };
        case "event_details_data": {
          return {
            data: eventDetailsData,
          };
        }
        default:
          return {};
      }
    });

    render(
      <Wrapper>
        <AllowanceCardContent
          cardIndex={0}
          cardItemIndex={0}
          isNew={false}
          route={{}}
          cardConfiguration={cardConfiguration}
          getValues={jest.fn().mockReturnValue({})}
        />
      </Wrapper>
    );
    screen.debug();
    expect(screen.queryByText("Bypass Allowance")).not.toBeInTheDocument();
  });
});
