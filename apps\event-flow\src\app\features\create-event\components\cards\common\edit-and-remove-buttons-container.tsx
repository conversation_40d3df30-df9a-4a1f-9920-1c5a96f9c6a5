import {
  Edit2,
  Trash2 as Trash,
  XCircle as CancelIcon,
  Ban as BanI<PERSON>,
} from "lucide-react";
import { EEVENT_STATUS } from "../../../../event-types/components/event-action/event-action.model";

const EditAndRemoveButtons = ({
  onEdit,
  onRemove,
  onReject,
  onCancel,
  disabled,
  isAddAnotherOffer = false,
  offerAllowanceStatus,
  isEdit = false,
  isEditDisabled = false,
}) => {
  const isDisabled =
    Object.values(disabled).includes(true) || isAddAnotherOffer;
  return (
    <div
      id="abs-edit-and-remove-buttons-container1"
      className={`min-w-fit ml-2 ${
        isDisabled ? "opacity-40 pointer-events-none" : ""
      } ${offerAllowanceStatus === EEVENT_STATUS?.REJECTED ? "hidden" : ""}`}
    >
      <span
        className={`mr-2 font-semibold text-[#1B6EBB] ${
          isEdit ? "opacity-40 pointer-events-none" : ""
        } ${isEditDisabled ? "opacity-40 pointer-events-none" : ""}`}
        onClick={onEdit}
      >
        <Edit2
          width={16}
          height={16}
          color="#1B6EBB"
          className="inline-block mb-[3px] mr-[8px]"
        />
        Edit
      </span>
      {[EEVENT_STATUS.DRAFT].includes(offerAllowanceStatus) && (
        <span className="mr-2  font-semibold text-[#1B6EBB]" onClick={onRemove}>
          <Trash
            width={16}
            height={16}
            color="#1B6EBB"
            className="inline-block mb-[3px] mr-[8px]"
          />
          Remove
        </span>
      )}
      {[
        EEVENT_STATUS.PENDING_WITH_VENDOR,
        EEVENT_STATUS.PENDING_WITH_MERCHANT,
      ].includes(offerAllowanceStatus) && (
        <span className="mr-5  font-semibold text-[#1B6EBB]" onClick={onReject}>
          <BanIcon
            width={16}
            height={16}
            color="#1B6EBB"
            className="inline-block mb-[3px] mr-[8px]"
          />
          Reject
        </span>
      )}
      {[
        EEVENT_STATUS.AGREED,
        EEVENT_STATUS.READY,
        EEVENT_STATUS.ACTIVE,
        EEVENT_STATUS.EXECUTED,
        EEVENT_STATUS.AGREED_PENDING,
      ].includes(offerAllowanceStatus) && (
        <span className="mr-3  font-semibold text-[#1B6EBB]" onClick={onCancel}>
          <CancelIcon
            width={16}
            height={16}
            color="#1B6EBB"
            className="inline-block mb-[3px] mr-[8px]"
          />
          Cancel
        </span>
      )}
    </div>
  );
};

export default EditAndRemoveButtons;
