import { useState, useEffect } from "react";
import {
  checkIfExternal,
  getDefaultBillingModalState,
  isNationalType,
  tableDataToVendorsArray,
} from "../../all-allowances/allowance-lead-distributors/billing-selection-utils";
import { useSelectorWrap } from "@me/data-rtk";
import { useDispatch } from "react-redux";
import { setDivisionWiseBillingSectionData } from "../service/slice/lead-distributors-slice";

export default function useBillingSelection({
  vendors,
  currentDivId = "",
  isNdpType = false,
}) {
  const dispatch = useDispatch();
  const {
    data: { tableData, allDivisionsTableData = [] },
  } = useSelectorWrap("allowanceTableData_rn");
  const { data: divisionWiseBillingData } = useSelectorWrap("division_wise_billing_selection_data_rn");
  const { data: excludedVendorData } =
    useSelectorWrap("excludedVendorForAllowance_rn") || {};
  const isNational = isNationalType(isNdpType);
  const currentDivTableData = allDivisionsTableData?.find(
    e => e?.divisionId === currentDivId
  );

  const vendorWithDetails = tableDataToVendorsArray(
    isNational ? (currentDivTableData?.tableData || []) : tableData
  );
  const excludedVendForDiv =
    excludedVendorData?.divisionWiseExcludedVendors?.[currentDivId] ?? {};
  const excludeDataToCheck = isNational
    ? excludedVendForDiv
    : excludedVendorData;

  const mainVendors: string[] = vendors
    ?.filter(item => {
      const vendor = vendorWithDetails?.find(
        v => v?.vendorNbr === item?.vendorNbr
      );
      return !checkIfExternal(vendor);
    })
    ?.filter(
      vend => !excludeDataToCheck?.excludedVendors?.includes(vend?.vendorNbr)
    )
    ?.map(item => item?.vendorNbr);
  const otherVendors: string[] = vendors
    ?.filter(item => {
      const vendor = vendorWithDetails?.find(
        v => v?.vendorNbr === item.vendorNbr
      );
      return checkIfExternal(vendor);
    })
    ?.filter(
      vend => !excludeDataToCheck?.excludedVendors?.includes(vend?.vendorNbr)
    )
    ?.map(item => item?.vendorNbr);

  const [childParentMap, setChildParentMap] = useState({});

  useEffect(() => {
    setChildParentMap(
      getDefaultBillingModalState({
        mainVendors,
        otherVendors,
        tableData: isNational ? currentDivTableData?.tableData : tableData,
        allDivisionsTableData: isNational ? allDivisionsTableData : [],
      })
    );
  }, [vendors]);

  function moveUp(childNbr) {
    const parentNbr = childParentMap[childNbr];
    const parentIndex = mainVendors?.findIndex(item => item === parentNbr);
    const prevParent = mainVendors?.[parentIndex - 1];

    setChildParentMap(curr => ({
      ...curr,
      [childNbr]: prevParent,
    }));
  }

  function moveDown(childNbr) {
    const parentNbr = childParentMap?.[childNbr];
    const parentIndex = mainVendors?.findIndex(item => item === parentNbr);
    const nextParent = mainVendors?.[parentIndex + 1];

    setChildParentMap(curr => ({
      ...curr,
      [childNbr]: nextParent,
    }));
  }
  useEffect(() => {
    if (!isNational || !currentDivId) return;
    dispatch(
      setDivisionWiseBillingSectionData({
        ...divisionWiseBillingData,
        [currentDivId]: {
          stepData: mainVendors?.map(item => {
            return {
              id: item,
              child: otherVendors?.filter(
                vNbr => childParentMap?.[vNbr] === item
              ),
            };
          }),
        },
      })
    );
  }, [childParentMap]);

  const mapping = mainVendors?.map(item => {
    return {
      id: item,
      child: otherVendors?.filter(vNbr => childParentMap?.[vNbr] === item),
    };
  });

  return { moveDown, moveUp, mapping, mainVendors };
}
