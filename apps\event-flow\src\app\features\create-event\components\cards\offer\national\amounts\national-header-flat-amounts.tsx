import { useState } from "react";
import { ChevronDown } from "lucide-react";
import { InputTextAtom } from "../../../../fields/index";
import { validateAmount } from "../../../../../hooks/allowance-amount-validations";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";

// Define an interface for the division errors object
interface DivisionErrors {
  [key: string]: string;
}

const NationalHeaderFlatAmounts = ({
  register,
  control,
  setValue,
  trigger,
  allowanceAmountField,
  onChange,
  divisionIds,
  hfAmountChangeKey = "",
  onSectionUpdate,
}) => {
  const [isEditNationalAllowanceAmounts, setIsNationalAllowanceAmounts] =
    useState(true);
  const [divisionErrors, setDivisionErrors] = useState<DivisionErrors>({});

  const onDivisionAmonutChange = (
    value: any,
    isHeader = false,
    registerKey = ""
  ) => {
    setValue(hfAmountChangeKey, true);
    onSectionUpdate?.();
    const amount = validateAmount(value);
    const isZeroAmount = amount && Number(amount) === 0;

    if (isHeader) {
      divisionIds?.forEach((division, index) => {
        const fieldKey = getUpdatedFieldProps(allowanceAmountField, index);
        setValue(fieldKey.registerField, value);
      });

      const newErrors: DivisionErrors = {};
      divisionIds?.forEach((_, index) => {
        newErrors[index.toString()] = isZeroAmount
          ? allowanceAmountField?.errors?.digitError?.text
          : "";
      });
      setDivisionErrors(newErrors);
    } else {
      setValue(registerKey, value);
      const fieldParts = registerKey?.split(".");
      const divIndex = fieldParts?.[fieldParts?.length - 1];
      if (divIndex) {
        setDivisionErrors(prev => ({
          ...prev,
          [divIndex]: isZeroAmount
            ? allowanceAmountField?.errors?.digitError?.text
            : "",
        }));
      }
    }
    trigger?.();
  };

  const getUpdatedFieldProps = (
    fieldProps: any,
    index: number,
    isHeader = false
  ) => {
    return {
      ...fieldProps,
      required: isHeader ? false : fieldProps?.required,
      registerField: `${
        isHeader ? "hfHeader" : fieldProps.registerField
      }.${index}`,
    };
  };

  const renderHfAmountsInputField = record => {
    const { index, isHeader = false } = record;
    const updatedRegisterField = getUpdatedFieldProps(
      allowanceAmountField,
      index,
      isHeader
    );

    const errorKey = isHeader ? "header" : index.toString();
    const hasError = divisionErrors?.[errorKey] ? true : false;

    return (
      <InputTextAtom
        fieldProps={updatedRegisterField}
        key={updatedRegisterField?.registerField}
        defaultValue="$"
        className={`w-full max-w-[30rem]`}
        register={register}
        control={control}
        onWheel={event => event.currentTarget.blur()}
        onChange={value => {
          onDivisionAmonutChange(
            value,
            isHeader,
            updatedRegisterField?.registerField
          );
        }}
        onFocus={(value: string) => {
          setValue(updatedRegisterField.registerField, value);
        }}
        prefixValue={"$"}
        showLabel={false}
        isCustomValidationEnabled={true}
        customValidation={{
          value: hasError,
          message: divisionErrors?.[errorKey],
        }}
      />
    );
  };

  const renderHfHeaderAmountField = () => {
    return renderHfAmountsInputField({
      index: 0,
      isHeader: true,
    });
  };

  return (
    <>
      <div
        onClick={() => setIsNationalAllowanceAmounts(state => !state)}
        className="cursor-pointer flex gap-1 py-4 text-[#1B6EBB]"
      >
        <div>Edit Divison Allowance Amount</div>
        <ChevronDown
          className={`transform transition-transform duration-300 ease-in-out ${
            isEditNationalAllowanceAmounts ? "rotate-180" : "rotate-0"
          }`}
          color="#1B6EBB"
        />
      </div>
      {isEditNationalAllowanceAmounts && <div className="bg-blue-306 flex items-center text-[#1b6ebb] w-fit mb-3">
        <span>{efConstants.AMOUNTS_POWERLINE_TEXT}</span>
      </div>}
      <div>
        {isEditNationalAllowanceAmounts && control ? (
          <div className="allowance-details-container">
            <div className="flex w-full border-b-[1px] border-[#C8DAEB] bg-[#F1F4F9] p-2">
              <div className="w-[15%] pb-10 flex items-center">Division</div>
              <div className="w-[20%]">
                <div>Allowance Amount</div>
                {
                  <div style={{ background: "white" }} className="w-[96%]">
                    {renderHfHeaderAmountField()}
                  </div>
                }
              </div>
            </div>

            {divisionIds?.map((division, index) => {
              return (
                <div
                  key={index}
                  className="flex w-full border-b-[1px] border-[#C8DAEB]"
                >
                  <div className="w-[15%] py-0.5 px-2 flex items-center">
                    {division}
                  </div>
                  <div className="w-[20%] py-0.5 px-2">
                    {renderHfAmountsInputField({
                      index,
                      isHeader: false,
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        ) : null}
      </div>
    </>
  );
};

export default NationalHeaderFlatAmounts;
