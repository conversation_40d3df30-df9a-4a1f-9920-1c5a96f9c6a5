import { useSelectorWrap } from "@me/data-rtk";
import { useDispatch } from "react-redux";
import { getAllowanceMapKey } from "../service/allowance/allowance-service";
import { allowanceTempWorkHandler } from "../service/slice/allowance-temp-work-slice";
import { IAllowancePerformaceProps } from "../components/cards/allowance/stepper/allowance-type-performance/allowance-type-performance.model";
import { getObjectKeys } from "../service/allowance/allowance-stepper-service";
import { usePostAllowanceTempWorkDataMutation } from "../service/apis/allowance-api";

export default function useAllowanceTempUpdate() {
  const dispatch = useDispatch();
  const { allowanceData: allowanceTempWorkInfo } =
    useSelectorWrap("allowance_temp_work")?.data || {};
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");

  const [postAllowanceTempWork, { isSuccess, isError, error, isLoading }] =
    usePostAllowanceTempWorkDataMutation();

  const allowName =
    getAllowanceMapKey(allowanceTempWorkInfo?.allowanceType) || "";

  async function saveTempWorkData(allowanceTempData, isFormSubmitted = false) {
    try {
      let updatedAllowanceData = {
        ...allowanceTempData,
        URL_PARAM: eventDetailsData?.id,
        queryParams: {},
      };

      if (!isFormSubmitted) {
        if (allowanceTempData?.planEventId !== eventDetailsData?.id) {
          updatedAllowanceData = {
            ...updatedAllowanceData,
            planEventId: eventDetailsData?.id,
            tempWorkAllowanceId: null,
          };
        }
        const updatedTempData = await postAllowanceTempWork(
          updatedAllowanceData
        );
        !updatedTempData?.error &&
          dispatch(
            allowanceTempWorkHandler({
              allowanceData: updatedTempData?.data || {},
              isTempLoaded: true,
            })
          );
        return updatedTempData;
      } else if (isFormSubmitted) return updatedAllowanceData;
      return {};
    } catch (e) {
      console.error(e);
      return {};
    }
  }

  function checkTempDefaultDataAvailablity() {
    //Check Temp has vaild data
    if (
      allowanceTempWorkInfo === undefined ||
      !getObjectKeys(allowanceTempWorkInfo)?.length
    ) {
      return;
    }
  }

  async function addAllowanceTypeAndPerformanceId({
    isEditEnable = false,
    allowanceType,
    allowancePerformances,
    createInd,
    allowancesMap,
    overrideHeaderFlatAmt,
    productSources,
  }: {
    isEditEnable: boolean;
    allowanceType: string;
    allowancePerformances: IAllowancePerformaceProps;
    createInd?: string;
    allowancesMap?: object;
    overrideHeaderFlatAmt?: number;
    productSources?: string[] | [];
  }) {
    checkTempDefaultDataAvailablity();
    const allowanceTypeTemp = allowanceTempWorkInfo?.allowanceType,
      allowanceTypeKey = allowanceType || allowanceTypeTemp,
      allowKey = getAllowanceMapKey(allowanceTypeKey) || "";
    return await saveTempWorkData({
      ...allowanceTempWorkInfo,
      allowanceType: allowanceTypeKey,
      allowancePerformanceId: allowancePerformances?.id,
      allowanceTypeSpecification: {
        [allowKey]: {
          ...(isEditEnable &&
            allowanceTempWorkInfo?.allowanceTypeSpecification?.[allowKey]),
          allowanceType: allowanceTypeKey,
          allowancePerformanceId: allowancePerformances?.id,
          allowancePerformances,
          createInd,
          allowancesMap,
          overrideHeaderFlatAmt,
          productSources,
        },
      },
    });
  }

  async function addAllowancesToBeCreated({
    offerAllowanceGroup,
    createIndex,
    productSources,
    offerAllowancesGroupInfoMap = {},
    allowancePerformances = {},
    allowanceTempWrk = null,
  }) {
    const perfData = getObjectKeys(allowancePerformances).length
      ? {
          allowancePerformanceId: allowancePerformances?.["id"],
          allowancePerformances,
        }
      : {};
    const allowanceTempWorkData = allowanceTempWrk || allowanceTempWorkInfo;
    const allowName =
      getAllowanceMapKey(allowanceTempWorkData?.allowanceType) || "";
    if (
      allowanceTempWorkData === undefined ||
      !getObjectKeys(allowanceTempWorkData).length
    ) {
      return {};
    }
    return await saveTempWorkData({
      ...allowanceTempWorkData,
      allowanceTypeSpecification: {
        [allowName]: {
          ...allowanceTempWorkData?.allowanceTypeSpecification?.[allowName],
          createInd: createIndex,
          allowancesMap: offerAllowanceGroup,
          productSources,
          offerAllowancesGroupInfoMap,
          ...perfData,
        },
      },
    });
  }

  const setAllowancesInfo = (
    allowances,
    offerAllowanceGroup,
    isDatesStep = false,
    vehicleDatesMap = null
  ) => {
    const { allowanceTypeSpecification: allowanceTypeSpecificationValue } =
      allowanceTempWorkInfo || {};

    const allowanceTypeSpecification = { ...allowanceTypeSpecificationValue };

    const allowancesMap = {
      ...allowanceTypeSpecification?.[allowName]?.allowancesMap,
      [offerAllowanceGroup]: [...(allowances || [])],
    };

    if (isDatesStep) return allowancesMap;

    allowanceTypeSpecification[allowName] = {
      ...allowanceTypeSpecification?.[allowName],
      allowancesMap,
      ...((vehicleDatesMap && { vehicleDatesMap }) || {}),
    };
    return allowanceTypeSpecification;
  };

  async function addAllowancesDates({
    vehicleDatesMap,
    datesArray,
    isAmountsIncluded = false,
    allowances = [],
    offerAllowanceGroup = "",
    isEditEnable = false,
  }) {
    checkTempDefaultDataAvailablity();

    let allowancesMap = null;
    if (isAmountsIncluded) {
      allowancesMap = setAllowancesInfo(allowances, offerAllowanceGroup, true);
    } else {
      const allowMapData =
        allowanceTempWorkInfo?.allowanceTypeSpecification?.[allowName]
          ?.allowancesMap;

      const defaultValuesForAllowance = {
        leadDistributorMode: null,
        leadDistributorInfos: [],
        leadDistributorInd: false,
        includeInd: true,
        createAllowInd: true,
      };

      const allowanesOffsetDates =
        allowMapData?.[offerAllowanceGroup]?.length && isEditEnable // Edit All
          ? allowMapData?.[offerAllowanceGroup]?.map((values, index) => {
              return {
                ...values,
                ...datesArray?.[index],
                vehicleId: vehicleDatesMap?.[offerAllowanceGroup]?.vehicleId,
                finalizedAmountsInd: isAmountsIncluded,
                allowanceBillingInfo: values?.allowanceBillingInfo,
              };
            })
          : datesArray?.length
          ? datesArray.map(values => {
              // Case Scan and S2S Create
              return {
                ...values,
                finalizedAmountsInd: isAmountsIncluded,
                ...defaultValuesForAllowance,
              };
            })
          : allowMapData?.[offerAllowanceGroup]?.map(values => {
              //  HF IF Create
              return {
                ...values,
                vehicleId: vehicleDatesMap?.[offerAllowanceGroup]?.vehicleId,
                finalizedAmountsInd: isAmountsIncluded,
                ...defaultValuesForAllowance,
              };
            });

      allowancesMap = {
        ...allowMapData,
        [offerAllowanceGroup]: allowanesOffsetDates,
      };
    }
    return await saveTempWorkData({
      ...allowanceTempWorkInfo,
      allowanceTypeSpecification: {
        [allowName]: {
          ...allowanceTempWorkInfo?.allowanceTypeSpecification?.[allowName],
          allowancesMap,
          vehicleDatesMap,
        },
      },
    });
  }

  async function addAllowancesAmounts({
    allowances,
    offerAllowanceGroup,
    vehicleDatesMap = null,
  }) {
    checkTempDefaultDataAvailablity();
    const allowanceTypeSpecification = setAllowancesInfo(
      allowances,
      offerAllowanceGroup,
      false,
      vehicleDatesMap
    );

    return await saveTempWorkData({
      ...allowanceTempWorkInfo,
      allowanceTypeSpecification,
    });
  }

  async function addAllowancesBillingInformation({
    createAllowInd,
    allowances,
    offerAllowanceGroup,
    isLastStep,
  }) {
    checkTempDefaultDataAvailablity();

    const allowanceTypeSpecification = setAllowancesInfo(
      allowances,
      offerAllowanceGroup
    );

    return await saveTempWorkData(
      {
        ...allowanceTempWorkInfo,
        allowanceTypeSpecification,
      },
      isLastStep
    );
  }

  async function addStoreSelectionInfo(overrideStoreGroups) {
    checkTempDefaultDataAvailablity();
    return await saveTempWorkData({
      ...allowanceTempWorkInfo,
      allowanceTypeSpecification: {
        [allowName]: {
          ...allowanceTempWorkInfo?.allowanceTypeSpecification?.[allowName],
          overrideStoreGroups,
        },
      },
    });
  }

  return {
    addAllowanceTypeAndPerformanceId,
    addStoreSelectionInfo,
    addAllowancesToBeCreated,
    addAllowancesDates,
    addAllowancesAmounts,
    addAllowancesBillingInformation,
    isSuccess,
    isError,
    error,
    isLoading,
  };
}
