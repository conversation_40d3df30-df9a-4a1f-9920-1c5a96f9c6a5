import { renderHook, act } from "@testing-library/react";
import { useHandleAmountFieldsChange } from "./useHandleAmountFieldsChange";

jest.mock("../../../../../hooks/allowance-amount-validations", () => ({
  __esModule: true,
  default: jest.fn(() => ({
    calcError: "",
    validateNewAmount: jest.fn(value => ({ amount: value })),
    clearError: jest.fn(),
  })),
  validateAmount: jest.fn(value => value),
}));

describe("useHandleAmountFieldsChange hook", () => {
  let defaultProps;

    beforeEach(() => {
        defaultProps = {
            allowancesResp: { summary: {}, allowances: [{ allowanceItems: [{ allowUomType: "KG" }] }] },
            allowanceType: "SomeType",
            formControls: { setValue: jest.fn() },
            initialValues: { uom: "KG" },
            setAllowanceAmountData: jest.fn(),
            allowanceAmountFields: {},
            setAllowanceAmountFields: jest.fn(),
            isHeaderFlat: false,
            isHfIfWhseCase: false,
            cardIndex: 0,
            cardItemIndex: 0,
            searchId: "",
            eventDetailsData: {},
            fieldProp: { registerField: "amount", errors: { digitError: { text: "Invalid amount" } } },
            isAmtSavedInTemp: false,
            isEditEnable: true,
        };
    });

  it("should initialize with default values", () => {
    const { result } = renderHook(() => useHandleAmountFieldsChange(defaultProps));
    expect(result.current.itemCount).toBe(0);
    expect(result.current.totalAmount).toBe(0);
    expect(result.current.wareHouseCount).toBe(0);
    expect(result.current.digitError).toBe("");
    expect(result.current.differentAmountFound).toBe(false);
  });

  it("should handle amount change", () => {
    const { result } = renderHook(() => useHandleAmountFieldsChange(defaultProps));

    act(() => {
      result.current.handleAmountChange("100");
    });

    expect(defaultProps.formControls.setValue).toHaveBeenCalledWith("amount", "100", { shouldDirty: true });
    expect(defaultProps.setAllowanceAmountData).toHaveBeenCalled();
  });

  it("should calculate final cost correctly", () => {
    const { result } = renderHook(() => useHandleAmountFieldsChange(defaultProps));

    const finalCost = result.current.calculateFinalCost(100);
    expect(finalCost).toBeDefined();
  });
});
