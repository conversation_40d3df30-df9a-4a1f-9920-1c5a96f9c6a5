import { Button } from "@albertsons/uds/molecule/Button";
import { InputText } from "../../../../fields/allowance-atoms";

export default function BillingCommentAllSection({
  formControls,
  commonComment,
  onUpdateAllCommentSubmit,
  isCommentRequired,
  isNationalEvent,
}) {
  const { control, register, setValue } = formControls;

  return (
    <div id="abs-billing-comment-all-section-input-container">
      <div className="flex gap-4">
        <InputText
          control={control}
          fieldProps={commonComment}
          onChange={e => {
            setValue(commonComment.registerField, e?.trimStart());
          }}
          placeHolder={commonComment.placeholder}
          register={register}
        />
        <Button
          onClick={onUpdateAllCommentSubmit}
          width={isNationalEvent ? 150 : 300}
          variant={isNationalEvent ? "primary" : "secondary"}
          type="button"
        >
          {isNationalEvent ? "Apply" : "Save Comment & Apply to All"}
        </Button>
      </div>

      {isCommentRequired && (
        <div
          id="abs-billing-comment-all-section-error"
          className="text-red-700 text-sm mt-2"
        >
          * Selected Performance = Other (99) - Vendor Comment is required.
        </div>
      )}
    </div>
  );
}

