import efConstants from "../../../../../shared/ef-constants/ef-constants";
import { ALLOWANCE_AMOUNT } from "../../../constants/fields/allowance/field-allowance-amount";
import { ALLOWANCE_DATES } from "../../../constants/fields/allowance/field-allowance-dates-section";
import { ALLOWANCE_TYPE_PERFORMANCE } from "../../../constants/fields/allowance/field-allowance-type-performance";
import {
  ALLOWANCE_TO_BE_CREATED,
  ALLOWANCE_TO_BE_CREATED_OPTIONS,
} from "../../../constants/fields/allowance/field-allowance-to-be-created-section";
import { BILLING_INFORMATION } from "../../../constants/fields/allowance/field-billing-information";
import { STORE_SELECTION } from "../../../constants/fields/allowance/field-store-selection";

const { ALLOWANCE_TYPES, ALLOWANCE_SCREEN_TYPES, PRODUCT_SOURCE_INFO } =
  efConstants;
const { CASE, SCAN, SHIPTOSTORE, ITEMFLAT, HEADERFLAT } = ALLOWANCE_TYPES;
const { DP, AO, NDP, NCDP } = ALLOWANCE_SCREEN_TYPES;
const { HEADER_FLAT } = PRODUCT_SOURCE_INFO;
const {
  CASE: CASE_OPTIONS,
  SCAN: SCAN_OPTIONS,
  SHIPTOSTORE: SHIPTOSTORE_OPTIONS,
} = ALLOWANCE_TO_BE_CREATED_OPTIONS;

export const AMT_DESCRIPTION_TEXT = {
  BASE_DESC: `All Costs on all Items are the same, you can enter one Allowance amount for all of the Items. Please resolve or Accept any Allowance Overlaps.`,
  HF_IF_BASE_DESC: `If Allowance Amount is the same for all items, you can enter one amount
    here. Click 'Edit / View All Items' to enter separate amounts for each item.`,
  HF_WHSE_DESC: `If Allowance Amount is the same for all the Warehouses, you can enter one amount here and an Allowance will be created for each Warehouse.   Click 'Edit / View All Items' to enter separate amounts for each warehouse`,
  HF_IF_WHSE_DESC: `If Allowance Amount is the same for all the Warehouses for all items, you can enter one amount here and an Allowance will be created for each Warehouse for each item, all with the same amount.   Click 'Edit / View All Items' to enter separate amounts for each item for each warehouse`,
};
export const BASE_HEIGHT = 85;

export const EVENT_DATE_INFO_MSG = `Offer Vehicle and Allowance dates have been updated from your Event date change.  Please review the dates and Overlaps and then Save the new Allowance dates.`;

export const OFFER_SECTIONS = {
  ALLOWANCE_PRIME_SECTION: "Allowance Prime Section",
  ALLOWANCE_DATES: "Allowance Dates",
  DSD_ALLOWANCE_DATES: "DSD Allowance Dates",
  ALLOWANCE_AMOUNT: "Allowance Amounts",
  DSD_ALLOWANCE_AMOUNT: "DSD Allowance Amount",
  DEFAULT_BILLING_INFO: "Billing Details",
  DSD_DEFAULT_BILLING_INFO: "DSD Billing Details",
  WAREHOUSE_ALLOWANCE_DATES: "Warehouse Allowance Dates",
  WAREHOUSE_ALLOWANCE_AMOUNT: "Warehouse Allowance Amount",
  WAREHOUSE_DEFAULT_BILLING_INFO: "Warehouse Billing Details",
};

export const OFFER_FORM_FIELDS = {
  AllowanceToBeCreatedRegField: "allowanceToBeCreated",
  storeGroupsRegField: "storeGroups",
  allowanceToBeCreatedOptionKey: "allowanceToBeCreatedOption",
  perfConfigKey: "perfConfig",
  createIndKey: "createInd",
  allowanceTypeChangeKey: "isAllowanceTypeChanged",
  performanceChangeKey: "isPerformanceChanged",
  hfAmountChangeKey: "isHfAmountChanged",
  storeSelectionChangeKey: "isStoreGroupChanged",
  AllowanceToBeCreatedChangeKey: "isAllowanceToBeCreatedChanged",
  additionalDatesChangeKey: "isAdditionalDatesChanged",
  orderDatesChangeKey: "isOrderDatesUpdated",
  additionalDatesKey: "additionalDates",
  datesValidateKey: "isDatesValid",
  hfAmountRegField: "overrideHeaderFlatAmt",
  hfAmountHeaderField: "hfHeader",
};

export const SECTION_KEYS = {
  ALLOWANCE_PRIME_SECTION: "allowancePrimeSection",
  ALLOWANCE_DATES: "allowanceDates",
  ALLOWANCE_AMOUNT: "allowanceAmount",
  ALLOWANCE_BILLING: "allowanceBillingInfo",
};

export const FORM_SECTION_KEYS = {
  ALLOWANCE_PRIME_SECTION: "allowancePrimeData",
  ALLOWANCE_TO_BE_CREATED: "allowanceToBeCreatedOption",
  ALLOWANCE_STORE_SELECTION: "allowanceStoreSelection",
  ALLOWANCE_DATES: "allowanceCreationVehicle",
  ALLOWANCE_AMOUNT: "allowanceAmountsData",
  ALLOWANCE_BILLING: "billingInformationData",
};

export const OFFER_ALLOWANCE: {
  sections: string[];
} = {
  sections: Object.values(OFFER_SECTIONS),
  [OFFER_SECTIONS.ALLOWANCE_PRIME_SECTION]: {
    key: OFFER_SECTIONS.ALLOWANCE_PRIME_SECTION,
    sectionKey: SECTION_KEYS.ALLOWANCE_PRIME_SECTION,
    includeAll: true,
    createInd: [],
    fields: {
      ALLOWANCE_TYPE_PERFORMANCE,
      STORE_SELECTION,
      ALLOWANCE_TO_BE_CREATED,
    },
    containerId: "abs-common-allowance-prime-section-container",
    headerLinkLabel: "Details",
    formKey: FORM_SECTION_KEYS.ALLOWANCE_PRIME_SECTION,
    allowanceTypeAndPerformance: {
      key: "allowanceTypeAndPerformance",
    },
    allowanceToBeCreated: {
      key: "allowanceToBeCreated",
      hidden: {
        [DP.key]: [HEADERFLAT.key, ITEMFLAT.key],
        [AO.key]: [HEADERFLAT.key, ITEMFLAT.key],
        [NDP.key]: [HEADERFLAT.key, ITEMFLAT.key],
        [NCDP.key]: [HEADERFLAT.key, ITEMFLAT.key],
      },
    },
    storeSelection: {
      key: "storeSelection",
      hidden: {
        [DP.key]: [
          CASE.key,
          SCAN.key,
          SHIPTOSTORE.key,
          HEADERFLAT.key,
          ITEMFLAT.key,
        ],
        [AO.key]: [CASE.key],
        [NDP.key]: [
          CASE.key,
          SCAN.key,
          SHIPTOSTORE.key,
          HEADERFLAT.key,
          ITEMFLAT.key,
        ],
        [NCDP.key]: [
          CASE.key,
          SCAN.key,
          SHIPTOSTORE.key,
          HEADERFLAT.key,
          ITEMFLAT.key,
        ],
      },
      notFoundText: "No Store Groups Found",
    },
    allowanceDates: {
      key: "allowanceDates",
    },
    isOpen: true,
    label: "Allowance Details",
    disable: {},
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: true,
      isView: true,
      label: "Review Overlaps & Enter Amounts",
      hfLabel: "Continue to Billing Details",
    },
    edit: {
      isEdit: true,
      isView: true,
      label: "Review Overlaps & Enter Amounts",
      hfLabel: "Continue to Billing Details",
    },
  },
  [OFFER_SECTIONS.ALLOWANCE_DATES]: {
    key: OFFER_SECTIONS.ALLOWANCE_DATES,
    sectionKey: SECTION_KEYS.ALLOWANCE_DATES,
    createInd: ["CC", "TC", "SB", "LC", "AC"],
    fields: ALLOWANCE_DATES,
    offerTypeKey: "COMBINED",
    containerId: "abs-common-allowance-dates-container",
    headerLinkLabel: "Dates",
    formKey: FORM_SECTION_KEYS.ALLOWANCE_DATES,
    excluded: true,
    allowanceToBeCreated: [
      CASE_OPTIONS.oneAllowance,
      SCAN_OPTIONS.oneAllowance,
      SHIPTOSTORE_OPTIONS.oneAllowance,
    ],
    label: "Allowance Dates",
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
  },
  [OFFER_SECTIONS.DSD_ALLOWANCE_DATES]: {
    key: OFFER_SECTIONS.DSD_ALLOWANCE_DATES,
    sectionKey: SECTION_KEYS.ALLOWANCE_DATES,
    createInd: ["CB", "CD", "TS", "SD"],
    fields: ALLOWANCE_DATES,
    formKey: FORM_SECTION_KEYS.ALLOWANCE_DATES,
    containerId: "abs-common-allowance-dsd-dates-container",
    headerLinkLabel: "DSD Dates",
    offerTypeKey: "DSD",
    excluded: true,
    allowanceToBeCreated: [
      CASE_OPTIONS.both,
      CASE_OPTIONS.dsdOnly,
      CASE_OPTIONS.separateAllowances,
      SCAN_OPTIONS.separateAllowances,
      SHIPTOSTORE_OPTIONS.separateAllowances,
    ],
    label: "DSD Allowance Dates",
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
  },
  [OFFER_SECTIONS.WAREHOUSE_ALLOWANCE_DATES]: {
    key: OFFER_SECTIONS.WAREHOUSE_ALLOWANCE_DATES,
    sectionKey: SECTION_KEYS.ALLOWANCE_DATES,
    createInd: ["CB"],
    fields: ALLOWANCE_DATES,
    containerId: "abs-common-allowance-warehouse-dates-container",
    headerLinkLabel: "W/H Dates",
    formKey: FORM_SECTION_KEYS.ALLOWANCE_DATES,
    allowanceToBeCreated: [CASE_OPTIONS.both, CASE_OPTIONS.warehouseOnly],
    label: "Warehouse Allowance Dates",
    offerTypeKey: "WAREHOUSE",
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: true,
      isView: true,
      label: "Review Overlaps & Enter Amounts",
    },
    edit: {
      isEdit: true,
      isView: true,
      label: "Review Overlaps & Enter Amounts",
    },
  },
  [OFFER_SECTIONS.ALLOWANCE_AMOUNT]: {
    key: OFFER_SECTIONS.ALLOWANCE_AMOUNT,
    sectionKey: SECTION_KEYS.ALLOWANCE_AMOUNT,
    createInd: ["CC", "TC", "SB", "AC"],
    containerId: "abs-common-allowance-amount-container",
    headerLinkLabel: "Amounts",
    fields: ALLOWANCE_AMOUNT,
    formKey: FORM_SECTION_KEYS.ALLOWANCE_AMOUNT,
    description: {
      ...AMT_DESCRIPTION_TEXT,
    },
    allowanceToBeCreated: [
      CASE_OPTIONS.oneAllowance,
      SCAN_OPTIONS.oneAllowance,
      SHIPTOSTORE_OPTIONS.oneAllowance,
    ],
    label: "Review Overlaps & Allowance Amounts",
    offerTypeKey: "COMBINED",
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Update",
    },
    disable: {
      createInd: [HEADER_FLAT.DSD.createIndex],
      allowanceScreenType: [DP.key, NDP.key, NCDP.key],
    },
  },
  [OFFER_SECTIONS.DSD_ALLOWANCE_AMOUNT]: {
    key: OFFER_SECTIONS.DSD_ALLOWANCE_AMOUNT,
    sectionKey: SECTION_KEYS.ALLOWANCE_AMOUNT,
    createInd: ["CB", "CD", "TS", "SD"],
    fields: ALLOWANCE_AMOUNT,
    formKey: FORM_SECTION_KEYS.ALLOWANCE_AMOUNT,
    description: {
      ...AMT_DESCRIPTION_TEXT,
    },
    containerId: "abs-common-allowance-dsd-amount-container",
    headerLinkLabel: "DSD Amounts",
    allowanceToBeCreated: [
      CASE_OPTIONS.both,
      CASE_OPTIONS.dsdOnly,
      CASE_OPTIONS.separateAllowances,
      SCAN_OPTIONS.separateAllowances,
      SHIPTOSTORE_OPTIONS.separateAllowances,
    ],
    label: "DSD Review Overlaps & Enter Amounts",
    offerTypeKey: "DSD",
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Update",
    },
  },
  [OFFER_SECTIONS.WAREHOUSE_ALLOWANCE_AMOUNT]: {
    key: OFFER_SECTIONS.WAREHOUSE_ALLOWANCE_AMOUNT,
    sectionKey: SECTION_KEYS.ALLOWANCE_AMOUNT,
    createInd: ["CB", "CW", "LW", "AW"],
    fields: ALLOWANCE_AMOUNT,
    formKey: FORM_SECTION_KEYS.ALLOWANCE_AMOUNT,
    containerId: "abs-common-allowance-whse-amount-container",
    headerLinkLabel: "W/H Amounts",
    description: {
      ...AMT_DESCRIPTION_TEXT,
    },
    allowanceToBeCreated: [CASE_OPTIONS.both, CASE_OPTIONS.warehouseOnly],
    label: "Warehouse Review Overlaps & Enter Amounts",
    offerTypeKey: "WAREHOUSE",
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Update",
    },
  },
  [OFFER_SECTIONS.DEFAULT_BILLING_INFO]: {
    key: OFFER_SECTIONS.DEFAULT_BILLING_INFO,
    sectionKey: SECTION_KEYS.ALLOWANCE_BILLING,
    createInd: ["CC", "TC", "SB", "LC", "AC"],
    fields: BILLING_INFORMATION,
    formKey: FORM_SECTION_KEYS.ALLOWANCE_BILLING,
    allowanceToBeCreated: [
      CASE_OPTIONS.oneAllowance,
      SCAN_OPTIONS.oneAllowance,
      SHIPTOSTORE_OPTIONS.oneAllowance,
    ],
    label: "Billing Details",
    offerTypeKey: "COMBINED",
    containerId: "abs-common-allowance-default-billing-container",
    headerLinkLabel: "Billing",
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save & Continue",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Update & Continue",
    },
  },
  [OFFER_SECTIONS.DSD_DEFAULT_BILLING_INFO]: {
    key: OFFER_SECTIONS.DSD_DEFAULT_BILLING_INFO,
    sectionKey: SECTION_KEYS.ALLOWANCE_BILLING,
    createInd: ["CB", "CD", "TS", "SD"],
    fields: BILLING_INFORMATION,
    formKey: FORM_SECTION_KEYS.ALLOWANCE_BILLING,
    containerId: "abs-common-allowance-dsd-default-billing-container",
    headerLinkLabel: "DSD Billing",
    allowanceToBeCreated: [
      CASE_OPTIONS.both,
      CASE_OPTIONS.dsdOnly,
      CASE_OPTIONS.separateAllowances,
      SCAN_OPTIONS.separateAllowances,
      SHIPTOSTORE_OPTIONS.separateAllowances,
    ],
    label: "DSD Billing Details",
    offerTypeKey: "DSD",
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Continue to Warehouse Dates",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Continue to Warehouse Dates",
    },
  },
  [OFFER_SECTIONS.WAREHOUSE_DEFAULT_BILLING_INFO]: {
    key: OFFER_SECTIONS.WAREHOUSE_DEFAULT_BILLING_INFO,
    sectionKey: SECTION_KEYS.ALLOWANCE_BILLING,
    createInd: ["CB", "CW", "LW", "AW"],
    fields: BILLING_INFORMATION,
    formKey: FORM_SECTION_KEYS.ALLOWANCE_BILLING,
    allowanceToBeCreated: [CASE_OPTIONS.both, CASE_OPTIONS.warehouseOnly],
    label: "Warehouse Billing Details",
    containerId: "abs-common-allowance-whse-default-billing-container",
    headerLinkLabel: "W/H Billing",
    offerTypeKey: "WAREHOUSE",
    allowanceScreenTypes: [DP.key, AO.key, NDP.key, NCDP.key],
    create: {
      isEdit: false,
      isView: false,
      label: "Save & Continue",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Update & Continue",
    },
  },
};

export const OFFER_DATES_MAPPER = [
  OFFER_ALLOWANCE[OFFER_SECTIONS.ALLOWANCE_DATES],
  OFFER_ALLOWANCE[OFFER_SECTIONS.DSD_ALLOWANCE_DATES],
  OFFER_ALLOWANCE[OFFER_SECTIONS.WAREHOUSE_ALLOWANCE_DATES],
];

export const DEFAULT_OFFER_SECTIONS = [
  OFFER_ALLOWANCE[OFFER_SECTIONS.ALLOWANCE_PRIME_SECTION],
  OFFER_ALLOWANCE[OFFER_SECTIONS.ALLOWANCE_AMOUNT],
  OFFER_ALLOWANCE[OFFER_SECTIONS.DEFAULT_BILLING_INFO],
];

export const DEFAULT_OFFER_SECTIONS_ENABLE_CONFIG = {
  [OFFER_SECTIONS.ALLOWANCE_PRIME_SECTION]: {
    isActive: true,
    scrollTo: true,
  },
  [OFFER_SECTIONS.ALLOWANCE_AMOUNT]: { isActive: false, scrollTo: false },
  [OFFER_SECTIONS.DEFAULT_BILLING_INFO]: { isActive: false, scrollTo: false },
};
