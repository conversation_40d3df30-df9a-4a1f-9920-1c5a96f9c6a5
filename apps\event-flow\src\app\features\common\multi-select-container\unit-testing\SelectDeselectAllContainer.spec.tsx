import { render, screen, fireEvent } from "@testing-library/react";
import SelectDeselectAllContainer from "../SelectDeselectAllContainer"; // Adjust path as necessary
import "@testing-library/jest-dom";

describe("SelectDeselectAllContainer", () => {
  const mockHandleClick = jest.fn();
  const configObj = {
    selectDeselectLabel: "Select All Items",
    id: "test-id",
  };

  afterEach(() => {
    mockHandleClick.mockClear();
  });

  it("renders with the correct checkbox states", () => {
    render(
      <SelectDeselectAllContainer
        configObj={configObj}
        handleClick={mockHandleClick}
        isChecked={true}
        partialClass={false}
        isDisabled={false}
      />
    );

    const checkbox = screen.getByTestId("selection-list-select-all");
    const label = screen.getByText("Select All Items");

    expect(checkbox).toBeChecked();
    expect(checkbox).not.toHaveAttribute("aria-disabled");
    expect(label).toBeInTheDocument();
  });

  it("renders with the default label when no label is provided", () => {
    render(
      <SelectDeselectAllContainer
        configObj={{ id: "test-id" }}
        handleClick={mockHandleClick}
        isChecked={false}
        partialClass={false}
        isDisabled={false}
      />
    );

    const label = screen.getByText("Select/Deselect All");
    expect(label).toBeInTheDocument();
  });

  it("calls handleClick when the checkbox is clicked", () => {
    render(
      <SelectDeselectAllContainer
        configObj={configObj}
        handleClick={mockHandleClick}
        isChecked={false}
        partialClass={false}
        isDisabled={false}
      />
    );

    const checkbox = screen.getByTestId("selection-list-select-all");
    fireEvent.click(checkbox);

    expect(mockHandleClick).toHaveBeenCalledTimes(1);
  });

  it("does not call handleClick when the checkbox is disabled", () => {
    render(
      <SelectDeselectAllContainer
        configObj={configObj}
        handleClick={mockHandleClick}
        isChecked={false}
        partialClass={false}
        isDisabled={true}
      />
    );

    const checkbox = screen.getByTestId("selection-list-select-all");
    fireEvent.click(checkbox);

    expect(mockHandleClick).toHaveBeenCalledTimes(0); // Should not be called
  });

  it("renders with the disabled attribute when isDisabled is true", () => {
    render(
      <SelectDeselectAllContainer
        configObj={configObj}
        handleClick={mockHandleClick}
        isChecked={false}
        partialClass={false}
        isDisabled={true}
      />
    );

    const checkbox = screen.getByTestId("selection-list-select-all");
    expect(checkbox).toBeDisabled();
  });
});
