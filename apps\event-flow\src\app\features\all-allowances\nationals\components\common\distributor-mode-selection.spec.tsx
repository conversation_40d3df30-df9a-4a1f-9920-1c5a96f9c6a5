import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import DistributorModeSelection from "./distributor-mode-selection";

describe("DistributorModeSelection", () => {
  // Sample distConfig to pass as prop.
  const distConfig = {
    LEAD_DIST_ONLY: {
      key: "lead_dist_only",
      value: "LEAD_DIST_ONLY",
      displayLabel: "Lead Distributor Only",
      subText: "Lead Distributor Only Subtext",
      disable: false,
    },
    BILL_LEAD_DIST: {
      key: "bill_lead_dist",
      value: "BILL_LEAD_DIST",
      displayLabel: "Bill Lead Distributor",
      subText: "Bill Lead Distributor Subtext",
      disable: false,
    },
  };

  it("renders radio options and subtexts", () => {
    const { container } = render(
      <DistributorModeSelection
        distMode="LEAD_DIST_ONLY"
        setDistMode={jest.fn()}
        distConfig={distConfig}
        isBillingSection={false}
      />
    );

    // Instead of using a data-testid, find the radio group using the class name.
    const radioGroup = container.querySelector(".lead-distributor-radio-group");
    expect(radioGroup).toBeInTheDocument();

    // Check for the radio options labels.
    expect(screen.getByText("Lead Distributor Only")).toBeInTheDocument();
    expect(screen.getByText("Bill Lead Distributor")).toBeInTheDocument();

    // Check for the subtext content.
    expect(screen.getByText("Lead Distributor Only Subtext")).toBeInTheDocument();
    expect(screen.getByText("Bill Lead Distributor Subtext")).toBeInTheDocument();

    // Verify that there are two radio options rendered.
    const radioOptions = screen.getAllByRole("radio");
    expect(radioOptions).toHaveLength(2);
  });

  it("calls setDistMode on radio change", () => {
    const setDistModeMock = jest.fn();
    render(
      <DistributorModeSelection
        distMode="LEAD_DIST_ONLY"
        setDistMode={setDistModeMock}
        distConfig={distConfig}
        isBillingSection={false}
      />
    );

    // Get all radio inputs.
    const radioInputs = screen.getAllByRole("radio");
    expect(radioInputs).toHaveLength(2);

    // Simulate clicking the second radio option (BILL_LEAD_DIST).
    fireEvent.click(radioInputs[1]);
    expect(setDistModeMock).toHaveBeenCalledWith("BILL_LEAD_DIST");
  });

  it("disables radio option based on isBillingSection", () => {
    const distConfigBilling = {
      LEAD_DIST_ONLY: {
        key: "lead_dist_only",
        value: "LEAD_DIST_ONLY",
        displayLabel: "Lead Distributor Only",
        subText: "Lead Distributor Only Subtext",
        disable: true,
      },
      BILL_LEAD_DIST: {
        key: "bill_lead_dist",
        value: "BILL_LEAD_DIST",
        displayLabel: "Bill Lead Distributor",
        subText: "Bill Lead Distributor Subtext",
        disable: false,
      },
    };

    render(
      <DistributorModeSelection
        distMode="LEAD_DIST_ONLY"
        setDistMode={jest.fn()}
        distConfig={distConfigBilling}
        isBillingSection={true}
      />
    );

    // Get the radio inputs and verify that the LEAD_DIST_ONLY option is disabled.
    const radioInputs = screen.getAllByRole("radio");
    expect(radioInputs[0]).toBeDisabled();
    expect(radioInputs[1]).not.toBeDisabled();
  });

  it("disables radio option based on isExternalVendor and efConstants", () => {
    // When isExternalVendor is true, disableLeadDistOption disables the LEAD_DIST_ONLY option
    // if its value equals efConstants.LEAD_DIST_ONLY.
    const distConfigExternal = {
      LEAD_DIST_ONLY: {
        key: "lead_dist_only",
        value: "LEAD_DIST_ONLY",
        displayLabel: "Lead Distributor Only",
        subText: "Lead Distributor Only Subtext",
        disable: false,
      },
      BILL_LEAD_DIST: {
        key: "bill_lead_dist",
        value: "BILL_LEAD_DIST",
        displayLabel: "Bill Lead Distributor",
        subText: "Bill Lead Distributor Subtext",
        disable: false,
      },
    };

    render(
      <DistributorModeSelection
        distMode="BILL_LEAD_DIST"
        setDistMode={jest.fn()}
        distConfig={distConfigExternal}
        isBillingSection={false}
        isExternalVendor={true}
      />
    );

    // For isExternalVendor true, LEAD_DIST_ONLY should be disabled if its value equals efConstants.LEAD_DIST_ONLY.
    const radioInputs = screen.getAllByRole("radio");
    // radioInputs[0] corresponds to LEAD_DIST_ONLY
    expect(radioInputs[0]).toBeDisabled();
    // radioInputs[1] (BILL_LEAD_DIST) should not be disabled.
    expect(radioInputs[1]).not.toBeDisabled();
  });
});
