/* You can add global styles to this file, and also import other style files */
@tailwind base;
@tailwind components;
@tailwind utilities;
/* Your styles goes here. */
/* You can add global styles to this file, and also import other style files */

@layer base {
  body {
    font-family: "Nunito Sans", sans-serif;
  }
}

.bg-brand {
  --tw-bg-opacity: 1;
  background-color: rgb(27 110 187 / var(--tw-bg-opacity)) !important;
}

#headlessui-portal-root [id*="headlessui-dialog"] {
  z-index: 999;
}
.accordian-header-class>span{
display: none;
}
@media only screen and (min-width: 1024px) and (max-width:1100px) {
 
  .mainentry-switch {
    margin-left: 3.2rem !important;
  }
}
@media only screen and (min-width: 1101px) and (max-width:1300px) {

  .mainentry-switch {
    margin-left: 1rem !important;
  }
}
@media only screen and (min-width: 1024px) and (max-width:1300px) {
.ndp-update-all-division {
    width:143px !important;
    font-size:13px !important;
  }
.ndp-update-selected-division{
      width: 200px !important;
      font-size: 13px !important;
  }
}
@media only screen and (min-width: 1301px) {
  .ndp-update-all-division {
    width: 170px !important;
    font-size: 16px !important;
  }
}
@media only screen and (min-width: 1301px) and (max-width:1500px) {
 
  .ndp-update-selected-division {
    width: 336px !important;
    font-size: 16px !important;
  }
        .mainentry-switch {
          margin-left: 0.6rem !important;
        }
}
@media only screen and (min-width: 1501px)  {
  .ndp-update-selected-division {
    width: 300px !important;
    font-size: 16px !important;
  }
     .mainentry-switch {
       margin-left: 1px !important;
     }
}

.periodScroll ul {
  &::-webkit-scrollbar {
    width: 10px;
    height: 8px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.2);
  }
}

.component-scroll::-webkit-scrollbar {
  -webkit-appearance: none;
  margin-left: 2px;
  width: 7px;
}

.component-scroll::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  -webkit-box-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
}

.component-scroll div {
  &::-webkit-scrollbar {
    width: 10px;
    height: 8px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.2);
  }
}

.component-scroll ul {
  &::-webkit-scrollbar {
    width: 10px;
    height: 8px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.2);
  }
}
.parent-child-events{
  padding-left: 0px;
  padding-right: 0px;
  padding-top: 0px;
}
.component-scroll section div {
  &::-webkit-scrollbar {
    width: 10px;
    height: 8px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.2);
  }
}

.start-week-container {
  .ReactVirtualized__Grid__innerScrollContainer {
    max-height: 2400px !important;
    height: 2400px !important;
  }
}
.amounts-input-field input:-webkit-autofill,
.amounts-input-field input:-webkit-autofill:hover,
.amounts-input-field input:-webkit-autofill:focus,
.amounts-input-field input:-webkit-autofill:active {
  background-color: transparent !important;
  -webkit-box-shadow: 0 0 0px 1000px white inset !important;
  -webkit-text-fill-color: inherit !important;
}

.truncate-vendornumber{
  display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
}

input[name*="vendorComment"],input[name*="acApOrArNumber"],button[name*="suggestedVendorPaymentType"] {
  border-radius: 10px !important;
}

.accordian-header-class>span {
  display: none;
}

button[name*='allowanceCreationVehicle.startDate'],
button[name*='allowanceCreationVehicle.endDate']{
  background:white
}