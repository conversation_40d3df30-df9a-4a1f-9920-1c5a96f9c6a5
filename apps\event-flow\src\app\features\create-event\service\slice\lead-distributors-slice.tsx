import { createGenericSlice } from "@me/data-rtk";

// slice for lead distributors data
export const leadDistributorsSlice = createGenericSlice({
  name: "leadDistributors_rn",
  initialState: {
    status: "loading",
    data: { stepData: [], leadOptions: [], isLeadChange: false, leadSelectionType: null, allDivisionLeadOptions: [], allDivisionStepsData: {} },
  },
})({
  leadDistributorsHandler(state, { payload }) {
    state.data = { ...state.data, ...payload };
    state.status = "finished";
  },
});
export const { leadDistributorsHandler } = leadDistributorsSlice.actions;

export const leadDistributorsModeSlice = createGenericSlice({
  name: "leadDistributorsMode_rn",
  initialState: {
    status: "loading",
    data: { leadDistributorsMode: null },
  },
})({
  leadDistributorsModeHandler(state, { payload }) {
    state.data = { ...state.data, ...payload };
    state.status = "finished";
  },
});
export const { leadDistributorsModeHandler } =
  leadDistributorsModeSlice.actions;

export const leadDistErrorslice = createGenericSlice({
  name: "leadDistErrorslice_rn",
  initialState: {
    status: "loading",
    data: { isLeadDistributorError: false },
  },
})({
  setIsLeadDistributorError(state, { payload }) {
    state.data = { ...state.data, ...payload };
    state.status = "finished";
  },
});
export const { setIsLeadDistributorError } =
  leadDistErrorslice.actions;

export const isLeadDistributorChange = createGenericSlice({
  name: "leadDistributorsChanged_rn",
  initialState: {
    status: "loading",
    data: { isLeadChange: false },
  },
})({
  setIsLeadDataChange(state, { payload }) {
    state.data = { ...state.data, ...payload };
    state.status = "finished";
  },
});
export const { setIsLeadDataChange } = isLeadDistributorChange.actions;

export const initialLeadDistSelected = createGenericSlice({
  name: "initialLeadDistSelected_rn",
  initialState: {
    status: "loading",
    data: {},
  },
})({
  setInitialLeadDistData(state, { payload }) {
    state.data = { ...state.data, ...payload };
    state.status = "finished";
  },
});
export const { setInitialLeadDistData } = initialLeadDistSelected.actions;

export const divisionWiseLeadDistData = createGenericSlice({
  name: "division_wise_lead_dist_data_rn",
  initialState: {
    status: "loading",
    data: {},
  },
})({
  setDivisionWiseLeadDistData(state, { payload }) {
    state.data = { ...state.data, ...payload };
    state.status = "finished";
  },
  resetDivisionWiseLeadDistData(state) {
    state.data = {};
    state.status = "finished";
  },
});
export const { setDivisionWiseLeadDistData, resetDivisionWiseLeadDistData } = divisionWiseLeadDistData.actions;

export const divisionWiseBillingSectionData = createGenericSlice({
  name: "division_wise_billing_selection_data_rn",
  initialState: {
    status: "loading",
    data: {},
  },
})({
  setDivisionWiseBillingSectionData(state, { payload }) {
    state.data = { ...state.data, ...payload };
    state.status = "finished";
  },
  resetDivisionWiseBillingSectionData(state) {
    state.data = {};
    state.status = "finished";
  },
});
export const { setDivisionWiseBillingSectionData, resetDivisionWiseBillingSectionData } = divisionWiseBillingSectionData.actions;
