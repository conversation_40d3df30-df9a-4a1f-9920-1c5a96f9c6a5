import { Column } from "@albertsons/uds/molecule/Table/Table.types";
import ToolTipWrapper from "../../../../shared/ui/atoms/tooltip-wrapper/tooltip-wrapper";
import AllowanceViewDealSheet from "./allowance-view-deal-sheet";
import { OfferViewModal } from "./allowance-view-model";
import { formatDate } from "apps/promotion-management/src/app/shared/utility/utility";
import { DASHBOARD_CONSTANTS, EVENT_TYPE } from "../../../../constants";
import { SortLabel } from "../../common/components/sort-label";
import {
  isSortFeatureFlagEnabled,
  useGetAppBaseNationalIcon,
} from "@me-upp-js/utilities";
import { PROMOTION_MANAGEMENT_COLUMNS } from "apps/promotion-management/src/app/shared/pm-constants/pm-constants";
import { NavLink } from "react-router-dom";
import { getIsNationalEvent } from "apps/event-flow/src/app/features/event-types/event-types-helper";

const {
  Offer_Id,
  DivisionIds,
  Allowance_Type,
  Offer_Events,
  Date,
  Vendor,
  Event_Status,
  Offer_Status,
} = PROMOTION_MANAGEMENT_COLUMNS;
const { nationalRolesAvailable } = getIsNationalEvent("NDP");

export const ALLOWANCE_VIEW_GRID_COLUMNS: Column<OfferViewModal>[] = [
  {
    id: "offerId",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel _key={"offerId"} label={Offer_Id} view="allowanceView" />
    ) : (
      Offer_Id
    ),
    width: "8vw",
    value: (offer: OfferViewModal) => (
      <div
        id="abs-allowance-view-column-config-offerId"
        className="text-md px-3 flex"
      >
        {offer?.["eventType"] === EVENT_TYPE.NDP &&
          useGetAppBaseNationalIcon() && (
            <img
              src={useGetAppBaseNationalIcon()}
              alt="national-icon"
              className="h-6 w-6 mr-1"
            />
          )}
        {offer?.["offerNumber"]}
      </div>
    ),
  },
  {
    id: "DivisionIds",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel
        _key={"divisionIds"}
        label={DivisionIds}
        view="allowanceView"
      />
    ) : (
      DivisionIds
    ),
    width: "6vw",
    value: (offer: OfferViewModal) => {
      const { divisionIds = [] } = offer || {};
      const divIds = divisionIds?.join(", ");
      return (
        <div
          id="abs-allowance-view-column-config-divisionIds"
          className="text-sm px-3"
        >
          {divIds}
        </div>
      );
    },
  },
  {
    id: "allowanceType",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel
        _key={"allowanceType"}
        label={Allowance_Type}
        view="allowanceView"
      />
    ) : (
      Allowance_Type
    ),
    width: "8.5vw",
    value: (offer: OfferViewModal) => {
      const { allowanceTypes = [] } = offer || {};
      const allowType = `${allowanceTypes?.[0] || ""}`;
      return (
        <div
          id="abs-allowance-view-column-config-allowanceType"
          className="text-sm px-3"
        >
          {allowType}
        </div>
      );
    },
  },
  {
    id: "offerAndAssociateEvents",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel _key={"event"} label={Offer_Events} view="allowanceView" />
    ) : (
      Offer_Events
    ),
    width: "22.5vw",
    value: (offer: OfferViewModal) => {
      const {
        planEventIdNbr = "",
        allowanceTypes = [],
        name = "",
        eventCreationVehicle = { vehicleNm: "", startDate: "", endDate: "" },
      } = offer || {};
      const offerName = `${allowanceTypes?.[0] || ""} - ${
        eventCreationVehicle?.vehicleNm || ""
      } - ${formatDate(eventCreationVehicle?.startDate)} - ${formatDate(
        eventCreationVehicle?.endDate
      )}`;
      return (
        <div
          id="abs-allowance-view-column-config-offerAndAssociateEvents"
          className="flex flex-col px-3 py-2 gap-2 w-[22vw]"
        >
          <ToolTipWrapper label={offerName} width="20vw">
            {((offer?.["eventType"] === "NDP" && nationalRolesAvailable) ||
              offer?.["eventType"] !== "NDP") && (
              <NavLink
                to={`events/edit/${offer?.["planEvent"]}?offerIdForExpand=${offer?.["planEventIdNbr"]}`}
                className="text-brand text-[#1B6EBB] cursor-pointer font-bold text-base whitespace-nowrap truncate"
                id={`abs-allowance-view-column-config-offerName`}
                data-testid="offer-name"
              >
                {offerName}
              </NavLink>
            )}
            {offer?.["eventType"] === "NDP" && !nationalRolesAvailable && (
              <span
                className="text-brand font-bold text-base whitespace-nowrap truncate"
                id={`abs-allowance-view-column-config-offerName`}
              >
                {offerName}
              </span>
            )}
          </ToolTipWrapper>

          <ToolTipWrapper
            label={`${planEventIdNbr} - ${name}`}
            width="20vw"
            className="text-sm"
          />
        </div>
      );
    },
  },
  {
    id: "date",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel _key={"date"} label={"Date"} view="allowanceView" />
    ) : (
      Date
    ),
    width: "10vw",
    value: (offer: OfferViewModal) => (
      <div
        id={`abs-allowance-view-column-config-data-${offer?.["offerId"]}`}
        className="text-sm px-3"
      >
        {`${formatDate(offer?.["startDate"])} - ${formatDate(
          offer?.["endDate"]
        )}`}
      </div>
    ),
  },
  {
    id: "vendor",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel _key={"vendor"} label={"Vendor"} view="allowanceView" />
    ) : (
      Vendor
    ),
    // label: <SortLabel _key={"vendor"} label={"Vendor"} view="allowanceView" />,
    width: "10vw",
    value: ({ simsVendorList }: OfferViewModal) => {
      const uniquesimsTaskList: string[] = [];
      const label = simsVendorList?.reduce(
        (simsTaskLabel, { supplierId, vendorSubAccountName = "" }) => {
          if (!uniquesimsTaskList.includes(supplierId)) {
            uniquesimsTaskList.push(supplierId);
            simsTaskLabel =
              simsTaskLabel +
              `${
                simsTaskLabel && ", "
              }${supplierId} ${vendorSubAccountName?.trim()}`;
          }
          return simsTaskLabel;
        },
        ""
      );
      return (
        <ToolTipWrapper label={label} width="11vw" className="px-3 text-sm" />
      );
    },
  },
  {
    id: "eventStatus",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel _key={"type"} label={"Event Status"} view="allowanceView" />
    ) : (
      Event_Status
    ),
    // label: (
    //   <SortLabel _key={"type"} label={"Event Status"} view="allowanceView" />
    // ),
    width: "7vw",
    value: (offer: OfferViewModal) => (
      <div
        id="abs-allowance-view-column-config-eventStatus"
        className="px-3 text-sm"
      >
        {offer?.["eventStatus"]}
      </div>
    ),
  },
  {
    id: "offerStatus",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel
        _key={"offerStatus"}
        label={"Offer Status"}
        view="allowanceView"
      />
    ) : (
      Offer_Status
    ),
    // label: (
    //   <SortLabel
    //     _key={"offerStatus"}
    //     label={"Offer Status"}
    //     view="allowanceView"
    //   />
    // ),
    width: "7vw",
    value: (offer: OfferViewModal) => (
      <div
        id="abs-allowance-view-column-config-offerStatus"
        className="px-3 text-sm"
      >
        {offer?.["allowanceStatus"]}
      </div>
    ),
  },
  {
    id: "amount",
    label: "Amount",
    width: "6.5vw",
    value: (offer: OfferViewModal) => {
      const minAmnt = offer?.["minAllowAmount"] || 0;
      const maxAmnt = offer?.["maxAllowAmount"] || 0;
      const isEqual = minAmnt === maxAmnt;
      const isHf = offer?.["allowanceTypes"]?.includes(
        DASHBOARD_CONSTANTS.ALLOWANCE_TYPES.HEADERFLAT.label
      );
      const hfAmnt = offer?.["headerFlatSumAmt"] || 0;
      return (
        <div
          id="abs-allowance-view-column-config-amount"
          className="px-5 text-sm"
        >
          {isHf
            ? `$${hfAmnt}`
            : isEqual
            ? `$${minAmnt}`
            : `$${minAmnt} - $${maxAmnt}`}
        </div>
      );
    },
  },
  {
    id: "actions",
    label: "Actions",
    width: "10vw",
    value: (offer: OfferViewModal) => {
      return (
        <div
          id="abs-allowance-view-column-config-actions"
          className="flex w-full gap-2 px-3 justify-start"
        >
          {((offer?.["eventType"] === "NDP" && nationalRolesAvailable) ||
            offer?.["eventType"] !== "NDP") && (
            <AllowanceViewDealSheet offer={offer} />
          )}
        </div>
      );
    },
  },
];
