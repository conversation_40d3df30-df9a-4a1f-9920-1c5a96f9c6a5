import Checkbox from "@albertsons/uds/molecule/Checkbox";
import RenderMoveBtn from "../../../allowance-lead-distributors/move-button";
import useLeadDistributors from "../../../../create-event/hooks/useLeadDistributors";
import { useSelectorWrap } from "@me/data-rtk";
import classNames from "classnames";
import React from "react";
import { isNationalType } from "../../../allowance-lead-distributors/billing-selection-utils";

function VendorSelections({ currentDivision = {divisionId: ""}, isNdpType = false }) {
  const {
    data: { leadOptions, allDivisionLeadOptions },
  } = useSelectorWrap("leadDistributors_rn") || {};
  const { divisionId = "" } = currentDivision || {};
  const {
    data: excludedVendorData
  } = useSelectorWrap("excludedVendorForAllowance_rn") || {};
  const isNational = isNationalType(isNdpType);
  const currentDivLeadOptions = isNational ? allDivisionLeadOptions?.[divisionId] || [] : leadOptions;
  const {
    selectedList,
    stepData,
    updateSelected,
    includeToVendor,
    otherVendors,
    isAllExternal,
  } = useLeadDistributors({
    leadOptions: currentDivLeadOptions?.map(item => item?.vendorNbr),
    currentDivId: isNational ? currentDivision?.divisionId : "",
    isNdpType: isNational,
  });
  const currentDivExcludedVendors = excludedVendorData?.divisionWiseExcludedVendors?.[divisionId] || {};
  const excludedVendToCheck = isNational ? currentDivExcludedVendors : excludedVendorData;

  const excludedVendorIds = excludedVendToCheck
    ? Object.values(excludedVendToCheck)
        ?.filter((item: any) => item?.isExclude)
        ?.map((e: any) => e?.vendorNbr)
    : [];
  function renderAvailableVendors() {
    return currentDivLeadOptions
      ?.filter(
        item => item?.vendorNbr && !excludedVendorIds?.includes(item?.vendorNbr)
      )
      ?.map((item, leadIndex) => (
        <VendorCheckBox
          key={item?.vendorNbr}
          id={item?.vendorNbr}
          index={leadIndex}
          disabledMove={true}
          selectionDisabled={false}
        />
      ));
  }
  function renderSelectedVendors() {
    return stepData?.map((step, index) => (
      <React.Fragment key={step?.id}>
        <VendorCheckBox
          key={step?.id}
          id={step?.id}
          disabledMove={selectedList?.length < 2}
          selectionDisabled={selectedList?.length >= 2}
          child={step?.child}
          isMoveDirectionDown={!index}
          index={index}
        />
        {index + 1 !== stepData?.length && (
          <div className="w-full h-[1px] my-1 bg-[#C8DAEB]" />
        )}
      </React.Fragment>
    ));
  }
  return (
    <div className="pl-[3.2rem] pr-4">
      <section className="flex flex-col pb-3 w-[100%] p-2 modal-cls  overflow-auto lead-dist-vendor-section">
        {!selectedList?.length
          ? renderAvailableVendors()
          : renderSelectedVendors()}
      </section>
    </div>
  );
  function VendorCheckBox({
    id,
    child,
    disabledMove,
    selectionDisabled,
    index,
  }: {
    id: string;
    child?: string[];
    disabledMove: boolean;
    selectionDisabled: boolean;
    isMoveDirectionDown?: boolean;
    index?: number;
  }) {
    return (
      <>
        <Checkbox
          checked={selectedList?.includes(id)}
          onChange={() => {
            updateSelected({ id });
          }}
          disabled={excludedVendorIds?.includes(id)}
          key={id}
          className="py-[2px]"
        >
          {getDistributorsNameSummary(id, currentDivLeadOptions)}
        </Checkbox>

        <div
          className="pl-3 w-full"
          id="abs-lead-distributors-modal-checkbox-with-multiple-children"
        >
          {child?.map(item => checkBoxWithMultipleChildren(item, index))}
        </div>
      </>
    );
    function RenderMoveBtns({ index, item }) {
      const isFirstItem = index === 0,
        isLastItem = index === stepData?.length - 1;
      return (
        <div>
          {!disabledMove && (
            <RenderMoveBtn
              item={item}
              includeToVendor={includeToVendor}
              isFirstItem={isFirstItem}
              isLastItem={isLastItem}
            />
          )}
        </div>
      );
    }
    function checkBoxWithMultipleChildren(item: string, index): JSX.Element {
      return (
        <div className="flex justify-between w-full" id={item} key={item}>
          <Checkbox
            checked={false}
            disabled={
              selectionDisabled ||
              excludedVendorIds?.includes(item) ||
              (!isAllExternal && otherVendors?.includes(item))
            }
            onChange={() => {
              updateSelected({ id: item });
            }}
            key={item}
            className={classNames({
              "py-[2px]": true,
              "[&>label]:w-full": true,
            })}
          >
            {getDistributorsNameSummary(item, currentDivLeadOptions)}
          </Checkbox>
          <RenderMoveBtns item={item} index={index} />
        </div>
      );
    }
  }

  function getDistributorsNameSummary(id, leadOptions) {
    const vendor = leadOptions?.find(item => item?.vendorNbr === id);
    return (
      <span>
        <span className="font-bold">{vendor?.vendorName} </span>- {id}{" "}
        {vendor?.costAreaDesc}
      </span>
    );
  }
}

export default React.memo(VendorSelections);
