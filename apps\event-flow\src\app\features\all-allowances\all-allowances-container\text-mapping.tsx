import efConstants from "../../../shared/ef-constants/ef-constants";
import {
  CASE_WAREHSE_CASE_VENDOR_COLS,
  CASE_WAREHSE_MAIN_COLUMNS,
  CASE_WAREHSE_UNIT_VENDOR_COLS,
  HF_<PERSON>N_GROUP_COLUMNS,
  HF_VENDORS_COLUMNS,
  ITEM_FLAT_DSD_WHSE_COLUMNS,
  ITEM_FLAT_WAREHSE_MAIN_COLS,
  ITEM_FLAT_WAREHSE_VENDOR_COLS,
  S2S_DSD_WHSE_COLUMNS,
  S2S_DSD_WHSE_RETAIL_DIVISION_CASE_VENDOR_COLS,
  S2S_DSD_WHSE_RETAIL_DIVISION_UNIT_VENDOR_COLS,
  S2S_MULTI_DSD_COLUMNS,
  S2S_DSD_LEAD_DIST_CASE_VENDOR_COLS,
  S2S_DSD_LEAD_DIST_UNIT_VENDOR_COLS,
  ALL_SUMMARY_VENDOR_COLS_CASE,
  ALL_SUMMARY_VENDOR_COLS_COMMON,
  ALL_SUMMARY_VENDOR_COLS_IF,
  ALL_SUMMARY_ITEM_FLAT_DSD_WHSE_COLUMNS,
} from "../columns/allowance-column-constants";

const { ALLOWANCE_HF_AMT_KEY } = efConstants;

export const getEntryTextConfig = (
  offerType,
  groupType,
  isAllSummary = false
) => {
  const configData = ENTRY_TEXT_CONFIG(isAllSummary);
  return configData?.[offerType]?.[groupType];
};

export const ENTRY_TEXT_CONFIG = isAllSummary => {
  return {
    SCAN: {
      DSD_WHSE_RETAIL_DIVISION: {
        isHideColGroupHeader: true,
        allowanceTypeDisplay: "Scan",
        newCostCalculateKey: "unitCostAllow",
        isShowUOMOption: true,
        showExclude: false,
        description:
          "You can update allowance amounts for Items together or individually",
        showFilterTabsSection: false,
        showColumnGrouping: true,
        showLeadDistributorSection: false,
        impactedFieldsOnTable: [
          "allowUomType",
          "allowanceAmount",
          "caseListCost",
          "costAreaDesc",
          "unitCostAllow",
          "unitListCost",
          "newCaseCostAllow",
        ],
        filterTabs: [],
        allowGridCols: S2S_DSD_WHSE_COLUMNS,
        isSwitchingRequires: true,
        defaultUpdateAllUom: efConstants.EA_UOM_TYPE,
        switchCaseOptions: {
          switchOff: {
            value: "Case",
            text: "Switch to Unit",
            warningMsg: "*Switch to unit to enter/update amounts.",
            isOnlyDisplay: true,
          },
          switchOn: {
            value: "Unit",
            text: "Switch to Case",
            isOnlyDisplay: false,
            warningMsg: "",
          },
        },
        vendorGridCols: {
          columns: isAllSummary
            ? ALL_SUMMARY_VENDOR_COLS_COMMON
            : S2S_DSD_WHSE_RETAIL_DIVISION_UNIT_VENDOR_COLS,
          switchColumnsCase: {
            Case: {
              isOnlyDisplay: true,
              label: "Case",
              displayAmtKey: "displayUnitAmt",
              displayUOMKey: "displayUnitUOM",
              respUpdateKeys: [],
              columns: isAllSummary
                ? ALL_SUMMARY_VENDOR_COLS_COMMON
                : S2S_DSD_WHSE_RETAIL_DIVISION_CASE_VENDOR_COLS,
            },
            Unit: {
              isOnlyDisplay: false,
              displayAmtKey: "displayUnitAmt",
              displayUOMKey: "displayUnitUOM",
              respUpdateKeys: [],
              label: "Unit",
              columns: isAllSummary
                ? ALL_SUMMARY_VENDOR_COLS_COMMON
                : S2S_DSD_WHSE_RETAIL_DIVISION_UNIT_VENDOR_COLS,
            },
          },
        },
      },
      DSD_LEAD_DISTRIBUTORS: {
        groupText: "DSD",
        isShowUOMOption: true,
        showExclude: true,
        description:
          "You can update Allowance amounts for all the Distributors and items together or individually.",
        showFilterTabsSection: true,
        showColumnGrouping: true,
        showLeadDistributorSection: true,
        filterTabs: [
          {
            key: "byDistributor",
            displayText: "By Distributor",
            value: 0,
          },
          {
            key: "byItem",
            displayText: "By Item",
            value: 1,
          },
        ],
        leadDistConfig: efConstants.LEAD_DIST_CONFIG,
        billingInfoConfig: efConstants.BILLING_SELECTION_CONFIG,
        defaultUpdateAllUom: efConstants.EA_UOM_TYPE,
        impactedFieldsOnTable: [
          "allowUomType",
          "allowanceAmount",
          "caseListCost",
          "costAreaDesc",
          "newUnitCostAllow",
          "unitCostAllow",
          "unitListCost",
          "includeInd",
        ],
        allowGridCols: S2S_MULTI_DSD_COLUMNS,
        isSwitchingRequires: true,
        switchCaseOptions: {
          switchOff: {
            value: "Case",
            text: "Switch to Unit",
            warningMsg: "*Switch to unit to enter/update amounts.",
            isOnlyDisplay: true,
          },
          switchOn: {
            value: "Unit",
            text: "Switch to Case",
            isOnlyDisplay: false,
            warningMsg: "",
          },
        },
        vendorGridCols: {
          columns: isAllSummary
            ? ALL_SUMMARY_VENDOR_COLS_CASE
            : S2S_DSD_LEAD_DIST_CASE_VENDOR_COLS,
          switchColumnsCase: {
            Case: {
              isOnlyDisplay: true,
              label: "Case",
              displayAmtKey: "displayUnitAmt",
              displayUOMKey: "displayUnitUOM",
              respUpdateKeys: [],
              columns: isAllSummary
                ? ALL_SUMMARY_VENDOR_COLS_CASE
                : S2S_DSD_LEAD_DIST_CASE_VENDOR_COLS,
            },
            Unit: {
              isOnlyDisplay: false,
              displayAmtKey: "displayUnitAmt",
              displayUOMKey: "displayUnitUOM",
              respUpdateKeys: [],
              label: "Unit",
              columns: isAllSummary
                ? ALL_SUMMARY_VENDOR_COLS_CASE
                : S2S_DSD_LEAD_DIST_UNIT_VENDOR_COLS,
            },
          },

          headerKey: "vendorDetails",
          headerLabelFunc: "renderVendorDetails",
          align: "left",
        },

        newCostCalculateKey: "unitCostAllow",
        allowanceTypeDisplay: "Scan",
        excludeByKey: "vendorNbr",
        isExcludeByVendorNbr: true,
      },
    },
    CASE: {
      WAREHOUSE_DIST_CENTERS: {
        groupText: "Warehouse",
        newCostCalculateKey: "unitCostAllow",
        allowanceTypeDisplay: "Case",
        isShowUOMOption: true,
        isDistCenter: true,
        showExclude: true,
        excludeByKey: "distCenter",
        isExcludeByVendorNbr: false,
        description:
          "You can update allowance amounts for all the Warehouses and Items together or individually.",
        showFilterTabsSection: true,
        showColumnGrouping: true,
        showLeadDistributorSection: false,
        defaultUpdateAllUom: efConstants.CA_UOM_TYPE,
        impactedFieldsOnTable: [
          "allowUomType",
          "allowanceAmount",
          "caseListCost",
          "costAreaDesc",
          "unitCostAllow",
          "unitListCost",
          "newCaseCostAllow",
        ],
        filterTabs: [
          {
            key: "byWarehouse",
            displayText: "By Warehouse",
            value: 0,
          },
          {
            key: "byItem",
            displayText: "By Item",
            value: 1,
          },
        ],
        allowGridCols: CASE_WAREHSE_MAIN_COLUMNS,
        isSwitchingRequires: true,
        switchCaseOptions: {
          switchOff: {
            value: "Case",
            text: "Switch to Unit",
            warningMsg: "",
            isOnlyDisplay: false,
          },
          switchOn: {
            value: "Unit",
            text: "Switch to Case",
            isOnlyDisplay: true,
            warningMsg: "*Switch to case to enter/update amounts.",
          },
        },
        vendorGridCols: {
          headerKey: "vendorDetails",
          headerLabelFunc: "renderCenterExcludeGroupHeader",
          align: "center",
          columns: isAllSummary
            ? ALL_SUMMARY_VENDOR_COLS_CASE
            : CASE_WAREHSE_CASE_VENDOR_COLS,
          switchColumnsCase: {
            Case: {
              isOnlyDisplay: false,
              label: "Case",
              columns: isAllSummary
                ? ALL_SUMMARY_VENDOR_COLS_CASE
                : CASE_WAREHSE_CASE_VENDOR_COLS,
            },
            Unit: {
              isOnlyDisplay: true,
              displayAmtKey: "displayUnitAmt",
              displayUOMKey: "displayUnitUOM",
              respUpdateKeys: [],
              label: "Unit",
              columns: isAllSummary
                ? ALL_SUMMARY_VENDOR_COLS_CASE
                : CASE_WAREHSE_UNIT_VENDOR_COLS,
            },
          },
        },
      },
      DSD_LEAD_DISTRIBUTORS: {
        groupText: "DSD",
        newCostCalculateKey: "unitCostAllow",
        allowanceTypeDisplay: "Case",
        isShowUOMOption: true,
        description:
          "You can update Allowance amounts for all the Distributors and items together or individually.",
        showFilterTabsSection: true,
        showLeadDistributorSection: true,
        leadDistConfig: efConstants.LEAD_DIST_CONFIG,
        billingInfoConfig: efConstants.BILLING_SELECTION_CONFIG,
        showColumnGrouping: true,
        showExclude: true,
        excludeByKey: "vendorNbr",
        defaultUpdateAllUom: efConstants.CA_UOM_TYPE,
        isExcludeByVendorNbr: true,
        filterTabs: [
          {
            key: "byDistributor",
            displayText: "By Distributor",
            value: 0,
          },
          {
            key: "byItem",
            displayText: "By Item",
            value: 1,
          },
        ],
        switchCaseOptions: {
          switchOff: {
            value: "Case",
            text: "Switch to Unit",
            warningMsg: "",
            isOnlyDisplay: false,
          },
          switchOn: {
            value: "Unit",
            text: "Switch to Case",
            isOnlyDisplay: true,
            warningMsg: "*Switch to case to enter/update amounts.",
          },
        },
        allowGridCols: CASE_WAREHSE_MAIN_COLUMNS,
        isSwitchingRequires: true,
        impactedFieldsOnTable: [
          "allowUomType",
          "allowanceAmount",
          "caseListCost",
          "costAreaDesc",
          "newUnitCostAllow",
          "unitCostAllow",
          "unitListCost",
          "includeInd",
        ],
        vendorGridCols: {
          headerKey: "vendorDetails",
          headerLabelFunc: "renderVendorDetails",
          align: "left",
          columns: isAllSummary
            ? ALL_SUMMARY_VENDOR_COLS_CASE
            : CASE_WAREHSE_CASE_VENDOR_COLS,
          switchColumnsCase: {
            Case: {
              isOnlyDisplay: false,
              label: "Case",
              columns: isAllSummary
                ? ALL_SUMMARY_VENDOR_COLS_CASE
                : CASE_WAREHSE_CASE_VENDOR_COLS,
            },
            Unit: {
              isOnlyDisplay: true,
              displayAmtKey: "displayUnitAmt",
              displayUOMKey: "displayUnitUOM",
              respUpdateKeys: [],
              label: "Unit",
              columns: isAllSummary
                ? ALL_SUMMARY_VENDOR_COLS_CASE
                : CASE_WAREHSE_UNIT_VENDOR_COLS,
            },
          },
        },
      },
      DSD_WHSE_RETAIL_DIVISION: {
        isHideColGroupHeader: true,
        groupText: "DSD Combined",
        newCostCalculateKey: "unitCostAllow",
        allowanceTypeDisplay: "Case",
        isShowUOMOption: true,
        description:
          "You can update Allowance amounts for items together or individually.",
        showFilterTabsSection: false,
        showLeadDistributorSection: false,
        leadDistConfig: efConstants.LEAD_DIST_CONFIG,
        billingInfoConfig: efConstants.BILLING_SELECTION_CONFIG,
        showColumnGrouping: true,
        showExclude: false,
        defaultUpdateAllUom: efConstants.CA_UOM_TYPE,
        isExcludeByVendorNbr: true,
        filterTabs: [],
        switchCaseOptions: {
          switchOff: {
            value: "Case",
            text: "Switch to Unit",
            warningMsg: "",
            isOnlyDisplay: false,
          },
          switchOn: {
            value: "Unit",
            text: "Switch to Case",
            isOnlyDisplay: true,
            warningMsg: "*Switch to case to enter/update amounts.",
          },
        },
        allowGridCols: CASE_WAREHSE_MAIN_COLUMNS,
        isSwitchingRequires: true,
        impactedFieldsOnTable: [
          "allowUomType",
          "allowanceAmount",
          "caseListCost",
          "costAreaDesc",
          "newUnitCostAllow",
          "unitCostAllow",
          "unitListCost",
          "includeInd",
        ],
        vendorGridCols: {
          headerKey: "vendorDetails",
          headerLabelFunc: "renderVendorDetails",
          align: "left",
          columns: isAllSummary
            ? ALL_SUMMARY_VENDOR_COLS_CASE
            : CASE_WAREHSE_CASE_VENDOR_COLS,
          switchColumnsCase: {
            Case: {
              isOnlyDisplay: false,
              label: "Case",
              columns: isAllSummary
                ? ALL_SUMMARY_VENDOR_COLS_CASE
                : CASE_WAREHSE_CASE_VENDOR_COLS,
            },
            Unit: {
              isOnlyDisplay: true,
              displayAmtKey: "displayUnitAmt",
              displayUOMKey: "displayUnitUOM",
              respUpdateKeys: [],
              label: "Unit",
              columns: isAllSummary
                ? ALL_SUMMARY_VENDOR_COLS_CASE
                : CASE_WAREHSE_UNIT_VENDOR_COLS,
            },
          },
        },
      },
    },
    SHIP_TO_STORE: {
      DSD_WHSE_RETAIL_DIVISION: {
        isHideColGroupHeader: true, //Hides the header
        allowanceTypeDisplay: "Ship to Store",
        newCostCalculateKey: "caseCostAllow",
        isShowUOMOption: true,
        showExclude: false,
        description:
          "You can update allowance amounts for Items together or individually",
        showFilterTabsSection: false,
        showColumnGrouping: true,
        showLeadDistributorSection: false,
        defaultUpdateAllUom: efConstants.CA_UOM_TYPE,
        impactedFieldsOnTable: [
          "allowUomType",
          "allowanceAmount",
          "caseListCost",
          "costAreaDesc",
          "unitCostAllow",
          "unitListCost",
          "newCaseCostAllow",
        ],
        filterTabs: [],
        allowGridCols: S2S_DSD_WHSE_COLUMNS,
        isSwitchingRequires: true,
        switchCaseOptions: {
          switchOff: {
            value: "Case",
            text: "Switch to Unit",
            warningMsg: "",
            isOnlyDisplay: false,
          },
          switchOn: {
            value: "Unit",
            text: "Switch to Case",
            isOnlyDisplay: true,
            warningMsg: "*Switch to case to enter/update amounts.",
          },
        },
        vendorGridCols: {
          columns: isAllSummary
            ? ALL_SUMMARY_VENDOR_COLS_COMMON
            : S2S_DSD_WHSE_RETAIL_DIVISION_CASE_VENDOR_COLS,
          switchColumnsCase: {
            Case: {
              isOnlyDisplay: false,
              label: "Case",
              columns: isAllSummary
                ? ALL_SUMMARY_VENDOR_COLS_COMMON
                : S2S_DSD_WHSE_RETAIL_DIVISION_CASE_VENDOR_COLS,
            },
            Unit: {
              isOnlyDisplay: true,
              displayAmtKey: "displayUnitAmt",
              displayUOMKey: "displayUnitUOM",
              respUpdateKeys: [],
              label: "Unit",
              columns: isAllSummary
                ? ALL_SUMMARY_VENDOR_COLS_COMMON
                : S2S_DSD_WHSE_RETAIL_DIVISION_UNIT_VENDOR_COLS,
            },
          },
        },
      },
      DSD_LEAD_DISTRIBUTORS: {
        allowanceTypeDisplay: "Ship to Store",
        newCostCalculateKey: "caseCostAllow",
        groupText: "DSD",
        isShowUOMOption: true,
        showExclude: true,
        excludeByKey: "vendorNbr",
        isExcludeByVendorNbr: true,
        description:
          "You can update allowance amounts for all the Distributors and items together or individually.",
        showFilterTabsSection: true,
        filterTabs: [
          {
            key: "byDistributor",
            displayText: "By Distributor",
            value: 0,
          },
          {
            key: "byItem",
            displayText: "By Item",
            value: 1,
          },
        ],
        showColumnGrouping: true,
        showLeadDistributorSection: true,
        defaultUpdateAllUom: efConstants.CA_UOM_TYPE,
        leadDistConfig: efConstants.LEAD_DIST_CONFIG,
        billingInfoConfig: efConstants.BILLING_SELECTION_CONFIG,
        impactedFieldsOnTable: [
          "allowUomType",
          "allowanceAmount",
          "caseListCost",
          "costAreaDesc",
          "unitCostAllow",
          "unitListCost",
          "newCaseCostAllow",
        ],
        allowGridCols: S2S_MULTI_DSD_COLUMNS,
        isSwitchingRequires: true,
        switchCaseOptions: {
          switchOff: {
            value: "Case",
            text: "Switch to Unit",
            warningMsg: "",
            isOnlyDisplay: false,
          },
          switchOn: {
            value: "Unit",
            text: "Switch to Case",
            isOnlyDisplay: true,
            warningMsg: "*Switch to case to enter/update amounts.",
          },
        },
        vendorGridCols: {
          columns: isAllSummary
            ? ALL_SUMMARY_VENDOR_COLS_CASE
            : S2S_DSD_LEAD_DIST_CASE_VENDOR_COLS,
          switchColumnsCase: {
            Case: {
              isOnlyDisplay: false,
              label: "Case",
              columns: isAllSummary
                ? ALL_SUMMARY_VENDOR_COLS_CASE
                : S2S_DSD_LEAD_DIST_CASE_VENDOR_COLS,
            },
            Unit: {
              isOnlyDisplay: true,
              displayAmtKey: "displayUnitAmt",
              displayUOMKey: "displayUnitUOM",
              respUpdateKeys: [],
              label: "Unit",
              columns: isAllSummary
                ? ALL_SUMMARY_VENDOR_COLS_CASE
                : S2S_DSD_LEAD_DIST_UNIT_VENDOR_COLS,
            },
          },

          headerKey: "vendorDetails",
          headerLabelFunc: "renderVendorDetails",
          align: "left",
        },
      },
    },
    HEADER_FLAT: {
      WAREHOUSE_DIST_CENTERS: {
        groupText: "",
        newCostCalculateKey: "caseCostAllow",
        allowanceTypeDisplay: "Header Flat",
        description:
          "You can update allowance amounts for all the Warehouses and  items together or individually",
        customAmtWidth: 110,
        isSkipLessThanAmtValidation: true,
        isShowUOMOption: false,
        showExclude: true,
        isDistCenter: true,
        excludeByKey: "distCenter",
        isExcludeByVendorNbr: false,
        headerOnlyAmt: {
          //Amt field would only at header, not available @ item level. In other cases, remove this object or set the value to null
          headerOnlyAmtKey: ALLOWANCE_HF_AMT_KEY, //API Key to save and retrieve the value
        },
        showFilterTabsSection: false,
        showColumnGrouping: true,
        showLeadDistributorSection: false,
        impactedFieldsOnTable: [
          "allowanceAmount",
          ALLOWANCE_HF_AMT_KEY,
          "includeInd",
        ],
        filterTabs: [],
        allowGridCols: HF_NON_GROUP_COLUMNS,
        isSwitchingRequires: false,
        switchCaseOptions: {},
        vendorGridCols: {
          headerKey: "vendorDetails",
          headerLabelFunc: "renderCenterExcludeGroupHeader",
          align: "left",
          amtColmHeaderLabel: "Allowance",
          columns: isAllSummary
            ? ALL_SUMMARY_VENDOR_COLS_CASE
            : HF_VENDORS_COLUMNS,
          switchColumnsCase: null,
        },
      },
    },
    ITEM_FLAT: {
      DSD_WHSE_RETAIL_DIVISION: {
        isHideColGroupHeader: false, //Hides the header
        groupText: "",
        newCostCalculateKey: "caseCostAllow",
        allowanceTypeDisplay: "Item Flat",
        customAmtWidth: 110,
        showExclude: false,
        isSkipLessThanAmtValidation: true,
        isShowUOMOption: false,
        description:
          "You can update allowance amounts for Items together or individually.",
        showFilterTabsSection: false,
        showColumnGrouping: false,
        showLeadDistributorSection: false,
        impactedFieldsOnTable: ["allowanceAmount", "includeInd"],
        filterTabs: [],
        allowGridCols: isAllSummary
          ? ALL_SUMMARY_ITEM_FLAT_DSD_WHSE_COLUMNS
          : ITEM_FLAT_DSD_WHSE_COLUMNS,
        isSwitchingRequires: false,
        switchCaseOptions: {},
        vendorGridCols: {},
      },
      WAREHOUSE_DIST_CENTERS: {
        isHideColGroupHeader: false, //Hides the header
        groupText: "",
        newCostCalculateKey: "caseCostAllow",
        allowanceTypeDisplay: "Warehouse Item Flats",
        customAmtWidth: 110,
        showExclude: true,
        isDistCenter: true,
        excludeByKey: "distCenter",
        isExcludeByVendorNbr: false,
        isSkipLessThanAmtValidation: true,
        isShowUOMOption: false,
        description:
          "You can update allowance amounts for Items together or individually.",
        showFilterTabsSection: false,
        showColumnGrouping: true,
        showLeadDistributorSection: false,
        impactedFieldsOnTable: ["allowanceAmount", "includeInd"],
        filterTabs: [],
        allowGridCols: ITEM_FLAT_WAREHSE_MAIN_COLS,
        isSwitchingRequires: false,
        switchCaseOptions: {},
        vendorGridCols: {
          headerKey: "vendorDetails",
          headerLabelFunc: "renderCenterExcludeGroupHeader",
          align: "left",
          amtColmHeaderLabel: "Allowance",
          columns: isAllSummary
            ? ALL_SUMMARY_VENDOR_COLS_IF
            : ITEM_FLAT_WAREHSE_VENDOR_COLS,
          switchColumnsCase: null,
        },
      },
    },
  };
};

export const textMapping = obj => {
  const { allowanceTempWorkData, allow_type } = obj;
  const queryString = window.location.search;
  const urlParams = new URLSearchParams(queryString);
  const group = urlParams.get("group") || "";
  let allowanceType = allow_type;

  if (!allowanceType) {
    allowanceType = allowanceTempWorkData?.allowanceType;
  }

  // const groupDetails = ENTRY_TEXT_CONFIG(false)?.[allowanceType]?.[group] || {};
  const groupDetails = getEntryTextConfig(allowanceType, group, false) || {};

  return { ...groupDetails };
};

/**
 *
 * @param param0 allowanceTempWork
 * @returns This function is responsible to take allowType and Group and return the column config object
 */
export const getAllowTypeConfig = ({ allowanceTempWorkData }) => {
  if (!allowanceTempWorkData) {
    return null;
  }
  const queryString = window.location.search;
  const urlParams = new URLSearchParams(queryString);
  const group = urlParams.get("group"),
    { allowanceType } = allowanceTempWorkData;
  return (
    allowanceType &&
    group &&
    allowanceType &&
    group &&
    getEntryTextConfig(allowanceType, group, false)
    // ENTRY_TEXT_CONFIG(true)?.[allowanceType]?.[group]
  );
};

export const getTableConfigOnAllowType = (allowType, group) => {
  return allowType && group && getEntryTextConfig(allowType, group, true);
};
