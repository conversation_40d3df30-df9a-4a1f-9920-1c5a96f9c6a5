import Button from "@albertsons/uds/molecule/Button";
import Checkbox from "@albertsons/uds/molecule/Checkbox";
import Modal from "@albertsons/uds/molecule/Modal";
import { useSelectorWrap } from "@me/data-rtk";
import classNames from "classnames";
import { useDispatch } from "react-redux";
import {
  leadDistributorsHandler,
  setInitialLeadDistData,
  setIsLeadDistributorError,
} from "../../create-event/service/slice/lead-distributors-slice";
import useLeadDistributors from "../../create-event/hooks/useLeadDistributors";
import { Radio } from "@albertsons/uds/molecule/Radio";
import { useEffect, useState } from "react";
import efConstants from "../../../shared/ef-constants/ef-constants";
import LeadDistributorSubLabel from "./lead-distributor-sublabel-text";
import "./lead-distributor-modal.scss";
import { leadDistError, onCloseDistPopup } from "./billing-selection-utils";
import RenderMoveBtn from "./move-button";

type Props = {
  isModelOpen: boolean;
  setIsModelOpen: any;
};

export default function LeadDistributorsModal({
  isModelOpen = false,
  setIsModelOpen,
}: Props) {
  const {
      data: { leadOptions, stepData: selectedSteps, leadDistMode: defaultModeSelection,    leadSelectionType },
    } = useSelectorWrap("leadDistributors_rn") || {},
    { data: allowGroupConfigObj } =
      useSelectorWrap("allowanceTableColsGroupConfig_rn") || {};
  const { data: excludedVendorData } =
    useSelectorWrap("excludedVendorForAllowance_rn") || {};
  const { data: {isLeadDistributorError} } = useSelectorWrap("leadDistErrorslice_rn");
  const [leadDistMode, setLeadDistMode] = useState(
    defaultModeSelection || efConstants.LEAD_DIST_ONLY
  );
  const { leadDistConfig = {} } = allowGroupConfigObj || {};
  const dispatch = useDispatch();
  const {
    selectedList,
    stepData,
    updateSelected,
    includeToVendor,
    otherVendors,
    isExternalVendor,
    isAllExternal
  } = useLeadDistributors({
    leadOptions: leadOptions?.map(item => item?.vendorNbr),
  });

  useEffect(() => {
    isExternalVendor && setLeadDistMode(efConstants?.BILL_LEAD_DIST);
  }, [isExternalVendor]);

  useEffect(() => {
    // IF LEADS DIST ARE MORE THAN 2 THEN SHOW ERROR
    if(selectedList?.length > 2 && !isLeadDistributorError) {
      dispatch(setIsLeadDistributorError({isLeadDistributorError: true}))
    }
  },[selectedList])

  const leadDistStaticHtml = (
    <div className="text-center select-none font-bold text-[28px] mt-8">
      Select a Lead Distributor(s)
    </div>
  );
  let maxLength;
  leadOptions?.forEach(vendor => {
    if (vendor) {
      const length =
        vendor?.vendorName?.length +
        vendor?.vendorNbr?.length +
        vendor?.costAreaDesc?.length;
      if (length > maxLength?.length) {
        maxLength = length;
      }
    }
  });
  const subLabelText = <LeadDistributorSubLabel></LeadDistributorSubLabel>;
  const excludedVendorIds = excludedVendorData
    ? Object.values(excludedVendorData)
        ?.filter((item: any) => item?.isExclude)
        ?.map((e: any) => e.vendorNbr)
    : [];

  const vendorCheckBoxes = (
    <div className="pl-[10rem] pr-4">
      <section
        className="flex flex-col w-[100%] p-2 modal-cls border border-gray-500 overflow-scroll lead-dist-vendor-section"
        style={{ height: "10vw" }}
      >
        {!selectedList.length ? (
          <>
            {leadOptions
              ?.filter(item => !excludedVendorIds?.includes(item?.vendorNbr))
              .map(item => item.vendorNbr)
              .map((item, leadIndex) => (
                <VendorCheckBox
                  id={item}
                  key={item}
                  disabledMove={true}
                  selectionDisabled={false}
                  index={leadIndex}
                />
              ))}
          </>
        ) : (
          <>
            {stepData.map((step, index) => {
              return (
                <>
                  <VendorCheckBox
                    key={step.id}
                    id={step.id}
                    disabledMove={selectedList?.length < 2}
                    selectionDisabled={selectedList?.length >= 2}
                    child={step.child}
                    isMoveDirectionDown={!index}
                    index={index}
                  />
                  {index + 1 !== stepData.length && (
                    <div className="w-full h-[1px] my-1 bg-[#C8DAEB]" />
                  )}
                </>
              );
            })}
          </>
        )}
      </section>
    </div>
  );
  const onChangeHandler = e => {
    setLeadDistMode(e);
  };

  const disableLeadDistOption = leadDisOption =>
    isExternalVendor &&
    leadDistConfig?.[leadDisOption]?.value === efConstants.LEAD_DIST_ONLY;

  const leadDistModeOptions = (
    <div className="eet py-6 border-t border-b mx-5 my-8">
      <div className="flex flex-col">
        <Radio.Group
          horizontal={true}
          value={leadDistMode}
          onChange={e => onChangeHandler(e)}
          className="lead-distributor-radio-group"
        >
          {Object?.keys(leadDistConfig)?.map(leadDisOption => (
            <Radio
              label={leadDistConfig[leadDisOption]?.displayLabel}
              value={leadDistConfig[leadDisOption]?.value}
              key={leadDistConfig[leadDisOption]?.key}
              disabled={disableLeadDistOption(leadDisOption)}
            />
          ))}
        </Radio.Group>
        <div className="flex justify-around">
          <div style={{ width: "41%" }} className="text-[#5A697B]">
            {leadDistConfig?.LEAD_DIST_ONLY?.subText}
          </div>
          <div className="pl-[3.2rem] text-[#5A697B]" style={{ width: "43%" }}>
            {leadDistConfig?.BILL_LEAD_DIST?.subText}
          </div>
        </div>
      </div>
    </div>
  );
  const popperCloseArgs = {
    selectedSteps, defaultModeSelection, dispatch, setIsModelOpen, leadSelectionType
  }
  const saveButton = (
    <div className="flex items-center justify-center w-full my-4">
      <Button
        width={82}
        size="lg"
        className="mr-2 whitespace-nowrap"
        variant="secondary"
        onClick={() => onCloseDistPopup(popperCloseArgs)}
      >
        Cancel
      </Button>
      <Button
        width={92}
        size="lg"
        className="ml-2 whitespace-nowrap"
        onClick={() => {
          dispatch(
            leadDistributorsHandler({
              stepData,
              leadDistMode,
              leadSelectionType: efConstants.LEAD_DIST_LABEL,
            })
          );
          dispatch(
            setInitialLeadDistData({
              leadDistData: leadDistMode ? stepData : [],
            })
          );
          setIsModelOpen(false);
        }}
        disabled={selectedList?.length < 1 || selectedList?.length > 2}
      >
        Confirm
      </Button>
    </div>
  );

  return (
    <Modal
      isOpen={isModelOpen}
      onClose={() => onCloseDistPopup(popperCloseArgs)}
      height={630}
      width={800 + maxLength * leadOptions?.length}
      className="overflow-y-auto display-scroll"
    >
      {leadDistStaticHtml}
      {leadDistModeOptions}
      {subLabelText}
      {vendorCheckBoxes}
      {leadDistError(isLeadDistributorError)}
      {saveButton}
    </Modal>
  );

  function VendorCheckBox({
    id,
    child,
    disabledMove,
    selectionDisabled,
    isMoveDirectionDown = true,
    index,
  }: {
    id: string;
    child?: string[];
    disabledMove: boolean;
    selectionDisabled: boolean;
    isMoveDirectionDown?: boolean;
    index?: number;
  }) {
    return (
      <>
        <Checkbox
          checked={selectedList.includes(id)}
          onChange={e => {
            updateSelected({ id });
          }}
          disabled={excludedVendorIds?.includes(id)}
          key={id}
          className="py-[2px]"
        >
          {getDistributorsNameSummary(id, leadOptions)}
        </Checkbox>

        <div
          className="pl-3 w-full"
          id="abs-lead-distributors-modal-checkbox-with-multiple-children"
        >
          {child?.map(item => checkBoxWithMultipleChildren(item, index))}
        </div>
      </>
    );
    function RenderMoveBtns({ index, item }) {
      const isFirstItem = index === 0,
        isLastItem = index === stepData?.length - 1;
      return (
        <div>
          {!disabledMove && (
            <RenderMoveBtn
              item={item}
              includeToVendor={includeToVendor}
              isFirstItem={isFirstItem}
              isLastItem={isLastItem}
            />
          )}
        </div>
      );
    }
    function checkBoxWithMultipleChildren(item: string, index): JSX.Element {
      return (
        <div className="flex justify-between w-full" id={item} key={item}>
          <Checkbox
            checked={false}
            disabled={selectionDisabled || excludedVendorIds?.includes(item) || (!isAllExternal &&  otherVendors?.includes(item))}
            onChange={e => {
              updateSelected({ id: item });
            }}
            key={item}
            className={classNames({
              "py-[2px]": true,
              "[&>label]:w-full": true,
            })}
          >
            {getDistributorsNameSummary(item, leadOptions)}
          </Checkbox>
          <RenderMoveBtns item={item} index={index} />
        </div>
      );
    }
  }
}

function getDistributorsNameSummary(id, leadOptions) {
  const vendor = leadOptions.find(item => item.vendorNbr === id);
  return (
    <span>
      <span className="font-bold">{vendor?.vendorName} </span>- {id}{" "}
      {vendor?.costAreaDesc}
    </span>
  );
}
