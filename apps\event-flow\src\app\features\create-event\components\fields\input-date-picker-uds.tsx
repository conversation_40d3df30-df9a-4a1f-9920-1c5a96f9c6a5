import { FunctionComponent } from "react";
import DatePicker from "@albertsons/uds/molecule/DatePicker";
import { FormFieldError } from "@me/util-form-wrapper";
import { Controller, useFormContext } from "react-hook-form";
import { DateFormat, dateFormat } from "@me/util-helpers";

interface IInputDatePickerProps {
  fieldProps?: any;
  onChange?: any;
  customDate?: string | number;
  control?: any;
  isUDSEnabled?: any;
  minimumDate?: any;
  maximumDate?: any;
  isInvalid?: boolean;
  inValid?: boolean;
  disabled?: boolean;
  addAsterisk?: boolean;
  key?: number;
  isAllowFlow?: boolean;
  name?: string;
}

const InputDatePickerUDS: FunctionComponent<IInputDatePickerProps> = ({
  onChange,
  fieldProps,
  customDate,
  control,
  isUDSEnabled,
  minimumDate,
  maximumDate,
  isInvalid,
  inValid,
  disabled,
  addAsterisk = false,
  key = 0,
  isAllowFlow = false,
  name = "",
}) => {
  const [year, month, day] =
    ((customDate as string)?.split("-")?.map(Number) as number[]) || [];
  const { registerField } = fieldProps;
  const { register } = useFormContext();

  const handleDateChange = ([e]: Date[]) => {
    const formatedDate = dateFormat(e, DateFormat["YYYY-MM-DD"]);
    formatedDate !== "NaN-0NaN-0NaN" && onChange && onChange(formatedDate);
  };

  const getDefaultValue = () => {
    return customDate ? ([new Date(year, month - 1, day)] as Date[]) : [];
  };

  return (
    <div className="w-full" id="abs-input-date-picker-uds-main">
      <div
        className="flex font-bold gap-1"
        id="abs-input-date-picker-uds-lable-d"
      >
        <p id="abs-input-date-picker-uds-label">{fieldProps.label}</p>
        {fieldProps.required || addAsterisk ? (
          <p className="text-sm text-left text-[#bf2912]">*</p>
        ) : null}
      </div>
      <div className="w-full" id="abs-input-date-picker-uds-controler">
        <Controller
          name={name}
          control={control}
          rules={{
            required: {
              value: fieldProps?.required,
              message: fieldProps?.error?.required?.text,
            },
          }}
          render={({ fieldState: { error } }) => (
            <>
              <div
                id="abs-input-date-picker-uds-error"
                className={`inputDatePicker ${
                  error?.message || isInvalid
                    ? "border border-[red] rounded pointer-events-none bg-[#EDEEEF] "
                    : " "
                } ${inValid ? "rounded-md border border-[red]" : ""}`}
              >
                <DatePicker
                  {...register(registerField)}
                  key={`${registerField}-${key}`}
                  className={`${
                    disabled ? "pointer-events-none !bg-[#EDEEEF]" : ""
                  } ${
                    isAllowFlow
                      ? "!w-full !min-w-[100px] date-no-min"
                      : !isUDSEnabled
                      ? "pointer-events-none !bg-[#EDEEEF] date-no-min"
                      : "date-no-min"
                  }`}
                  value={getDefaultValue()}
                  onChange={e => {
                    handleDateChange(e);
                  }}
                  minDate={minimumDate}
                  maxDate={maximumDate}
                  disabled={disabled}
                  dateFormat="MM/DD/YY"
                  id={`abs-input-date-picker-uds-datepicker-${key}`}
                  configuration={
                    { firstDay: 0 }
                  }
                />
              </div>
              {error && <FormFieldError error={error}></FormFieldError>}
            </>
          )}
        />
      </div>
    </div>
  );
};

export default InputDatePickerUDS;
