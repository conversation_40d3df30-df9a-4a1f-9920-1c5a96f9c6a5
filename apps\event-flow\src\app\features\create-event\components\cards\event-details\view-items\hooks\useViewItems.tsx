import { useEffect, useState } from "react";
import { VIEW_ITEMS_TYPE_CONFIG } from "apps/event-flow/src/app/features/event-types/constants/view_items_config";

type Props = {
  isOpen: any;
  formData: any;
  isLoading: any;
  responseData: any;
  mutationMethod: any;
  setOpen: any;
  itemType: any;
};

export default function useViewItems({
  isOpen,
  formData,
  isLoading,
  responseData,
  mutationMethod,
  setOpen,
  itemType,
}: Props) {
  const [totalItems, setTotalItems] = useState(0);
  const [allItemsSum, setAllItemsSum] = useState(0);
  const [isAPICalled, setAPICalled] = useState(false);
  const { itemId, itemsKey, payloadKey } = VIEW_ITEMS_TYPE_CONFIG[itemType];

  useEffect(() => {
    if (isOpen) {
      setTotalItems(0);
      const promoIds = formData.map(p => p["id"] || p[itemId]);
      const countSum = formData.reduce( (accumulator, curObj) => {
        return accumulator + (curObj?.itemCount || 0)
      }, 0)
      setAllItemsSum(countSum)
      if (formData?.length) {
        const payload = {
          [payloadKey]: promoIds,
        };
        mutationMethod && mutationMethod(payload);
        setAPICalled(true);
      }
    }
  }, [formData, isOpen, payloadKey, itemId]);

  useEffect(() => {
    if (!isLoading && isAPICalled && !responseData) {
      setAPICalled(false);
      setOpen(false);
    }
  }, [isLoading, isAPICalled]);

  useEffect(() => {
    let total = 0;
    responseData?.map(a => {
      total += a?.["totalCount"];
    });
    setTotalItems(total);
  }, [responseData]);

  return {
    totalValidItems: totalItems,
    allItemsSum,
  };
}
