import { useGetAppBasePath } from "@me/util-helpers";
import { NavLink } from "react-router-dom";

const ViewEventLink = ({ eventId, parentEventStatus }) => {
  const { basePath } = useGetAppBasePath();
  return (
    <NavLink
      to={`${basePath}/events/edit/${eventId}`}
      className="cursor-pointer text-blue-500 flex gap-1 items-center"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="26"
        viewBox="0 0 22 22"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="lucide lucide-square-arrow-out-up-right-icon lucide-square-arrow-out-up-right"
      >
        <path d="M21 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h6" />
        <path d="m21 3-9 9" />
        <path d="M15 3h6v6" />
      </svg>
      {parentEventStatus}
    </NavLink>
  );
};
export default ViewEventLink;
