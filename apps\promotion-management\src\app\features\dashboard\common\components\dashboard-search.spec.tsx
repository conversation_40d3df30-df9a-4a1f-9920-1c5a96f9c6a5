import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { Provider } from "react-redux";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import configureStore from "redux-mock-store";
import DashboardSearch from "./dashboard-search";
import * as selectors from "@me/data-rtk";
import {
  SEARCH_KEY_CONFIG,
  SEARCH_VALUE_CONFIG,
} from "apps/promotion-management/src/app/config/dashboard-config";
import thunk from "redux-thunk";

jest.mock("apps/promotion-management/src/app/shared/ui/atoms", () => ({
  SelectDropDown: ({ id, value, setValue, items, itemKey }) => (
    <select
      data-testid={id}
      value={value}
      onChange={e =>
        setValue(items.find(item => item[itemKey] === e.target.value))
      }
    >
      {items.map(item => (
        <option key={item[itemKey]} value={item[itemKey]}>
          {item.name}
        </option>
      ))}
    </select>
  ),
}));

jest.mock("../utils/planning-sub-header-service", () => ({
  processSearchText: jest.fn(),
}));

jest.mock("lodash", () => ({
  isEmpty: jest.fn(),
}));

const mockStore = configureStore([thunk]);
let store;

const renderComponent = props =>
  render(
    <Provider store={store}>
      <BrowserRouter>
        <DashboardSearch {...props} />
      </BrowserRouter>
    </Provider>
  );

describe("DashboardSearch useEffect hooks", () => {
  const props = {
    action: jest.fn().mockReturnValue({ type: "test" }),
    sliceKey: "testSlice",
    viewType: "planningView",
    viewKey: "planningView",
  };

  beforeEach(() => {
    store = mockStore({
      expandedDefaultView_rn: { data: { isOpen: false } },
      validateDuplicateEventsRes_rn: { data: [] },
      testSlice: {
        data: {
          searchObj: {},
        },
      },
    });
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      switch (sel_name) {
        case "duplicateModal_rn":
          return { data: { isOpen: false } };
        case "validateDuplicateEventsRes_rn":
          return { data: [] };
        case "expandedDefaultView_rn":
          return { data: { searchObj: { value: "test" } } };
        default:
          return { data: {} };
      }
    });
  });

  test("should set value and key when isValidEventIds is true", () => {
    store = mockStore({
      expandedDefaultView_rn: { data: { isOpen: false } },
      validateDuplicateEventsRes_rn: {
        data: [{ eventIdNumber: 1 }, { eventIdNumber: 2 }],
      },
      duplicateModal_rn: { data: { isOpen: false } },
      testSlice: {
        data: {
          searchObj: {},
        },
      },
    });
    renderComponent(props);
    expect(
      (
        screen.getByPlaceholderText(
          SEARCH_VALUE_CONFIG.extraConfig.placeHolder
        ) as HTMLInputElement
      )?.value
    ).toBe("");
  });

  test("should update value and key from filterData", () => {
    store = mockStore({
      expandedDefaultView_rn: { data: { isOpen: false } },
      validateDuplicateEventsRes_rn: { data: [] },
      testSlice: {
        data: {
          searchObj: {
            key: SEARCH_KEY_CONFIG.options[1].eventKey,
            rawString: "test search",
          },
        },
      },
    });
    renderComponent(props);
    expect(
      (
        screen.getByPlaceholderText(
          SEARCH_VALUE_CONFIG.extraConfig.placeHolder
        ) as HTMLInputElement
      )?.value
    ).toBe("");
  });

  test("should not update value and key from filterData if searchObj is empty", () => {
    store = mockStore({
      expandedDefaultView_rn: { data: { isOpen: false } },
      validateDuplicateEventsRes_rn: { data: [] },
      testSlice: {
        data: {
          searchObj: {},
        },
      },
    });
    renderComponent(props);
    expect(
      (
        screen.getByPlaceholderText(
          SEARCH_VALUE_CONFIG.extraConfig.placeHolder
        ) as HTMLInputElement
      )?.value
    ).toBe("");
  });

  test("should render search input with placeholder", () => {
    renderComponent(props);
    const searchInput = screen.getByPlaceholderText(
      SEARCH_VALUE_CONFIG.extraConfig.placeHolder
    );
    expect(searchInput).toBeTruthy();
  });

  test("should not display error message when search input is valid", async () => {
    renderComponent(props);
    const searchInput = screen.getByPlaceholderText(
      SEARCH_VALUE_CONFIG.extraConfig.placeHolder
    );
    await fireEvent.change(searchInput, { target: { value: "valid search value" } });
    fireEvent.keyDown(searchInput, { key: "Enter", code: "Enter" });
    const errorMessage = screen.queryByText("Please check your input!");
    expect(errorMessage).not.toBeTruthy();
  });
    test("should not display error message when search input is valid", async () => {
    renderComponent({...props});
    const searchInput = screen.getByPlaceholderText(
      SEARCH_VALUE_CONFIG.extraConfig.placeHolder
    );
    sessionStorage.setItem("searchValue", "1234");
    await fireEvent.change(searchInput, {
      target: { value: "1234" },
    });
    await fireEvent.keyDown(searchInput, { key: "Enter", code: "Enter" });
    const errorMessage = screen.queryByText("Please check your input!");
    expect(errorMessage).not.toBeTruthy();
  });
});
