import { render, renderHook } from "@testing-library/react";
import usePromoSummaryForUOM from "./usePromoSummaryForUOM";
import { Provider } from "react-redux";
import { FormProvider, useForm } from "react-hook-form";
import { app_store } from "@me/data-rtk";

import * as RTK_TOOLS from "@me/data-rtk";
import * as ALLOW_API from "../../../../../service/apis/allowance-api";

const Wrapper = props => {
  const formMethods = useForm<any>({
    defaultValues: {
      promotionsLists: [
        {
          promotionsList: [
            {
              vehicle: {
                vehicleId: "656644934710ba7ffc49afe6",
                vehicleNm: "27 Week 48 Insert 2024        ",
                sourceVehicleSk: 59452,
                startDate: "2024-11-27",
                endDate: "2024-12-03",
                vehicleType: {
                  vehicleTypeId: "636abba1b426ee543a94d3ac",
                  sourceVehicleTypeSk: 198,
                  vehicleTypNm: "insrt",
                  vehicleTypDesc: "Weekly Insert",
                },
              },
              promoStartDate: "2024-11-27",
              promoEndDate: "2024-12-03",
            },
          ],
        },
      ],
    },
  });
  return (
    <Provider store={app_store}>
      <FormProvider {...formMethods}>{props.children}</FormProvider>
    </Provider>
  );
};

const mockPlanEventResp = [
  {
    eventId: "65bc3a8aeaa60d774c45aa8e",
    productSources: ["DSD"],
    itemVendorTypes: ["DSD"],
    plannedProductGroupId: "636bde8d9665d0440e006e42",
    plannedEventItemsFilters: [
      {
        itemIds: [2014079],
        divisions: ["27"],
        stores: [],
        warehouse: [],
        promoStartDate: "2024-11-27",
      },
    ],
    plannedProductGroupItems: [
      {
        itemId: "2014079",
        itemDescription: "SIMPLE MILLS COOKIES SOFT BAK CHC CHIP  ",
        primaryUpc: "085606900538",
        caseUPC: "0085606900538",
        pack: 0,
        rogPackRetails: {
          rogPrimaryPackRetailMap: {
            SACG: 6,
            SSEA: 6,
          },
          primaryUpcFirstPackRetail: 6,
        },
        descriptiveSize: "6.2 OZ",
        plannedCicUpcs: [
          {
            upcDetail: "085606900538",
            statusUpc: " ",
            rogs: [
              {
                rog: "SACG",
                primaryUpcSwitch: "Y",
                productSourceIndicator: "D",
                division: "27",
                retailStatus: "V",
                unitType: 1,
                packRetail: 6,
                retailUpcStatusCode: "V",
                packDescription: "006",
                sizeDescription: "6.2 OZ ",
                pos: {
                  posItemDescription: "SIMPLE MILLS COOKI",
                  ringTypeCode: 0,
                },
              },
              {
                rog: "SSEA",
                primaryUpcSwitch: "Y",
                productSourceIndicator: "D",
                division: "27",
                retailStatus: "V",
                unitType: 1,
                packRetail: 6,
                retailUpcStatusCode: "V",
                packDescription: "006",
                sizeDescription: "6.2 OZ ",
                pos: {
                  posItemDescription: "SIMPLE MILLS COOKI",
                  ringTypeCode: 0,
                },
              },
            ],
          },
        ],
        randomWeightCodes: [" "],
        buyUomTypes: ["CA"],
        sellUomTypes: ["EA"],
        dsdCostAggrs: [],
        warehouseCosts: [
          {
            warehouseCostOrigin: "LOCATION",
            rogs: ["SSEA"],
            division: "",
            rogDivisions: ["27"],
            facility: "DDSE",
            distributionCenter: "DDSE",
            vendorNumber: "006446",
            vendorName: "KEHE DISTRIBUTORS                       ",
            vendorSubAccount: "001",
            wimsSubvendorNumber: "    ",
            itemOnNewDsdSwitch: "Y",
          },
          {
            warehouseCostOrigin: "LOCATION",
            rogs: ["SACG", "SSEA"],
            division: "",
            rogDivisions: ["27"],
            facility: "DDSE",
            distributionCenter: "DDSE",
            vendorNumber: "006446",
            vendorName: "KEHE DISTRIBUTORS {JBG WHLSALE}         ",
            vendorSubAccount: "013",
            wimsSubvendorNumber: "    ",
            itemOnNewDsdSwitch: "Y",
          },
        ],
        productSources: ["DSD"],
        itemVendorTypes: ["DSD"],
        vendorPackConversionFactor: 1,
        smic: {
          groupDescription: "COOKIES/CRACKERS & MISC SNACKS",
          categoryDescription: "COOKIES",
          retailSectionId: "338",
          groupCode: 2,
          categoryCode: 1,
        },
        smicCategoryCode: "0201",
        displayItemFlag: " ",
      },
    ],
    invalidPlannedProductGroupItems: [],
    invalidItemStatuses: [],
    createTs: *************,
    planEventVendors: [],
    storeGroupOverrideInd: false,
  },
];

const mockPlanEventRespCA = [
  {
    plannedProductGroupItems: [
      {
        buyUomTypes: ["EA"],
        sellUomTypes: ["CA"],
      },
    ],
  },
];

describe("use promo sumary uom test suite", () => {
  beforeEach(() => {
    jest
      .spyOn(RTK_TOOLS, "useSelectorWrap")
      .mockImplementation(selector_name => {
        switch (selector_name) {
          case "DSD_WHSE_RETAIL_DIVISION":
            return [{}];
          case "event_details_data":
            return {
              data: {
                name: "185036 - Simple Mills Soft Baked Cookies - 84964 - S - 1 - 27 Week 48 Insert 2024",
                startDate: "2024-11-27",
                endDate: "2024-12-03",
                id: 111,
                divisionIds: ["27"],
              },
            };
          default:
            return [{}];
        }
      });
  });
  it("should show correct value for a sellUOM of EA", () => {
    jest
      .spyOn(ALLOW_API, "usePostAllowanceStartDateDataMutation")
      .mockReturnValue([
        jest.fn(() => {
          return Promise.resolve();
        }),
        { data: mockPlanEventResp, isLoading: false, isSuccess: true },
      ]);

    const { result } = renderHook(
      () => usePromoSummaryForUOM({ cardIndex: 0, cardItemIndex: 0 }),
      { wrapper: Wrapper }
    );

    expect(result.current.defaultUomValue).toEqual("EA");
  });

  it("should show correct value for a sellUOM of CA", () => {
    jest
      .spyOn(ALLOW_API, "usePostAllowanceStartDateDataMutation")
      .mockReturnValue([
        jest.fn(() => {
          return Promise.resolve();
        }),
        { data: mockPlanEventRespCA, isLoading: false, isSuccess: true },
      ]);

    const { result } = renderHook(
      () => usePromoSummaryForUOM({ cardIndex: 0, cardItemIndex: 0 }),
      { wrapper: Wrapper }
    );

    expect(result.current.defaultUomValue).toEqual("CA");
  });
});
