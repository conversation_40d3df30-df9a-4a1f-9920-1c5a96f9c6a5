import { useEffect, useMemo, useRef, useState } from "react";
import { useSelectorWrap } from "@me/data-rtk";
import { EVENT_DATE_INFO_MSG, SECTION_KEYS } from "../offer-flow-config";
import {
  checkElementRendered,
  checkIsNationalEvent,
  scrollToActiveSection,
  setFocusedSection,
} from "../offer-service";

import PrimeSection from "./prime";
import DatesSection from "./dates";
import AmmountSection from "./amounts";
import BillingSection from "./billing";
import NationalBillingSection from "../national/billing";
import { warningIconMaroon } from "@me-upp-js/utilities";

interface IOfferSectionProps {
  cardIndex: number;
  cardItemIndex: number;
  isEditEnable: boolean;
}

const OfferSections = ({
  cardIndex = 0,
  cardItemIndex = 0,
  isEditEnable = false,
}: IOfferSectionProps) => {
  const offerSections = useSelectorWrap("offer_sections_data")?.data || [];
  const offerSectionsEnableConfig =
    useSelectorWrap("offer_sections_enable_config")?.data || {};
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const [activeSection, setActiveSection] = useState<string | null>(null);
  const sectionRefs = useRef<{ [key: string]: HTMLElement | null }>({});

  const { offerAllowances = [], inValidAllowances = [] } =
    eventDetailsData || {};
  const isOfferInvalid = inValidAllowances?.includes(
    offerAllowances?.[cardIndex]?.id
  );
  const isNationalEvent = checkIsNationalEvent(eventDetailsData?.eventType);
  const inValidDatesInfoMsg = isOfferInvalid && (
    <div className="flex w-full text-[#9D2210] text-sm gap-3 mb-4">
      <span>{warningIconMaroon}</span>
      <span>{EVENT_DATE_INFO_MSG}</span>
    </div>
  );

  const getSection = (sectionKey: string) => {
    switch (sectionKey) {
      case SECTION_KEYS.ALLOWANCE_PRIME_SECTION:
        return PrimeSection;
      case SECTION_KEYS.ALLOWANCE_DATES:
        return DatesSection;
      case SECTION_KEYS.ALLOWANCE_AMOUNT:
        return AmmountSection;
      case SECTION_KEYS.ALLOWANCE_BILLING:
        return isNationalEvent ? NationalBillingSection : BillingSection;
      default:
        return null;
    }
  };

  useEffect(() => {
    if (
      activeSection &&
      sectionRefs.current[activeSection] &&
      checkElementRendered(activeSection && sectionRefs.current[activeSection])
    ) {
      scrollToActiveSection(sectionRefs, activeSection);
    }
  }, [
    activeSection,
    checkElementRendered(activeSection && sectionRefs.current[activeSection]),
  ]);

  const OfferSectionContent = useMemo(
    () =>
      offerSections?.map((section, index) => {
        const SectionComponent = getSection(section.sectionKey);
        setFocusedSection(section, offerSectionsEnableConfig, setActiveSection);
        return offerSectionsEnableConfig?.[section.key]?.isActive &&
          SectionComponent ? (
          <section
            key={section?.key}
            ref={el => (sectionRefs.current[section?.sectionKey] = el)}
            id={section?.containerId}
            className={` border-[#C8DAEB] ${
              index === offerSections?.length - 1 ? "border-b-[1px]" : ""
            } ${index !== 0 ? "border-t-[1px] pt-2" : ""}`}
          >
            <SectionComponent
              sectionKey={section.key}
              cardIndex={cardIndex}
              cardItemIndex={cardItemIndex}
              isEditEnable={isEditEnable}
              isLastStep={index === offerSections?.length - 1}
            />
          </section>
        ) : (
          <div
            className={`text-[#98A1BE] py-3 border-t-[1px] border-[#C8DAEB] ${
              index === offerSections?.length - 1 ? "border-b-[1px]" : ""
            }`}
            key={section?.key}
          >
            {section?.label}
          </div>
        );
      }),
    [
      JSON.stringify(offerSections),
      JSON.stringify(offerSectionsEnableConfig),
      cardIndex,
      cardItemIndex,
    ]
  );

  return (
    <div>
      {inValidDatesInfoMsg}
      {OfferSectionContent}
    </div>
  );
};

export default OfferSections;
