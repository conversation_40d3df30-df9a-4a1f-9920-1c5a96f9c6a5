import React from "react";
import { SkeletonLoader } from "../../../shared/ui/atoms";
import Tooltip from "@albertsons/uds/molecule/Tooltip";
import pmConstants from "../../../shared/pm-constants/pm-constants";
import classNames from "classnames";
import { useGetAppBaseNationalIcon } from "@me-upp-js/utilities";
import { EVENT_TYPE } from "../../../constants";

function RenderEventNamePopover({task, isLoading}) {
  const nationalIcon = useGetAppBaseNationalIcon();
  const renderedUI = !isLoading ? (
    <SkeletonLoader height={100} width={300} />
  ) : (
    <Tooltip zIndex={10} anchor="bottom">
      <Tooltip.Popover>
        {task?.planProductGroups?.length && (
          <div
            className={`flex flex-col mx-4 mt-4 w-fit overflow-auto rounded ${pmConstants.componentClassName.RENDER_EVENT_NAME_WITH_PPG_ON_HOVER}`}
          >
            {task?.planProductGroups?.map((ppg, index) => {
              return (
                <div className="h-[37px] text-black text-sm font-normal leading-4">
                  {ppg?.planProductGroupName}
                </div>
              );
            })}
          </div>
        )}
      </Tooltip.Popover>

      <div
        className={classNames({
          "text-brand text-[#1B6EBB] cursor-pointer truncate w-[30vw] font-bold text-base flex":
            true,
        })}
      >
        {task?.eventType === EVENT_TYPE.NDP && nationalIcon && (
          <img
            src={nationalIcon}
            className="h-6 w-6 mr-1"
            alt="national-icon"
          />
        )}
        {task?.eventType === EVENT_TYPE.NCDP
          ? `${
              task?.parentEvent?.planEventIdNbr
                ? task?.parentEvent?.planEventIdNbr
                : task?.planEventIdNbr
            } - ${task?.eventName}`
          : `${task?.planEventIdNbr} - ${task?.eventName}`}
      </div>
    </Tooltip>
  );
  return renderedUI;
}

export default React.memo(RenderEventNamePopover);
