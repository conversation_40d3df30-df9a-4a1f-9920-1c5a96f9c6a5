import { InputText } from "@me/input-fields";
import { useFormContext } from "react-hook-form";
import Button from "@albertsons/uds/molecule/Button";
import { handleKeyDown } from "apps/event-flow/src/app/shared/helpers/event-flow-helpers";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import { useLazyGetEventPeriscopeDataQuery } from "../../../../../service/apis/event-api";
import { RenderStates } from "@me/ui-render-states";
import { useDispatch } from "react-redux";
import { eventDetailsDataHandler } from "../../../../../service/slice/event-detail-slice";
import { modifyEventDetailsData } from "../../event-details-helper";
import DEFAULT_EVENT_DETAILS from "apps/event-flow/src/app/features/create-event/constants/event-details/eventDetails";
import { periscopeIdChangesEnabled } from "@me-upp-js/utilities";
import {
  getIsValidDivisionIdForNationalEvent,
  isPeriscopeValidFromApi,
  periscopeFieldEnableStatus,
} from "../../utility/utility";
import { useSelectorWrap } from "@me/data-rtk";
import Alert from "@albertsons/uds/molecule/Alert";

function PeriscopeField({
  periscopeField,
  formFields,
  setFormFields,
  setStoreGroupFieldChanged,
  eventType,
}) {
  const [
    getEventDetailsWithPeriscope,
    { isFetching: isEventDetailsFectcedFromPeriscope },
  ] = useLazyGetEventPeriscopeDataQuery({
    URL_PARAM: formFields?.periscopeIdState,
  });
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const {
    PERISCOPE_LABELS: { BUTTON_LABEL },
  } = efConstants;
  const dispatch = useDispatch();
  const { setValue, getValues } = useFormContext();
  const { piDButtonEnable } = periscopeFieldEnableStatus(getValues);

  const handlePidValidations = e => {
    const regex = /^\d{0,10}$/;
    const val = e?.target?.value;
    if (regex.test(val)) {
      setValue("periscopeFormField", val);
      setFormFields(prevState => ({
        ...prevState,
        periscopeIdState: val,
        warningCount: 0,
      }));
      setValue("periscopeFormField", val);
    }
    if (!val) {
      setFormFields(prevState => ({
        ...prevState,
        periscopeIdState: val,
        periscopeValid: true,
        periscopeValidFromApi: true,
      }));
    }
  };
  const handleFetchEventDetailsFromPeriscope = async e => {
    e?.stopPropagation();
    e?.preventDefault();
    const val = formFields?.periscopeIdState;
    if (val) {
      const { data: eventDetailsDataFetchedFromPeriscope } =
        await getEventDetailsWithPeriscope({
          URL_PARAM: val,
        });
      const checkValidDivisions = getIsValidDivisionIdForNationalEvent(
        eventType,
        eventDetailsDataFetchedFromPeriscope?.divisionIds ?? []
      );
      const validPID = isPeriscopeValidFromApi(
        eventDetailsDataFetchedFromPeriscope?.periscopeDetails
      );
      const modifiedEventDetailsData =
        validPID && checkValidDivisions
          ? modifyEventDetailsData(
              eventDetailsDataFetchedFromPeriscope &&
                eventDetailsDataFetchedFromPeriscope.divisionIds?.includes("98")
                ? {
                    ...eventDetailsDataFetchedFromPeriscope,
                    eventType: "NDP",
                    eventTypeEnum: "NDP",
                  }
                : eventDetailsDataFetchedFromPeriscope
            )
          : modifyEventDetailsData({
              ...eventDetailsData,
              periscopeDetails:
                eventDetailsDataFetchedFromPeriscope?.periscopeDetails ?? {},
            });

      const periscopeFetchCount =
        (eventDetailsData?.periscopeFetchCount || 0) + 1;

      if (checkValidDivisions && validPID) {
        dispatch(
          eventDetailsDataHandler({
            ...DEFAULT_EVENT_DETAILS,
            ...modifiedEventDetailsData,
            isChangeEventTypeVisible: false,
            dataFetchedFromPeriscope: true,
            periscopeValid: validPID,
            periscopeValidFromApi: validPID,
            periscopeFetchCount,
          })
        );
        return modifiedEventDetailsData;
      } else {
        setFormFields(prevState => ({
          ...prevState,
          periscopeValid: checkValidDivisions && validPID,
          periscopeValidFromApi: checkValidDivisions && validPID,
        }));
        dispatch(
          eventDetailsDataHandler({
            ...modifiedEventDetailsData,
            periscopeValid: checkValidDivisions && validPID,
            periscopeValidFromApi: checkValidDivisions && validPID,
            periscopeFetchCount,
          })
        );
      }
    }
  };
  const handleOnEnter = e => {
    if (
      e?.keyCode === 13 &&
      periscopeIdChangesEnabled() &&
      formFields?.periscopeIdState &&
      piDButtonEnable
    ) {
      e?.stopPropagation();
      e?.preventDefault();
      handleFetchEventDetailsFromPeriscope(e);
      setStoreGroupFieldChanged(false);
    }
  };
  const alignItemsClass =
    !formFields?.periscopeValid ||
    (!formFields?.periscopeValidFromApi &&
      periscopeIdChangesEnabled() &&
      formFields?.periscopeIdState)
      ? "items-start"
      : "items-center";

  const renderHtml = (
    <div className={`flex flex-grow-0 flex-shrink-0 mb-4 ${alignItemsClass}`}>
      <div className="w-[220px] mr-4">
        <InputText
          fieldProps={periscopeField}
          type="number"
          onChange={e => handlePidValidations(e)}
          builtInError={
            !formFields?.periscopeValid || !formFields?.periscopeValidFromApi
          }
          defaultValue={formFields?.periscopeIdState}
          onKeyDown={e => {
            handleKeyDown(e);
            handleOnEnter(e);
          }}
        />
      </div>
      {periscopeIdChangesEnabled() ? (
        <div className="periscope-fetch-details mt-6">
          <Button
            onClick={e => handleFetchEventDetailsFromPeriscope(e)}
            width={165}
            variant="secondary"
            disabled={!piDButtonEnable}
          >
            {BUTTON_LABEL}
          </Button>
        </div>
      ) : null}
    </div>
  );
  const renderDetails = {
    isApiLoading: isEventDetailsFectcedFromPeriscope,
    isPageLevelSpinner: true,
    isRenderMainHtml: true,
    renderHtml,
  };
  return <RenderStates details={renderDetails} />;
}

export default PeriscopeField;
