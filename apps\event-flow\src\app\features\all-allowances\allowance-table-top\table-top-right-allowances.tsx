import Search from "@albertsons/uds/molecule/Search";
import { useSelectorWrap } from "@me/data-rtk";
import { SidebarDrawer } from "@me/ui-side-drawer";
import FacetFilterContainer from "libs/features/feature-primary-filters/src/lib/facet-filters/components/containers/facet-filter-container";
import {
  setIsSelectedAll,
  setSelectedFilters,
} from "libs/features/feature-primary-filters/src/lib/facet-filters/components/fields/filter-item-slice";
import _ from "lodash";
import { useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import Toggle from "@albertsons/uds/molecule/Toggle";
import Divider from "@albertsons/uds/molecule/Divider";
import { setSelectedSwitchValue } from "../../create-event/service/slice/allowances-dashboard-slices";
import { getFilterFormedObject } from "../nationals/service/table-service";
import { cloneDeep } from "lodash";
import { isNationalType } from "../allowance-lead-distributors/billing-selection-utils";

export default function TableTopRightAllowances({ isSummary }) {
  const [switchEnabled, setSwitchEnabled] = useState<boolean>(false);
  const dispatch = useDispatch();
  const [isOpenDrawer, setIsOpenDrawer] = useState(false);
  const {
    data: { tableData },
  } = useSelectorWrap("allowanceTableData_rn");
  const { data: switchValue } = useSelectorWrap("selectedSwitchValue_rn");
  const { data: selectedFiltersData } = useSelectorWrap("selectedFilters_rn"),
    { data: allowGroupConfigObj } = useSelectorWrap(
      "allowanceTableColsGroupConfig_rn"
    ),
    {
      isSwitchingRequires = false,
      switchCaseOptions,
      allowGridCols,
      vendorGridCols,
    } = allowGroupConfigObj || {};
  const filterObjData = useRef(null);
  const [filterDisplayObjData, setFilterDisplayObjData] = useState(null);
  const { selectedDivisionData = {}, isNdpType } =
    useSelectorWrap("national_divisions_config")?.data || {};
  const isNational = isNationalType(isNdpType);
  useEffect(() => {
    handle_ResetFilter();
  }, [switchValue?.selectedSwitch]);

  useEffect(() => {
    if (!switchValue || !vendorGridCols || !allowGridCols) return;
    filterObjData.current = getFilterFormedObject({
      switchValue,
      vendorGridCols,
      allowGridCols,
    });
  }, [
    allowGridCols,
    vendorGridCols,
    switchValue,
    selectedDivisionData?.divisionId,
  ]);

  useEffect(() => {
    isNational && handle_ResetFilter();
  }, [selectedDivisionData?.divisionId]);

  useEffect(() => {
    if (!allowGroupConfigObj) return;

    setSwitchEnabled(allowGroupConfigObj?.allowanceTypeDisplay === "Scan");
  }, [allowGroupConfigObj]);

  const handle_ResetFilter = () => {
    setSearchVal("");
    dispatch(setSelectedFilters({ isFilterReset: true }));
    dispatch(
      setIsSelectedAll({
        isSelectedAll: null,
        dataProp_selectedAll: null,
      })
    );
  };

  const updateFilterValue = (filterKey, value, formedData) => {
    if (
      !formedData?.[filterKey]?.filterValues?.includes(value) &&
      !_.isNil(value)
    ) {
      formedData?.[filterKey]?.filterValues?.push(value);
    }
  };

  const updateFilterDisplayWithValues = () => {
    if (_.isEmpty(tableData)) return;
    const formedData = cloneDeep(filterObjData.current);
    tableData.forEach(productGroupItem => {
      const { packWhse, size, vendorDetails } = productGroupItem;
      updateFilterValue("packWhse", packWhse, formedData);
      updateFilterValue("size", size, formedData);

      vendorDetails.forEach(
        ({
          caseListCost,
          unitListCost,
          unitCostAllow,
          caseCostAllow,
          shippingCost,
        }) => {
          updateFilterValue("caseListCost", caseListCost, formedData);
          updateFilterValue("unitListCost", unitListCost, formedData);
          updateFilterValue("unitCostAllow", unitCostAllow, formedData);
          updateFilterValue("caseCostAllow", caseCostAllow, formedData);
          updateFilterValue("shippingCost", shippingCost, formedData);
        }
      );
    });
    formedData &&
      Object.keys(formedData)?.forEach(key => {
        formedData?.[key].filterValues?.sort((a, b) =>
          typeof a === "number" ? a - b : a.localeCompare(b)
        );
      });
    setFilterDisplayObjData(formedData);
  };

  const [filterCategoriesCount, setFilterCategoriesCount] = useState(0);

  const get_FilterCategoriesCount = () => {
    const { filteredOptions, isFilterApplied, isFilterReset } =
      selectedFiltersData;
    let count: any = null;
    if (isFilterApplied) {
      const uniqueFilters = Object.keys(filteredOptions).filter(
        item => filteredOptions[item].length > 0
      );
      count = uniqueFilters.length;
    } else if (isFilterReset) {
      count = 0;
    }

    !_.isNull(count) && setFilterCategoriesCount(count);
  };

  useEffect(() => {
    updateFilterDisplayWithValues();
  }, [JSON.stringify(tableData), JSON.stringify(filterObjData?.current)]);

  useEffect(() => {
    get_FilterCategoriesCount();
  }, [selectedFiltersData]);

  /**
   * This function check if switchingRequires for allowType.
   * If Yes, then dispatch the current switch selected value based on config from Allowance group
   *
   * For Example: Switch On for case means Unit selected, if Off Case selected
   * so setting dynamicall from config file
   */
  const setSwitchSelectedValue = () => {
    if (switchCaseOptions && isSwitchingRequires) {
      const switchOnValue = switchCaseOptions?.["switchOn"]?.value,
        switchOffValue = switchCaseOptions["switchOff"]?.value;
      dispatch(
        setSelectedSwitchValue({
          selectedSwitch: switchEnabled ? switchOnValue : switchOffValue,
        })
      );
    }
  };

  useEffect(() => {
    setSwitchSelectedValue();
  }, [switchEnabled, allowGroupConfigObj]);

  const filterDrawer = (
    <div
      id="abs-table-top-right-allowances-filter-drawer"
      className="flex flex-col justify-center items-center flex-grow-0 flex-shrink-0 h-8 relative rounded"
    >
      <div
        id="abs-table-top-right-allowances-filter-drawer-stretch"
        className="self-stretch flex-grow-0 flex-shrink-0 h-1 rounded-tl rounded-tr"
      ></div>
      <div
        id="abs-table-top-right-allowances-filter-drawer-content"
        className="flex flex-col justify-center items-center flex-grow overflow-hidden px-1 rounded-tl rounded-tr"
      >
        <div
          className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-2 cursor-pointer"
          role={"tabpanel"}
          onClick={() => setIsOpenDrawer(!isOpenDrawer)}
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="flex-grow-0 flex-shrink-0 w-6 h-6 relative"
            preserveAspectRatio="xMidYMid meet"
          >
            <path
              d="M10 18H14V16H10V18ZM3 6V8H21V6H3ZM6 13H18V11H6V13Z"
              fill="#1B6EBB"
            ></path>
          </svg>
          <p className="flex-grow-0 flex-shrink-0 text-base font-semibold text-center text-[#1b6ebb]">
            <span>
              Filters{" "}
              {filterCategoriesCount > 0 ? `(${filterCategoriesCount})` : ""}
            </span>
          </p>
        </div>
        <SidebarDrawer
          isOpen={isOpenDrawer}
          setOpen={setIsOpenDrawer}
          headerContent="Filters"
        >
          <FacetFilterContainer
            setIsOpenDrawer={setIsOpenDrawer}
            showSideFilterView={true}
            filterDisplayObj={filterDisplayObjData}
          />
        </SidebarDrawer>
      </div>
      <div className="self-stretch flex-grow-0 flex-shrink-0 h-1 rounded-bl rounded-br"></div>
    </div>
  );
  /**
   * Adding switch button based on config, and showing text
   * For Example:  when switch on need to show Change to Case
   * so getting the text from config dynamically and showing it
   */
  const switchBtn = (
    <>
      <Toggle checked={switchEnabled} onChange={setSwitchEnabled} />
      {switchEnabled ? (
        <span id="abs-table-top-right-allowances-switch-text-one">
          {switchCaseOptions?.["switchOn"]?.text}
        </span>
      ) : (
        <span id="abs-table-top-right-allowances-switch-text-two">
          {switchCaseOptions?.["switchOff"]?.text}
        </span>
      )}
      <Divider className="mx-3" height={24} color="#C8DAEB" />
    </>
  );
  const resetBtn = (
    <div
      className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0"
      id="abs-table-top-right-allowances-reset-btn-main-div"
    >
      <div
        className="flex flex-col justify-center items-center flex-grow-0 flex-shrink-0 h-8 overflow-hidden gap-2.5 px-2 py-0.5 rounded-lg"
        id="abs-table-top-right-allowances-reset-btn-sub-div"
      >
        <div
          onClick={handle_ResetFilter}
          className="flex cursor-pointer justify-center items-center flex-grow-0 flex-shrink-0 relative gap-2"
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="flex-grow-0 flex-shrink-0 w-6 h-6 relative"
            preserveAspectRatio="none"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M6.35023 6.35C7.80023 4.9 9.79023 4 12.0002 4C16.4202 4 19.9902 7.58 19.9902 12C19.9902 16.42 16.4202 20 12.0002 20C8.27023 20 5.16023 17.45 4.27023 14H6.35023C7.17023 16.33 9.39023 18 12.0002 18C15.3102 18 18.0002 15.31 18.0002 12C18.0002 8.69 15.3102 6 12.0002 6C10.3402 6 8.86023 6.69 7.78023 7.78L11.0002 11H4.00023V4L6.35023 6.35Z"
              fill="#1B6EBB"
            ></path>
          </svg>
          <p
            id="abs-table-top-right-allowances-reset-text"
            className="flex-grow-0 flex-shrink-0 text-base font-semibold text-center text-[#1b6ebb]"
          >
            Clear Filters
          </p>
        </div>
      </div>
    </div>
  );

  const set_SearchInput = query => {
    dispatch(
      setSelectedFilters({
        ...selectedFiltersData,
        searchOptions: [query],
        isFilterApplied: true,
        isFilterReset: false,
      })
    );
  };

  const [searchVal, setSearchVal] = useState("");

  const handleSearchQuery = (query: string) => {
    setSearchVal(query);
    set_SearchInput(query);
  };
  const searchInput = (
    <div className="w-30">
      <Search
        value={searchVal}
        onChange={handleSearchQuery}
        placeholder="Search"
        variant="simple"
      />
    </div>
  );
  /**
   *
   * @returns Warning message when we do switching
   * Ex-  For unit, we need to show warning message, when we switch to Unit.
   */
  const showWarningOnSwitch = () => {
    if (!isSwitchingRequires) return "";
    const { isOnlyDisplay = false, warningMsg } =
      switchCaseOptions?.[switchEnabled ? "switchOn" : "switchOff"] || {};
    return (
      <div
        id="abs-table-top-right-allowances-warning-message"
        className="text-left text-[#bf2912]"
      >
        {isOnlyDisplay ? warningMsg : ""}
      </div>
    );
  };

  return (
    <div
      id="abs-table-top-right-allowances-is-switching-requires"
      className={`${isNational ? "w-[40%] flex-1" : isSummary ? "flex-1" : ""}`}
    >
      <div
        className="flex justify-end items-center gap-4 mainentry-switch"
        id="abs-table-top-right-allowances-is-switching-requires-justify"
      >
        {isSwitchingRequires && !isSummary ? switchBtn : null}
        {resetBtn}
        {filterDrawer}
        {searchInput}
      </div>
      <div id="abs-table-top-right-allowances-show-warning-onswitch">
        {showWarningOnSwitch()}
      </div>
    </div>
  );
}
