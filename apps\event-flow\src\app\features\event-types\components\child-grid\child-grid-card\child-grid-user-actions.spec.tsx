import { render, screen } from "@testing-library/react";
import ChildGridUserActions from "./child-grid-user-actions";
import "@testing-library/jest-dom";

jest.mock("./child-events-actions", () => jest.fn(() => <div data-testid="child-event-actions">Child Event Actions</div>));
jest.mock("./child-offer-actions", () => jest.fn(() => <div data-testid="child-offer-actions">Child Offer Actions</div>));

describe("ChildGridUserActions", () => {
  it("renders ChildEventActions when selectedTabConfig key is 'events'", () => {
    render(
      <ChildGridUserActions
        cardIndex={1}
        selectedTabConfig={{ key: "events" }}
      />
    );

    const childEventActions = screen.getByTestId("child-event-actions");
    expect(childEventActions).toBeInTheDocument();
    expect(childEventActions).toHaveTextContent("Child Event Actions");
  });

  it("renders ChildOfferActions when selectedTabConfig key is 'offers'", () => {
    render(
      <ChildGridUserActions
        cardIndex={2}
        selectedTabConfig={{ key: "offers" }}
      />
    );

    const childOfferActions = screen.getByTestId("child-offer-actions");
    expect(childOfferActions).toBeInTheDocument();
    expect(childOfferActions).toHaveTextContent("Child Offer Actions");
  });

  it("renders an empty div when selectedTabConfig key is not 'events' or 'offers'", () => {
    render(
      <ChildGridUserActions
        cardIndex={3}
        selectedTabConfig={{ key: "unknown" }}
      />
    );

    const parentDiv = screen.getByText((content, element) => {
      return element?.tagName === "DIV" && element.className.includes("flex");
    });

    const emptyDiv = parentDiv.querySelector("div");
    expect(emptyDiv).toBeInTheDocument();
    expect(emptyDiv).toBeEmptyDOMElement();
  });

it("renders an empty div when selectedTabConfig is undefined", () => {
    render(<ChildGridUserActions cardIndex={4} selectedTabConfig={undefined} />);

    const emptyDivs = screen.getAllByText((content, element) => {
      return element?.tagName === "DIV" && content === "";
    });

    expect(emptyDivs.length).toBeGreaterThan(0);
  });
});
