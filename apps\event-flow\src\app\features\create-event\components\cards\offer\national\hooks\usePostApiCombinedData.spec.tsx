import { renderHook, act } from '@testing-library/react';
import usePostApiCombinedData from './usePostApiCombinedData'; // Adjust the path as necessary

describe('usePostApiCombinedData', () => {
  let mockQuery: jest.Mock;

  beforeEach(() => {
    mockQuery = jest.fn();
  });

  it('should initialize with the correct state', () => {
    const { result } = renderHook(() => usePostApiCombinedData());
    expect(result.current.state.data).toEqual([]); // Initial data should be an empty array
    expect(result.current.state.loading).toBe(false); // Initial loading should be false
    expect(result.current.state.error).toBeNull(); // Initial error should be null
  });

  it('should set loading state when postCombinedData is called', async () => {
    const { result } = renderHook(() => usePostApiCombinedData());

    // Mock the query function
    mockQuery.mockResolvedValueOnce({ data: [{ id: 1, name: 'test' }] });

    // Call postCombinedData and update state
    act(() => {
      result.current.postCombinedData(mockQuery, ['param1']);
    });

    // Check loading state is set to true
    expect(result.current.state.loading).toBe(true);

    // Wait for the state to update after async call
    await act(async () => {
      await result.current.postCombinedData(mockQuery, ['param1']);
    });

    // Check that loading state is false after data is fetched
    expect(result.current.state.loading).toBe(false);
    expect(result.current.state.data).toEqual([{ id: 1, name: 'test' }]); // Verify the correct data is set
  });

  it('should handle successful data fetching', async () => {
    const { result } = renderHook(() => usePostApiCombinedData());

    const mockResponse = { data: [{ id: 1, name: 'test' }] };
    mockQuery.mockResolvedValueOnce(mockResponse); // Mock API response

    // Call postCombinedData to trigger data fetching
    await act(async () => {
      await result.current.postCombinedData(mockQuery, ['param1']);
    });

    // Verify the state is updated with the fetched data
    expect(result.current.state.data).toEqual([{ id: 1, name: 'test' }]);
    expect(result.current.state.loading).toBe(false); // Ensure loading state is false
    expect(result.current.state.error).toBeNull(); // Ensure no error
  });

  it('should handle error state when API request fails', async () => {
    const { result } = renderHook(() => usePostApiCombinedData());

    const mockError = new Error('Something went wrong');
    mockQuery.mockRejectedValueOnce(mockError); // Mock API error

    // Call postCombinedData to trigger an error
    await act(async () => {
      await result.current.postCombinedData(mockQuery, ['param1']);
    });

    // Verify the state is updated with error
    expect(result.current.state.error).toBe(mockError);
    expect(result.current.state.data).toEqual([]); // Ensure data is empty on error
    expect(result.current.state.loading).toBe(false); // Ensure loading state is false
  });

  it('should combine data from multiple API responses', async () => {
    const { result } = renderHook(() => usePostApiCombinedData());

    const mockResponse1 = { data: [{ id: 1, name: 'test1' }] };
    const mockResponse2 = { data: [{ id: 2, name: 'test2' }] };

    mockQuery.mockResolvedValueOnce(mockResponse1); // Mock first response
    mockQuery.mockResolvedValueOnce(mockResponse2); // Mock second response

    // Call postCombinedData to trigger data fetching
    await act(async () => {
      await result.current.postCombinedData(mockQuery, ['param1', 'param2']);
    });

    // Verify that the data is combined
    expect(result.current.state.data).toEqual([
      { id: 1, name: 'test1' },
      { id: 2, name: 'test2' },
    ]);
    expect(result.current.state.loading).toBe(false); // Ensure loading state is false
  });
});
