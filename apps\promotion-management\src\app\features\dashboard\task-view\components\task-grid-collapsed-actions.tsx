import { useEffect, useState } from "react";
import { setTasksFilterData } from "apps/promotion-management/src/app/library/data-access/dashboard";
import {
  usePostAgreedMutation,
  usePostAgreedPendingMutation,
  usePostAgreedPendingToAgreeToCancelEventMutation,
  usePostRejectMutation,
  usePostSendToMerchantMutation,
  usePostSendToVendorMutation,
  usePutTaskStatusMutation,
} from "apps/promotion-management/src/app/library/data-service/dashboard/dashboard-service";
import classNames from "classnames";
import { useDispatch } from "react-redux";
import TaskGridModal from "./task-grid-modal";
import {
  DEFER_ACTION,
  IMMINENT_TASK,
  PPG_ITEM_DECISION_CONFIG,
  TASK_ACTIONS_LABELS,
  TASK_ACTION_MAPPER,
  TASK_MODAL_CONFIG,
  addItemSendToVendorTooltipLabel,
} from "./task-view-columns-config";
import pmConstants from "../../../../shared/pm-constants/pm-constants";
import { useSelectorWrap } from "@me/data-rtk";
import {
  DASHBOARD_SLICE_CONFIG,
  DEFAULT_PAGINATION_CONFIG,
} from "apps/promotion-management/src/app/config/dashboard-config";
import { addDays, compareAsc } from "date-fns";
import {
  formatDate,
  formatTimestamp,
  showAddEventCTA,
} from "@me-upp-js/utilities";
import { checkForSendBackAction, getUniqueNumber } from "./task-service";
import Button from "@albertsons/uds/molecule/Button";
import { EEVENT_STATUS } from "@me/util-helpers";
import { checkIsEventDetailsChanged } from "./task-view-util";
import { EndDateModal } from "./end-date-modal";
import { Tooltip } from "@albertsons/uds/molecule/Tooltip";
import { appConstants } from "@me/utils-root-props";
import { getIsNationalEvent } from "apps/event-flow/src/app/features/event-types/event-types-helper";
import { Info } from "lucide-react";

/**
 * This function will be used to render and manage the Action available at event level
 * @param taskDetails
 * @param eventDetails
 * @returns
 */
export default function TaskGridCollapsedActions({
  taskDetails,
  eventDetails,
}: {
  taskDetails: any;
  eventDetails: any;
}) {
  const {
    taskId,
    itemIds = [],
    options = [],
    type: taskType = "",
    divisionIds = [],
    simsVendors = [],
    subType,
  } = taskDetails;
  const dispatch = useDispatch();
  const { taskView } = DASHBOARD_SLICE_CONFIG;
  const { data: tasksFilterData } =
    useSelectorWrap(taskView.FILTER_SLICE_KEY) || {};
  const { isNationalEvent: isNational } = getIsNationalEvent(eventDetails?.planEvent?.eventType);
  const [postAgreed, { isLoading: isPostAgreedLoading }] =
    usePostAgreedMutation();
  const [postReject, { isLoading: isPostRejectionLoading }] =
    usePostRejectMutation();
  const [postSendToMerchant, { isLoading: isPostSendToMerchantLoading }] =
    usePostSendToMerchantMutation();
  const [postSendToVendor, { isLoading: isPostSendToVendorLoading }] =
    usePostSendToVendorMutation();
  const [putTaskStatus, { isLoading: isPutTaskStatusLoading }] =
    usePutTaskStatusMutation();
  const [lastItemLabel, setLastItemLabel] = useState<string>("");
  const [postAgreedPending] = usePostAgreedPendingMutation();
  const [postAgreedPendingToAgreeToCancel] =
    usePostAgreedPendingToAgreeToCancelEventMutation();

  const [modalProps, setModalProps] = useState({});
  const [isModalPopupOpen, setModalPopupOpen] = useState<boolean>(false);
  const [modalType, setModalType] = useState<string>("");
  const [showEndDateModal, setShowEndDateModal] = useState(false);
  const [endDate, setEndDate] = useState<Date[]>([addDays(new Date(), 1)]);
  const API_STATUS_ERROR_CODES  = {
    ERROR_STATUS: 422,
    ERROR_STATUS_2: 423
 }
  const [divIds, setDivIds] = useState<number[]>([]);
  const divisionName = Array.isArray(divIds)
    ? divIds.map(
        item =>
          appConstants.DIVISIONS_ID_NAME_MAP[String(item).padStart(2, "0")]
      )
    : [];
  function onActionResolved() {
    dispatch(
      setTasksFilterData({
        ...tasksFilterData,
        ...DEFAULT_PAGINATION_CONFIG,
        reloadSlice: getUniqueNumber(),
      })
    );
  }
  const onDoNotDelete = async () => {
    await putTaskStatus(
      getTakUpdatePayload(TASK_ACTIONS_LABELS.DO_NOT_DELETE_ITEM)
    )?.then(() => onActionResolved());
  };
  const postActionMethod = async (action: any, urlParam?: any,type?:any) => {
    if ([lastItemLabel].includes(type) ||(taskType === IMMINENT_TASK && options?.includes(DEFER_ACTION))) {
      await putTaskStatus(getTakUpdatePayload(null,type)).then(() =>
        {
          onActionResolved()
        });
    } else {
      await action({
        URL_PARAM: urlParam || eventDetails?.planEvent?.id,
      }).then(() => onActionResolved());
    }
  };
  const getTakUpdatePayload = (actionType: string | null = null,type?:any) => {
    const payloadConfigkeys =
      PPG_ITEM_DECISION_CONFIG[
        actionType || TASK_ACTIONS_LABELS.SEND_TO_VENDOR
      ];
    const payload = {
      URL_PARAMS: [taskId],
      taskId,
      itemIds,
      ...(actionType === TASK_ACTIONS_LABELS.SET_END_DATE && {
        customEndDate: formatTimestamp({
          timestamp: endDate?.[0],
          pattern: "YYYY-MM-DD",
          timestampConvert: true,
        }),
      }),
      ...payloadConfigkeys,
      ...([lastItemLabel].includes(type) && {
        selectedOption: "CANCEL",
        modificationType: "ITEM_DELETION",
        divisionIds: divIds?.length ? divIds : [],
      }),
    };
    return payload;
  };

  const eventStatus =
    eventDetails?.planEvent?.eventStatus || taskDetails?.eventStatus;

    const taskActionHandlerForLastItemCancel = async (action: any, actionType) => {
      await action(getTakUpdatePayload(actionType,lastItemLabel)).then(res =>{
        if([API_STATUS_ERROR_CODES.ERROR_STATUS].includes(res?.error?.status) || [API_STATUS_ERROR_CODES.ERROR_STATUS_2].includes(res?.error?.status)){
          setModalType(lastItemLabel);
          setModalPopupOpen(true);
        }else{
          onActionResolved()
        }

      })
    }

  const separateDivIds = (message: string) => {
    const ids = message?.replace(/[\[\]]/g, "").split(",").map(Number);
    setDivIds(ids);
};

  const taskActionHandler = async (action: any, actionType) => {
    await action(getTakUpdatePayload(actionType)).then(res => {
      const response = res?.error;
      if ([API_STATUS_ERROR_CODES.ERROR_STATUS, API_STATUS_ERROR_CODES.ERROR_STATUS_2].includes(response?.status)) {
        let labelToSet = TASK_ACTIONS_LABELS.LAST_ITEM_CANCEL;
        if (response?.status === API_STATUS_ERROR_CODES.ERROR_STATUS_2) {
          const errorDisplayMessage = response?.data?.displayMessage;
          labelToSet = TASK_ACTIONS_LABELS.LAST_ITEM_CANCEL_FOR_NDP;
          setLastItemLabel(TASK_ACTIONS_LABELS.LAST_ITEM_CANCEL);
          if (isNational) {
            separateDivIds(errorDisplayMessage);
          }
        }
        setLastItemLabel(labelToSet);
        setModalType(labelToSet);
        setModalPopupOpen(true);
      } else {
        onActionResolved();
      }
    });
  };
  const postRejectHandler = () => {
    postActionMethod(postReject);
    onCloseHandler();
  };

  const modalConfirmHandler = () => {
    postActionMethod(postAgreed);
    onCloseHandler();
  };

  const modalConfirmHandlerForLastItemCancel = () => {
    postActionMethod(postAgreed,null,lastItemLabel);
    onCloseHandler();
  };
  const onCloseHandler = () => {
    setModalPopupOpen(false);
    document.body.style.overflow = "visible";
  };
  const agreeToEvent = () => {
    if (
      compareAsc(
        formatDate(eventDetails?.planEvent?.startDate),
        formatDate(new Date())
      ) < 1 ||
      compareAsc(
        formatDate(eventDetails?.planEvent?.endDate),
        formatDate(new Date())
      ) < 1
    ) {
      setModalType("agree");
      setModalPopupOpen(true);
    } else {
      postActionMethod(postAgreed);
    }
  };

  const modalPropsdefault = {
    common: {
      showHideBtns: true,
      isModalPopupOpen,
      setModalPopupOpen,
      onClose: onCloseHandler,
      cancelBtnHandler: onCloseHandler,
      ...(isNational && {
        divIds,
        divisionName,
      })
    },
    reject: {
      ...TASK_MODAL_CONFIG.REJECT,
      modalNameHandler: postRejectHandler,
    },
    agree: {
      ...TASK_MODAL_CONFIG.AGREE,
      warningMessage:
        compareAsc(
          formatDate(eventDetails?.planEvent?.endDate),
          formatDate(new Date())
        ) < 0
          ? "This event is executed."
          : "This event is active.",
      modalNameHandler: modalConfirmHandler,
    }, [lastItemLabel]: {
      ...(lastItemLabel === TASK_ACTIONS_LABELS.LAST_ITEM_CANCEL_FOR_NDP
        ? TASK_MODAL_CONFIG.LAST_ITEM_CANCEL_FOR_NDP
        : TASK_MODAL_CONFIG.LAST_ITEM_CANCEL),
      modalNameHandler: modalConfirmHandlerForLastItemCancel,
      cancelBtnHandler: onDoNotDelete,
    },
  };

  useEffect(() => {
    setModalProps({
      ...modalPropsdefault.common,
      ...modalPropsdefault[modalType],
    });
  }, [isModalPopupOpen]);

  const API_MAPPING = {
    [TASK_ACTIONS_LABELS.AGREE]: agreeToEvent,
    [TASK_ACTIONS_LABELS.REJECT]: () => {
      setModalType("reject");
      setModalPopupOpen(true);
    },
    [TASK_ACTIONS_LABELS.SEND_BACK_WITH_COMMENT]: () =>
      // navigate to event page and open the comment section
      {
        window.open(
          `meupp/events/edit/${
            eventDetails?.planEvent?.id
          }?isCommentOpen=${true}`,
          "_blank"
        );
        onActionResolved();
      },
    [TASK_ACTIONS_LABELS.AGREE_TO_PENDING_REQUEST]: agreeToEvent,
    [TASK_ACTIONS_LABELS.REJECT_PENDING_REQUEST]: () =>
      postActionMethod(postReject),
    [TASK_ACTIONS_LABELS.SEND_TO_MERCHANT]: () =>
      postActionMethod(postSendToMerchant),
    [TASK_ACTIONS_LABELS.SEND_TO_VENDOR]: () =>
      postActionMethod(postSendToVendor),
    [TASK_ACTIONS_LABELS.ADD_ITEM_SEND_TO_VENDOR]: () =>
      postActionMethod(postSendToVendor),
    [TASK_ACTIONS_LABELS.DELETE_ITEM]: () =>
      taskActionHandler(putTaskStatus, TASK_ACTIONS_LABELS.DELETE_ITEM),
    [lastItemLabel]: () =>
      taskActionHandlerForLastItemCancel(putTaskStatus, lastItemLabel),
    [TASK_ACTIONS_LABELS.DO_NOT_DELETE_ITEM]: () =>
      taskActionHandler(putTaskStatus, TASK_ACTIONS_LABELS.DO_NOT_DELETE_ITEM),
    [TASK_ACTIONS_LABELS.DO_NOT_ADD_ITEM]: () =>
      taskActionHandler(putTaskStatus, TASK_ACTIONS_LABELS.DO_NOT_ADD_ITEM),
    [TASK_ACTIONS_LABELS.SET_END_DATE]: () => {
      setShowEndDateModal(true);
    },
    [TASK_ACTIONS_LABELS.ADD_ITEM]: () =>
      taskActionHandler(putTaskStatus, TASK_ACTIONS_LABELS.ADD_ITEM),
    [TASK_ACTIONS_LABELS.AGREE_TO_CANCEL_REQUEST]: () =>
      postActionMethod(
        postAgreedPendingToAgreeToCancel,
        eventDetails?.planEvent?.id
      ),
  };

  const actions =
    ([
      EEVENT_STATUS.PENDING_WITH_MERCHANT,
      EEVENT_STATUS.PENDING_WITH_VENDOR,
    ].includes(eventStatus) &&
      checkIsEventDetailsChanged(eventDetails)) ||
    (EEVENT_STATUS.AGREED_PENDING === eventStatus &&
      checkForSendBackAction(eventDetails, subType))
      ? []
      : TASK_ACTION_MAPPER[eventStatus] || [];
  const taskActions = actions.map(action => {
    return  {
      ...action,
      onClick: API_MAPPING[action.label],
      isVisible: action.checkIfVisible(eventDetails, taskDetails),
      divisionIds,
      simsVendors,
      icon :action.icon
    };
  });

  const handleSetEndDate = () => {
    setShowEndDateModal(false);
    taskActionHandler(putTaskStatus, TASK_ACTIONS_LABELS.SET_END_DATE);
  };

  const taskActionsIcons = taskActions?.filter((action: any) => {
    return action?.icon && action?.isVisible;

  });
  return (
    <div
      id="abs-task-grid-collapsed-actions"
      className={` w-full h-full gap-4 py-4 flex ${
        taskActionsIcons?.length
          ? ""
          : `flex-col ${pmConstants?.componentClassName?.TASK_GRID_COLLAPSED_ACTIONS}`
      } justify-start } `}
    >
      <EndDateModal
        items={taskDetails?.itemDetailsList}
        showEndDateModal={showEndDateModal}
        endDate={endDate}
        setEndDate={setEndDate}
        setShowEndDateModal={setShowEndDateModal}
        handleSetEndDate={handleSetEndDate}
      />
      {taskActions
        .filter(action => action.isVisible)
        .map((action: any) => {
          const isAddItemSendToVendorAction =
            action.label === TASK_ACTIONS_LABELS.ADD_ITEM_SEND_TO_VENDOR;
          const simsVendorList = [
            ...new Set(action?.simsVendors?.flat(Infinity) || []),
          ];
          return (
            showAddEventCTA(action?.divisionIds, simsVendorList as string[]) &&
            (action.icon ? (
              <Tooltip zIndex={10} anchor="bottom">
              <Tooltip.Popover>
                <div className="m-1 w-fit overflow-auto rounded text-black text-sm font-normal leading-4">
                  {action.toolipText}
                </div>
              </Tooltip.Popover>
                <span
                    id="abs-task-grid-collapsed-actions"
                    key={action.label}
                    className={classNames("!h-auto", {
                      "text-brand text-lg": true,
                      "text-brand cursor-pointer": true
                    })}
                    onClick={() => action?.onClick()}
                ><action.icon/>
                </span>
              </Tooltip>
            ) : isAddItemSendToVendorAction ? (
              <Tooltip
                zIndex={10}
                anchor="bottom"
                label={addItemSendToVendorTooltipLabel}
              >
                <Button
                  id="abs-task-grid-collapsed-actions"
                  // width={160} // Auto width for this specific button
                  key={action.label}
                  variant="secondary"
                  className={classNames("!h-auto", "px-2", "mr-2", {
                    "text-brand text-lg": true,
                    "text-brand cursor-pointer": true,
                    hidden: action.disabled,
                  })}
                  onClick={() => action?.onClick()}
                >
                  {action.label}
                  <Info size={20} aria-label="More details" className="ml-2" />
                </Button>
              </Tooltip>
            ) : (
              <Button
              id="abs-task-grid-collapsed-actions"
              width={160}
              key={action.label}
              variant="secondary"
              className={classNames("!h-auto", {
                "text-brand text-lg": true,
                "text-brand cursor-pointer": true,
                hidden: action.disabled,
              })}
              onClick={() => action?.onClick()}
              >
                {action.label}
              </Button>
            ))
          );
        })}

      {/* Waning modal for reject or agree */}
      <TaskGridModal modalProps={modalProps} />
    </div>
  );
}
