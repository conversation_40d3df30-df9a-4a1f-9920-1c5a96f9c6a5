import React, { useEffect, useState } from "react";
import ProgressBar from "@albertsons/uds/molecule/ProgressBar";
import { useDispatch } from "react-redux";
import { useSelectorWrap } from "@me/data-rtk";
import { eventProgressDataHandler } from "../create-event/service/slice/event-progress-slice";
import EventAction from "./components/event-action/event-action";
import { EEVENT_STATUS } from "./components/event-action/event-action.model";
import { EVENT_STATUS as eventStatusData } from "../create-event/constants/event-status/event-status";
import { INFO_GRAPHICS } from "@me-upp-js/utilities";
import { useFormContext } from "react-hook-form";
import { eventPlanCardUpdateDataSlice } from "../create-event/service/slice/event-plan-slice";
import { useLocation, useParams, useSearchParams } from "react-router-dom";
import { EVENT_TYPES } from "../create-event/constants/event-types/event-type-index";
import { RBAC } from "albertsons-react-rbac";
import { eventProgressCheck } from "./event-progress-util";
import { useGetAllEventCommentsDataQuery } from "./event-comments-section-service";
import EventCommentsCollapsed from "./components/event-comments/event-comments-collapsed";
import { getLoggedInUserType } from "@me-upp-js/utilities";
import InfoGraphics from "./components/info-graphics/info-graphics";
import { DIVISION_PROMOTION } from "../create-event/constants/event-types/types/division-promotion";
import efConstants from "../../shared/ef-constants/ef-constants";
import LastModificationDetails from "./components/event-progress/last-modification-details";
import EventProgressRow from "./components/event-progress/event-progress-row";
import EventProgressService from "./components/event-progress/event-progress-service";
import EventToggleService from "./components/event-progress/event-toggle-service";
import { setEventProgressConfigData } from "./slices/event-types-slice";
import { LoadBaseEventWorkFlowData } from "./components/event-status-buttons/event-buttons";

const EventProgressContainer: React.FunctionComponent = () => {
  const dispatch = useDispatch();
  const { getValues } = useFormContext();
  const location = useLocation();
  const state: any = location.state || {};
  const { id: eventId, eventStatus } = getValues();

  const { data: eventProgressData, status } = useSelectorWrap(
    "event_progress_data"
  );
  const {
    NEW_DIRECTION_FEATURES_FLAGS: { isDirectionalChangesEnable },
  } = efConstants;
  const { data: planAndPendingData } = useSelectorWrap("plan_event_indicators");
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const {
    data: { allowanceData },
  } = useSelectorWrap("allowance_temp_work");

  const { divisionIds = [], negotiationSimsVendors = [] } = {
    ...eventDetailsData,
  };
  const [eventStatusFormData, setEventStatusFormData] = useState<unknown>({
    ...eventStatusData,
  });
  const [commentOpen, setCommentOpen] = useState<boolean>(false);
  const [searchParams] = useSearchParams();
  const eventTypeName = searchParams.get("eventType");

  const eventType = eventTypeName || eventDetailsData?.eventType;

  const eventConfig = EVENT_TYPES?.event_abbreviations?.[eventType];

  const userRole = getLoggedInUserType();

  const { id } = useParams();
  // allEventComments being returned as {all_comments:[{}], id, commentsLoading:boolean}
  const {
    data: allEventComments,
    isFetching: isAllEventsLoading,
    isSuccess: isAllEventsSuccess,
  } = useGetAllEventCommentsDataQuery(
    {
      URL_PARAM: id,
    },
    {
      skip: !id,
    }
  );

  const eventProgressService = EventProgressService(
    eventType,
    eventProgressData,
    eventDetailsData,
    userRole
  );
  LoadBaseEventWorkFlowData.init(
    eventProgressService,
    eventDetailsData,
    planAndPendingData,
    allowanceData
  );

  const eventToggleService = EventToggleService(
    eventProgressService,
    eventDetailsData,
    eventStatusData,
    userRole,
    allowanceData,
    eventStatus,
    planAndPendingData
  );

  useEffect(() => {
    const data = eventToggleService.handleToggleDisable(eventStatusFormData);
    dispatch(setEventProgressConfigData(data));
    setEventStatusFormData(data);
  }, [eventDetailsData, eventProgressData, allowanceData]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const progressCheckHandler = () => {
    const eventProgressSectionData = eventProgressCheck(
      eventDetailsData,
      eventConfig
    );

    const secFilledNames: string[] = [];
    const sectionNameList: string[] = [];
    const invalidSections: string[] = [];
    let selectedSection = "Event Details";
    let activeCard = 0;
    if (eventProgressSectionData) {
      for (const item of eventProgressSectionData) {
        if (!item.sectionCheck) {
          selectedSection = item.sectionName;
          activeCard = item.index;
          break;
        }
      }
      for (const item of eventProgressSectionData) {
        sectionNameList.push(item?.sectionName);
        if (item?.invalidSection && !isDirectionalChangesEnable) {
          invalidSections.push(item.fillName);
        } else if (item?.sectionCheck) {
          secFilledNames.push(item.fillName);
        }
      }
    }
    if (!location.pathname.includes("create")) {
      dispatch(eventPlanCardUpdateDataSlice({ EVENT_PLAN_CARD: activeCard }));
    }
    dispatch(
      eventProgressDataHandler({
        selected: selectedSection,
        sectionNames: sectionNameList,
        filledSections: secFilledNames,
        invalidSections: invalidSections,
      })
    );
  };

  useEffect(() => {
    progressCheckHandler();
  }, [eventDetailsData, eventConfig]);

  if (status === "loading") return <h2>Loading</h2>;

  const percentageCompletion =
    eventDetailsData?.eventStatus === EEVENT_STATUS.CANCELED
      ? 0
      : (eventProgressData?.filledSections?.length * 100) /
      eventProgressData?.sectionNames?.length;

  const isAllowanceByPassed =
    eventDetailsData?.promotionsLists?.length &&
    !eventDetailsData?.offerAllowances?.length;

  return (
    <div
      id="abs-event-progress-container-main-div"
      className="flex flex-col gap-5"
    >
      <div
        id="abs-event-progress-container-div"
        className="flex flex-col w-full p-0 m-0 rounded-[8px] bg-[#fdfdff] border border-[#c8daeb]"
      >
        <p
          id="abs-event-progress-container-p"
          className="flex p-[18px] text-base font-bold text-left text-[#2b303c] border-b border-[#C8DAEB]"
        >
          Event
          <div
            id="abs-event-progress-container-info-graphics"
            className="flex items-center pl-2"
          >
            <InfoGraphics
              anchor="top"
              variant="light"
              registerField={INFO_GRAPHICS?.EVENT}
              classNameInfo="flex-col m-4 text-xs text-[#5a697b]"
              hoverContent={INFO_GRAPHICS?.EVENT}
              size="14"
            />
          </div>
        </p>

        <div
          id="abs-event-progress-container-progress-bar"
          className="p-[16px]"
        >
          <ProgressBar
            className="mt-[16px] mb-[8px]"
            color="#7ab9f4"
            progress={percentageCompletion}
          />
          <div id="abs-event-progress-container" className="p-0 text-xs flex">
            {eventStatus}
            <span
              id="abs-event-progress-container-info-graphics-span"
              className="pl-2"
              style={{ display: "flex", flexDirection: "row" }}
            >
              <InfoGraphics
                anchor="top"
                variant="light"
                registerField={INFO_GRAPHICS[`${eventStatus}`]}
                classNameInfo="flex flex-col m-4 text-md text-[#5a697b]"
                hoverContent={
                  INFO_GRAPHICS[`${eventStatus}`]?.INFO_GRAPHICS_LABEL
                }
                size="14"
              />
            </span>
          </div>
        </div>

        <div
          id="abs-event-progress-container-filter"
          className="flex flex-col justify-start items-start w-full"
        >
          {eventProgressData?.sectionNames
            ?.filter(
              item =>
                !(
                  eventProgressService.isMultiVendorEvent &&
                  item === DIVISION_PROMOTION.Allowance.section
                )
            )
            ?.map((section, idx) => (
              <EventProgressRow
                key={section}
                sectionIndex={idx}
                sectionName={section}
                isDone={eventProgressData.filledSections.includes(section)}
                isInvalid={eventProgressData.invalidSections.includes(section)}
                isSelected={section === eventProgressData.selected}
                isByPassed={
                  eventProgressData.byPassedSections.includes(section) ||
                  (section === "Allowance" && isAllowanceByPassed)
                }
                isEventMultiVendor={eventProgressService.isMultiVendorEvent}
              />
            ))}
        </div>

        <div
          id="abs-event-progress-container-event-action"
          className="flex flex-col gap-[16px] px-[16px] pt-[16px] pb-[34px]"
        >
          <EventAction
            userRole={userRole}
            eventStatus={eventStatus}
            eventStatusData={eventStatusFormData}
            eventId={eventId}
            setCommentOpen={setCommentOpen}
            modifyOrCancel={eventDetailsData?.planEvent?.planEventWorkFlowType}
          />
          <RBAC
            divisionIds={divisionIds}
            permissionsOnly={["PROMOTION_EVENT_MGMT_EDIT_PROGRESS_DUPLICATE"]}
            simsVendors={negotiationSimsVendors}
          >
            {/* <div id="abs-event-progress-container-event-action1"  className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-2 mt-[10px]">
              <p  id="abs-event-progress-container-event-action-p" className="flex-grow-0 flex-shrink-0 text-base font-semibold text-center text-[#1b6ebb]">
                Duplicate Event
              </p>
            </div> */}
          </RBAC>
        </div>
      </div>

      {eventDetailsData.id ? (
        <>
          <EventCommentsCollapsed
            eventID={eventDetailsData.id}
            setCommentOpen={setCommentOpen}
            commentViewData={commentOpen}
          />
          <RBAC
            divisionIds={divisionIds}
            permissionsOnly={["PROMOTION_HISTORY_VIEW"]}
            simsVendors={negotiationSimsVendors}
          >
            <LastModificationDetails />
          </RBAC>
        </>
      ) : null}
    </div>
  );
};

export default EventProgressContainer;
