import { app_store } from "@me/data-rtk";
import { render, screen } from "@testing-library/react";
import { FormProvider, useForm } from "react-hook-form";
import { Provider } from "react-redux";
import * as selectors from "@me/data-rtk";
import PromotionDetails from "./promotion-details";
import { MockedProvider } from "@apollo/client/testing";
import { GetVehicleByTypeAndYearDocument } from "apps/event-flow/src/app/graphql/generated/schema";
import { RBAC } from "albertsons-react-rbac";
import { BrowserRouter } from "react-router-dom";
import { ApolloProvider } from "@apollo/client";

const mocks = [
  {
    request: {
      query: GetVehicleByTypeAndYearDocument,
      variables: {
        vehicleTypId: "636abba1b426ee543a94d394",
        year: 2023,
      },
    },
    result: {
      data: {
        getVehicleByTypeAndYear: [
          {
            id: "63d4d1ec705a29397068db30",
            vehicleNm: "05 Week 02 Insert 2023",
            vehicleTypNm: "insrt",
            sourceVehicleSk: 42640,
            startDate: 1673395200000,
            endDate: 1673913600000,
            vehicleType: {
              vehicleTypeId: "636abba1b426ee543a94d394",
              sourceVehicleTypeSk: 146,
              vehicleTypNm: "insrt",
              vehicleTypDesc: "Weekly Insert",
              __typename: "VehicleTypeRef",
            },
            __typename: "Vehicle",
          },
        ],
      },
    },
  },
];
jest.mock("../../../../../../../graphql/generated/schema.ts", () => ({
  useGetVendorAuthorizationsByVendorNumbersQuery: jest.fn().mockReturnValue([
    jest.fn().mockReturnValue({
      data: {
        getVendorAuthorizationsByVendorNumbers: [],
      },
    }),
    { loading: false },
  ]),
}));
const Wrapper = props => {
  const formMethods = useForm<any>();
  return (
    <BrowserRouter>
      <Provider store={app_store}>
        <FormProvider {...formMethods}>{props.children}</FormProvider>
      </Provider>
    </BrowserRouter>
  );
};

const formInput = {
  id: "63b42f705ae96500e864b636",
  name: "KLLGG SPCL-Ravi",
  divisionIds: ["05"],
  startDate: 1673395200000,
  endDate: 1673913600000,
  promotionsLists: [
    {
      promotionsList: [
        {
          customerGroup: "ALL",
          vehicle: {
            vehicleId: "63d4d1ec705a29397068db30",
            vehicleNm: "05 Week 02 Insert 2023",
            sourceVehicleSk: 42640,
            startDate: 1673395200000,
            endDate: 1673913600000,
            vehicleType: {
              vehicleTypeId: "636abba1b426ee543a94d394",
              sourceVehicleTypeSk: 146,
              vehicleTypNm: "insrt",
              vehicleTypDesc: "Weekly Insert",
            },
          },
        },
      ],
    },
  ],
};
const promoMockForAdDetails = {
  id: "63b42f705ae96500e864b636",
  name: "KLLGG SPCL-Ravi",
  divisionIds: ["05"],
  startDate: 1673395200000,
  endDate: 1673913600000,
  promotionsLists: [
    {
      promotionsList: [
        {
          createPricing: true,
          customerGroup: "ALL",
          vehicle: {
            vehicleId: "63d4d1ec705a29397068db30",
            vehicleNm: "05 Week 02 Insert 2023",
            sourceVehicleSk: 42640,
            startDate: 1673395200000,
            endDate: 1673913600000,
            vehicleType: {
              vehicleTypeId: "636abba1b426ee543a94d394",
              sourceVehicleTypeSk: 146,
              vehicleTypNm: "insrt",
              vehicleTypDesc: "Disco",
            },
          },
        },
      ],
    },
  ],
};
describe("Promotion Details Test Suite", () => {
  beforeEach(() => {
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      switch (sel_name) {
        case "event_details_data":
          return {
            data: { inValidPromotions: [], promotionsList: [] },
          };
        case "hidden_price_rn":
          return {
            data: { hiddenToggleStatus: false },
          };
        case "promotion_regular_price_data":
          return {
            data: {
              minRegularPrice: 0,
              maxRegularPrice: 0,
              maxListCost: 0,
              minListCost: 0,
              maxListAGP: 0,
              minListAGP: 0,
            },
          };
        case "lastPromoStep_rn":
          return {
            data: { isLastPromoStep: false },
          };
        case "is_event_edit_enable":
          return {
            data: {},
          };
        case "promotion_cancel_rn":
          return {
            data: {
              isCancelled: false,
            },
          };
        case "promotion_overlaps_rn": {
          return {
            data: { isPromotionOverlapsDataAvailable: false },
          };
        }
        case "promotion_hidden_pricing_checkboxes_status_rn":
          return {
            data: {
              doNotPrice: false,
              adDetails: false,
              splitBIB: false,
              doNotShowPromoPrice: false,
            },
          };
        default:
          break;
      }
    });
  });
  xit("should render Promotion Details component", () => {
    const { baseElement } = render(
      <Wrapper preselectedFilterValues={formInput}>
        <MockedProvider mocks={mocks}>
          <PromotionDetails
            cardIndex={0}
            cardItemIndex={0}
            stepperElement={{
              fields: {
                promotionType: {},
                factor: {},
                amount: {},
                uom: {},
                itemLimit: {},
                minQuantity: {},
              },
            }}
            savePromotionHandler={jest.fn()}
            saveAndGetForecastHandler={jest.fn()}
          ></PromotionDetails>
        </MockedProvider>
      </Wrapper>
    );
    expect(baseElement).toBeTruthy();
  });

  xit("should contain correct text in Promotion Details component", () => {
    const { baseElement } = render(
      <Wrapper>
        <PromotionDetails
          cardIndex={0}
          cardItemIndex={0}
          stepperElement={{
            fields: {
              promotionType: {},
              factor: {},
              amount: {},
              uom: {},
              itemLimit: {},
              minQuantity: {},
            },
          }}
          savePromotionHandler={jest.fn()}
          saveAndGetForecastHandler={jest.fn()}
        ></PromotionDetails>
      </Wrapper>
    );

    expect(screen.queryByText("View All Items")).toBeTruthy();
    expect(screen.queryByText("List AGP")).toBeTruthy();
    expect(screen.queryByText("List Cost")).toBeTruthy();
    expect(screen.queryByText("Regular Price")).toBeTruthy();
    expect(screen.queryByText("Net Cost")).toBeNull();
    expect(screen.queryByText("Calculate")).toBeNull();
    expect(screen.queryByText("AGP")).toBeNull();
  });

  xit("should render the promotion details stepper with View All Items label", async () => {
    const { baseElement } = render(
      <Wrapper preselectedFilterValues={formInput}>
        <RBAC
          divisionIds={["05"]}
          permissionsOnly={["PROMOTION_PROMO_MGMT_EDIT_GET_FORECAST"]}
          simsVendors={["000000"]}
        >
          <PromotionDetails
            cardIndex={0}
            cardItemIndex={0}
            stepperElement={{
              fields: {
                promotionType: {},
                factor: {},
                amount: {},
                uom: {},
                itemLimit: {},
                minQuantity: {},
              },
            }}
            savePromotionHandler={jest.fn()}
            saveAndGetForecastHandler={jest.fn()}
          ></PromotionDetails>
        </RBAC>
      </Wrapper>
    );
    expect(screen.queryByText("View All Items")).toBeTruthy();
  });

  xit("renders create promotion text", () => {
    const { baseElement, getByRole } = render(
      <Wrapper preselectedFilterValues={promoMockForAdDetails}>
        <RBAC
          divisionIds={["05"]}
          permissionsOnly={["PROMOTION_PROMO_MGMT_EDIT_GET_FORECAST"]}
          simsVendors={["000000"]}
        >
          <ApolloProvider client={selectors.initiateGraphQlCall}>
            <PromotionDetails
              cardIndex={0}
              cardItemIndex={0}
              stepperElement={{
                fields: {
                  promotionType: {},
                  factor: {},
                  amount: {},
                  uom: {},
                  itemLimit: {},
                  minQuantity: {},
                },
              }}
              savePromotionHandler={jest.fn()}
              saveAndGetForecastHandler={jest.fn()}
            ></PromotionDetails>
          </ApolloProvider>
        </RBAC>
      </Wrapper>
    );
    expect(screen.queryByText("Create Promotion")).toBeTruthy();
  });
});
