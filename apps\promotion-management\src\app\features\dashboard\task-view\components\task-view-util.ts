import {
  checkIsEventDatesChanged,
  EEVENT_STATUS,
  getInvalidAllowanceAndPromotions,
  GetInvalidAllowanceKeys,
  GetInvalidPromoKeys,
} from "@me/util-helpers";
import { getLoggedInUserType } from "@me-upp-js/utilities";

const getInvalidOffersAndPromotions = (
  planEventPending,
  eventDetails,
  isEventDetailsChanged
) => {
  const userType = getLoggedInUserType();
  const inValidAllowances = getInvalidAllowanceAndPromotions(
    planEventPending,
    planEventPending?.["eventCreationVehicle"],
    GetInvalidAllowanceKeys.iterateKey1,
    GetInvalidAllowanceKeys.iterateKey2,
    GetInvalidAllowanceKeys.startDateKey,
    GetInvalidAllowanceKeys.endDateKey,
    "id",
    userType,
    eventDetails,
    isEventDetailsChanged
  );
  const inValidPromotions = inValidAllowances?.length
    ? []
    : getInvalidAllowanceAndPromotions(
      planEventPending,
      planEventPending?.["eventCreationVehicle"],
      GetInvalidPromoKeys.iterateKey1,
      GetInvalidPromoKeys.iterateKey2,
      GetInvalidPromoKeys.startDateKey,
      GetInvalidPromoKeys.endDateKey,
      "id",
      userType,
      eventDetails,
      isEventDetailsChanged
    );
  return [...inValidAllowances, ...inValidPromotions];
};

export const checkIsEventDetailsChanged = eventDetails => {
  const {
    eventDetailsEventInd,
    allowanceEventInd,
    promotionEventInd,
    otherDetailsChangedInd,
    planEvent,
    planEventPending,
    planEventPendingChanges,
  } = eventDetails || {};
  const isEventDatesChanged =
    eventDetailsEventInd && otherDetailsChangedInd &&
    checkIsEventDatesChanged(
      eventDetailsEventInd,
      allowanceEventInd,
      promotionEventInd,
      otherDetailsChangedInd,
      planEvent,
      planEventPending,
      planEventPendingChanges
    );

  return (
    isEventDatesChanged &&
    !!getInvalidOffersAndPromotions(
      planEventPending,
      eventDetails,
      isEventDatesChanged
    )?.length
  );
};

export function validateIfAnyChangesAreMade(eventDetails: any): boolean {
  const {
    eventDetailsEventInd,
    allowanceEventInd,
    promotionEventInd,
    otherDetailsChangedInd,
    planEvent: { offerAllowances = [], promotionsList = [] },
  } = eventDetails;

  // to check for newly added allow/promo
  let isNewAllowanceAdded = false;
  let isNewPromoAdded = false;
  offerAllowances.forEach(offer => {
    offer?.allowances?.forEach(allow => {
      if (allow?.allowanceStatus === EEVENT_STATUS.DRAFT) {
        isNewAllowanceAdded = true;
      }
    });
  });
  promotionsList.forEach(promo => {
    if (promo?.promotionWorkflowStatus === EEVENT_STATUS.DRAFT) {
      isNewPromoAdded = true;
    }
  });

  return (
    eventDetailsEventInd && otherDetailsChangedInd ||
    allowanceEventInd ||
    promotionEventInd ||
    isNewAllowanceAdded ||
    isNewPromoAdded
  );
}

export function validateIfSentBackWithCmt(eventDetails: any): boolean {
  return !!eventDetails?.planEvent?.isStatusUpdateWithComment;
}

export const filterCommentsByUser = (comments) => {
  const userBasedComments = comments.map(comment => {
    const { eventComments } = comment;
    const userComment = eventComments.filter(
      userComm =>
        userComm?.userType?.toLowerCase() !==
        getLoggedInUserType()?.toLowerCase()
    );
    return { ...comment, eventComments: userComment };
  });
  return userBasedComments;
};
