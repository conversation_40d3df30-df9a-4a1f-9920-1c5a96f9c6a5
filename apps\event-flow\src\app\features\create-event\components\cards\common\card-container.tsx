import Button from "@albertsons/uds/molecule/Button";
// import Card from "@albertsons/uds/molecule/Card";
import { <PERSON>, CardHeader, CardBody } from "@material-tailwind/react";
import { useSelectorWrap } from "@me/data-rtk";
import { eventProgressDataHandler } from "../../../service/slice/event-progress-slice";
import { lazy, memo, useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import {
  generateOfferAndAllowanceName,
  getAllowanceTypeByPerformance,
  getCreateIndByLocation,
  getDsdFundingViewSearchParams,
  getQueryParams,
  isCrossUserEditingCard,
} from "../../../service/allowance/allowance-service";
import MultipleCardContainer from "./multiple-card-container";
import {
  allowanceNewCardConfiguration,
  offerCardConfiguration,
  offerResendStatus,
  promoCardConfiguration,
  resetIsOfferSectionUpdated,
  resetOfferAmountsData,
  resetOfferSectionsData,
  resetOfferSectionsEnableConfig,
  setAllowanceFormInfo,
  setAllowTypeChange,
} from "../../../service/slice/allowance-details-slice";
import Divider from "@albertsons/uds/molecule/Divider";
import "./common-modal.scss";
import {
  useDeletePromotionDataMutation,
  usePutPromotionRejectCancelDataMutation,
} from "../../../service/apis/promotion-api";
import { useFormContext } from "react-hook-form";
import {
  promotionDetailDataHandler,
  promotionHiddenPricingChecboxesStatusHandler,
} from "../../../service/slice/promotion-details-slice";
import { CommonModal } from "./common-modal";
import _ from "lodash";
import { EEVENT_STATUS } from "../../../../event-types/components/event-action/event-action.model";
import {
  allowanceTempWorkHandler,
  allowanceTempWorkReset,
} from "../../../service/slice/allowance-temp-work-slice";
import { RBAC } from "albertsons-react-rbac";
import {
  getLoggedInUserType,
  getNationalOfferStatus,
  hasCreateIndChange,
  isAllowanceFeatureEnabled,
  isComingFromtask,
  isPromoChanges,
  isOfferAttachmentFlagEnabled,
} from "@me-upp-js/utilities";
import { getSubTitle } from "../../../service/event-details/event-detail-service";
import EditAndRemoveButtons from "./edit-and-remove-buttons-container";
import CardWrapper from "./CardWrapper";
import efConstants from "../../../../../shared/ef-constants/ef-constants";
import {
  useDeleteAllowanceTempWorkDataMutation,
  usePostAllowanceEditTempWorkDataMutation,
  usePutAllowanceRejectCancelDataMutation,
  useDeleteAllowancePendingCancelDataMutation,
  useDeleteOfferDataMutation,
  usePostResendOfferMutation,
  useDeleteNationalAllowanceTempWorkDataMutation,
  usePostNationalOfferEditMutation,
  useDeleteNationalOfferDataMutation,
  useNationalDeleteAllowancePendingCancelDataMutation,
} from "../../../service/apis/allowance-api";
import {
  isPermissionToEdit,
  DeepClone,
  EUSER_ROLES,
  useGetAppBasePath,
  PROMO_DETAILS_HIDDEN_PRICING_FEATURE,
} from "@me/util-helpers";
import StatusBadge from "../common/status-badge";
import { RenderStates } from "@me/ui-render-states";
import {
  setAllowancesDeletePendingCancel,
  setAllowancesResponse,
} from "../../../service/slice/allowances-response-slice";
import { isLastPromoStep } from "../promotion-details/stepper/promotion-details/promotion-details-service";
import { isAddingPromoConfig } from "./card-container-service";
import {
  getObjectKeys,
  getObjectValues,
} from "../../../service/allowance/allowance-stepper-service";
import { ChevronsDownUp, ChevronsUpDown, RotateCcw, Eye } from "lucide-react";
import Tooltip from "@albertsons/uds/molecule/Tooltip";
import {
  Last_Reject_Modal_Labels_For_Offer,
  Last_Reject_Modal_Labels_For_Promo,
  Last_Cancel_Modal_Labels_For_Offer,
  Last_Cancel_Modal_Labels_For_Promo,
  getModelLabelMessage,
} from "../../../constants/event-status/contsants";
import { ViewAgreementModal } from "../../../../event-types/components/event-action/view-agreement-modal";
import { compareAsc } from "date-fns";
import { checkIsLastOfferOrPromotion } from "../event-details/event-details-card-service";
import {
  usePostRejectMutation,
  usePostCancelMutation,
  usePostAgreedToRequestCancelMutation,
} from "../../../../event-types/components/event-action/event-action-service";
import {
  doRefetchEventsPlanxAapi,
  eventDetailsDataHandler,
  setEventStatusChangedIndicators,
} from "../../../service/slice/event-detail-slice";
import { useNavigate } from "react-router-dom";
import { appConstants } from "@me/utils-root-props";
import { checkHiddenPrice } from "../promotion-details/promotion-details-helper";
import { ApexPromoChangeStatus } from "../promotion-details/promotion-details-model";
import { DISTRIBUTORS_IND_KEYS } from "../../../constants/fields/allowance/allowance-steps-config";
import OfferSectionLinks from "../offer/offer-sections/offer-section-links";
import { compareDivisionIdFn } from "../offer/offer-service";
import {
  resetDivisionWiseShrdWhseData,
  resetNationalDivisionsConfig,
} from "../../../../all-allowances/nationals/slices/national-main-entry-slices";
import { EVENT_TYPE } from "../../../constants/constants";
import { getIsNationalEvent } from "../../../../event-types/event-types-helper";
import OfferFileUploadContainer from "./offer-file-upload-container";
import { getTableConfigOnAllowType } from "../../../../all-allowances/all-allowances-container/text-mapping";
import {
  setAllowanceTableColsGroupConfig,
  setSelectedSwitchValue,
} from "../../../service/slice/allowances-dashboard-slices";
import OffersSummaryModal from "../../../../all-allowances/nationals/components/table/offers-summary-modal";

interface ICardContainerProps {
  cardContainerData: any;
  cardConfiguration: any;
  cardIndex;
  step: any;
  sectionIndex: any;
  isAddAnotherOffer?: any;
}

const CardContainer = ({
  cardContainerData,
  cardConfiguration,
  cardIndex,
  step,
  sectionIndex,
  isAddAnotherOffer,
}: ICardContainerProps) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { getValues, setValue } = useFormContext();
  const {
    promotionsLists = [],
    offerAllowances = [],
    eventStatus,
  } = getValues() || {};
  const { taskType } = getQueryParams();
  const { data: eventCard } = useSelectorWrap("event_plan_card_update_details");
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const {
    data: { isAddPromo },
  } = useSelectorWrap("add_promo_card_rn");
  const { divisionIds = [], negotiationSimsVendors = [] } = {
    ...eventDetailsData,
  };
  const { editCardConfig = {}, openCardConfig = {} } =
    useSelectorWrap("offer_card_configutation_rn").data || {};
  const {
    openCardConfig: promoOpenCardConfig = {},
    editCardConfig: promoEditCardConfig = {},
    isAddNewPromo: newPromoAdd = {},
  } = useSelectorWrap("promo_card_configutation_rn").data || {};
  const isPostPromotionSuccessConfig = useSelectorWrap(
    "post_promotion_success_configuration_rn"
  );

  const {
    data: { allowanceData: allowanceTempWorkData },
  } = useSelectorWrap("allowance_temp_work");
  const { data: isAddingPromoStore } = useSelectorWrap(
    "isAddingPromoConfig_rn"
  );
  const {
    data: { isEditPromotion: { isEdit = {} } = {} },
  } = useSelectorWrap("promotion_edit_enable_configutation_rn") || {};
  const { isDatesUpdated = false } =
    useSelectorWrap("promotion_dates_update_rn")?.data || {};
  const { data: eventProgressConfigData } = useSelectorWrap(
    "eventProgressConfigData_rn"
  );
  const { isAllowanceTypeChanged } = useSelectorWrap(
    "allow_type_change_rn"
  ).data;
  const { productSources = [] } =
    useSelectorWrap("allowance_product_sources_rn")?.data || {};

  const { isEventCardOpen } = useSelectorWrap("is_event_edit_enable").data;
  const { hiddenPricingFeatureEnabled } = PROMO_DETAILS_HIDDEN_PRICING_FEATURE;
  const commonWarningLabel =
    "Please add at minimum one Offer or Promotion to event in order to";
  const isAllowanceCard = cardConfiguration.key === "Allowance";
  const isPromoCard = cardConfiguration.key === "Promotion";
  const openCardKey = isAllowanceCard
    ? "allowanceCardConfig"
    : "promoCardConfig";
  const isCrossUserEditCard =
    isAllowanceCard &&
    isCrossUserEditingCard(eventStatus, allowanceTempWorkData);
  const newPromoIndexKey =
    Object.keys(newPromoAdd)?.filter(index => newPromoAdd[index])?.[0] || 0;
  const [isAddingAnotherPromotion, setIsAddingAnotherPromotion] =
    useState(false);
  const [offerAllowanceStatus, setOfferAllowanceStatus] = useState("");
  const [newPromotionIndex, setNewPromotionIndex] = useState(newPromoIndexKey);
  const [isOpenCard, setIsOpenCard] = useState({
    allowanceCardConfig: {},
    promoCardConfig: {},
  });
  const [isEditEnable, setIsEditEnable] = useState({});
  const [isModalPopupOpen, setModalPopupOpen] = useState(false);
  const [isInValidCardOpen, setInvalidCardOpen] = useState(false);
  const [isOpenOfferSummaryModal, setIsOpenOfferSummaryModal] = useState(false);
  const [commonModalData, setCommonModalData] = useState({
    popUpModalTitle: "",
    warningMessage: "",
    confirmBtnTitle: "",
    cancelBtnTitle: "",
    showHideBtns: true,
    modalNameHandler: (
      cardIndex: any,
      cardItemIndex: any,
      isLastOfferOrPromo: boolean
    ) => null,
    cardIndex: "",
    cardItemIndex: "",
    height: "",
    isLastOfferPromotion: false,
    cancelBtnHandler: (isLastOfferOrPromotion: boolean, title: string) => null,
  });
  const [isAllowanceAgreementVisible, setIsAllowanceAgreementVisible] =
    useState(false);
  const isAgreementClicked = useRef(false);
  const isOfferDisabled = getObjectValues(editCardConfig)?.includes(true);
  const isOfferCard = cardConfiguration?.key === "Allowance";
  const { isNDPAndFlagEnabled: isNationalEvent, isNCDP } = getIsNationalEvent(
    eventDetailsData?.eventType
  );

  // to check if current Card is required to be expanded from URL data
  const { offerIdForExpand } = getQueryParams();
  const isCurrentCardExpandedOffer =
    cardConfiguration.key === "Allowance" &&
    parseInt(offerIdForExpand) === cardContainerData?.offerNumber;

  const [
    deleteAllowanceTempWorkData,
    { isLoading: isDeleteAllowanceTempDataloading },
  ] = useDeleteAllowanceTempWorkDataMutation();
  const [
    deleteAllowancePendingCancelData,
    { isLoading: isDeleteAllowancePendingCancelLoading },
  ] = useDeleteAllowancePendingCancelDataMutation();
  const [
    deleteNationalAllowancePendingCancelData,
    { isLoading: isDeleteNationalAllowancePendingCancelLoading },
  ] = useNationalDeleteAllowancePendingCancelDataMutation();
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  const [
    postAllowanceEditTempWorkData,
    { isLoading: isAlowanceEditTempLoading },
  ] = usePostAllowanceEditTempWorkDataMutation();

  const [postNationalOfferEdit, { isLoading: isNationalTempLoading }] =
    usePostNationalOfferEditMutation();

  const [
    deletePromotionData,
    { isLoading: isDeletePromoDataloading, isSuccess: deletePromoData },
  ] = useDeletePromotionDataMutation();

  const [
    deleteOfferData,
    { isLoading: isDeleteOfferDataloading, isSuccess: deletedOfferData },
  ] = useDeleteOfferDataMutation();

  const [
    deleteNationalOfferData,
    {
      isLoading: isNationalDeleteOfferDataloading,
      isSuccess: deletedNationalOfferData,
    },
  ] = useDeleteNationalOfferDataMutation();

  const [deleteNationalAllowanceTempWorkData] =
    useDeleteNationalAllowanceTempWorkDataMutation();

  const [
    rejectCancelAllowance,
    {
      isLoading: isRejectCancelAllowanceDataloading,
      data: rejectCancelAllowanceData,
    },
  ] = usePutAllowanceRejectCancelDataMutation();

  const [
    rejectCancelPromotion,
    { isLoading: isRejectCancelDataloading, data: rejectCancelData },
  ] = usePutPromotionRejectCancelDataMutation();

  const [postResendOffer, { isError: isPostResnedOfferError }] =
    usePostResendOfferMutation();

  const [postCancel, { isLoading: isPostCancelPending }] =
    usePostCancelMutation();
  const [
    postAgreedToRequestCancel,
    { isLoading: isPostAgreedToRequestCancelLoading },
  ] = usePostAgreedToRequestCancelMutation();
  const [postReject, { isLoading: isPostRejectionLoading }] =
    usePostRejectMutation();

  const executeApiCall = async apiCall => {
    const eId = eventDetailsData?.id;
    const {
      data: { eventId: rEventId = "", eventStatus: rEventStatus = "" } = {},
    } = await apiCall({
      URL_PARAM: eId,
      queryParams: {},
    });
    if (rEventId) {
      document
        .getElementById("event-header")
        ?.scrollIntoView({ behavior: "smooth" });
      dispatch(eventDetailsDataHandler({ eventStatus: rEventStatus }));
      dispatch(setEventStatusChangedIndicators({ isEventStatusChanged: true }));
      dispatch(
        doRefetchEventsPlanxAapi({
          isRefetch: _.uniqueId("refetch"),
        })
      );
    }
    setModalPopupOpen(false);
  };

  const {
    NEW_DIRECTION_FEATURES_FLAGS: {
      isDirectionalChangesEnable,
      isPromoDirectionalChangesEnable,
    },
    OFFER_POPUP_MESSAGES,
    VIEW_OFFER_DEAL_SHEET_BUTTON,
    OFFER_UPLOAD_LABEL,
    VIEW_OFFER_SUMMARY,
    ALLOWANCE_TYPES,
  } = efConstants;
  useEffect(() => {
    if (deletePromoData || rejectCancelData)
      dispatch(
        promotionDetailDataHandler({
          isPromotionAdded: deletePromoData
            ? _.uniqueId("deletepromotion")
            : _.uniqueId("rejectCancelPromotion"),
        })
      );
  }, [deletePromoData, rejectCancelData]);

  useEffect(() => {
    if (deletedOfferData || deletedNationalOfferData)
      dispatch(
        setAllowancesResponse({
          isAllowanceDeleted:
            deletedOfferData || deletedNationalOfferData
              ? _.uniqueId("deleteOffer")
              : "",
        })
      );
  }, [deletedOfferData, deletedNationalOfferData]);

  const inValidCardOpenHandler = () => {
    if (
      ["Allowance", "Promotion"].includes(cardConfiguration.key) &&
      eventDetailsData?.[
        cardConfiguration.key.includes("Allowance")
          ? "offerAllowances"
          : "promotionsLists"
      ]?.length &&
      (eventDetailsData?.inValidAllowances?.length ||
        eventDetailsData?.inValidPromotions?.length)
    ) {
      const isAllowanceKey = ["Allowance"].includes(cardConfiguration.key);
      if (!isAllowanceKey) {
        for (const data of cardContainerData["promotionsList"]) {
          if (
            eventDetailsData?.["inValidPromotions"]?.includes(data.id) &&
            isPermissionToEdit(eventDetailsData?.eventStatus)
          ) {
            setInvalidCardOpen(true);
            break;
          }
        }
      }
    }
  };

  useEffect(() => {
    inValidCardOpenHandler();
  }, [cardContainerData, eventDetailsData, cardConfiguration.key]);

  useEffect(() => {
    if (eventDetailsData?.id && isAllowanceCard) {
      setIsEditEnable(editCardConfig);
      setIsOpenCard({ ...isOpenCard, allowanceCardConfig: openCardConfig });
    }
  }, [editCardConfig, openCardConfig, cardConfiguration]);

  useEffect(() => {
    if (eventDetailsData?.id && !isAllowanceCard) {
      setIsOpenCard({
        ...isOpenCard,
        promoCardConfig: promoOpenCardConfig,
      });
    }
  }, [promoOpenCardConfig, cardConfiguration]);

  useEffect(() => {
    if (isPostPromotionSuccessConfig) {
      // if the Post was successful, redux store gets updated (in file: promo-det-card-con.tst)
      setIsAddingAnotherPromotion(false);
      dispatch(isLastPromoStep(false));
    }
  }, [isPostPromotionSuccessConfig]);

  useEffect(() => {
    if (rejectCancelAllowanceData) {
      dispatch(setAllowancesResponse(rejectCancelAllowanceData));
    }
  }, [rejectCancelAllowanceData]);

  useEffect(() => {
    if (isAddingPromoStore) {
      setIsAddingAnotherPromotion(isAddingPromoStore.isAddingPromoConfig);
    }
  }, [isAddingPromoStore]);

  useEffect(() => {
    if (isAllowanceCard) {
      const offerStatus = `offerAllowances[${cardIndex}].allowances[0].allowanceStatus`;
      const validOfferStatus = isNationalEvent
        ? getNationalOfferStatus(offerAllowances?.[cardIndex]?.allowances)
        : getValues(offerStatus);
      setOfferAllowanceStatus(validOfferStatus);
    }
  }, [cardConfiguration.key, cardIndex, getValues, eventDetailsData]);

  const handleCardOpenStatus = (value = false) => {
    if (isAllowanceCard) {
      dispatch(
        offerCardConfiguration({
          editCardConfig,
          openCardConfig: { ...openCardConfig, [cardIndex]: value },
        })
      );
    } else {
      dispatch(
        promoCardConfiguration({
          editCardConfig: promoEditCardConfig,
          openCardConfig: { ...promoOpenCardConfig, [cardIndex]: value },
          isAddNewPromo: newPromoAdd,
        })
      );
    }
  };

  const handleOfferCardConfig = (
    records = {},
    cardIndex = 0,
    value = false,
    isEditRequired = false
  ) => {
    const values = getObjectKeys(records).length
      ? records
      : { [cardIndex]: value };
    dispatch(
      offerCardConfiguration({
        editCardConfig: isEditRequired ? { [cardIndex]: value } : {},
        openCardConfig: { ...openCardConfig, ...values },
      })
    );
  };

  useEffect(() => {
    if (cardConfiguration.key === "Promotion") {
      const promotionsList = getValues(`promotionsLists[0].promotionsList`);
      let allStatuses: string[] = [];
      promotionsList?.forEach((_item: any, index: number) => {
        const promoStatus = promotionsList[index]?.promotionWorkflowStatus;
        allStatuses = [...allStatuses, promoStatus];
      });
      const statusList = [EEVENT_STATUS.AGREED_PENDING, EEVENT_STATUS.REJECTED];
      const promoCardStatus = statusList.some(item =>
        allStatuses.includes(item)
      );
      promoCardStatus && handleCardOpenStatus(promoCardStatus);
    }
  }, [cardConfiguration.key, cardIndex]);

  useEffect(() => {
    isAddPromo && handleAddAnotherPromoAllowance();
  }, [isAddPromo, JSON.stringify(newPromoAdd)]);

  const setCardData = () => {
    const cardData = new DeepClone(
      cardContainerData[cardConfiguration?.cardFieldSubProp]
    ).getDeepClone();
    const exisitingOfferPromos = cardData?.slice(
      0,
      cardData?.filter(item => item?.id && item)?.length
    );
    return exisitingOfferPromos || [];
  };
  const updatePromoDetailsHiddenPricingCheckboxesSlice = () => {
    dispatch(
      promotionHiddenPricingChecboxesStatusHandler({
        doNotPrice: false,
        adDetails: false,
        splitBIB: false,
        doNotShowPromoPrice: false,
      })
    );
  };

  const cardData = setCardData();
  const userRole = getLoggedInUserType();
  const isMerchantUser = userRole === EUSER_ROLES?.MERCHANT;
  const [eventStatusFormData, setIsEventStatusFormData] = useState<unknown>({
    ...eventProgressConfigData,
  });
  const isEditRemoveButtonsRender =
    eventStatusFormData?.[eventDetailsData.eventStatus]?.USERS?.[userRole]
      ?.edit?.[cardConfiguration.key.toLowerCase()];
  const isAddPromoAllowanceButtonsRender =
    eventStatusFormData?.[eventDetailsData.eventStatus]?.USERS?.[userRole]
      ?.add?.[cardConfiguration.key.toLowerCase()];

  let offerHeader = "";
  let promotionHeader = "";
  if (cardContainerData) {
    const configKeys = getObjectKeys(cardContainerData);
    if (configKeys.includes("offerNumber"))
      offerHeader = generateOfferAndAllowanceName(cardContainerData);
    else if (configKeys.includes("promotionsList"))
      promotionHeader = getSubTitle(
        cardContainerData?.promotionsList?.[0]?.updateUser
      );
  }

  const offerDeleteHandler = (cardIndex, cardItemIndex) => {
    setModalPopupOpen(false);
    const offerId = offerAllowances[cardIndex]?.id;
    const offerNumber = offerAllowances?.[cardIndex]?.offerNumber;
    if (isNationalEvent) {
      offerNumber && deleteNationalOfferData({ URL_PARAM: offerNumber });
    } else {
      if (offerId) {
        deleteOfferData({ URL_PARAM: offerId });
      }
    }
    return null;
  };
  /** get offer allowances list which are non rejected and non cancelled
   * @returns - {Array}
   */
  const excludeStatuses = [
    EEVENT_STATUS?.REJECTED,
    EEVENT_STATUS?.CANCELED,
    EEVENT_STATUS?.AGREED_PENDING,
  ];
  const offerAllowancesStatusList = () => {
    const validOfferList = isNationalEvent
      ? offerAllowances?.filter(offer =>
          offer?.allowances?.some(
            allow => !excludeStatuses?.includes(allow?.allowanceStatus)
          )
        )
      : offerAllowances?.filter(
          offer =>
            !excludeStatuses.includes(offer?.allowances[0]?.allowanceStatus)
        );
    return validOfferList;
  };
  /** get promotions list which are non rejected and non cancelled
   *  @returns - {Array}
   */
  const promoStatusList = () => {
    const countPromotionCardStatus =
      eventDetailsData?.promotionsLists[0]?.promotionsList;
    // const planEventWorkFlowType = eventDetailsData?.planEventWorkFlowType;

    return countPromotionCardStatus?.filter(promotion => {
      if (
        [EEVENT_STATUS.AGREED_PENDING].includes(
          promotion.promotionWorkflowStatus
        ) &&
        [
          ApexPromoChangeStatus.UPDATE_PROMO,
          ApexPromoChangeStatus.LINK_PROMO,
        ].includes(promotion.promotionChangeStatus?.toUpperCase())
      ) {
        return promotion;
      } else
        return !excludeStatuses.includes(promotion.promotionWorkflowStatus);
    });
  };
  /** Checks for at least one offer or one promo which are non rejected and non cancelled
   * @returns - {boolean}
   */
  const checkAllowancePromoCountCardsStatus = () => {
    const filteredAllowData = offerAllowancesStatusList() || [];
    const filteredPormoData = promoStatusList() || [];
    return filteredAllowData.length + filteredPormoData.length >= 2;
  };
  const renderAddPromotionButton = () => {
    return !isPromoDirectionalChangesEnable ? true : !promoStatusList()?.length;
  };

  const removeAllowanceHandler = event => {
    event?.stopPropagation();

    const offerId = offerAllowances?.[cardIndex]?.offerNumber;
    const draftEventStatus =
      eventDetailsData?.eventStatus === EEVENT_STATUS.DRAFT;
    const isValidOfferPromoStatus = checkAllowancePromoCountCardsStatus();
    const statusToCheck = isNationalEvent
      ? getNationalOfferStatus(offerAllowances?.[cardIndex]?.allowances)
      : offerAllowances?.[cardIndex]?.allowances?.[0]?.allowanceStatus;
    const draftStatusCheck =
      statusToCheck === EEVENT_STATUS.DRAFT && draftEventStatus;

    const modalConfirmBtnName = offerDeleteHandler;
    setCommonModalData({
      popUpModalTitle:
        draftStatusCheck ||
        isValidOfferPromoStatus ||
        isDirectionalChangesEnable
          ? `Selected offer - ${offerId} and it's associated allowances will be deleted, Are you sure?`
          : `You cannot remove this Offer`,
      warningMessage:
        draftStatusCheck || isValidOfferPromoStatus
          ? ""
          : isDirectionalChangesEnable
          ? "Please add another offer and send for review"
          : `${commonWarningLabel} remove Offer ${offerId}`,
      confirmBtnTitle: "Confirm",
      cancelBtnTitle: "Cancel",
      showHideBtns: isDirectionalChangesEnable
        ? true
        : draftStatusCheck || isDirectionalChangesEnable,
      modalNameHandler: modalConfirmBtnName,
      cardIndex: cardIndex,
      cardItemIndex: "",
      height: "300px",
      isLastOfferPromotion: false,
      cancelBtnHandler: (isLastOfferOrPromotion: boolean, title: string) =>
        null,
    });
    setModalPopupOpen(true);
  };

  const rejectAllowanceHandler = (btnAction, cardIndex) => {
    const modalConfirmBtnName = rejectCancelAllowanceCard;
    const checkRejectStatus = checkAllowancePromoCountCardsStatus();
    const isLastOfferOrPromo = checkIsLastOfferOrPromotion(eventDetailsData);
    const isMultiple = !isLastOfferOrPromo ? "MULTIPLE" : "SINGLE";
    const messageLabel = getModelLabelMessage(
      btnAction,
      false,
      userRole,
      isMultiple
    );
    setCommonModalData({
      popUpModalTitle: messageLabel.LABEL,
      warningMessage: messageLabel.WARNING,
      confirmBtnTitle: isLastOfferOrPromo
        ? Last_Reject_Modal_Labels_For_Offer.CONFIRM_BUTTON_TITLE
        : "Confirm",
      cancelBtnTitle: isLastOfferOrPromo
        ? Last_Reject_Modal_Labels_For_Offer.CANCEL_BUTTON_TITLE
        : "Cancel",
      showHideBtns:
        checkRejectStatus || isDirectionalChangesEnable ? true : false,
      modalNameHandler: modalConfirmBtnName,
      cardIndex: cardIndex,
      cardItemIndex: "",
      height: "",
      isLastOfferPromotion: isLastOfferOrPromo,
      cancelBtnHandler: cancelOrRejectEventHandler,
    });
    setModalPopupOpen(true);
  };

  const cancelAllowanceHandler = (btnAction, cardIndex) => {
    const modalConfirmBtnName = rejectCancelAllowanceCard;
    const checkCancelStatus = checkAllowancePromoCountCardsStatus();
    const isLastOfferOrPromo = checkIsLastOfferOrPromotion(eventDetailsData);
    const isMultiple = !isLastOfferOrPromo ? "MULTIPLE" : "SINGLE";
    const messageLabel = getModelLabelMessage(
      btnAction,
      false,
      userRole,
      isMultiple
    );
    setCommonModalData({
      popUpModalTitle: messageLabel.LABEL,
      warningMessage: messageLabel.WARNING,
      confirmBtnTitle: isLastOfferOrPromo
        ? Last_Cancel_Modal_Labels_For_Offer.CONFIRM_BUTTON_TITLE
        : "Confirm",
      cancelBtnTitle: isLastOfferOrPromo
        ? Last_Cancel_Modal_Labels_For_Offer.CANCEL_BUTTON_TITLE
        : "Cancel",
      showHideBtns:
        checkCancelStatus || isDirectionalChangesEnable ? true : false,
      modalNameHandler: modalConfirmBtnName,
      cardIndex: cardIndex,
      cardItemIndex: "",
      height: "",
      isLastOfferPromotion: isLastOfferOrPromo,
      cancelBtnHandler: cancelOrRejectEventHandler,
    });
    setModalPopupOpen(true);
  };

  const rejectCancelAllowanceCard = (
    cardIndex,
    _cardItemIndex,
    isLastOfferOrPromo
  ) => {
    setModalPopupOpen(false);
    const allowanceId = offerAllowances[cardIndex]?.offerNumber;
    const allowStatusFromFirstAllow =
      offerAllowances[cardIndex]?.allowances?.[0]?.allowanceStatus;
    const allowStatusToCheck = isNationalEvent
      ? getNationalOfferStatus(offerAllowances?.[cardIndex]?.allowances)
      : allowStatusFromFirstAllow;

    const allowanceFinalAPIStatus = [
      EEVENT_STATUS.PENDING_WITH_VENDOR,
      EEVENT_STATUS.PENDING_WITH_MERCHANT,
    ].includes(allowStatusToCheck)
      ? EEVENT_STATUS.REJECTED
      : EEVENT_STATUS.CANCELED;

    if (allowanceId) {
      rejectCancelAllowance({
        URL_PARAMS: [allowanceId, allowanceFinalAPIStatus],
      });
    }
    if (isLastOfferOrPromo) {
      dispatch(
        allowanceTempWorkHandler({
          isNewAllowance: true,
          allowanceData: {},
        })
      );
      handleOfferCardConfig({}, offerAllowances?.length, true);
      dispatch(
        allowanceNewCardConfiguration({
          isNewAllowance: false,
          stepperType: "offerAllowances",
          isAddAnotherOffer: true,
        })
      );
    }
    return null;
  };

  const removeCardHandler = (cardIndex, cardItemIndex) => {
    const checkAllPromotionCardsStatus = checkAllowancePromoCountCardsStatus();
    const promotionNo =
      promotionsLists[cardIndex]?.promotionsList[cardItemIndex]?.promotionId;
    const modalConfirmBtnName = deleteCardHandler;
    const draftEventStatus =
      eventDetailsData?.eventStatus === EEVENT_STATUS.DRAFT;
    const promoRemoveTitleText =
      "Are you sure you want to remove this Promotion?";
    const promoRemoveWarningText = "All promo details will be lost";
    updatePromoDetailsHiddenPricingCheckboxesSlice();

    //setModalNameHandler(modalConfirmBtnName);
    setCommonModalData({
      popUpModalTitle:
        isPromoDirectionalChangesEnable ||
        draftEventStatus ||
        checkAllPromotionCardsStatus
          ? promoRemoveTitleText
          : "You cannot remove this Promotion",
      warningMessage:
        draftEventStatus || checkAllPromotionCardsStatus
          ? promoRemoveWarningText
          : isPromoDirectionalChangesEnable
          ? "Please add another promotion."
          : `${commonWarningLabel} Remove Promotion #${promotionNo}`,
      confirmBtnTitle: "Confirm",
      cancelBtnTitle: "Cancel",
      showHideBtns: isPromoDirectionalChangesEnable
        ? true
        : draftEventStatus || checkAllPromotionCardsStatus,
      modalNameHandler: modalConfirmBtnName,
      cardIndex: cardIndex,
      cardItemIndex: cardItemIndex,
      height: "",
      isLastOfferPromotion: false,
      cancelBtnHandler: (isLastOfferOrPromotion: boolean, title: string) =>
        null,
    });
    setModalPopupOpen(true);
  };

  const rejectCardHandler = (cardIndex, cardItemIndex) => {
    const checkAllPromotionCardsStatus = checkAllowancePromoCountCardsStatus();
    const promotionNo =
      promotionsLists[cardIndex]?.promotionsList[cardItemIndex]?.promotionId;
    const modalConfirmBtnName = rejectCancelCard;
    const isLastOfferOrPromo = checkIsLastOfferOrPromotion(eventDetailsData);
    const isMultiple = !isLastOfferOrPromo ? "MULTIPLE" : "SINGLE";
    const messageLabel = getModelLabelMessage(
      "REJECT",
      true,
      userRole,
      isMultiple
    );
    updatePromoDetailsHiddenPricingCheckboxesSlice();
    setCommonModalData({
      popUpModalTitle: messageLabel.LABEL,
      warningMessage: messageLabel.WARNING,
      confirmBtnTitle: isLastOfferOrPromo
        ? Last_Reject_Modal_Labels_For_Promo.CONFIRM_BUTTON_TITLE
        : "Confirm",
      cancelBtnTitle: isLastOfferOrPromo
        ? Last_Reject_Modal_Labels_For_Promo.CANCEL_BUTTON_TITLE
        : "Cancel",
      showHideBtns:
        isPromoDirectionalChangesEnable || checkAllPromotionCardsStatus,
      modalNameHandler: modalConfirmBtnName,
      cardIndex: cardIndex,
      cardItemIndex: cardItemIndex,
      height: "",
      isLastOfferPromotion: isLastOfferOrPromo,
      cancelBtnHandler: cancelOrRejectEventHandler,
    });
    setModalPopupOpen(true);
  };

  const cancelCardHandler = (cardIndex, cardItemIndex) => {
    const modalConfirmBtnName = rejectCancelCard;
    const checkCancelStatus = checkAllowancePromoCountCardsStatus();
    const promotionNo =
      promotionsLists[cardIndex]?.promotionsList[cardItemIndex]?.promotionId;
    const isLastOfferOrPromo = checkIsLastOfferOrPromotion(eventDetailsData);
    const isMultiple = !isLastOfferOrPromo ? "MULTIPLE" : "SINGLE";
    const messageLabel = getModelLabelMessage(
      "CANCEL",
      true,
      userRole,
      isMultiple
    );
    updatePromoDetailsHiddenPricingCheckboxesSlice();
    setCommonModalData({
      popUpModalTitle: messageLabel.LABEL,
      warningMessage: messageLabel.WARNING,
      confirmBtnTitle: isLastOfferOrPromo
        ? Last_Cancel_Modal_Labels_For_Promo.CONFIRM_BUTTON_TITLE
        : "Confirm",
      cancelBtnTitle: isLastOfferOrPromo
        ? Last_Cancel_Modal_Labels_For_Promo.CANCEL_BUTTON_TITLE
        : "Cancel",
      showHideBtns: isPromoDirectionalChangesEnable || checkCancelStatus,
      modalNameHandler: modalConfirmBtnName,
      cardIndex: cardIndex,
      cardItemIndex: cardItemIndex,
      height: "",
      isLastOfferPromotion: isLastOfferOrPromo,
      cancelBtnHandler: cancelOrRejectEventHandler,
    });
    setModalPopupOpen(true);
  };

  const deleteCardHandler = (cardIndex, cardItemIndex) => {
    setModalPopupOpen(false);
    const id = promotionsLists[cardIndex]?.promotionsList[cardItemIndex]?.id;
    if (id) {
      updatePromoDetailsHiddenPricingCheckboxesSlice();
      deletePromotionData({ URL_PARAM: "", queryParams: { promoId: id } });
    }
    return null;
  };

  const rejectCancelCard = (cardIndex, cardItemIndex, isLastOfferOrPromo) => {
    setModalPopupOpen(false);
    const promotionId =
      promotionsLists[cardIndex]?.promotionsList[cardItemIndex]?.id;
    const promoStatus =
      promotionsLists[cardIndex]?.promotionsList[cardItemIndex]
        ?.promotionWorkflowStatus;
    const promotFinalStatus = [
      EEVENT_STATUS.PENDING_WITH_VENDOR,
      EEVENT_STATUS.PENDING_WITH_MERCHANT,
    ].includes(promoStatus)
      ? EEVENT_STATUS.REJECTED
      : EEVENT_STATUS.CANCELED;
    //const promoStatus = EEVENT_STATUS.REJECTED;

    if (promotionId) {
      rejectCancelPromotion({ URL_PARAMS: [promotionId, promotFinalStatus] });
      updatePromoDetailsHiddenPricingCheckboxesSlice();
    }
    if (isLastOfferOrPromo) {
      dispatch(isAddingPromoConfig(true));
      dispatch(isLastPromoStep(false));
      handleAddAnotherPromoAllowance();
    }
    return null;
  };

  const cancelCard = (cardIndex, cardItemIndex) => {
    setModalPopupOpen(false);
    const promotionId =
      promotionsLists[cardIndex]?.promotionsList[cardItemIndex]?.id;
    const promoStatus = EEVENT_STATUS.CANCELED;

    if (promotionId) {
      rejectCancelPromotion({ URL_PARAMS: [promotionId, promoStatus] });
    }
  };
  const inValidCardHandler = () => {
    editAllowanceHandler();
  };

  const editAllowanceHandler = (event?) => {
    event?.stopPropagation();
    openOfferOnConfirm();
  };

  const resetSectionAndAmountsSlices = () => {
    dispatch(resetOfferSectionsData());
    dispatch(resetOfferSectionsEnableConfig());
    dispatch(resetOfferAmountsData());
    dispatch(resetNationalDivisionsConfig());
    dispatch(resetIsOfferSectionUpdated());
    dispatch(resetDivisionWiseShrdWhseData());
  };

  const openOfferOnConfirm = async () => {
    dispatch(allowanceTempWorkReset());
    resetSectionAndAmountsSlices();
    dispatch(
      offerCardConfiguration({
        editCardConfig: { [cardIndex]: true },
        openCardConfig: { ...openCardConfig, [cardIndex]: true },
      })
    );
    const apiCall = isNationalEvent
      ? postNationalOfferEdit
      : postAllowanceEditTempWorkData;

    const offerId = isNationalEvent
      ? offerAllowances?.[cardIndex]?.offerNumber
      : cardContainerData?.offerId;

    try {
      const result = await apiCall({ URL_PARAM: offerId });

      if (result?.data) {
        let sortedData = _.cloneDeep(result.data);
        sortedData = isNationalEvent
          ? sortedData?.sort(compareDivisionIdFn)
          : sortedData;

        dispatch(
          allowanceTempWorkHandler({
            allowanceData: sortedData,
            isTempLoaded: true,
          })
        );
        sessionStorage.setItem("OFFER_ID", result.data.offerAllowancesId);
      }
    } catch (error) {
      console.error("Error fetching offer data:", error);
    }
  };

  const handleCancel = async event => {
    if (isAllowanceCard) {
      event?.stopPropagation();
      setValue("isAllowanceChanged", false);
      dispatch(
        setAllowanceFormInfo({
          allowanceFormData: {
            [cardIndex]: { cancel: true },
          },
        })
      );
      if (isComingFromtask(taskType, eventDetailsData) && isEditEnable) {
        setIsPopupOpen(true);
      } else {
        isAllowanceFeatureEnabled &&
          isAllowanceTypeChanged?.[cardIndex] &&
          dispatch(
            setAllowTypeChange({
              isAllowanceTypeChanged: { [cardIndex]: false },
            })
          );
        onCancelOfferCard();
      }
    }
  };

  const onCancelOfferCard = async () => {
    dispatch(allowanceTempWorkReset());
    resetSectionAndAmountsSlices();
    !isNationalEvent &&
      allowanceTempWorkData?.tempWorkAllowanceId &&
      (await deleteAllowanceTempWorkData({
        URL_PARAM: allowanceTempWorkData?.tempWorkAllowanceId,
      }));
    isNationalEvent &&
      eventDetailsData?.id &&
      (await deleteNationalAllowanceTempWorkData({
        URL_PARAM: eventDetailsData?.id,
      }));
    sessionStorage.removeItem("TEMPWORK_ID");
    sessionStorage.removeItem("OFFER_ID");
    dispatch(
      offerCardConfiguration({
        editCardConfig: { [cardIndex]: false },
        openCardConfig: { ...openCardConfig, [cardIndex]: false },
        offerData: `${offerAllowances?.[cardIndex]?.id}_${_.uniqueId()}`,
      })
    );
    const offerStatus = `offerAllowances[${cardIndex}].allowances[0].allowanceStatus`;
    const statusToCheck = isNationalEvent
      ? getNationalOfferStatus(offerAllowances?.[cardIndex]?.allowances)
      : getValues(offerStatus);
    if (
      [
        EEVENT_STATUS.AGREED,
        EEVENT_STATUS.ACTIVE,
        EEVENT_STATUS.EXECUTED,
      ].includes(statusToCheck)
    ) {
      const deleteMutationHandler = isNationalEvent
        ? deleteNationalAllowancePendingCancelData
        : deleteAllowancePendingCancelData;
      const allowanceNo = offerAllowances?.[cardIndex]?.id;
      const offerNumber = offerAllowances?.[cardIndex]?.offerNumber;
      await deleteMutationHandler({
        URL_PARAM: isNationalEvent ? offerNumber : allowanceNo,
      });
      dispatch(
        setAllowancesDeletePendingCancel({ isAllowanceCancelled: true })
      );
    }
  };

  const cancelOrRejectEventHandler = (
    isLastOfferPromotion: boolean,
    title: string
  ) => {
    if (isLastOfferPromotion) {
      // run only when last valid offer/promo available
      let actionRequest = postReject;
      if (title.includes("Cancel")) {
        actionRequest =
          userRole === EUSER_ROLES.MERCHANT
            ? postCancel
            : postAgreedToRequestCancel;
      }
      executeApiCall(actionRequest);
    }
    document.body.style.overflow = "visible";
    return null;
  };

  const renderCancelButton = () => {
    return (
      <span
        id="abs-card-container-efconstants"
        className={`mr-3 ml-5 font-semibold text-[#1B6EBB] cursor-pointer ${efConstants.componentClassName.RENDER_CANCEL_BUTTON}`}
        onClick={handleCancel}
      >
        Cancel Editing
      </span>
    );
  };
  const offersData = getValues(`offerAllowances[${cardIndex}]`);
  const isAllowanceFailed = offersData?.allowances?.find(
      item => item?.allowanceProcessStatus === "FAILED"
    ),
    offerProcessedStatus = isAllowanceFailed
      ? "FAILED"
      : getValues(
          `offerAllowances[${cardIndex}].allowances[0].allowanceProcessStatus`
        ),
    isRenderResendButton = [
      EEVENT_STATUS.AGREED,
      EEVENT_STATUS.READY,
      EEVENT_STATUS.ACTIVE,
      EEVENT_STATUS.EXECUTED,
      EEVENT_STATUS.CANCELED,
    ].includes(offerAllowanceStatus as EEVENT_STATUS);
  const handleAddAnotherPromoAllowance = () => {
    if (cardConfiguration.key === "Promotion") {
      setIsAddingAnotherPromotion(true);
      const newPromoIndex = promotionsLists?.[
        promotionCardIndex
      ]?.promotionsList?.filter(item => item.id).length;
      setNewPromotionIndex(newPromoIndex);
      dispatch(
        promoCardConfiguration({
          ...promoEditCardConfig,
          ...promoOpenCardConfig,
          isAddNewPromo: { [newPromoIndex]: true },
        })
      );
    } else if (isAllowanceCard) {
      // Disabling Add Another Allowance Trigger for now
      // setIsAddingAnotherPromotion(true);
    }
  };

  const layout = cardConfiguration.section?.replace(/ /g, "-").toLowerCase();

  const [isOpenCardStatus, setIsOpenCardStatus] = useState(
    eventCard.EVENT_PLAN_CARD === sectionIndex ||
      isInValidCardOpen ||
      isOpenCard?.[openCardKey]?.[cardIndex]
  );

  useEffect(() => {
    setIsOpenCardStatus(
      eventCard.EVENT_PLAN_CARD === sectionIndex ||
        isInValidCardOpen ||
        isOpenCard?.[openCardKey]?.[cardIndex] ||
        isCurrentCardExpandedOffer
    );
  }, [
    isInValidCardOpen,
    isOpenCard,
    eventCard.EVENT_PLAN_CARD,
    isCurrentCardExpandedOffer,
  ]);
  const ContentComponent = lazy(
    () => import(`../${layout}/${layout}-card-content`)
  );

  useEffect(() => {
    if (isPostResnedOfferError) {
      dispatch(offerResendStatus({ isResendOfferSuccess: true }));
    }
  }, [isPostResnedOfferError]);

  useEffect(() => {
    Object?.keys(eventProgressConfigData)?.length &&
      setIsEventStatusFormData(eventProgressConfigData);
  }, [JSON.stringify(eventProgressConfigData)]);

  const promotionCardIndex = 0;

  const showCancelEvent = () => {
    let statusValue = "";
    const allowanceStatus = offerAllowances[cardIndex]?.overrideAllowanceStatus;
    const updatedUser =
      offerAllowances[cardIndex]?.updateUser?.type?.toUpperCase();
    const allowStatusToCheck = isNationalEvent
      ? getNationalOfferStatus(offerAllowances?.[cardIndex]?.allowances)
      : offerAllowances[cardIndex]?.allowances[0]?.allowanceStatus;

    if (
      allowanceStatus &&
      [EEVENT_STATUS.CANCELED].includes(
        offerAllowances[cardIndex]?.overrideAllowanceStatus
      ) &&
      ([EUSER_ROLES.VENDOR].includes(updatedUser) ||
        [EEVENT_STATUS?.AGREED_PENDING].includes(eventStatus)) &&
      [EEVENT_STATUS.AGREED_PENDING].includes(allowStatusToCheck)
    ) {
      statusValue = "Cancel Request";
    }

    return statusValue;
  };

  const isOfferCardOpen = () => {
    return (
      (editCardConfig &&
        getObjectValues(editCardConfig).includes(true) &&
        (!eventDetailsData?.inValidAllowances?.length ||
          !eventDetailsData?.inValidPromotions?.length)) ||
      (openCardConfig &&
        getObjectValues(openCardConfig).includes(true) &&
        !eventDetailsData?.inValidAllowances?.length &&
        !eventDetailsData?.inValidPromotions?.length &&
        allowanceTempWorkData &&
        allowanceTempWorkData?.allowanceTypeSpecification)
    );
  };

  const onChangeHandler = async () => {
    if (isComingFromtask(taskType, eventDetailsData) && isEditEnable) {
      setIsPopupOpen(false);
      const onDeleteTemp =
        (await allowanceTempWorkData?.tempWorkAllowanceId) &&
        deleteAllowanceTempWorkData({
          URL_PARAM: allowanceTempWorkData?.tempWorkAllowanceId,
        });
      onDeleteTemp &&
        dispatch(
          offerCardConfiguration({
            offerData: `${offerAllowances[cardIndex]?.id}_${_.uniqueId()}`,
          })
        );
    }
  };

  const CardWrapperContent = (
    <CardWrapper
      cardContainerData={cardContainerData}
      cardIndex={promotionCardIndex} //card index for promotion will always be 0.
      cardItemIndex={newPromotionIndex}
      isNew={true}
      cardConfiguration={cardConfiguration}
      ContentComponent={ContentComponent}
      step={step}
      isEditMode={false}
      sectionIndex={sectionIndex}
      handleCancel={handleCancel}
    />
  );
  const isPromoOpen = () => {
    return isEdit === true || isDatesUpdated;
  };

  const onCloseHandler = () => {
    setModalPopupOpen(false);
    document.body.style.overflow = "visible";
  };

  const isCancelShow =
    eventDetailsData?.eventType === EVENT_TYPE.NCDP ||
    (eventDetailsData?.inValidAllowances?.length && isEventCardOpen);

  const getCancelLabel = () => {
    const cancelValue = showCancelEvent();
    return cancelValue ? (
      <div
        id="abs-card-container-cancel-value-section"
        className={`text-[14px] mx-2 min-w-[8%] text-[#AB4205] pointer-events-none ${efConstants.componentClassName.GET_CANCEL_LABEL}`}
      >
        {cancelValue}
      </div>
    ) : null;
  };
  const resendTheStatus = () => {
    const eventId = eventDetailsData?.id,
      offerNumber = offerAllowances[cardIndex]?.offerNumber;
    postResendOffer({ eventId, offerNumber });
  };
  const onPopupCloseHandler = () => {
    setIsPopupOpen(false);
  };
  const showPopup = (
    <div>
      {isPopupOpen ? (
        <CommonModal
          isModalPopupOpen={isPopupOpen}
          setModalPopupOpen={setIsPopupOpen}
          title={OFFER_POPUP_MESSAGES?.TITLE}
          confirmBtnTitle={OFFER_POPUP_MESSAGES?.CONFIRM_BTN_TITLE}
          cancelBtnTitle={OFFER_POPUP_MESSAGES?.CANCEL_BTN_TITLE}
          showHideBtns={true}
          height={316}
          onClose={onPopupCloseHandler}
          cancelBtnHandler={onPopupCloseHandler}
          modalNameHandler={onChangeHandler}
          isCancelAmountStep={true}
          minBtnWidth={240}
        />
      ) : null}
    </div>
  );
  const renderOfferActions = () => {
    return (
      ![EEVENT_STATUS.REJECTED, EEVENT_STATUS.CANCELED].includes(
        offerAllowanceStatus as EEVENT_STATUS
      ) &&
      offerHeader &&
      isEditRemoveButtonsRender &&
      ![EEVENT_STATUS.CANCELED].includes(
        offerAllowances[cardIndex]?.overrideAllowanceStatus
      ) &&
      !isCrossUserEditCard
    );
  };

  const isAddNewPromoVisible = () => {
    const checkStartDateIsPast = compareAsc(
      new Date(eventDetailsData?.startDate),
      new Date(Date.now())
    );
    return !promoStatusList()?.length ? checkStartDateIsPast > 0 : false;
  };

  const isVendorAddNewPromo = () => {
    return userRole === "VENDOR" && isPromoChanges()
      ? [EEVENT_STATUS.DRAFT, EEVENT_STATUS.PENDING_WITH_VENDOR].includes(
          eventStatus as EEVENT_STATUS
        )
      : true;
  };

  const { basePath } = useGetAppBasePath();

  const handleDsdFundingRedirectClick = () => {
    const searchParams = getDsdFundingViewSearchParams({
      cardIndex,
      offerAllowances,
      promotionsLists,
      isAllowanceCard,
      isPromoCard,
    });
    navigate(
      `${basePath}?isDsdView=true&${searchParams.key}=${searchParams.value}`
    );
  };

  const checkIsAlestOneDsdOffer = () => {
    return (
      offerAllowances?.filter(offer =>
        DISTRIBUTORS_IND_KEYS.includes(offer?.createInd)
      )?.length > 0
    );
  };
  const offerNumber = offerAllowances?.[cardIndex]?.offerNumber;

  const isAllowanceTypeChangedData = hasCreateIndChange(
    eventDetailsData,
    isAllowanceFeatureEnabled,
    offerNumber
  );

  const renderOfferDealSheetButton = () => {
    return !isAllowanceTypeChangedData ? (
      <Button
        variant="secondary"
        className="!min-w-[10%] !w-[45%]  xl:!w-[30%]"
        onClick={() => {
          isAgreementClicked.current = true;
          setIsAllowanceAgreementVisible(true);
        }}
      >
        {VIEW_OFFER_DEAL_SHEET_BUTTON}
      </Button>
    ) : null;
  };
  const handleViewOfferSummary = () => {
    const allowType = getAllowanceTypeByPerformance(
      offerAllowances?.[cardIndex]?.allowances?.[0].performance
    );
    const createInd = getCreateIndByLocation(offerAllowances?.[cardIndex], 0);
    const offerGroup =
      efConstants?.CREATEIND_ALLOWANCE_TO_BE_CREATED_OPTION_MAPPER[createInd];
    const allowanceType = efConstants?.ALLOWANCE_TYPE_NAME_MAPPER[allowType];
    const allowTypeTableConfig = getTableConfigOnAllowType(
      allowanceType,
      offerGroup
    );
    dispatch(setAllowanceTableColsGroupConfig(allowTypeTableConfig));
    setIsOpenOfferSummaryModal(true);
    if (
      allowanceType === ALLOWANCE_TYPES.SCAN.key ||
      allowanceType === "SCAN"
    ) {
      dispatch(
        setSelectedSwitchValue({
          selectedSwitch: "Unit",
        })
      );
    }
  };
  const renderViewSummaryLink = () => {
    return !isAllowanceTypeChangedData ? (
      <span
        className="mr-2 flex text-[#1B6EBB]"
        onClick={e => {
          e.stopPropagation();
          handleViewOfferSummary();
        }}
      >
        <Eye className="ml-2 mr-1" />
        {VIEW_OFFER_SUMMARY}
      </span>
    ) : null;
  };

  const renderDsdFundingButton = () => {
    const createInd = getCreateIndByLocation(offerAllowances?.[cardIndex], 0);
    const isDsd = DISTRIBUTORS_IND_KEYS.includes(createInd);
    const dsdKillSwitch =
      appConstants.DSD_FUNDING_KILL_SWITCH &&
      isMerchantUser &&
      !isNationalEvent &&
      !isNCDP;
    return (dsdKillSwitch && isDsd && isAllowanceCard) ||
      (dsdKillSwitch && isPromoCard && checkIsAlestOneDsdOffer()) ? (
      <div
        className="flex justify-end underline font-semibold text-[#1B6EBB] cursor-pointer"
        onClick={handleDsdFundingRedirectClick}
      >
        <span>DSD Funding</span>
      </div>
    ) : null;
  };
  const checkIsValidPromoPresent = () => {
    return promoStatusList()?.length;
  };

  const checkToShowAddNewBtn = () => {
    const indexval = eventDetailsData?.promotionsList?.length - 1 || 0;
    if (hiddenPricingFeatureEnabled) {
      if (!isMerchantUser) {
        return checkIsValidPromoPresent()
          ? checkHiddenPrice(eventDetailsData?.promotionsList[indexval])
          : true;
      } else {
        return true;
      }
    }
    return true;
  };

  const parentTag2 = document.querySelectorAll(
    "#abs-card-container-render-resend-button-sec3"
  );

  if (isAllowanceTypeChanged?.[cardIndex]) {
    parentTag2?.forEach?.(item => {
      const immediateDiv = item?.querySelectorAll(":scope > div");
      if (isAllowanceTypeChanged?.[cardIndex] && immediateDiv) {
        immediateDiv?.forEach(div => {
          div?.classList?.remove(
            "bg-gray-208",
            "border",
            "border-gray-204",
            "rounded-lg",
            "drop-shadow"
          );
        });
      }
    });
  }

  const renderResendButton = (
    <div id="abs-card-container-processed-status-cont">
      {isAllowanceCard && isRenderResendButton ? (
        <div
          id="abs-card-container-processed-status-sec"
          className={`flex gap-4 justify-end mb-[5px] ${efConstants.componentClassName.RENDER_RESEND_BUTTON}`}
        >
          <div id="abs-card-container-processed-status-sec2">
            Processed Status:{" "}
            <span
              className={`${
                isAllowanceFailed ? "text-[#BF2912]" : "text-[#2D7207]"
              } font-bold text-sm`}
            >
              {_.capitalize(offerProcessedStatus) || "-"}
            </span>
          </div>
          <RBAC
            divisionIds={divisionIds}
            permissionsOnly={[cardConfiguration.permission]}
            simsVendors={negotiationSimsVendors}
          >
            {isRenderResendButton && isAllowanceFailed ? (
              <div
                className="font-semibold text-[#1B6EBB] cursor-pointer"
                id="abs-card-container-resend-text"
              >
                <span onClick={resendTheStatus}>
                  <RotateCcw
                    width={16}
                    height={16}
                    color="#1B6EBB"
                    className="inline-block mb-[3px] mr-[8px]"
                    strokeWidth={3}
                  />
                  Resend
                </span>
              </div>
            ) : null}
          </RBAC>
        </div>
      ) : null}
    </div>
  );
  const shouldRenderViewSummaryLink = () => {
    const allowance = offerAllowances?.[cardIndex];
    const allowanceType = getAllowanceTypeByPerformance(
      allowance?.allowances?.[0]?.performance
    );

    if (eventDetailsData?.eventType === EVENT_TYPE.NDP) {
      return !(
        allowance?.createInd === ALLOWANCE_TYPES.HEADERFLAT.createInd[0] &&
        allowanceType === ALLOWANCE_TYPES.HEADERFLAT.label
      );
    } else {
      return false;
    }
  };

  const renderHtml = (
    <>
      {showPopup}
      {isAllowanceAgreementVisible && (
        <ViewAgreementModal
          setIsAllowanceAgreementVisible={setIsAllowanceAgreementVisible}
          eventId={eventDetailsData?.id}
          divisionName={divisionIds?.[0]}
          allowanceType={offerAllowances?.[cardIndex]?.createInd}
          offerNumber={offerAllowances?.[cardIndex]?.offerNumber}
          allowanceChangeStatus={
            offerAllowances?.[cardIndex]?.allowanceChangeStatus
          }
          eventStatus={eventDetailsData?.eventStatus}
          allowances={offerAllowances?.[cardIndex]?.allowances}
          eventType={eventDetailsData?.eventType}
        />
      )}
      {isModalPopupOpen ? (
        <CommonModal
          isModalPopupOpen={isModalPopupOpen}
          setModalPopupOpen={setModalPopupOpen}
          deleteCardHandler={deleteCardHandler}
          title={commonModalData?.popUpModalTitle}
          warningMessage={commonModalData?.warningMessage}
          confirmBtnTitle={commonModalData?.confirmBtnTitle}
          cancelBtnTitle={commonModalData?.cancelBtnTitle}
          modalNameHandler={commonModalData?.modalNameHandler}
          showHideBtns={commonModalData?.showHideBtns}
          cardIndex={commonModalData?.cardIndex}
          cardItemIndex={commonModalData?.cardItemIndex}
          height={commonModalData?.height}
          onClose={onCloseHandler}
          isLastOfferPromotion={commonModalData?.isLastOfferPromotion}
          cancelBtnHandler={commonModalData?.cancelBtnHandler}
        />
      ) : null}
      {isOpenOfferSummaryModal && (
        <OffersSummaryModal
          setIsOpenOfferSummaryModal={setIsOpenOfferSummaryModal}
          allowanceType={getAllowanceTypeByPerformance(
            offerAllowances?.[cardIndex]?.allowances?.[0]?.performance
          )}
          isOpenOfferSummaryModal={isOpenOfferSummaryModal}
          cardContainerData={cardContainerData}
        />
      )}
      <Card
        title={
          cardConfiguration.section === "Allowance"
            ? offerHeader || "Offer"
            : "Promotion"
        }
        // isOpen={isOpenCardStatus}
        onChange={value => {
          handleCardOpenStatus(true);
          dispatch(
            eventProgressDataHandler({
              selected: "Allowance",
            })
          );
        }}
        //customHeader={true}
        className={`min-w-[600px] z-index-dynamic-class
        ${isOfferCard && !isEditEnable?.[cardIndex] ? "z-[1]" : ""}`}
      >
        <CardHeader
          className={`mx-0 shadow-none border-b border-gray-204 rounded-t-xl ${
            isOpenCardStatus ? "rounded-b-none" : ""
          }
            ${
              isAllowanceCard && isEditEnable?.[cardIndex]
                ? "!sticky z-[8] bg-white top-[53px] abs-card-container-active-offer-section"
                : ""
            }`}
          onClick={() => {
            !isAgreementClicked.current &&
              setIsOpenCardStatus(!isOpenCardStatus);
            handleCardOpenStatus(!isOpenCardStatus);
            isAgreementClicked.current = false;
          }}
        >
          <div
            id="abs-card-container-is-agreement-clicked-sec"
            className={`w-full -mb-[1px] px-4 cursor-pointer py-[12px]
              ${efConstants.componentClassName.RENDER_HTML_CARD_HEADER}
              `}
          >
            <section
              className={`flex items-center ${
                isAllowanceCard ? "justify-between" : ""
              }`}
            >
              <section className="flex max-w-[55%]">
                <div
                  className="flex flex-col items-center justify-center min-w-[32px] w-[32px] min-h-[32px] h-[32px]"
                  id="abs-card-container-isopen-card-status-sec"
                >
                  {isOpenCardStatus ? (
                    <ChevronsDownUp
                      height={12}
                      strokeWidth={1}
                      className={
                        "min-w-[24px] min-h-[24px] transition duration-200"
                      }
                    />
                  ) : (
                    <ChevronsUpDown
                      height={12}
                      strokeWidth={1}
                      className={
                        "min-w-[24px] min-h-[24px] transition duration-200"
                      }
                    />
                  )}
                </div>
                <div
                  id="abs-card-container-allowance-sec"
                  className={`flex items-center justify-start  ${
                    cardConfiguration.key === "Allowance"
                      ? `${
                          appConstants.IS_ENABLE_REDESIGN_ALLOWANCE_CHANGES
                            ? "w-[90%]"
                            : "w-[42%]"
                        } overflow-hidden text-ellipsis whitespace-nowrap`
                      : ""
                  }`}
                >
                  <Tooltip
                    zIndex={10}
                    anchor="right"
                    label={offerHeader ? offerHeader : ""}
                  >
                    <span className="font-extrabold text-lg leading-6 text-dark-text cursor-text">
                      {cardConfiguration.section === "Allowance"
                        ? offerHeader || "Offer"
                        : "Promotion"}
                    </span>
                  </Tooltip>
                </div>
              </section>
              <section
                className={`flex items-center ${
                  isAllowanceCard ? "justify-end max-w-[45%] w-full" : "gap-4"
                }`}
              >
                {offerAllowanceStatus ? (
                  <>
                    <StatusBadge status={offerAllowanceStatus} />
                    {getCancelLabel()}
                  </>
                ) : null}
                {renderOfferActions() ? (
                  <RBAC
                    divisionIds={divisionIds}
                    permissionsOnly={[cardConfiguration.permission]}
                    simsVendors={negotiationSimsVendors}
                  >
                    {eventDetailsData?.eventType !== EVENT_TYPE.NCDP &&
                    !isEditEnable?.[cardIndex] ? (
                      <EditAndRemoveButtons
                        onEdit={event => editAllowanceHandler(event)}
                        onRemove={event => removeAllowanceHandler(event)}
                        onReject={event =>
                          rejectAllowanceHandler("REJECT", cardIndex)
                        }
                        onCancel={event =>
                          cancelAllowanceHandler("CANCEL", cardIndex)
                        }
                        disabled={isEditEnable}
                        isAddAnotherOffer={isAddAnotherOffer}
                        offerAllowanceStatus={offerAllowanceStatus}
                        isEditDisabled={
                          isAllowanceTypeChangedData || !productSources?.length
                        }
                      />
                    ) : (
                      <>{!isCancelShow ? renderCancelButton() : null}</>
                    )}
                  </RBAC>
                ) : null}
                {isAllowanceCard ? (
                  <>
                    {shouldRenderViewSummaryLink()
                      ? renderViewSummaryLink()
                      : null}
                    {renderDsdFundingButton()}
                    {renderOfferDealSheetButton()}
                  </>
                ) : null}
                {promotionHeader && (
                  <div
                    className="flex text-lg ml-5"
                    id="abs-card-container-promotion-header-sec"
                  >
                    <Divider className="mx-3" height={24} color="#C8DAEB" />
                    {promotionHeader}
                  </div>
                )}
                {isPromoCard && renderDsdFundingButton()}
              </section>
            </section>
            {isAllowanceCard && (
              <OfferSectionLinks {...{ isEditEnable, cardIndex }} />
            )}
          </div>
        </CardHeader>
        {isOpenCardStatus && (
          <CardBody>
            <div
              id="abs-card-container-render-resend-button-cont"
              className={`${efConstants.componentClassName.RENDER_HTML_CARD_CONTENT}`}
            >
              {renderResendButton}
              <div id="abs-card-container-render-resend-button-sec2">
                {cardData
                  ?.slice(0, isEditEnable?.[cardIndex] ? 1 : cardData.length)
                  ?.map((cardItem, cardItemIndex) => (
                    <div
                      className="mb-4"
                      id="abs-card-container-render-resend-button-sec3"
                    >
                      <MultipleCardContainer
                        key={cardItemIndex}
                        cardContainerData={cardItem}
                        cardIndex={cardIndex}
                        allowanceIndex={cardItemIndex}
                        cardConfiguration={cardConfiguration}
                        step={step}
                        sectionIndex={sectionIndex}
                        removeCardHandler={removeCardHandler}
                        rejectCardHandler={rejectCardHandler}
                        cancelCardHandler={cancelCardHandler}
                        inValidCardHandler={inValidCardHandler}
                      />
                    </div>
                  ))}
              </div>
              {isOfferCard && !isOfferAttachmentFlagEnabled() && (
                <div id="abs-card-attachemnt-section">
                  <div
                    className="bg-gray-208 border border-gray-204 rounded-lg drop-shadow
                relative z-index-dynamic-class "
                  >
                    <div className="flex flex-auto items-center w-full h-[50px] -mb-[1px] px-4 border-b border-gray-204 justify-between">
                      <div
                        className="flex items-center justify-start md:w-full"
                        id="abs-allowance-card-header-preview-warehouse-allowance-amount-container"
                      >
                        <p
                          className="text-[15px] leading-4 mr-1 font-bold cursor-text text-[#2B303C] select-none"
                          id="abs-allowance-card-header-preview-warehouse-text"
                        >
                          Attachments
                        </p>
                        <Divider className="mx-3" height={24} color="#C8DAEB" />
                        <span
                          className="text-[#5A697B] mr-5 text-[14px] break-all"
                          id="abs-allowance-id-preview-value"
                        >
                          {offerAllowances?.[cardIndex]?.fileNames?.length
                            ? offerAllowances?.[cardIndex]?.fileNames
                            : "Not applicable"}
                        </span>
                        {![
                          EEVENT_STATUS?.CANCELED,
                          EEVENT_STATUS?.REJECTED,
                        ].includes(eventStatus) ? (
                          <OfferFileUploadContainer
                            label={OFFER_UPLOAD_LABEL}
                            isEdit={isEventCardOpen || isPromoOpen()}
                            isEditDisabled={isAllowanceTypeChangedData}
                            offerNumber={
                              offerAllowances?.[cardIndex]?.offerNumber
                            }
                            fileNames={offerAllowances?.[cardIndex]?.fileNames}
                            cardIndex={cardIndex}
                          />
                        ) : null}
                      </div>
                    </div>
                  </div>
                </div>
              )}
              {isAddPromoAllowanceButtonsRender &&
                !isAddingAnotherPromotion &&
                cardConfiguration.allAnotherItem !==
                  efConstants.ADD_ANOTHER_ALLOWANCE &&
                renderAddPromotionButton() &&
                isAddNewPromoVisible() &&
                checkToShowAddNewBtn() &&
                isVendorAddNewPromo() && (
                  <RBAC
                    divisionIds={divisionIds}
                    permissionsOnly={[cardConfiguration.permission]}
                    simsVendors={negotiationSimsVendors}
                  >
                    {
                      <Button
                        width={302}
                        className={"mt-5"}
                        variant="secondary"
                        onClick={handleAddAnotherPromoAllowance}
                        disabled={
                          isOfferDisabled ||
                          isEventCardOpen ||
                          isOfferCardOpen()
                        }
                      >
                        {`+ ${cardConfiguration.allAnotherItem}`}
                      </Button>
                    }
                  </RBAC>
                )}
              {isAddingAnotherPromotion && (
                <div
                  className="mt-[30px]"
                  id="abs-card-container-card-wrapper-content-cont"
                >
                  <div id="abs-card-container-wrapper-content-sec1">
                    {CardWrapperContent}
                  </div>
                  {/* {!isPromoCardOnLastStep?.isLastPromoStep ? (
                  <Button
                    size="md"
                    width={260}
                    variant="secondary"
                    onClick={() => {
                      setIsAddingAnotherPromotion(false);
                      dispatch(isAddingPromoConfig(false));
                      dispatch(
                        promoCardConfiguration({
                          isAddNewPromo: { 0: false },
                        })
                      );
                    }}
                  >
                    Cancel New{" "}
                    {`${
                      cardConfiguration.key === "Promotion"
                        ? "Promo"
                        : cardConfiguration.key
                    }`}{" "}
                    Creation
                  </Button>
                ) : null} */}
                </div>
              )}
            </div>
          </CardBody>
        )}
      </Card>
    </>
  );

  const isApiLoading = () => {
    return (
      isDeletePromoDataloading ||
      isDeleteAllowanceTempDataloading ||
      isDeleteNationalAllowancePendingCancelLoading ||
      isAlowanceEditTempLoading ||
      isRejectCancelDataloading ||
      isRejectCancelAllowanceDataloading ||
      isDeleteAllowancePendingCancelLoading ||
      isDeleteOfferDataloading ||
      isNationalDeleteOfferDataloading ||
      isPostCancelPending ||
      isPostRejectionLoading ||
      isPostAgreedToRequestCancelLoading ||
      isNationalTempLoading
    );
  };

  const renderDetails = {
    isApiLoading: isApiLoading(),
    isPageLevelSpinner: true,
    isRenderMainHtml: true,
    renderHtml,
  };

  return <RenderStates details={renderDetails} />;
};

export default memo(CardContainer);
