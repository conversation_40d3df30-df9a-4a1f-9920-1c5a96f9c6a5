import React, { useEffect, useState } from "react";
import { sortSaveData } from "../../service/table-service";
import NationalAllowanceTableComponent from "./national-allowance-table-component";
import { appConstants } from "libs/utils/root-props/src/lib/app-constants/app-constants";
import { useSelectorWrap } from "@me/data-rtk";
import classNames from "classnames";

function DivisionWiseOfferSummaryTable({ divisionTableObj }) {
  const [filteredAndSortedTableData, setFilteredAndSortedTableData] = useState(
    []
  );
  const { data: allowGroupConfigObj } = useSelectorWrap(
    "allowanceTableColsGroupConfig_rn"
  );
  const [tableWrapClass, setTableWrapClass] = useState("");
    const {
    headerOnlyAmt,
    isHideColGroupHeader,
    showLeadDistributorSection,
    excludeByKey,
  } = allowGroupConfigObj || {};
    const setTableContainerClasses = () => {
    const wrapperClassName = classNames({
      tableWrapper: true,
      isHideColGroupHeader: isHideColGroupHeader,
    });
    setTableWrapClass(wrapperClassName);
  };
  useEffect(() => {
    if (allowGroupConfigObj && Object.keys(allowGroupConfigObj)?.length) {
      setTableContainerClasses();
    }
  }, [JSON.stringify(allowGroupConfigObj)]);

  useEffect(() => {
    const { tableData = [] } = divisionTableObj || {};
    const sortedTableData = sortSaveData(tableData);
    const sortedTableIds = sortedTableData.map(item => item?.itemId);
    const filteredAndSortedTableData = sortedTableIds?.map(itemId => {
      return tableData?.find(item => item?.itemId === itemId);
    });
    setFilteredAndSortedTableData(filteredAndSortedTableData);
  }, [divisionTableObj?.divisionId]);

  return (
    <div>
      <div className="font-semibold pb-2">
        {`${divisionTableObj?.divisionId} - ${
          appConstants.DIVISIONS_ID_NAME_MAP[divisionTableObj?.divisionId]
        }`}
      </div>
      <div className={tableWrapClass}>
        <NationalAllowanceTableComponent items={filteredAndSortedTableData} />
      </div>
    </div>
  );
}

export default DivisionWiseOfferSummaryTable;
