import { fireEvent, render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import Allowance<PERSON><PERSON><PERSON>ield from "./allowance-amount-field";
import { IAmountStepFieldProps } from "../props-types";
import "@testing-library/jest-dom";

jest.mock("./disabled-field", () => () => <div data-testid="disabled-field">Disabled Field</div>);

jest.mock("../../../../../../fields/allowance-atoms", () => ({
  InputText: ({ fieldProps, onChange, onFocus }: any) => (
    <input
      data-testid="input-text"
      placeholder={fieldProps?.label || "Amount"}
      onChange={(e) => onChange(e.target.value)}
      onFocus={(e) => onFocus(e.target.value)}
    />
  ),
}));

jest.mock(
  "../../../../../allowance/stepper/common-stepper/allowance-amount/allowance-amounts-services",
  () => ({
    getFieldWithHighletedWrapper: (component: JSX.Element) => component,
  })
);

jest.mock("../../../../../../fields/allowance-atoms", () => ({
  InputText: ({ fieldProps, onChange, onFocus, onWheel }: any) => (
    <input
      data-testid="input-text"
      placeholder={fieldProps?.label || "Amount"}
      onChange={(e) => onChange(e.target.value)}
      onFocus={(e) => onFocus(e.target.value)}
      onWheel={(e) => {
        e.currentTarget.blur();
        onWheel && onWheel(e);
      }}
    />
  ),
}));

describe("AllowanceAmountField Component", () => {
    const defaultProps: IAmountStepFieldProps = {
        fieldProps: { label: "Allowance Amount", required: true, registerField: "amount" },
        formControls: {
            control: {},
            register: jest.fn(),
            setValue: jest.fn(),
        },
        onChange: jest.fn(),
        error: null,
        prefixValue: "$",
        className: "test-class",
        disabled: false,
        value: "10",
        multiplier: 1,
        allowanceAmountFields: {},
        getEventData: jest.fn(),
        searchId: "123",
        baseId: "",
    };

    it("renders DisabledField when disabled is true", () => {
        render(<AllowanceAmountField {...defaultProps} disabled={true} />);
        expect(screen.getByTestId("disabled-field")).toBeInTheDocument();
    });

    it("renders InputText when disabled is false", () => {
        render(<AllowanceAmountField {...defaultProps} disabled={false} />);
        expect(screen.getByTestId("input-text")).toBeInTheDocument();
    });

    it("calls onChange when input value changes", async () => {
        render(<AllowanceAmountField {...defaultProps} />);
        const inputElement = screen.getByTestId("input-text");
        await userEvent.type(inputElement, "20");
        expect(defaultProps.onChange).toHaveBeenCalledWith("20");
    });

    it("calls setValue when input is focused", async () => {
        render(<AllowanceAmountField {...defaultProps} />);
        const inputElement = screen.getByTestId("input-text");
        await userEvent.click(inputElement);
  
        expect(defaultProps.formControls.setValue).toHaveBeenCalled();
  
        // Check if it was called with "amount" and any non-empty value
        expect(defaultProps.formControls.setValue).toHaveBeenCalledWith(
            "amount",
            expect.any(String)
        );
    });

    it("renders correctly when baseId is an empty string", () => {
        const propsWithEmptyBaseId = { ...defaultProps, baseId: "" };
        render(<AllowanceAmountField {...propsWithEmptyBaseId} />);
        expect(screen.getByTestId("input-text")).toBeInTheDocument();
    });

    it("renders with default prefixValue", () => {
        const propsWithoutPrefix = { ...defaultProps, prefixValue: undefined };
        render(<AllowanceAmountField {...propsWithoutPrefix} />);
        expect(screen.getByTestId("input-text")).toBeInTheDocument();
    });
    
    it("blurs the input field on mouse wheel event", async () => {
        render(<AllowanceAmountField {...defaultProps} />);
        const inputElement = screen.getByTestId("input-text");

        const blurSpy = jest.spyOn(inputElement, "blur");

        inputElement.focus();
        expect(document.activeElement).toBe(inputElement);

        fireEvent.wheel(inputElement);

        expect(blurSpy).toHaveBeenCalled();
    });
    
});
