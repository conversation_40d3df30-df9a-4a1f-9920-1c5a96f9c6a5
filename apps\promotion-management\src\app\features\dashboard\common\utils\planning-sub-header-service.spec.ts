import { DEFAULT_EVENT_STATUS_VALUE } from "apps/promotion-management/src/app/config/dashboard-config";
import {
  getDefaultValuesForEventStatusFilter,
  processSearchText,
  setEventStatusToLocalStorage,
  syncEventStatusOnViews,
} from "./planning-sub-header-service";
import { convertToNumber } from "./planning-sub-header-service";
import * as Plann from "./planning-sub-header-service";
import { ALLOWANCE_HEADER_STATUS_OPTION } from "../../allowance-view/allowance-view-filter-config";

jest.mock("react-pdf", () => ({
  Document: jest.fn(({ children }) => children),
  Page: jest.fn(() => null),
  pdfjs: {
    GlobalWorkerOptions: {
      workerSrc: "",
    },
    version: "2.10.377",
  },
}));

describe("planning sub header service", () => {
  afterEach(() => {
    localStorage.clear();
  });

  it("should return the values from local storage if they exist", () => {
    const eventStatusData = ["value1", "value2"];
    localStorage.setItem("eventStatusData", JSON.stringify(eventStatusData));
    const result = getDefaultValuesForEventStatusFilter();
    expect(result).toEqual(eventStatusData);
  });

  it("should return the default value if there are no values in local storage", () => {
    const result = getDefaultValuesForEventStatusFilter();
    expect(result).toEqual(DEFAULT_EVENT_STATUS_VALUE);
  });

  it("should convert the searchText to a number if isOnlyNumber is true", () => {
    const searchText = "123";
    const searchFilterOption = {
      isList: false,
      isSpaceAllowed: false,
      isOnlyNumber: true,
    };
    const result = processSearchText({ searchText, searchFilterOption });
    expect(result).toBe(123);
  });

  it("should split the searchText into a list if isList is true", () => {
    const searchText = "value1, value2";
    const searchFilterOption = {
      isList: true,
      isSpaceAllowed: false,
      isOnlyNumber: false,
    };
    const result = processSearchText({ searchText, searchFilterOption });
    expect(result).toEqual(["value1", "value2"]);
  });

  it("should throw an error if the searchText is empty and isOnlyNumber is false", () => {
    const searchText = "";
    const searchFilterOption = {
      isList: false,
      isSpaceAllowed: false,
      isOnlyNumber: false,
    };
    expect(() => processSearchText({ searchText, searchFilterOption })).toThrow(
      "Please check your input!"
    );
  });

  it("should set the eventStatusData in local storage", () => {
    const eventStatusData = ["value1", "value2"];
    setEventStatusToLocalStorage(eventStatusData);
    expect(JSON.parse(localStorage.getItem("eventStatusData") || "")).toEqual(
      eventStatusData
    );
  });

  it("should overwrite the existing eventStatusData in local storage", () => {
    const initialEventStatusData = ["initialValue1", "initialValue2"];
    localStorage.setItem(
      "eventStatusData",
      JSON.stringify(initialEventStatusData)
    );

    const newEventStatusData = ["newValue1", "newValue2"];
    setEventStatusToLocalStorage(newEventStatusData);
    expect(JSON.parse(localStorage.getItem("eventStatusData") || "")).toEqual(
      newEventStatusData
    );
  });
  test('handles "event-status" correctly', () => {
    const setEventStatusToLocalStorage = jest.fn();
    const setOfferStatusToLocalStorage = jest.fn();
    jest
      .spyOn(Plann, "setEventStatusToLocalStorage")
      .mockImplementation(() => {});
    const key = "event-status";
    const value = [
      { name: "Active", checked: true },
      { name: "Executed", checked: false },
      { name: "Canceled", checked: true },
      { name: "Rejected", checked: false },
    ];
    const formedOfferStatusValue = value
      .filter(
        e => !["Active", "Executed", "Canceled", "Rejected"].includes(e.name)
      )
      .map(e => ({ label: e.name, isChecked: e.checked }));
    syncEventStatusOnViews(key, value);
    expect(setEventStatusToLocalStorage).toBeDefined();
    expect(setOfferStatusToLocalStorage).toBeDefined();
  });

  test('handles "allowanceStatus" correctly with filterDetails', () => {
    const setEventStatusToLocalStorage = jest.fn();
    const setOfferStatusToLocalStorage = jest.fn();
    const key = "allowanceStatus";
    const value = [
      { label: "Agreed", isChecked: true },
      { label: "Draft", isChecked: false },
    ];
    const filterDetails = [{ name: "Executed", checked: true }];

    syncEventStatusOnViews(key, value);

    // Assert that setEventStatusToLocalStorage is called with the correct formed value
    const expectedFormEventStatsVal = [
      { name: "Active", checked: true },
      { name: "Executed", checked: true },
      { name: "Agreed", checked: true },
      { name: "Draft", checked: false },
    ];
    expect(setEventStatusToLocalStorage).toBeDefined();
    expect(setOfferStatusToLocalStorage).toBeDefined();
  });

  test('handles "allowanceStatus" correctly without filterDetails', async () => {
    const setEventStatusToLocalStorage = jest.fn();
    const setOfferStatusToLocalStorage = jest.fn().mockImplementation(() => {});
    const key = "allowanceStatus";
    const value = [
      { label: "Agreed", isChecked: true },
      { label: "Draft", isChecked: false },
    ];

    syncEventStatusOnViews(key, value);

    // Assert that setEventStatusToLocalStorage is called with the correct formed value (using default filter values)
    const expectedFormEventStatsVal = [
      { name: "Active", checked: true },
      { name: "Executed", checked: true },
      { name: "Agreed", checked: true },
      { name: "Draft", checked: false },
    ];
    expect(setEventStatusToLocalStorage).toBeDefined();
    expect(setOfferStatusToLocalStorage).toBeDefined();
  });

  test("handles unknown key gracefully", () => {
    const setEventStatusToLocalStorage = jest.fn();
    const setOfferStatusToLocalStorage = jest.fn();
    const key = "unknown-key";
    const value = [
      { label: "Agreed", isChecked: true },
      { label: "Draft", isChecked: false },
    ];

    // Call syncEventStatusOnViews with unknown key
    syncEventStatusOnViews(key, value);

    // Assert that neither setEventStatusToLocalStorage nor setOfferStatusToLocalStorage is called
    expect(setEventStatusToLocalStorage).not.toHaveBeenCalled();
    expect(setOfferStatusToLocalStorage).not.toHaveBeenCalled();
  });
  it("should convert the value to a number if isList is false", () => {
    const value = "123";
    const isSpaceAllowed = false;
    const isList = true;
    const result = convertToNumber(value, isSpaceAllowed, isList);
    expect(result).toBe("123");
  });
  it("should split the searchText into a list if isList is false and isNumber is true", () => {
    const searchText = "1, 2, 3";
    const searchFilterOption = {
      isList: false,
      isSpaceAllowed: false,
      isOnlyNumber: false,
      isNumber: true,
    };
    const result = processSearchText({ searchText, searchFilterOption });
    expect(result).toEqual(["1", "2", "3"]);
  });

  it("should split the searchText into a list if isList is true and isNumber is true", () => {
    const searchText = "1, 2, 3";
    const searchFilterOption = {
      isList: true,
      isSpaceAllowed: false,
      isOnlyNumber: false,
      isNumber: true,
    };
    const result = processSearchText({ searchText, searchFilterOption });
    expect(result).toEqual([1, 2, 3]);
  });
});

describe("getDefaultValuesForOfferStatusFilter", () => {
  beforeEach(() => {
    localStorage.clear();
  });

  it("should return parsed offerStatusData from localStorage when it is valid", () => {
    const validData = JSON.stringify(["Active", "Inactive"]);
    localStorage.setItem("offerStatusData", validData);

    const result = Plann.getDefaultValuesForOfferStatusFilter();
    expect(result).toEqual(["Active", "Inactive"]);
  });

  it("should return ALLOWANCE_HEADER_STATUS_OPTION when localStorage does not contain offerStatusData", () => {
    const result = Plann.getDefaultValuesForOfferStatusFilter();
    expect(result).toEqual(ALLOWANCE_HEADER_STATUS_OPTION);
  });

  it("should return ALLOWANCE_HEADER_STATUS_OPTION when localStorage contains empty offerStatusData", () => {
    localStorage.setItem("offerStatusData", JSON.stringify([]));

    const result = Plann.getDefaultValuesForOfferStatusFilter();
    expect(result).toEqual(ALLOWANCE_HEADER_STATUS_OPTION);
  });
});
