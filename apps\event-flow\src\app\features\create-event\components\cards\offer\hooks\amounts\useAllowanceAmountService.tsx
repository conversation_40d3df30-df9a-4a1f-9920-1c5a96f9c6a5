import { isComingFromtask } from "@me-upp-js/utilities";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";
import _ from "lodash";
import {
  checkIsWarehouseItem,
  getAllowanceMapKey,
  getDatesFromTempWork,
  getUOMFromNameOrValue,
} from "../../../../../service/allowance/allowance-service";
import {
  allowanceNewCardConfiguration,
  offerCardConfiguration,
  setAllowanceFormInfo,
  setIsOfferSectionUpdated,
  setOfferAmontsData,
} from "../../../../../service/slice/allowance-details-slice";
import { setAllowancesResponse } from "../../../../../service/slice/allowances-response-slice";
import useGetOfferSectionConfiguration from "../useGetOfferSectionConfiguration";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useSelectorWrap } from "@me/data-rtk";

export const useAllowanceService = (
  sectionKey: string,
  allowanceRegField: string
) => {
  const { ALLOWANCE_TYPES } = efConstants;
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { data: offerAmountsDataObj } =
    useSelectorWrap("offer_amounts_details") || {};
  const { offerAmounts = {}, isAdditionalDatesChanged = {} } =
    offerAmountsDataObj || {};
  const { moveToNextSectionOnCreate, moveToNextSectionOnUpdate } =
    useGetOfferSectionConfiguration({
      allowanceRegField,
    });
  const processAllowanceData = ({
    allowanceAmountData,
    allowancesResp,
    isEditEnable,
    isAllowConvEnable,
    allowanceTypeKey,
    tempAllowItems,
  }) => {
    const { allowanceAmount, finalAmountKey, finalAmountVal } =
      allowanceAmountData || {};
    let uom = allowanceAmountData?.uom;
    const vehicleId = !isEditEnable
      ? allowancesResp?.allowances?.[0]?.vehicleId
      : null;

    uom = uom ? getUOMFromNameOrValue({ name: uom })?.value : undefined;

    const amountsData = {
      allowanceAmount,
      ...(uom && {
        uom,
        allowUomType: uom,
        [finalAmountKey]: finalAmountVal,
      }),
    };

    const isAllLeadIndFalse = allowancesResp?.allowances?.every(
      allow => !allow?.leadDistributorInd
    );

    const allowances = allowancesResp?.allowances?.map((element, index) => ({
      ...element,
      createAllowInd: true,
      leadDistributorInd:
        !isEditEnable && isAllLeadIndFalse
          ? !checkIsWarehouseItem({
              vendorNbr: element?.vendorNbr,
              location: { locationTypeCd: element?.locationTypeCd },
            })
          : element?.leadDistributorInd,
      finalizedAmountsInd: true,
      leadDistributorInfos: element?.leadDistributorInfos || [],
      ...(vehicleId && { vehicleId }),
      ...(allowanceTypeKey === ALLOWANCE_TYPES.HEADERFLAT.key
        ? { headerFlatAmt: allowanceAmount }
        : {
            allowanceItems: element?.allowanceItems?.map(item => ({
              ...item,
              ...amountsData,
            })),
          }),
      ...(isAllowConvEnable
        ? { ...getDatesFromTempWork(tempAllowItems?.[index]) }
        : {}),
    }));

    return { allowances, amountsData };
  };

  const getOfferNumber = (allowanceTempWorkData, allowanceTypeKey) => {
    const allowName = getAllowanceMapKey(allowanceTypeKey) || "";
    return allowanceTempWorkData?.allowanceTypeSpecification?.[allowName]
      ?.offerNumber;
  };

  const saveAllowanceDataInForm = ({
    allowanceAmountData,
    allowanceTypeKey,
    mapperKey,
    allowanceRegField,
    allowanceFormData,
    allowances,
    isEditEnable,
  }) => {
    const { allowanceAmount, uom } = allowanceAmountData || {};
    const updatedFormValues = {
      [allowanceRegField]: {
        ...allowanceFormData,
        allowanceAmountsData: {
          ...allowanceFormData?.allowanceAmountsData,
          [mapperKey]: {
            ...(allowanceTypeKey === ALLOWANCE_TYPES.HEADERFLAT.key
              ? {
                  headerFlatAmt: allowanceAmount,
                  headerFlatAmtItems: allowances,
                }
              : {
                  allowanceAmount,
                  allowances: allowances,
                  allowanceItems: allowances?.[0]?.allowanceItems,
                  uom,
                }),
          },
        },
      },
    };
    isEditEnable &&
      dispatch(
        setAllowanceFormInfo({
          allowanceFormData: updatedFormValues,
        })
      );
    // offerAmounts && mapperKey && allowances;
    dispatch(
      setOfferAmontsData({
        offerAmounts: {
          ...(offerAmounts || {}),
          [mapperKey]: {
            ...(offerAmounts?.[mapperKey] || {}),
            allowances,
          },
        },
        isAdditionalDatesChanged: {
          ...isAdditionalDatesChanged,
          [mapperKey]: 0,
        },
      })
    );
    return updatedFormValues;
  };

  const handlePostSave = async ({
    isEditEnable,
    eventDetailsData,
    allowanceTempWorkData,
    putOfferAllowance,
    taskType,
    offerNumber,
    mapperKey,
    updatedFormValues,
    isInValidOffer,
  }) => {
    if (isComingFromtask(taskType, eventDetailsData) && isEditEnable) {
      const putOfferData = await putOfferAllowance({
        URL_PARAMS: [offerNumber],
        ...allowanceTempWorkData,
      });
      if (putOfferData) {
        dispatch(
          offerCardConfiguration({
            offerData: `${
              allowanceTempWorkData?.offerAllowancesId
            }_${_.uniqueId()}`,
          })
        );
        dispatch(setAllowancesResponse(putOfferData));
      }
    } else {
      !isEditEnable
        ? moveToNextSectionOnCreate(sectionKey, mapperKey, updatedFormValues)
        : moveToNextSectionOnUpdate(sectionKey);
      isEditEnable &&
        dispatch(
          setIsOfferSectionUpdated({
            isOfferSectionUpdated: true,
            ...(isInValidOffer && {
              isAmountsSectionUpdated: true,
            }),
          })
        );
    }
  };

  const editViewAllItemsClick = allAllowancesPath => {
    dispatch(
      allowanceNewCardConfiguration({
        isNewAllowance: true,
        stepperType: "",
        isAddAnotherOffer: false,
      })
    );
    navigate(allAllowancesPath);
  };

  return {
    processAllowanceData,
    handlePostSave,
    getOfferNumber,
    saveAllowanceDataInForm,
    editViewAllItemsClick,
  };
};
