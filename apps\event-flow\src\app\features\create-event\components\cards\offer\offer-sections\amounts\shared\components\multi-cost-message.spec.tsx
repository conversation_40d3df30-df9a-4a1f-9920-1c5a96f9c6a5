import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import MultiCostMessage from './multi-cost-message';


describe('MultiCostMessage Component', () => {
  test('renders warning icon and message', () => {
    render(<MultiCostMessage />);

    // Verify the warning icon is rendered
    const warningIcon = screen.getByTestId('warning-icon');
    expect(warningIcon).toBeInTheDocument();

    // Verify the warning message is rendered
    const warningMessage = screen.getByText(
      "Multiple costs associated with the items. Click 'Edit/View All Items' to enter allowance amounts."
    );
    expect(warningMessage).toBeInTheDocument();
  });

  test('applies correct styles to the container', () => {
    render(<MultiCostMessage />);

    // Verify the container has the correct class and styles
    const container = screen.getByTestId('multi-cost-message-container');
    expect(container).toHaveClass('flex mt-4 w-full text-[#9D2210] font-bold text-sm gap-3');
  });
});
