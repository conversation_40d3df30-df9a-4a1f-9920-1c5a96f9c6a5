import { render, screen, fireEvent } from "@testing-library/react";
import DivisionAccordion from "./division-accordion-wrapper";
import { useSelectorWrap } from "@me/data-rtk";

// Mock dependencies
jest.mock("@me/data-rtk", () => ({
  useSelectorWrap: jest.fn(), // Mocking useSelectorWrap without the need for casting as jest.Mock
}));

jest.mock("./accordion-atom", () => ({
  __esModule: true,
  default: ({ children }) => <div>{children}</div>,
}));

jest.mock("../lead-distributor/vendor-selection", () => ({
  __esModule: true,
  default: ({ currentDivision }) => (
    <div>Vendor Selection for {currentDivision?.divisionName}</div>
  ),
}));

jest.mock("../billing-section/billing-vendor-selection", () => ({
  __esModule: true,
  default: ({ currentDivision }) => (
    <div>Billing Vendor Selection for {currentDivision?.divisionName}</div>
  ),
}));

jest.mock("@albertsons/uds/molecule/Button", () => ({
  __esModule: true,
  default: ({ children, onClick }) => (
    <button onClick={onClick}>{children}</button>
  ),
}));

describe("DivisionAccordion Component", () => {
  const mockDivisionsList = [
    { divisionId: "1", divisionName: "Division 1" },
    { divisionId: "2", divisionName: "Division 2" },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
      (useSelectorWrap as jest.Mock).mockImplementation(() => {
        console.log("Mock Data: ", {
          data: { divisionsList: mockDivisionsList },
        });
        return { data: { divisionsList: mockDivisionsList } };
      });
  });

  xit("should render with divisions and toggle expand/collapse", () => {
    // Mocking useSelectorWrap to return the mock divisions list
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: { divisionsList: mockDivisionsList },
    });

    render(<DivisionAccordion />);

    // Check if the divisions are rendered
    expect(screen.getByText("Vendor Selection for Division 1")).toBeTruthy();
    expect(screen.getByText("Vendor Selection for Division 2")).toBeTruthy();

    // Check if the Expand All button is present
    const expandButton = screen.getByText("Collapse All");
    expect(expandButton).toBeTruthy();

    // Simulate clicking the Expand All button
    fireEvent.click(expandButton);

    // Ensure the button text changes to 'Expand All'
    expect(screen.getByText("Expand All")).toBeTruthy();
  });

  it("should render 'No divisions available' if divisionsList is empty", () => {
    // Mocking useSelectorWrap to return an empty divisions list
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: { divisionsList: [] },
    });

    render(<DivisionAccordion />);

    // Check if the "No divisions available" message is displayed
    expect(screen.getByText("No divisions available.")).toBeTruthy();
  });

  xit("should render the correct component based on the 'isBilling' prop", () => {
    // Mocking useSelectorWrap to return the mock divisions list
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: { divisionsList: mockDivisionsList },
    });

    render(<DivisionAccordion isBilling={false} />);

    // Check if VendorSelections is rendered (for non-billing case)
    expect(screen.getByText("Vendor Selection for Division 1")).toBeTruthy();
    expect(screen.getByText("Vendor Selection for Division 2")).toBeTruthy();

    render(<DivisionAccordion isBilling={true} />);

    // Check if BillingVendorSelection is rendered (for billing case)
    expect(
      screen.getByText("Billing Vendor Selection for Division 1")
    ).toBeTruthy();
    expect(
      screen.getByText("Billing Vendor Selection for Division 2")
    ).toBeTruthy();
  });

  it("should handle the expandAll state correctly", () => {
    // Mocking useSelectorWrap to return the mock divisions list
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: { divisionsList: mockDivisionsList },
    });

    render(<DivisionAccordion />);

    // Check that the initial button text is "Collapse All"
    const expandButton = screen.getByText("Collapse All");
    expect(expandButton).toBeTruthy();

    // Simulate a button click to change the state to "Expand All"
    fireEvent.click(expandButton);
    expect(screen.getByText("Expand All")).toBeTruthy();
  });
});
