import { result } from "lodash";
import MultipleCardContainer from "./multiple-card-container";
import { Provider } from "react-redux";
import { app_store } from "@me/data-rtk";
import * as selectors from "@me/data-rtk";
import { <PERSON>rowserRouter } from "react-router-dom";
import { FormProvider, useForm } from "react-hook-form";
import { renderTestWithProviders } from "../../../../../../../main-test-utils";
import { promo10006123 } from "../../../../../shared/event-flow-mocks/promotions-mock";

const Wrapper = props => {
  const formMethods = useForm<any>({
    defaultValues: {
      promotionsLists: [
        {
          promotionsList: [
            {
              promotionId: 10004811,
              vehicle: {
                id: "63d4d1ec705a29397068db47",
                vehicleNm: "05 Week 25 Insert 2023",
                sourceVehicleSk: 42663,
                vehicleTypNm: "insrt",
                startDate: "2023-06-21T00:00:00.000Z",
                endDate: "2023-06-27T00:00:00.000Z",
                vehicleType: {
                  vehicleTypeId: "636abba1b426ee543a94d394",
                  sourceVehicleTypeSk: 146,
                  vehicleTypNm: "insrt",
                  vehicleTypDesc: "Weekly Insert",
                },
              },
              promoStartDate: "2023-06-21T00:00:00.000Z",
              promoEndDate: "2023-06-27T00:00:00.000Z",
              forecast: {
                quadrant: "1A",
                forecastSales: 18081,
                forecastUnits: 1234,
                forecastAgp: 7009,
                forecastAgpPercent: 81.67,
                markdown: 16124,
                markdownPercent: 7,
                incrementalUnits: 297,
                incrementalPercent: 42.77,
                incrementalSales: 4384,
                incrementalSalesPercent: 30.92,
                incrementalAgp: 2200,
                incrementalAgpPercent: 34.95,
                vendorFunding: 6677,
                coverage: 2000,
                coveragePercent: 85,
                periscopePromotionId: 880942,
                promotionId: 160675936500,
                promotionObjectId: "63e5d21e380cbc74021afc46",
                planEvent: "63a3f10fce2d351bc2a74f68",
                lastUpdated: "2022-12-22T05:54:27.646Z",
                lastUpdateBy: "UPP",
              },
            },
          ],
        },
      ],
    },
  });

  return (
    <Provider store={app_store}>
      <BrowserRouter>
        <FormProvider {...formMethods}>{props.children}</FormProvider>
      </BrowserRouter>
    </Provider>
  );
};

jest.mock("react-pdf", () => ({
  Document: jest.fn(({ children }) => children),
  Page: jest.fn(() => <div data-testid="mock-page"></div>),
  pdfjs: {
    GlobalWorkerOptions: {
      workerSrc: "",
    },
    version: "2.10.377",
  },
}));

describe("MultipleCardContainer", () => {
  class ResizeObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
  }
  beforeEach(() => {
    window.ResizeObserver = ResizeObserver as any;
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      switch (sel_name) {
        case "event_plan_card_update_details":
          return {
            data: {
              EVENT_PLAN_CARD: 0,
              isPromotionAdded: false,
            },
          };
        case "allowance_new_card_configuration_rn":
          return {
            data: {
              isNewAllowanceDetails: true,
            },
          };
        case "apiError_rn":
          return {
            data: {
              apiErrorsMsgs: [],
              showTokenError: false,
            },
          };
        case "promotion_pricing_save_all_changes_rn":
          return {
            data: {
              isPromotionPricingSaveAllChanges: false,
              isSaveAllChangesClicked: false
            },
          };
        case "promotion_plan_xapi_retry_count_rn":
          return {
            data: {
              promotionPlanXapiRetryCount: 0,
              apexStatusLoader: false
            },
          }
        case "event_details_data": {
          return {
            data: {
              name: "Annies Homegrown Snacks Box - 84882 - S - 9 - 27 Week 50 Insert 2023",
              startDate: "2023-12-13",
              endDate: "2023-12-19",
              divisionIds: ["27"],
              divisions: [],
              planProductGroups: [
                {
                  planProductGroupId: "636bde8d9665d0440e006df8",
                  sourceProductGroupId: 184779,
                  name: "Annies Homegrown Snacks Box - 84882",
                  divisionId: "27",
                  smicGroupCode: 2,
                  smicCategoryCode: "0202",
                  supplier: {
                    supplierId: "515",
                    supplierName: "GENERAL MILLS",
                  },
                  itemCount: 9,
                  displayInd: false,
                  simsVendors: ["006446"],
                  simsSubAccntVendors: ["006446-001"],
                  negotiationSimsVendors: ["006446"],
                  unitType: 1,
                },
              ],
              storeGroups: [
                {
                  storeGroupId: "6453d8fa31c0c4e30d8f11d3",
                  sourceStoreGroupId: "EDM",
                  storeGroupName: "Seattle All Stores",
                  storeGroupType: {
                    groupType: "S",
                    storeGrpTypeName: "Division",
                    groupInd: "D",
                  },
                  divisionIds: ["27"],
                  storeCount: 220,
                },
              ],
              storeGroupType: "",
              allowance: [],
              isEventChanged: false,
              id: "650ab6854deca247948296c7",
              forecast: {
                forecastSales: 0,
                forecastUnits: 0,
                quadrant: "",
              },
              eventCreationVehicle: {
                vehicleId: "63d4d1d425fbce4fd8d3165f",
                vehicleNm: "27 Week 50 Insert 2023",
                sourceVehicleSk: 43000,
                startDate: "2023-12-13",
                endDate: "2023-12-19",
                vehicleType: {
                  vehicleTypeId: "636abba1b426ee543a94d3ac",
                  sourceVehicleTypeSk: 198,
                  vehicleTypNm: "insrt",
                  vehicleTypDesc: "Weekly Insert",
                },
              },
              planProductGroupPricing: {
                planProductGroup: {
                  planProductGroupId: "",
                  sourceProductGroupId: "",
                  name: "",
                  divisionId: "",
                  smicGroupCode: "",
                  smicCategoryCode: "",
                  supplier: {
                    id: "",
                    supplierId: "",
                    supplierName: "",
                  },
                },
                quadrant: "",
                priceAmount: "",
                priceFactor: "",
                priceUnit: "",
                priceLimitQty: "",
                forecastAmt: "",
                userName: "",
                supplier: "",
              },
              startWeekVehicle: "27 Week 50 Insert 2023",
              planStoreGroupType: {
                groupType: "S",
                storeGrpTypeName: "Division",
                groupInd: "D",
              },
              vehicleType: "Weekly Insert",
              eventStatus: "Draft",
              eventType: "DP",
              inValidPromotions: [],
              inValidAllowances: [],
              isChangeEventTypeVisible: false,
              planEventIdNbr: 10014676,
              sourceEventType: "ECP",
              pricing: [
                {
                  planProductGroup: {
                    planProductGroupId: "636bde8d9665d0440e006df8",
                    sourceProductGroupId: 184779,
                    name: "Annies Homegrown Snacks Box - 84882",
                    divisionId: "27",
                    smicGroupCode: 2,
                    smicCategoryCode: "0202",
                    supplier: {
                      supplierId: "515",
                      supplierName: "GENERAL MILLS",
                    },
                    itemCount: 9,
                    displayInd: false,
                    simsVendors: ["006446"],
                    simsSubAccntVendors: ["006446-001"],
                    negotiationSimsVendors: ["006446"],
                    unitType: 1,
                  },
                  quadrant: "",
                  priceAmount: "",
                  priceFactor: "0",
                  priceLimitQty: "1",
                  prcMtd: "",
                  promoType: "BUY_ONE_GET_ONE",
                },
              ],
              offerAllowances: [
                {
                  id: "650ab7299ccba9649e8e8795",
                  offerNumber: 7011588,
                  origOfferNumber: 0,
                  planEvent: "650ab6854deca247948296c7",
                  createInd: "TC",
                  minAggrAllowanceStartDate: "2023-12-13",
                  maxAggrAllowanceEndDate: "2023-12-19",
                  divisionIds: ["27"],
                  allowances: [
                    {
                      id: "650ab7299ccba9649e8e8794",
                      allowanceIdNbr: 701158801,
                      vendorNbr: "",
                      allowanceStatus: "Draft",
                      leadDistributorInfos: [],
                      vehicle: {
                        vehicleId: "63d4d1d425fbce4fd8d3165f",
                        vehicleNm: "27 Week 50 Insert 2023",
                        sourceVehicleSk: 43000,
                        startDate: "2023-12-13",
                        endDate: "2023-12-19",
                        vehicleType: {
                          vehicleTypeId: "636abba1b426ee543a94d3ac",
                          sourceVehicleTypeSk: 198,
                          vehicleTypNm: "insrt",
                          vehicleTypDesc: "Weekly Insert",
                        },
                      },
                      defaultAllowanceDates: {
                        allowanceStartDate: "2023-12-13",
                        allowanceEndDate: "2023-12-19",
                        performStartDate: "2023-12-13",
                        performEndDate: "2023-12-19",
                        orderStartDate: "0001-01-01",
                        orderEndDate: "0001-01-01",
                        shipStartDate: "0001-01-01",
                        shipEndDate: "0001-01-01",
                        arrivalStartDate: "0001-01-01",
                        arrivalEndDate: "0001-01-01",
                        overrideInd: false,
                        notPreSaved: false,
                      },
                      allowanceStartDate: "2023-12-13",
                      allowanceEndDate: "2023-12-19",
                      performStartDate: "2023-12-13",
                      performEndDate: "2023-12-19",
                      orderStartDate: "0001-01-01",
                      orderEndDate: "0001-01-01",
                      shipStartDate: "0001-01-01",
                      shipEndDate: "0001-01-01",
                      arrivalStartDate: "0001-01-01",
                      arrivalEndDate: "0001-01-01",
                      cancelledTs: "0001-01-03",
                      processTs: "0001-01-03",
                      lastApprovedDate: "0001-01-01",
                      lastApprovedUserId: "",
                      allowanceBillingInfo: {
                        vendorIds: [
                          {
                            vendorNbr: "006446",
                            vendorSubAccount: "001",
                            costArea: "4",
                            fullVendorNbr: "006446-001-4",
                          },
                          {
                            vendorNbr: "006446",
                            vendorSubAccount: "001",
                            costArea: "2",
                            fullVendorNbr: "006446-001-2",
                          },
                          {
                            vendorNbr: "006446",
                            vendorSubAccount: "001",
                            costArea: "1",
                            fullVendorNbr: "006446-001-1",
                          },
                        ],
                        absMerchVendor: "006446-001",
                        absVendorName: "KEHE DISTRIBUTORS",
                        absVendorPaymentType: "Deduct",
                        acPayableVendorNbr: "110056",
                        acReceivableVendorNbr: "082099",
                        suggestedVendorPaymentType: "",
                        suggestedAcPayableVendorNbr: "",
                        suggestedAcReceivableVendorNbr: "",
                        billingContactName: "<EMAIL>        ",
                        billingContactEmail: "<EMAIL>",
                        vendorComment: "",
                        vendorOfferTrackingNbr: "",
                        vendorBillingList: [
                          {
                            billingContactName: "<EMAIL>        ",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "BRANDON SWEET            ",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "<EMAIL>        ",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "<EMAIL>     ",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "<EMAIL>        ",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "BRANDON SWEET            ",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "<EMAIL>        ",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "<EMAIL>     ",
                            billingContactEmail: "<EMAIL>",
                          },
                        ],
                        vendorItemCount: 7,
                        source: "SIMS_VENDOR",
                      },
                      allowanceBillingInfos: [
                        {
                          vendorIds: [
                            {
                              vendorNbr: "006446",
                              vendorSubAccount: "001",
                              costArea: "4",
                              fullVendorNbr: "006446-001-4",
                            },
                            {
                              vendorNbr: "006446",
                              vendorSubAccount: "001",
                              costArea: "2",
                              fullVendorNbr: "006446-001-2",
                            },
                            {
                              vendorNbr: "006446",
                              vendorSubAccount: "001",
                              costArea: "1",
                              fullVendorNbr: "006446-001-1",
                            },
                          ],
                          absMerchVendor: "006446-001",
                          absVendorName: "KEHE DISTRIBUTORS",
                          absVendorPaymentType: "D",
                          acPayableVendorNbr: "110056",
                          acReceivableVendorNbr: "082099",
                          billingContactName: "<EMAIL>        ",
                          billingContactEmail: "<EMAIL>",
                          vendorComment: "",
                          vendorOfferTrackingNbr: "",
                          vendorBillingList: [
                            {
                              billingContactName: "<EMAIL>        ",
                              billingContactEmail: "<EMAIL>",
                            },
                            {
                              billingContactName: "BRANDON SWEET            ",
                              billingContactEmail: "<EMAIL>",
                            },
                            {
                              billingContactName: "<EMAIL>        ",
                              billingContactEmail: "<EMAIL>",
                            },
                            {
                              billingContactName: "<EMAIL>     ",
                              billingContactEmail: "<EMAIL>",
                            },
                            {
                              billingContactName: "<EMAIL>        ",
                              billingContactEmail: "<EMAIL>",
                            },
                            {
                              billingContactName: "BRANDON SWEET            ",
                              billingContactEmail: "<EMAIL>",
                            },
                            {
                              billingContactName: "<EMAIL>        ",
                              billingContactEmail: "<EMAIL>",
                            },
                            {
                              billingContactName: "<EMAIL>     ",
                              billingContactEmail: "<EMAIL>",
                            },
                          ],
                          vendorItemCount: 7,
                          source: "SIMS_VENDOR",
                        },
                      ],
                      storeGroups: [
                        {
                          storeGroupId: "6453d8fa31c0c4e30d8f11d3",
                          sourceStoreGroupId: "EDM",
                          storeGroupName: "Seattle All Stores",
                          storeGroupType: {
                            groupType: "S",
                            storeGrpTypeName: "Division",
                            groupInd: "D",
                          },
                          divisionIds: ["27"],
                          storeCount: 220,
                        },
                      ],
                      performance: {
                        allowanceCd: "T",
                        perfCode1: "20",
                        perfCode2: "52",
                        payType: "B",
                        allwPerfId: "63a3a12743a6cee87995b834",
                      },
                      planProductGroups: [
                        {
                          planProductGroupId: "636bde8d9665d0440e006df8",
                          sourceProductGroupId: 184779,
                          name: "Annies Homegrown Snacks Box - 84882",
                          divisionId: "27",
                          smicGroupCode: 2,
                          smicCategoryCode: "0202",
                          supplier: {
                            supplierId: "515",
                            supplierName: "GENERAL MILLS",
                          },
                          itemCount: 9,
                          displayInd: false,
                          simsVendors: ["006446"],
                          simsSubAccntVendors: ["006446-001"],
                          negotiationSimsVendors: ["006446"],
                          unitType: 1,
                        },
                      ],
                      allowanceItems: [
                        {
                          itemId: "2010289",
                          primaryUpc: "001356200018",
                          consumerUpc: "001356200018",
                          caseUpc: "0000000000000",
                          itemUpcs: ["0000000000000", "001356200018"],
                          consumerUpcs: [
                            {
                              upc: "001356200018",
                              rog: "SSPK",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356200018",
                              rog: "SSEA",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356200018",
                              rog: "SACG",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                          ],
                          effectiveEndDate: "0001-01-01",
                          vendorPackConversionFactor: 1,
                          packWhse: 12,
                          ringType: 0,
                          uom: "EA",
                          allowanceItemComps: [
                            {
                              performance: {
                                allowanceCd: "T",
                                perfCode1: "20",
                                perfCode2: "52",
                                payType: "B",
                                allwPerfId: "63a3a12743a6cee87995b834",
                              },
                              allowanceTypeStartDate: "2023-12-13",
                              allowanceTypeEndDate: "2023-12-19",
                              allowAmount: 0.3,
                            },
                          ],
                        },
                        {
                          itemId: "2020113",
                          primaryUpc: "001356200015",
                          consumerUpc: "001356200015",
                          caseUpc: "0001356200015",
                          itemUpcs: ["0001356200015", "001356200015"],
                          consumerUpcs: [
                            {
                              upc: "001356200015",
                              rog: "SSEA",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356200015",
                              rog: "SSPK",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356200015",
                              rog: "SACG",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                          ],
                          effectiveEndDate: "0001-01-01",
                          vendorPackConversionFactor: 1,
                          packWhse: 12,
                          ringType: 0,
                          uom: "EA",
                          allowanceItemComps: [
                            {
                              performance: {
                                allowanceCd: "T",
                                perfCode1: "20",
                                perfCode2: "52",
                                payType: "B",
                                allwPerfId: "63a3a12743a6cee87995b834",
                              },
                              allowanceTypeStartDate: "2023-12-13",
                              allowanceTypeEndDate: "2023-12-19",
                              allowAmount: 0.3,
                            },
                          ],
                        },
                        {
                          itemId: "2020118",
                          primaryUpc: "001356230215",
                          consumerUpc: "001356230215",
                          caseUpc: "0001356230215",
                          itemUpcs: ["0001356230215", "001356230215"],
                          consumerUpcs: [
                            {
                              upc: "001356230215",
                              rog: "SSPK",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356230215",
                              rog: "SSEA",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356230215",
                              rog: "SACG",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                          ],
                          effectiveEndDate: "0001-01-01",
                          vendorPackConversionFactor: 1,
                          packWhse: 12,
                          ringType: 0,
                          uom: "EA",
                          allowanceItemComps: [
                            {
                              performance: {
                                allowanceCd: "T",
                                perfCode1: "20",
                                perfCode2: "52",
                                payType: "B",
                                allwPerfId: "63a3a12743a6cee87995b834",
                              },
                              allowanceTypeStartDate: "2023-12-13",
                              allowanceTypeEndDate: "2023-12-19",
                              allowAmount: 0.3,
                            },
                          ],
                        },
                        {
                          itemId: "2020201",
                          primaryUpc: "001356210923",
                          consumerUpc: "001356210923",
                          caseUpc: "0001356210923",
                          itemUpcs: ["0001356210923", "001356210923"],
                          consumerUpcs: [
                            {
                              upc: "001356210923",
                              rog: "SSPK",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356210923",
                              rog: "SSEA",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356210923",
                              rog: "SACG",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                          ],
                          effectiveEndDate: "0001-01-01",
                          vendorPackConversionFactor: 1,
                          packWhse: 12,
                          ringType: 0,
                          uom: "EA",
                          allowanceItemComps: [
                            {
                              performance: {
                                allowanceCd: "T",
                                perfCode1: "20",
                                perfCode2: "52",
                                payType: "B",
                                allwPerfId: "63a3a12743a6cee87995b834",
                              },
                              allowanceTypeStartDate: "2023-12-13",
                              allowanceTypeEndDate: "2023-12-19",
                              allowAmount: 0.3,
                            },
                          ],
                        },
                        {
                          itemId: "2020228",
                          primaryUpc: "001356211645",
                          consumerUpc: "001356211645",
                          caseUpc: "0001356211645",
                          itemUpcs: ["0001356211645", "001356211645"],
                          consumerUpcs: [
                            {
                              upc: "001356211645",
                              rog: "SACG",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356211645",
                              rog: "SSEA",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                          ],
                          effectiveEndDate: "0001-01-01",
                          vendorPackConversionFactor: 1,
                          packWhse: 12,
                          ringType: 0,
                          uom: "EA",
                          allowanceItemComps: [
                            {
                              performance: {
                                allowanceCd: "T",
                                perfCode1: "20",
                                perfCode2: "52",
                                payType: "B",
                                allwPerfId: "63a3a12743a6cee87995b834",
                              },
                              allowanceTypeStartDate: "2023-12-13",
                              allowanceTypeEndDate: "2023-12-19",
                              allowAmount: 0.3,
                            },
                          ],
                        },
                        {
                          itemId: "2020393",
                          primaryUpc: "001356200053",
                          consumerUpc: "001356200053",
                          caseUpc: "0000000000000",
                          itemUpcs: ["0000000000000", "001356200053"],
                          consumerUpcs: [
                            {
                              upc: "001356200053",
                              rog: "SSPK",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356200053",
                              rog: "SACG",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356200053",
                              rog: "SSEA",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                          ],
                          effectiveEndDate: "0001-01-01",
                          vendorPackConversionFactor: 1,
                          packWhse: 12,
                          ringType: 0,
                          uom: "EA",
                          allowanceItemComps: [
                            {
                              performance: {
                                allowanceCd: "T",
                                perfCode1: "20",
                                perfCode2: "52",
                                payType: "B",
                                allwPerfId: "63a3a12743a6cee87995b834",
                              },
                              allowanceTypeStartDate: "2023-12-13",
                              allowanceTypeEndDate: "2023-12-19",
                              allowAmount: 0.3,
                            },
                          ],
                        },
                        {
                          itemId: "2020491",
                          primaryUpc: "001356211722",
                          consumerUpc: "001356211722",
                          caseUpc: "0001356211722",
                          itemUpcs: ["0001356211722", "001356211722"],
                          consumerUpcs: [
                            {
                              upc: "001356211722",
                              rog: "SSPK",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356211722",
                              rog: "SSEA",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356211722",
                              rog: "SACG",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                          ],
                          effectiveEndDate: "0001-01-01",
                          vendorPackConversionFactor: 1,
                          packWhse: 12,
                          ringType: 0,
                          uom: "EA",
                          allowanceItemComps: [
                            {
                              performance: {
                                allowanceCd: "T",
                                perfCode1: "20",
                                perfCode2: "52",
                                payType: "B",
                                allwPerfId: "63a3a12743a6cee87995b834",
                              },
                              allowanceTypeStartDate: "2023-12-13",
                              allowanceTypeEndDate: "2023-12-19",
                              allowAmount: 0.3,
                            },
                          ],
                        },
                        {
                          itemId: "2020895",
                          primaryUpc: "001356230261",
                          consumerUpc: "001356230261",
                          caseUpc: "0001356230261",
                          itemUpcs: ["0001356230261", "001356230261"],
                          consumerUpcs: [
                            {
                              upc: "001356230261",
                              rog: "SSEA",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7 OZ   ",
                            },
                            {
                              upc: "001356230261",
                              rog: "SACG",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7 OZ   ",
                            },
                            {
                              upc: "001356230261",
                              rog: "SSPK",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7 OZ   ",
                            },
                          ],
                          effectiveEndDate: "0001-01-01",
                          vendorPackConversionFactor: 1,
                          packWhse: 12,
                          ringType: 0,
                          uom: "EA",
                          allowanceItemComps: [
                            {
                              performance: {
                                allowanceCd: "T",
                                perfCode1: "20",
                                perfCode2: "52",
                                payType: "B",
                                allwPerfId: "63a3a12743a6cee87995b834",
                              },
                              allowanceTypeStartDate: "2023-12-13",
                              allowanceTypeEndDate: "2023-12-19",
                              allowAmount: 0.3,
                            },
                          ],
                        },
                      ],
                      location: {
                        locationId: "639033d538196056762e6e28",
                        locationName: "27 - Seattle",
                        distCenter: "DDSE",
                        locationTypeCd: "D",
                        locationTypeCdEnum: "RETAIL_DIVISION",
                      },
                      headerFlatAmt: 0,
                      createTs: 1695201065550,
                      createUserId: "RROUT04",
                      lastUpdTs: 1695201065550,
                      lastUpdUserId: "RROUT04",
                      costAreaDesc: "",
                      leadDistributorInd: true,
                      includeInd: true,
                      excludedItems: [
                        {
                          itemId: "2021027",
                          excludeInd: "NOT_FOUND",
                        },
                      ],
                      allowanceProcessStatus: "",
                      allowanceDateOffsets: {
                        allowanceTypes: ["SCAN", "HEADER_FLAT", "ITEM_FLAT"],
                        startDateOffset: 0,
                        endDateOffset: 0,
                        defaultOrderLeadTimeDays: 0,
                        defaultShipTransitDays: 0,
                      },
                    },
                  ],
                  periscopeIds: ["902333"],
                  createUser: {
                    userId: "RROUT04",
                    name: "Ravi Kiran Routhu (Contractor)",
                    type: "Merchant",
                    userRoles: [
                      "az-meupp-nonprod-promointeditor",
                      "az-meupp-nonprod-promoitintadmin",
                    ],
                    createTs: "2023-09-20T09:11:05.549Z",
                  },
                  updateUser: {
                    userId: "RROUT04",
                    name: "Ravi Kiran Routhu (Contractor)",
                    type: "Merchant",
                    userRoles: [
                      "az-meupp-nonprod-promointeditor",
                      "az-meupp-nonprod-promoitintadmin",
                    ],
                    createTs: "2023-09-20T09:11:05.550Z",
                  },
                  allowanceChangeStatus: "UPDATED",
                  createTempWorkAllowanceId: "650ab6e29ccba9649e8e8793",
                  offerId: "650ab7299ccba9649e8e8795",
                },
              ],
              promotionsList: [promo10006123],
              allowances: ["650ab7299ccba9649e8e8795"],
              promotions: ["650ab74a3b01e74b8d16ca09"],
              createUser: {
                userId: "RROUT04",
                name: "Ravi Kiran Routhu (Contractor)",
                type: "Merchant",
                userRoles: [
                  "az-meupp-nonprod-promointeditor",
                  "az-meupp-nonprod-promoitintadmin",
                ],
                createTs: "2023-09-20T09:08:21.408Z",
              },
              updateUser: {
                userId: "RROUT04",
                name: "Ravi Kiran Routhu (Contractor)",
                type: "Merchant",
                userRoles: [
                  "az-meupp-nonprod-promointeditor",
                  "az-meupp-nonprod-promoitintadmin",
                ],
                createTs: "2023-09-20T09:08:21.408Z",
              },
              planEventWorkFlowType: "NOT FOUND",
              eventTypeEnum: "DP",
              promotionsLists: [
                {
                  promotionsList: [promo10006123],
                },
              ],
              promoProductGroup: "Annies Homegrown Snacks Box - 84882",
              storeGroupName: "Seattle All Stores",
            },
          };
        }
        case "event_status_change_indicators":
          return {
            data: { isEventStatusChanged: false },
          };
        case "is_event_edit_enable": {
          return {
            data: {
              isEventCardOpen: {},
            },
          };
        }
        case "offer_card_configutation_rn": {
          return {
            data: {
              editCardConfig: {},
              openCardConfig: {},
            },
          };
        }
        case "promo_card_configutation_rn":
          return {
            data: {
              openCardConfig: {},
              editCardConfig: {},
              isAddNewPromo: {},
            },
          };
        case "by_pass_offer_allowance": {
          return {
            data: { isByPassOfferAllowanceSelected: false },
          };
        }
        case "promotion_edit_enable_configutation_rn":
          return {
            data: { isEditPromotion: {} },
          };
        case "promotion_regular_price_data":
          return {
            data: {
              minRegularPrice: 0,
              maxRegularPrice: 0,
              maxListCost: 0,
              minListCost: 0,
              maxListAGP: 0,
              minListAGP: 0,
            },
          };

        case "allowance_temp_work":
          return {
            data: { id: "hh" },
          };
        case "offer_sub_card_configutation_rn": {
          return {
            data: {
              offerSubCardConfig: {},
            },
          };
        }
        case "promo_sub_card_configutation_rn": {
          return {
            data: {
              promoSubCardConfig: {},
            },
          };
        }
        case "eventProgressConfigData_rn":
          return {
            data: {},
          };
        default:
          break;
      }
    });
  });
  it("should render MultipleCardContainer without errors", () => {
    const result = renderTestWithProviders(
      <Wrapper>
        <MultipleCardContainer
          cardContainerData={undefined}
          cardIndex={undefined}
          allowanceIndex={undefined}
          cardConfiguration={{ section: "Allowance" }}
        />
      </Wrapper>
    );
  });
  expect(result).toBeTruthy();
});
