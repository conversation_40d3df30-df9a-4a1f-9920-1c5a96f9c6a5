import Input from "@albertsons/uds/molecule/Input";
import { FormFieldError } from "@me/util-form-wrapper";
import * as React from "react";
import { useFormContext, Controller } from "react-hook-form";
import "./input-text.scss";
import InfoGraphics from "../../../event-types/components/info-graphics/info-graphics";
import { INFO_GRAPHICS } from "@me-upp-js/utilities";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import { roundToDecimalPlaces } from "@me/util-helpers";

// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface IInputTextProps {
  fieldProps?: any;
  onChange?: any;
  disabled?: boolean;
  dynamicRegisterField?: any;
  type?: any;
  defaultValue?: any;
  suffixElement?: any;
  prefixElement?: any;
  isChanged?: boolean;
  builtInError?: any;
  className?: string;
  isPromotionAmountField?: boolean;
  isRequired?: boolean;
  fieldErrorMessage?: string;
  onKeyDown?: (event: any) => void;
}

const InputText: React.FunctionComponent<IInputTextProps> = ({
  fieldProps,
  onChange,
  disabled,
  dynamicRegisterField,
  type,
  defaultValue,
  suffixElement,
  isChanged,
  builtInError = false,
  prefixElement,
  className = "",
  isPromotionAmountField = false,
  isRequired,
  fieldErrorMessage = "",
  onKeyDown,
}) => {
  const { getValues, setValue, register, control, clearErrors } =
    useFormContext();
  const { registerField, errorMessage = builtInError } = fieldProps;
  const { PERISCOPE_ERRORS } = efConstants;
  const onChangeHandler = e => {
    // if (e.target.value !== "" && registerField === "name") {
    //   clearErrors("name");
    // }

    onChange(e);
  };
  const handleOnKeyDown = (event: any) => {
    event?.stopPropagation();
    if (onKeyDown) {
      onKeyDown(event);
    }
  };

  return (
    <div className="w-full" id="abs-input-text-main-div">
      <div
        className="flex font-bold whitespace-nowrap gap-1"
        id="abs-input-text-dd"
      >
        <p id="abs-input-text-label">{fieldProps.label}</p>
        {fieldProps.required ? (
          <p
            className="text-sm text-left text-[#bf2912]"
            id="abs-input-text-star"
          >
            *
          </p>
        ) : null}
        <span className="flex items-center pl-1" id="abs-input-text-info">
          <InfoGraphics
            anchor="top"
            registerField={
              INFO_GRAPHICS?.PROMOTION_INFO[fieldProps?.registerField]
            }
            variant="light"
            classNameInfo="flex-col m-4 text-xs text-[#5a697b]"
            hoverContent={
              INFO_GRAPHICS?.PROMOTION_INFO[fieldProps?.registerField]
                ?.INFO_GRAPHICS_LABEL
            }
            size="14"
          />
        </span>
      </div>
      <div className="w-full inputMargin" id="abs-input-text-control">
        <Controller
          name={dynamicRegisterField ? dynamicRegisterField : registerField}
          control={control}
          rules={{
            required: {
              value: isRequired ? !isRequired : fieldProps?.required,
              message: fieldProps?.error?.required?.text,
            },
          }}
          render={({ field, fieldState: { error } }) => (
            <>
              <Input
                {...(isPromotionAmountField
                  ? {
                      ...field,
                      value: roundToDecimalPlaces(
                        getValues(dynamicRegisterField),
                        2
                      ),
                    }
                  : { ...field })}
                isRequired={isRequired ? !isRequired : fieldProps.required}
                disabled={disabled}
                tooltip={fieldProps.tooltip}
                error={builtInError}
                value={
                  dynamicRegisterField
                    ? getValues(dynamicRegisterField) ?? defaultValue
                    : defaultValue ?? ""
                }
                type={type || "text"}
                className={`${className} ${
                  isChanged || error?.message
                    ? "rounded border border-[red] mt-[0px]"
                    : "rounded border border-solid border-[#bfd4e7]"
                }`}
                onChange={onChangeHandler}
                onKeyDown={handleOnKeyDown}
                suffixElement={suffixElement || ""}
                prefixElement={prefixElement || ""}
              />
              <>
                {builtInError && (
                  <FormFieldError error={fieldErrorMessage || errorMessage} />
                )}
                {!builtInError && error && <FormFieldError error={error} />}
              </>
            </>
          )}
        />
      </div>
    </div>
  );
};

export default InputText;
