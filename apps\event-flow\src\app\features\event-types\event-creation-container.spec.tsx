import { app_store } from "@me/data-rtk";
import { render } from "@testing-library/react";
import { FormProvider, useForm } from "react-hook-form";
import { Provider } from "react-redux";
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import EventCreationContainer from "./event-creation-container";
import * as selectors from "@me/data-rtk";

jest.mock("../create-event/components/cards/layout/event-creation-layout", () =>
  jest.fn(() => <></>)
);
jest.mock("./event-header-container", () => jest.fn(() => <></>));
jest.mock("./event-progress-container", () => jest.fn(() => <></>));
jest.mock("./event-hook-form-wrapper", () =>
  jest.fn(({ children }) => <>{children}</>)
);

jest.mock("react-pdf", () => ({
  Document: jest.fn(({ children }) => children),
  Page: jest.fn(() => <div data-testid="mock-page"></div>),
  pdfjs: {
    GlobalWorkerOptions: {
      workerSrc: "",
    },
    version: "2.10.377",
  },
}));

jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
  return (
    {
      event_details_data: {
        data: {
          divisionIds: [],
        },
      },
    }[sel_name] || {}
  );
});

function Wrapper({ children }) {
  const formMethods = useForm<any>();
  return (
    <BrowserRouter>
      <Provider store={app_store}>
        <FormProvider {...formMethods}>{children}</FormProvider>
      </Provider>
    </BrowserRouter>
  );
}

describe("event-creation-container.spec", () => {
  it("component is rendering without error", () => {
    const Component = (
      <Wrapper>
        <EventCreationContainer />
      </Wrapper>
    );
    render(Component);
  });
});
