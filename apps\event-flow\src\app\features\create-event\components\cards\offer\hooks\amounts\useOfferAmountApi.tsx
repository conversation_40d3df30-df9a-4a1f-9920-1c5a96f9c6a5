import { useSelectorWrap } from "@me/data-rtk";
import { useGetAppBasePath } from "@me/util-helpers";
import { useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  getQueryParams,
  isHfOrIfType,
  sortAllowanceById,
} from "../../../../../service/allowance/allowance-service";
import { appConstants } from "@me/utils-root-props";
import { buildURLforAllowanceDashboard } from "../../../allowance/stepper/allowance-amounts/allowance-amounts-helper";
import { useGetAllowancesItemsQuery } from "../../../../../service/apis/allowance-api";
import { saveToSessionStorage } from "../../../../../../../shared/helpers/event-flow-helpers";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";
import { isEmpty } from "lodash";
import { isComingFromtask } from "@me-upp-js/utilities";
import { setOfferAmontsData } from "../../../../../service/slice/allowance-details-slice";
import { useDispatch } from "react-redux";

/**
 * this hook is used to make the amounts API call,
 * navigate to main entry screen
 * as well as return path for main entry screen
 * @param isEditEnable
 * @param key
 * @param productSources
 * @returns
 */
export default function useOfferAmountApi(
  cardIndex: number,
  isEditEnable: boolean,
  key: string,
  productSources: string[],
  isSkipRedirection = false
) {
  const { basePath } = useGetAppBasePath();
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { data: offerAmountsDataObj } =
    useSelectorWrap("offer_amounts_details") || {};
  const {
      data: { allowanceData: allowanceTempWorkData },
    } = useSelectorWrap("allowance_temp_work"),
    { isAllowConvEnable } = useSelectorWrap("allow_conv_status_rn").data;
  const { isPrimeSectionSectionUpdated = false } =
    useSelectorWrap("offer_section_update")?.data || {};
  const isOfferInvalid = eventDetailsData?.inValidAllowances?.includes(
    eventDetailsData?.offerAllowances?.[cardIndex]?.id
  );

  const [allowData, setAllowData] = useState<any>(null);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { taskType } = getQueryParams();
  const ROUTE_PARAM = {
    eventId: eventDetailsData.id,
    offerAllowanceGroup: key,
    isEdit: isEditEnable,
    ...(taskType === appConstants.TASK_TYPE_NEW_ITEM && { taskType }),
  };
  const isCommingFromTaskView = useMemo(
    () => isComingFromtask(taskType, eventDetailsData),
    [taskType, eventDetailsData]
  );
  const { offerAmounts = {}, isAdditionalDatesChanged = {} } =
    offerAmountsDataObj || {};

  const amountsApiDataFromSlice =
    offerAmounts && !isEmpty(offerAmounts) && key ? offerAmounts?.[key] : {};
  const isDateChanged =
    !isEmpty(isAdditionalDatesChanged) && key
      ? isAdditionalDatesChanged?.[key]
      : 0;

  const allAllowancesPath = `${basePath}/${buildURLforAllowanceDashboard(
    ROUTE_PARAM,
    isAllowConvEnable
  )}`;
  const isIfHf = isHfOrIfType(allowanceTempWorkData?.allowanceType);

  const allowanceAmountsPayload = {
    URL_PARAMS: [eventDetailsData?.id, key],
  };
  const {
    data: allowancesResp,
    isFetching,
    refetch,
  } = useGetAllowancesItemsQuery(allowanceAmountsPayload, {
    refetchOnMountOrArgChange: true,
    skip:
      !key ||
      !allowanceTempWorkData?.tempWorkAllowanceId ||
      !productSources?.length ||
      (isOfferInvalid && !isPrimeSectionSectionUpdated && !isIfHf) ||
      (amountsApiDataFromSlice &&
        !isEmpty(amountsApiDataFromSlice) &&
        !isDateChanged),
  });

  useEffect(() => {
    if (isDateChanged > 0) {
      allowancesResp && refetch();
    }
  }, [isDateChanged]);

  useEffect(() => {
    if (
      allowancesResp &&
      (!amountsApiDataFromSlice ||
        isEmpty(amountsApiDataFromSlice) ||
        isDateChanged)
    ) {
      const updatedAllowRespData = getSortedAllowances(allowancesResp);
      setAllowData(updatedAllowRespData);
      dispatch(
        setOfferAmontsData({
          offerAmounts: { ...offerAmounts, [key]: updatedAllowRespData },
        })
      );
      isNavigatedToMainEntry(updatedAllowRespData);
    }
  }, [JSON.stringify(allowancesResp)]);

  useEffect(() => {
    if (
      amountsApiDataFromSlice &&
      !isEmpty(amountsApiDataFromSlice) &&
      !isDateChanged
    ) {
      const updatedAllowRespData = getSortedAllowances(amountsApiDataFromSlice);
      setAllowData(updatedAllowRespData);
      isNavigatedToMainEntry(updatedAllowRespData);
    }
  }, [offerAmountsDataObj]);

  const getSortedAllowances = allowAmountData => {
    const sortedAllowances =
      allowAmountData?.allowances?.length > 0
        ? sortAllowanceById([...(allowAmountData?.allowances || [])])
        : allowAmountData?.allowances;
    const updatedAllowRespData =
      sortedAllowances?.length > 0
        ? { ...allowAmountData, allowances: sortedAllowances }
        : allowAmountData;
    return updatedAllowRespData;
  };

  const isNavigatedToMainEntry = allowRespData => {
    const isItemAmountsSummarized: boolean =
      allowRespData?.summary?.itemAmountsCouldBeSummarized;
    const isOverlapsAvailable =
      allowRespData?.offerAllowanceOverlapResults?.offerAllowanceOverlaps
        ?.length > 0;
    if (allowRespData?.summary) {
      saveToSessionStorage(
        efConstants.ITEM_AMOUNTS_SUMMARIZED_KEY,
        isItemAmountsSummarized
      );
    }
    const isRedirectToMainEntry = !isEditEnable
      ? !isOverlapsAvailable &&
        isItemAmountsSummarized === false &&
        !isSkipRedirection
      : false;
    if (isRedirectToMainEntry || isCommingFromTaskView) {
      navigate(allAllowancesPath);
    }
  };
  return {
    allAllowancesPath,
    allowancesResp: allowData,
    isFetching,
    basePath,
  };
}
