import { FunctionComponent } from "react";
// eslint-disable-next-line @nrwl/nx/enforce-module-boundaries
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
interface IInvalidCICItemsProps {
  cicsErrorMessage: string;
  removeInvalidItems: any;
  invalidCICIdItems: any;
}
const InvalidCICItems: FunctionComponent<IInvalidCICItemsProps> = ({
  cicsErrorMessage,
  invalidCICIdItems,
  removeInvalidItems,
}) => {
  const { REMOVE_INVALID_CIC_IDS } = efConstants;
  const invalidMessages = invalidCICIdItems.map(el => el.itemId);

  return (
    <>
      <div className="text-[#E53014] text-[14px] leading-4 mt-2.5 font-bold">
        {cicsErrorMessage}
      </div>
      <p className="mt-2">
        {invalidMessages.join(", ")}
        <span
          className="text-[13px] text-[#1b6ebb] ml-4 cursor-pointer"
          onClick={removeInvalidItems}
        >
          {REMOVE_INVALID_CIC_IDS}
        </span>
      </p>
    </>
  );
};
export default InvalidCICItems;
