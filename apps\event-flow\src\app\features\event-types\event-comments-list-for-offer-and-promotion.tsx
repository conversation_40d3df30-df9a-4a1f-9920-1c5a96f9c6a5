import { useEffect, useState } from "react";
import { Info } from "lucide-react";
import {
  getHeaderTitlesList,
  handleSelectOfferPromoList,
} from "./event-comments-helper";
import EventCommentsCheckBox from "./event-comments-check-box";
import { EVENT_COMMENTS } from "./constants/event-comments-constants";
const EventCommentsListForOfferAndPromotion = ({
  checked,
  eventDetailsData,
  setEventCommentsList,
  setCommentsPlaceholder,
}) => {
  const {
    COMMENTS_FOR_OFFER_PROMO_LIST_FEATURE: { LABEL },
  } = EVENT_COMMENTS;
  const offerAndPromoListForComments = getHeaderTitlesList(eventDetailsData);
  const [selectedOffers, setSelectedOffers] = useState<
    { id: string; type: string }[]
  >([]);
  const [selectedPromos, setSelectedPromos] = useState<
    { id: string; type: string }[]
  >([]);
  const handleSelectOffer = (offer, checked) => {
    const { isOffer } = offer;
    isOffer
      ? handleSelectOfferPromoList(
          offer,
          checked,
          setSelectedOffers,
          selectedOffers
        )
      : handleSelectOfferPromoList(
          offer,
          checked,
          setSelectedPromos,
          selectedPromos
        );
  };

  useEffect(() => {
    setEventCommentsList({ offers: selectedOffers, promos: selectedPromos });
  }, [
    selectedOffers,
    selectedPromos,
    setEventCommentsList,
    setCommentsPlaceholder,
  ]);

  return offerAndPromoListForComments?.length ? (
    <div className="border-solid border-b border-gray-204 my-4">
      {checked && (
        <div className="flex items-center">
          {LABEL}
          <span className="ml-2">
            <Info color="#1B6EBB" size={15} />
          </span>
        </div>
      )}
      <ul className="my-4">
        {offerAndPromoListForComments.map(offer => (
          <li key={offer.id} className="my-2">
            <EventCommentsCheckBox
              offer={offer}
              handleSelectOffer={handleSelectOffer}
              checked={checked}
            />
          </li>
        ))}
      </ul>
    </div>
  ) : null;
};
export default EventCommentsListForOfferAndPromotion;
