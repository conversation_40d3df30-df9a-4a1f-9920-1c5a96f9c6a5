import React, { useEffect } from "react";
import {
  Accordion,
  AccordionBody,
  AccordionHeader,
} from "@material-tailwind/react";
import { ChevronRight } from "lucide-react";
import { useSelectorWrap } from "@me/data-rtk";
import { isEmpty } from "lodash";

function AccordionAtom({ division, children, isExpandAll }) {
  const [openAccordion, setOpenAccordion] = React.useState(false);
  const {
    data: { allDivisionStepsData },
  } = useSelectorWrap("leadDistributors_rn") || {};

  useEffect(() => {
    // Open accordion for division if it has selected vendors
    const stepDataForDiv = allDivisionStepsData?.[division?.divisionId] || {};
    if (!isEmpty(stepDataForDiv)) {
      stepDataForDiv?.selectedList?.length > 0 && setOpenAccordion(true);
    }
  }, [allDivisionStepsData?.[division?.divisionId]]);

  useEffect(() => {
    setOpenAccordion(isExpandAll);
  }, [isExpandAll]);

  // useEffect(() => {
  //   setExpandAll(false);
  // }, [openAccordion]);

  return (
    <Accordion
      open={openAccordion}
      id={division?.divisionId}
      className="bg-white"
    >
      <AccordionHeader
        onClick={() => setOpenAccordion(!openAccordion)}
        className={`accordian-header-class py-3 text-sm font-normal cursor-pointer`}
      >
        <section className="flex gap-6 px-2">
          <ChevronRight
            className={`h-5 w-5 transition-transform ${
              openAccordion ? "rotate-90" : ""
            }`}
          />
          <div>
            {division?.divisionId} - {division?.divisionName}
          </div>
        </section>
      </AccordionHeader>
      <div
        id={`abs-accordion-container-body-wrapper-${division?.divisionId}`}
        className={`accordion-body-wrapper`}
      >
        {openAccordion && (
          <AccordionBody className="py-0">
            <div
              id={`abs-accordion-container-body-${division?.divisionId}`}
              className="pl-4 justify-start items-center flex-grow-0 flex-shrink-0 gap-2"
            ></div>
            {children}
          </AccordionBody>
        )}
      </div>
    </Accordion>
  );
}

export default AccordionAtom;
