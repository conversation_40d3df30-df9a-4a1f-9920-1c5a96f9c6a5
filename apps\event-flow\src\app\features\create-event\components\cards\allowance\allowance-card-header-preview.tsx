import { FunctionComponent, memo } from "react";
import { ChevronRight as ChevronRightIcon } from "lucide-react";
import { useFormContext } from "react-hook-form";
import Divider from "@albertsons/uds/molecule/Divider";
import {
  getAllowanceAmountsSubHeaderValue,
  getAllowanceTypeByPerformance,
  getCreateIndByLocation,
} from "../../../service/allowance/allowance-service";
import { formateVehicleDate } from "../../../service/event-details/event-detail-service";
import { useSelectorWrap } from "@me/data-rtk";
import { EEVENT_STATUS } from "../../../../event-types/components/event-action/event-action.model";
import efConstants from "../../../../../shared/ef-constants/ef-constants";
import StatusBadge from "../common/status-badge";
import Tooltip from "@albertsons/uds/molecule/Tooltip";
import PeriscopeID from "../common/periscope-id";
import { PROPOSAL } from "../common/common-components-model";
import { getIsNationalEvent } from "../../../../event-types/event-types-helper";
import { appConstants } from "@me/utils-root-props";

interface IAllowanceCardHeaderProps {
  isOpenCard?: any;
  openCard?: any;
  cardIndex?: any;
  cardItemIndex?: any;
  isEditMode?: any;
  handleCancel: any;
}

const AllowanceCardHeader: FunctionComponent<IAllowanceCardHeaderProps> = ({
  isOpenCard,
  openCard,
  cardIndex,
  cardItemIndex,
  isEditMode,
  handleCancel,
}) => {
  const {
    ALLOWANCE_TYPES,
    NEW_DIRECTION_FEATURES_FLAGS: { isDirectionalChangesEnable, ONE_ALLOWANCE },
    S2S_WAREHOUSE_DSD_COMBINED_KEY,
  } = efConstants;
  const { SCAN, CASE, SHIPTOSTORE, HEADERFLAT, ITEMFLAT } = ALLOWANCE_TYPES;
  const { getValues } = useFormContext(),
    offerAllowance = getValues("offerAllowances"),
    allowTypeRegField = `offerAllowances[${cardIndex}].allowances[${cardItemIndex}]`,
    allowanceInfo = getValues(allowTypeRegField) || {},
    {
      allowanceStartDate,
      allowanceEndDate,
      allowanceItems = [],
      includeInd,
      excludedTag,
      allowanceProcessStatus,
    } = allowanceInfo;

  const apexAllowanceStatus = `${allowanceProcessStatus}`;
  const { editCardConfig } = useSelectorWrap(
    "offer_card_configutation_rn"
  ).data;
  const { data: eventData } = useSelectorWrap("event_details_data");
  const { isEventCardOpen } = useSelectorWrap("is_event_edit_enable").data;
  const { isNDPAndFlagEnabled = false } = getIsNationalEvent(eventData?.eventType);
  const offerStatus = `offerAllowances[${cardIndex}].allowances[0].allowanceStatus`;
  const forNationalStatus = `offerAllowances[${cardIndex}].allowances[${cardItemIndex}].allowanceStatus`;
  const offerAllowanceStatus = getValues(isNDPAndFlagEnabled ? forNationalStatus : offerStatus);
  const openCardHandler = () => {
    openCard(!isOpenCard);
  };

  const allowanceType = getAllowanceTypeByPerformance(
    offerAllowance?.[cardIndex]?.allowances[cardItemIndex]?.performance
  );
  const offerCreateInd = getCreateIndByLocation(
    getValues(`offerAllowances[${cardIndex}]`),
    cardItemIndex
  );
  const isPeriscopeIdShow =
    !isDirectionalChangesEnable && ONE_ALLOWANCE[allowanceType].showPeriscopeId;
  const isHeaderFlat = allowanceType === HEADERFLAT.label;
  const isItemFlat = allowanceType === ITEMFLAT.label;
  const amountItems = isHeaderFlat
    ? [getValues(allowTypeRegField)]
    : allowanceItems;
  const getIsopenpreview = () => {
    if (eventData?.inValidAllowances && isEventCardOpen) {
      return eventData?.inValidAllowances && isEventCardOpen;
    } else if (!editCardConfig?.[cardIndex]) {
      return !editCardConfig?.[cardIndex];
    } else {
      return false;
    }
  };
  const getWareHouseLocation = () => {
    const { isNDPAndFlagEnabled: isNational } = getIsNationalEvent(
      eventData?.eventType
    );
    const allowDivId = allowanceInfo?.divisionIds?.[0] || "";
    const divDisplay =
      isNational && allowDivId
        ? `${allowDivId} - ${appConstants?.DIVISIONS_ID_NAME_MAP[allowDivId]} - `
        : "";
    if (
      [CASE.createInd[1], SCAN.createInd[1], SHIPTOSTORE.createInd[1]].includes(
        offerCreateInd
      )
    ) {
      const dispVendNm = allowanceInfo?.vendorName ? `${allowanceInfo?.vendorName} -` : "",
        dispCostArea = allowanceInfo?.costAreaDesc ? `- ${allowanceInfo?.costAreaDesc}` : "";
      return (
        <Tooltip
          zIndex={10}
          anchor="right"
          label={`${divDisplay}${dispVendNm}${allowanceInfo?.vendorNbr}${allowanceInfo?.costAreaDesc || ""}`}
        >
          <span id="abs-allowance-card-header-preview-vendor-number">
            {`${divDisplay}${dispVendNm}${allowanceInfo?.vendorNbr}${dispCostArea}`}
          </span>
        </Tooltip>
      );
    } else if (
      SHIPTOSTORE.createInd[0] === offerCreateInd &&
      allowanceInfo?.vendorNbr === "" &&
      allowanceInfo?.location?.locationTypeCd !== "W"
    ) {
      return `${divDisplay}${S2S_WAREHOUSE_DSD_COMBINED_KEY}`;
    } else if (
      [CASE.createInd[0], SHIPTOSTORE.createInd[0]].includes(offerCreateInd)
    ) {
      return `${divDisplay}${allowanceInfo?.location?.distCenter}`;
    } else if (
      allowanceInfo?.vendorNbr === "" &&
      allowanceInfo?.location?.locationName
    ) {
      return `${divDisplay}${isNational ? allowanceInfo?.location?.distCenter : allowanceInfo?.location?.locationName} `;
    } else if (
      allowanceInfo?.vendorNbr === "" &&
      allowanceInfo?.location?.distCenter
    ) {
      return `${divDisplay}${allowanceInfo?.location?.distCenter} `;
    }
    return "";
  };

  const cheveronRightIconContent = (
    <div
      className="flex flex-col items-center justify-center min-w-[32px] w-[32px] min-h-[32px] h-[32px] abs-ef-allowance-card-header-preview-cheveron-right-icon-content"
      id="abs-allowance-card-header-preview-cheveron-right-icon-container"
    >
      <ChevronRightIcon
        height={12}
        strokeWidth={1}
        color="#5A697B"
        className={`min-w-[24px] min-h-[24px] transition duration-200 ${
          isOpenCard ? "rotate-90" : ""
        }`}
      />
    </div>
  );

  function getHeaderPreviewContent() {
    const wareHouseText = getWareHouseLocation() || "";
    const allowanceAmountText = getAllowanceAmountsSubHeaderValue(
      allowanceType,
      amountItems,
      false,
      false
    );

    return (
      <div
        className="flex items-center justify-start md:w-full"
        id="abs-allowance-card-header-preview-warehouse-allowance-amount-container"
      >
        <p
          className="text-[13px] leading-4 mr-1 font-bold cursor-text"
          id="abs-allowance-card-header-preview-warehouse-text"
        >
          {wareHouseText}
        </p>
        <p
          className="text-[#1b6ebb] leading-4 text-[13px] font-semibold select-none"
          id="abs-allowance-card-header-preview-allowance-amount-text"
        >
          {` - ${allowanceAmountText} `}
        </p>
        {!includeInd && (
          <>
            <span className="ml-1">-</span>
            <p
              className={`ml-1 text-${
                excludedTag ? "[#AB4205]" : "[#1b6ebb]"
              } leading-4 text-[13px] font-semibold select-none`}
              id="abs-allowance-card-header-preview-excluded"
            >
              Excluded
            </p>
          </>
        )}
        {(wareHouseText || allowanceAmountText) && (
          <Divider className="mx-3" height={24} color="#C8DAEB" />
        )}
        <p
          className="text-[13px] leading-4 mr-1 select-none"
          id="abs-allowance-card-header-preview-start"
        >
          Vehicle Start Date:
        </p>
        <p
          className="text-[#1b6ebb] text-[13px] leading-4 font-semibold select-none"
          id="abs-allowance-card-header-preview-allowance-start-date"
        >
          {formateVehicleDate(allowanceStartDate)}
        </p>
        <Divider className="mx-3" height={24} color="#C8DAEB" />
        <p
          className="text-[13px] leading-4 mr-1 select-none"
          id="abs-allowance-card-header-preview-end"
        >
          Vehicle End Date:
        </p>
        <p
          className="text-[#1b6ebb] text-[13px] leading-4 font-semibold select-none"
          id="abs-allowance-card-header-preview-allowance-end-date"
        >
          {formateVehicleDate(allowanceEndDate)}
        </p>
        <Divider className="mx-3" height={24} color="#C8DAEB" />
        {apexAllowanceStatus ? (
          <>
            <p
              className="text-[13px] leading-4 mr-1 select-none"
              id="abs-allowance-card-header-preview-apex"
            >
              Apex Status:
            </p>
            <StatusBadge status={apexAllowanceStatus} />
          </>
        ) : null}
      </div>
    );
  }

  return getIsopenpreview() ? (
    <>
      <div
        onClick={openCardHandler}
        className={`flex items-center w-full h-[50px] -mb-[1px] px-4 cursor-pointer bg-[#EBF3FA] border-b border-gray-204 justify-between
      ${
        [EEVENT_STATUS.REJECTED, EEVENT_STATUS.CANCELED].includes(
          offerAllowanceStatus
        )
          ? "grayscale"
          : ""
      }
      `}
        id="abs-allowance-card-header-preview-container"
      >
        <div
          className="flex flex-row"
          id="abs-allowance-card-header-preview-cheveron-right-icon-content"
        >
          {cheveronRightIconContent}
          {getHeaderPreviewContent()}
          {isNDPAndFlagEnabled && <StatusBadge status={offerAllowanceStatus} />}
        </div>
      </div>
      {isPeriscopeIdShow && !isItemFlat ? (
        <div id="abs-allowance-card-header-preview-periscopeid-1">
          <Divider />
          <div id="abs-allowance-card-header-preview-periscopeid-2">
            <div
              className="bg-[#EBF3FA] rounded-md"
              id="abs-allowance-card-header-preview-periscopeid-3"
            >
              <PeriscopeID
                cardIndex={cardIndex}
                cardItemIndex={cardItemIndex}
                isPromoOrAllow={PROPOSAL.ALLOWANCE}
                eventData={eventData}
              />
            </div>
          </div>
        </div>
      ) : null}
    </>
  ) : null;
};

export default memo(AllowanceCardHeader);
