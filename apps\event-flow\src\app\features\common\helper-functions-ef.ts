export const searchData = (
  itemsData: object[],
  searchItemKey: string,
  searchedText,
) => {
    if (searchedText) {
      const filtered = itemsData.filter((item: any) =>
        item?.[searchItemKey]?.toLowerCase().includes(searchedText.toLowerCase())
      );
      return [...filtered];
    } else {
      return itemsData;
    }
};

export const selectDeselectAll = (originalData, selectedData, uniqueId)=> {
    const allSelected = originalData?.length === selectedData?.length;
    const selectedItemIds = allSelected
      ? []
      : originalData.map(item => item?.[uniqueId]);
    const selectedItems = allSelected ? [] : originalData;
    return {
      selectedItemIds,
      selectedItems,
    };
}