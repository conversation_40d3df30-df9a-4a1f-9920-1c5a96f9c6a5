import { useSelectorWrap } from "@me/data-rtk";
import { useDispatch } from "react-redux";
import {
  setAllowanceFormInfo,
  setOfferSectionsEnableConfig,
} from "../../../../service/slice/allowance-details-slice";

const useGetOfferSectionConfiguration = ({ allowanceRegField = "" }) => {
  const offerSectionsEnableConfig =
    useSelectorWrap("offer_sections_enable_config")?.data || {};
  const offerSections = useSelectorWrap("offer_sections_data")?.data || [];
  const { allowanceFormData } =
    useSelectorWrap("allowance_form_data")?.data || {};
  const formData = allowanceFormData?.[allowanceRegField];

  // console.log("offerSectionsEnableConfig=>>>", offerSectionsEnableConfig);

  const dispatch = useDispatch();

  const updateSectionConfig = sectionConfig => {
    dispatch(setOfferSectionsEnableConfig(sectionConfig));
  };

  const moveToNextSectionOnCreate = (
    currentKey: string,
    offerMapKey: string,
    updatedFormValues?: any
  ) => {
    const sectionConfig = {};
    const updatedFormData = updatedFormValues?.[allowanceRegField] || formData;
    let isCurrentSectionFound = false;
    let isNextSectionUpdated = false;
    offerSections?.forEach((section, index: number) => {
      if (isCurrentSectionFound) {
        sectionConfig[section?.key] = {
          isActive: true,
          scrollTo: true,
        };
        isCurrentSectionFound = false;
        isNextSectionUpdated = true;
        if (!index) {
          updatedFormData[section?.formKey] = {
            ...updatedFormData?.[section?.formKey],
            [offerMapKey]: {},
          };
        }
      } else if (section?.key === currentKey) {
        sectionConfig[currentKey] = {
          isActive: true,
          scrollTo: false,
        };
        isCurrentSectionFound = true;
      } else {
        sectionConfig[section?.key] = {
          isActive: !isNextSectionUpdated,
          scrollTo: false,
        };
        if (isNextSectionUpdated && !index) {
          updatedFormData[section?.formKey] = {
            ...updatedFormData?.[section?.formKey],
            [offerMapKey]: {},
          };
        }
      }
    });
    dispatch(
      setAllowanceFormInfo({
        allowanceFormData: { [allowanceRegField]: updatedFormData },
      })
    );
    updateSectionConfig(sectionConfig);
    return sectionConfig;
  };

  const moveToNextSectionOnUpdate = (currentKey: string) => {
    const sectionConfig = {};
    let isCurrentSectionFound = false;
    offerSections?.forEach(section => {
      if (isCurrentSectionFound) {
        sectionConfig[section?.key] = {
          isActive: true,
          scrollTo: true,
        };
        isCurrentSectionFound = false;
      } else {
        sectionConfig[section?.key] = {
          isActive: true,
          scrollTo: false,
        };
        isCurrentSectionFound = section?.key === currentKey;
      }
    });
    updateSectionConfig(sectionConfig);
    return sectionConfig;
  };

  const clearNextSectionsOnOptionChange = (currentKey: string) => {
    const sectionConfig = {};
    let isCurrentSectionFound = false;
    offerSections?.forEach(section => {
      if (isCurrentSectionFound) {
        sectionConfig[section?.key] = {
          isActive: false,
          scrollTo: false,
        };
      } else {
        isCurrentSectionFound = section?.key === currentKey;
        sectionConfig[section?.key] = {
          isActive: true,
          scrollTo: isCurrentSectionFound,
        };
      }
    });
    updateSectionConfig(sectionConfig);
    return sectionConfig;
  };

  return {
    offerSectionsEnableConfig,
    moveToNextSectionOnCreate,
    moveToNextSectionOnUpdate,
    clearNextSectionsOnOptionChange,
  };
};

export default useGetOfferSectionConfiguration;
