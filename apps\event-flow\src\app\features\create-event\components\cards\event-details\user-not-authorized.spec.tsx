import React from "react";
import "@testing-library/jest-dom";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { MemoryRouter } from "react-router-dom";
import UserNotAuthorized from "./user-not-authorized";

// Mock useNavigate from react-router-dom
const mockNavigate = jest.fn();
jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useNavigate: () => mockNavigate,
}));

jest.mock("@me/util-helpers", () => ({
  useGetAppBasePath: () => ({ basePath: "/app/basePath" }),
}));

jest.mock(
  "../../../../event-types/components/event-header/warning-modal",
  () => props =>
    (
      <div data-testid="warning-modal">
        <div>{props.warningTitle}</div>
        <div>{props.warningBodyText}</div> 
        <button onClick={props.onChange}>{props.confirmButtonLabel}</button>
      </div>
    )
);

describe("UserNotAuthorized Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render WarningModal with correct props", () => {
    render(
      <MemoryRouter>
        <UserNotAuthorized isOpen={true} />
      </MemoryRouter>
    );

    expect(screen.getByText("The user isn't Authorized!")).toBeInTheDocument();
    expect(
      screen.getByText("The user does not have permissions to proceed.")
    ).toBeInTheDocument();
    expect(screen.getByText("Yes, Return to Task View")).toBeInTheDocument();
  });

  it("should call navigate when 'Yes, Return to Task View' is clicked", async () => {
    render(
      <MemoryRouter>
        <UserNotAuthorized isOpen={true} />
      </MemoryRouter>
    );

    const confirmButton = screen.getByText("Yes, Return to Task View");
    await userEvent.click(confirmButton);

    expect(mockNavigate).toHaveBeenCalledWith("/app/basePath/");
  });

  it("should not call navigate when 'No, Continue Editing' is clicked", async () => {
    render(
      <MemoryRouter>
        <UserNotAuthorized isOpen={true} />
      </MemoryRouter>
    );

    const cancelButton = screen.queryByText("No, Continue Editing");
    if (cancelButton) {
      await userEvent.click(cancelButton);
    }

    expect(mockNavigate).not.toHaveBeenCalled();
  });

  it("should call navigate when WarningModal is closed", async () => {
    render(
      <MemoryRouter>
        <UserNotAuthorized isOpen={true} />
      </MemoryRouter>
    );

    // Simulate closing modal
    await userEvent.click(screen.getByText("Yes, Return to Task View"));

    expect(mockNavigate).toHaveBeenCalledWith("/app/basePath/");
  });
});
