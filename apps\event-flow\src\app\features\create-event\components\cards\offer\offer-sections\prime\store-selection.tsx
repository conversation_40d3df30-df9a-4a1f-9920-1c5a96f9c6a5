import { useEffect, useRef, useState } from "react";
import { useSelectorWrap } from "@me/data-rtk";
import PlanStoreGroups from "../../../allowance/stepper/store-selection/PlanStoreGroups";
import PlanStoreGroupType from "../../../allowance/stepper/store-selection/PlanStoreGroupType";
import LoadingSpinner from "../../../../../constants/LoadingSpinner/LoadingSpinner";
import StoreGroupItemModal from "../../../event-details/view-items/store-group-item-modal";
import { isEmpty } from "lodash";
import useGetOfferSectionConfiguration from "../../hooks/useGetOfferSectionConfiguration";
import { OFFER_FORM_FIELDS } from "../../offer-flow-config";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";

function StoreSelection({
  formControls,
  sectionConfiguration,
  isEditEnable,
  allowanceRegField,
}) {
  const sectionKey = sectionConfiguration?.key;
  const {
    planStoreGroupType: planStoreGroupTypeField,
    storeGroups: storeGroupsField,
    notFoundText,
  } = sectionConfiguration?.fields?.STORE_SELECTION || {};
  const { storeSelectionChangeKey } = OFFER_FORM_FIELDS;
  const {
    register = () => null,
    control,
    setValue = () => null,
    getValues = () => {},
  } = formControls || {};

  const { allowanceType = "" } = getValues?.();
  const [storeGroupTypeOptions, setStoreGroupTypeOptions] = useState([]);
  const [selectedStrGrpOptions, setSelStrGrpOptions] = useState([]);
  const defaultStoreGroupData = useRef<any>({})
  const [storeGroupType, setStoreGroupType] = useState({ id: "", name: "" });
  const [isViewStoresModalOpen, setViewStoresModalOpen] =
    useState<boolean>(false);

  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { data: allowanceForm } = useSelectorWrap("allowance_form_data");

  const { clearNextSectionsOnOptionChange } = useGetOfferSectionConfiguration({
    allowanceRegField,
  });

  const allowanceFormData =
    allowanceForm?.allowanceFormData?.[allowanceRegField]?.allowancePrimeData;
  const {
    storeGroups: stroeGrps = [],
    storeGroupType: stroreGrpType = { id: "", name: "" },
  } = allowanceFormData?.allowanceStoreSelection || {};

  const { divisionIds: divIds, planProductGroups } = eventDetailsData || {};

  const onSectionUpdate = () => {
    if (!isEditEnable) {
      clearNextSectionsOnOptionChange(sectionKey);
    }
  };

  const setFormInitialData = () => {
    setStoreGroupType(stroreGrpType);
    setValue("storeGroupType", stroreGrpType);
    setValue(planStoreGroupTypeField.registerField, stroreGrpType?.name);
    setValue(storeGroupsField.registerField, stroeGrps);
  };

  useEffect(() => {
    if (
      allowanceType &&
      !isEditEnable &&
      allowanceType != efConstants?.ALLOWANCE_TYPES?.CASE?.label &&
      !isEmpty(defaultStoreGroupData?.current)
    ) {
      const { storeGroupType, planStoreGroupType, storeGroups } =
        defaultStoreGroupData?.current || {};
      planStoreGroupType &&
        setValue(
          planStoreGroupTypeField.registerField,
          planStoreGroupType || ""
        );
      storeGroupType && setValue("storeGroupType", storeGroupType);
      storeGroups && setValue(storeGroupsField.registerField, storeGroups);
      storeGroups && setSelStrGrpOptions(storeGroups);
    }
  }, [allowanceType]);

  // Need this to have storeGroupType details in the form data otherwise we are getting only name
  useEffect(() => {
    setValue("storeGroupType", storeGroupType);
  }, [storeGroupType]);

  useEffect(() => {
    !isEmpty(allowanceFormData?.allowanceStoreSelection) &&
      setFormInitialData();
  }, [allowanceFormData]);

  const onStoreGroupTypeChange = () => {
    setValue(storeSelectionChangeKey, true);
    setValue(storeGroupsField.registerField, []);
    setSelStrGrpOptions([]);
    onSectionUpdate();
  };

  const onStoreGroupChange = () => {
    !isEditEnable && onSectionUpdate();
    setValue(storeSelectionChangeKey, true);
    return null;
  };

  const bodyContent = (
    <div
      className="flex gap-3"
      id="abs-store-selection-plan-store-group-type-field-cont"
    >
      {planStoreGroupTypeField && (
        <div
          className="w-[250px]"
          id="abs-store-selection-plan-store-group-type-field-sec"
        >
          <PlanStoreGroupType
            register={register}
            control={control}
            planStoreGroupType={planStoreGroupTypeField}
            stroreGrpType={storeGroupType}
            setStoreGroupType={setStoreGroupType}
            storeGroupTypeOptions={storeGroupTypeOptions}
            setStoreGroupTypeOptions={setStoreGroupTypeOptions}
            setValue={setValue}
            divId={divIds?.[0] || ""}
            isEditEnable={isEditEnable}
            onStoreGroupTypeChange={onStoreGroupTypeChange}
            defaultStoreGroupData = {defaultStoreGroupData}
          />
        </div>
      )}

      {storeGroupsField && (
        <div
          className="w-[250px]"
          id="abs-store-selection-plan-store-groups-cont"
        >
          <div
            className="autocomplete-scroll"
            id="abs-store-selection-plan-store-groups-sec"
          >
            <PlanStoreGroups
              register={register}
              control={control}
              storeGroupsField={storeGroupsField}
              strGrpDefaultValue={stroeGrps}
              selectedStrGrpOptions={selectedStrGrpOptions}
              setSelStrGrpOptions={setSelStrGrpOptions}
              storeGroupType={storeGroupType}
              planProductGroups={planProductGroups}
              divId={divIds?.[0] || ""}
              setValue={setValue}
              isEditEnable={isEditEnable}
              notFoundText={notFoundText}
              onStoreGroupChange={onStoreGroupChange}
              defaultStoreGroupData = {defaultStoreGroupData}
            />
          </div>
          {selectedStrGrpOptions?.length ? (
            <div
              className="flex justify-start items-start flex-grow-0 flex-shrink-0  gap-2.5 mt-[5px]"
              id="abs-store-selection-view-stores-cont"
            >
              <div
                className="flex justify-start items-start flex-grow-0 flex-shrink-0  gap-2.5"
                id="abs-store-selection-view-stores-sec"
              >
                <p
                  className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#1b6ebb] cursor-pointer"
                  onClick={() => setViewStoresModalOpen(true)}
                  id="abs-store-selection-view-stores-text"
                >
                  View Stores
                </p>
              </div>
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
  const footerSubContent = isEditEnable && (
    <p
      className="text-sm text-left text-[#9D2210] my-[14px] italic"
      id="abs-store-selection-store-groups-text2"
    >
      Store group type & store groups cannot be changed.
    </p>
  );
  return (
    <div className="my-4">
      <StoreGroupItemModal
        isOpen={isViewStoresModalOpen}
        setOpen={setViewStoresModalOpen}
        storeGroups={selectedStrGrpOptions}
      />
      <LoadingSpinner
        classname="!h-full !w-full rounded-md"
        isLoading={false}
      />
      {control && bodyContent}
      {footerSubContent}
    </div>
  );
}

export default StoreSelection;
