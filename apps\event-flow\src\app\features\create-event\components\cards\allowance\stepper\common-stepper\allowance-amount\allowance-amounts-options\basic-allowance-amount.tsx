import Toggle from "@albertsons/uds/molecule/Toggle";
import { OverlappingAllowanceGrid } from "@me-upp-js/features/expandable-table";
import { useSelectorWrap } from "@me/data-rtk";
import { EEVENT_STATUS } from "@me/util-helpers";
import InValidAmountTooltip from "apps/event-flow/src/app/features/all-allowances/allowance-table/invalid-amount-tooltip";
import useAllowanceAmountValidation from "apps/event-flow/src/app/features/create-event/hooks/allowance-amount-validations";
import {
  formatAmount,
  getUOMBasedOnType,
  getUOMFromNameOrValue,
  roundOfNumber,
} from "apps/event-flow/src/app/features/create-event/service/allowance/allowance-service";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import classNames from "classnames";
import { isEmpty } from "lodash";
import { ChevronDown, Timer } from "lucide-react";
import { useEffect, useState } from "react";
import {
  InputSelect,
  InputText,
} from "../../../../../../fields/allowance-atoms";
import AllowanceFormWrapper, {
  IFormControls,
} from "../../../../../common/allowance-form-wrapper";
import {
  dashedVerticalDivider,
  getFieldWithHighletedWrapper,
} from "../allowance-amounts-services";
import AmountsToggleSwitch from "./amounts-toggle-switch";

export default function BasicAllowanceAmount({
  isEditEnable,
  allowanceAmountFields,
  setAllowanceAmountFields,
  allowanceType,
  offerIndex,
  allowanceIndex,
  stepperElement,
  allowanceAmountData,
  setAllowanceAmountData,
  allowancesResp,
  amountsInitialValueOnLoad,
  editViewAllItems,
  handleSave,
  setisFormValid,
  isZeroCost
}: any) {
  const {
    fields: { allowanceAmount, uom },
    create: { label: createLabel },
    edit: { label: editLabel },
  } = stepperElement;
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const searchId = eventDetailsData?.offerAllowances?.[offerIndex]?.offerNumber;

  const allowanceStatus =
    eventDetailsData?.offerAllowances?.[offerIndex]?.allowances?.[
      allowanceIndex
    ]?.allowanceStatus;
  const eventStatus = eventDetailsData?.planEvent?.eventStatus;

  const { SWITCH_OPTIONS, UOM_OPTIONS } = efConstants;

  const { isSwitching, defaultToggleValue } =
    SWITCH_OPTIONS?.[allowanceType] || {};

  const [switchEnabled, setSwitchEnabled] = useState(
    defaultToggleValue === "Unit"
  );
  const switchValue = switchEnabled ? "Unit" : "Case";
  const areInputsDisabled = defaultToggleValue !== switchValue;

  const switchObj =
    SWITCH_OPTIONS?.[allowanceType]?.dataMapping?.[switchValue] ?? {};

  const { costLabel, costAllowLabel, newCostAllowLabel } =
    switchObj.labels || {};

  const { cost, costAllow } = allowancesResp?.summary?.[switchObj.key] || {};

  const finalAmountKey =
    defaultToggleValue === "Case" ? "newCaseCostAllow" : "newUnitCostAllow";

  const [formControls, setFormControls] = useState<IFormControls>();
  const [digitError, setDigitError] = useState("");

  const {
    register = () => null,
    control,
    setValue = () => null,
  } = formControls || {};
  const getEventData = () => {
    return eventDetailsData;
  };

  useEffect(() => {
    setValue(uom.registerField, allowanceAmountData?.uom);
  }, [allowanceAmountData?.uom]);

  useEffect(() => {
    if (!allowancesResp) return;

    if (!isEmpty(amountsInitialValueOnLoad)) {
      setAllowanceAmountData({
        ...amountsInitialValueOnLoad,
        uom: getUOMFromNameOrValue({ value: amountsInitialValueOnLoad.uom })
          ?.name,
        finalAmountKey,
        finalAmountVal: costAllow - amountsInitialValueOnLoad.allowanceAmount,
      });

      return;
    }

    const prevAmount = "";
    // TODO: how the data will be provided for the UOM
    let prevUom =
      allowancesResp?.allowances?.[0]?.allowanceItems?.[0]?.allowUomType;
    prevUom = getUOMFromNameOrValue({ value: prevUom })?.name;
    setValue(allowanceAmount.registerField, prevAmount);
    // setValue(uom.registerField, prevUom);
    setAllowanceAmountData({
      allowanceAmount: prevAmount,
      uom: prevUom,
      finalAmountKey,
      finalAmountVal: costAllow - parseFloat(prevAmount),
    });
  }, [allowancesResp]);

  const { calcError, validateNewAmount } = useAllowanceAmountValidation({
    maxLimit: roundOfNumber(costAllow),
  });

  const onAmountChange = (value: string) => {
    const { amount } = validateNewAmount(value);
    amount && Number(amount) === 0
      ? setDigitError(allowanceAmount?.errors?.digitError?.text)
      : setDigitError("");
    if (
      searchId &&
      ![allowanceStatus, eventStatus].includes(EEVENT_STATUS.DRAFT)
    ) {
      setAllowanceAmountFields({
        ...allowanceAmountFields,
        allowanceAmountChange: true,
      });
    }
    setValue("allowanceAmount", amount);
    setAllowanceAmountData({
      ...allowanceAmountData,
      allowanceAmount: amount,
      finalAmountVal: costAllow - parseFloat(amount),
    });
  };

  const onUomChange = val => {
    if (
      searchId &&
      ![allowanceStatus, eventStatus].includes(EEVENT_STATUS.DRAFT)
    ) {
      setAllowanceAmountFields({
        ...allowanceAmountFields,
        uomChange: true,
      });
    }
    setAllowanceAmountData({ ...allowanceAmountData, uom: val.name });
    setValue("uom", val.name);
  };

  function renderAllowanceAmount() {
    return (
      <span
        className="relative max-w-[180px]"
        id="abs-basic-allowance-amount-max-w-container"
      >
        {areInputsDisabled
          ? renderDisabledComponent(
              allowanceAmount.label,
              allowanceAmount.required,
              roundOfNumber(
                parseFloat(allowanceAmountData.allowanceAmount) *
                  (areInputsDisabled
                    ? allowanceType === "SCAN"
                      ? allowancesResp?.summary?.packRetail
                      : 1 / allowancesResp?.summary?.packRetail
                    : 1)
              ) || "",
              false,
              "$"
            )
          : getFieldWithHighletedWrapper(
              <InputText
                fieldProps={allowanceAmount}
                type={allowanceAmount.type}
                register={register}
                control={control}
                onChange={onAmountChange}
                className={`${
                  calcError && !isZeroCost ? "rounded border-error border-2 inputSelect" : ""
                } allowance-amounts-padding`}
                onFocus={(value: string) => {
                  setValue(allowanceAmount.registerField, value);
                }}
                onWheel={event => event.currentTarget.blur()}
                prefixValue={"$"}
                error={{ message: digitError }}
              />,
              allowanceAmount,
              allowanceAmountFields,
              getEventData,
              searchId
            )}
      </span>
    );
  }

  function renderDisabledComponent(
    label,
    required,
    value,
    isSelect,
    prefix = ""
  ) {
    return (
      <div id="abs-basic-allowance-amount-container-one">
        <div
          className="flex font-bold gap-1"
          id="abs-basic-allowance-amount-label"
        >
          <p id="abs-basic-allowance-amount-label-text">{label}</p>
          {required ? (
            <p
              className="text-sm text-left text-[#bf2912]"
              id="abs-basic-allowance-amount-label-required"
            >
              *
            </p>
          ) : null}
        </div>
        <div
          className="flex rounded bg-gray-205 h-[40px] text-disabled-text border border-[#a5a7ab] p-2 cursor-not-allowed items-center justify-between"
          id="abs-basic-allowance-amount-value"
        >
          <p
            className={classNames({
              "text-base font-bold text-left mt-1": true,
              "": !isSelect,
            })}
            id="abs-basic-allowance-amount-value-text"
          >
            {prefix}
            {value}
          </p>
          {isSelect && <ChevronDown />}
        </div>
      </div>
    );
  }

  function renderUOM() {
    return (
      control && (
        <div
          className="min-w-[100px]"
          id="abs-basic-allowance-amount-are-inputs-disabled-container"
        >
          {areInputsDisabled
            ? renderDisabledComponent(
                uom.label,
                uom.required,
                allowanceAmountData.uom,
                true
              )
            : control &&
              getFieldWithHighletedWrapper(
                <InputSelect
                  fieldProps={uom}
                  register={register}
                  control={control}
                  options={getUOMBasedOnType(
                    allowanceType,
                    UOM_OPTIONS,
                    switchValue
                  )}
                  displayLabel={uom.displayLabel}
                  onChange={onUomChange}
                />,
                uom,
                allowanceAmountFields,
                getEventData,
                searchId
              )}
        </div>
      )
    );
  }

  function renderAmountVal(label: string, value: number, icon: any = "") {
    return (
      <div id="abs-basic-allowance-amount-renderAmountVal-container">
        <p
          className="text-sm font-bold text-left text-[#2b303c]"
          id="abs-basic-allowance-amount-render-amount-val-labelOne"
        >
          {label}
        </p>
        <p
          className="text-base font-bold text-left text-[#033b69] mt-2 flex items-center"
          id="abs-basic-allowance-amount-render-amount-val-value"
        >
          <span>${formatAmount(value)}</span>
          {icon}
        </p>
      </div>
    );
  }

const renderTopSection = (
  <>
    <p
      className="w-full text-sm text-left text-[#5a697b]"
      id="abs-basic-allowance-amount-render-top-section-container"
    >
      <span className="w-full text-sm text-left text-[#5a697b]">
        All Costs on all Items are the same, you can enter one Allowance amount
        for all of the Items:
      </span>
    </p>
    <div
      className="mr-0 mb-5 mt-5 flex justify-between items-center"
      id="abs-basic-allowance-amount-render-amount-val-section"
    >
      <div className="flex items-center">
        {!isZeroCost ? (
          <>
            {renderAmountVal(
              costLabel,
              cost,
              <span className="ml-1 text-brand-light">
                <Timer size={15} />
              </span>
            )}
          </>
        ) : null}
      </div>

      <div className="ml-auto">
        <AmountsToggleSwitch
          {...{
            switchEnabled,
            setSwitchEnabled,
            switchValue,
            defaultToggleValue,
          }}
        />
      </div>
    </div>
  </>
);

  const renderMiddleSection = (
    <>
      {allowancesResp?.offerAllowanceOverlapResults?.offerAllowanceOverlaps && (
        <OverlappingAllowanceGrid
          quickEntryData={
            allowancesResp.offerAllowanceOverlapResults.offerAllowanceOverlaps
          }
          isCase={!switchEnabled}
        />
      )}
      <div
        className="flex-grow-0 flex-shrink-0 w-full mt-4 h-px  bg-[#fdfdff] border-t border-r border-b-0 border-l border-[#c8daeb]"
        id="abs-basic-allowance-amount-render-middle-section-one"
      ></div>
    </>
  );

  const getFormControls = (controls: IFormControls) => {
    setFormControls(controls);
  };

  // functions
  function calculateFinalCost(amount) {
    const totalAmount = roundOfNumber(
      amount *
        (areInputsDisabled
          ? allowanceType === "SCAN"
            ? allowancesResp?.summary?.packRetail
            : 1 / allowancesResp?.summary?.packRetail
          : 1)
    );
    return roundOfNumber(costAllow) - (isNaN(totalAmount) ? 0 : totalAmount);
  }

const renderBottomSection = (
  <div
    className="flex mt-4 w-full justify-between"
    id="abs-basic-allowance-amount-render-bottom-section-container"
  >
    <div
      className="flex gap-3"
      id="abs-basic-allowance-amount-cost-allow-label-cost-allow"
    >
      {!isZeroCost ? (
        <>
          {renderAmountVal(costAllowLabel, costAllow)}

          {dashedVerticalDivider}
        </>
      ) : null}

      {renderAllowanceAmount()}
      {renderUOM()}
      {!isZeroCost ? (
        <>
          <div
            className="pt-5"
            id="abs-basic-allowance-amount-invalid-amount-tooltip-one"
          >
            <InValidAmountTooltip calcError={calcError} />
          </div>

          {dashedVerticalDivider}

          {renderAmountVal(
            newCostAllowLabel,
            calculateFinalCost(allowanceAmountData.allowanceAmount)
          )}

          {dashedVerticalDivider}
        </>
      ) : null}
    </div>

    <p
      className="mt-[18px] text-base font-semibold text-center text-[#1b6ebb]"
      id="abs-basic-allowance-amount-edit-view-all-items"
    >
      <a
        className="cursor-pointer"
        onClick={editViewAllItems}
        id="abs-basic-allowance-amount-edit-view-all-items-link"
      >
        <span> Edit / View All Items</span>
      </a>
    </p>
  </div>
);

  const renderHtml = (
    <div
      className="w-full"
      id="abs-basic-allowance-amount-render-top-section-one-con"
    >
      {renderTopSection}
      <AllowanceFormWrapper
        defaultValues={{
          ...amountsInitialValueOnLoad,
          uom: amountsInitialValueOnLoad.uom
            ? getUOMFromNameOrValue({ value: amountsInitialValueOnLoad.uom })
                .name
            : undefined,
        }}
        handleSave={handleSave}
        getFormControls={getFormControls}
        footerProps={{
          label: isEditEnable ? editLabel : createLabel,
        }}
      >
        <div id="abs-basic-allowance-amount-render-middle-section-two">
          {renderMiddleSection}
          {control && renderBottomSection}
        </div>
      </AllowanceFormWrapper>
    </div>
  );

  return renderHtml;
}
