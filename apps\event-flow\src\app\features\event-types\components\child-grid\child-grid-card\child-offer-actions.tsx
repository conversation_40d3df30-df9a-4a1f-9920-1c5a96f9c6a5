import React, { memo, useState } from "react";
import { ViewAgreementModal } from "../../event-action/view-agreement-modal";
import { useSelectorWrap } from "@me/data-rtk";

function ChildOfferActions({ cardIndex, selectedTabConfig }) {
  const { gridDataSliceKey = "" } = selectedTabConfig || {};
  const { data: gridData = [] } = useSelectorWrap(gridDataSliceKey) || {};
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const [isAllowanceAgreementVisible, setIsAllowanceAgreementVisible] =
    useState(false);
  const cardData = gridData?.[cardIndex];
  const createInd = cardData?.createInd;

  return (
    <div>
      <div
        className="text-[#1B6EBB] cursor-pointer flex gap-1"
        onClick={() => {
          setIsAllowanceAgreementVisible(true);
        }}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          className="lucide lucide-file-spreadsheet-icon lucide-file-spreadsheet mt-1"
        >
          <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z" />
          <path d="M14 2v4a2 2 0 0 0 2 2h4" />
          <path d="M8 13h2" />
          <path d="M14 13h2" />
          <path d="M8 17h2" />
          <path d="M14 17h2" />
        </svg>
        <span>View Deal Sheet</span>
      </div>
      {isAllowanceAgreementVisible && (
        <ViewAgreementModal
          setIsAllowanceAgreementVisible={setIsAllowanceAgreementVisible}
          eventId={eventDetailsData?.id}
          divisionName={cardData?.divisionIds?.[0]}
          allowanceType={createInd}
          offerNumber={cardData?.offerNumber}
          allowanceChangeStatus={cardData?.allowanceChangeStatus}
          eventStatus={eventDetailsData?.eventStatus}
          allowances={cardData?.allowances}
          eventType={eventDetailsData?.eventType}
        />
      )}
    </div>
  );
}

export default memo(ChildOfferActions);
