import { InputSelectAtom } from "../../../../fields/index";
import { useGetStoreGroupTypesByDivisionIdQuery } from "../../../../../../../graphql/generated/schema";
import { useEffect } from "react";
import { getLoggedInUserType } from "@me-upp-js/utilities";
import { EUSER_ROLES } from "@me/util-helpers";

const PlanStoreGroupType = ({
  planStoreGroupType,
  register,
  control,
  stroreGrpType,
  setStoreGroupType,
  storeGroupTypeOptions = [],
  setStoreGroupTypeOptions,
  setValue,
  divId,
  isEditEnable,
  onStoreGroupTypeChange,
  defaultStoreGroupData = { current: {} },
}) => {
  const variables = {
    divId,
  };
  const userRole = getLoggedInUserType();
  const { data } = useGetStoreGroupTypesByDivisionIdQuery({ variables });

  useEffect(() => {
    const stroGrpTypeOptions =
      data?.getStoreGroupTypesByDivisionId?.reduce((result: any, val) => {
        const updatedGroupType = {
          id: val?.groupInd,
          name: val?.storeGrpTypeName,
          item: val,
        };
        if (
          userRole === EUSER_ROLES?.MERCHANT ||
          updatedGroupType?.id !== "PA"
        ) {
          result?.push(updatedGroupType as any);
        }
        return result;
      }, []) || [];

    setStoreGroupTypeOptions(stroGrpTypeOptions);
    const groupName = stroreGrpType?.name || "Division";
    const selectedStoreGrp = stroGrpTypeOptions?.find(
      groupType => groupType.name === groupName
    );
    setValue(planStoreGroupType.registerField, selectedStoreGrp?.name || "");
    setStoreGroupType(selectedStoreGrp);
    if (!isEditEnable && !stroreGrpType?.name && defaultStoreGroupData) {
      defaultStoreGroupData.current = {
        ...(defaultStoreGroupData.current || {}),
        storeGroupType: selectedStoreGrp,
        planStoreGroupType: selectedStoreGrp?.name,
      };
    }
  }, [data]);

  const onChange = option => {
    setStoreGroupType(option);
    setValue(planStoreGroupType.registerField, option?.name || "");
    onStoreGroupTypeChange(option);
  };

  return (
    <InputSelectAtom
      fieldProps={planStoreGroupType}
      register={register}
      control={control}
      options={storeGroupTypeOptions}
      displayLabel={planStoreGroupType.displayLabel}
      disabled={!storeGroupTypeOptions.length || isEditEnable}
      onChange={onChange}
    />
  );
};
export default PlanStoreGroupType;
