export const multi_dvision_allowance = [
  {
    title: "Scan",
    info: [
      {
        isTick: true,
        title: "Price / Ad / Display (88)",
        text: "Event Description. Explain briefly about best usage of this event type",
      },
      {
        isTick: true,
        title: "4U Event (52)",
        text: "Event Description. Explain briefly about best usage of this event type",
      },
    ],
  },
  {
    title: "Case",
    info: [
      {
        isTick: true,
        title: "Non-Performance (01)",
        text: "Event Description. Explain briefly about best usage of this event type",
      },
      {
        isTick: true,
        title: "Billback Liquor (85)",
        text: "Event Description. Explain briefly about best usage of this event type",
      },
      {
        isTick: true,
        title: "Off Invoice (01)",
        text: "Per Case Allowances for Warehouse items where Performance is optional and will be given Off Invoice.",
      },
      {
        isTick: true,
        title: "Case Purchase (01)",
        text: "Event Description. Explain briefly about best usage of this event type",
      },
    ],
  },
  {
    title: "Ship to Store",
    info: [
      {
        isTick: true,
        title: "Price / Ad / Display (88)",
        text: "Event Description. Explain briefly about best usage of this event type",
      },
    ],
  },
  {
    title: "Header Flat",
    info: [
      {
        isTick: true,
        title: "Contract Allow (03)",
        text: "Event Description. Explain briefly about best usage of this event type",
      },
      {
        isTick: true,
        title: "Coupon/Complex (20)",
        text: "Event Description. Explain briefly about best usage of this event type",
      },
      {
        isTick: true,
        title: "ABS Foundation (29)",
        text: "Event Description. Explain briefly about best usage of this event type",
      },
      {
        isTick: true,
        title: "BBS/4U Tags (32)",
        text: "Event Description. Explain briefly about best usage of this event type",
      },
      {
        isTick: true,
        title: "4U Event (52)",
        text: "Event Description. Explain briefly about best usage of this event type",
      },
      {
        isTick: true,
        title: "Boise/Corp Contract (70)",
        text: "Event Description. Explain briefly about best usage of this event type",
      },
      {
        isTick: true,
        title: "Price / Ad / Display (88)",
        text: "Event Description. Explain briefly about best usage of this event type",
      },
      {
        isTick: true,
        title: "Special Major Events (92)",
        text: "Event Description. Explain briefly about best usage of this event type",
      },
      {
        isTick: true,
        title: "Other (99)",
        text: "Event Description. Explain briefly about best usage of this event type",
      },
    ],
  },
  {
    title: "Item Flat",
    info: [
      {
        isTick: true,
        title: "Price / Ad / Display (88)",
        text: "Event Description. Explain briefly about best usage of this event type",
      },
      {
        isTick: true,
        title: "Other (99)",
        text: "Event Description. Explain briefly about best usage of this event type",
      },
    ],
  },
];
