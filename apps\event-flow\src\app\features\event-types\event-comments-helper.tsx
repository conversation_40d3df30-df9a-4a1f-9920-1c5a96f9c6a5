import { EEVENT_STATUS, PROMOTION_TYPES } from "@me/util-helpers";
import efConstants from "../../shared/ef-constants/ef-constants";
import {
  getAllowanceTypeByPerformance,
  getOfferAllowanceAmount,
} from "../create-event/service/allowance/allowance-service";
import {
  getNationalOfferStatus,
  isRenderPromosInComments,
} from "@me-upp-js/utilities";
import { getIsNationalEvent } from "./event-types-helper";

const { ALLOWANCE_TYPES } = efConstants;
const { HEADERFLAT } = ALLOWANCE_TYPES;

export const getOfferTitle = responseData => {
  const allowanceType = getAllowanceTypeByPerformance(
    responseData?.allowances?.[0]?.performance
  );

  const allowances = responseData?.allowances;
  const isHeaderFlat = allowanceType === HEADERFLAT.label;
  const offetAmount =
    getOfferAllowanceAmount(allowanceType, allowances, isHeaderFlat) || "";
  const header = `Offer # ${responseData?.offerNumber} - ${allowanceType} ${offetAmount}`;
  return {
    title: header,
    id: responseData?.offerNumber,
    type: allowanceType,
    isOffer: true,
  };
};
export const getPromoTittle = promotion => {
  const {
    promoDetails: {
      promotionType,
      amount,
      unitMeasure,
      itemsLimit,
      minQuantity,
    },
    promotionId,
  } = promotion;
  const promotType = PROMOTION_TYPES[promotionType];
  const udpateUOMLabel = unitMeasure ? `Each` : "";

  const NET_PRICE_AND_CENT_OFF = `${promotType}, $${amount} ${udpateUOMLabel}`;
  const promoSummary = {
    NET_PRICE: NET_PRICE_AND_CENT_OFF,
    BUY_ONE_GET_ONE: `${promotType}${
      itemsLimit > 0 ? `, Item limit ${itemsLimit}` : ""
    }`,
    BUY_X_GET_ONE: `${promotType}, Minimum Quantity ${minQuantity}`,
    CENT_OFF: NET_PRICE_AND_CENT_OFF,
    PERCENT_OFF: `${promotType}, ${amount}% ${udpateUOMLabel}`,
  };

  return {
    title: `Promo # ${promotionId} - ${promoSummary[promotionType]}`,
    id: promotionId,
    type: promotionType,
    isOffer: false,
  };
};

export const getHeaderTitlesList = eventDetailsData => {
  const headerList: {
    title: string;
    id: string;
    type: string;
    isOffer: boolean;
  }[] = [];
  const { isNDPAndFlagEnabled: isNational } = getIsNationalEvent(
    eventDetailsData?.eventType
  );
  if (eventDetailsData?.offerAllowances?.length) {
    eventDetailsData.offerAllowances.forEach(offerAllowance => {
      const statusToCheck = isNational
        ? getNationalOfferStatus(offerAllowance?.allowances)
        : offerAllowance?.allowances[0]?.allowanceStatus;
      if (
        [
          EEVENT_STATUS.AGREED,
          EEVENT_STATUS.ACTIVE,
          EEVENT_STATUS.EXECUTED,
        ].includes(statusToCheck)
      ) {
        const offerHeader = getOfferTitle(offerAllowance);
        headerList.push(offerHeader);
      }
    });
  }
  if (eventDetailsData?.promotionsList?.length && isRenderPromosInComments()) {
    eventDetailsData.promotionsList.forEach(promotion => {
      if (
        [
          EEVENT_STATUS.AGREED,
          EEVENT_STATUS.ACTIVE,
          EEVENT_STATUS.EXECUTED,
        ].includes(promotion?.promotionWorkflowStatus)
      ) {
        const promotionHeader = getPromoTittle(promotion);
        headerList.push(promotionHeader);
      }
    });
  }
  return headerList;
};
export const handleSelectOfferPromoList = (
  offerPromo,
  checked,
  setFn,
  offerPromoVal
) => {
  const { id, type } = offerPromo;
  if (checked) {
    setFn([...offerPromoVal, { id, type: type }]);
  } else {
    setFn(offerPromoVal.filter(o => o?.id !== id));
  }
};
