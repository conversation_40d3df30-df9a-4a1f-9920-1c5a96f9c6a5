import { Control, useController, UseFormRegister } from "react-hook-form";
import Input from "@albertsons/uds/molecule/Input";
import { FormFieldError } from "@me/util-form-wrapper";
import { ChangeEvent, FocusEventHandler } from "react";
import "../input-text.scss";
import classNames from "classnames";
import { getObjectValueFromString } from "@me-upp-js/utilities";

interface CustomValidationProps {
  value: boolean;
  message: string;
}

interface IInputTextProps {
  register: UseFormRegister<any> | (() => void);
  control: Control<any, any> | undefined;
  fieldProps: IFieldProps;
  defaultValue?: string | undefined;
  tooltip?: string;
  className?: string;
  disabled?: boolean;
  type?: "number" | "search" | "text" | "password";
  onChange: (value: string) => void;
  onFocus?: (event: any) => void;
  error?: any;
  onWheel?: any;
  suffixValue?: string;
  prefixValue?: string;
  onKeyDown?: (event: any) => void;
  placeHolder?: string;
  showLabel?: boolean;
  customValidation?: CustomValidationProps;
  isCustomValidationEnabled?: boolean;
}

interface IFieldProps {
  label: string;
  registerField: string;
  required: boolean;
  errors: any;
}

const InputText = ({
  register,
  fieldProps,
  control,
  tooltip = "",
  disabled = false,
  type = "text",
  className = "",
  onChange,
  onFocus = (event: FocusEventHandler<HTMLInputElement>) => null,
  error,
  onWheel = () => null,
  suffixValue,
  prefixValue,
  onKeyDown,
  placeHolder,
  showLabel = true,
  customValidation = { value: true, message: "" },
  isCustomValidationEnabled = false,
}: IInputTextProps) => {
  const { registerField, label, required, errors: errorMessages } = fieldProps;
  const { formState, field } = useController({ name: registerField, control });
  const fieldErrorData =
    getObjectValueFromString(formState.errors, registerField) || error;
  const {
    required: requiredValidation,
    maxLength,
    formatError,
  } = errorMessages;

  const onChangeHandler = (event: ChangeEvent<HTMLInputElement>) => {
    field.onChange(event?.target?.value || "");
    onChange(event?.target?.value || "");
  };

  const onFocusHandler = (event: ChangeEvent<HTMLInputElement>) => {
    onFocus(event?.target?.value || "");
  };
  return (
    <div className="w-full" id="abs-input-text-label-main-dv">
      {showLabel ? (
        <div className="flex font-bold gap-1" id="abs-input-text-label-main-d">
          <p id="abs-input-text-label">{label}</p>
          {required ? (
            <p
              className="text-sm text-left text-[#bf2912]"
              id="abs-input-text-label-null"
            >
              *
            </p>
          ) : null}
        </div>
      ) : null}
      <div className="w-full inputMargin" id="abs-input-text-input">
        <Input
          {...field}
          {...register(registerField, {
            required: required ? requiredValidation?.text : required,
            validate: value => {
              return (
                !isCustomValidationEnabled ||
                (value !== undefined && !customValidation?.value) ||
                customValidation?.message
              );
            },
            maxLength: maxLength
              ? { value: maxLength.length, message: maxLength.text }
              : undefined,
            pattern: formatError
              ? {
                  value: new RegExp(formatError.pattern),
                  message: formatError.text,
                }
              : undefined,
          })}
          onWheel={onWheel}
          type={type}
          disabled={disabled}
          placeholder={placeHolder}
          className={classNames(
            { [className as string]: !fieldErrorData?.message },
            "rounded periodScroll w-full inputSelect",
            {
              "rounded border-error border-2": fieldErrorData?.message,
            }
          )}
          tooltip={tooltip}
          onChange={onChangeHandler}
          onFocus={onFocusHandler}
          suffixElement={suffixValue}
          prefixElement={prefixValue}
          onKeyDown={onKeyDown}
          id="abs-input-text-input"
        />
        {fieldErrorData?.message && (
          <FormFieldError
            error={fieldErrorData}
            classes={registerField.includes("acApOrArNumber")}
          />
        )}
      </div>
    </div>
  );
};

export default InputText;
