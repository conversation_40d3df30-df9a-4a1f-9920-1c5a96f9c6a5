import { useSelectorWrap } from "@me/data-rtk";
import React from "react";
import { generateOfferAndAllowanceName } from "../../../../create-event/service/allowance/allowance-service";

function ChildOfferCardHeader({ selectedTabConfig, cardIndex }) {
  const { gridDataSliceKey } = selectedTabConfig;
  const { data: gridData = [] } = useSelectorWrap(gridDataSliceKey);
  const currentOfferData = gridData?.[cardIndex] || {};
  const offerHeader = generateOfferAndAllowanceName(currentOfferData);

  return (
    <div>
      <section className="flex justify-between w-full">
        <div className="font-extrabold text-lg leading-6 text-dark-text">
          {offerHeader}
        </div>
      </section>
    </div>
  );
}

export default ChildOfferCardHeader;
