import { EEVENT_STATUS } from "@me/util-helpers";

const CANCEL_EVENT_MODAL_TITLE = `Are you sure you want to cancel the selected division event(s)? Once canceled, they cannot be added back.`;
const REJECT_EVENT_MODAL_TITLE = `Are you sure you want to reject the selected division event(s)? Once rejected, they cannot be added back.`;
const DELETE_EVENT_MODAL_TITLE = `Are you sure you want to remove the selected division event(s)? Once removed, they will no longer be part of this national event.`;
export const ALL_DIV_DELETE_MODAL_TITLE =
  "Are you sure you want to remove all division event(s)? Once removed, event will be Removed.";
export const ALL_DIV_REJECT_MODAL_TITLE =
  "Are you sure you want to reject all division event(s)? Once rejected, event will be marked as Rejected.";
export const ALL_DIV_CANCEL_MODAL_TITLE =
  "Are you sure you want to cancel all division event(s)? Once canceled, event will be marked as Canceled.";

export const CHILD_EVENTS_CONFIG = {
  events: {
    label: "Event Details",
    default: true,
    isDisabled: false,
    columns: {
      division: {
        label: "Division",
        default: false,
      },
      ppg: {
        label: "PPG",
        default: false,
      },
      stores: {
        label: "Stores",
        default: false,
      },
      startDate: {
        label: "Start Date",
        default: false,
      },
      endDate: {
        label: "End Date",
        default: false,
      },
      pid: {
        label: "PID",
        default: false,
      },
      eventId: {
        label: "Event ID",
        default: false,
      },
      status: {
        label: "Status",
        default: false,
      },
    },
  },
  offers: {
    label: "Offers",
    default: false,
    isDisabled: false,
    columns: {
      division: {
        label: "Division",
        default: false,
      },
      ppg: {
        label: "PPG",
        default: false,
      },
      stores: {
        label: "Stores",
        default: false,
      },
      startDate: {
        label: "Start Date",
        default: false,
      },
      endDate: {
        label: "End Date",
        default: false,
      },
      pid: {
        label: "PID",
        default: false,
      },
      eventId: {
        label: "Event ID",
        default: false,
      },
      status: {
        label: "Status",
        default: false,
      },
    },
  },
  promotions: {
    label: "Promotions",
    default: false,
    isDisabled: true,
  },
};

const COMMON_MODAL_CONFIG = {
  confirmBtnTitle: "Yes",
  cancelBtnTitle: "No",
  height: 255,
  minHeight: 280,
};

// Reusable action button configuration
const getActionBtnConfig = (key, label, variant = "primary") => ({
  key,
  label,
  variant,
});

const CHILD_EVENT_USER_ACTIONS_CONFIG = {
  actionConfig: {
    [EEVENT_STATUS.DRAFT]: {
      actionBtns: [
        getActionBtnConfig("deleteChildEvent", "Remove Selected Division"),
      ],
      modalConfig: {
        ...COMMON_MODAL_CONFIG,
        title: DELETE_EVENT_MODAL_TITLE,
      },
      allDivModalTitle: ALL_DIV_DELETE_MODAL_TITLE,
    },
    [EEVENT_STATUS.PENDING_WITH_VENDOR]: {
      actionBtns: [
        getActionBtnConfig("rejectChildEvent", "Reject Selected Division"),
      ],
      modalConfig: {
        ...COMMON_MODAL_CONFIG,
        title: REJECT_EVENT_MODAL_TITLE,
      },
      allDivModalTitle: ALL_DIV_REJECT_MODAL_TITLE,
    },
    [EEVENT_STATUS.PENDING_WITH_MERCHANT]: {
      actionBtns: [
        getActionBtnConfig("rejectChildEvent", "Reject Selected Division"),
      ],
      modalConfig: {
        ...COMMON_MODAL_CONFIG,
        title: REJECT_EVENT_MODAL_TITLE,
      },
      allDivModalTitle: ALL_DIV_REJECT_MODAL_TITLE,
    },
    [EEVENT_STATUS.AGREED]: {
      actionBtns: [
        getActionBtnConfig("cancelChildEvent", "Cancel Selected Division"),
      ],
      modalConfig: {
        ...COMMON_MODAL_CONFIG,
        title: CANCEL_EVENT_MODAL_TITLE,
      },
      allDivModalTitle: ALL_DIV_CANCEL_MODAL_TITLE,
    },
    [EEVENT_STATUS.ACTIVE]: {
      actionBtns: [
        getActionBtnConfig("cancelChildEvent", "Cancel Selected Division"),
      ],
      modalConfig: {
        ...COMMON_MODAL_CONFIG,
        title: CANCEL_EVENT_MODAL_TITLE,
      },
      allDivModalTitle: ALL_DIV_CANCEL_MODAL_TITLE,
    },
    [EEVENT_STATUS.EXECUTED]: {
      actionBtns: [
        getActionBtnConfig("cancelChildEvent", "Cancel Selected Division"),
      ],
      modalConfig: {
        ...COMMON_MODAL_CONFIG,
        title: CANCEL_EVENT_MODAL_TITLE,
      },
      allDivModalTitle: ALL_DIV_CANCEL_MODAL_TITLE,
    },
  },
};

export const CHILD_EVENTS_TABS = [
  {
    label: "Event Details",
    id: "event-details",
    default: true,
    tabIndex: 0,
    key: "events",
    childDataKey: "events",
    isDisabled: false,
    gridDataSliceKey: "child_events_details_data",
    gridConfig: {
      id: "abs-child-events-view-table",
      checkboxes: true,
      itemKey: "eventId",
      noPagination: true,
      className: "",
      columnConfigComponent: "event-details",
      columns: {
        divisionId: {
          id: "divisionId",
          label: "Division",
          width: "8.5vw",
          key: "divisionId",
          isEditable: false,
          sortable: false,
        },
        ppg: {
          id: "ppg",
          label: "PPG",
          width: "8.5vw",
          key: "ppg",
          isEditable: false,
        },
        stores: {
          id: "stores",
          label: "Stores",
          width: "8.5vw",
          key: "stores",
          isEditable: false,
        },
        startDate: {
          id: "startDate",
          label: "Start Date",
          width: "8.5vw",
          key: "startDate",
          isEditable: false,
          field: {
            type: "date",
            placeholder: "Start Date",
          },
        },
        endDate: {
          id: "endDate",
          label: "End Date",
          width: "8.5vw",
          key: "endDate",
          isEditable: false,
          field: {
            type: "date",
            placeholder: "End Date",
          },
        },
        pid: {
          id: "pid",
          label: "PID",
          width: "8.5vw",
          key: "pid",
          isEditable: false,
          field: {
            type: "text",
            placeholder: "Pid",
            name: "pid",
            validation: {
              required: true,
              errorMessage: "Please enter a valid PID",
            },
          },
        },
        eventId: {
          id: "eventId",
          label: "Event ID",
          width: "8.5vw",
          key: "eventId",
          isEditable: false,
          field: {
            type: "text",
            placeholder: "eventId",
            name: "Event Id",
            validation: {
              required: true,
              errorMessage: "Please enter a valid PID",
            },
          },
        },
        status: {
          id: "status",
          label: "Status",
          width: "8.5vw",
          key: "status",
          isEditable: false,
        },
      },
    },
    userActions: CHILD_EVENT_USER_ACTIONS_CONFIG,
  },
  {
    label: "Offers",
    id: "offers",
    default: false,
    tabIndex: 1,
    key: "offers",
    isDisabled: false,
    isMultiCard: true,
    childDataKey: "allowances",
    gridDataSliceKey: "child_offers_details_data",
    gridConfig: {
      id: "abs-child-offers-view-table",
      checkboxes: true,
      itemKey: "allowanceIdNbr",
      noPagination: true,
      className: "",
      columns: {
        divisionId: {
          id: "divisionId",
          label: "Division",
          width: "8vw",
          key: "divisionId",
          isEditable: false,
          sortable: false,
        },
        allowanceAmount: {
          id: "allowanceAmount",
          label: "Allowance Amount",
          width: "4vw",
          key: "allowanceAmount",
          isEditable: false,
          sortable: false,
        },
        allowanceStartDate: {
          id: "allowanceStartDate",
          label: "Allowance Start Date",
          width: "3vw",
          key: "allowanceStartDate",
          isEditable: false,
          sortable: false,
        },
        allowanceEndDate: {
          id: "allowanceEndDate",
          label: "Allowance End Date",
          width: "3vw",
          key: "allowanceEndDate",
          isEditable: false,
          sortable: false,
        },
        orderStartDate: {
          id: "orderStartDate",
          label: "Order Start Date",
          width: "3vw",
          key: "orderStartDate",
          isEditable: false,
          sortable: false,
        },
        orderEndDate: {
          id: "orderEndDate",
          label: "Order End Date",
          width: "3vw",
          key: "orderEndDate",
          isEditable: false,
          sortable: false,
        },
        arrivalStartDate: {
          id: "arrivalStartDate",
          label: "Arrival Start Date",
          width: "3vw",
          key: "arrivalStartDate",
          isEditable: false,
          sortable: false,
        },
        arrivalEndDate: {
          id: "arrivalEndDate",
          label: "Arrival End Date",
          width: "3vw",
          key: "arrivalEndDate",
          isEditable: false,
          sortable: false,
        },
        shipStartDate: {
          id: "shipStartDate",
          label: "Ship Start Date",
          width: "3vw",
          key: "shipStartDate",
          isEditable: false,
          sortable: false,
        },
        shipEndDate: {
          id: "shipEndDate",
          label: "Ship End Date",
          width: "3vw",
          key: "shipEndDate",
          isEditable: false,
          sortable: false,
        },
        status: {
          id: "status",
          label: "Status",
          width: "8vw",
          key: "status",
          isEditable: false,
          sortable: false,
        },
      }
    },
  },
  // {
  //   id: "promotions",
  //   label: "Promotions",
  //   default: false,
  //   isDisabled: true,
  //   tabIndex: 2,
  //   key: "promotions",
  //   gridDataSliceKey: "event_details_data",
  // },
];
export const getSelectedTab = (tabNo = 0) =>
  CHILD_EVENTS_TABS?.find(tab => tab.tabIndex === tabNo);

export const CHILD_EVENTS_MOCK_DATA = {
  eventDetails: [
    {
      nationalEventId: "NationalEvent 123",
      events: [
        {
          rowIndex: 0,
          divisionId: 5,
          divisionName: "Denver",
          items: "Starbucks K-Cups",
          stores: "All Denver",
          startDate: "2022-01-04",
          endDate: "2022-01-20",
          pid: "3493445",
          eventId: "123",
          isSelected: true,
        },
        {
          rowIndex: 1,
          divisionId: 33,
          divisionName: "Shows",
          items: "Starbucks K-Cups",
          stores: "All Denver",
          startDate: "2022-01-04",
          endDate: "2022-01-20",
          pid: "3493445",
          eventId: "456",
          isSelected: true,
        },
      ],
    },
    {
      nationalEventId: "NationalEvent 456",
      events: [
        {
          rowIndex: 0,
          divisionId: 5,
          divisionName: "socal",
          items: "Hey K-Cups",
          stores: "All Denver",
          startDate: "2022-01-04",
          endDate: "2022-01-20",
          pid: "3493445",
          eventId: "235",
          isSelected: true,
        },
        {
          rowIndex: 1,
          divisionId: 33,
          divisionName: "ss",
          items: "NNNNN K-Cups",
          stores: "All Denver",
          startDate: "2022-01-04",
          endDate: "2022-01-20",
          pid: "3493445",
          eventId: "456",
          isSelected: true,
        },
      ],
    },
  ],
};
