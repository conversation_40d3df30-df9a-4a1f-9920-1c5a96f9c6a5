import { useState, useEffect, useRef, FunctionComponent } from "react";
import Divider from "@albertsons/uds/molecule/Divider";
import Popper from "@albertsons/uds/molecule/Popper";
import { useSelectorWrap } from "@me/data-rtk";
import {
  getEventCreationVehicleName,
  getEventDetailsDates,
  getEventName,
  getStoreGroups,
  getProductGroups,
  getSelectedStoreGroup,
  getStoreGroupsListFromOffers,
  checkForCaseAllowance,
} from "../../../service/event-details/event-detail-service";
import EventDetailStoreGroupHoverContent from "./event-details-store-groups-hover-content";
import EventDetailPromoProductHoverContent from "./event-details-promo-product-hover-content";
import {
  getQuadrantColors,
  periscopeUrl,
  roundToDecimalPlaces,
} from "@me/util-helpers";
import { RBAC } from "albertsons-react-rbac";
import { promotionTypeFieldPropsMock } from "../../fields/input-select-field-props-mocks";
import efConstants, {
  EEVENT_STATUS,
} from "../../../../../shared/ef-constants/ef-constants";
import ViewEventItemsCardContainer from "./view-event-items-card-container";
import EventDetailsVendorNamesHoverContent from "./event-details-vendor-details-hover-content";
import { getLoggedInUserType } from "@me-upp-js/utilities";
import "./event-details-card.scss";
import { usePostAllowanceToBeCreatedMutation } from "../../../service/apis/allowance-api";
import {
  formatTimestampToDate,
  getUniqueItems,
  setProductSourceError,
} from "../../../service/allowance/allowance-service";
import { allowanceProductSources } from "../../../service/slice/allowance-details-slice";
import { useDispatch } from "react-redux";
import {
  setIsProductSourcesEmptyHandler,
  setItemMinApiResp,
} from "../../../service/slice/event-detail-slice";
import { usePostPlanEventItemMinMutation } from "../../../service/apis/event-api";
import { eventTypes } from "../../../../event-types/constants/event-type-constant";
import { getIsNationalEvent } from "../../../../event-types/event-types-helper";
import { EVENT_TYPE } from '../../../constants/constants';
// eslint-disable-next-line @typescript-eslint/no-empty-interface

interface IEventDetailsPreviewProps {
  isApiLoading?: any;
  eventPreviewData?: any;
}

const EventDetailsPreview: FunctionComponent<IEventDetailsPreviewProps> = ({
  isApiLoading,
  eventPreviewData,
}) => {
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const {
    data: { apiErrorsMsgs: rtkErrorsArr, showTokenError = false },
  } = useSelectorWrap("apiError_rn");
  const [promoPopoverOpen, setPromoPopoverOpen] = useState<boolean>(false);
  const [storeGroupOpen, setStoreGroupOpen] = useState<boolean>(false);
  const [vendorNameOpen, setvendorNameOpen] = useState<boolean>(false);
  const [allowanceCount, setAllowanceCount] = useState(0);
  const [promoCount, setPromoCount] = useState(0);
  const promoProductGroupRef = useRef<HTMLDivElement>(null);
  const storeGroupRef = useRef<HTMLDivElement>(null);
  const vendorNameRef = useRef<HTMLDivElement>(null);
  const loggedInUser = getLoggedInUserType();
  const eventName = getEventName(eventDetailsData?.name);
  const [postEventDetailsData, { data: postEventData }] =
    usePostAllowanceToBeCreatedMutation();
  const [postPlanEventItemMin, { isLoading, data, isError }] =
    usePostPlanEventItemMinMutation();
  const dispatch = useDispatch();
  const isFirstRender = useRef(false);
  const promoProductGroups = getProductGroups(
    eventDetailsData?.planProductGroups
  );
  const eventVehicleTime = getEventDetailsDates(eventDetailsData);
  const eventCreationVehicle: any = getEventCreationVehicleName(
    eventDetailsData?.eventCreationVehicle || {}
  );
  const { isNDPAndFlagEnabled: isNational } = getIsNationalEvent(
    eventDetailsData?.eventType
  );
  const { PERISCOPE_ERRORS, EXCLUDED_OFFER_PROMO_STATUS } = efConstants;
  const [DP, AO, NDP, NAO] = eventTypes.map(event => event?.eventType);

  const PID_Message_Fields = ["DATES_NOT_MATCH", "ITEMS_NOT_MATCH"];
  const Invalid_PID_Message = ["INVALID_PID", "PID_NOT_VALIDATED"];

  const statusConfig = {
    Draft: {
      color: "#033B69",
    },
    Approved: {
      color: "#80662C",
    },
    Executed: {
      color: "#105F0E",
    },
    Proposed: {
      color: "#bababa",
    },
    Rejected: {
      color: "#fd5a53",
    },
    Done: {
      color: "#7adf67",
    },
    Cancelled: {
      color: "#384250",
    },
    Active: {
      color: "105F0E",
    },
    Agreed: {
      color: "0D2D49",
    },
  };

  const removeDuplicates = vendorsData => {
    const uniqueVendors = {};
    return vendorsData?.filter(item => {
      const isUnique = !uniqueVendors[item?.supplierId];
      uniqueVendors[item.supplierId] = true;
      return isUnique;
    });
  };

  const vendorNames = removeDuplicates(eventDetailsData?.simsVendorList);

  useEffect(() => {
    setAllowanceCount(eventDetailsData?.offerAllowances?.length);
    setPromoCount(eventDetailsData?.promotionsList?.length);
  }, [
    eventDetailsData?.offerAllowances?.length,
    eventDetailsData?.promotionsList?.length,
  ]);

  const getLargestPromoProductName = promoProductGroups => {
    const promoProdName = promoProductGroups
      ? promoProductGroups[0]?.productGroupName
      : "";
    if (promoProductGroups?.length >= 1) {
      const findPromoName = promoProductGroups?.reduce((prev, curr) => {
        return +prev?.itemCount > +curr?.itemCount ? prev : curr;
      });
      return findPromoName ? findPromoName?.productGroupName : promoProdName;
    }
  };

  const hasSinglePromotion = promotionsList => promotionsList?.length === 1;
  const hasMultiplePromotions = promotionsList => promotionsList?.length > 1;
  const getPromotionType = promoDetails => {
    const promotionType = promotionTypeFieldPropsMock?.["options"]?.find(item =>
      item?.id === promoDetails?.promotionType ? item?.name : ""
    );
    return promotionType?.name;
  };

  const getPromoTypeDetails = key => {
    const promotionsList = eventDetailsData?.promotionsList,
      regularPrice =
        promotionsList?.[0]?.promoDetails?.regularPrice?.toFixed(2);
    return hasSinglePromotion(promotionsList)
      ? key === "promoType"
        ? getPromotionType(promotionsList?.[0]?.promoDetails) || "Unavailable"
        : `$ ${regularPrice}`
      : hasMultiplePromotions(promotionsList)
      ? key === "promoType"
        ? "multiple"
        : `$ ${regularPrice}`
      : key === "promoType"
      ? "Unavailable"
      : "-";
  };

  const onPopperBlur = (ev: any, ref, cb) => {
    if (ev.relatedTarget !== ref.current?.firstChild) {
      cb(false);
    }
  };

  const periScopeDetailsData = eventDetailsData?.periscopeDetails;
  const { divisionIds = [], negotiationSimsVendors = [] } = {
    ...eventDetailsData,
  };
  const { quadrantBackGround, quadrantFont } = getQuadrantColors(
    eventDetailsData?.forecast?.quadrant
  );

  const appliedIcon = (
    <svg
      width="9"
      height="8"
      viewBox="0 0 9 8"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="flex-grow-0 flex-shrink-0 w-2 h-2"
      preserveAspectRatio="none"
    >
      <circle cx="4.33334" cy="4" r="3.5" fill="#5FBFA8" />
      <path
        d="M3.58334 5.0499L2.53334 3.9999L2.18334 4.3499L3.58334 5.7499L6.58334 2.7499L6.23334 2.3999L3.58334 5.0499Z"
        fill="white"
      />
    </svg>
  );
  const [isItemsCardOpen, setIsItemsCardOpen] = useState<boolean>(false);

  const notApplicable = (
    <svg
      width="9"
      height="8"
      viewBox="0 0 9 8"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="flex-grow-0 flex-shrink-0 w-2 h-2"
      preserveAspectRatio="none"
    >
      <circle cx="4.33334" cy="4" r="3.5" fill="#98A1BE" />
    </svg>
  );

  const warningIcon = (
    <svg
      width="16"
      height="16"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0.940166 23.998C4.62581 23.9988 8.31186 23.9984 11.9975 23.9984C14.4273 23.9984 16.857 23.9988 19.2868 23.9988C20.5567 23.9988 21.8267 24.0024 23.0967 23.9971C23.8253 23.9944 24.2204 23.267 23.8713 22.589C23.7387 22.3312 23.6107 22.0712 23.4806 21.8121C22.3615 19.5847 21.2427 17.3569 20.1235 15.13C18.8999 12.6956 17.6755 10.2616 16.4519 7.82723C15.235 5.40563 14.0193 2.9836 12.8011 0.562888C12.7493 0.46048 12.6872 0.358514 12.6109 0.276852C12.193 -0.170741 11.5183 -0.0630362 11.2283 0.509035C10.3113 2.31839 9.40248 4.13216 8.49163 5.9446C7.21006 8.49509 5.92933 11.046 4.64817 13.5969C3.13631 16.6074 1.6232 19.6169 0.113817 22.6291C0.0566555 22.7426 0.0135777 22.875 0.00363663 23.0021C-0.0423407 23.5822 0.351987 23.998 0.940166 23.998Z"
        fill="#9B3E08"
      ></path>
      <path d="M13 16H11V9H13V16Z" fill="#FFF"></path>
      <path
        d="M13 19.995C12.625 19.995 12.2602 19.995 11.8955 19.995C11.6406 19.995 11.3857 19.9943 11.1308 19.9953C11.0586 19.9956 10.9997 19.9933 11 19.8943C11.002 19.267 11.001 18.6393 11.001 18H12.9997V19.9946L13 19.995Z"
        fill="#FFF"
      ></path>
    </svg>
  );

  const getPeriscopeError = label => {
    return (
      <div className="flex">
        {warningIcon}
        <span className="ml-2 text-sm font-bold text-[#9B3E08]">{label}</span>
      </div>
    );
  };
  const checkIsItValid = PID_Message => {
    return PID_Message?.find(item =>
      periScopeDetailsData?.[0]?.periscopeMessages?.includes(item)
    );
  };

  const onCloseVendorPopOver = () => {
    setvendorNameOpen(false);
  };
  const hidePromoDetailsForNational = () => {
    return !isNational && eventDetailsData?.eventType !== EVENT_TYPE?.NCDP
  }

  const checkIsValidStoresAvailable = () => {
    return eventDetailsData?.offerAllowances?.filter(offerInfo => {
      const allowanceInfo = offerInfo?.allowances?.[0];
      const statusToCheck = isNational
        ? offerInfo?.allowances?.some(
            allow =>
              !EXCLUDED_OFFER_PROMO_STATUS.includes(allow?.allowanceStatus)
          )
        : !EXCLUDED_OFFER_PROMO_STATUS.includes(allowanceInfo?.allowanceStatus);
      return (
        statusToCheck &&
        !checkForCaseAllowance(offerInfo) &&
        allowanceInfo?.storeGroups?.length
      );
    })?.length;
  };

  const isValidOffersAndStoresAvailable = () =>
    eventDetailsData?.eventType === AO &&
    eventDetailsData?.offerAllowances?.length &&
    checkIsValidStoresAvailable();

  const navigateToPeriscopeWindow = e => {
    e.preventDefault();
    window.open(e.target.href, `periScopeWindowOpener`);
  };

  const { id: eventId = "", eventCreationVehicle: { startDate } = "" } =
    eventDetailsData;

  async function getProductSourcesInfo() {
    isFirstRender.current = true;
    await postEventDetailsData({
      URL_PARAM: eventId,
      queryParams: {
        responseFilter: "allowMin",
      },
      division: divisionIds,
      promoStartDate: formatTimestampToDate(startDate),
    });
  }

  useEffect(() => {
    const getProductGroupsData = async () => {
      dispatch(allowanceProductSources({ productSources: [] }));
      await getProductSourcesInfo();
    };
    if (
      !isApiLoading &&
      (eventPreviewData?.divisionIds?.length
        ? divisionIds?.length === eventPreviewData?.divisionIds?.length &&
          eventPreviewData?.divisionIds?.every(divId =>
            divisionIds?.includes(divId)
          )
        : true) &&
      eventId &&
      eventDetailsData?.eventCreationVehicle &&
      divisionIds?.length &&
      !isFirstRender.current
    ) {
      getProductGroupsData();
    }
  }, [eventId, startDate, divisionIds]);

  async function callPostPlanEventItemMin() {
    try {
      const payload = {
        URL_PARAM: eventId,
        division: divisionIds,
        promoStartDate: formatTimestampToDate(startDate),
      };
      const res = await postPlanEventItemMin(payload);
      dispatch(setItemMinApiResp({ res }));
    } catch (e) {
      console.log(e);
    }
  }

  useEffect(() => {
    if (postEventData && postEventData?.[0]?.productSources?.length === 0) {
      setProductSourceError(dispatch, rtkErrorsArr, showTokenError);
      callPostPlanEventItemMin();
      dispatch(
        setIsProductSourcesEmptyHandler({
          isProductSourcesEmpty: true,
        })
      );
    } else if (postEventData && postEventData?.length) {
      const productSources = getUniqueItems(postEventData, "productSources");
      dispatch(
        allowanceProductSources({
          productSources,
        })
      );
      dispatch(
        setIsProductSourcesEmptyHandler({
          isProductSourcesEmpty: false,
        })
      );
    }
  }, [postEventData]);
  const divIds = eventDetailsData?.divisionIds || [];
  const getStoreGroupDetails = () => {
    const storeGroups = [DP, NDP, EVENT_TYPE.NCDP].includes(
      eventDetailsData?.eventType
    )
      ? getStoreGroups(eventDetailsData?.storeGroups || [])
      : isValidOffersAndStoresAvailable()
      ? getStoreGroups(getStoreGroupsListFromOffers(eventDetailsData) || [])
      : [];
    const selectedGroup = getSelectedStoreGroup(storeGroups);

    return storeGroups?.length ? (
      <tr>
        <td className="text-sm font-bold text-left text-[#2b303c] pr-[20px]">
          Store Groups
        </td>
        <td>
          <div className="inline-flex">
            {eventDetailsData?.eventType === EVENT_TYPE.NDP &&
            divIds?.length > 1 ? (
              <>
                <p className="text-base text-[#2b303c] p-0 m-0">{`Divisions (`}</p>
                <span
                  className="self-center text-sm font-semibold text-left text-[#1b6ebb] p-0 m-0 cursor-pointer"
                  ref={storeGroupRef}
                  onClick={() => setStoreGroupOpen(true)}
                >
                  {storeGroups?.length > 1 && (
                    <p className="p-0 m-0">{divIds?.length}</p>
                  )}
                </span>
                {`)`}
              </>
            ) : (
              <span className="text-base text-[#2b303c] p-0 m-0">
                {`${
                  selectedGroup?.storeGroupName
                    ? `${selectedGroup?.storeGroupName} (${selectedGroup?.storeCount} Stores)`
                    : "Unavailable"
                }`}
              </span>
            )}
            {eventDetailsData?.eventType !== EVENT_TYPE.NDP &&
              storeGroups?.length > 1 && (
                <Divider
                  height={15}
                  className="inline-block mx-2"
                  color="#C8DAEB"
                />
              )}
            <span
              className="self-center text-sm font-semibold text-left text-[#1b6ebb] cursor-pointer"
              ref={storeGroupRef}
              onClick={() => setStoreGroupOpen(true)}
            >
              {eventDetailsData?.eventType !== EVENT_TYPE.NDP &&
                storeGroups?.length > 1 && <p>More</p>}
            </span>
            <Popper
              anchor={storeGroupRef}
              open={storeGroupOpen}
              onBlur={e => onPopperBlur(e, storeGroupRef, setStoreGroupOpen)}
              autoFocus={true}
              zIndex={10}
            >
              <EventDetailStoreGroupHoverContent
                storeGroups={storeGroups}
                popoverCloser={() => setStoreGroupOpen(false)}
              />
            </Popper>
          </div>
        </td>
      </tr>
    ) : null;
  };

  return (
    <>
      <div
        className={`flex gap-[22.5px] ${efConstants.componentClassName.EVENT_DETAILS_PREVIEW}`}
      >
        {/* <LoadingSpinner isLoading={isApiLoading} classname="!w-full !h-full" /> */}

        <div className="w-max relative pl-5 mb-5">
          {periScopeDetailsData?.length && checkIsItValid(Invalid_PID_Message)
            ? getPeriscopeError(
                PERISCOPE_ERRORS[checkIsItValid(Invalid_PID_Message)]
              )
            : checkIsItValid(PID_Message_Fields)
            ? getPeriscopeError(PERISCOPE_ERRORS?.ITEM_DATE_INVALID_MESSAGE)
            : null}

          <p className="flex-grow-0 flex-shrink-0 text-base font-bold text-left text-[#2b303c] whitespace-pre-wrap">
            {eventName}
          </p>

          <div className="flex justify-start items-start flex-grow-0 flex-shrink-0 gap-4">
            <div className="flex justify-start items-center flex-grow-0 flex-shrink-0  gap-1">
              {allowanceCount ? appliedIcon : notApplicable}
              <p className="flex-grow-0 flex-shrink-0 text-[11px] text-left text-[#98a1be]">
                {`Allowance ${allowanceCount ? `(${allowanceCount})` : ""}`}
              </p>
            </div>
            {eventDetailsData?.eventType !== AO && hidePromoDetailsForNational() ? (
              <div className="flex justify-start items-center flex-grow-0 flex-shrink-0  gap-1">
                {promoCount ? appliedIcon : notApplicable}
                <p className="flex-grow-0 flex-shrink-0 text-[11px] text-left text-[#98a1be]">
                  {`Promo ${promoCount ? `(${promoCount})` : ""}`}
                </p>
              </div>
            ) : null}
          </div>

          <table>
            <tbody className="w-max align-text-top">
              {periScopeDetailsData &&
              periScopeDetailsData?.[0]?.periscopeId ? (
                <tr>
                  <td>PID</td>
                  <td className="flex">
                    <div>
                      <a
                        className="text-[#1B6EBB] underline cursor-pointer"
                        onClick={e => navigateToPeriscopeWindow(e)}
                        href={periscopeUrl(
                          window["UPP_CONFIG"]?.["env"],
                          divIds?.[0],
                          periScopeDetailsData?.[0]?.periscopeId
                        )}
                        target={"_blank"}
                        rel="noreferrer"
                      >
                        {periScopeDetailsData[0]?.periscopeId}
                      </a>
                    </div>
                    {eventDetailsData?.eventStatus === EEVENT_STATUS.DRAFT ? (
                      <>
                        <Divider className="mx-3" height={24} color="#C8DAEB" />
                        <p
                          color={
                            statusConfig[periScopeDetailsData[0]?.workflowState]
                              ?.color
                          }
                        >
                          {periScopeDetailsData[0]?.workflowState}
                        </p>
                      </>
                    ) : null}
                  </td>
                </tr>
              ) : null}

              <tr>
                <td className="text-sm font-bold text-left text-[#2b303c] pr-[20px]">
                  Promo Product Groups
                </td>
                <td>
                  <div className="flex">
                    <span className="text-base text-[#2b303c] p-0 m-0">
                      {getLargestPromoProductName(promoProductGroups)}
                    </span>
                    {promoProductGroups?.length > 1 && (
                      <Divider
                        height={15}
                        className="inline-block mx-2"
                        color="#C8DAEB"
                      />
                    )}
                    <span
                      className="self-center text-sm font-semibold text-left text-[#1b6ebb] cursor-pointer"
                      ref={promoProductGroupRef}
                      onClick={() => setPromoPopoverOpen(true)}
                    >
                      {promoProductGroups?.length > 1 && <>More</>}
                    </span>

                    <Popper
                      anchor={promoProductGroupRef}
                      open={promoPopoverOpen}
                      onBlur={e =>
                        onPopperBlur(
                          e,
                          promoProductGroupRef,
                          setPromoPopoverOpen
                        )
                      }
                      autoFocus={true}
                      zIndex={10}
                    >
                      <EventDetailPromoProductHoverContent
                        popoverCloser={() => setPromoPopoverOpen(false)}
                        event={eventDetailsData}
                      />
                    </Popper>
                  </div>
                </td>
              </tr>
              {getStoreGroupDetails()}
              <tr>
                <td className="text-sm font-bold text-left text-[#2b303c] pr-[20px]">
                  Time Period
                </td>
                <td>
                  <span className="text-base text-[#2b303c] p-0 m-0">
                    {eventVehicleTime}
                  </span>
                  <Divider
                    height={15}
                    className="inline-block mx-2"
                    color="#C8DAEB"
                  />
                  <span className="text-base text-[#2b303c] p-0 m-0">
                    {eventCreationVehicle}
                  </span>
                </td>
              </tr>
              <tr>
                <td className="text-sm font-bold text-left text-[#2b303c] pr-[20px]">
                  Vendor
                </td>
                <td className="flex">
                  <span className="text-base text-[#2b303c] p-0 m-0">
                    {vendorNames?.length > 0
                      ? vendorNames?.[0]?.supplierName
                      : !vendorNames?.length
                      ? promoProductGroups?.[0]?.supplier
                      : "-"}
                  </span>
                  {vendorNames?.length > 1 ? (
                    <>
                      <Divider
                        height={15}
                        className="inline-block mx-2"
                        color="#C8DAEB"
                      />
                      <span
                        ref={vendorNameRef}
                        className="self-center text-sm font-semibold text-left text-[#1b6ebb] cursor-pointer"
                        onClick={() => setvendorNameOpen(true)}
                      >
                        More
                      </span>
                      <Popper
                        anchor={vendorNameRef}
                        open={vendorNameOpen}
                        onBlur={e =>
                          onPopperBlur(e, vendorNameRef, setvendorNameOpen)
                        }
                        autoFocus={true}
                        zIndex={10}
                      >
                        <EventDetailsVendorNamesHoverContent
                          vendorNames={vendorNames}
                          popoverCloser={onCloseVendorPopOver}
                        />
                      </Popper>
                    </>
                  ) : null}
                </td>
              </tr>
              {eventDetailsData?.eventType !== AO && hidePromoDetailsForNational() ? (
                <tr>
                  <td className="text-sm font-bold text-left text-[#2b303c] pr-[20px]">
                    Promo Type
                  </td>
                  <td>
                    <span className="text-base text-[#2b303c] p-0 m-0">
                      {getPromoTypeDetails("promoType")}
                    </span>
                  </td>
                </tr>
              ) : null}
              {eventDetailsData?.eventType !== AO && hidePromoDetailsForNational() ? (
                <tr>
                  <td className="text-sm font-bold text-left text-[#2b303c] pr-[20px]">
                    Regular Price
                  </td>
                  <td>
                    <span className="text-base text-[#2b303c] p-0 m-0">
                      {getPromoTypeDetails("regularPrice")}
                    </span>
                  </td>
                </tr>
              ) : null}
            </tbody>
          </table>
        </div>

        {efConstants.FORECAST_DISPLAY[loggedInUser] ? (
          <div className="flex flex-col flex-grow-0 flex-shrink-0 items-center ml-auto gap-1">
            <RBAC
              divisionIds={divisionIds}
              permissionsOnly={["PROMOTION_PROMO_MGMT_EDIT_GET_FORECAST"]}
              simsVendors={negotiationSimsVendors}
            >
              <div className="flex-grow-0 flex-shrink-0 w-[202px] h-[68px] bg-[#ebf3fa]">
                {/* <div className="w-[202px] h-[68px]  left-[-0.5px] top-[-0.5px] bg-[#ebf3fa]"></div> */}
                <div className="m-auto h-auto">
                  <p className="w-[201px] pt-[10px] left-0 top-2.5 text-[13px] text-center text-[#2b303c]">
                    Forecasted Sales
                  </p>
                  <div className="h-[24px] text-center">
                    <span className="text-[18px] align-middle leading-6 font-extrabold text-left text-[#2b303c]">
                      $
                      {roundToDecimalPlaces(
                        eventDetailsData?.forecast?.forecastSales,
                        2
                      )}
                    </span>
                  </div>
                </div>
              </div>
            </RBAC>
            <RBAC
              divisionIds={divisionIds}
              permissionsOnly={["PROMOTION_EVENT_DETAILS_VIEW_UNITS"]}
              simsVendors={negotiationSimsVendors}
            >
              <div className="flex-grow-0 flex-shrink-0 w-[202px] h-[68px] bg-[#ebf3fa]">
                <div className="m-auto h-auto">
                  <p className="w-[201px] pt-[10px] text-[13px] text-center text-[#2b303c]">
                    Forecasted Units
                  </p>
                  <p className="text-lg font-bold text-center text-[#2b303c]">
                    {roundToDecimalPlaces(
                      eventDetailsData?.forecast?.forecastUnits,
                      0
                    )}
                  </p>
                </div>
              </div>
            </RBAC>
            <RBAC
              divisionIds={divisionIds}
              // permissionsOnly={["PROMOTION_EVENT_DETAILS_VIEW_QUADRANT"]}
              permissionsOnly={[""]}
              simsVendors={negotiationSimsVendors}
            >
              <div className="flex-grow-0 flex-shrink-0 w-[201px] h-[34px] bg-[#ebf3fa] text-[11px] leading-3 font-extrabold uppercase flex justify-center items-center">
                <span
                  className="rounded-sm px-[5px] py-[1.5px]"
                  style={{
                    backgroundColor: quadrantBackGround,
                    color: quadrantFont,
                  }}
                >
                  {eventDetailsData?.forecast?.quadrant || "-"}
                </span>
              </div>
            </RBAC>
          </div>
        ) : null}
      </div>
      {/*<ViewEventItemsCardContainer/>*/}
      <div
        className={`overflow-auto ${
          eventDetailsData?.eventType === AO ? "mt-[19px]" : ""
        }`}
      >
        <ViewEventItemsCardContainer
          isItemsCardOpen={isItemsCardOpen}
          setIsItemsCardOpen={setIsItemsCardOpen}
          promoProductGroups={promoProductGroups}
        />
      </div>
    </>
  );
};

export default EventDetailsPreview;
