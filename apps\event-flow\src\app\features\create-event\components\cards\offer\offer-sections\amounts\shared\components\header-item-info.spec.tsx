import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import HeaderItemInfo from './header-item-info';

describe('HeaderItemInfo Component', () => {
  const defaultProps = {
    itemCount: 10,
    totalAmount: 5000,
    wareHouseCount: 3,
    hasItemCount: true,
    isHfIfWhseCase: false,
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('renders Item Count and Total Allowance Amount by default', () => {
    const { container } = render(<HeaderItemInfo {...defaultProps} />);
    const dividers = container.querySelectorAll('#abs-allowance-amounts-services-dashed-vertical-divider');
    // Verify dashedVerticalDivider is rendered twice
    expect(dividers?.length).toBe(1);
  });

  test('renders Warehouse Count when isHfIfWhseCase is true', () => {
    const { container } = render(<HeaderItemInfo {...defaultProps} isHfIfWhseCase={true} />);
    const dividers = container.querySelectorAll('#abs-allowance-amounts-services-dashed-vertical-divider');
    // Verify dashedVerticalDivider is rendered twice
    expect(dividers?.length).toBe(2);
    expect(screen.getByText('Warehouse Count')).toBeInTheDocument();
  });

  test('does not render Total Allowance Amount when hasItemCount is false', () => {
    render(<HeaderItemInfo {...defaultProps} hasItemCount={false} />);

    expect(screen.getByText('Item Count')).toBeInTheDocument();
    expect(screen.getByText('10')).toBeInTheDocument();
    expect(screen.queryByText('Total Allowance Amount')).not.toBeInTheDocument();

    // Verify dashedVerticalDivider is not rendered
    expect(screen.queryByTestId('divider')).not.toBeInTheDocument();
  });
});
