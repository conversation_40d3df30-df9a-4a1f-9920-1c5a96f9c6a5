import { render, screen } from "@testing-library/react";
import SearchBox from "../SearchBox"; // Adjust path as necessary
import "@testing-library/jest-dom";

describe("SearchBox", () => {
  const mockFilterListBySearch = jest.fn();
  const mockSetSearchedText = jest.fn();
  const searchPlaceholder = "Search by name...";
  const searchedText = "Test";

  afterEach(() => {
    mockFilterListBySearch.mockClear();
    mockSetSearchedText.mockClear();
  });

  it("renders with the correct placeholder and value", () => {
    render(
      <SearchBox
        filterListBySearch={mockFilterListBySearch}
        searchedText={searchedText}
        setSearchedText={mockSetSearchedText}
        searchPlaceholder={searchPlaceholder}
      />
    );

    const inputElement = screen.getByPlaceholderText(searchPlaceholder);
    expect(inputElement).toBeInTheDocument();
    expect(inputElement).toHaveValue(searchedText);
  });

  it("does not call filterListBySearch on the first render", () => {
    render(
      <SearchBox
        filterListBySearch={mockFilterListBySearch}
        searchedText={searchedText}
        setSearchedText={mockSetSearchedText}
        searchPlaceholder={searchPlaceholder}
      />
    );

    expect(mockFilterListBySearch).not.toHaveBeenCalled();
  });

  it("renders the SearchIcon component", () => {
    render(
      <SearchBox
        filterListBySearch={mockFilterListBySearch}
        searchedText={searchedText}
        setSearchedText={mockSetSearchedText}
        searchPlaceholder={searchPlaceholder}
      />
    );

    const searchIcon = screen.getByRole("button");
    expect(searchIcon).toBeInTheDocument();
  });
});
