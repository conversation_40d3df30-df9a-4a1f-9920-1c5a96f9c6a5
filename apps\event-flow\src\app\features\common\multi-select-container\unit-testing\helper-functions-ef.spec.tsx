import { searchData, selectDeselectAll } from "../../helper-functions-ef"; // adjust path as needed

describe("searchData", () => {
  const items = [
    { id: 1, name: "<PERSON>" },
    { id: 2, name: "<PERSON><PERSON>" },
    { id: 3, name: "<PERSON>" },
    { id: 4, name: "apple pie" },
  ];

  it("should return filtered items that match the search text (case-insensitive)", () => {
    const result = searchData(items, "name", "apple");
    expect(result).toEqual([
      { id: 1, name: "Apple" },
      { id: 4, name: "apple pie" },
    ]);
  });

  it("should return all items if search text is empty", () => {
    const result = searchData(items, "name", "");
    expect(result).toEqual(items);
  });

  it("should return an empty array if no items match the search text", () => {
    const result = searchData(items, "name", "orange");
    expect(result).toEqual([]);
  });

  it("should handle missing key gracefully (undefined value)", () => {
    const invalidItems = [
      { id: 1, title: "One" },
      { id: 2, title: "Two" },
    ];
    const result = searchData(invalidItems, "name", "One");
    expect(result).toEqual([]);
  });

  it("should handle mixed item structures without error", () => {
    const mixedItems = [
      { name: "Apple" },
      { title: "Banana" }, // no 'name' key
      { name: "Cherry" },
    ];
    const result = searchData(mixedItems, "name", "a");
    expect(result).toEqual([{ name: "Apple" }]);
  });
});

describe("selectDeselectAll", () => {
  const original = [
    { id: 1, name: "One" },
    { id: 2, name: "Two" },
    { id: 3, name: "Three" },
  ];

  it("should deselect all if everything is already selected", () => {
    const selected = [...original];
    const result = selectDeselectAll(original, selected, "id");
    expect(result).toEqual({
      selectedItemIds: [],
      selectedItems: [],
    });
  });

  it("should select all if not everything is selected", () => {
    const selected = [{ id: 1, name: "One" }];
    const result = selectDeselectAll(original, selected, "id");
    expect(result).toEqual({
      selectedItemIds: [1, 2, 3],
      selectedItems: original,
    });
  });

  it("should handle empty original data gracefully", () => {
    const result = selectDeselectAll([], [], "id");
    expect(result).toEqual({
      selectedItemIds: [],
      selectedItems: [],
    });
  });

  it("should handle empty selected data", () => {
    const result = selectDeselectAll(original, [], "id");
    expect(result).toEqual({
      selectedItemIds: [1, 2, 3],
      selectedItems: original,
    });
  });

  it("should handle missing uniqueId field gracefully", () => {
    const data = [
      { uid: 1, label: "A" },
      { uid: 2, label: "B" },
    ];
    const result = selectDeselectAll(data, [], "id"); // 'id' doesn't exist
    expect(result).toEqual({
      selectedItemIds: [undefined, undefined],
      selectedItems: data,
    });
  });
});
