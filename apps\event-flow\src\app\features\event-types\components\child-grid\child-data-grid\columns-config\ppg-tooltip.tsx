import Tooltip from "@albertsons/uds/molecule/Tooltip";
import { AlertCircle, CheckCircle2 } from "lucide-react";
import { checkForPPGItems } from "../../service/child-events-service";

const TooltipComponent = ({ eventDetails, itemsData }) => {
  const toolTipData = {
    circle: {
      Component: <CheckCircle2 size={16} className="text-green-500 ml-1" />,
      text: `All items are available in this Division`,
    },
    alert: {
      Component: <AlertCircle size={16} className="text-amber-500 ml-1" />,
      text: `${eventDetails?.items?.length || 0}/${itemsData?.length || 0
        } items are available in this Division`,
    },
  };
  const isAllitemsPresent =
    itemsData?.length && checkForPPGItems(itemsData, eventDetails?.items)
      ? "circle"
      : "alert";

  const TooltipPopup = ({ tooltip, children }) => {
    return (
      <Tooltip zIndex={16} anchor="bottom" variant="light">
        <Tooltip.Popover>
          <span className="flex-col m-1 text-sm font-bold">{tooltip}</span>
        </Tooltip.Popover>
        {children}
      </Tooltip>
    );
  };

  return (
    <>
      <TooltipPopup tooltip={toolTipData[isAllitemsPresent].text}>
        {toolTipData[isAllitemsPresent].Component}
      </TooltipPopup>
    </>
  );
};
export default TooltipComponent;
