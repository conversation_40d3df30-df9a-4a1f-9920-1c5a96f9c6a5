import Button from "@albertsons/uds/molecule/Button";
import { useSelectorWrap } from "@me/data-rtk";
import { InputText } from "../../../../fields/allowance-atoms";
import { RenderStates } from "@me/ui-render-states";
import { EEVENT_STATUS, editFieldHighlight } from "@me/util-helpers";
import { isEmpty } from "lodash";
import { Fragment, useCallback, useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import _ from "lodash";
import classNames from "classnames";
import { appConstants } from "@me/utils-root-props";
import {
  checkIsPaymentTypeDeduct,
  getAllowanceFormRegisterKey,
  getAllowanceKey,
  getAllowanceMapKey,
  removeParams,
  saveAllowanceFormData,
} from "../../../../../service/allowance/allowance-service";
import {
  usePostNationalOfferAllowanceMutation,
  useDeleteNationalAllowanceTempWorkDataMutation,
  usePutNationalOfferUpdateAllowanceMutation,
} from "../../../../../service/apis/allowance-api";
import AllowanceFormWrapper, {
  IFormControls,
} from "../../../common/allowance-form-wrapper";
import {
  allowanceNewCardConfiguration,
  allowanceOfferDetailsConfiguration,
  offerCardConfiguration,
  resetAllowanceStepSkipInfo,
  resetIsOfferSectionUpdated,
  resetOfferAmountsData,
  resetOfferSectionsData,
  resetOfferSectionsEnableConfig,
  setAllowanceFormInfo,
} from "../../../../../service/slice/allowance-details-slice";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";
import { allowanceTempWorkReset } from "../../../../../service/slice/allowance-temp-work-slice";
import { setAllowancesResponse } from "../../../../../service/slice/allowances-response-slice";
import { VENDOR_COMMENT_CHAR_LIMIT } from "../../../../../constants/fields/allowance/field-billing-information";
import LoadingSpinner from "../../../../../constants/LoadingSpinner/LoadingSpinner";
import { OFFER_ALLOWANCE } from "../../offer-flow-config";
import { getOfferMapKey } from "../../offer-service";
import useGetOfferSectionConfiguration from "../../hooks/useGetOfferSectionConfiguration";
import { useFormContext } from "react-hook-form";
import { ChevronDown } from "lucide-react";
import { VendorInfo } from "./vendor-info";
import { PowerLineInfo } from "./power-line";
import BillingInfoForAllowance from "./billing-info-allowance";
import AccordionAtom from "./accordion-container";
import useNationalAllowTempUpdate from "../../../../../hooks/useNationalAllowTempUpdate";
import { billingInformationMessages } from "../../../../../../../shared/ef-constants/ef-constants";
import {
  resetDivisionWiseShrdWhseData,
  resetNationalDivisionsConfig,
} from "../../../../../../all-allowances/nationals/slices/national-main-entry-slices";

type IBillingInformationProps = {
  sectionKey;
  cardIndex;
  cardItemIndex;
  isEditEnable;
  isLastStep;
};

export default function CommonBillingInformation({
  sectionKey,
  cardIndex = 0,
  cardItemIndex = 0,
  isLastStep,
  isEditEnable,
}: IBillingInformationProps) {
  const sectionConfiguration = OFFER_ALLOWANCE?.[sectionKey] || {};
  const {
    fields: {
      billingInformationData,
      suggestedVendorPaymentType,
      acApOrArNumber,
      vendorComment,
      vendorOfferTrackingNbr,
      commonComment,
      commonSuggestedVendorPaymentType,
      commonAcApOrArNumber,
    },
  } = sectionConfiguration;
  const formContext = useFormContext();
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const dispatch = useDispatch();
  const {
    data: { offerDivisions = [] },
  } = useSelectorWrap("national_offer_divisions") || {};
  const {
    data: { allowanceData: allowanceTempWorkData },
  } = useSelectorWrap("allowance_temp_work");
  const { editCardConfig = {}, openCardConfig = {} } =
    useSelectorWrap("offer_card_configutation_rn")?.data || {};
  const { isAdditionalDatesChanged = {} } =
    useSelectorWrap("offer_amounts_details")?.data || {};
  const offerSectionsEnableConfig =
    useSelectorWrap("offer_sections_enable_config")?.data || {};
  const {
    isOfferSectionUpdated = false,
    isPrimeSectionSectionUpdated = false,
    isAmountsSectionUpdated = false,
  } = useSelectorWrap("offer_section_update")?.data || {};
  const allowanceRegField = getAllowanceFormRegisterKey(
    cardIndex,
    cardItemIndex
  );
  const {
    addNationalAllowancesBillingInformation,
    isLoading: isBillingLoading,
  } = useNationalAllowTempUpdate();

  const allowFormData = useSelectorWrap("allowance_form_data")?.data || {};
  const divisions = offerDivisions?.map(item => {
    return {
      id: item,
      divisionId: item,
      name: `${appConstants?.DIVISIONS_ID_NAME_MAP?.[item]}`,
      divisionName: `${appConstants?.DIVISIONS_ID_NAME_MAP?.[item]}`,
      checked: true,
    };
  });

  const [divisionsData, setDivisionsData] = useState(divisions);
  const [isError, setIsError] = useState(false);
  const setDivisionsDataCallback = divisionsData => {
    setDivisionsData(prev => [...prev, ...divisionsData]);
  };

  useCallback(() => {
    setDivisionsDataCallback(divisionsData);
  }, [divisionsData]);

  const allowanceFormData =
      allowFormData?.allowanceFormData?.[allowanceRegField] || {},
    { allowanceType, createInd, performance } =
      allowanceFormData?.allowancePrimeData || {},
    allowanceTypeKey = getAllowanceKey(allowanceType?.toUpperCase()),
    mapperKey = getOfferMapKey(createInd, sectionConfiguration?.offerTypeKey),
    billingRegFieldKey = billingInformationData?.registerKeyName,
    billingInitialValueOnFormLoad =
      allowanceFormData?.[billingRegFieldKey]?.[mapperKey],
    billingInitialValueOnLoad = allowanceTempWorkData?.map(item => {
      const divisionId = item?.divisionId;
      const allowanceValues =
        item?.allowanceTypeSpecification?.[
          getAllowanceMapKey(allowanceTypeKey) || ""
        ]?.allowancesMap?.[mapperKey]?.map(value => ({
          ...value,
          divisionId: item?.divisionId,
        })) || [];

      return { [divisionId]: allowanceValues.map(value => ({ ...value })) };
    }),
    searchId = eventDetailsData?.offerAllowances?.[cardIndex]?.offerNumber,
    eventStatus = eventDetailsData?.eventStatus,
    allowanceStatus =
      eventDetailsData?.offerAllowances?.[cardIndex]?.allowances[cardItemIndex]
        ?.allowanceStatus;

  const defaultValues = allowanceTempWorkData?.map(item => {
    const allowanceValues =
      item?.allowanceTypeSpecification?.[
        getAllowanceMapKey(allowanceTypeKey) || ""
      ]?.allowancesMap?.[mapperKey]?.map(value => ({
        ...value,
        divisionId: item?.divisionId,
      })) || [];

    return allowanceValues.map(value => {
      return {
        acApOrArNumber:
          value?.allowanceBillingInfo?.suggestedVendorPaymentType === "Deduct"
            ? value?.allowanceBillingInfo?.suggestedAcPayableVendorNbr
            : value?.allowanceBillingInfo?.suggestedAcReceivableVendorNbr,
        vendorComment: value?.allowanceBillingInfo?.vendorComment,
        suggestedVendorPaymentType:
          value?.allowanceBillingInfo?.suggestedVendorPaymentType,
      };
    });
  });

  const [
    putNationalOfferAllowance,
    { isLoading: isNationalAllowanceUpdateLoading },
  ] = usePutNationalOfferUpdateAllowanceMutation();

  const [
    postNationalOfferAllowance,
    { isLoading: isNationalAllowancePostLoading },
  ] = usePostNationalOfferAllowanceMutation();
  const isCancelled = useRef(false);

  const [
    deleteNationalAllowanceTempWorkData,
    { isLoading: isDeleteNationalTempworkDataLoading },
  ] = useDeleteNationalAllowanceTempWorkDataMutation();

  const { moveToNextSectionOnCreate, moveToNextSectionOnUpdate } =
    useGetOfferSectionConfiguration({
      allowanceRegField,
    });

  const isCommentRequired = performance === "Other (99)";

  const [formControls, setFormControls] = useState<IFormControls>();
  const [errorData, setErrorData] = useState<any>([]);
  const [vendorTrackingNumberChanged, setVendorTrackingNumberChanged] =
    useState(false);
  const defaultTrackNumber = useMemo(() => {
    let trackingNumber = "";

    billingInitialValueOnFormLoad?.allowanceBillingInfo?.forEach(item => {
      if (item?.vendorOfferTrackingNbr) {
        trackingNumber = item?.vendorOfferTrackingNbr;
      }
    });

    return trackingNumber;
  }, [billingInitialValueOnFormLoad]);
  const [openExpand, setOpenExpand] = useState(false);
  const [isExpand, setIsExpand] = useState(false);
  const isShowCommentField =
    (allowanceType?.toUpperCase() === "CASE" &&
      mapperKey === "WAREHOUSE_DIST_CENTERS") ||
    mapperKey === "DSD_LEAD_DISTRIBUTORS";

  const {
    register = () => null,
    control,
    setValue = () => null,
    getValues = (key: string) => null,
    setError = () => null,
    clearErrors = () => null,
    formState,
    reset = () => null,
    trigger = () => null,
  } = formControls || {};

  const getEventData = () => {
    return eventDetailsData;
  };

  function getRegKeyForBilling(field, divisionIndex, allowIndex) {
    return `allowanceBillingInfo.[${divisionIndex}].[${allowIndex}].${field?.registerField}`;
  }

  const getHighletedClassName = (fieldProps, fieldChanged: boolean) => {
    return isEditEnable && fieldChanged
      ? "border-2 border-blue-500 "
      : editFieldHighlight(
          fieldProps.mapperKey,
          getEventData,
          searchId,
          "allowance"
        );
  };

  const handleCancel = () => {
    isCancelled.current = true;
    eventDetailsData?.id &&
      deleteNationalAllowanceTempWorkData({
        URL_PARAM: [eventDetailsData?.id],
      });
    if (eventDetailsData?.inValidAllowances) {
      dispatch(
        offerCardConfiguration({
          ...editCardConfig,
          ...openCardConfig,
          offerData: `${
            eventDetailsData?.offerAllowances?.[cardIndex]?.id
          }_${_.uniqueId()}`,
        })
      );
    }
    changeStoreDataAfterUpdate(null);
    dispatch(resetOfferAmountsData());
    dispatch(resetIsOfferSectionUpdated());
  };

  const cancelButton = isEditEnable && (
    <Button onClick={handleCancel} width={140} variant="secondary">
      Cancel
    </Button>
  );

  const getSubmitBtnLabel = () => {
    if (isEditEnable) return efConstants.UPDATE_ALLOWANCE;
    else if (!isEditEnable && isLastStep) return efConstants.CREATE_ALLOWANCE;
    return sectionConfiguration.create.label;
  };

  const getOfferStatus = () => {
    if (eventStatus === EEVENT_STATUS.CANCELED) return true;
    else if (isEditEnable) {
      const isInValidOffer = eventDetailsData?.inValidAllowances?.includes(
        eventDetailsData?.offerAllowances?.[cardIndex]?.id
      );
      if (isInValidOffer) {
        return !isPrimeSectionSectionUpdated;
      }
      return allowanceStatus === EEVENT_STATUS.CANCELED || isOfferSectionUpdated
        ? false
        : !formState?.isDirty;
    }
    return false;
  };

  const getFormControls = (controls: IFormControls) => {
    setFormControls(controls);
  };

  const changeStoreDataAfterUpdate = data => {
    dispatch(allowanceTempWorkReset());
    dispatch(
      offerCardConfiguration({
        editCardConfig: {},
        openCardConfig: {},
      })
    );
    dispatch(resetAllowanceStepSkipInfo());
    data && dispatch(setAllowancesResponse(data));
    dispatch(
      setAllowanceFormInfo({
        allowanceFormData: {
          [cardIndex]: { cancel: true },
        },
      })
    );
    dispatch(resetOfferSectionsData());
    dispatch(resetOfferAmountsData());
    dispatch(resetNationalDivisionsConfig());
    dispatch(resetIsOfferSectionUpdated());
    dispatch(resetOfferSectionsEnableConfig());
    dispatch(resetDivisionWiseShrdWhseData());
  };

  const handleUpdateOrCreateOffer = async (data: object = {}) => {
    if (isEditEnable) {
      const putOfferData = await putNationalOfferAllowance({
        URL_PARAMS: [searchId],
      });
      putOfferData?.data && changeStoreDataAfterUpdate(putOfferData?.data);
    } else {
      const postOfferData = await postNationalOfferAllowance({
        URL_PARAM: [eventDetailsData?.id],
      });
      postOfferData?.data && handleDataAfterCreate(postOfferData?.data);
    }
    //remove temp work id from session storage after update or save
    sessionStorage.removeItem("TEMPWORK_ID");
    sessionStorage.removeItem("OFFER_ID");
  };

  const handleDataAfterCreate = postOfferData => {
    dispatch(
      allowanceNewCardConfiguration({
        isNewAllowance: true,
        stepperType: "offerAllowances",
        isAddAnotherOffer: false,
      })
    );
    changeStoreDataAfterUpdate(postOfferData);
    dispatch(allowanceOfferDetailsConfiguration(postOfferData));
  };

  const handleSave = async formValues => {
    if (!isCancelled.current) {
      const { ...restFormValues } = formValues;
      let allowances = billingInitialValueOnLoad;

      let invalidFormData = false;
      const newErrors: any[] = [];
      const groupedErrors: Record<number, any[]> = {};
      const result = billingInitialValueOnLoad
        .flatMap(obj => Object.values(obj))
        .flat();
      const formAllowData = restFormValues?.allowanceBillingInfo?.flat();

      allowances = result?.map((allowance, index) => {
        const formAllow = formAllowData?.[index] || {};
        const isDeduction = formAllow?.suggestedVendorPaymentType === "Deduct";

        formAllow.suggestedVendorPaymentType =
          formAllow?.suggestedVendorPaymentType === "Select"
            ? ""
            : formAllow?.suggestedVendorPaymentType;

        const error: any = newErrors[index] || {};

        if (isCommentRequired && !formAllow?.vendorComment) {
          error.vendorComment = "Vendor Comment is required";
        }

        if (
          Boolean(formAllow?.suggestedVendorPaymentType) !==
          Boolean(formAllow?.acApOrArNumber)
        ) {
          if (formAllow?.suggestedVendorPaymentType) {
            error.acApOrArNumber = "Suggested A/P or A/R Number is required";
          } else {
            error.suggestedVendorPaymentType =
              "Suggested Payment Type is required";
          }
        }

        invalidFormData = invalidFormData || !isEmpty(error);
        newErrors.push(error);
        if (!groupedErrors[allowance?.divisionId]) {
          groupedErrors[allowance?.divisionId] = [];
        }
        groupedErrors[allowance?.divisionId].push(error);
        return {
          ...allowance,
          allowanceBillingInfo: {
            ...allowance?.["allowanceBillingInfo"],
            absVendorPaymentType: checkIsPaymentTypeDeduct(
              performance,
              allowance
            )
              ? "Deduct"
              : "Invoice",
            suggestedVendorPaymentType:
              formAllow?.suggestedVendorPaymentType || "",
            vendorComment: formAllow?.vendorComment || "",
            vendorOfferTrackingNbr: formAllow?.vendorOfferTrackingNbr || "",
            suggestedAcPayableVendorNbr:
              (isDeduction ? formAllow?.acApOrArNumber : "") || "",
            suggestedAcReceivableVendorNbr:
              (!isDeduction ? formAllow?.acApOrArNumber : "") || "",
          },
        };
      });

      setErrorData(groupedErrors);
      if (invalidFormData) return;
      if (isLastStep) {
        const results = await addNationalAllowancesBillingInformation({
          allowances,
          offerAllowanceGroup: mapperKey,
          isLastStep,
        });
        formContext?.setValue("isAllowanceChanged", false);
        await handleUpdateOrCreateOffer(results);
        removeParams();
        localStorage.removeItem(
          `${appConstants.RESTRICT_NAV_STORAGE_NAME}${sessionStorage.getItem(
            appConstants.SESSION_STORAGE_RESTRICTED_APP_KEY_NAME
          )}`
        );
      } else {
        const results = await addNationalAllowancesBillingInformation({
          allowances,
          offerAllowanceGroup: mapperKey,
          isLastStep,
        });
        if (results?.data?.length) {
          const updatedFormValues = {
            ...allowanceFormData,
            [billingRegFieldKey]: {
              ...allowanceFormData?.[billingRegFieldKey],
              [mapperKey]: {
                ...restFormValues,
              },
            },
          };
          isEditEnable &&
            saveAllowanceFormData({
              dispatch,
              key: billingRegFieldKey,
              allowanceRegField,
              value: {
                ...allowanceFormData?.[billingRegFieldKey],
                [mapperKey]: {
                  ...restFormValues,
                  //  changedFields.length !== 0,
                },
              },
              isPreviousDataRequired: true,
              previousData: allowanceFormData,
            });
          !isEditEnable
            ? moveToNextSectionOnCreate(
                sectionKey,
                mapperKey,
                updatedFormValues
              )
            : moveToNextSectionOnUpdate(sectionKey);
          reset?.(undefined, { keepValues: true });
        }
      }
      analyticsLinkClickEvent();
    }
  };

  const analyticsLinkClickEvent = () => {
    if (
      window["AB"] &&
      window["AB"]["DATALAYER"] &&
      typeof window["AB"]["DATALAYER"].setLinkClickEvents === "function" &&
      getSubmitBtnLabel() === efConstants.CREATE_ALLOWANCE
    ) {
      window["AB"]["DATALAYER"].setLinkClickEvents("promotions:create-offer");
    }
  };

  function onUpdateAllCommentSubmit(e) {
    if (
      getValues(commonComment.registerField)?.trimStart().length >
      VENDOR_COMMENT_CHAR_LIMIT
    ) {
      setError(commonComment.registerField, {
        message: `Maximum ${VENDOR_COMMENT_CHAR_LIMIT} characters allowed`,
      });
      setIsError(true);
      return;
    } else {
      clearErrors(commonComment.registerField);
      setIsError(false);
    }

    const selectedDivisionIds = divisionsData
      ?.filter(item => item.checked)
      .map(item => item.id);

    billingInitialValueOnLoad?.forEach((element, outerIndex) => {
      const updatedCommentValue = getValues(commonComment.registerField);
      const updatedAcApOrArNumber = getValues(
        commonAcApOrArNumber.registerField
      );
      Object.keys(element).forEach(key => {
        if (selectedDivisionIds?.includes(key)) {
          element[key]?.forEach((item, innerIndex) => {
            if (item?.includeInd) {
              setValue(
                getRegKeyForBilling(vendorComment, outerIndex, innerIndex),
                updatedCommentValue || ""
              );
              const updatedSuggestedVendorPaymentType = getValues(
                commonSuggestedVendorPaymentType.registerField
              )?.name;
              if (updatedSuggestedVendorPaymentType) {
                setValue(
                  getRegKeyForBilling(
                    suggestedVendorPaymentType,
                    outerIndex,
                    innerIndex
                  ),
                  updatedSuggestedVendorPaymentType
                );
              }
              setValue(
                getRegKeyForBilling(acApOrArNumber, outerIndex, innerIndex),
                updatedAcApOrArNumber
              );
            }
          });
        }
      });
    });
    trigger?.();
  }

  function onPaymentTypeChange(e, divisionIndex, cardItemIndex: number) {
    updateAllRelatedAllowances(billingInitialValueOnLoad, index => {
      setValue(
        getRegKeyForBilling(
          suggestedVendorPaymentType,
          divisionIndex,
          cardItemIndex
        ),
        e.id
      );
    });
    trigger?.();
  }

  function onApArNumberChange(e, divisionIndex, cardItemIndex: number) {
    updateAllRelatedAllowances(billingInitialValueOnLoad, index => {
      setValue(
        getRegKeyForBilling(acApOrArNumber, divisionIndex, cardItemIndex),
        e
      );
    });
    trigger?.();
  }

  function onVendorCommentChange(e, divisionIndex, cardItemIndex: number) {
    updateAllRelatedAllowances(billingInitialValueOnLoad, index => {
      setValue(
        getRegKeyForBilling(vendorComment, divisionIndex, cardItemIndex),
        e
      );
    });
    trigger?.();
  }

  function onVendorTrackingNumberChange(e) {
    const value = e?.trim();

    billingInitialValueOnLoad?.forEach((divisionObj, outerIndex) => {
      Object.values(divisionObj).forEach((allowanceArray: any) => {
        allowanceArray?.forEach((allowance, index) => {
          if (allowance?.includeInd) {
            setValue(
              getRegKeyForBilling(vendorOfferTrackingNbr, outerIndex, index),
              value
            );
          }
        });
      });
    });

    setVendorTrackingNumberChanged(true);
    setValue(vendorOfferTrackingNbr.registerField, value);
    trigger?.();
  }

  function updateAllRelatedAllowances(
    data,
    callBackFunction = (divisionIndex, index) => {}
  ) {
    const selectedDivisionIdsData = divisionsData
      ?.filter(item => item.checked)
      .map(item => item.id);

    data?.forEach((divisionObj, divisionIndex) => {
      Object.keys(divisionObj).forEach(divisionId => {
        if (selectedDivisionIdsData.includes(divisionId)) {
          divisionObj[divisionId]?.forEach((allowance, index) => {
            if (allowance?.includeInd) {
              callBackFunction(divisionIndex, index);
            }

            if (efConstants.LEAD_DIST_ONLY === allowance?.leadDistributorMode) {
              allowance?.leadDistributorInfos?.forEach(element => {
                const subAllowIndex = divisionObj[divisionId]?.findIndex(
                  allow => element?.vendorNbr === allow?.vendorNbr
                );

                if (
                  subAllowIndex !== -1 &&
                  divisionObj[divisionId]?.[subAllowIndex]?.includeInd
                ) {
                  callBackFunction(divisionIndex, subAllowIndex);
                }
              });
            }
          });
        }
      });
    });
  }

  function getErrorForVendorTrackingNumber() {
    let errorMsg = "";

    for (let i = 0; i < errorData?.length; ++i) {
      const item = errorData?.[i];
      if (item?.vendorOfferTrackingNbr) {
        errorMsg = item?.vendorOfferTrackingNbr;
        break;
      }
    }
    return errorMsg;
  }

  const uniqueVendorNames = () => {
    const uniqueVendorNames: any = [];
    const vendorSet = new Set();

    billingInitialValueOnLoad?.forEach(divisionObj => {
      Object.values(divisionObj).forEach((allowanceArray: any) => {
        allowanceArray?.forEach(allowance => {
          const vendorName = allowance?.allowanceBillingInfo?.absMerchVendor;
          if (vendorName && !vendorSet.has(vendorName)) {
            vendorSet.add(vendorName);
            uniqueVendorNames.push(allowance);
          }
        });
      });
    });

    return uniqueVendorNames;
  };
  const onHandleExpand = () => {
    setIsExpand(!isExpand);
  };

  const renderVendorInfo = (
    <VendorInfo uniqueVendorNames={uniqueVendorNames()} />
  );

  const renderStaticData = (
    <div
      id="abs-common-billing-information-merch-vendor-text1"
      className="py-4 px-3 bg-[#F3F4F6] text-sm text-[#5a697b]"
    >
      {billingInformationMessages.map(
        ({ preContent, postContent, urlLink, css, id, urlText }, index) => {
          const formUrl = (urlLink, css, urlText) => {
            return (
              <a
                href={urlLink}
                target="_blank"
                rel="noreferrer"
                className={css}
              >
                {urlText}
              </a>
            );
          };
          return (
            <div key={index}>
              <p id={id}>
                {preContent} {formUrl(urlLink, css, urlText)} {postContent}
              </p>
              <br />
            </div>
          );
        }
      )}
    </div>
  );

  const renderPowerLineInfo = (
    <PowerLineInfo
      formControls={formControls}
      onUpdateAllCommentSubmit={onUpdateAllCommentSubmit}
      isCommentRequired={isCommentRequired}
      commonSuggestedVendorPaymentType={commonSuggestedVendorPaymentType}
      commonAcApOrArNumber={commonAcApOrArNumber}
      commonComment={commonComment}
      divisionsData={divisionsData}
      setDivisionsData={setDivisionsData}
      isError={isError}
    />
  );

  const renderHorizontalDivider = (
    <div
      className="flex-grow-0 flex-shrink-0 w-full h-px mt-2  bg-[#fdfdff] border-t border-r border-b-0 border-l border-[#c8daeb]"
      id="abs-basic-allowance-amount-render-middle-section-one"
    ></div>
  );

  const renderEditBillingInfoSection = (
    <div className="flex gap-8">
      <Button
        className="w-fit p-3"
        variant="secondary"
        size="md"
        type="button"
        onClick={() => setOpenExpand(!openExpand)}
      >
        Edit Division Billing Info
        <ChevronDown
          className={`transform transition-transform duration-300 ease-in-out ${
            openExpand ? "rotate-180" : "rotate-0"
          }`}
          color="#1B6EBB"
        />
      </Button>
      {openExpand ? (
        <div>
          <Button
            variant="secondary"
            size="md"
            width={120}
            type="button"
            onClick={onHandleExpand}
          >
            {!isExpand ? "Expand " : "Collapsed "} All
          </Button>
        </div>
      ) : null}
    </div>
  );

  const billingInfo = (division, divisionIndex) => {
    return billingInitialValueOnLoad?.map((obj, outerIndex) => {
      return Object.keys(obj).map(key => {
        const { divisionId } = division;

        return obj[divisionId]?.map((item, index) => {
          if (!item?.includeInd) return null;

          if (
            efConstants.LEAD_DIST_ONLY === item?.leadDistributorMode &&
            !item?.leadDistributorInd
          )
            return null;
          return (
            <BillingInfoForAllowance
              control={control}
              register={register}
              sectionKey={sectionConfiguration}
              allowance={item}
              allowancesResp={billingInitialValueOnLoad}
              divisionIndex={outerIndex}
              index={index}
              divisionId={divisionId}
              onPaymentTypeChange={onPaymentTypeChange}
              onApArNumberChange={onApArNumberChange}
              onVendorCommentChange={onVendorCommentChange}
              getHighletedClassName={getHighletedClassName}
              errorData={errorData}
              getValues={getValues}
              keyValue={mapperKey}
              allowanceType={allowanceType}
              performance={performance}
              key={`${allowanceType}-${outerIndex}-${divisionIndex}-${index}`}
            />
          );
        });
      });
    });
  };

  const renderVendorSection = (
    <>
      <div className="py-4 px-3 bg-[#F3F4F6] text-sm text-[#5a697b]">
        <p id="abs-common-billing-optional-text">
          Optional - if Vendor wishes to enter a Tracking Number to be displayed
          on all Allowance Agreements for the Offer enter it here:
        </p>
      </div>
      <div className="w-1/5">
        <InputText
          control={control}
          register={register}
          fieldProps={vendorOfferTrackingNbr}
          onChange={onVendorTrackingNumberChange}
          className={classNames(
            getHighletedClassName(
              vendorOfferTrackingNbr,
              vendorTrackingNumberChanged
            )
          )}
          error={{
            message: getErrorForVendorTrackingNumber(),
          }}
        />
      </div>
    </>
  );

  const billingContent = (
    <div
      className="flex flex-col gap-4 mb-4"
      id="abs-common-billing-information-text-container"
    >
      {renderVendorInfo}
      {renderStaticData}
      {renderPowerLineInfo}
      {renderHorizontalDivider}
      {renderEditBillingInfoSection}
      <div className="bg-blue-306 flex items-center text-[#1b6ebb] w-fit">
        <span>{efConstants.BILLING_POWERLINE_TEXT}</span>
      </div>
      {openExpand
        ? divisionsData?.map((division, index) => (
            <AccordionAtom division={division} key={index} isExpand={isExpand}>
              {billingInfo(division, index)}
            </AccordionAtom>
          ))
        : null}
      {renderVendorSection}
    </div>
  );
  const checkIfVisible = () => {
    if (formControls) {
      const isSectionFocus = offerSectionsEnableConfig?.[sectionKey]?.scrollTo;
      if (!isEditEnable && !isLastStep) {
        return isSectionFocus ? true : formState?.isDirty;
      }
    }
    return true;
  };
  const renderHtml = (
    <>
      <div className="text-xl text-[#3997EF] font-bold mb-3">
        {sectionConfiguration?.label}
      </div>
      <AllowanceFormWrapper
        defaultValues={{
          allowanceBillingInfo: defaultValues,
          vendorOfferTrackingNbr: defaultTrackNumber,
        }}
        handleSave={handleSave}
        getFormControls={getFormControls}
        variant={isLastStep ? "primary" : "secondary"}
        footerProps={{
          label: getSubmitBtnLabel(),
          className: "flex gap-2 p-3 pl-[0px]",
          FooterChildren: cancelButton,
          visable: checkIfVisible(),
          style: {
            width:
              getSubmitBtnLabel() === efConstants.UPDATE_ALLOWANCE ? 180 : 260,
          },
          disabled: getOfferStatus(),
        }}
      >
        <>
          <LoadingSpinner
            isLoading={
              isNationalAllowancePostLoading ||
              isDeleteNationalTempworkDataLoading ||
              isNationalAllowanceUpdateLoading ||
              isBillingLoading
            }
            classname="!h-full !w-full rounded-md"
          />
          {control && billingContent}
        </>
      </AllowanceFormWrapper>
    </>
  );

  const renderDetails = {
    isApiLoading: false,
    isPageLevelSpinner: true,
    isRenderMainHtml: true,
    renderHtml,
  };

  return <RenderStates details={renderDetails} />;
}
