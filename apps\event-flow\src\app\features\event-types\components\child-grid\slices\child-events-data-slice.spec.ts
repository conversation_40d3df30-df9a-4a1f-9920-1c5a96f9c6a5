import { configureStore } from "@reduxjs/toolkit";
import {
  childEventsDataSlice,
  childEventTabConfig,
  setChildEventsDetailsData,
  updateChildEventGridData,
  setChildEventTabConfigData,
} from "./child-events-data-slice";

describe("Redux Slice tests - Child Events", () => {
  let store;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        childEventsData: childEventsDataSlice.reducer,
        childEventTabConfig: childEventTabConfig.reducer,
      },
    });
  });

  it("should handle initial state for childEventsDataSlice", () => {
    const initialState = store.getState().childEventsData;
    expect(initialState.status).toEqual("loading");
    expect(initialState.data).toEqual({});
  });

  it("should handle setChildEventsDetailsData correctly", () => {
    const testData = { eventId: 1, name: "Sample Event" };
    store.dispatch(setChildEventsDetailsData(testData));
    const updatedState = store.getState().childEventsData;
    expect(updatedState.data).toEqual(testData);
  });

  it("should handle updateChildEventGridData correctly", () => {
    const initialState = {
      data: {
        eventDetails: [
          {
            events: [{ rowIndex: 0, key1: "value1" }],
          },
        ],
      },
    };
    store.dispatch(setChildEventsDetailsData(initialState.data));
    store.dispatch(
      updateChildEventGridData({ cardIndex: 0, rowIndex: 0, key: "key1", value: "updatedValue" })
    );
    const updatedState = store.getState().childEventsData;
    expect(updatedState.data.eventDetails[0].events[0].key1).toEqual("updatedValue");
  });

  it("should handle initial state for childEventTabConfig", () => {
    const initialState = store.getState().childEventTabConfig;
    expect(initialState.status).toEqual("loading");
    expect(initialState.data).toEqual({});
  });

  it("should handle setChildEventTabConfigData correctly", () => {
    const testData = { activeTab: "Details" };
    store.dispatch(setChildEventTabConfigData(testData));
    const updatedState = store.getState().childEventTabConfig;
    expect(updatedState.data).toEqual(testData);
  });
});
