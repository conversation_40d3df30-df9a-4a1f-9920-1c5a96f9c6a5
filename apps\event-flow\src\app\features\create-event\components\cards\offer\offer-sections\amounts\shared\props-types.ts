import { FormState } from "react-hook-form";


//TODO: NEED TO GIVE TYPES PROPERLY
export interface IBaseAmtProp{
  cardIndex: number;
  cardItemIndex?: number;
  isEditEnable: boolean;
  sectionConfiguration: any;
  allowanceAmountFields: any;
  setAllowanceAmountFields?: any;
  allowanceType: any;
  allowanceAmountData?: any;
  setAllowanceAmountData: any;
  allowancesResp: any;
  editViewAllItems: () => void;
  setisFormValid?: any;
  isZeroCost: any;
  formControls: any;
  isHfIfWhseCase?: boolean;
  isSectionCompleted?:boolean
  amountsInitialValueOnLoad: any;
  amtSubLabelDisplayVal?: string;
  isAmtSavedInTemp?: boolean;
}

export interface IAmountStepFieldProps {
  fieldProps: any;
  formControls: any;
  onChange: any;
  error?: any;
  prefixValue?: string;
  className?: string;
  disabled?: boolean;
  value?: any;
  multiplier?: number;
  allowanceAmountFields: any;
  getEventData: any;
  searchId: any;
  baseId: string;
  options?: any;
}

export interface IHeaderItemInfoProps {
  itemCount: number;
  totalAmount: number;
  wareHouseCount?: number;
  hasItemCount: boolean;
  isHfIfWhseCase?: boolean;
}

export interface IAmountDisplayProps {
  label: string;
  value: any;
  icon?: any;
  baseId: string;
  directDisplay?: boolean;
}

export interface IUseAllowanceAmountProps {
  allowancesResp: any;
  allowanceType: string;
  formControls: any;
  initialValues: any;
  setAllowanceAmountData: any;
  allowanceAmountFields?: any;
  setAllowanceAmountFields?: any;
  isHeaderFlat?: boolean;
  cardIndex?: number;
  cardItemIndex?: number;
  searchId?: any;
  eventDetailsData?: any;
  fieldProp: any;
  isHfIfWhseCase?: boolean;
  isAmtSavedInTemp?: boolean
  isEditEnable?: boolean
}

export interface IUserActionsProps {
  actionsConfig: {
    isEditEnable: boolean;
    isSectionCompleted: boolean;
    isFormDirty: boolean;
    isHideFieldsForMainEntry?: boolean;
    baseId: string;
    onClick: () => void;
    sectionConfiguration: any
    isInValidOffer: boolean
  };
}
