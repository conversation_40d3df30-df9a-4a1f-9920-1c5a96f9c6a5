import Button from "@albertsons/uds/molecule/Button";
import Modal from "@albertsons/uds/molecule/Modal";
import { FunctionComponent, ReactNode, useCallback } from "react";
import efConstants from "../../../../../shared/ef-constants/ef-constants";
import LoadingSpinner from "../../../constants/LoadingSpinner/LoadingSpinner";

interface ICommonModalProps {
  isModalPopupOpen: any;
  setModalPopupOpen: any;
  deleteCardHandler?: any;
  title?: any;
  warningMessage?: any;
  infoMessage?: any;
  childComp?: any;
  isChildRender?: any;
  confirmBtnTitle?: any;
  cancelBtnTitle?: any;
  width?: number | string;
  height?: number | string;
  minHeight?: number | string;
  minWidth?: number | string;
  minBtnWidth?: number;
  minCancelBtnWidth?: number;
  modalNameHandler?: any;
  showHideBtns?: any;
  cardIndex?: any;
  cardItemIndex?: any;
  modalContent?: ReactNode;
  isLoading?: boolean;
  onClose?: () => void;
  extClassName?: string;
  isConfirmDisabled?: boolean;
  isLastOfferPromotion?: boolean;
  cancelBtnHandler?: any;
  isCancelAmountStep?: boolean;
  mixedTypeError?: string;
  modalContainerClass?: string;
  confrimBtnHandler?: any;
}

export const CommonModal: FunctionComponent<ICommonModalProps> = ({
  isModalPopupOpen,
  setModalPopupOpen,
  deleteCardHandler,
  title,
  warningMessage,
  infoMessage,
  childComp,
  isChildRender,
  confirmBtnTitle,
  cancelBtnTitle,
  width,
  height,
  minHeight,
  minWidth,
  minBtnWidth,
  minCancelBtnWidth,
  modalNameHandler,
  showHideBtns,
  cardIndex,
  cardItemIndex,
  modalContent,
  isLoading = false,
  onClose,
  isLastOfferPromotion,
  cancelBtnHandler,
  extClassName,
  isConfirmDisabled = false,
  isCancelAmountStep,
  mixedTypeError = "",
  modalContainerClass = "",
  confrimBtnHandler,
}) => {
  const { REQUEST_FOR_CHANGE, componentClassName } = efConstants;
  const confirmHandler = () => {
    if (title === REQUEST_FOR_CHANGE) {
      confrimBtnHandler();
    } else {
      modalNameHandler(cardIndex, cardItemIndex, isLastOfferPromotion);
      document.body.style.overflow = "visible";
    }

    setModalPopupOpen(false);
  };
  const cancelButtonClickHandler = () => {
    if (title !== REQUEST_FOR_CHANGE) {
      cancelBtnHandler(isLastOfferPromotion, cancelBtnTitle);
    }
    document.body.style.overflow = "visible";
    setModalPopupOpen(false);
  };
  const getCommonContent = () => {
    return (
      <div
        id="abs-common-modal-common-content-container"
        className={`${
          componentClassName?.GET_COMMON_CONTENT
        } flex flex-col gap-[16px] mx-[56px] pt-12 ${
          extClassName ? extClassName : ""
        }`}
      >
        <div
          className="text-center select-none font-bold text-[28px] text-[#2B303C]  mx-4"
          id="abs-common-modal-title-sec"
        >
          {title}
        </div>

        {warningMessage ? (
          <div
            className="text-center select-none text-xl mt-4"
            id="abs-common-modal-warning-sec"
          >
            <span className="text-[20px] text-[#bf2912]">Warning: </span>
            {warningMessage.split("\n").map((line, index) => (
              <span key={index}>
                {line}
                <br />
              </span>
            ))}
          </div>
        ) : null}
        {infoMessage ? (
          <div
            className="text-center select-none text-xl mt-4"
            id="abs-common-modal-info-message-sec"
          >
            {infoMessage}
          </div>
        ) : null}
        {childComp ? childComp() : null}
        {mixedTypeError ? (
          <p
            className="text-sm leading-4 font-bold text-error mt-1 select-none"
            id="abs-common-modal-mixed-type-error-text"
          >
            <span>{mixedTypeError}</span>
          </p>
        ) : null}
        {showHideBtns && (
          <div
            id="abs-common-modal-islast-offer-promotion-section"
            className={`flex items-center justify-center w-full mt-[16px] ${
              isLastOfferPromotion ? "flex-row-reverse" : ""
            }`}
          >
            {cancelBtnTitle && <Button
              width={minCancelBtnWidth || 200}
              size="lg"
              className="mx-2 whitespace-nowrap"
              variant={`${
                isLastOfferPromotion || isCancelAmountStep
                  ? "primary"
                  : "secondary"
              }`}
              onClick={e => {
                e?.preventDefault();
                cancelButtonClickHandler();
              }}
            >
              {cancelBtnTitle}
            </Button> }
            {confirmBtnTitle ? (
              <Button
                width={minBtnWidth || 216}
                size="lg"
                className="mx-2 whitespace-nowrap"
                variant={`${
                  isLastOfferPromotion || isCancelAmountStep
                    ? "secondary"
                    : "primary"
                }`}
                onClick={e => {
                  e?.preventDefault();
                  confirmHandler();
                }}
                disabled={isConfirmDisabled || !!mixedTypeError}
              >
                {confirmBtnTitle}
              </Button>
            ) : null}
          </div>
        )}
      </div>
    );
  };

  const getDimensions = useCallback(() => {
    return width || height
      ? { width: width || 800, height: mixedTypeError ? 390 : height || 360 }
      : {};
  }, [mixedTypeError, height, width]);

  return (
    <div
      id="abs-common-modal-position-modal-section"
      className={`${isModalPopupOpen ? "positionModal" : ""} ${
        componentClassName?.COMMON_MODAL
      }`}
    >
      <Modal
        minHeight={minHeight || 316}
        minWidth={minWidth || 800}
        isOpen={isModalPopupOpen}
        onClose={onClose}
        {...getDimensions()}
        className={modalContainerClass}
      >
        <LoadingSpinner
          classname="!h-full !w-full rounded-md"
          isLoading={isLoading}
        />
        {modalContent ? modalContent : getCommonContent()}
      </Modal>
    </div>
  );
};
