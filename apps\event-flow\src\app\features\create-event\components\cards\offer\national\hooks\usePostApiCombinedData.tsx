import { useState } from "react";
import { compareDivisionIdFn } from "../../offer-service";

const initialState = {
  data: [],
  loading: false,
  error: null,
};

const usePostApiCombinedData = () => {
  const [state, setState] = useState(initialState);

  const updateState = newState => {
    setState(() => ({
      ...initialState,
      ...newState,
    }));
  };

  const postCombinedData = async (query, params) => {
    updateState({ loading: true });
    try {
      const responses = await Promise.all(
        params.map(param => query(param)?.then(res => res))
      );
      const combinedData: any = responses
        .map(response => response?.data)
        ?.flat();
      // Sort combinedData based on divisionId field
      combinedData.sort(compareDivisionIdFn);
      updateState({ data: combinedData });
      return { data: combinedData, error: null };
    } catch (err: any) {
      updateState({ error: err });
      return { data: [], error: err };
    }
  };

  return { state, postCombinedData };
};

export default usePostApiCombinedData;
