import { useEffect, useRef, useState } from "react";
import { useSelectorWrap } from "@me/data-rtk";
import LoadingSpinner from "../../../../../constants/LoadingSpinner/LoadingSpinner";
import { getAllowanceFormRegister<PERSON>ey } from "../../../../../service/allowance/allowance-service";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";
import {
  addDaysHelper,
  checkInvalidDates,
  getFieldWithHighletedWrapper,
  renderVendorDetails,
  renderWarehouseDetails,
} from "../../../allowance/stepper/common-stepper/allowance-dates/allowance-dates-service";
import { useGetAllowancesItemsQuery } from "../../../../../service/apis/allowance-api";
import {
  setAllowanceFormInfo,
  setOfferAmontsData,
  setVendorsForAllowances,
} from "../../../../../service/slice/allowance-details-slice";
import { useDispatch } from "react-redux";
import { DatePickerAtom } from "../../../../fields/allowance-atoms";
import { FormFieldError } from "@me/util-form-wrapper";
import useGetOfferSectionConfiguration from "../../hooks/useGetOfferSectionConfiguration";
import { OFFER_FORM_FIELDS } from "../../offer-flow-config";

const CaseAdditionalDates = ({
  cardIndex,
  cardItemIndex,
  formControls,
  sectionConfiguration,
  isEditEnable = false,
  offerMapKey = "",
  vehicleFields,
  sectionKey,
}) => {
  const {
    OFFER_ALLOWANCE_GROUP,
    ALLOWANCE_TYPES: { SHIPTOSTORE },
  } = efConstants;
  const {
    fields: { orderStart, orderEnd, arrivalStart, arrivalEnd, errors },
  } = sectionConfiguration;
  const {
    additionalDatesChangeKey,
    orderDatesChangeKey,
    additionalDatesKey,
    datesValidateKey,
  } = OFFER_FORM_FIELDS;

  const getEventData = () => {
    return eventDetailsData;
  };

  const dispatch = useDispatch();
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { data: allowanceSliceData } = useSelectorWrap("allowance_form_data");
  const {
    data: { allowanceData: allowanceTempWorkData },
  } = useSelectorWrap("allowance_temp_work");
  const { productSources = [] } =
    useSelectorWrap("allowance_product_sources_rn")?.data || {};
  const { isAllowConvEnable } = useSelectorWrap("allow_conv_status_rn").data;
  const { offerAmounts = {}, isAdditionalDatesChanged = {} } =
    useSelectorWrap("offer_amounts_details")?.data || {};

  const {
    vendors: { myVendors = [] } = {},
    isMultiVendor = false,
    isAllowMinSfoChanged = false,
    allowancesAmontsResp = {},
    isMultiVendorEnabled,
  } = useSelectorWrap("vendors_data_for_allowance_rn").data;

  const allowanceRegField = getAllowanceFormRegisterKey(
    cardIndex,
    cardItemIndex
  );
  const { clearNextSectionsOnOptionChange } = useGetOfferSectionConfiguration({
    allowanceRegField,
  });

  const {
    register = () => null,
    control,
    setValue = () => null,
    trigger,
  } = formControls || {};

  const vehicleStart = vehicleFields?.startDate;
  const vehicleEnd = vehicleFields?.endDate;
  const minimumDate = addDaysHelper(vehicleStart, -30);
  const maximumDate = addDaysHelper(vehicleEnd, 30);
  const allowanceFormData =
    allowanceSliceData?.allowanceFormData?.[allowanceRegField];
  const { allowanceType } = allowanceFormData?.allowancePrimeData || {};
  const isCancelled = allowanceFormData?.isCancelled?.[offerMapKey];
  const formAllowItems = allowanceFormData?.additionalDates?.[offerMapKey];
  const isDsdOffer = offerMapKey === OFFER_ALLOWANCE_GROUP.CASE.DSD;
  const isCaseWarehouse = offerMapKey === OFFER_ALLOWANCE_GROUP.CASE.WAREHOUSE;
  const isCombinedOffer = offerMapKey === OFFER_ALLOWANCE_GROUP.CASE.COMBINED;
  const isS2sOffer = allowanceType === SHIPTOSTORE.label;
  const typeKey = isDsdOffer ? "vendorNbr" : "locationName";

  const searchId =
    eventDetailsData?.offerAllowances?.[cardIndex || 0]?.offerNumber;
  const amountsData = offerAmounts?.[offerMapKey]?.allowances || [];
  const isOfferInvalid = eventDetailsData?.inValidAllowances?.includes(
    eventDetailsData?.offerAllowances?.[cardIndex]?.id
  );

  const dates = useRef<any>([]);
  const additionalDatesCount = useRef(0);
  const addionalDatesUpdateCount = useRef(0);
  const [datesArray, setDatesArray] = useState<any>([]);
  const [invalidDatesVendor, setInvalidDatesVendor] = useState<any>([]);
  const [dateType, setDateType] = useState<any>([]);
  const [count, setCount] = useState(0);
  const [refreshDates, setRefreshDates] = useState(false);

  const allowanceAmountsPayload = {
    URL_PARAMS: [eventDetailsData.id, offerMapKey],
  };

  const {
    data: allowancesResp,
    isFetching,
    isLoading,
  } = useGetAllowancesItemsQuery(allowanceAmountsPayload, {
    refetchOnMountOrArgChange: true,
    skip:
      isAllowConvEnable ||
      !allowanceTempWorkData?.tempWorkAllowanceId ||
      !productSources?.length ||
      !!amountsData?.length ||
      isAllowMinSfoChanged ||
      !!count,
  });

  const isAmountsDataLoading = isFetching || isLoading;

  useEffect(() => {
    if (
      (amountsData?.length ||
        allowancesResp?.allowances?.length ||
        isAllowConvEnable) &&
      (!count || isAllowMinSfoChanged)
    ) {
      setRefreshDates(isAllowMinSfoChanged);
      const amountsResult =
        !isAllowMinSfoChanged && amountsData?.length
          ? offerAmounts?.[offerMapKey]
          : allowancesResp || allowancesAmontsResp;

      let filteredResp: any = amountsResult;
      if (isMultiVendorEnabled && isDsdOffer) {
        filteredResp = !isMultiVendor
          ? {
              ...amountsResult,
              allowances: amountsResult?.allowances?.filter(item =>
                myVendors?.some(
                  vendor => vendor?.dsdVendorNbr === item?.vendorNbr
                )
              ),
            }
          : amountsResult;
        dispatch(
          setVendorsForAllowances({
            ...myVendors,
            isMultiVendor,
            isAllowMinSfoChanged: false,
            ...(allowancesResp && { allowancesAmontsResp: allowancesResp }),
          })
        );
      }

      (!amountsData?.length || isAllowMinSfoChanged) &&
        dispatch(
          setOfferAmontsData({
            offerAmounts: { ...offerAmounts, [offerMapKey]: filteredResp },
            ...(!isCancelled && {
              isAdditionalDatesChanged: {
                ...isAdditionalDatesChanged,
                [offerMapKey]: 0,
              },
            }),
          })
        );
      const vendorsArray = isAllowConvEnable
        ? formAllowItems
        : filteredResp?.allowances;

      additionalDatesCount.current =
        (vendorsArray?.length || 1) * (isCaseWarehouse ? 4 : 2);
      vendorsArray?.length &&
        setDatesArray(() => updateDatesArray(vendorsArray));
      setValue(datesValidateKey, !!vendorsArray?.length);
      setValue(additionalDatesChangeKey, false);
      setValue(orderDatesChangeKey, false);
    } else {
      setValue(datesValidateKey, false);
    }
  }, [allowancesResp, isMultiVendor]);

  const fillDatesFromForm = dates => {
    const arrivalStartDate = dates?.["arrivalStartDate"];
    const arrivalEndDate = dates?.["arrivalEndDate"];
    const orderStartDate = dates?.["orderStartDate"];
    const orderEndDate = dates?.["orderEndDate"];
    return {
      arrivalStartDate,
      arrivalEndDate,
      ...(isCaseWarehouse ? { orderStartDate, orderEndDate } : {}),
    };
  };

  const updateDatesArray = vendorsArray => {
    const data = vendorsArray?.map((vendor: any, index: number) => {
      if (
        !isOfferInvalid &&
        formAllowItems?.length &&
        formAllowItems?.[index]
      ) {
        const {
          arrivalStartDate,
          arrivalEndDate,
          orderStartDate,
          orderEndDate,
        } = fillDatesFromForm(formAllowItems?.[index]);

        return {
          ...vendor,
          defaultAllowanceDates: {
            ...vendor?.["defaultAllowanceDates"],
            overrideInd: true,
          },
          arrivalStartDate,
          arrivalEndDate,
          ...(isCaseWarehouse ? { orderStartDate, orderEndDate } : {}),
        };
      }
      return {
        ...vendor,
        defaultAllowanceDates: {
          ...vendor?.["defaultAllowanceDates"],
          overrideInd: true,
        },
      };
    });
    return data;
  };

  const getUpdatedFieldProps = (fieldProps: any, index: number) => {
    return {
      ...fieldProps,
      registerField: `${fieldProps.registerField}.${index}`,
    };
  };

  useEffect(() => {
    if (!count || refreshDates) {
      dates.current = datesArray;
      setRefreshDates(false);
    }
    setValue(additionalDatesKey, datesArray);
    if (datesArray?.length) {
      datesArray?.map((vendor: any, index: number) => {
        if (isCaseWarehouse) {
          setValue(
            getUpdatedFieldProps(orderStart, index)?.registerField,
            vendor?.orderStartDate
          );
          setValue(
            getUpdatedFieldProps(orderEnd, index)?.registerField,
            vendor?.orderEndDate
          );
        }

        setValue(
          getUpdatedFieldProps(arrivalStart, index)?.registerField,
          vendor?.arrivalStartDate
        );
        setValue(
          getUpdatedFieldProps(arrivalEnd, index)?.registerField,
          vendor?.arrivalEndDate
        );
        return vendor;
      });
    }

    const { inValidVendors, type } = checkInvalidDates(
      datesArray,
      isCaseWarehouse,
      vehicleStart,
      vehicleEnd,
      typeKey
    );
    setInvalidDatesVendor(inValidVendors);
    setDateType(type);
    trigger?.();
  }, [JSON.stringify(datesArray)]);

  useEffect(() => {
    count && setDatesArray(() => dates.current);
  }, [count]);

  const onSectionUpdate = () => {
    if (!isEditEnable) {
      clearNextSectionsOnOptionChange(sectionKey);
    } else {
      !allowanceFormData?.isAdditionalDatesChanged &&
        dispatch(
          setAllowanceFormInfo({
            allowanceFormData: {
              [allowanceRegField]: {
                ...allowanceFormData,
                isAdditionalDatesChanged: true,
              },
            },
          })
        );
    }
  };

  const setDateValues = (fieldKey: string, date: string, index: number) => {
    dates.current = dates.current.map((vendor, i) => {
      return i === index
        ? {
            ...vendor,
            [fieldKey]: date,
          }
        : vendor;
    });
    setCount(count => count + 1);
  };

  const updateSavedData = (dateType, date, index: number) => {
    const isCountExceded =
      addionalDatesUpdateCount.current > additionalDatesCount.current;
    if (isCountExceded) {
      setValue(additionalDatesChangeKey, true);
      onSectionUpdate();
    }

    switch (dateType) {
      case orderStart.label:
        isCountExceded && setValue(orderDatesChangeKey, true);
        setDateValues("orderStartDate", date, index);
        break;

      case orderEnd.label:
        isCountExceded && setValue(orderDatesChangeKey, true);
        setDateValues("orderEndDate", date, index);
        break;

      case arrivalStart.label:
        setDateValues("arrivalStartDate", date, index);
        break;

      case arrivalEnd.label:
        setDateValues("arrivalEndDate", date, index);
        break;

      default:
        break;
    }
  };

  const onVehicleDateChange = (
    key: string,
    dateType,
    e: string,
    index: number
  ) => {
    addionalDatesUpdateCount.current = addionalDatesUpdateCount.current + 1;
    key && updateSavedData(dateType, e, index);
  };

  const orderStartField = (row, index: number) => {
    const _key = row?.[typeKey];
    return orderStart
      ? getFieldWithHighletedWrapper(
          <DatePickerAtom
            key={index}
            fieldProps={getUpdatedFieldProps(orderStart, index)}
            register={register}
            control={control}
            customValidation={{
              value: !(
                invalidDatesVendor?.includes(_key) &&
                (dateType?.[_key]?.order || dateType?.[_key]?.orderStart)
              ),
              message: orderStart?.errors?.invalid?.text,
            }}
            onChange={e => {
              onVehicleDateChange(_key, orderStart?.label, e, index);
            }}
            minDate={minimumDate}
            maxDate={maximumDate}
          />,
          orderStart,
          getEventData,
          searchId
        )
      : null;
  };

  const orderEndField = (row, index: number) => {
    const _key = row?.[typeKey];
    return orderEnd
      ? getFieldWithHighletedWrapper(
          <DatePickerAtom
            key={index}
            fieldProps={getUpdatedFieldProps(orderEnd, index)}
            register={register}
            control={control}
            customValidation={{
              value: !(
                invalidDatesVendor?.includes(_key) &&
                (dateType?.[_key]?.order || dateType?.[_key]?.orderEnd)
              ),
              message: orderEnd?.errors?.invalid?.text,
            }}
            onChange={e => {
              onVehicleDateChange(_key, orderEnd?.label, e, index);
            }}
            minDate={minimumDate}
            maxDate={maximumDate}
          />,
          orderEnd,
          getEventData,
          searchId
        )
      : null;
  };

  const arrivalStartField = (row, index: number) => {
    const _key = row?.[typeKey];
    return arrivalStart
      ? getFieldWithHighletedWrapper(
          <DatePickerAtom
            key={index}
            fieldProps={getUpdatedFieldProps(arrivalStart, index)}
            register={register}
            control={control}
            customValidation={{
              value: !(
                invalidDatesVendor?.includes(_key) &&
                (dateType?.[_key]?.arrival ||
                  dateType?.[_key]?.vehicle ||
                  (isCaseWarehouse && dateType?.[_key]?.orderStart) ||
                  dateType?.[_key]?.ship)
              ),
              message: arrivalStart?.errors?.invalid?.text,
            }}
            onChange={e => {
              onVehicleDateChange(_key, arrivalStart?.label, e, index);
            }}
            minDate={minimumDate}
            maxDate={maximumDate}
          />,
          arrivalStart,
          getEventData,
          searchId
        )
      : null;
  };

  const arrivalEndField = (row, index: number) => {
    const _key = row?.[typeKey];
    return arrivalEnd
      ? getFieldWithHighletedWrapper(
          <DatePickerAtom
            key={index}
            fieldProps={getUpdatedFieldProps(arrivalEnd, index)}
            register={register}
            control={control}
            customValidation={{
              value: !(
                invalidDatesVendor?.includes(_key) &&
                (dateType?.[_key]?.arrival ||
                  dateType?.[_key]?.vehicle ||
                  (isCaseWarehouse && dateType?.[_key]?.orderEnd) ||
                  dateType?.[_key]?.ship)
              ),
              message: arrivalEnd?.errors?.invalid?.text,
            }}
            onChange={e => {
              onVehicleDateChange(_key, arrivalEnd?.label, e, index);
            }}
            minDate={minimumDate}
            maxDate={maximumDate}
          />,
          arrivalEnd,
          getEventData,
          searchId
        )
      : null;
  };

  const renderAdditionalDateFields = (index: number) => {
    const fields = [
      ...(isCaseWarehouse ? [orderStartField, orderEndField] : []),
      arrivalStartField,
      arrivalEndField,
    ];

    return <>{fields.map(field => field(dates.current?.[index], index))}</>;
  };

  return (
    <div className="pb-2">
      <LoadingSpinner
        isLoading={isAmountsDataLoading}
        classname="!h-full !w-full rounded-md"
      />

      {dates.current?.map((vendor, index: number) => (
        <>
          {isDsdOffer
            ? renderVendorDetails(vendor)
            : !isCombinedOffer &&
              renderWarehouseDetails(vendor, !isCombinedOffer)}
          <div>
            <div className="grid grid-cols-4 gap-2 mt-4">
              {renderAdditionalDateFields(index)}
            </div>
            <div>
              {invalidDatesVendor?.includes(vendor?.[typeKey]) && (
                <div className="flex flex-col gap-1 my-1">
                  {Object.keys(dateType?.[vendor?.[typeKey]] || {})?.map(
                    (type: string) =>
                      dateType?.[vendor?.[typeKey]]?.[type] && (
                        <FormFieldError
                          error={
                            errors.INVALID_DATES?.[
                              isS2sOffer
                                ? efConstants?.ALLOWANCE_TYPES.SHIPTOSTORE.key
                                : efConstants?.ALLOWANCE_TYPES.CASE.key
                            ]?.[type]
                          }
                        />
                      )
                  )}
                </div>
              )}
            </div>
          </div>
        </>
      ))}
    </div>
  );
};

export default CaseAdditionalDates;
