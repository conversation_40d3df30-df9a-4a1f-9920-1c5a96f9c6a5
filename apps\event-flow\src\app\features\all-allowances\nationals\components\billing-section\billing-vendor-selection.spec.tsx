import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import BillingVendorSelection from "./billing-vendor-selection";
import { useSelectorWrap } from "@me/data-rtk";
import useBillingSelection from "../../../../create-event/hooks/useBillingSelection";
import { isNationalType } from "../../../allowance-lead-distributors/billing-selection-utils";
import '@testing-library/jest-dom';

// Mock the custom hook and dependencies.
jest.mock("@me/data-rtk", () => ({
  ...jest.requireActual("@me/data-rtk"),
  useSelectorWrap: jest.fn(),
}));
jest.mock("../../../../create-event/hooks/useBillingSelection");
jest.mock("../../../allowance-lead-distributors/billing-selection-utils", () => ({
  isNationalType: jest.fn(),
}));

describe("BillingVendorSelection", () => {
  // Sample vendor data for the Redux store.
  const leadOptions = [
    { vendorNbr: "1", vendorName: "Vendor 1", costAreaDesc: "Cost Area 1" },
    { vendorNbr: "2", vendorName: "Vendor 2", costAreaDesc: "Cost Area 2" },
  ];
  const allDivisionLeadOptions = {
    div1: [
      { vendorNbr: "1", vendorName: "Vendor 1", costAreaDesc: "Cost Area 1" },
      { vendorNbr: "2", vendorName: "Vendor 2", costAreaDesc: "Cost Area 2" },
    ],
  };

  beforeEach(() => {
    // For this test, assume isNationalType returns true.
    isNationalType.mockReturnValue(true);

    // Return fake Redux data.
    useSelectorWrap.mockImplementation((key) => {
      if (key === "leadDistributors_rn") {
        return { data: { leadOptions, allDivisionLeadOptions } };
      }
      return {};
    });

    // Default implementation of useBillingSelection for one main vendor with one child.
    useBillingSelection.mockReturnValue({
      moveUp: jest.fn(),
      moveDown: jest.fn(),
      mainVendors: ["1"],
      mapping: [{ child: ["2"] }],
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders main vendor checkbox and child vendor checkbox", () => {
    const currentDivision = { divisionId: "div1" };
    render(
      <BillingVendorSelection currentDivision={currentDivision} isNdpType={false} />
    );

    // Check for vendor name summaries.
    expect(screen.getByText(/Vendor 1/)).toBeInTheDocument();
    expect(screen.getByText(/Vendor 2/)).toBeInTheDocument();

    // There should be two checkboxes rendered.
    const checkboxes = screen.getAllByRole("checkbox");
    expect(checkboxes).toHaveLength(2);
  });

  it("calls moveDown when Move Down action is clicked", () => {
    // For this test, simulate more than one main vendor.
    const mockMoveUp = jest.fn();
    const mockMoveDown = jest.fn();
    useBillingSelection.mockReturnValue({
      moveUp: mockMoveUp,
      moveDown: mockMoveDown,
      mainVendors: ["1", "3"],
      mapping: [
        { child: ["2"] },
        { child: [] },
      ],
    });

    const currentDivision = { divisionId: "div1" };
    render(
      <BillingVendorSelection currentDivision={currentDivision} isNdpType={false} />
    );

    // In the first main vendor section (index 0), the "Move Down" action should be rendered.
    // Note: In the component, both moveUp and moveDown render a div with text "Move" followed by an icon.
    // We'll use getAllByText to target one of these buttons.
    const moveButtons = screen.getAllByText(/Move/i);
    expect(moveButtons.length).toBeGreaterThan(0);

    // Click on the first move button (for moveDown in index 0)
    fireEvent.click(moveButtons[0]);
    expect(mockMoveDown).toHaveBeenCalled();
  });
});
