import {
  getFromSessionStorage,
  getTimeObject,
  handleKeyDown,
  removeItemFromSessionStorage,
  saveToSessionStorage,
  sortViewItems,
} from "./event-flow-helpers";
import _ from "lodash";

describe("getTimeObject", () => {
  it("should return a Date object for a given date string", () => {
    const dateStr = "2022-03-01";
    const dateObj = getTimeObject(dateStr);
    expect(dateObj).toBeInstanceOf(Date);
  });

  it("should return the current Date object if no date string is provided", () => {
    const dateObj = getTimeObject();
    expect(dateObj).toBeInstanceOf(Date);
    const now = new Date();
    expect(dateObj.toISOString().slice(0, 10)).toBe(
      now.toISOString().slice(0, 10)
    );
  });

  it("should handle date strings with different formats", () => {
    const dateStr = "2022/03/01";
    const dateObj = getTimeObject(dateStr);
    expect(dateObj).toBeInstanceOf(Date);
  });

  it("should handle invalid date strings gracefully", () => {
    const dateStr = "invalid-date";
    const dateObj = getTimeObject(dateStr);
    expect(dateObj).toBeInstanceOf(Date);
    expect(isNaN(dateObj.getTime())).toBe(true); // Invalid Date objects have NaN time value
  });
  it("should handle date strings with mixed delimiters", () => {
    const dateStr = "2022-03/01";
    const dateObj = getTimeObject(dateStr);
    expect(dateObj).toBeInstanceOf(Date);
    expect(isNaN(dateObj.getTime())).toBe(false);
  });
});

describe("sortViewItems", () => {
  it("should sort items by the given key", () => {
    const items = [
      { name: "Charlie", age: 30 },
      { name: "Alice", age: 25 },
      { name: "Bob", age: 20 },
    ];
    const sortedItems = sortViewItems(items, "name");
    expect(sortedItems).toEqual([
      { name: "Alice", age: 25 },
      { name: "Bob", age: 20 },
      { name: "Charlie", age: 30 },
    ]);
  });

  it("should return an empty array if input is an empty array", () => {
    const sortedItems = sortViewItems([], "name");
    expect(sortedItems).toEqual([]);
  });

  it("should return an empty array if input is not an array", () => {
    const sortedItems = sortViewItems(null, "name");
    expect(sortedItems).toEqual([]);
  });

  it("should sort items by a numerical key", () => {
    const items = [
      { name: "Charlie", age: 30 },
      { name: "Alice", age: 25 },
      { name: "Bob", age: 20 },
    ];
    const sortedItems = sortViewItems(items, "age");
    expect(sortedItems).toEqual([
      { name: "Bob", age: 20 },
      { name: "Alice", age: 25 },
      { name: "Charlie", age: 30 },
    ]);
  });

  it("should handle items with missing sort keys gracefully", () => {
    const items = [
      { name: "Charlie", age: 30 },
      { name: "Alice" },
      { name: "Bob", age: 20 },
    ];
    const sortedItems = sortViewItems(items, "age");
    expect(sortedItems).toEqual([
      { name: "Bob", age: 20 },
      { name: "Charlie", age: 30 },
      { name: "Alice" },
    ]);
  });
});
describe("handleKeyDown", () => {
  let preventDefaultMock: jest.Mock;

  beforeEach(() => {
    preventDefaultMock = jest.fn();
  });

  const createEvent = (key: string) => ({
    key,
    preventDefault: preventDefaultMock,
  });

  it('should prevent default action for keys "e", "E", "+", "-", "."', () => {
    const keysToTest = ["e", "E", "+", "-", "."];
    keysToTest.forEach(key => {
      const event = createEvent(key);
      handleKeyDown(event);
      expect(preventDefaultMock).toHaveBeenCalled();
    });
  });

  it("should not prevent default action for other keys", () => {
    const keysToTest = ["a", "1", "Enter", "ArrowUp"];
    keysToTest.forEach(key => {
      const event = createEvent(key);
      handleKeyDown(event);
      expect(preventDefaultMock).not.toHaveBeenCalled();
    });
  });
});
describe("saveToSessionStorage", () => {
  beforeEach(() => {
    // Clear all items in sessionStorage before each test
    sessionStorage.clear();
  });

  it("should save a string value to sessionStorage", () => {
    const key = "testKey";
    const value = "testValue";

    saveToSessionStorage(key, value);

    expect(sessionStorage.getItem(key)).toBe(JSON.stringify(value));
  });

  it("should save an object value to sessionStorage", () => {
    const key = "testKey";
    const value = { name: "testName", age: 30 };

    saveToSessionStorage(key, value);

    expect(sessionStorage.getItem(key)).toBe(JSON.stringify(value));
  });

  it("should save an array value to sessionStorage", () => {
    const key = "testKey";
    const value = [1, 2, 3, 4, 5];

    saveToSessionStorage(key, value);

    expect(sessionStorage.getItem(key)).toBe(JSON.stringify(value));
  });

  it("should save a number value to sessionStorage", () => {
    const key = "testKey";
    const value = 12345;

    saveToSessionStorage(key, value);

    expect(sessionStorage.getItem(key)).toBe(JSON.stringify(value));
  });

  it("should save a boolean value to sessionStorage", () => {
    const key = "testKey";
    const value = true;

    saveToSessionStorage(key, value);

    expect(sessionStorage.getItem(key)).toBe(JSON.stringify(value));
  });
});
describe("getFromSessionStorage", () => {
  beforeEach(() => {
    sessionStorage.clear();
  });

  it("should return the value from sessionStorage if it exists", () => {
    sessionStorage.setItem("testKey", "testValue");
    const result = getFromSessionStorage("testKey");
    expect(result).toBe("testValue");
  });

  it("should return an empty string if the key does not exist in sessionStorage", () => {
    const result = getFromSessionStorage("nonExistentKey");
    expect(result).toBe("");
  });
});

describe("removeItemFromSessionStorage", () => {
  beforeEach(() => {
    sessionStorage.clear();
  });

  it("should remove the item from sessionStorage", () => {
    sessionStorage.setItem("testKey", "testValue");
    removeItemFromSessionStorage("testKey");
    const result = sessionStorage.getItem("testKey");
    expect(result).toBeNull();
  });

  it("should do nothing if the key does not exist in sessionStorage", () => {
    removeItemFromSessionStorage("nonExistentKey");
    const result = sessionStorage.getItem("nonExistentKey");
    expect(result).toBeNull();
  });
});
