import { configureStore } from "@reduxjs/toolkit";
import {
  byPassOfferAllowanceHandler,
  byPassOfferAllowanceSlice,
  divisionDataSlice,
  editEventStatusHandler,
  enableAllowanceTypeSlice,
  eventDetailConfigurationSlice,
  eventDetailDataSlice,
  eventDetailsAllowance,
  eventDetailsConfiguration,
  eventDetailsDataHandler,
  eventStatusChangedSlice,
  isEventEditEnableStatusSlice,
  planEventIndicatorsSlice,
  resetEventDetailsHandler,
  setEventStatusChangedIndicators,
  setPlanEventIndicators,
} from "./event-detail-slice";
import { setDivisionData } from "libs/features/feature-primary-filters/src/lib/facet-filters/slices/facet-filter-slice";
const eventData = {
  allowance: [],
  dataFetchedFromPeriscope: false,
  divisionIds: [],
  divisions: [],
  endDate: "",
  eventCreationVehicle: {
    endDate: "",
    sourceVehicleSk: "",
    startDate: "",
    vehicleId: "",
    vehicleNm: "",
    vehicleType: {
      sourceVehicleTypeSk: "",
      vehicleTypDesc: "",
      vehicleTypeId: "",
    },
    year: "",
  },
  eventStatus: "Draft",
  eventType: "DP",
  forecast: {
    forecastSales: 0,
    forecastUnits: 0,
    quadrant: "",
  },
  id: "",
  inValidAllowances: [],
  inValidPromotions: [],
  isChangeEventTypeVisible: true,
  isEventChanged: false,
  name: "",
  planProductGroupPricing: {
    forecastAmt: "",
    planProductGroup: {
      divisionId: "",
      name: "",
      planProductGroupId: "",
      smicCategoryCode: "",
      smicGroupCode: "",
      sourceProductGroupId: "",
      supplier: {
        id: "",
        supplierId: "",
        supplierName: "",
      },
    },
    priceAmount: "",
    priceFactor: "",
    priceLimitQty: "",
    priceUnit: "",
    quadrant: "",
    supplier: "",
    userName: "",
  },
  planProductGroups: [],
  planStoreGroupType: "Division",
  startDate: "",
  startWeekVehicle: "",
  storeGroupType: "",
  storeGroups: [],
  vehicleType: "",
};
describe("Event details slice", () => {
  describe("eventDetailDataSlice", () => {
    it("should set eventDetailsDataHandler", () => {
      const initialState = eventDetailDataSlice.reducer(undefined, {
        type: undefined,
      });

      const data = { ...eventData };
      const newState = eventDetailDataSlice.reducer(
        initialState,
        eventDetailsDataHandler(data)
      );
      expect(newState.data).toEqual(data);
    });
  });

  describe("eventDetailConfigurationSlice", () => {
    it("should set eventDetailsConfiguration", () => {
      const initialState = eventDetailConfigurationSlice.reducer(undefined, {
        type: undefined,
      });

      const data = { ...eventData };
      const newState = eventDetailConfigurationSlice.reducer(
        initialState,
        eventDetailsConfiguration(data)
      );
      expect(newState.data).toEqual(data);
    });
  });
  describe("enableAllowanceTypeSlice", () => {
    it("should set eventDetailsAllowance", () => {
      const initialState = enableAllowanceTypeSlice.reducer(undefined, {
        type: undefined,
      });

      const newState = enableAllowanceTypeSlice.reducer(
        initialState,
        eventDetailsAllowance({ enableAllowance: true })
      );
      expect(newState.data).toEqual({ enableAllowance: true });
    });
  });
  describe("planEventIndicatorsSlice", () => {
    it("should set setPlanEventIndicators", () => {
      const initialState = planEventIndicatorsSlice.reducer(undefined, {
        type: undefined,
      });
      const data1 = {
        eventDetailsEventInd: false,
        allowanceEventInd: false,
        promotionEventInd: false,
        otherDetailsChangedInd: false,
        pidDetailsEventInd: false,
        planEvent: {},
        planEventPending: {},
      };
      const newState = planEventIndicatorsSlice.reducer(
        initialState,
        setPlanEventIndicators(data1)
      );
      expect(newState.data).toEqual(data1);
    });
  });
  describe("eventStatusChangedSlice", () => {
    it("should set setEventStatusChangedIndicators", () => {
      const initialState = eventStatusChangedSlice.reducer(undefined, {
        type: undefined,
      });
      const data1 = {
        isEventStatusChanged: false,
      };
      const newState = eventStatusChangedSlice.reducer(
        initialState,
        setEventStatusChangedIndicators(data1)
      );
      expect(newState.data).toEqual(data1);
    });
  });
  describe("byPassOfferAllowanceSlice", () => {
    it("should set byPassOfferAllowanceHandler", () => {
      const initialState = byPassOfferAllowanceSlice.reducer(undefined, {
        type: undefined,
      });
      const data1 = {
        isOfferBypassed: false,
      };
      const newState = byPassOfferAllowanceSlice.reducer(
        initialState,
        byPassOfferAllowanceHandler(data1)
      );
      expect(newState.data).toEqual(data1);
    });
  });
});

describe("divisionDataSlice", () => {
  let store;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        divisionData: divisionDataSlice.reducer,
      },
    });
  });

  it("should handle initial state correctly", () => {
    const initialState = store.getState().divisionData;
    expect(initialState.status).toEqual("loading");
    expect(initialState.data).toBeUndefined();
  });

  it("should handle setDivisionData correctly", () => {
    const testData = { key: "value" };

    store.dispatch(setDivisionData(testData));

    const updatedState = store.getState().divisionData;
    expect(updatedState.data).toEqual(testData);
    expect(updatedState.status).toEqual("loading");
  });
});

describe("isEventEditEnableStatusSlice", () => {
  let store;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        eventEditStatus: isEventEditEnableStatusSlice.reducer,
      },
    });
  });

  it("should handle initial state correctly", () => {
    const initialState = store.getState().eventEditStatus;
    expect(initialState.status).toEqual("loading");
    expect(initialState.data.isEventCardOpen).toBe(false);
  });

  it("should handle editEventStatusHandler correctly", () => {
    const testData = { isEventCardOpen: true };

    store.dispatch(editEventStatusHandler(testData));

    const updatedState = store.getState().eventEditStatus;
    expect(updatedState.data.isEventCardOpen).toEqual(true);
    expect(updatedState.status).toEqual("loading");
  });
});
