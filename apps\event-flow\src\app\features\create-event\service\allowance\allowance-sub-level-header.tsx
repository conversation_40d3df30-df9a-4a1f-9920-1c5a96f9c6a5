import Divider from "@albertsons/uds/molecule/Divider";
import { Tooltip } from "@albertsons/uds/molecule/Tooltip";
import { isEmpty } from "lodash";
import efConstants from "../../../../shared/ef-constants/ef-constants";
import {
  convertToUTCandPush,
  getDate,
  getEndDate,
  getStartDate,
} from "../../components/cards/allowance/stepper/common-stepper/allowance-dates/allowance-dates-service";
import {
  getAllowanceAmountsSubHeaderValue,
  getAllowanceCaseTypeValue,
} from "./allowance-service";
import { getObjectKeys } from "./allowance-stepper-service";
import { checkIfBillingInfoChanged } from "../../components/cards/allowance/preview/steppers/allowance-default-billing-info-preview";
import { roundToDecimalPlaces } from "@me/util-helpers";

const {
  ALLOWANCE_TYPES,
  OFFER_ALLOWANCE_GROUP,
  S2S_WAREHOUSE_ALLOWANCE_OPTION,
  CASE_DSD_ALLOWANCE_OPTION,
} = efConstants;
const { SCAN, SHIPTOSTORE, CASE, HEADERFLAT } = ALLOWANCE_TYPES;

interface IsubLevelInput {
  subLevelInputValues: object;
  fields: any;
}

const getSubLevelOptions = (
  subLevelInputValues: object,
  key: string,
  isSplitRequired = false
) => {
  if (isSplitRequired) {
    const keys = key?.split(".");
    return subLevelInputValues?.[keys?.[0]]?.[keys?.[1]] || {};
  }
  return subLevelInputValues?.[key] || {};
};

const checIskHfAmountStepDisable = (
  allowanceType?: string,
  createInd?: string,
  overrideHeaderFlatAmt?: number
) => {
  return (
    allowanceType === HEADERFLAT.key &&
    createInd !== HEADERFLAT.createInd[1] &&
    overrideHeaderFlatAmt
  );
};

const checIskHfAmountStepEnable = (
  allowanceType?: string,
  createInd?: string
) => {
  return (
    allowanceType === HEADERFLAT.key && createInd === HEADERFLAT.createInd[1]
  );
};

export const getAllowanceTypePerformanceSubHeader = ({
  subLevelInputValues,
  fields,
}: IsubLevelInput) => {
  const { allowanceTypeAndPerformance } = fields,
    {
      allowanceType,
      performance,
      overrideHeaderFlatAmt,
      createInd,
      perfChildInfo = [],
    } = getSubLevelOptions(
      subLevelInputValues,
      allowanceTypeAndPerformance.registerKeyName
    );

  const { offerAllowancesGroupInfoMap = {} } =
    subLevelInputValues?.["allowanceToBeCreated"] || {};
  let childPerfValue = "";
  if (
    getObjectKeys(offerAllowancesGroupInfoMap).length &&
    perfChildInfo?.length
  ) {
    childPerfValue = perfChildInfo?.[0]?.performance;
  }
  return `${
    allowanceType ? `${getAllowanceCaseTypeValue(allowanceType)}, ` : ""
  }${performance || ""}${childPerfValue ? `, ${childPerfValue} ` : ""}${
    checIskHfAmountStepDisable(allowanceType, createInd, overrideHeaderFlatAmt)
      ? `, $${roundToDecimalPlaces(Number(overrideHeaderFlatAmt), 2)}`
      : ""
  }`;
};

export const getAllowanceStoreSelectionSubHeader = ({
  subLevelInputValues,
  fields,
}: IsubLevelInput) => {
  const NUMBER_OF_STORE_GROUPS_TO_BE_SHOW = 2;

  const { allowanceStoreSelection } = fields,
    { storeGroupType, storeGroups } = getSubLevelOptions(
      subLevelInputValues,
      allowanceStoreSelection.registerKeyName
    ),
    displayStoreDroupType = storeGroupType?.name || "",
    displayStoreDroupNames = storeGroups?.length
      ? storeGroups.map(v => v.name).join(", ")
      : "";

  if (!storeGroups || !displayStoreDroupType) return "";

  if (storeGroups?.length <= NUMBER_OF_STORE_GROUPS_TO_BE_SHOW) {
    return `${displayStoreDroupType ? `${displayStoreDroupType}, ` : ""}${
      displayStoreDroupNames || ""
    }`;
  }

  return (
    <StoreGroupePopOver
      displayStoreDroupType={displayStoreDroupType}
      storeGroups={storeGroups}
      NUMBER_OF_STORE_GROUPS_TO_BE_SHOW={NUMBER_OF_STORE_GROUPS_TO_BE_SHOW}
    />
  );
};

export default function StoreGroupePopOver({
  storeGroups,
  NUMBER_OF_STORE_GROUPS_TO_BE_SHOW,
  displayStoreDroupType,
}) {
  const displayStoreGroupNamesToShow = storeGroups
    .slice(0, NUMBER_OF_STORE_GROUPS_TO_BE_SHOW)
    .map(v => v.name)
    .join(", ");

  const displayStoreGroupToHide = storeGroups.slice(
    NUMBER_OF_STORE_GROUPS_TO_BE_SHOW
  );

  return (
    <span className="flex gap-2" id="abs-allowance-sub-level-header-main-span">
      <span id="abs-allowance-sub-level-header-sub-span">
        {displayStoreDroupType ? `${displayStoreDroupType}, ` : ""}
        {displayStoreGroupNamesToShow}
      </span>

      <Divider height={20} color="text-blue-400" />

      <Tooltip zIndex={10} anchor="right">
        <Tooltip.Popover>
          <div
            className={`flex flex-col m-4 w-[240px] overflow-auto`}
            id="abs-allowance-sub-level-header-grpup"
          >
            {displayStoreGroupToHide.map(group => (
              <div
                className="h-[22px]"
                id="abs-allowance-sub-level-header-group-name"
              >
                {group.name}
              </div>
            ))}
          </div>
        </Tooltip.Popover>

        <span
          className="text-blue-600"
          id="abs-allowance-sub-level-header-more"
        >
          More
        </span>
      </Tooltip>
    </span>
  );
}

export const getAllowanceToBeCreatedSubHeader = ({
  subLevelInputValues,
  fields,
}: IsubLevelInput) => {
  const { allowanceToBeCreated } = fields,
    { option } = getSubLevelOptions(
      subLevelInputValues,
      allowanceToBeCreated.registerKeyName
    );

  const allowanceType =
    subLevelInputValues?.["allowanceTypeAndPerformance"]?.allowanceType;
  const productsArray =
    subLevelInputValues?.[allowanceToBeCreated.registerKeyName]?.productSources;

  if (
    [SHIPTOSTORE.key, SCAN.key].includes(allowanceType) &&
    productsArray?.includes("WAREHOUSE")
  ) {
    return S2S_WAREHOUSE_ALLOWANCE_OPTION;
  } else if (
    option &&
    productsArray?.length === 2 &&
    CASE.createInd[1] ===
      subLevelInputValues?.["allowanceTypeAndPerformance"]?.createInd
  ) {
    return CASE_DSD_ALLOWANCE_OPTION;
  }

  return option || "";
};

export const getAllowanceDatesSubHeader = ({
  subLevelInputValues,
  fields,
}: IsubLevelInput) => {
  return null;
  // getCommonDatesSubheader({
  //   subLevelInputValues,
  //   key: fields?.allowanceDatesData?.registerKeyName,
  // });
};

export function constructDatesArray(
  vendorsObject,
  allowanceType,
  offerAllowanceGroup,
  datesArray,
  startDate,
  endDate
) {
  if (
    allowanceType === CASE.key &&
    offerAllowanceGroup === OFFER_ALLOWANCE_GROUP.CASE.WAREHOUSE
  ) {
    vendorsObject?.forEach(vendor => {
      datesArray.push(
        convertToUTCandPush(vendor?.arrivalStartDate),
        convertToUTCandPush(vendor?.arrivalEndDate),
        convertToUTCandPush(vendor?.orderStartDate),
        convertToUTCandPush(vendor?.orderEndDate)
      );
    });
  } else {
    vendorsObject?.forEach(vendor => {
      datesArray.push(
        convertToUTCandPush(vendor?.arrivalStartDate),
        convertToUTCandPush(vendor?.arrivalEndDate)
      );
    });
  }
  datesArray.push(convertToUTCandPush(startDate), convertToUTCandPush(endDate));
}

function getCommonDatesSubheader({ subLevelInputValues, key }) {
  const allowanceType = getSubLevelOptions(
    subLevelInputValues,
    "allowanceTypeAndPerformance.allowanceType",
    true
  );

  const { dateRange = {}, vendorsObject } = getSubLevelOptions(
      subLevelInputValues,
      key,
      true
    ),
    { startDate, endDate } = dateRange;

  const datesArray = [];

  if (isEmpty(vendorsObject || []) && startDate && endDate) {
    return `${getDate(startDate)} - ${getDate(endDate)}`;
  } else {
    const offerAllowanceGroup = getSubLevelOptions(
      subLevelInputValues,
      "allowanceToBeCreated.offerAllowanceGroup",
      true
    );
    vendorsObject &&
      constructDatesArray(
        vendorsObject,
        allowanceType,
        offerAllowanceGroup,
        datesArray,
        startDate,
        endDate
      );
  }

  const displayStartDate = startDate
      ? `${getStartDate(datesArray, startDate, endDate)} - `
      : "",
    displayEndDate = endDate
      ? `${getEndDate(datesArray, startDate, endDate)}`
      : "";

  return `${displayStartDate}${displayEndDate}`;
}

export const getDSDAllowanceDatesSubHeader = ({
  subLevelInputValues,
  fields,
}: IsubLevelInput) => {
  return null;
  // return getCommonDatesSubheader({
  //   subLevelInputValues,
  //   key: fields?.allowanceDatesData?.registerKeyName,
  // });
};

export const getAllowanceCaseDatesSubHeader = ({
  subLevelInputValues,
  fields,
}: IsubLevelInput) => {
  return null;
  // return getCommonDatesSubheader({
  //   subLevelInputValues,
  //   key: fields?.allowanceDatesData?.registerKeyName,
  // });
};

export const getAllowanceAmountsSubHeader = ({
  subLevelInputValues,
  fields,
}: IsubLevelInput) => {
  const { allowanceType, createInd } = getSubLevelOptions(
    subLevelInputValues,
    "allowanceTypeAndPerformance"
  );

  const { allowanceAmountsData } = fields || {},
    { allowances = [], headerFlatAmtItems = [] } = getSubLevelOptions(
      subLevelInputValues,
      allowanceAmountsData?.registerKeyName,
      true
    ),
    isHeaderFlat = checIskHfAmountStepEnable(allowanceType, createInd),
    combinedAllowanceItems = isHeaderFlat
      ? headerFlatAmtItems
      : allowances?.reduce((data: object[] | [] = [], allowance) => {
          if (allowance?.includeInd) {
            data = [...data, ...(allowance?.allowanceItems || [])];
          }
          return data;
        }, []);

  return getAllowanceAmountsSubHeaderValue(
    allowanceType,
    combinedAllowanceItems,
    true
  );
};

export const getAllowanceBillingInfoSubHeader = ({
  subLevelInputValues,
  fields,
}: IsubLevelInput) => {
  const { billingInformationData } = fields || {},
    data = getSubLevelOptions(
      subLevelInputValues,
      billingInformationData.registerKeyName,
      true
    );

  if (data?.allowanceBillingInfo === undefined) return "";

  let label = "Unchanged";
  data?.allowanceBillingInfo?.forEach(item => {
    const value = checkIfBillingInfoChanged({
      suggestedVendorPaymentType: item.suggestedVendorPaymentType,
      suggestedAcPayableVendorNbr: item.suggestedAcPayableVendorNbr,
      suggestedAcReceivableVendorNbr: item.suggestedAcReceivableVendorNbr,
    });
    if (value === "Changed") {
      label = value;
    }
  });

  return label;
};
