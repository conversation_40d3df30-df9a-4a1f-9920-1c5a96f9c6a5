import { getAllow<PERSON><PERSON><PERSON> } from "../../../../../service/allowance/allowance-service";
import CaseAdditionalDates from "./case-additional-dates-section";
import ScanAdditionalDates from "./scan-additional-dates-section";

const AdditionalDatesSection = ({
  cardIndex,
  cardItemIndex,
  formControls,
  sectionConfiguration,
  isEditEnable = false,
  offerMapKey = "",
  vehicleFields,
  sectionKey,
}) => {
  const { getValues = () => null } = formControls || {};
  const formData = getValues() || {};
  const { allowanceType = "" } = formData || {};
  const allowanceTypeKey = getAllowanceKey(allowanceType?.toUpperCase());

  const componentMap = {
    SCAN: {
      DSD_WHSE_RETAIL_DIVISION: ScanAdditionalDates,
      DSD_LEAD_DISTRIBUTORS: ScanAdditionalDates,
    },
    CASE: {
      DSD_LEAD_DISTRIBUTORS: CaseAdditionalDates,
      WAREHOUSE_DIST_CENTERS: CaseAdditionalDates,
      DSD_WHSE_RETAIL_DIVISION: CaseAdditionalDates,
    },
    SHIP_TO_STORE: {
      DSD_LEAD_DISTRIBUTORS: CaseAdditionalDates,
      DSD_WHSE_RETAIL_DIVISION: CaseAdditionalDates,
    },
  };

  const AdditonalDatesComponent =
    componentMap?.[allowanceTypeKey]?.[offerMapKey];

  return (
    <div>
      {AdditonalDatesComponent ? (
        <AdditonalDatesComponent
          cardIndex={cardIndex}
          cardItemIndex={cardItemIndex}
          formControls={formControls}
          sectionConfiguration={sectionConfiguration}
          isEditEnable={isEditEnable}
          offerMapKey={offerMapKey}
          vehicleFields={vehicleFields}
          sectionKey={sectionKey}
        />
      ) : null}
    </div>
  );
};

export default AdditionalDatesSection;
