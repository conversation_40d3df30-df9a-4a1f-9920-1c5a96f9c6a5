import { renderHook } from '@testing-library/react';
import useGetGridConfigData from './useGetGridConfigData'; // adjust the path if needed
import { useSelectorWrap } from '@me/data-rtk';
import EventDetailsColumnConfig from '../child-data-grid/columns-config/event-details-column-config';
import OfferDetailsColumnConfig from '../child-data-grid/columns-config/offer-details-column-config';

// Mocks
jest.mock('@me/data-rtk', () => ({
  useSelectorWrap: jest.fn(),
}));

jest.mock('../child-data-grid/columns-config/event-details-column-config', () => ({
  __esModule: true,
  default: jest.fn(() => [{ headerName: 'Event Name', field: 'eventName' }]),
}));

jest.mock('../child-data-grid/columns-config/offer-details-column-config', () => ({
  __esModule: true,
  default: jest.fn(() => [{ headerName: 'Offer Name', field: 'offerName' }]),
}));

describe('useGetGridConfigData', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should return correct data when key is "events"', () => {
    (useSelectorWrap as jest.Mock)
      .mockReturnValueOnce({
        data: {
          key: 'events',
          gridConfig: { configKey: 'grid-config-events' },
          gridDataSliceKey: 'gridDataKey',
          childDataKey: 'childEvents',
        },
      })
      .mockReturnValueOnce({
        data: [
          {
            childEvents: [{ id: 1, eventName: 'Test Event' }],
          },
        ],
      });

    const { result } = renderHook(() => useGetGridConfigData({ cardIndex: 0 }));

    expect(result.current).toEqual({
      gridConfig: { configKey: 'grid-config-events' },
      columns: [{ headerName: 'Event Name', field: 'eventName' }],
      gridData: [{ id: 1, eventName: 'Test Event' }],
      key: 'events',
    });

    expect(EventDetailsColumnConfig).toHaveBeenCalledWith({
      gridConfig: { configKey: 'grid-config-events' },
      gridData: [
        {
          childEvents: [{ id: 1, eventName: 'Test Event' }],
        },
      ],
      cardIndex: 0,
    });

  });

  test('should return correct data when key is "offers"', () => {
    (useSelectorWrap as jest.Mock)
      .mockReturnValueOnce({
        data: {
          key: 'offers',
          gridConfig: { configKey: 'grid-config-offers' },
          gridDataSliceKey: 'gridDataKey',
          childDataKey: 'childOffers',
        },
      })
      .mockReturnValueOnce({
        data: [
          {
            childOffers: [{ id: 2, offerName: 'Test Offer' }],
          },
        ],
      });

    const { result } = renderHook(() => useGetGridConfigData({ cardIndex: 0 }));

    expect(result.current).toEqual({
      gridConfig: { configKey: 'grid-config-offers' },
      columns: [{ headerName: 'Offer Name', field: 'offerName' }],
      gridData: [{ id: 2, offerName: 'Test Offer' }],
      key: 'offers',
    });

    expect(OfferDetailsColumnConfig).toHaveBeenCalledWith({
      gridConfig: { configKey: 'grid-config-offers' },
      gridData: [
        {
          childOffers: [{ id: 2, offerName: 'Test Offer' }],
        },
      ],
      cardIndex: 0,
    });

  });

  test('should handle missing selectedTabConfig gracefully', () => {
    (useSelectorWrap as jest.Mock)
      .mockReturnValueOnce({ data: undefined })
      .mockReturnValueOnce({ data: [] });

    const { result } = renderHook(() => useGetGridConfigData({ cardIndex: 0 }));

    expect(result.current).toEqual({
      gridConfig: undefined,
      columns: [],
      gridData: [],
      key: undefined,
    });
  });
});
