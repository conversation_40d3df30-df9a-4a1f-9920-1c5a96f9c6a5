import efConstants from "../../../shared/ef-constants/ef-constants";
import _ from "lodash";
import { textMapping } from "../all-allowances-container/text-mapping";
import { current } from "@reduxjs/toolkit";
import { isVendorLoggedIn } from '../../../../../../../libs/utilities/src/lib/utilities';
import { setDivisionWiseShrdWhseData } from "../nationals/slices/national-main-entry-slices";
const {
  ALLOWANCE_AMT_INPUT_KEY,
  ALLOWANCE_UOM_KEY,
  SWITCH_OPTIONS,
  CA_UOM_TYPE,
  EA_UOM_TYPE,
  UOM_KEY_VALUE_MAPPER,
} = efConstants;

const updateAmountValue = ({
  amount,
  isOnlyDisplay,
  vendor,
  allowType,
  onlyUpdateEmptyAmount,
  vendorObj,
  updateForExcludeAlso = false
}) => {
  let amountValue = amount;
  if (amount || amount === "") {
    if (!isOnlyDisplay && (vendor?.includeInd || updateForExcludeAlso)) {
      const { headerOnlyAmt } = textMapping({
        allowanceTempWorkData: null,
        allow_type: allowType,
      });

      const headerOnlyAmtKey = headerOnlyAmt?.headerOnlyAmtKey;
      const oldAmount = headerOnlyAmtKey
        ? vendorObj[headerOnlyAmtKey]
        : vendorObj[ALLOWANCE_AMT_INPUT_KEY];

      amountValue =
        onlyUpdateEmptyAmount && oldAmount !== "" ? oldAmount : amount;

      if (headerOnlyAmtKey) {
        vendorObj[headerOnlyAmtKey] = amountValue;
      } else {
        vendorObj[ALLOWANCE_AMT_INPUT_KEY] = amountValue;
      }
    }
  }
  return amountValue;
};

export function getUpdateVendorObj({
  amount,
  uom,
  vendor,
  allowType,
  switchValue,
  packWhse,
  vendorPackConversionFactor,
  onlyUpdateEmptyAmount = false,
  updateForExcludeAlso = false
}: {
  amount?: any;
  uom?: any;
  vendor: any;
  allowType: any;
  switchValue?: any;
  packWhse: number;
  vendorPackConversionFactor: number;
  onlyUpdateEmptyAmount?: boolean;
  updateForExcludeAlso?: boolean
}) {
  const vendorObj = _.cloneDeep(vendor);
  const isOnlyDisplay = checkIsDisplayOnSwitch(allowType, switchValue);

  const amountValue = updateAmountValue({
    amount,
    isOnlyDisplay,
    vendor,
    allowType,
    onlyUpdateEmptyAmount,
    vendorObj,
    updateForExcludeAlso
  });
  setAmoutAndNewCost({
    vendorObj,
    amount: amountValue,
    allowType,
    packWhse,
    vendorPackConversionFactor,
    updateForExcludeAlso
  });

  if (uom) {
    vendorObj[ALLOWANCE_UOM_KEY] = uom;
  }
  return vendorObj;
}

/**
 *
 * @param param0
 * This function update allowanceNewCostKey based on key passd from getAmountCostKeys functions in vendorObj
 */
export function setAmoutAndNewCost({
  vendorObj,
  amount,
  allowType,
  packWhse,
  vendorPackConversionFactor,
  updateForExcludeAlso = false
}) {
  if (vendorObj.includeInd || updateForExcludeAlso) {
    const amountVal = parseFloat(amount);

    if (allowType === "SCAN") {
      vendorObj["newUnitCostAllow"] =
        vendorObj["unitCostAllow"] - (amountVal || 0);
      vendorObj["newCaseCostAllow"] =
        vendorObj["caseCostAllow"] -
        (amountVal * packWhse * vendorPackConversionFactor || 0);
    } else {
      vendorObj["newUnitCostAllow"] =
        vendorObj["unitCostAllow"] -
        ((amountVal / packWhse / vendorPackConversionFactor) *
          vendorPackConversionFactor || 0);
      vendorObj["newCaseCostAllow"] =
        vendorObj["caseCostAllow"] - (amountVal || 0);
    }
  }
}
export function searchAndUpdateTableData({
  currentTableData,
  filteredAndSortedIds,
  itemId,
  vendorIndex,
  amount,
  uom,
  switchValue,
  onlyUpdateEmptyAmount = false,
}: {
  currentTableData: any[];
  filteredAndSortedIds: string[];
  itemId?: string;
  vendorIndex?: string;
  amount?: any;
  uom?: any;
  switchValue?: any;
  onlyUpdateEmptyAmount?: boolean;
}) {
  const data = currentTableData?.map((item, i_index) => {
    // if we are not considering itemId in search or itemId is matched
    // also checking if id exist in filtered list or not
    if (
      (itemId === undefined || itemId === item.itemId) &&
      filteredAndSortedIds.includes(item.itemId)
    ) {
      return setVendorsForUpdatedTable({
        item,
        amount,
        uom,
        switchValue,
        vendorIndex,
        onlyUpdateEmptyAmount,
      });
    } else return item;
  });

  return data;
}
const setVendorsForUpdatedTable = ({
  item,
  amount,
  uom,
  switchValue,
  vendorIndex,
  onlyUpdateEmptyAmount,
  updateForExcludeAlso = false
}) => {
  const vendors = item?.vendorDetails.map((vendor, v_index) => {
    const allowType = item?.allowanceType;
    // if we are not considering vendorIndex in search or vendorIndex is matched
    if (vendorIndex === undefined || v_index === vendorIndex) {
      const { packWhse, vendorPackConversionFactor } = item;
      return getUpdateVendorObj({
        amount,
        vendor,
        uom,
        allowType,
        switchValue,
        packWhse,
        vendorPackConversionFactor,
        onlyUpdateEmptyAmount,
        updateForExcludeAlso
      });
    }
    return vendor;
  });

  return { ...item, vendorDetails: vendors };
};
export function updateTableDataInAllDivData({ currentState }) {
  const {
    tableData = [],
    allDivisionsTableData = [],
    ndpConfigData: {
      selectedDivision = "",
    } = {}
  } = currentState || {};
  return allDivisionsTableData?.map((divAllowObj: any) =>
    divAllowObj?.divisionId === selectedDivision
      ? {
          ...divAllowObj,
          tableData,
        }
      : divAllowObj
  );
}
/**
 *
 * @param param0
 * @returns This function will run whenver a switch value is changed
 * On switch change need to calculate the amount and Uom value and set the table data
 */
export function updateTableOnSwtich(state, { switchValue, isOnlySwitchUpdate }) {
  const {
    tableData: currentTableData = [],
    filteredAndSortedIds = [],
    ndpConfigData: { isNdpType = false },
  } = current(state)?.data || {};
  let displayPack = "";
  const updatedTableData = currentTableData?.map(item => {
    if (filteredAndSortedIds?.includes(item.itemId)) {
      const vendors = item?.vendorDetails?.map(vendor => {
        const updatedVendorObj = getUpdatedVendorOnSwitch({
          vendor,
          item,
          switchValue,
          isOnlySwitchUpdate
        });
        displayPack = updatedVendorObj?.displayPack;
        return updatedVendorObj;
      });
      return { ...item, vendorDetails: vendors, displayPack };
    } else return item;
  });
  state.data = {
    ...state.data,
    tableData: updatedTableData,
    allDivisionsTableData: isNdpType
      ? updateTableDataInAllDivData({
          currentState: { ...state?.data, tableData: updatedTableData },
        })
      : [],
  };
}
/**
 *
 * @param vendor- Vendor Obj, item- item Obj, switchSelected: selected switch Value
 * @returns
 * This function based on switch value update vendor Object with amount, Uom or Pack
 * Example - when we switch to Unit, we need to display the unit amount as display only
 * so updating vendor object with new key to display the unit amount.
 */
export const getUpdatedVendorOnSwitch = ({ vendor, item, switchValue, isOnlySwitchUpdate = false }) => {
  const vendorObj = _.cloneDeep(vendor),
    { allowanceAmount, allowUomType } = vendor,
    { vendorPackConversionFactor, packWhse, allowanceType } = item,
    { displayAmtKey, displayUOMKey } = getSwitchOptions(
      allowanceType,
      switchValue
    ) || {
      displayAmtKey: "",
      displayUOMKey: "",
    };
  const { amount, uom, displayPk, displayAmt } = caseUnitSwitchConversion({
    switchValue,
    allowanceAmount,
    vendorPackConversionFactor,
    packWhse,
    allowUomType,
  });
  vendorObj[displayAmtKey] = displayAmt || "";
  vendorObj[displayUOMKey] = UOM_KEY_VALUE_MAPPER[uom] || "";
  return {
    ...getUpdateVendorObj({
      amount,
      uom,
      vendor: vendorObj,
      allowType: allowanceType,
      switchValue,
      packWhse,
      vendorPackConversionFactor,
    }),
    displayPack: displayPk,
  };
};

/**
 *
 * @param param0 Params include switcValue, allowAMount, pack value, vendorPakConversionFactor
 * @returns This function checks the current switch value selected and convert the amount with calculation
 * and return to update in vendor Object
 * This function also switch the UOM value based on switch value selcted and update in vendor Object
 */
export const caseUnitSwitchConversion = ({
  switchValue,
  allowanceAmount,
  vendorPackConversionFactor,
  packWhse,
  allowUomType,
}) => {
  switch (switchValue) {
    case "Case": {
      return calculateCaseAmtOrUom(
        allowUomType,
        packWhse,
        allowanceAmount,
        vendorPackConversionFactor
      );
    }
    case "Unit": {
      return calculateUnitAmtOnSwitch(
        allowUomType,
        packWhse,
        allowanceAmount,
        vendorPackConversionFactor
      );
    }
    default:
      return {
        amount: allowanceAmount,
        uom: allowUomType,
        displayPk: packWhse,
        displayAmt: "",
      };
  }
};

const calculateCaseAmtOrUom = (
  allowUomType,
  packWhse,
  allowanceAmount,
  vendorPackConversionFactor
) => {
  const updatedUnitAmount = allowanceAmount
      ? parseFloat(allowanceAmount) * vendorPackConversionFactor * packWhse
      : allowanceAmount,
    updatedUomVal = allowUomType,
    roundOffUnitValue = updatedUnitAmount
      ? parseFloat(updatedUnitAmount).toFixed(2)
      : "";

  return {
    amount: allowanceAmount,
    uom: updatedUomVal,
    displayPk: 1,
    displayAmt: roundOffUnitValue,
  };
};

const calculateUnitAmtOnSwitch = (
  allowUomType,
  packWhse,
  allowanceAmount,
  vendorPackConversionFactor
) => {
  const updatedUnitAmount = allowanceAmount
      ? parseFloat(allowanceAmount) / vendorPackConversionFactor / packWhse
      : allowanceAmount,
    updatedUomVal = allowUomType,
    roundOffUnitValue = updatedUnitAmount
      ? parseFloat(updatedUnitAmount).toFixed(2)
      : "";
  return {
    amount: allowanceAmount,
    uom: updatedUomVal,
    displayPk: 1,
    displayAmt: roundOffUnitValue,
  };
};

export const updateKeysOnSwitch = itemObj => {
  const { allowanceAmount, allowUomType } = itemObj;
  if (!allowanceAmount) return {};
  return {
    allowUomType: allowUomType === EA_UOM_TYPE ? CA_UOM_TYPE : allowUomType,
  };
};
/**
 *
 * @param allowType Allowance Type
 * @param switchValue Switch value selected
 * @returns This wil return if the selected switch value is to show Only display fields
 */
export const checkIsDisplayOnSwitch = (allowType, switchValue) => {
  const switchOptions = getSwitchOptions(allowType, switchValue);
  return switchOptions?.isOnlyDisplay || false;
};
/**
 *
 * @param allowType Allowance Type
 * @param switchValue Switch value selected
 * @returns Switch option config Object
 */
export const getSwitchOptions = (allowType: string, switchValue: string) => {
  const { isSwitching = false, switchOptions = {} } =
    SWITCH_OPTIONS?.[allowType] || {};
  return isSwitching ? switchOptions?.[switchValue] || {} : {};
};

export function updateItemsAcrossAllDivOnUpdateAll({
  currentTableData,
  filteredAndSortedIds,
  itemId,
  vendorIndex,
  amount,
  uom,
  switchValue,
  onlyUpdateEmptyAmount = false,
  isConsiderFilter,
}: {
  currentTableData: any[];
  filteredAndSortedIds: string[];
  itemId?: string;
  vendorIndex?: string;
  amount?: any;
  uom?: any;
  switchValue?: any;
  onlyUpdateEmptyAmount?: boolean;
  isConsiderFilter: boolean;
}) {
  const updatedVendors = currentTableData?.map((item, i_index) => {
  const { allowanceType = "", createInd = "" } = item || {};
  const isCaseWhse =
    allowanceType === efConstants?.ALLOWANCE_TYPES.CASE.key &&
    createInd === efConstants.CASE_WHSE_CREATE_IND;
  const isShrdWhsePresent = item?.vendorDetails?.some(
    v => (v?.isSharedWhse || v?.sharedWhse) === true
  );
  const isRunShrdWhs = isCaseWhse && isShrdWhsePresent;
    const isFiltered =
      !isConsiderFilter || filteredAndSortedIds?.includes(item?.itemId);
    if ((itemId === undefined || itemId === item.itemId) && isFiltered) {
      return setVendorsForUpdatedTable({
        item,
        amount,
        uom,
        switchValue,
        vendorIndex,
        onlyUpdateEmptyAmount,
        updateForExcludeAlso: isRunShrdWhs ? true : false
      });
    } else return item;
  });

  return updatedVendors;
}

export const updatedIncludeIndValInTable = ({
  currentState,
  excludedVendors,
  excludedByKey,
}) => {
  const {tableData, ndpConfigData = {} } = currentState || {};
  const updatedTableData = tableData?.map(item => ({
    ...item,
    vendorDetails: item?.vendorDetails?.map(vendor => ({
      ...vendor,
      includeInd: !excludedVendors?.includes(vendor?.[excludedByKey]),
    })),
  }));

  const allDivisionsTableData  = ndpConfigData?.isNdpType ? updateTableDataInAllDivData({
    currentState: {...(currentState || {}), tableData: updatedTableData },
  }) : [];
  return { tableData: updatedTableData, allDivisionsTableData };
};

// 🟢 Helper function to update vendor details
const updateVendorDetails = (vendors, selectedVendors, leadDistMapping, leadDistMode) => {
  return vendors?.map(vendor => {
    const isSelectedVendor = selectedVendors?.includes(vendor?.vendorNbr);
    const isLeadDistributor = !selectedVendors?.length && !leadDistMode;
    return {
      ...vendor,
      leadDistributorInfos: isSelectedVendor
        ? leadDistMapping
            ?.find(step => step.id === vendor?.vendorNbr)
            ?.child?.map(id => ({ vendorNbr: id })) || []
        : [],
      createAllowInd: isSelectedVendor || isLeadDistributor,
      leadDistributorInd: isSelectedVendor || isLeadDistributor,
      leadDistributorMode: leadDistMode,
    };
  });
};

// 🟢 Update division-wise lead distributors
export const updateDivisionWiseLeadDistributors = (
  state,
  { allDivLeadDistMapping, leadDistMode }
) => {
  if (!state?.data?.allDivisionsTableData) return;

  const {
    ndpConfigData: { selectedDivision = "" },
    allDivisionsTableData = [],
  } = current(state)?.data || {};

  state.data.allDivisionsTableData = allDivisionsTableData?.map(division => {
    const divisionId = division?.divisionId?.toString();
    const leadDistData = allDivLeadDistMapping?.[divisionId] || {};
    const selectedVendors = leadDistData?.stepData?.map(item => item?.id) || [];
    const updatedTableData = division?.tableData?.map(item => ({
      ...item,
      vendorDetails: updateVendorDetails(
        item?.vendorDetails,
        selectedVendors,
        leadDistData?.stepData,
        leadDistMode
      ),
    }));
    return { ...division, tableData: updatedTableData };
  });
  state.data.tableData =
    state.data?.allDivisionsTableData?.find(
      divObj => divObj?.divisionId === selectedDivision
    )?.tableData || state?.data?.tableData;
};

// 🟢 Update lead distributors
export const updateLeadDistributors = (
  state,
  { leadDistMapping, leadDistMode }
) => {
  if (!state?.data?.tableData) return;

  const selectedVendors = leadDistMapping?.map(item => item?.id);
  const updatedTableData = current(state)?.data?.tableData?.map(item => ({
    ...item,
    vendorDetails: updateVendorDetails(
      item?.vendorDetails,
      selectedVendors,
      leadDistMapping,
      leadDistMode
    ),
  }));
  state.data = { ...state.data, tableData: updatedTableData };
};


export const updateAllDivisions = (state, { amount, uom, switchValue }) => {
  if (!state?.data) return;
  const { filteredAndSortedIds, allDivisionsTableData, ndpConfigData: { selectedDivision = "" } } =
    current(state)?.data || {};

  state.data.allDivisionsTableData =
    allDivisionsTableData?.map(divAllowObj => ({
      ...divAllowObj,
      tableData: updateItemsAcrossAllDivOnUpdateAll({
        currentTableData: divAllowObj?.tableData,
        filteredAndSortedIds,
        amount,
        uom,
        switchValue,
        isConsiderFilter: divAllowObj?.divisionId === selectedDivision,
      }),
    })) || [];
};

export const updateTableData = (state, payload) => {
  if (!state?.data) return;

  const {
    tableData: currentTableData,
    filteredAndSortedIds,
    ndpConfigData = {},
  } = state?.data || {};

  const { selectedDivision = "", isNdpType = false } = ndpConfigData || {};
  const { amount } = payload;

  // STEP 1: Update current table data
  const updatedTableData = searchAndUpdateTableData({
    currentTableData,
    filteredAndSortedIds,
    ...payload,
  });
  const tableDataObj = updatedTableData?.find(item => item);
  const { allowanceType = "", createInd = "" } = tableDataObj || {};
  const isCaseWhse =
    allowanceType === efConstants?.ALLOWANCE_TYPES.CASE.key &&
    createInd === efConstants.CASE_WHSE_CREATE_IND;
  const updatedAllDivisionsTableData = isNdpType
    ? updateTableDataInAllDivData({
        currentState: { ...(state?.data || {}), tableData: updatedTableData },
      })
    : [];
  const isShrdWhsePresent = updatedTableData?.find(item =>
    item?.vendorDetails?.some(v => (v?.isSharedWhse || v?.sharedWhse) === true)
  );
  const isRunShrdWhs = isNdpType && isCaseWhse && isShrdWhsePresent;
  const finalAllDivTableData = isRunShrdWhs
    ? runShardWhseDataUpdate({
        updatedTableData,
        payload,
        updatedAllDivisionsTableData,
        isNdpType,
        amount,
        selectedDivision,
      })
    : isNdpType
    ? updatedAllDivisionsTableData
    : [];
  // FINAL STATE UPDATE
  state.data = {
    ...state.data,
    tableData: updatedTableData,
    allDivisionsTableData: finalAllDivTableData,
  };
};

const runShardWhseDataUpdate = ({
  updatedTableData,
  payload,
  updatedAllDivisionsTableData,
  isNdpType,
  amount,
  selectedDivision,
}) => {
  const vendorsToSync = getVendorsToSyncFromUpdatedTableData({
    updatedTableData,
    vendorIndex: payload?.vendorIndex,
  });
  const syncedAllDivData =
    isNdpType && vendorsToSync?.length
      ? syncSharedWhseItemsAcrossDivisions({
          allDivisionsTableData: updatedAllDivisionsTableData,
          amount: amount,
          uom: payload.uom,
          vendorsToSync,
          firstDivisionTableData: updatedTableData,
          selectedDivision,
          payload,
        })
      : updatedAllDivisionsTableData;
  return syncedAllDivData;
};

export function syncSharedWhseItemsAcrossDivisions({
  allDivisionsTableData,
  payload,
  vendorsToSync,
  firstDivisionTableData,
  selectedDivision,
  amount,
  uom,
}) {
  const updatedItemIds = getUpdatedItemIds(firstDivisionTableData);
  const targetItemIds = getTargetItemIds(payload, updatedItemIds);

  return allDivisionsTableData?.map(divisionData =>
    updateDivisionIfRequired({
      divisionData,
      selectedDivision,
      targetItemIds,
      vendorsToSync,
      amount,
      uom,
      payload,
    })
  );
}

function getUpdatedItemIds(firstDivisionTableData) {
  return firstDivisionTableData
    ?.flatMap(item =>
      item?.vendorDetails?.some(v => v?.isSharedWhse === true) ? [item?.itemId] : []
    )
    ?.filter((value, index, self) => self.indexOf(value) === index);
}

function getTargetItemIds(payload, updatedItemIds) {
  return payload?.itemId ? [payload.itemId] : updatedItemIds;
}

function updateDivisionIfRequired({
  divisionData,
  selectedDivision,
  targetItemIds,
  vendorsToSync,
  amount,
  uom,
  payload,
}) {
  const { tableData: divisionTableData, divisionId } = divisionData || {};

  // Skip if it's the same division where the update originated
  if (divisionId === selectedDivision) return divisionData;

  const updatedTableData = divisionTableData?.map(item =>
    updateItemVendorsIfRequired({
      item,
      targetItemIds,
      vendorsToSync,
      amount,
      uom,
      payload,
    })
  );

  return {
    ...divisionData,
    tableData: updatedTableData,
  };
}

function updateItemVendorsIfRequired({
  item,
  targetItemIds,
  vendorsToSync,
  amount,
  uom,
  payload,
}) {
  const { allowanceType, packWhse, vendorPackConversionFactor } = item || {};

  const updatedVendorDetails = item?.vendorDetails?.map(vendor => {
    const isMatch =
      vendor?.isSharedWhse &&
      targetItemIds.includes(item?.itemId) &&
      vendorsToSync?.some(v => v?.distCenter === vendor?.distCenter);

    if (!isMatch) return vendor;

    return getUpdateVendorObj({
      amount,
      vendor,
      uom,
      allowType: allowanceType,
      switchValue: payload?.switchValue,
      packWhse,
      vendorPackConversionFactor,
      onlyUpdateEmptyAmount: payload?.onlyUpdateEmptyAmount,
      updateForExcludeAlso: true
    });
  });

  return {
    ...item,
    vendorDetails: updatedVendorDetails,
  };
}

export function getVendorsToSyncFromUpdatedTableData({
  updatedTableData,
  vendorIndex,
}) {
  if (!Array.isArray(updatedTableData)) return [];

  if (vendorIndex !== undefined) {
    return getRowLevelVendor(updatedTableData, vendorIndex);
  }

  return getPowerlineLevelVendors(updatedTableData);
}

function getRowLevelVendor(updatedTableData, vendorIndex) {
  if (!Array.isArray(updatedTableData)) return [];

  const updatedItem = updatedTableData?.find(
    item =>
      Array.isArray(item?.vendorDetails) && item?.vendorDetails?.[vendorIndex]
  );
  const updatedVendor = updatedItem?.vendorDetails?.[vendorIndex];
  if (updatedVendor?.isSharedWhse && updatedVendor?.distCenter) {
    return [{ distCenter: updatedVendor.distCenter }];
  }

  return [];
}

function getPowerlineLevelVendors(updatedTableData) {
  if (!Array.isArray(updatedTableData)) return [];
  const vendors = updatedTableData
    ?.flatMap(
      item =>
        item?.vendorDetails
          ?.filter(v => v?.isSharedWhse && v?.distCenter)
          ?.map(v => ({
            distCenter: v?.distCenter,
          })) || []
    )
    ?.filter(
      (v, i, arr) =>
        v?.distCenter &&
        arr.findIndex(x => x?.distCenter === v?.distCenter) === i
    );
  return vendors;
}

export const setSharedWhseData = (
  setIsPopupOpen = e => {},
  dispatch = e => {},
  allDivisionAllowResp?: any[]
) => {
  if (
    !Array.isArray(allDivisionAllowResp) ||
    allDivisionAllowResp?.length === 0
  )
    return allDivisionAllowResp;

  const validAllowObj = allDivisionAllowResp?.find(
    allow => allow?.allowances?.length
  );

  const isCaseWhse =
    validAllowObj?.allowances?.[0]?.createInd ===
      efConstants?.CASE_WHSE_CREATE_IND &&
    validAllowObj?.offerAllowancesGroup === efConstants?.CASE_WHSE_OFFER_GROUP;

  if (!isCaseWhse) return allDivisionAllowResp;

  const distCenterMap: Record<string, Set<string>> = {};

  allDivisionAllowResp?.forEach(({ divisionId, allowances }) => {
    allowances?.forEach(({ distCenter }) => {
      if (!distCenter) return;
      if (!distCenterMap?.[distCenter]) {
        distCenterMap[distCenter] = new Set();
      }
      distCenterMap?.[distCenter]?.add(divisionId);
    });
  });

  const updatedAllDivAllowResp = allDivisionAllowResp?.map(allowObj => {
    const updatedAllowances = allowObj?.allowances?.map(allowance => {
      const divisions = distCenterMap?.[allowance?.distCenter];
      const isSharedWhse = divisions?.size > 1;
      return {
        ...allowance,
        isSharedWhse: allowObj?.sharedWhse || isSharedWhse,
      };
    });

    return {
      ...allowObj,
      allowances: updatedAllowances ?? [],
    };
  });
  const sharedWhseDivData = updatedAllDivAllowResp?.map(allowObj => {
    const isAnyShrdWhse = allowObj?.allowances?.some(
      allowance => allowance?.sharedWhse || allowance?.isSharedWhse
    );
    return {
      divisionId: allowObj?.divisionId,
      isAnyShrdWhse: isAnyShrdWhse,
    };
  });
  dispatch?.(setDivisionWiseShrdWhseData(sharedWhseDivData || []));
  const isAnyShrdWhse = sharedWhseDivData?.some(allowObj => allowObj?.isAnyShrdWhse);
  const isModalDisplayed = sessionStorage?.getItem("isSharedWhsePresent");
  if (isAnyShrdWhse && !isModalDisplayed) {
    setIsPopupOpen(true);
    sessionStorage.setItem("isSharedWhsePresent", "true");
  }
  return updatedAllDivAllowResp;
};


