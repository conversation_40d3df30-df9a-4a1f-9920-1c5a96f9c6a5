import { useSelectorWrap } from "@me/data-rtk";
import { InputSelectAtom } from "@me/input-fields";
import {
  checkIsDateRangeWithInBounds,
  editFieldHighlight,
} from "@me/util-helpers";
import {
  getAllowanceFormReg<PERSON><PERSON>ey,
  getAllowanceTypeByPerformance,
} from "apps/event-flow/src/app/features/create-event/service/allowance/allowance-service";
import {
  useGetVehicleByStartAndEndDateQuery,
  useGetVehicleByTypeAndYearQuery,
} from "apps/event-flow/src/app/graphql/generated/schema";
import { useEffect, useRef, useState } from "react";
import { useFormContext } from "react-hook-form";
import InputDatePickerUDS from "../../../../../fields/input-date-picker-uds";
import VehicleTypes from "../../../../layout/vehicle-types-custom-date";
import { renderDummyComponent } from "../allowance-amount/allowance-amounts-services";
import { getYearFromFormDate } from "apps/event-flow/src/app/features/create-event/service/event-details/event-detail-service";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import { parseISO } from "date-fns";

const BaseAllowanceDate = ({
  stepperElement,
  offerIndex,
  allowanceIndex,
  isDateChanged,
  setIsDateChanged,
  vehicleFields,
  setVehicleFields,
  setIsVehicleDatesChanged,
  dateFieldsChange,
  setDateFieldsChange,
  keyVal,
  isInvalidDate,
  setIsInvalid,
  isEditEnable,
  isVehicleDatesChanged,
}) => {
  const { setValue, control, register } = useFormContext();
  const {
    fields: {
      vehicleTypeOrCustomDate,
      year,
      startWeekOrVehicle,
      vehicleStart,
      vehicleEnd,
      allowanceCreationVehicle,
    },
  } = stepperElement;

  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const {
    NEW_DIRECTION_FEATURES_FLAGS: { isDirectionalChangesEnable, ONE_ALLOWANCE },
  } = efConstants;
  const { data: formData } = useSelectorWrap("allowance_form_data");
  const constRegField = getAllowanceFormRegisterKey(offerIndex, allowanceIndex);

  const allowanceFormData = formData?.allowanceFormData?.[constRegField];
  const allowanceVehicleData =
    allowanceFormData?.allowanceCreationVehicle?.[keyVal];
  const vehicleRegField = `${allowanceCreationVehicle.registerKeyName}.vehicleRef`;
  const eventStartDate = eventDetailsData?.eventCreationVehicle?.startDate;
  const eventEndDate = eventDetailsData?.eventCreationVehicle?.endDate;
  const searchId = eventDetailsData?.offerAllowances?.[offerIndex]?.offerNumber;
  const offerId = eventDetailsData?.offerAllowances?.[offerIndex]?.id;
  const allow_type = getAllowanceTypeByPerformance(
    allowanceFormData?.allowanceTypeAndPerformance?.allowancePerformances
  );
  const [isInvalidDateOffer, setisInvalidDateOffer] = useState(isInvalidDate);
  const [
    startWeekOrVehicleDropdownOptions,
    setStartWeekOrVehicleDropdownOptions,
  ] = useState<
    {
      id: string | null | object | undefined;
      vehicleNm: string | null | object | undefined;
      startDate: string;
      endDate: string;
    }[]
  >([]);
  const [isDivisionChanged] = useState(true);
  const [yearOptions, setYearOptions] = useState<
    { name: string | null | undefined | number }[]
  >([]);

  const vehicleTypeID = useRef("");
  const vehicleFieldValidations =
    !isDirectionalChangesEnable &&
    !ONE_ALLOWANCE[allow_type].disableVehicleStepper;
  const getEventData = () => {
    return eventDetailsData;
  };

  useEffect(() => {
    setFormInitialData();
    if (eventDetailsData?.inValidAllowances?.length) {
      checkIsInvalidDatesAfterUpdate();
    }
  }, [allowanceFormData]);

  const [isDateInValidAfterEventChange, setIsDateInValidAfterEventChange] =
    useState(
      !!eventDetailsData?.inValidAllowances?.length &&
        !checkIsDateRangeWithInBounds(
          eventStartDate,
          eventEndDate,
          allowanceVehicleData?.dateRange?.startDate,
          allowanceVehicleData?.dateRange?.endDate
        )
    );

  const [isVehicleTypeInvalid, setIsVehicleTypeInvalid] = useState(false);

  const checkIsInvalidAllowance = () => {
    return (
      eventDetailsData?.inValidAllowances?.find(item => item === offerId) &&
      checkIsDateRangeWithInBounds(
        eventStartDate,
        eventEndDate,
        allowanceVehicleData?.dateRange?.startDate,
        allowanceVehicleData?.dateRange?.endDate
      )
    );
  };
  const checkIsInvalidDatesAfterUpdate = () => {
    if (checkIsInvalidAllowance()) {
      setIsInvalid(true);
      setisInvalidDateOffer(false);
    }
  };

  const isCustomDate = ["Custom Date", "CustomDate"].includes(
    vehicleFields.vehicleTabType
  );

  useEffect(() => {
    checkIsInvalidDatesAfterUpdate();
  }, [isInvalidDateOffer]);

  const setFormInitialData = () => {
    setValue("year", yearOptions?.[0]?.name);
    if (!isDirectionalChangesEnable) {
      setValue(
        "startWeekOrVehicle",
        allowanceVehicleData
          ? allowanceVehicleData?.vehicleRef?.vehicleNm
          : eventDetailsData?.eventCreationVehicle?.vehicleNm
      );
    } else {
      setValue(
        "startWeekOrVehicle",
        eventDetailsData?.eventCreationVehicle?.vehicleNm
      );
    }
  };

  const { data: startWeekOrVehicleOptions, refetch: fetchVehicleOptions } =
    useGetVehicleByStartAndEndDateQuery({
      variables: {
        vehicleTypId: vehicleTypeID.current,
        startDate: eventStartDate,
        endDate: eventEndDate,
      },
      skip: isCustomDate || !vehicleTypeID.current,
    });

  const { data: startWeekOrVehicleCustomOptions, refetch: fetchCustomOptions } =
    useGetVehicleByTypeAndYearQuery({
      variables: { vehicleTypId: vehicleTypeID.current, year: null },
      skip: !isCustomDate || !vehicleTypeID.current,
    });

  useEffect(() => {
    const startWeekOrVehicleCustomOptionsData =
      startWeekOrVehicleCustomOptions?.getVehicleByTypeAndYear;

    let startWeekOrVehicleAPIResponse: any[] = [];

    setIsVehicleTypeInvalid(
      !isCustomDate &&
        !startWeekOrVehicleOptions?.getVehicleByStartAndEndDate?.length
    );

    if (isCustomDate) {
      startWeekOrVehicleAPIResponse =
        startWeekOrVehicleCustomOptionsData?.map(ele => {
          return {
            id: ele?.id,
            vehicleNm: ele?.vehicleNm,
            startDate: ele?.startDate,
            endDate: ele?.endDate,
          };
        }) || [];
    } else {
      startWeekOrVehicleAPIResponse =
        startWeekOrVehicleOptions?.getVehicleByStartAndEndDate?.map(ele => {
          return {
            id: ele?.id,
            vehicleNm: ele?.vehicleNm,
            startDate: ele?.startDate,
            endDate: ele?.endDate,
          };
        }) || [];
    }

    if (startWeekOrVehicleCustomOptionsData) {
      setVehicleFields({
        ...vehicleFields,
        vehicleId: startWeekOrVehicleCustomOptionsData?.[0]?.id || "",
      });
    }
    setStartWeekOrVehicleDropdownOptions(startWeekOrVehicleAPIResponse);
  }, [startWeekOrVehicleOptions, startWeekOrVehicleCustomOptions]);

  useEffect(() => {
    if (!allowanceVehicleData) {
      const eventVehicle = eventDetailsData?.eventCreationVehicle;
      setVehicleValues(eventVehicle);
    } else {
      setDefaultValuesOnStepLoad();
    }
  }, [allowanceFormData]);

  useEffect(() => {
    if (!vehicleTypeID.current) {
      isCustomDate ? fetchCustomOptions() : fetchVehicleOptions();
    }
  }, [vehicleTypeID.current]);

  useEffect(() => {
    const startYear = getYear();
    isDateChanged &&
      setVehicleFields({
        ...vehicleFields,
        year: startYear,
      });
  }, [vehicleFields.customStartDate, vehicleFields.customEndDate]);

  useEffect(() => {
    setValue("year", yearOptions?.[0]?.name);
  }, [yearOptions]);

  function getYear() {
    let startYear = getYearFromDate(vehicleFields.customStartDate);
    let endYear = getYearFromDate(vehicleFields.customEndDate);

    if (!startYear) {
      startYear = isEditEnable
        ? getYearFromFormDate(
            parseISO(allowanceVehicleData?.dateRange?.startDate)
          )
        : getYearFromFormDate(parseISO(eventStartDate));
      endYear = isEditEnable
        ? getYearFromFormDate(
            parseISO(allowanceVehicleData?.dateRange?.endDate)
          )
        : getYearFromFormDate(parseISO(eventEndDate));
    }

    startYear === endYear
      ? setYearOptions([{ name: startYear }])
      : setYearOptions([{ name: startYear }, { name: endYear }]);

    return startYear;
  }

  function getYearFromDate(date) {
    return new Date(date?.replace(/-/g, "/")).getFullYear();
  }

  function setDefaultValuesOnStepLoad() {
    const { vehicleRef = {}, dateRange = {} } = allowanceVehicleData || {};
    const vehicleValue = {
      ...vehicleRef,
      startDate: dateRange?.startDate,
      endDate: dateRange?.endDate,
    };
    setVehicleValues(
      !isDirectionalChangesEnable
        ? vehicleValue
        : eventDetailsData?.eventCreationVehicle
    );
  }

  function setVehicleValues(vehicleData) {
    vehicleTypeID.current = vehicleData?.vehicleType?.vehicleTypeId;
    setVehicleFields({
      ...vehicleFields,
      vehicleId: vehicleData?.vehicleId,
      vehicleNm: vehicleData?.vehicleNm,
      vehicleType: vehicleData?.vehicleType,
      vehicleTypeId: vehicleData?.vehicleType?.vehicleTypeId,
      vehicleTypeDesc: vehicleData?.vehicleType?.vehicleTypDesc,
      vehicleTabType: vehicleData?.vehicleType?.vehicleTypDesc,
      customStartDate: vehicleData?.startDate,
      customEndDate: vehicleData?.endDate,
      year: yearOptions?.[0]?.name,
    });
  }

  const onChangevehicleTypeOrCustomDate = ele => {
    setIsVehicleDatesChanged(true);
    setIsDateInValidAfterEventChange(false);
    setIsVehicleTypeInvalid(true);

    vehicleTypeID.current = ele?.id;
    if (ele.type === "Vehicle") {
      setValue(startWeekOrVehicle.registerField, "");
      setVehicleFields({
        ...vehicleFields,
        vehicleNm: "",
        vehicleType: ele,
        vehicleTypeId: ele?.id,
        vehicleTypeDesc: ele?.name,
        vehicleTabType: ele?.type,
        customStartDate: "",
        customEndDate: "",
      });
      if (searchId) {
        if (searchId) {
          setDateFieldsChange({
            startWeekOrVehicleChange: false,
            vehicleStartChange: false,
            vehicleEndChange: false,
            vehicleTypeOrCustomDateChange: true,
          });
        }
      }
      setIsInvalid(false);
    } else {
      setValue(startWeekOrVehicle.registerField, "Other");
      const date = ele.customDates?.split(" - ");
      const [startDate, endDate] = date;
      setIsDateChanged(true);
      setVehicleFields({
        ...vehicleFields,
        vehicleNm: "Other",
        vehicleType: ele,
        vehicleTypeId: ele?.id,
        vehicleTypeDesc: ele?.name,
        vehicleTabType: ele?.name,
        customStartDate: startDate,
        customEndDate: endDate,
      });
      if (searchId) {
        setDateFieldsChange({
          startWeekOrVehicleChange: false,
          vehicleTypeOrCustomDateChange: true,
          vehicleStartChange: true,
          vehicleEndChange: true,
        });
      }
      setisInvalidDateOffer(false);
      setIsInvalid(true);
    }
  };

  const onChangestartWeekOrVehicle = ele => {
    setIsDateChanged(true);
    if (searchId) {
      setDateFieldsChange({
        ...dateFieldsChange,
        startWeekOrVehicleChange: true,
        vehicleStartChange: true,
        vehicleEndChange: true,
      });
    }
    setVehicleFields({
      ...vehicleFields,
      customStartDate: ele.startDate,
      customEndDate: ele.endDate,
      vehicleId: ele.id,
      vehicleNm: ele?.vehicleNm,
    });
    setIsInvalid(true);
    setIsVehicleDatesChanged(true);
    setisInvalidDateOffer(false);
  };

  const onVehicleDateChange = (field: string) => {
    if (searchId) {
      setDateFieldsChange({
        ...dateFieldsChange,
        [`${field}Change`]: true,
      });
    }
  };

  const handleYearChange = ele => {
    setVehicleFields({
      ...vehicleFields,
      year: ele?.name,
    });
  };

  const borderBlueLight = `borderBlueLight`;

  const getFieldWithHighletedWrapper = (
    field,
    fieldProps,
    isInvalid = false
  ) => {
    const fieldPath = fieldProps.registerField?.split(".");
    const fieldLen = fieldPath?.length;
    const label = fieldPath[fieldLen - 1];
    return (
      <div
        id="abs-base-allownace-dates-get-field-with-highleted-wrapper-cont"
        className={`${
          vehicleFieldValidations && isEditEnable && isInvalid
            ? "border-[red]"
            : dateFieldsChange?.[`${label}Change`]
            ? borderBlueLight
            : !isInvalid
            ? editFieldHighlight(
                fieldProps.mapperKey,
                getEventData,
                searchId,
                "allowance"
              )
            : ""
        }`}
      >
        {field}
      </div>
    );
  };

  const onClickSelect = event => {
    event?.stopPropagation();
    (event?.target as HTMLElement)
      ?.closest(".z-index-dynamic-class")
      ?.classList?.add("z-[10]");
    window?.addEventListener("click", windowRemoveIndex);
  };
  
  const windowRemoveIndex = () => {
    document
      ?.querySelectorAll(".z-index-dynamic-class")
      ?.forEach(e => e?.classList?.remove("z-[10]"));
    window?.removeEventListener("click", windowRemoveIndex);
  };

  const vehicleTypeOrCustomDateField =
    Object.keys(vehicleFields?.vehicleType || {}).length &&
    getFieldWithHighletedWrapper(
      <div
        className="w-[200px]"
        id="abs-base-allownace-dates-vehicle-type-cont"
      >
        <VehicleTypes
          onChange={onChangevehicleTypeOrCustomDate}
          fieldProps={vehicleTypeOrCustomDate}
          dynamicRegField={vehicleRegField}
          vehicleTypeProps={vehicleFields?.vehicleType}
          minDate={eventStartDate}
          maxDate={eventEndDate}
          isAllowanceForm={true}
          isInvalid={vehicleFieldValidations && isVehicleTypeInvalid}
          disabled={isDirectionalChangesEnable}
        />
      </div>,
      vehicleTypeOrCustomDate,
      isVehicleTypeInvalid
    );

  const yearField = control ? (
    <div className="max-w-[90px]" id="abs-base-allownace-dates-year-field-cont">
      {getFieldWithHighletedWrapper(
        <InputSelectAtom
          fieldProps={year}
          register={register}
          control={control}
          displayLabel={year.displayLabel}
          options={yearOptions}
          disabled={
            vehicleFieldValidations ? isCustomDate : !vehicleFieldValidations
          }
          onChange={handleYearChange}
        />,
        year
      )}
    </div>
  ) : null;

  const oldVehicle = (
    <div
      id="abs-base-allownace-dates-old-vehicle-container"
      onClick={() => {
        setIsDateInValidAfterEventChange(false);
        if (
          startWeekOrVehicleOptions?.getVehicleByStartAndEndDate?.length !== 1
        )
          return;

        onChangestartWeekOrVehicle({
          ...eventDetailsData?.eventCreationVehicle,
          id: eventDetailsData?.eventCreationVehicle?.vehicleId,
        });

        setValue(
          "startWeekOrVehicle",
          eventDetailsData.eventCreationVehicle?.vehicleNm
        );
      }}
    >
      {renderDummyComponent(
        "Start Week/Vehicle",
        true,
        allowanceVehicleData?.vehicleRef?.vehicleNm,
        true,
        ""
      )}
    </div>
  );

  const startWeekOrVehicleField =
    vehicleFieldValidations &&
    isDateInValidAfterEventChange &&
    !isCustomDate ? (
      oldVehicle
    ) : control ? (
      <div
        className="flex-1"
        id="abs-base-allownace-dates-input-select-atom-container"
      >
        {getFieldWithHighletedWrapper(
          <InputSelectAtom
            fieldProps={startWeekOrVehicle}
            register={register}
            onChange={onChangestartWeekOrVehicle}
            control={control}
            displayLabel={startWeekOrVehicle.displayLabel}
            options={startWeekOrVehicleDropdownOptions}
            disabled={
              vehicleFieldValidations ? isCustomDate : !vehicleFieldValidations
            }
            onClick={onClickSelect}
            isInvalidDate={
              vehicleFieldValidations && !isCustomDate && isInvalidDateOffer
                ? isInvalidDate
                : false
            }
          />,
          startWeekOrVehicle,
          !isCustomDate && isInvalidDateOffer ? isInvalidDate : false
        )}
      </div>
    ) : null;

  const vehicleStartField = control ? (
    <div
      className="flex-1"
      id="abs-base-allownace-dates-input-date-picker-UDS-container"
    >
      {getFieldWithHighletedWrapper(
        <InputDatePickerUDS
          fieldProps={vehicleStart}
          customDate={vehicleFields.customStartDate}
          onChange={onVehicleDateChange}
          isInvalid={vehicleFieldValidations && isInvalidDateOffer}
          disabled={!vehicleFieldValidations}
          addAsterisk={true}
          isAllowFlow={true}
        />,
        vehicleStart,
        isInvalidDateOffer
      )}
    </div>
  ) : null;

  const VehicleEndField = control ? (
    <div
      className="flex-1"
      id="abs-base-allownace-dates-get-field-with-highleted-wrapper-cont-one"
    >
      {getFieldWithHighletedWrapper(
        <InputDatePickerUDS
          fieldProps={vehicleEnd}
          customDate={vehicleFields.customEndDate}
          onChange={onVehicleDateChange}
          isInvalid={vehicleFieldValidations && isInvalidDateOffer}
          disabled={!vehicleFieldValidations}
          addAsterisk={true}
          isAllowFlow={true}
        />,
        vehicleEnd,
        isInvalidDateOffer
      )}
    </div>
  ) : null;

  return (
    <div
      className="w-full gap-4 flex inline-vehicle-info"
      id="abs-base-allownace-dates-vehicle-details"
    >
      {vehicleTypeOrCustomDateField}
      {yearField}
      {startWeekOrVehicleField}
      {vehicleStartField}
      {VehicleEndField}
    </div>
  );
};

export default BaseAllowanceDate;
