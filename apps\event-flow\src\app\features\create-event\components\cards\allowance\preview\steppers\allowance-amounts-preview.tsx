import { useFormContext } from "react-hook-form";
import {
  getAllowanceTypeByPerformance,
  getCreateIndByLocation,
  isAmtUomSameInPlanAndPending,
} from "../../../../../service/allowance/allowance-service";
// eslint-disable-next-line @nrwl/nx/enforce-module-boundaries
import StepperPreviewContainer from "libs/preview-card/src/lib/preview-card-container/preview-card-container";
// eslint-disable-next-line @nrwl/nx/enforce-module-boundaries
import PreviewCardFields from "libs/preview-card/src/lib/preview-card-fields/preview-card-fields";
import {
  getAllowIdFromOffer,
  getConstRegKey,
  getHeaderStepperValue,
  getSearchId,
  getValidCardIndex,
  isEventReturnForDraft,
  showEditedLabel,
  useGetAppBasePath,
} from "@me/util-helpers";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import { useSelectorWrap } from "@me/data-rtk";
import { ALLOWANCE_TO_BE_CREATED_CD_MAPPER } from "../../../../../constants/fields/allowance/allowance-steps-config";
import { buildURLforAllowanceDashboardSummary } from "../../stepper/allowance-amounts/allowance-amounts-helper";
import { useNavigate } from "react-router-dom";
import { getIsNationalEvent } from "../../../../../../event-types/event-types-helper";

const AllowanceAmountPreview: React.FunctionComponent<any> = ({
  previewConfigObj,
  allowStepperType,
  stepperIndex,
  steppersData,
  offerIndex,
  allowIndex,
  childClassName,
}) => {
  const { getValues } = useFormContext();
  const { fieldsHistoryKeys = null } = previewConfigObj;
  const allowanceRegisterField = getConstRegKey(
    "allowance",
    offerIndex,
    allowIndex,
    getValues
  );
  const validCardIndex = getValidCardIndex(
    "allowance",
    offerIndex,
    allowIndex,
    getValues
  );
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { isNDPAndFlagEnabled: isNational } = getIsNationalEvent(eventDetailsData?.eventType);
  const offerAllowance = getValues("offerAllowances");
  const { basePath } = useGetAppBasePath();
  const allowanceType = getAllowanceTypeByPerformance(
    offerAllowance?.[validCardIndex]?.allowances[allowIndex]?.performance
  );
  const offerAllowIdForNdp = offerAllowance?.[validCardIndex]?.allowances[allowIndex]?.offerAllowanceId || "";
  const offerAllowId = offerAllowance?.[validCardIndex]?.id;
  const createInd = getCreateIndByLocation(
    getValues(`offerAllowances[${validCardIndex}]`),
    allowIndex
  );
  const offerAllowGrpKey =
    ALLOWANCE_TO_BE_CREATED_CD_MAPPER?.[createInd]?.key || "";

  const ROUTE_PARAM = {
    eventId: eventDetailsData?.id,
    offerAllowancesId: isNational  ? offerAllowIdForNdp : offerAllowId,
    offerAllowanceGroup: offerAllowGrpKey,
    isSummary: true,
    isNdpType: isNational
  };
  const allAllowancesPath = `${basePath}/${buildURLforAllowanceDashboardSummary(
    ROUTE_PARAM
  )}`;
  const navigate = useNavigate();
  const fieldsMapper = {
    allowanceAmount:
      efConstants.ALLOWANCE_TYPES.HEADERFLAT.label === allowanceType
        ? `${allowanceRegisterField}.headerFlatAmt`
        : `planEvent.${allowanceRegisterField}.allowanceItems`,
    uom: `${allowanceRegisterField}.allowanceItems[0].uom`,
    baseKey: `${allowanceRegisterField}`,
  };

  const isSameAmtInPlanAndPending = fieldObj => {
    const {
      historyMapperKey,
      key,
      allowTypeHistoryKeyMapper = null,
    } = fieldObj || {};
    const searchId = getSearchId(
        "allowance",
        offerAllowance,
        [],
        offerIndex,
        allowIndex
      ),
      allowId = getAllowIdFromOffer(offerAllowance, offerIndex, allowIndex),
      historyKey =
        allowTypeHistoryKeyMapper?.[allowanceType] || historyMapperKey;
    const isHideForReturnAndDraft = isEventReturnForDraft(eventDetailsData, getValues);
    return isAmtUomSameInPlanAndPending(
      getValues,
      searchId,
      historyKey,
      key,
      allowId
    ) || isHideForReturnAndDraft;
  };

  const ViewItemLink = () => {
    return (
      <span className="flex leading-4 select-none whitespace-nowrap truncate text-brand-light cursor-pointer">
        <a
          className="flex text-blue-700"
          target="_blank"
          rel="noreferrer"
          onClick={() => navigate(allAllowancesPath)}
        >
          <span className="pl-1">View All Item</span>
        </a>
      </span>
    );
  };

  const getPreviewFields = (
    <PreviewCardFields
      previewConfigObj={previewConfigObj}
      fieldsMapper={fieldsMapper}
      module="allowance"
      getValues={getValues}
      cardIndex={offerIndex}
      cardItemIndex={allowIndex}
      getCustomPlanValue={null}
      className={childClassName}
      isHideStrikeOffCustom={isSameAmtInPlanAndPending}
      allowanceType={allowanceType}
    />
  );
  const getStepperHeaderLabel = () => {
    const amtFieldObj =
      previewConfigObj?.stepperHeaderFields?.["allowanceAmount"];
    const { allowanceAmount } = getHeaderStepperValue(
      [amtFieldObj],
      getValues,
      "allowance",
      fieldsMapper,
      offerIndex,
      allowIndex,
      null,
      allowanceType
    );
    return allowanceAmount;
  };
  const isShowEdited = showEditedLabel(
    fieldsHistoryKeys,
    getValues,
    offerIndex,
    allowIndex,
    "allowance"
  );

  return (
    <StepperPreviewContainer
      stepperPreviewHeaderLabel={allowStepperType}
      stepperPreviewHeaderValue={getStepperHeaderLabel()}
      steppersData={steppersData}
      stepperIndex={stepperIndex}
      isShowEdited={isShowEdited}
      CustomCompInHeader={ViewItemLink}
    >
      {getPreviewFields}
    </StepperPreviewContainer>
  );
};

export default AllowanceAmountPreview;
