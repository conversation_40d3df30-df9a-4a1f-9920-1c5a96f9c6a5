import { createGenericSlice } from "@me/data-rtk";

// slice for event progress container
export const allowanceTempWorkSlice = createGenericSlice({
  name: "allowance_temp_work",
  initialState: {
    status: "loading",
    data: {
      allowanceData: {},
      isTempLoaded: false,
    },
  },
})({
  allowanceTempWorkHandler(state, { payload }) {
    state.data = { ...state.data, ...payload };
    state.status = "finished";
  },
  allowanceTempWorkReset(state) {
    state.data = {
      allowanceData: {},
      isTempLoaded: true,
    };
  },
});
export const { allowanceTempWorkHandler, allowanceTempWorkReset } =
  allowanceTempWorkSlice.actions;
