import Checkbox from "@albertsons/uds/molecule/Checkbox";
import { useDispatch } from "react-redux";
import { setExcludedVendorData } from "./exclude-allowance-slice";
import { useSelectorWrap } from "@me/data-rtk";
import { useEffect, useState } from "react";
import {
  updateIncludeIndicatorOnExclude,
  updateItemIfEmptyByDistributorAction,
} from "../../create-event/service/slice/table-data-slice";
import {
  disableIntrnlVendor,
  getExternalDistDetails,
  isNationalType,
  setLeadsForOwnDistOnReInclude,
} from "../allowance-lead-distributors/billing-selection-utils";
import efConstants from "../../../shared/ef-constants/ef-constants";
import _ from "lodash";
import Alert from "@albertsons/uds/molecule/Alert";
import { appConstants } from "@me/utils-root-props";
import { isFeatureFlagEnabled } from "@me-upp-js/utilities";

export default function ExcludeAllowanceCheckbox({ vendorIndex, rowData }) {
  const { data: excludedVendorData } = useSelectorWrap(
      "excludedVendorForAllowance_rn"
    ),
    { data: switchValue } = useSelectorWrap("selectedSwitchValue_rn"),
    { data: allowGroupConfigObj } = useSelectorWrap(
      "allowanceTableColsGroupConfig_rn"
    );
  const { selectedDivisionData = {}, isNdpType = false } =
    useSelectorWrap("national_divisions_config")?.data || {};
  const isNational = isNationalType(isNdpType);
  const {
    data: { leadDistData = isNational ? {} : [] },
  } = useSelectorWrap("initialLeadDistSelected_rn") || {};
  const { data: leadDistSliceData } =
    useSelectorWrap("leadDistributors_rn") || {};
  const {
    data: { tableData },
  } = useSelectorWrap("allowanceTableData_rn");
  const { excludeByKey } = allowGroupConfigObj || {};
  const currentVendorObj = rowData?.vendorDetails?.[vendorIndex],
    { includeInd, modCommand: vendorModCommnd, vendorNbr } = currentVendorObj,
    [isExclude, setIsExclude] = useState(!includeInd),
    excludeAllwByKey = currentVendorObj?.[excludeByKey],
    [externalVendData, setExternalVendData] = useState<any>({
      mainVendors: [],
      otherVendors: [],
      isExternal: false,
    }),
    [showWarningAlert, setshowWarningAlert] = useState(false),
    dispatch = useDispatch();
    const { leadOptions = [], allDivisionLeadOptions = {} } = leadDistSliceData || {};
    const isCSDFeatureEnabled = isFeatureFlagEnabled(appConstants?.FEATURE_FLAGS?.CSD_VENDOR_ACCESS);
    const updatedLeadOptions = isNational
      ? allDivisionLeadOptions?.[selectedDivisionData?.divisionId] || []
      : leadOptions;

    const setExcludeData = () => {
      setExcludeDataOnLoad();
      getExternalDistDetails(
        tableData,
        updatedLeadOptions,
        setExternalVendData
      );
    };
  useEffect(() => {
    setExcludeData();
  }, []);

  useEffect(() => {
    setExcludeData();
    setIsExclude(!includeInd);
  },[selectedDivisionData?.divisionId, JSON.stringify(rowData)])

  const setExcludeDataOnLoad = () => {
    const excludedVendors: any = [];
    const excludedVendorData = rowData?.vendorDetails?.reduce(
      (result, vendorObj, v_index) => {
        result = result || {};
        const { includeInd } = vendorObj;
        !includeInd &&
          excludeByKey &&
          excludedVendors.push(vendorObj?.[excludeByKey]);
        result = {
          ...result,
          [v_index]: {
            [excludeByKey]: vendorObj?.[excludeByKey],
            isExclude: !includeInd,
          },
          excludedVendors,
        };
        return result;
      },
      {}
    );
    dispatch(setExcludedVendorData({ ...excludedVendorData }));
  };

  const onClickExclude = e => {
    const {
      target: { checked },
    } = e;
    setIsExclude(checked);
    setExcludedInfo(checked, false);
    leadDistSliceData?.leadDistMode && setLeadsOnExcludeCheck(checked);
  };
  const isExternalVendorClck = () =>
    externalVendData?.otherVendors?.includes(vendorNbr);

  const setLeadsOnExcludeCheck = checked => {
    if (!vendorNbr) return;
    const isExternalVendor = isExternalVendorClck();
    setLeadsForOwnDistOnReInclude({
      dispatch,
      checked,
      leadDistData,
      vendorNbr,
      leadDistSliceData,
      isExternalVendor,
      divisionId: isNational ? selectedDivisionData?.divisionId : "",
      isNational,
    });
    !checked && isExternalVendor && setshowWarningAlert(true);
  };

  const setExcludedInfo = (checkedValue, isOnLoad = false) => {
    const excludedVendors = checkedValue
      ? [...excludedVendorData?.excludedVendors || [], excludeAllwByKey]
      : excludedVendorData?.excludedVendors?.filter(
          vendor => vendor !== excludeAllwByKey
        );
    dispatch(
      setExcludedVendorData({
        ...excludedVendorData,
        [vendorIndex]: {
          [excludeByKey]: excludeAllwByKey,
          isExclude: checkedValue,
        },
        excludedVendors,
        divisionWiseExcludedVendors: isNational ? {
          ...(excludedVendorData?.divisionWiseExcludedVendors || {}),
          [selectedDivisionData?.divisionId]: {
            ...(excludedVendorData?.divisionWiseExcludedVendors?.[
              selectedDivisionData?.divisionId
            ] || {}),
            [vendorIndex]: {
              [excludeByKey]: excludeAllwByKey,
              isExclude: checkedValue,
            },
            excludedVendors,
          },
        } : {},
      })
    );
    if (!isOnLoad) {
      dispatch(
        updateItemIfEmptyByDistributorAction({
          vendorIndex,
          amount: "0",
          switchValue: switchValue?.selectedSwitch,
        })
      );
      dispatch(
        updateIncludeIndicatorOnExclude({
          excludedVendors,
          excludedByKey: excludeByKey,
        })
      );
    }
  };
  const isDisableExcludeBox = () => {
    const excludedVendorObj = isNational
      ? excludedVendorData?.divisionWiseExcludedVendors?.[
          selectedDivisionData?.divisionId
        ] || {}
      : excludedVendorData;
    const excludedVendors = Object.values(excludedVendorObj)?.filter(
      (item: any) => item?.isExclude
    );
    if(isCSDFeatureEnabled){
        const isAllExternal = rowData?.vendorDetails?.[vendorIndex]?.isAllExternal;
        if(isAllExternal) return rowData?.vendorDetails?.length - excludedVendors?.length <= 1
    }
    return externalVendData?.isExternal &&
      vendorModCommnd === efConstants?.MOD_COMMAND_NONE
      ? disableIntrnlVendor(externalVendData, excludedVendors)
      : rowData?.vendorDetails?.length - excludedVendors?.length <= 1;
  };
  return (
    <>
      <Alert
        isOpen={showWarningAlert}
        sticky={true}
        autoClose={true}
        timeout={10}
        dismissible={true}
        top={10}
        onClose={() => setshowWarningAlert(false)}
        variant="warning"
        className="!fixed"
      >
        <div>{efConstants.REINCLUDE_EXTERNAL_DIST_WARN_MSG}</div>
      </Alert>
      <Checkbox
        key={vendorIndex}
        className={`py-[2px] ${efConstants.componentClassName.ALLOWANCE_EXCLUDE_COMPONENT}`}
        checked={isExclude}
        data-testid="exclude-checkbox"
        onChange={e => {
          onClickExclude(e);
        }}
        disabled={
          !excludedVendorData?.[vendorIndex]?.isExclude && isDisableExcludeBox()
        }
      >
        Exclude
      </Checkbox>
    </>
  );
}
