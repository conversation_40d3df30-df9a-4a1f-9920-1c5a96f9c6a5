import { FunctionComponent } from "react";
// eslint-disable-next-line @nrwl/nx/enforce-module-boundaries
import { formatCicPpgDisplayName } from "../../../service/event-details/event-detail-service";
import { X as Delete } from "lucide-react";
import { validateNCDP } from "./event-details-card-service";
import { useSelectorWrap } from "@me/data-rtk";

interface IPromoProductGroupSummaryProps {
  planProductGroups: any;
  isAllowanceOrPromotion: boolean;
  deleteSelectedPPG: any;
}
const PromoProductGroupSummary: FunctionComponent<
  IPromoProductGroupSummaryProps
> = ({ planProductGroups, deleteSelectedPPG, isAllowanceOrPromotion }) => {
  const { data: eventTypeAndDivisionsData } = useSelectorWrap(
    "event_type_and_divisions"
  );
  const eventType = eventTypeAndDivisionsData?.eventType || "";
  const deleteIcon = () => {
    return <Delete height={16} stroke="#2B303C" strokeWidth={1.5}></Delete>;
  };
  return (
    <div className="flex flex-col p-3">
      {planProductGroups.map((ppg, index) => (
        <div
          key={index}
          className={`w-fit border select-none h-fit my-[2px] mr-1 bg-[#BCDFFD] rounded-2xl truncate max-w-full ${
            validateNCDP(eventType) ? "pointer-events-none opacity-40" : ""
          }`}
        >
          <div className="flex items-center justify-between mx-2">
            <span className="mt-[2px] text-[13px] font-bold truncate text-dark-text w-fit">
              {ppg.name?.includes("CIC")
                ? ppg.name
                : formatCicPpgDisplayName(ppg)}
            </span>
            <span
              className={!isAllowanceOrPromotion ? "cursor-pointer" : ""}
              onClick={() =>
                // eslint-disable-next-line @typescript-eslint/no-empty-function
                !isAllowanceOrPromotion ? deleteSelectedPPG(ppg) : () => {}
              }
            >
              {deleteIcon()}
            </span>
          </div>
        </div>
      ))}
    </div>
  );
};
export default PromoProductGroupSummary;
