import React from "react";
import { Info } from "lucide-react";
import Tooltip from "@albertsons/uds/molecule/Tooltip";
import Button from "@albertsons/uds/molecule/Button";
import { RBAC } from "albertsons-react-rbac";
import { useDispatch } from "react-redux";
import { eventDetailsDataHandler } from "../../../create-event/service/slice/event-detail-slice";
import { allowanceTempWorkReset } from "../../../create-event/service/slice/allowance-temp-work-slice";
import {
  allowanceFormReset,
  resetIsOfferSectionUpdated,
  resetOfferAmountsData,
  resetOfferSectionsData,
  resetOfferSectionsEnableConfig,
} from "../../../create-event/service/slice/allowance-details-slice";
import { resetEventPlanCardUpdateDataSlice } from "../../../create-event/service/slice/event-plan-slice";
import { useNavigate } from "react-router-dom";
import { eventTypeAbbreviation } from "../../constants/event-type-abbrevations";
import { eventTypes } from "../../constants/event-type-constant";
import WarningModal from "./warning-modal";
import { useGetAppBasePath } from "@me/util-helpers";
import {
  resetDivisionWiseShrdWhseData,
  resetNationalDivisionsConfig,
} from "../../../all-allowances/nationals/slices/national-main-entry-slices";
import { EVENT_TYPE } from "../../../create-event/constants/constants";

// Props interface
interface EventTypeSectionProps {
  eventType?: string | undefined;
  eventTypeName?: string;
  isChangeEventTypeVisible?: boolean;
  divisionIds: string[] | undefined;
  negotiationSimsVendors: [] | undefined;
  state: any;
}

const EventTypeSection: React.FunctionComponent<EventTypeSectionProps> = ({
  eventType,
  eventTypeName,
  isChangeEventTypeVisible,
  divisionIds,
  negotiationSimsVendors,
  state,
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { basePath } = useGetAppBasePath();
  const [isOpen, setOpen] = React.useState<boolean>(false);

  const eventTypeDetails = eventTypes.find(
    item =>
      item.headerText === eventTypeAbbreviation[eventType ?? ""] || eventType
  );

  const confirmChangeEventType = () => {
    dispatch(eventDetailsDataHandler({}));
    dispatch(allowanceTempWorkReset());
    dispatch(allowanceFormReset());
    dispatch(resetOfferAmountsData());
    dispatch(resetNationalDivisionsConfig());
    dispatch(resetDivisionWiseShrdWhseData());
    dispatch(resetIsOfferSectionUpdated());
    dispatch(resetOfferSectionsEnableConfig());
    dispatch(resetOfferSectionsData());
    dispatch(resetEventPlanCardUpdateDataSlice());
    navigate(`${basePath}/events`);
  };

  const changeEventTypeModal = () => (
    <WarningModal
      warningBodyText={
        "All draft details will be lost if you change event type"
      }
      warningTitle={"Your event details will be lost!"}
      warningLabel={"Change Event Type?"}
      cancelButtonLabel={"No, Continue Editing"}
      confirmButtonLabel={"Yes, Change Event Types"}
      isOpen={isOpen}
      setOpen={() => setOpen(false)}
      onChange={confirmChangeEventType}
    />
  );

  const changeEventTypeButton = () => (
    <Button
      variant="secondary"
      className="items-center"
      width={164}
      onClick={e => {
        e?.preventDefault();
        setOpen(true);
      }}
    >
      Change Event Type
    </Button>
  );

  return (
    <div
      className="flex items-center gap-4"
      id="abs-event-header-container-event-type-div"
    >
      <div
        className="flex justify-center gap-1"
        id="abs-event-header-container-event-type"
      >
        <span className="" id="abs-event-header-container-event-type-name">
          {eventTypeName}
        </span>
        {eventType !== EVENT_TYPE.NCDP && (
          <div className="flex items-center">
            <Tooltip
              zIndex={10}
              anchor="bottom"
              label={eventTypeDetails?.description}
            >
              <Info width={16} height={16} color="#1B6EBB" />
            </Tooltip>
          </div>
        )}
      </div>
      {isChangeEventTypeVisible ? (
        <>
          <RBAC
            divisionIds={divisionIds}
            permissionsOnly={["PROMOTION_EVENT_DETAILS_CHANGE_EVENT"]}
            simsVendors={negotiationSimsVendors}
          >
            {changeEventTypeButton()}
          </RBAC>
          {changeEventTypeModal()}
        </>
      ) : null}
    </div>
  );
};

export default EventTypeSection;
