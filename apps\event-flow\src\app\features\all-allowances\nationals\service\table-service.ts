import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import { cloneDeep, pick, sortBy, isEmpty, some, isString, isNull } from "lodash";
import { getLeadDistributorInitialState } from "../../allowance-lead-distributors/billing-selection-utils";
import { leadDistributors<PERSON><PERSON><PERSON>, leadDistributorsModeHandler, resetDivisionWiseBillingSectionData, resetDivisionWiseLeadDistData, setInitialLeadDistData, setIsLeadDataChange } from "../../../create-event/service/slice/lead-distributors-slice";
import { sortAllowanceById } from "../../../create-event/service/allowance/allowance-service";
import { transformAllowancesData } from "../../allowance-table/transformer";
import { resetTableDataSliceData, setAllDivisionTableData, setFilteredAndSortedIdsAction, setNdpConfigData, setTableDataAction, updateDivisionWiseLeadDistributorsInfoAction } from "../../../create-event/service/slice/table-data-slice";
import { resetAllDivAllowancesRespCopy, resetAllDivisionsWarningData, resetDivisionWiseShrdWhseData, resetNationalDivisionsConfig, setAlDivisionWarningData, setAllDivAllowancesRespCopy } from "../slices/national-main-entry-slices";
import { setExcludedVendorData } from "../../allowance-exclude/exclude-allowance-slice";
import { getFilteredData } from "../../allowance-table/allowances-table-filter-data";
import { getBatchPayloadByDivisions } from "../../../create-event/components/cards/offer/offer-service";
import { appConstants } from "@me/utils-root-props";

export const getFilterFormedObject = ({
  switchValue,
  vendorGridCols,
  allowGridCols,
}) => {
  const displayObj = cloneDeep(efConstants.FILTER_DISPLAY_OBJ),
    { columns: mainCols, switchColumnsCase } = vendorGridCols || {},
    formedDisplayObj = {};

  if (!allowGridCols?.length) return displayObj;
  let vendorCols = [];

  if (switchColumnsCase) {
    vendorCols = switchColumnsCase?.[switchValue?.selectedSwitch]?.columns;
  } else if (mainCols?.length) {
    vendorCols = mainCols;
  }
  const allCols = [...allowGridCols, ...vendorCols];
  allCols?.forEach(filter => {
    if (Object.prototype.hasOwnProperty.call(displayObj, filter?.id)) {
      formedDisplayObj[filter?.id] = cloneDeep(displayObj[filter?.id]);
    }
  });
  return { ...formedDisplayObj };
};

export const extractLeadOptions = (allowances) => {
  return allowances?.map(item => {
    return pick(item, ["vendorNbr", "costAreaDesc", "vendorName"]);
  });
};
export const buildLeadDistributorInitialData = (allDivisionAllowResp, leadDistMode) => {
  return allDivisionAllowResp?.reduce((acc, item) => {
    acc[item?.divisionId] = getLeadDistributorsSelected(
      item?.allowances,
      leadDistMode
    );
    return acc;
  }, {});
};
const getLeadDistributorsSelected = (allowances, leadDistMode) => {
  const selectedAllowances = leadDistMode
    ? allowances?.filter(allowObj => allowObj?.leadDistributorInd)
    : [];
  return selectedAllowances?.map(({ vendorNbr, leadDistributorInfos }) => ({
    id: vendorNbr,
    child: leadDistributorInfos?.map(distObj => distObj?.vendorNbr) || [],
  }));
};

export const handleLeadDistributorState = (
  allDivisionAllowResp,
  allDivisionLeadOptions,
  dispatch,
  currentDivisionId,
  selectedDivisionTableData,
  allDivisionsTableData
) => {
  const currentDivAllowObj = allDivisionAllowResp?.find(
    e => e?.divisionId === currentDivisionId
  );
  const leadOptions = extractLeadOptions(currentDivAllowObj?.allowances || []);
  const leadDistMode =
    currentDivAllowObj?.allowances?.[0]?.leadDistributorMode || null;
  const leadRadioMode = getLeadDistributorInitialState(
    selectedDivisionTableData?.tableData,
    allDivisionsTableData
  );
  dispatch(
    leadDistributorsHandler({
      leadOptions,
      leadDistMode,
      leadSelectionType: leadRadioMode,
      allDivisionLeadOptions,
    })
  );

  dispatch(
    leadDistributorsModeHandler({
      leadDistributorsMode: leadRadioMode,
    })
  );

  return leadDistMode;
};

export const buildAllDivisionLeadOptions = (allDivisionAllowResp) => {
  return allDivisionAllowResp?.reduce((acc, item) => {
    const divId = item?.divisionId;
    const leadOptions = extractLeadOptions(item?.allowances);
    if (leadOptions?.length) {
      acc[divId] = leadOptions;
    }
    return acc;
  }, {});
};
export const addRowIndexes = (divisionData) => {
  return {
    ...divisionData,
    tableData: divisionData?.tableData?.map((item, rowIndex) => ({
      rowIndex,
      ...item,
    })),
  };
};
export const createDivisionTableData = (
  divisionAllowObj,
  isEditMode,
  isFromTemp,
  allowanceTempData,
  headerOnlyAmt,
  allowTypeChange,
  allowanceTempWorkData,
  skipCalculateNewFinalCostInTable,
  showLeadDistributorSection,
  isAllowanceFeatureEnabled
) => {
  const sortedAllowances = sortAllowancesByIdIfNeeded(
    divisionAllowObj?.allowances
  );

  const allowancesToUse = isEditMode
    ? sortedAllowances
    : isFromTemp
      ? allowanceTempData
      : sortedAllowances;

  return {
    divisionId: divisionAllowObj?.divisionId,
    tableData: transformAllowancesData(
      cloneDeep(allowancesToUse),
      headerOnlyAmt,
      (isEditMode && !allowTypeChange) || isFromTemp,
      isEditMode,
      allowanceTempWorkData?.allowanceType,
      false,
      false,
      skipCalculateNewFinalCostInTable,
      showLeadDistributorSection,
      isAllowanceFeatureEnabled && allowTypeChange
    ),
  };
};

// Helper functions
const sortAllowancesByIdIfNeeded = (allowances) => {
  return (allowances?.length && sortAllowanceById([...allowances])) || allowances;
};

export const setDataInTableSlice = ({
  dispatch,
  allDivisionsTableData,
  allDivisionAllowResp,
  currentDivisionId,
  isSummary = false
}) => {
  if (allDivisionsTableData?.length) {
    dispatch(setAllDivisionTableData(allDivisionsTableData));
  }
  const selectedDivisionTableData = allDivisionsTableData?.find(
    division => division?.divisionId === currentDivisionId
  );
  dispatch?.(setTableDataAction(selectedDivisionTableData?.tableData));
  dispatch?.(
    setNdpConfigData({ isNdpType: true, selectedDivision: currentDivisionId })
  );
  dispatch?.(setAllDivAllowancesRespCopy(allDivisionAllowResp));
  !isSummary && setLeadDistributorData({
    dispatch,
    allDivisionAllowResp,
    currentDivisionId,
    selectedDivisionTableData,
    allDivisionsTableData
  });
};

export const setLeadDistributorData = ({
  dispatch,
  allDivisionAllowResp,
  currentDivisionId,
  selectedDivisionTableData,
  allDivisionsTableData
}) => {
  const allDivisionLeadOptions =
    buildAllDivisionLeadOptions(allDivisionAllowResp);

  const leadDistMode = handleLeadDistributorState(
    allDivisionAllowResp,
    allDivisionLeadOptions,
    dispatch,
    currentDivisionId,
    selectedDivisionTableData,
    allDivisionsTableData
  );
  const leadDistInitialData = buildLeadDistributorInitialData(
    allDivisionAllowResp,
    leadDistMode
  );
  dispatch(
    setInitialLeadDistData({
      leadDistData: leadDistMode ? leadDistInitialData : [],
    })
  );
};
export const setExcludeDataOnLoad = ({
  allDivisionsTableData,
  excludeByKey,
  dispatch,
  excludedVendorData,
}) => {
  const excludedVendorsByDivision = {};
  allDivisionsTableData?.forEach(divTableData => {
    const validTableEntry = divTableData?.tableData?.find(e => e)// Get valid tableData entry
    if (!validTableEntry) return; // Skip if no tableData

    const excludedVendors: any = [];
    const excludedVendorData = validTableEntry?.vendorDetails?.reduce(
      (result, vendorObj, v_index) => {
        result = result || {};
        const { includeInd } = vendorObj || {};

        if (!includeInd && excludeByKey) {
          excludedVendors.push(vendorObj?.[excludeByKey]);
        }

        return {
          ...result,
          [v_index]: {
            [excludeByKey]: vendorObj?.[excludeByKey],
            isExclude: !includeInd,
          },
        };
      },
      {}
    );

    excludedVendorsByDivision[divTableData?.divisionId] = {
      ...excludedVendorData,
      excludedVendors,
    };
  });
  dispatch(
    setExcludedVendorData({
      ...excludedVendorData,
      divisionWiseExcludedVendors: excludedVendorsByDivision,
    })
  );
};
export const setTableDataForDiv = ({
  currentDivisionId,
  allDivisionsTableData,
  dispatch,
}) => {
  if (currentDivisionId && allDivisionsTableData?.length) {
    const selectedDivisionTableData = allDivisionsTableData?.find(
      division => division?.divisionId === currentDivisionId
    );

    dispatch(
      setNdpConfigData({
        isNdpType: true,
        selectedDivision: currentDivisionId,
      })
    );
    dispatch(setTableDataAction(selectedDivisionTableData?.tableData));
  }
};
//on table filter changes update the filterAndSortedIds
export const setTableDataOnPgLoad_Filter = ({
  selectedFiltersData,
  tableData,
  dispatch,
}) => {
  const { isFilterApplied, isFilterReset } = selectedFiltersData || {};
  let table_Data = tableData;
  if (isFilterApplied || isFilterReset) {
    table_Data = filterData({ tableData, selectedFiltersData });
  }
  const data: any = sortSaveData(table_Data);
  dispatch?.(setFilteredAndSortedIdsAction(data?.map(item => item?.itemId)));
};
const filterData = ({ tableData, selectedFiltersData }) => {
  let data;
  if (selectedFiltersData?.isFilterReset) {
    //If no filter applied / filter is reset, display the entire data from the response.
    data = tableData;
  } else if (selectedFiltersData?.filteredOptions) {
    //If filter is applied, display the filtered data
    data = getFilteredData({
      selectedFiltersData,
      transformed_data: tableData,
    });
  }
  return data;
};

//By default, sort Items By Descriptions before populating it in form
export const sortSaveData = data => {
  const sortItemsByDescriptions = obj => {
    return obj?.[efConstants?.ALLOWANCE_ITEM_DESC_KEY];
  };

  const sorted_plannedProductGroupItems = sortBy(
    data,
    sortItemsByDescriptions
  );
  return sorted_plannedProductGroupItems;
};

export const getBatchPayload = ({ eventId, allowGroup, divisionsList }) => {
  return getBatchPayloadByDivisions(
    {
      URL_PARAMS: [eventId, allowGroup],
    },
    divisionsList?.map(division => division?.divisionId),
    { skipOverlaps: false }
  );
};
export const getBatchPayloadForItemDifference = ({ offerAllowancesId, divisionsList }) => {
  return getBatchPayloadByDivisions(
    {
      URL_PARAMS: [offerAllowancesId],
    },
    divisionsList?.map(division => division?.divisionId),
    { skipOverlaps: false }
  );
}
export const updateLeadDataInSlice = ({
  dispatch,
  stepsData,
  isLeadChange,
  leadDistMode,
}) => {
  dispatch(
    updateDivisionWiseLeadDistributorsInfoAction({
      leadDistMode,
      allDivLeadDistMapping: stepsData,
    })
  );
  dispatch(
    setIsLeadDataChange({
      isLeadChange: !isLeadChange,
    })
  );
};

export const clearTableSlices = ({ dispatch }) => {
  dispatch(setTableDataAction([]));
  dispatch(setFilteredAndSortedIdsAction([]));
  dispatch(resetTableDataSliceData());
};
export const resetSlicesForMainEntry = ({ dispatch }) => {
  clearTableSlices({ dispatch });
  dispatch(
    leadDistributorsHandler({
      stepData: [],
      leadOptions: [],
      isLeadChange: false,
      leadDistMode: null,
      leadSelectionType: null,
      allDivisionLeadOptions: [],
      allDivisionStepsData: {},
    })
  );
  dispatch(resetNationalDivisionsConfig());
  dispatch(resetAllDivisionsWarningData());
  dispatch(resetAllDivAllowancesRespCopy());
  dispatch(resetDivisionWiseLeadDistData());
  dispatch(resetDivisionWiseBillingSectionData());
  dispatch(resetDivisionWiseShrdWhseData());
};
export const setAllowRespData = ({
  allowGroup,
  allowancesResp,
  offerAmountsDataObj,
}) => {
  const amountsApiDataFromSlice =
    offerAmountsDataObj?.offerAmounts?.[allowGroup] || null;
  const allowDataToSet =
    appConstants.IS_ENABLE_REDESIGN_ALLOWANCE_CHANGES &&
      amountsApiDataFromSlice?.length
      ? amountsApiDataFromSlice
      : allowancesResp;
  return allowDataToSet;
};

export const getEmptyFields = ({ tableData: data }, isDistCenter = false, isHeaderOnlyAmt = false) => {
  //Returns details of empty fields with itemId and vendor Id
  const checkForEmptyField = vendor => {
    const valTocheck = isHeaderOnlyAmt ? vendor?.headerFlatAmt : vendor?.allowanceAmount;
    return valTocheck === null || valTocheck === "" || valTocheck === ".";
  };
  const getEmptyAllowancesByItemId = itemId => {
    return data
      ?.filter(item => item?.itemId === itemId)
      ?.flatMap(item =>
        item?.vendorDetails
          ?.filter(vendor => vendor?.includeInd && checkForEmptyField(vendor))
          ?.map(
            vendor =>
              vendor[
              isDistCenter ? efConstants?.VENDOR_DIST_CENTER_KEY : efConstants?.TRANSFORMED_VENDOR_KEY
              ]
          )
      );
  };

  const emptyFieldsDetails = {};

  data?.forEach(item => {
    const emptyAllowances = getEmptyAllowancesByItemId(item.itemId);
    if (emptyAllowances?.length > 0) {
      emptyFieldsDetails[item?.itemId] = emptyAllowances;
    }
  });

  return { emptyFieldsDetails };
};
export const checkForEmptyFieldsDivisionWise = (
  allDivisionsTableData,
  isDistCenter,
  dispatch,
  divisionErrorData,
  isHeaderOnlyAmt
) => {
  const emptyFieldsDivisionWise = {};
  allDivisionsTableData?.forEach(divisionData => {
    const { emptyFieldsDetails } =
      getEmptyFields(divisionData, isDistCenter, isHeaderOnlyAmt) || {};
    emptyFieldsDivisionWise[divisionData?.divisionId] = emptyFieldsDetails;
  });
  const errorObject = Object.keys(emptyFieldsDivisionWise)?.reduce(
    (acc, divId) => {
      const isError = !isEmpty(emptyFieldsDivisionWise?.[divId]) ? true : false;
      return {
        ...acc,
        [divId]: {
          isError,
          emptyFieldsDetails: emptyFieldsDivisionWise?.[divId]
        },
      };
    },
    {}
  );
  const isAnyFieldEmpty = errorObject ? Object.keys(errorObject)?.some(e => errorObject?.[e]?.isError) : false;
  dispatch(
    setAlDivisionWarningData({
      ...divisionErrorData,
      divisionErrObj: {
        ...divisionErrorData?.divisionErrObj,
        ...(errorObject || {}),
      },
    })
  );
  return isAnyFieldEmpty;
};

const getErrorClass = (
  isError,
  emptyFieldsDetails,
  distCenter,
  defaultClass,
  getInputTextClass = arg => defaultClass
) => {
  if (!isError) return defaultClass;
  const hasError = emptyFieldsDetails
    ? Object.values(emptyFieldsDetails)?.some((arr: any) =>
      arr?.includes(distCenter)
    )
    : false;
  return hasError ? getInputTextClass?.(true) : defaultClass;
};

export const calculateErrorClass = ({
  divisionErrObj,
  selectedDivisionData,
  distCenter,
  inputTextClass,
  getInputTextClass = arg => inputTextClass,
}) => {
  if (!divisionErrObj || !selectedDivisionData) return inputTextClass;
  const divisionId = selectedDivisionData?.divisionId;
  const divisionError = divisionErrObj?.[divisionId];
  if (!divisionError) return inputTextClass;
  const { emptyFieldsDetails = {}, isError = false } = divisionError;
  const defaultErrorClass = getInputTextClass?.(false);
  const errorClass = getErrorClass?.(
    isError,
    emptyFieldsDetails,
    distCenter,
    defaultErrorClass,
    getInputTextClass
  );
  return errorClass !== inputTextClass ? errorClass : inputTextClass;
};

