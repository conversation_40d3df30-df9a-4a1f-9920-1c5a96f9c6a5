import '@testing-library/jest-dom';
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { useFormContext } from "react-hook-form";
import { useSelectorWrap } from "@me/data-rtk";
import StoreGroupTypeField from "./store-group-type-field";

// Mock dependencies
jest.mock("react-hook-form", () => ({
  useFormContext: jest.fn(),
}));

jest.mock("@me/data-rtk", () => ({
  useSelectorWrap: jest.fn(),
}));

jest.mock("@me/input-fields", () => ({
  InputSelect: ({ onChange, fieldProps, disabled, defaultValue }) => (
    <select
      data-testid="input-select"
      disabled={disabled}
      defaultValue={defaultValue}
      onChange={(e) => onChange({ groupInd: e.target.value })}
    >
      {fieldProps?.options?.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  ),
}));

describe("StoreGroupTypeField", () => {
  const mockSetValue = jest.fn();
  const mockSetFormFields = jest.fn();
  const mockSetStoreGroupFieldChanged = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock useFormContext
    (useFormContext as jest.Mock).mockReturnValue({
      setValue: mockSetValue,
    });

    // Mock useSelectorWrap
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: {
        eventTypeAndDivisionsData: {
          eventType: "someEventType",
        },
      },
    });
  });

  it("renders the InputSelect component when planStoreGroupType is provided", () => {
    const planStoreGroupType = {
      options: [
        { value: "type1", label: "Type 1" },
        { value: "type2", label: "Type 2" },
      ],
    };

    render(
      <StoreGroupTypeField
        planStoreGroupType={planStoreGroupType}
        storeGroups={{ registerField: "storeGroups" }}
        formFields={{ enableStoreGroupType: true, groupInd: "type1" }}
        setFormFields={mockSetFormFields}
        setStoreGroupFieldChanged={mockSetStoreGroupFieldChanged}
      />
    );

    const selectElement = screen.getByTestId("input-select");
    expect(selectElement).toBeInTheDocument();
    expect(selectElement).toHaveValue("type1");
  });

  it("does not render the InputSelect component when planStoreGroupType is not provided", () => {
    render(
      <StoreGroupTypeField
        planStoreGroupType={null}
        storeGroups={{ registerField: "storeGroups" }}
        formFields={{ enableStoreGroupType: true }}
        setFormFields={mockSetFormFields}
        setStoreGroupFieldChanged={mockSetStoreGroupFieldChanged}
      />
    );

    expect(screen.queryByTestId("input-select")).not.toBeInTheDocument();
  });

it("calls onGroupTypeChangeHandler when the group type is changed", () => {
  const planStoreGroupType = {
    options: [
      { value: "type1", label: "Type 1" },
      { value: "type2", label: "Type 2" },
    ],
    registerField: "planStoreGroupType",
  };

  render(
    <StoreGroupTypeField
      planStoreGroupType={planStoreGroupType}
      storeGroups={{ registerField: "storeGroups" }}
      formFields={{ enableStoreGroupType: true, groupInd: "type1" }}
      setFormFields={mockSetFormFields}
      setStoreGroupFieldChanged={mockSetStoreGroupFieldChanged}
    />
  );

  const selectElement = screen.getByTestId("input-select");
  fireEvent.change(selectElement, { target: { value: "type2" } });

  expect(mockSetValue).toHaveBeenCalledWith("storeGroupType", "type2");
  expect(mockSetValue).toHaveBeenCalledWith("planStoreGroupType", {
    groupInd: "type2",
  });
  expect(mockSetValue).toHaveBeenCalledWith("storeGroups", []);
  expect(mockSetStoreGroupFieldChanged).toHaveBeenCalledWith(true);
});

  it("disables the InputSelect when validateNCDP or disableStoreGroupType is true", () => {
    const planStoreGroupType = {
      options: [
        { value: "type1", label: "Type 1" },
        { value: "type2", label: "Type 2" },
      ],
    };

    render(
      <StoreGroupTypeField
        planStoreGroupType={planStoreGroupType}
        storeGroups={{ registerField: "storeGroups" }}
        formFields={{
          enableStoreGroupType: true,
          disableStoreGroupType: true,
        }}
        setFormFields={mockSetFormFields}
        setStoreGroupFieldChanged={mockSetStoreGroupFieldChanged}
      />
    );

    const selectElement = screen.getByTestId("input-select");
    expect(selectElement).toBeDisabled();
  });
});