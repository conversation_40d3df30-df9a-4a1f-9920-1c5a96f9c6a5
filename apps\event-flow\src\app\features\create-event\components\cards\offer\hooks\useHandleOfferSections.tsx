import { useDispatch } from "react-redux";
import { DEFAULT_OFFER_SECTIONS, OFFER_ALLOWANCE } from "../offer-flow-config";
import { setOfferSectionsData } from "../../../../service/slice/allowance-details-slice";

const useHandleOfferSections = () => {
  const dispatch = useDispatch();

  const setOfferSections = ({ createInd }: { createInd: string }) => {
    let sections: any = DEFAULT_OFFER_SECTIONS;
    if (createInd) {
      sections = OFFER_ALLOWANCE.sections.reduce(
        (activeSections: any = [], section) => {
          const offerConfig = OFFER_ALLOWANCE?.[section];
          if (
            offerConfig &&
            !offerConfig?.excluded &&
            (offerConfig?.includeAll ||
              offerConfig?.createInd?.includes(createInd))
          )
            activeSections.push(offerConfig);
          return activeSections;
        },
        []
      );
      sections = sections?.length ? sections : DEFAULT_OFFER_SECTIONS;
      dispatch(setOfferSectionsData(sections));
    } else dispatch(setOfferSectionsData(sections));
    return sections;
  };

  return { setOfferSections };
};

export default useHandleOfferSections;
