import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import AllAllowancesTopRight from "./all-allowances-top-right";
import { MockedProvider } from "@apollo/client/testing";
import { app_store } from "@me/data-rtk";
import { Provider } from "react-redux";
import AllowancesContainer from "./all-allowances-container/all-allowances-container";
import { BrowserRouter } from "react-router-dom";
import * as selectors from "@me/data-rtk";

const Wrapper = props => {
  return <Provider store={app_store}>{props.children}</Provider>;
};

jest.mock("@me/util-helpers", () => ({
  ...jest.requireActual("@me/util-helpers"),
  useGetQueryParams: () => ({
    queryParamValue: {
      isSummary: true,
      eventId: "123",
      offerAllowancesId: null,
    },
  }),
  useGetAppBasePath: () => ({ basePath: "" }),
}));

jest.mock("react-pdf", () => ({
  Document: jest.fn(({ children }) => children),
  Page: jest.fn(() => <div data-testid="mock-page"></div>),
  pdfjs: {
    GlobalWorkerOptions: {
      workerSrc: "",
    },
    version: "2.10.377",
  },
}));

describe("AllowancesContainer", () => {
  xit("should render the top section with the correct text", () => {
    const { queryByText } = render(
      <Wrapper>
        <BrowserRouter>
          <AllAllowancesTopRight offerId={"123"} isDsd={true}/>
          <AllowancesContainer />
        </BrowserRouter>
      </Wrapper>
    );

    expect(screen.queryByText("Allowance Amounts")).toBeNull();
    // expect(screen.queryByText("Allowance Amounts")).toBeTruthy();
    expect(screen.queryByText("|")).toBeNull();
    expect(screen.queryByText("Scan - DSD")).toBeNull();
    expect(
      screen.queryByText(
        "You can update allowance amounts for all the Distributors and items together or individually."
      )
    ).toBeNull();
  });

  it("should render the AllAllowancesTopRight component", () => {
    const { queryByText } = render(
      <Wrapper>
        <MockedProvider>
          <AllAllowancesTopRight offerId={"123"} isDsd={true}/>
          <AllowancesContainer />
        </MockedProvider>
      </Wrapper>
    );

    expect(screen.queryByText("Cancel")).toBeNull();
  });

  it("should render the TableTopAllowancesContainer component", () => {
    const { queryByText } = render(
      <Wrapper>
        <MockedProvider>
          <AllAllowancesTopRight offerId={"123"} isDsd={true}/>
          <AllowancesContainer />
        </MockedProvider>
      </Wrapper>
    );
    expect(screen.queryByText("Update All")).toBeNull();
    expect(screen.queryByText("Filters")).toBeNull();
    expect(screen.queryByText("Reset")).toBeNull();
  });

  it("should render the AllowancesTable component", () => {
    const { queryByText } = render(
      <Wrapper>
        <MockedProvider>
          <AllAllowancesTopRight offerId={"123"} isDsd={true}/>
          <AllowancesContainer />
        </MockedProvider>
      </Wrapper>
    );
    expect(screen.queryByText("NO RECORDS FOUND")).toBeNull();
  });
});
describe("AllowancesContainer test suit", () => {
  beforeEach(() => {
    jest.mock("../create-event/service/apis/allowance-api", () => ({
      usePostAllowanceTempWorkDataMutation: jest.fn().mockReturnValue([
        jest.fn().mockReturnValue({
          allowanceData: {
            allowanceType: "SCAN",
            allowanceTypeSpecification: {
              scanAllow: {
                allowancesMap: { key1: "value1", key2: "value2" },
                createInd: true,
              },
            },
          },
        }),
        {
          isSuccess: true,
          isLoading: false,
          isSaveApiError: false,
          error: {},
        },
      ]),
    }));
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      switch (sel_name) {
        case "allowanceTableData_rn":
          return {
            data: {
              filteredAndSortedIds: ["36200012", "36200011"],
              tableData: [
                {
                  rowIndex: 0,
                  itemId: "36200011",
                  itemDesc: "LUCERNE SOUR CREAM LIGHT",
                  primaryUpc: "002113007305",
                  caseUpc: "1002113007305",
                  itemUpcs: ["002113007305", "1002113007305"],
                  effectiveEndDate: "0001-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "8.0 OZ",
                  vendorPackConversionFactor: 1,
                  caseListCost: 9.84,
                  unitListCost: 0.82,
                  unitCostAllow: 0.7617,
                  caseCostAllow: 9.14,
                  allowanceAmount: 0,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  newUnitCostAllow: 0.7617,
                  newCaseCostAllow: 9.14,
                  overlaps: {
                    caseAllow: 0.1,
                    caseUnitizedAllow: 0.0083,
                    shipAllow: 0.6,
                    shipUnitizedAllow: 0.05,
                    scanAllow: 0,
                    scanCaseAllow: 0,
                    unitizedAllowanceOverlapsSum: 0.0583,
                    caseAllowanceOverlapsSum: 0.7,
                  },
                  distCenter: "DDNC",
                  vendorDetails: [
                    {
                      vendorNbr: "",
                      costAreaDesc: "",
                      createAllowInd: true,
                      headerFlatAmt: 0,
                      distCenter: "DDNC",
                      caseListCost: 9.84,
                      unitListCost: 0.82,
                      unitCostAllow: "0.76",
                      allowanceAmount: "",
                      allowUomType: "EA",
                      newUnitCostAllow: 0.7617,
                      caseCostAllow: "9.14",
                      newCaseCostAllow: 9.14,
                      shippingCost: "9.84",
                    },
                  ],
                },
              ],
            },
          };
        case "selectedSwitchValue_rn":
          return {
            data: { switchValue: { selectedSwitch: "Case" } },
          };
        case "leadDistributorsMode_rn": {
          return {
            data: {leadDistributorsMode: "LEAD" },
          };
        }
        case "allowancesRespCopy_rn":
          return {
            data: {},
          };
        case "leadDistributorsChanged_rn":
          return {
            data: {
              isLeadChange: false,
            },
          };
        case "headerOnlyAmt_rn":
          return {
            data: {},
          };
        case "allowanceTableColsGroupConfig_rn":
          return {
            data: { allowGroupConfigObj: { excludeByKey: "vendorNbr" } },
          };
        case "allowance_temp_work":
          return {
            data: {
              allowanceData: {
                allowanceType: "SCAN",
                allowanceTypeSpecification: {
                  scanAllow: {
                    allowancesMap: { key1: "value1", key2: "value2" },
                    createInd: true,
                  },
                },
              },
            },
          };
        case "event_details_data":
          return {
            data: {
              id: 1,
            },
          };
        case "isUpdateAllClicked_rn":
          return {
            data: { isUpdateAll: false },
          };
        case "selectedFilters_rn":
          return {
            data: {
              isAllowancesDistributorType: "ALL",
              isAllowancefilterType: 1,
            },
          };
        case "leadDistributors_rn":
          return {
            data: { leadOptions: [], stepData: [] },
          };
        case "allow_type_change_rn": {
          return {
            data: {  },
          };
        }
        default:
          return null;
      }
    });
  });
  xit("should render the top section with the correct text", async () => {
    render(
      <Wrapper>
        <BrowserRouter>
           <AllAllowancesTopRight offerId={"123"} isDsd={true}/>
          <AllowancesContainer />
        </BrowserRouter>
      </Wrapper>
    );
    await waitFor(() => {
      const parentElement = screen.getByTestId("cta-atr-save-allowance");
      const ctaNavigateToCancel = screen.getByTestId("cta-navigate-to-cancel");
      const saveButton = parentElement.querySelector("button");
      fireEvent.click(ctaNavigateToCancel);
      if (saveButton) {
        fireEvent.click(saveButton);
      }
    });
    expect(screen.queryByText("Allowance Amounts")).toBeTruthy();
  });
});
