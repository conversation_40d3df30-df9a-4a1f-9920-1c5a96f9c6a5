import { CommonModal } from "@me/event-flow/components";

export default function ChildEventActionModal({
  modalProps
}) {
  const {isModalPopupOpen, minHeight, height,setModalPopupOpen,confirmBtnHandler, title, warningMessage, infoMessage, confirmBtnTitle, cancelBtnTitle, cancelBtnHandler, onClose, minCancelBtnWidth, minBtnWidth} = modalProps;
  return (
    <CommonModal
      isModalPopupOpen={isModalPopupOpen}
      setModalPopupOpen={setModalPopupOpen}
      title={title}
      warningMessage={warningMessage}
      infoMessage={infoMessage}
      confirmBtnTitle={confirmBtnTitle}
      cancelBtnTitle={cancelBtnTitle}
      showHideBtns
      modalNameHandler={confirmBtnHandler}
      cancelBtnHandler={cancelBtnHandler}
      onClose={onClose || cancelBtnHandler}
      minCancelBtnWidth={minCancelBtnWidth}
      minBtnWidth={minBtnWidth}
      height={height}
      minHeight={minHeight}

    />
  );
}
