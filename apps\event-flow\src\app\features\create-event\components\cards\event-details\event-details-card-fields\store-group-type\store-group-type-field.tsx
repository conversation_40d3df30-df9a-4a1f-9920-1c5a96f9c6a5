import { useSelectorWrap } from "@me/data-rtk";
import { InputSelect } from "@me/input-fields";
import React, { memo } from "react";
import { useFormContext } from "react-hook-form";
import { validateNC<PERSON> } from "../../event-details-card-service";

function StoreGroupTypeField({
  planStoreGroupType,
  storeGroups,
  formFields,
  setFormFields,
  setStoreGroupFieldChanged,
}) {
  const { setValue } = useFormContext();
  const onGroupTypeChangeHandler = element => {
    setFormFields(prevState => {
      return {
        ...prevState,
        disableStoreGroups: false,
        groupInd: element?.groupInd,
      };
    });
    setValue("storeGroupType", element?.groupInd);
    setValue(planStoreGroupType.registerField, element);
    setValue(storeGroups.registerField, []);
    setStoreGroupFieldChanged(true);
  };

  const { data: eventTypeAndDivisionsData } = useSelectorWrap(
    "event_type_and_divisions"
  );
  const eventType = eventTypeAndDivisionsData?.eventType || "";

  return (
    <div className="w-1/4 abs-ef-store-group-type-container">
      {planStoreGroupType ? (
        <InputSelect
          onChange={onGroupTypeChangeHandler}
          fieldProps={planStoreGroupType}
          enableStoreGroupType={formFields?.enableStoreGroupType}
          disabled={
            validateNCDP(eventType) || formFields?.disableStoreGroupType
          }
          defaultValue={formFields?.groupInd}
        ></InputSelect>
      ) : null}
    </div>
  );
}

export default memo(StoreGroupTypeField);
