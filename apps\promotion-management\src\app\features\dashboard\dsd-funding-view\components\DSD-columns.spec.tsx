import '@testing-library/jest-dom';
import { fireEvent, render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";
import { DASHBOARD_SLICE_CONFIG } from "apps/promotion-management/src/app/config/dashboard-config";
import * as reactRedux from "react-redux";
import { DSDColumns } from "./DSD-columns";
import { onModalToggle } from 'apps/promotion-management/src/app/library/data-access/dashboard/dsd-funding-slice';

const mockStore = configureStore([]);

jest.mock("react-redux", () => ({
  ...jest.requireActual("react-redux"),
  useDispatch: jest.fn(),
  useSelector: jest.fn(),
}));

jest.mock('apps/promotion-management/src/app/library/data-access/dashboard/dsd-funding-slice', () => ({
  onModalToggle: jest.fn(),
}));

describe("DSDColumns Component", () => {
  let store;
  let useDispatchMock;
  let useSelectorMock;

  beforeEach(() => {
    store = mockStore({
      [DASHBOARD_SLICE_CONFIG.dsdFundingView.SLICE_KEY]: {
        data: {
          dsdVendorFunding: {
            dsdVendors: [
              { vendorId: "1-1", vendorName: "Vendor 1" },
              { vendorId: "2-2", vendorName: "Vendor 2" },
            ],
            dsdFundingItems: [
              {
                dsdOffersFundings: [
                  {
                    vendorId: { vendorNbr: "1", vendorSubAccount: "1", costArea: "101" },
                    costAreaDesc: "Area 101",
                    dsdOfferOverlapFunding: [],
                    itemDsdVendorAuthorized: true,
                  },
                ],
              },
            ],
          },
        },
      },
      [DASHBOARD_SLICE_CONFIG.dsdFundingView.TOGGLE_ITEM_DETAILS_COLS_SLICE_KEY]: {
        data: { viewItemDetailsColumns: true },
      },
      [DASHBOARD_SLICE_CONFIG.dsdFundingView.TOGGLE_POTENTIAL_FUNDING_SLICE_KEY]: {
        data: { viewOnlyPotentialFundingOmission: false },
      },
    });

    useDispatchMock = jest.fn();
    useSelectorMock = jest.fn();

    reactRedux.useDispatch.mockReturnValue(useDispatchMock);
    reactRedux.useSelector.mockImplementation((selector) =>
      selector(store.getState())
    );
  });

  const TestComponent = ({ excludedOffers, itemHeaderHeight }) => {
    const columns = DSDColumns(excludedOffers, itemHeaderHeight);
    return (
      <table>
        <thead>
          <tr>
            {columns.map((column) => (
              <th key={column.id}>
                {typeof column.label === "function" ? column.label() : column.label}
              </th>
            ))}
          </tr>
        </thead>
      </table>
    );
  };

  const renderWithProvider = (excludedOffers, itemHeaderHeight) => {
    return render(
      <Provider store={store}>
        <TestComponent
          excludedOffers={excludedOffers}
          itemHeaderHeight={itemHeaderHeight}
        />
      </Provider>
    );
  };

  it("should render all columns correctly", () => {
    const { getByText } = renderWithProvider([], 40);

    expect(getByText("CIC")).toBeInTheDocument();
    expect(getByText("Item Description")).toBeInTheDocument();
    expect(getByText("Primary UPC")).toBeInTheDocument();
    expect(getByText("UPCs")).toBeInTheDocument();
    expect(getByText("Pack")).toBeInTheDocument();
    expect(getByText("Size")).toBeInTheDocument();
  });

  it("should hide specific columns when viewItemDetailsColumns is false", () => {
    // Update mock store to set viewItemDetailsColumns to false
    store = mockStore({
      ...store.getState(),
      [DASHBOARD_SLICE_CONFIG.dsdFundingView.TOGGLE_ITEM_DETAILS_COLS_SLICE_KEY]: {
        data: { viewItemDetailsColumns: false },
      },
    });

    // Mock useSelector to return the updated store state
    reactRedux.useSelector.mockImplementation((selector) =>
      selector(store.getState())
    );

    const { container } = renderWithProvider([], 40);

    // Log the rendered DOM for debugging
    console.log(container.innerHTML);

    // Use querySelector to check if the column is rendered
    const primaryUPCColumn = container.querySelector('th[data-id="primaryUpc"]');
    const upcsColumn = container.querySelector('th[data-id="UPCs"]');

    // Assert that the columns are not in the document
    expect(primaryUPCColumn).toBeNull();
    expect(upcsColumn).toBeNull();
  });

  it("should handle empty vendor data gracefully", () => {
    store = mockStore({
      ...store.getState(),
      [DASHBOARD_SLICE_CONFIG.dsdFundingView.SLICE_KEY]: {
        data: {
          dsdVendorFunding: {
            dsdVendors: [],
            dsdFundingItems: [],
          },
        },
      },
    });

    reactRedux.useSelector.mockImplementation((selector) =>
      selector(store.getState())
    );

    const { queryByText } = renderWithProvider([], 40);

    expect(queryByText("Vendor 1")).not.toBeInTheDocument();
    expect(queryByText("Vendor 2")).not.toBeInTheDocument();
  });
});

describe('RenderUpcs Function', () => {
  let store;
  let useDispatchMock;

  beforeEach(() => {
    store = mockStore({});
    useDispatchMock = jest.fn();
    reactRedux.useDispatch.mockReturnValue(useDispatchMock);
  });

  const RenderUpcs = rowData => {
    const caseUpc = rowData?.caseUpc,
      caseUpcs = rowData?.itemUpcs?.filter(itemUpc => itemUpc == caseUpc);
    const caseUpcsLength =
      typeof rowData?.caseUpc == 'object'
        ? rowData?.caseUpc?.length
        : caseUpcs?.length;
    const displayUpcDetails = (e, rowData) => {
      e?.preventDefault();
      useDispatchMock(onModalToggle({ isOpen: true, modalData: rowData }));
    };

    return (
      <>
        <a
          href="#"
          className="text-[#1b6ebb] px-3"
          title={rowData?.itemUpcs}
          onClick={e => displayUpcDetails(e, rowData)}
        >
          {caseUpcsLength}
        </a>
      </>
    );
  };

  it('should render the correct number of UPCs', () => {
    const rowData = {
      caseUpc: '12345',
      itemUpcs: ['12345', '67890', '12345'],
    };

    render(
      <Provider store={store}>
        {RenderUpcs(rowData)}
      </Provider>
    );

    const linkElement = screen.getByText('2'); // "12345" appears twice in itemUpcs
    expect(linkElement).toBeInTheDocument();
    expect(linkElement).toHaveAttribute('title', rowData.itemUpcs.toString());
  });

  it('should dispatch onModalToggle when the link is clicked', () => {
    const rowData = {
      caseUpc: '12345',
      itemUpcs: ['12345', '67890', '12345'],
    };

    render(
      <Provider store={store}>
        {RenderUpcs(rowData)}
      </Provider>
    );

    const linkElement = screen.getByText('2');
    fireEvent.click(linkElement);

    expect(useDispatchMock).toHaveBeenCalledWith(
      onModalToggle({ isOpen: true, modalData: rowData })
    );
  });
});