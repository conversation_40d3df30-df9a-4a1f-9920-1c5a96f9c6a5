import { app_store } from "@me/data-rtk";
import { render, screen } from "@testing-library/react";
import { FormProvider, useForm } from "react-hook-form";
import { Provider } from "react-redux";
import PromotionDetailsCardContent from "./promotion-details-card-content-preview";
import { CardConfigMock } from "../event-details/event-details-mocks";
import * as selectors from "@me/data-rtk";

const Wrapper = props => {
  const formMethods = useForm<any>({
    defaultValues: {
      promotionsLists: [
        {
          promotionsList: [
            {
              vehicle: {
                id: "63d4d1ec705a29397068db47",
                vehicleNm: "05 Week 25 Insert 2023",
                sourceVehicleSk: 42663,
                vehicleTypNm: "insrt",
                startDate: "2023-06-21T00:00:00.000Z",
                endDate: "2023-06-27T00:00:00.000Z",
                vehicleType: {
                  vehicleTypeId: "636abba1b426ee543a94d394",
                  sourceVehicleTypeSk: 146,
                  vehicleTypNm: "insrt",
                  vehicleTypDesc: "Weekly Insert",
                },
              },
              promoStartDate: "2023-06-21T00:00:00.000Z",
              promoEndDate: "2023-06-27T00:00:00.000Z",
              forecast: {
                quadrant: "1A",
                forecastSales: 18081,
                forecastUnits: 1234,
                forecastAgp: 7009,
                forecastAgpPercent: 81.67,
                markdown: 16124,
                markdownPercent: 7,
                incrementalUnits: 297,
                incrementalPercent: 42.77,
                incrementalSales: 4384,
                incrementalSalesPercent: 30.92,
                incrementalAgp: 2200,
                incrementalAgpPercent: 34.95,
                vendorFunding: 6677,
                coverage: 2000,
                coveragePercent: 85,
                periscopePromotionId: 880942,
                promotionId: 160675936500,
                promotionObjectId: "63e5d21e380cbc74021afc46",
                planEvent: "63a3f10fce2d351bc2a74f68",
                lastUpdated: "2022-12-22T05:54:27.646Z",
                lastUpdateBy: "UPP",
              },
            },
          ],
        },
      ],
    },
  });
  return (
    <Provider store={app_store}>
      <FormProvider {...formMethods}>{props.children}</FormProvider>
    </Provider>
  );
};

describe("Promotion Details Card Content Preview Test Suite", () => {
  beforeEach(() => {
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      switch (sel_name) {
        case "promotion_forecast_rn":
          return {
            data: {},
          };

        default:
          return {};
      }
    });
  });
  it("should render Promotion Details Card Content Preview", () => {
    const { baseElement } = render(
      <Wrapper default>
        <PromotionDetailsCardContent
          cardConfiguration={CardConfigMock}
          promotionIndex={0}
          cardIndex={0}
          cardItemIndex={0}
        ></PromotionDetailsCardContent>
      </Wrapper>
    );
    expect(baseElement).toBeTruthy();
  });

  it("should show correct text in Promotion Details Card Content Preview", () => {
    render(
      <Wrapper>
        <PromotionDetailsCardContent
          cardConfiguration={CardConfigMock}
          promotionIndex={0}
          cardIndex={0}
          cardItemIndex={0}
        ></PromotionDetailsCardContent>
      </Wrapper>
    );
  });
});
