import { useState, useEffect, useMemo, useCallback, memo } from "react";
import { Card } from "@albertsons/uds/molecule/Card";
import "../card-height.scss";
import { useFormContext } from "react-hook-form";
import { useDispatch } from "react-redux";
import Card<PERSON>rapper from "./CardWrapper";
import PreviewCard from "./preview/preview-card";
import { useSelectorWrap } from "@me/data-rtk";
import "./common-modal.scss";
import {
  getLoggedInUserFromStorage,
  getLoggedInUserType,
} from "@me-upp-js/utilities";
import { RenderStates } from "@me/ui-render-states";
import {
  byPassOfferAllowanceHandler,
  eventDetailsDataHandler,
} from "../../../service/slice/event-detail-slice";
import { isPermissionToEdit } from "@me/util-helpers";
import {
  allowanceNewCardConfigurationUnset,
  allowanceFormReset,
  promoCardConfiguration,
  offerCardConfiguration,
  setOfferSubCardConfiguration,
  setPromoSubCardConfiguration,
  setAllowanceFormInfo,
  resetOfferSectionsData,
  resetIsOfferSectionUpdated,
  resetOfferAmountsData,
  resetOfferSectionsEnableConfig,
} from "../../../service/slice/allowance-details-slice";
import { allowanceTempWorkReset } from "../../../service/slice/allowance-temp-work-slice";
import {
  useDeleteAllowanceTempWorkDataMutation,
  useDeleteNationalAllowanceTempWorkDataMutation,
} from "../../../service/apis/allowance-api";
import { getObjectValues } from "../../../service/allowance/allowance-stepper-service";
import { DIVISION_PROMOTION } from "../../../constants/event-types/types/division-promotion";
import {
  promotionCancelHandler,
  promotionDatesHandler,
} from "../../../service/slice/promotion-details-slice";
import { getQueryParams } from "../../../service/allowance/allowance-service";
import AllowanceCardHeaderPreview from "../allowance/allowance-card-header-preview";
import PromotionDetailsCardHeader from "../promotion-details/promotion-details-card-header-preview";
import {
  checkIsNationalEvent,
  resetOfferDivisionIds,
} from "../offer/offer-service";
import { resetDivisionWiseShrdWhseData, resetNationalDivisionsConfig } from "../../../../all-allowances/nationals/slices/national-main-entry-slices";
import { EVENT_TYPE } from "../../../constants/constants";

interface ISingleCardContainerProps {
  isNew?: any;
  cardIndex?: any;
  cardItemIndex?: any;
  cardConfiguration?: any;
  cardContainerData?: any;
  step: any;
  sectionIndex: any;
  isMultiple: any;
  removeCardHandler?: any;
  rejectCardHandler?: any;
  cancelCardHandler?: any;
  isAddAnotherOffer?: boolean;
  inValidCardHandler?: any;
  isMultiVendorEvent?: boolean;
  isEventCardOpened?: boolean;
}

const SingleCardContainer = ({
  cardContainerData,
  cardConfiguration,
  cardIndex,
  cardItemIndex,
  isNew,
  step,
  sectionIndex,
  isMultiple,
  removeCardHandler,
  rejectCardHandler,
  cancelCardHandler,
  isAddAnotherOffer,
  inValidCardHandler,
  isMultiVendorEvent,
  isEventCardOpened,
}: ISingleCardContainerProps) => {
  const { title, subtitle, section } = cardConfiguration;
  const { setValue } = useFormContext();
  const allowanceTempWorkData = useSelectorWrap("allowance_temp_work")?.data;
  const { data: eventStatusChanged } = useSelectorWrap(
    "event_status_change_indicators"
  );
  const { data: eventData } = useSelectorWrap("event_details_data");
  const { isEventCardOpen } = useSelectorWrap("is_event_edit_enable").data;
  const {
    id: eventId = "",
    eventStatus = "",
    inValidPromotions = [],
    inValidAllowances = [],
    offerAllowances = [],
    promotionsList = [],
    eventType = "",
    divisionIds = [],
  } = eventData || {};
  const { allowanceData } = useSelectorWrap("allowance_temp_work").data;
  const { openCardConfig = {}, editCardConfig = {} } =
    useSelectorWrap("offer_card_configutation_rn").data || {};
  const { offerSubCardConfig = {} } =
    useSelectorWrap("offer_sub_card_configutation_rn").data || {};
  const { promoSubCardConfig = {} } =
    useSelectorWrap("promo_sub_card_configutation_rn").data || {};
  const {
    openCardConfig: promoOpenCardConfig = {},
    editCardConfig: promoEditCardConfig = {},
    isAddNewPromo: newPromoAdd = {},
  } = useSelectorWrap("promo_card_configutation_rn").data || {};
  const { isOfferBypassed = false } =
    useSelectorWrap("by_pass_offer_allowance")?.data || {};
  const {
    data: { isEditPromotion: { isEdit: isPromoEdit = {} } = {} },
  } = useSelectorWrap("promotion_edit_enable_configutation_rn") || {};
  const { data: eventProgressConfigData } = useSelectorWrap(
    "eventProgressConfigData_rn"
  );
  const { isOfferViewMode } = getQueryParams();
  const isNationalEvent = checkIsNationalEvent(eventType);
  const userRole = getLoggedInUserType();
  const [isEdit, setIsEditMode] = useState<boolean>(false);
  const [isEditEnablePromoLink, setIsEditEnablePromoLink] =
    useState<boolean>(false);
  const [isEditEnableAllowLink, setIsEditEnableAllowLink] =
    useState<boolean>(false);
  const [isPreviouslyInvalid, setPreviouslyInvalid] = useState(false);
  const [isOpenPreview, setIsOpenPreview] = useState(false);
  const isInvalidEditOpen =
    inValidPromotions.includes(cardContainerData?.id) ||
    inValidAllowances.includes(cardContainerData?.id);
  const isAllowanceCard = cardConfiguration.key === "Allowance";
  const subCardConfig = isAllowanceCard
    ? offerSubCardConfig
    : promoSubCardConfig;
  const [
    deleteAllowanceTempWorkData,
    { isLoading: isDeleteAllowanceTempDataloading, isError: isDeleteError },
  ] = useDeleteAllowanceTempWorkDataMutation();
  const [
    deleteNationalAllowanceTempWorkData,
    { isLoading: isDeleteNationalTempLoading },
  ] = useDeleteNationalAllowanceTempWorkDataMutation();

  const handleOfferSubCardConfig = (value: boolean, index?: number) => {
    value !== undefined &&
      dispatch(
        setOfferSubCardConfiguration({
          offerSubCardConfig: {
            ...offerSubCardConfig,
            [cardIndex]: {
              [index !== undefined ? index : cardItemIndex]: value,
            },
          },
        })
      );
  };
  const memoizedSubCardConfig = useMemo(
    () => subCardConfig?.[cardIndex]?.[cardItemIndex],
    [subCardConfig, cardIndex, cardItemIndex]
  );

  const handlePromoSubCardConfig = (value: boolean) => {
    dispatch(
      setPromoSubCardConfiguration({
        promoSubCardConfig: {
          ...promoSubCardConfig,
          [cardIndex]: {
            ...promoSubCardConfig?.[cardIndex],
            [cardItemIndex]: value,
          },
        },
      })
    );
  };

  useEffect(() => {
    if (eventStatusChanged.isEventStatusChanged) {
      const editStatus =
        eventProgressConfigData?.[eventStatus]?.USERS?.[userRole]?.edit;
      setIsEditEnablePromoLink(editStatus?.promotion);
      handleCancelMode();
    }
  }, [
    eventStatusChanged,
    eventStatus,
    JSON.stringify(eventProgressConfigData),
  ]);

  useEffect(() => {
    const editStatus =
      eventProgressConfigData?.[eventStatus]?.USERS?.[userRole]?.edit;
    section === "Allowance" && setIsEditEnableAllowLink(editStatus?.allowance);
    section === "Promotion Details" &&
      setIsEditEnablePromoLink(editStatus?.promotion);
  }, [section, JSON.stringify(eventProgressConfigData)]);

  useEffect(() => {
    if (
      isEventCardOpen &&
      (eventData?.inValidAllowances?.length ||
        eventData?.inValidPromotions?.length)
    ) {
      setIsOpenPreview(true);
    }
  }, [eventData, isEventCardOpen]);

  useEffect(() => {
    if (eventId && isAllowanceCard && isOfferViewMode === null) {
      setIsEditMode(editCardConfig?.[cardIndex]);
    }
  }, [JSON.stringify(editCardConfig)]);

  useEffect(() => {
    const allowData = allowanceTempWorkData?.allowanceData;
    const tempData =
      isNationalEvent && Array.isArray(allowData) ? allowData?.[0] : allowData;
    if (
      tempData &&
      Object.keys(tempData)?.length &&
      tempData?.lastUpdUserId?.toUpperCase() === getLoggedInUserFromStorage()
    )
      setValue("isAllowanceChanged", true);
  }, [JSON.stringify(allowanceTempWorkData)]);

  const handlePromoCardConfigStatus = (value = false) => {
    dispatch(
      promoCardConfiguration({
        ...promoEditCardConfig,
        openCardConfig: { [cardIndex]: value },
        isAddNewPromo: newPromoAdd,
      })
    );
  };

  const handleEditMode = () => {
    if (eventId && !isAllowanceCard) {
      handlePromoCardConfigStatus(true);
      setIsEditMode(true);
    }
  };

  useEffect(() => {
    if (isInvalidEditOpen && isPermissionToEdit(eventStatus)) {
      handleEditMode();
      setPreviouslyInvalid(true);
    } else if (isPreviouslyInvalid && !isInvalidEditOpen) {
      setIsEditMode(false);
      setPreviouslyInvalid(false);
    }
  }, [isInvalidEditOpen]);

  const dispatch = useDispatch();

  const layout = section?.replace(/ /g, "-").toLowerCase();

  // const HeaderComponent = lazy(
  //   () => import(`../${layout}/${layout}-card-header-preview`)
  // );

  // const ContentComponent = lazy(
  //   () => import(`../${layout}/${layout}-card-content`)
  // );

  const handleCancelMode = (isCanclClicked = false) => {
    setValue("isPromoChanged", false);
    isCanclClicked && dispatch(eventDetailsDataHandler(eventData));
    if (eventId && !isAllowanceCard) {
      handlePromoCardConfigStatus(false);
      setIsEditMode(false);
      dispatch(promotionDatesHandler({ isDatesUpdated: false }));
    }
  };

  useEffect(() => {
    if (
      !isPromoEdit &&
      editCardConfig &&
      Object.values(editCardConfig).some(e => e) &&
      !isAllowanceCard
    ) {
      setIsEditMode(false);
    }
  }, [isPromoEdit]);

  const handleSubCardOpenStatus = useCallback(
    value => {
      isAllowanceCard
        ? handleOfferSubCardConfig(value)
        : handlePromoSubCardConfig(value);
    },
    [
      isAllowanceCard,
      offerSubCardConfig,
      promoSubCardConfig,
      cardIndex,
      cardItemIndex,
    ]
  );

  const handleCardOpenStatus = (value = false) => {
    if (isAllowanceCard) {
      dispatch(
        offerCardConfiguration({
          editCardConfig: editCardConfig,
          openCardConfig: { ...openCardConfig, [cardIndex]: value },
        })
      );
    } else {
      dispatch(
        promoCardConfiguration({
          editCardConfig: promoEditCardConfig,
          openCardConfig: { ...promoOpenCardConfig, [cardIndex]: value },
          isAddNewPromo: newPromoAdd,
        })
      );
    }
  };

  useEffect(() => {
    isOfferBypassed && handleCardOpenStatus(!isAllowanceCard);
  }, [isOfferBypassed, isAllowanceCard]);

  // const CommonHeader = (
  //   <Suspense fallback={null}>
  //     <HeaderComponent
  //       isOpenCard={subCardConfig?.[cardIndex]?.[cardItemIndex]}
  //       openCard={handleSubCardOpenStatus}
  //       cardIndex={cardIndex}
  //       cardItemIndex={cardItemIndex}
  //       setIsEditMode={handleEditMode}
  //       //setPromoPopupOpen={setPromoPopupOpen}
  //       isEditEnablePromoLink={isEditEnablePromoLink}
  //       isEditMode={isEdit}
  //       handleCancel={handleCancelMode}
  //       removeCardHandler={removeCardHandler}
  //       rejectCardHandler={rejectCardHandler}
  //       cancelCardHandler={cancelCardHandler}
  //     />
  //   </Suspense>
  // );

  const CardWrapperContent = (
    <CardWrapper
      cardContainerData={cardContainerData}
      cardIndex={cardIndex}
      cardItemIndex={cardItemIndex}
      isNew={isNew}
      cardConfiguration={cardConfiguration}
      step={step}
      isEditMode={isEdit}
      sectionIndex={sectionIndex}
      handleCancel={handleCancelMode}
    />
  );

  const PreviewCardContent = (
    <PreviewCard
      cardConfiguration={cardConfiguration}
      cardIndex={cardIndex}
      cardItemIndex={cardItemIndex}
    />
  );
  const deleteTempData = () => {
    const deleteTempMutation = isNationalEvent
      ? deleteNationalAllowanceTempWorkData
      : deleteAllowanceTempWorkData;
    const allowData =
      isNationalEvent && Array.isArray(allowanceData)
        ? allowanceData?.find(e => e?.tempWorkAllowanceId)
        : allowanceData;
    allowData?.tempWorkAllowanceId &&
      eventId &&
      deleteTempMutation({
        URL_PARAM: isNationalEvent ? eventId : allowData?.tempWorkAllowanceId,
      });
  };
  const CancelNewOffer = () => {
    const onClick = e => {
      e.stopPropagation();
      setValue("isAllowanceChanged", false);
      dispatch(allowanceNewCardConfigurationUnset());
      handleCardOpenStatus(false);
      deleteTempData();
      dispatch(allowanceTempWorkReset());
      dispatch(resetOfferSectionsData());
      dispatch(resetOfferSectionsEnableConfig());
      dispatch(resetOfferAmountsData());
      resetOfferDivisionIds(dispatch, divisionIds);
      dispatch(resetNationalDivisionsConfig());
      dispatch(resetDivisionWiseShrdWhseData());
      dispatch(resetIsOfferSectionUpdated());
      // dispatch(allowanceFormReset());
      dispatch(
        setAllowanceFormInfo({
          allowanceFormData: {
            [cardIndex]: { cancel: true },
          },
        })
      );
    };

    return (
      <div className="ml-auto" id="abs-single-card-container1">
        <span className="mr-5  font-semibold text-[#1B6EBB]" onClick={onClick}>
          {/* <Trash2
            width={16}
            height={16}
            color="#1B6EBB"
            className="inline-block mb-[3px] mr-[8px]"
          /> */}
          {`Cancel${!isNew ? " Editing" : ""}`}
        </span>
      </div>
    );
  };

  const CancelPromo = () => {
    const onClick = e => {
      e.stopPropagation();
      dispatch(promotionCancelHandler({ isCancelled: true }));
    };

    return (
      <div className="ml-auto" id="abs-single-card-container-cancel-sec">
        <span className="mr-5  font-semibold text-[#1B6EBB]" onClick={onClick}>
          Cancel Editing
        </span>
      </div>
    );
  };
  const isNoPromoAndOfferEdit = () =>
    offerAllowances?.length > 0 &&
    !promotionsList?.length &&
    editCardConfig &&
    getObjectValues(editCardConfig)?.includes(true);

  const checkPromoCardOpen = () => {
    // For promo card, if there is no promo and offer edit, then card should not open
    return isNoPromoAndOfferEdit() ? false : memoizedSubCardConfig;
  };
  const renderSubCardHeader = () => {
    return (
      <div>
        {isAllowanceCard ? (
          <AllowanceCardHeaderPreview
            isOpenCard={memoizedSubCardConfig}
            openCard={handleSubCardOpenStatus}
            cardIndex={cardIndex}
            cardItemIndex={cardItemIndex}
            isEditMode={isEdit}
            handleCancel={handleCancelMode}
          />
        ) : (
          <PromotionDetailsCardHeader
            isOpenCard={memoizedSubCardConfig}
            openCard={handleSubCardOpenStatus}
            cardIndex={cardIndex}
            cardItemIndex={cardItemIndex}
            setIsEditMode={handleEditMode}
            isEditEnablePromoLink={isEditEnablePromoLink}
            isEditMode={isEdit}
            handleCancel={handleCancelMode}
            removeCardHandler={removeCardHandler}
            rejectCardHandler={rejectCardHandler}
            cancelCardHandler={cancelCardHandler}
          />
        )}
      </div>
    );
  };
  const disableCardForNcdp = () => {
    if(eventType === EVENT_TYPE.NCDP) {
      return isAllowanceCard ? !offerAllowances?.length : false;
    }
    return false;
  }

  const renderHtml = isEdit ? (
    <div id="abs-single-card-container-is-open-preview-sec">
      {isOpenPreview ? (
        <Card
          customHeader={isMultiple}
          collapsible
          title={title}
          subtitle={!memoizedSubCardConfig ? subtitle : ""}
          isOpen={memoizedSubCardConfig}
          onChange={handleSubCardOpenStatus}
          className={`z-index-dynamic-class ${
            !eventId ||
            (section === DIVISION_PROMOTION.Allowance.section &&
              isMultiVendorEvent)
              ? "pointer-events-none"
              : ""
          }`}
        >
          <Card.Header>{renderSubCardHeader()}</Card.Header>
          <Card.Content>
            {memoizedSubCardConfig ? PreviewCardContent : null}
          </Card.Content>
        </Card>
      ) : (
        <>
          <div
            className="mb-[15px]"
            id="abs-single-card-container-common-header-sec"
          >
            {renderSubCardHeader()}
          </div>
          <div id="abs-single-card-container-card-wrapper-content-sec">
            {CardWrapperContent}
          </div>
        </>
      )}
    </div>
  ) : (
    <Card
      key={`abs-single-card-container-card-${cardIndex}-${cardItemIndex}-${cardConfiguration?.key}`}
      customHeader={isMultiple}
      collapsible
      title={title}
      subtitle={!memoizedSubCardConfig ? subtitle : ""}
      isOpen={!isAllowanceCard ? checkPromoCardOpen() : memoizedSubCardConfig}
      onChange={value => {
        handleSubCardOpenStatus(value);
        handleCardOpenStatus(value);
        isOfferBypassed &&
          dispatch(
            byPassOfferAllowanceHandler({
              isOfferBypassed: false,
            })
          );
      }}
      className={`z-index-dynamic-class ${
        !eventId ||
        (section === DIVISION_PROMOTION.Allowance.section &&
          isMultiVendorEvent) ||
        isEventCardOpened || disableCardForNcdp() ||
        (!isAllowanceCard && isNoPromoAndOfferEdit())
          ? "pointer-events-none"
          : ""
      } `}
    >
      <Card.Header>
        {isMultiple ? renderSubCardHeader() : null}{" "}
        {!isMultiple && isAllowanceCard && eventId && memoizedSubCardConfig && (
          <CancelNewOffer />
        )}
      </Card.Header>
      <Card.Content>
        {memoizedSubCardConfig
          ? !isMultiple
            ? CardWrapperContent
            : PreviewCardContent
          : null}
      </Card.Content>
    </Card>
  );

  const isApiLoading = () => {
    return isDeleteAllowanceTempDataloading || isDeleteNationalTempLoading;
  };

  const renderDetails = {
    isApiLoading: isApiLoading(),
    isPageLevelSpinner: true,
    isRenderMainHtml: true,
    renderHtml,
  };
  return <RenderStates details={renderDetails} />;
};

export default memo(SingleCardContainer);
