import { render, screen } from "@testing-library/react";
import ViewAllPromoTable from "./view-all-promo-table";
import "@testing-library/jest-dom";
import { Provider } from "react-redux";
import { app_store } from "@me/data-rtk";
import userEvent from "@testing-library/user-event";

import { mockDataResponse } from "./promotion-details-mock";

describe("View All Promo Table Test Suite", () => {
  beforeEach(() => {
    // IntersectionObserver isn't available in test environment
    const mockIntersectionObserver = jest.fn();
    mockIntersectionObserver.mockReturnValue({
      observe: () => null,
      unobserve: () => null,
      disconnect: () => null,
    });
    window.IntersectionObserver = mockIntersectionObserver;
  });
  it("should render View All Promo Table Component", () => {
    render(
      <Provider store={app_store}>
        <ViewAllPromoTable
          setOpen={jest.fn}
          isOpen={true}
          promotionData={mockDataResponse}
        />
      </Provider>
    );
    const textsToExpect = [
      "Item Description",
      "CIC",
      "Primary UPC",
      "UPCs",
      "Pack",
      "Size",
      "Regular Retail",
      "List Cost",
      "Net Cost",
      "AGP",
    ];
    textsToExpect.forEach(currentText => {
      expect(screen.getByText(currentText)).toBeInTheDocument();
    });
  });
});
