import { useState } from "react";
import Checkbox from "@albertsons/uds/molecule/Checkbox";
const EventCommentsCheckBox = ({ offer, handleSelectOffer, checked }) => {
  const { title } = offer;
  const [checkedVal, setCheckedVal] = useState(false);
  const handleChange = event => {
    const value = event?.target?.checked;
    setCheckedVal(value);
    handleSelectOffer(offer, value);
  };
  return checked ? (
    <div>
      <Checkbox
        label={title}
        checked={checkedVal}
        onChange={event => handleChange(event)}
        disabled={!checked}
      />
    </div>
  ) : null;
};

export default EventCommentsCheckBox;
