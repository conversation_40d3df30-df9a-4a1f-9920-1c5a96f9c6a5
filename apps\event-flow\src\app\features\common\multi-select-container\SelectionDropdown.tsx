import {
  useRef,
  useState,
  FocusEvent,
  useEffect,
  useCallback,
  useMemo,
} from "react";
import SelectionListContainer from "./SelectionListContainer";
import DropdownButton from "./DropdownButton";
import Popper from "@albertsons/uds/molecule/Popper";
import { useSelectorWrap } from "@me/data-rtk";
import { useDispatch } from "react-redux";
import { setSelectedStoreGroups } from "../../create-event/service/slice/event-detail-slice";
import { Controller, useFormContext } from "react-hook-form";
import { FormFieldError } from "@me/util-form-wrapper";

const SelectionDropdown = ({
  configObj,
  disabled,
  onItemSelection,
  validationFn,
}) => {
  const {
    selectedDataSlice,
    id,
    dataSliceName,
    uniqueId,
    requiredErrorMsg,
    isRequired,
  } = configObj ?? {};
  const { control } = useFormContext();

  const [showPopper, setShowPopper] = useState<boolean>(false);

  const dispatch = useDispatch();

  const ref = useRef<HTMLDivElement>(null);

  const rawDropDownData = useSelectorWrap(dataSliceName);
  const dropDownData = useMemo(
    () => rawDropDownData?.data ?? [],
    [rawDropDownData]
  );
  const selectedDataRaw = useSelectorWrap(selectedDataSlice);
  const selectedData = useMemo(
    () => selectedDataRaw?.data ?? [],
    [selectedDataRaw]
  );
  const {
    data: { storeGroupDivisions },
  } = useSelectorWrap("store_group_divisions");

  const handleDropDownClick = useCallback(
    () => setShowPopper(prev => !prev),
    []
  );

  const onBlur = useCallback((ev: FocusEvent<HTMLElement>) => {
    if (ev.relatedTarget !== ref.current?.firstChild) {
      setShowPopper(false);
    }
  }, []);

  const defaultSelections = useCallback(() => {
    if (id === "storeGroup") {
      const selectedStoreIds =
        storeGroupDivisions?.length > 0
          ? dropDownData
              ?.filter(item =>
                storeGroupDivisions?.includes(item?.divisionIds[0])
              )
              ?.map(item => item?.[uniqueId])
          : dropDownData?.map(item => item?.[uniqueId]);
      const selectedStoreGroups =
        storeGroupDivisions?.length > 0
          ? dropDownData?.filter(item =>
              storeGroupDivisions?.includes(item?.divisionIds[0])
            )
          : dropDownData;
      dispatch(setSelectedStoreGroups(selectedStoreIds));
      onItemSelection(selectedStoreGroups);
    }
  }, [
    id,
    storeGroupDivisions,
    dropDownData,
    dispatch,
    onItemSelection,
    uniqueId,
  ]);

  useEffect(() => {
    if (Array.isArray(dropDownData)) {
      defaultSelections();
    }
    //donot addd defaultSelections to the dependency array
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dropDownData]);

  return (
    <Controller
      control={control}
      name={id}
      rules={{
        required: {
          value: false,
          message: requiredErrorMsg,
        },
        validate: isRequired ? validationFn : true,
      }}
      render={({ field, fieldState: { error } }) => (
        <div>
          <div ref={ref}>
            <DropdownButton
              {...field}
              disabled={disabled}
              handleClick={handleDropDownClick}
              configObj={configObj}
              selectedItemsCount={selectedData?.length || 0}
              error={error}
            />
          </div>
          {error && <FormFieldError error={requiredErrorMsg} />}
          <Popper
            anchor={ref}
            open={showPopper && dropDownData?.length > 0}
            onBlur={onBlur}
            className="w-auto w-[24%]"
            zIndex={10}
            autoFocus={true}
            options={{
              placement: "bottom",
            }}
          >
            <div className="flex flex-col w-auto bg-white rounded-lg border border-gray-204 py-4 pl-4 mt-[2px]">
              <SelectionListContainer
                configObj={configObj}
                onItemSelection={onItemSelection}
                dropDownData={dropDownData}
                selectedData={selectedData}
              />
            </div>
          </Popper>
        </div>
      )}
    />
  );
};

export default SelectionDropdown;
