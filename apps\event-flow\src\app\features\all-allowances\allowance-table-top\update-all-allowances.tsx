import Button from "@albertsons/uds/molecule/Button";
import Input from "@albertsons/uds/molecule/Input";
import Select from "@albertsons/uds/molecule/Select";
import { useSelectorWrap } from "@me/data-rtk";
import classNames from "classnames";
import { ChangeEvent, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import efConstants from "../../../shared/ef-constants/ef-constants";
import useAllowanceAmountValidation from "../../create-event/hooks/allowance-amount-validations";
import { checkIsDisplayOnSwitch } from "../allowance-table/allowance-table-data-utils";
import {
  setIsUpdateAllChange,
  updateAllDivisionsItemsAction,
  updateAllItemsAction,
} from "../../create-event/service/slice/table-data-slice";
import "./update-all-allowances.styles.scss";
import {
  getAllowanceTypeFromTempWork,
  getMinListCost<PERSON>ey,
  getUOMBasedOnType,
} from "../../create-event/service/allowance/allowance-service";
import InValidAmountTooltip from "../allowance-table/invalid-amount-tooltip";
import { textMapping } from "../all-allowances-container/text-mapping";
import { getLoggedInUserType } from "@me-upp-js/utilities";
import { getMinValueFromTable } from "../allowances-table-service";
import { isNationalType } from "../allowance-lead-distributors/billing-selection-utils";

export default function UpdateAllAllowances({ isSummary }) {
  const { UOM_OPTIONS, SWITCH_OPTIONS, ZERO_COST_TEXT } = efConstants;
  const inputTextClass = classNames({
    amtFieldWrap: true,
  });
  const { isNdpType = false, selectedDivisionData = {} } =
    useSelectorWrap("national_divisions_config")?.data || {};
  const isNational = isNationalType(isNdpType);
  const userRole = getLoggedInUserType();
  const [amount, setAmount] = useState<any>();
  const [uom, setUom] = useState<any>();
  const [amountError, setAmountError] = useState(false);

  const [minCostAllow, setMinCostAllow] = useState(0);
  const allowanceTempWorkData = useSelectorWrap("allowance_temp_work");
  const allowDataSet = allowanceTempWorkData?.data?.allowanceData;
  const allowanceData =
    isNational && Array.isArray(allowDataSet)
      ? allowDataSet?.[0]
      : allowDataSet;
  const allowanceType = allowanceData?.allowanceType || "";

  const {
    isShowUOMOption,
    customAmtWidth,
    isSkipLessThanAmtValidation = false,
    headerOnlyAmt,
    defaultUpdateAllUom,
  } = textMapping({
    allowanceTempWorkData: allowanceData,
  });
  const {
    data: { isUpdateAll },
  } = useSelectorWrap("isUpdateAllClicked_rn");
  const { data: all_divisions_warning_data } = useSelectorWrap(
    "all_divisions_warning_data"
  );
  const { data: switchValue } = useSelectorWrap("selectedSwitchValue_rn");
  const {
    data: { tableData, allDivisionsTableData = [] },
  } = useSelectorWrap("allowanceTableData_rn");

  const dispatch = useDispatch();
  const { calcError, validateNewAmount,clearError } = useAllowanceAmountValidation({
    maxLimit: minCostAllow,
  });

  useEffect(() => {
    if (defaultUpdateAllUom) {
      getUomVal();
    }
  }, [defaultUpdateAllUom, switchValue?.selectedSwitch]);

  useEffect(() => {
    if (isNational && selectedDivisionData?.divisionId) {
      amount && setAmount("");
      setAmountError(false);
      clearError();
    }
  }, [selectedDivisionData?.divisionId]);

  const getUomVal = () => {
    const uomValueObj: any = getUOMBasedOnType(
      allowanceType,
      UOM_OPTIONS,
      switchValue?.selectedSwitch
    )?.filter(option => {
      return option.value === defaultUpdateAllUom;
    });
    const val = uomValueObj?.[0];
    val && setUom(val);
  };

  useEffect(() => {
    calculateAndSaveMinUnitListCost();
  }, [tableData, allowanceType]);

  const handle_updateAllAllowances = (updateAllDivison = false) => {
    if (amountError) return;
    const { value: uomValue } = uom || {};
    const toUpdateAmount = {
      amount,
      uom: uomValue,
      switchValue: switchValue?.selectedSwitch
    };
    dispatch(updateAllItemsAction(toUpdateAmount));
    updateAllDivison && dispatch(updateAllDivisionsItemsAction(toUpdateAmount));
    if (headerOnlyAmt && headerOnlyAmt.headerOnlyAmtKey) {
      dispatch(
        setIsUpdateAllChange({
          isUpdateAll: !isUpdateAll,
        })
      );
    }
  };
  const handler_allowanceAmt = e => {
    const { amount } = validateNewAmount(e.target.value);
    amount && setAmountError(Number(amount) === 0);
    setAmount(amount);
  };

  const handler_allowanceUom = e => {
    setUom(e);
  };
  /**
   *
   * @returns This will return boolean value if switch selected shows only display only values
   * Ex = when we switch to unit, we need to display amt as display value and disable
   * Update All Button and inputs
   */
  const isOnlyDisplayOnSwitch = () => {
    return checkIsDisplayOnSwitch(allowanceType, switchValue?.selectedSwitch);
  };
  return (
    // eslint-disable-next-line react/jsx-no-useless-fragment
    <>
      {allowanceType && (
        <div
          className={`flex ${isNational ? "w-[60%] max-w-full" : ""} ${
            efConstants.componentClassName.UPDATE_ALL_ALLOWANCES
          } ${isSummary ? "invisible" : ""}`}
        >
          <div className={`flex justify-start items-start relative gap-3 ${isNational ? "w-full" : ""}`}>
            <p className={`text-base font-bold text-left text-[#2b303c] mt-2 ${isNational ? "w-[14%]" : ""}`}>
              {isNational ? "Allow Amounts:" : "Update All:"}
            </p>
            <div className={`flex-col ${isNational ? "w-full" : ""}`}>
              <div className={`flex ${isNational ? "w-[86%]" : ""}`}>
                <span className="relative">
                  <span className="absolute top-[10px] left-[10px]"> $ </span>
                  <div className="w-[50%]">
                    <Input
                      name="allowanceAmt"
                      type="number"
                      onWheel={event => event.currentTarget.blur()}
                      className={`${inputTextClass} ${
                        calcError ? "error-input-wrap" : ""
                      }`}
                      value={amount}
                      disabled={isOnlyDisplayOnSwitch()}
                      onChange={(e: ChangeEvent<HTMLInputElement>) =>
                        handler_allowanceAmt(e)
                      }
                      onKeyDown={e => {
                        // Block 'e','E','+', and '-' keys
                        if (
                          e.key === "e" ||
                          e.key === "E" ||
                          e.key === "+" ||
                          e.key === "-"
                        ) {
                          e.preventDefault();
                        }
                      }}
                      width={customAmtWidth || 90}
                      error={amountError}
                    />
                  </div>
                </span>
                {isShowUOMOption ? (
                  <div className="w-[76px]">
                    <Select
                      className="ml-1 mt-[3px]"
                      items={getUOMBasedOnType(
                        allowanceType,
                        UOM_OPTIONS,
                        switchValue?.selectedSwitch
                      )}
                      value={uom}
                      disabled={isOnlyDisplayOnSwitch()}
                      onChange={(e: any) => handler_allowanceUom(e)}
                      size="md"
                      itemText="name"
                    />
                  </div>
                ) : null}
                <div
                  className={`ml-1 mt-1 flex gap-2 ${
                    isNational ? "w-full" : ""
                  }`}
                >
                  <Button
                    onClick={() => handle_updateAllAllowances(isNational)}
                    variant="secondary"
                    disabled={isOnlyDisplayOnSwitch()}
                    className={`${isNational ? "!w-[40%]" : "w-[120px]"}`}
                  >
                    {isNational ? "Update All Divisions" : "Update All"}
                  </Button>
                  {isNational && (
                    <Button
                      onClick={() => handle_updateAllAllowances()}
                      variant="secondary"
                      disabled={isOnlyDisplayOnSwitch()}
                      className="!w-[60%]"
                    >
                      Update Selected Division Only
                    </Button>
                  )}
                </div>
                <div className="w-fit">
                  <InValidAmountTooltip calcError={calcError} />
                </div>
              </div>
              <div>
                {amountError && (
                  <span className="text-sm leading-4 font-bold text-error">
                    Overall Allowance amount cannot be zero
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
  // find the minimum value for minUnitListCost
  function calculateAndSaveMinUnitListCost() {
    const allowUnitCostKey = getMinListCostKey({
      allowanceType,
      SWITCH_OPTIONS,
      switchValue,
      allowanceTempWorkData,
    });

    function calculateMinValueAcrossAllDiv() {
      let minCost = Number.MAX_VALUE;
      (Array.isArray(allDivisionsTableData)
        ? allDivisionsTableData
        : []
      )?.forEach(division => {
        const divisionMinCost = getMinValueFromTable({
          tableData: division?.tableData,
          userRole,
          allowUnitCostKey,
          isSkipLessThanAmtValidation,
          ZERO_COST_TEXT,
        });
        minCost = Math.min(minCost, divisionMinCost);
      });
      return minCost;
    }
    const minValueToSet = isNational
      ? calculateMinValueAcrossAllDiv()
      : getMinValueFromTable({
          tableData,
          userRole,
          allowUnitCostKey,
          isSkipLessThanAmtValidation,
          ZERO_COST_TEXT,
        });
    setMinCostAllow(minValueToSet);
  }
}
