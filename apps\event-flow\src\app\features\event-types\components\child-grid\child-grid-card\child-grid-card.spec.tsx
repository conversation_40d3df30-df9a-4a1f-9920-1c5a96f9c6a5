import { render } from "@testing-library/react";
import { Provider } from "react-redux";
import { BrowserRouter } from "react-router-dom";
import { app_store } from "@me/data-rtk";
import ChildGridCard from "./child-grid-card";

jest.mock("react-pdf", () => ({
  Document: jest.fn(({ children }) => children),
  Page: jest.fn(() => <div data-testid="mock-page"></div>),
  pdfjs: {
    GlobalWorkerOptions: {
      workerSrc: "",
    },
    version: "2.10.377",
  },
}));

describe("ChildGridCard Component", () => {

  test.skip("renders ChildEventsCard component correctly", () => {
    const component = render(
      <BrowserRouter>
        <Provider store={app_store}>
          <ChildGridCard
            cardIndex={1}
            isFetching={false}
          />
        </Provider>
      </BrowserRouter>
    );

    expect(component).toBeTruthy();
  });
});
