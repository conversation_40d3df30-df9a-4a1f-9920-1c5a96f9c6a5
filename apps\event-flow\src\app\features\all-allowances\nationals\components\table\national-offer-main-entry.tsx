import { useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import classNames from "classnames";
import { cloneDeep } from "lodash";

// Constants and Utils
import efConstants from "../../../../../shared/ef-constants/ef-constants";
import { isAllowanceFeatureEnabled, useGetQueryParams } from "@me/util-helpers";
import { useSelectorWrap } from "@me/data-rtk";
import { saveToSessionStorage } from "../../../../../shared/helpers/event-flow-helpers";

// Services and Utilities
import { getAllowanceMapKey } from "../../../../create-event/service/allowance/allowance-service";
import {
  useGetNationalAllowanceItemsDifferenceQuery,
  useLazyGetNationalAllowancesItemsQuery,
} from "../../../../create-event/service/apis/allowance-api";

// Redux Actions
import {
  setTableDataAction,
  updateCaseAmountOnSwitch,
} from "../../../../create-event/service/slice/table-data-slice";

// Components
import { RenderStates } from "@me/ui-render-states";

// Utilities
import useFetchCombinedData from "../../../../create-event/components/cards/offer/national/hooks/useFetchCombinedData";
import {
  addRowIndexes,
  clearTableSlices,
  createDivisionTableData,
  getBatchPayload,
  getBatchPayloadForItemDifference,
  setAllowRespData,
  setDataInTableSlice,
  setExcludeDataOnLoad,
  setTableDataForDiv,
  setTableDataOnPgLoad_Filter,
  updateLeadDataInSlice,
} from "../../service/table-service";
import { AllowancesUpcDetails } from "../../../allowance-table/allowances-upc-details";
import MainEntryOverlapsTable from "../../../allowance-table/main-entry-overlaps-table";
import { transformAllowancesData } from "../../../allowance-table/transformer";
import NationalAllowanceTableComponent from "./national-allowance-table-component";
import { useHandleAmountsResponse } from "../../../../create-event/components/cards/offer/hooks/amounts/useHandleAmountsResponse";
import { SharedWhseIcon } from "@me-upp-js/utilities";
import { setSharedWhseData } from "../../../allowance-table/allowance-table-data-utils";
import { CommonModal } from "@me/event-flow/components";

function NationalOfferMainEntry() {
  // Constants
  const { SWITCH_OPTIONS, ITEM_AMOUNTS_SUMMARIZED_KEY } = efConstants;

  // State
  const [tableWrapClass, setTableWrapClass] = useState("");
  const [allDivAllowData, setAllDivAllowData] = useState<any>(null);
  const [currentAllowData, setCurrentAllowData] = useState<any>(null);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [stopPolling, setStopPolling] = useState(false);
  const [pollingAttempts, setPollingAttempts] = useState(0);
  const maxPollingAttempts = 12;
  const isTableEmpty = useRef(false);
  const dispatch = useDispatch();

  // Query Parameters
  const {
    queryParamValue: {
      isSummary,
      isEdit: isEditEnable,
      offerAllowancesId,
      group: allowGroup,
      isAllowTypeChange = false,
      eventId,
    },
  } = useGetQueryParams([
    "isEdit",
    "isSummary",
    "offerAllowancesId",
    "group",
    "isAllowTypeChange",
    "eventId",
  ]);

  // Derived State
  const isEditMode = isEditEnable === "true";
  const isSummaryScreen = isSummary === "true";
  const allowTypeChange =
    isAllowanceFeatureEnabled && isAllowTypeChange === "true";

  // Selectors
  const { selectedDivisionData = {}, divisionsList = [] } =
    useSelectorWrap("national_divisions_config")?.data || {};
  const { data: switchValue } = useSelectorWrap("selectedSwitchValue_rn");
  const { data: selectedFiltersData } = useSelectorWrap("selectedFilters_rn");
  const { data: excludedVendorData } = useSelectorWrap(
    "excludedVendorForAllowance_rn"
  );
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const {
    data: { leadDistMode, allDivisionStepsData },
  } = useSelectorWrap("leadDistributors_rn");
  const {
    data: { isLeadChange },
  } = useSelectorWrap("leadDistributorsChanged_rn");
  const {
    data: { tableData, filteredAndSortedIds, allDivisionsTableData },
  } = useSelectorWrap("allowanceTableData_rn");
  const {
    data: { allowanceData: allowanceTempWorkData = [] },
  } = useSelectorWrap("allowance_temp_work");
  const { data: offerAmountsDataObj } =
    useSelectorWrap("offer_amounts_details") || {};
  const { data: allowGroupConfigObj } = useSelectorWrap(
    "allowanceTableColsGroupConfig_rn"
  );
  const currentAllowTmpData = Array.isArray(allowanceTempWorkData)
    ? allowanceTempWorkData?.find(
        tmpData => tmpData?.divisionId === selectedDivisionData?.divisionId
      ) || {}
    : allowanceTempWorkData;
  const {
    headerOnlyAmt,
    isHideColGroupHeader,
    showLeadDistributorSection,
    excludeByKey,
  } = allowGroupConfigObj || {};

  // Derived data
  const [fetchNationalAmountsData] = useLazyGetNationalAllowancesItemsQuery();

  const { handleTempDeleteForInvalidItems, isTempDelInprogress } =
    useHandleAmountsResponse();

  const { offerAmounts = {} } = offerAmountsDataObj || {};
  const isSkipAmtCal =
    !eventId ||
    isSummary ||
    !currentAllowTmpData?.tempWorkAllowanceId ||
    offerAmounts?.[allowGroup]?.length > 0;

  const {
    state: {
      data: allowancesResp = null,
      loading: isAllowanceRespFetched = false,
      error: isAmountsFetchError = null,
    },
  } = useFetchCombinedData(
    fetchNationalAmountsData,
    getBatchPayload({ eventId, allowGroup, divisionsList }),
    isSkipAmtCal
  );

  const {
    data: allowItemDiffResp,
    error: allowItemDiffRespError,
    isFetching: allowItemDiffRespFetched,
  } = useGetNationalAllowanceItemsDifferenceQuery(
    {
      URL_PARAM: offerAllowancesId,
    },
    {
      skip: !offerAllowancesId || !isSummary,
      refetchOnMountOrArgChange: true,
      pollingInterval: stopPolling ? 0 : 2500,
    }
  );

  useEffect(() => {
    updateLeadDataInSlice({
      leadDistMode,
      stepsData: allDivisionStepsData,
      isLeadChange,
      dispatch,
    });
  }, [allDivisionStepsData]);

  useEffect(() => {
    clearTableSlices({ dispatch });
  }, []);

  useEffect(() => {
    if (isSummaryScreen) return;
    setTableDataForDiv({
      currentDivisionId: selectedDivisionData?.divisionId,
      allDivisionsTableData,
      dispatch,
    });
    setAllowDataForUpc();
    setEmptyRecordIfNoAllow(allDivAllowData);
  }, [selectedDivisionData?.divisionId]);

  const setAllowDataForUpc = () => {
    const currentAllowRespData = allDivAllowData?.find(
      e => e?.divisionId === selectedDivisionData?.divisionId
    );
    setCurrentAllowData(currentAllowRespData);
  };

  useEffect(() => {
    const handleAmountsData = async () => {
      const { amountsResp: filteredAmountsResp } =
        await handleTempDeleteForInvalidItems(allowancesResp);
      const allowDataToSet = setAllowRespData({
        allowGroup,
        allowancesResp: filteredAmountsResp,
        offerAmountsDataObj,
      });
      setAllDivAllowData(allowDataToSet);
      allowDataToSet &&
        allowanceTempWorkData?.length &&
        !isAllowanceRespFetched &&
        Object.keys(allowGroupConfigObj || {})?.length &&
        setMainEntryTableData(allowDataToSet);
      setAllowDataForUpc();
      if (allowDataToSet?.length) {
        saveToSessionStorage(
          ITEM_AMOUNTS_SUMMARIZED_KEY,
          allowDataToSet?.some(
            e => e?.summary?.itemAmountsCouldBeSummarized === false
          )
            ? false
            : true
        );
        setEmptyRecordIfNoAllow(allowDataToSet);
      }
    };

    !isSummaryScreen && handleAmountsData();
  }, [
    allowancesResp,
    allowanceTempWorkData,
    isAllowanceRespFetched,
    allowGroupConfigObj,
    JSON.stringify(offerAmountsDataObj),
  ]);

  const setEmptyRecordIfNoAllow = allDivAllowList => {
    const currentSelectedDivisionData =
      allDivAllowList?.find(
        allowObj => allowObj?.divisionId === selectedDivisionData?.divisionId
      ) || {};
    isTableEmpty.current =
      currentSelectedDivisionData?.allowances?.length === 0 ||
      currentSelectedDivisionData?.allowances?.every(
        allow => !allow?.allowanceItems?.length
      ) ||
      false;
  };

  const getTempworkKeys = divisionAllowObj => {
    const currentAllowTmpData = allowanceTempWorkData?.find(
      tempObj => tempObj?.divisionId === divisionAllowObj?.divisionId
    );
    const allowancetempDataRes =
      currentAllowTmpData?.allowanceTypeSpecification?.[
        getAllowanceMapKey(currentAllowTmpData?.allowanceType)
      ]?.allowancesMap?.[allowGroup || ""];
    const isFromTemp =
      !!allowancetempDataRes?.length &&
      allowancetempDataRes?.[0]?.finalizedAmountsInd;
    const skipCalculateNewFinalCostInTable =
      !isEditMode &&
      isSummary !== "true" &&
      !allowancetempDataRes?.[0]?.finalizedAmountsInd;
    return {
      isFromTemp,
      allowancetempDataRes,
      skipCalculateNewFinalCostInTable,
      currentAllowTmpData,
    };
  };

  // Main function
  const setMainEntryTableData = allDivisionAllowResp => {
    const updatedAllowResp = setSharedWhseData(
      setIsPopupOpen,
      dispatch,
      allDivisionAllowResp
    );
    let transformedAllDivTableData = updatedAllowResp?.map(divisionAllowObj => {
      const {
        isFromTemp,
        allowancetempDataRes,
        skipCalculateNewFinalCostInTable,
        currentAllowTmpData,
      } = getTempworkKeys(divisionAllowObj);
      return createDivisionTableData(
        divisionAllowObj,
        isEditMode,
        isFromTemp,
        allowancetempDataRes,
        headerOnlyAmt,
        allowTypeChange,
        currentAllowTmpData,
        skipCalculateNewFinalCostInTable,
        showLeadDistributorSection,
        isAllowanceFeatureEnabled
      );
    });
    transformedAllDivTableData = transformedAllDivTableData?.map(addRowIndexes);
    setDataInTableSlice({
      dispatch,
      allDivisionsTableData: transformedAllDivTableData,
      allDivisionAllowResp,
      currentDivisionId: selectedDivisionData?.divisionId,
      isSummary,
    });
    setExcludeDataOnLoad({
      excludedVendorData,
      excludeByKey,
      dispatch,
      allDivisionsTableData: transformedAllDivTableData,
    });
  };

  useEffect(() => {
    setTableDataOnPgLoad_Filter({
      selectedFiltersData,
      tableData,
      dispatch,
    });
  }, [selectedFiltersData, tableData]);

  /**
   * This useEffect is used to update the allowance amount when switch value changes.
   *
   */
  useEffect(() => {
    updateAmtOnSwitch();
  }, [switchValue?.selectedSwitch, selectedDivisionData?.divisionId]);

  useEffect(() => {
    if (allowGroupConfigObj && Object.keys(allowGroupConfigObj)?.length) {
      setTableContainerClasses();
    }
  }, [JSON.stringify(allowGroupConfigObj)]);

  useEffect(() => {
    if (!isSummary || !eventDetailsData?.id) return;
    if (pollingAttempts >= maxPollingAttempts) {
      setStopPolling(true);
      return;
    }
    if (
      allowItemDiffResp &&
      !allowItemDiffRespFetched &&
      eventDetailsData?.id
    ) {
      setTableDataForHighlightSummary();
      isTableEmpty.current = !allowItemDiffResp.allowances?.length;
      setStopPolling(true);
    } else if (!stopPolling) {
      setPollingAttempts(prev => prev + 1);
    }
  }, [allowItemDiffResp, allowItemDiffRespFetched, eventDetailsData]);

  const updateAmtOnSwitch = () => {
    const isSwitching =
      SWITCH_OPTIONS?.[currentAllowTmpData?.allowanceType]?.["isSwitching"];
    if (!isSwitching) return;
    dispatch(
      updateCaseAmountOnSwitch({
        switchValue: switchValue?.selectedSwitch,
        isOnlySwitchUpdate: true,
      })
    );
  };

  const setTableContainerClasses = () => {
    const wrapperClassName = classNames({
      tableWrapper: true,
      isHideColGroupHeader: isHideColGroupHeader,
    });
    setTableWrapClass(wrapperClassName);
  };

  const setTableDataForHighlightSummary = () => {
    setStopPolling(true);
    let transformed_data = transformAllowancesData(
      cloneDeep(allowItemDiffResp?.allowances),
      headerOnlyAmt,
      true,
      isEditMode,
      eventDetailsData?.allowanceType,
      true,
      allowItemDiffResp?.pendingChangesFlag,
      false,
      showLeadDistributorSection
    );
    //Adding index information to get the index of the updated text fields when validating for empty fields
    transformed_data = transformed_data.map((item, rowIndex) => ({
      rowIndex,
      ...item,
    }));
    dispatch(setTableDataAction(transformed_data));
  };

  // created the data for table according to the filtered and sorted Data
  const filteredAndSortedTableData = filteredAndSortedIds?.map(itemId => {
    return tableData?.find(item => item?.itemId === itemId);
  });

  const renderDetails = {
    isApiLoading:
      isAllowanceRespFetched || allowItemDiffRespFetched || isTempDelInprogress,
    apiError: {
      msg: (isAmountsFetchError || allowItemDiffRespError)?.data?.message,
    },
    isPageLevelSpinner: true,
    isRenderMainHtml: isSummary
      ? !allowItemDiffRespFetched && allowItemDiffResp && tableData?.[0]
      : tableData && tableData?.[0],
    noRecordsMessage: isTableEmpty?.current
      ? efConstants?.NORECORDS_FOUND_LABEL
      : "",
    renderHtml: (
      <>
        <div
          className={tableWrapClass}
          id="abs-allowances-table-allowance-table-component-div"
        >
          <NationalAllowanceTableComponent items={filteredAndSortedTableData} />
        </div>

        <AllowancesUpcDetails />
        <MainEntryOverlapsTable
          isSummary={isSummaryScreen}
          offerAllowancesId={offerAllowancesId}
          allowRespData={currentAllowData}
          allowDiffRespData={allowItemDiffResp}
        />
        {isPopupOpen ? (
          <CommonModal
            isModalPopupOpen={isPopupOpen}
            setModalPopupOpen={setIsPopupOpen}
            title={
              "Allowance amount changes for shared items will apply to all divisions where those items are available."
            }
            confirmBtnTitle={"OK"}
            showHideBtns={true}
            height={270}
            minHeight={270}
            width={830}
            onClose={() => setIsPopupOpen(false)}
            modalNameHandler={() => setIsPopupOpen(false)}
          />
        ) : null}
      </>
    ),
  };
  return <RenderStates details={renderDetails} />;
}

export default NationalOfferMainEntry;
