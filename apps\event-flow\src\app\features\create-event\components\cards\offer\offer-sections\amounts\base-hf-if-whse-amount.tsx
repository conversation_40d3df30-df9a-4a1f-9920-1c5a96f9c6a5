import { useSelectorWrap } from "@me/data-rtk";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";
import AllowanceAmountField from "./shared/components/allowance-amount-field";
import TopAmountSection from "./shared/components/top-amount-section";
import { dashedVerticalDivider } from "../../../allowance/stepper/common-stepper/allowance-amount/allowance-amounts-services";
import HeaderItemInfo from "./shared/components/header-item-info";
import UserActions from "./shared/components/user-actions";
import { IBaseAmtProp } from "./shared/props-types";
import { useHandleAmountFieldsChange } from "../../hooks/amounts/useHandleAmountFieldsChange";
import { memo, useMemo } from "react";
import AmountDisplay from "./shared/components/amount-display";
import { getIsNationalEvent } from "../../../../../../event-types/event-types-helper";

function BaseHfIfWhse({
  cardIndex,
  isEditEnable,
  sectionConfiguration,
  allowanceAmountFields,
  allowanceType,
  setAllowanceAmountData,
  allowancesResp,
  editViewAllItems,
  isZeroCost,
  formControls,
  isHfIfWhseCase = false,
  amountsInitialValueOnLoad = {},
  isSectionCompleted = false,
  isAmtSavedInTemp,
  amtSubLabelDisplayVal
}: IBaseAmtProp) {
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { isNDPAndFlagEnabled: isNational } = getIsNationalEvent(eventDetailsData?.eventType);
  allowancesResp = isNational && Array.isArray(allowancesResp) ? allowancesResp?.[0] : allowancesResp;
  const searchId = eventDetailsData?.offerAllowances?.[cardIndex]?.offerNumber;
  const isInValidOffer = eventDetailsData?.inValidAllowances?.includes(
    eventDetailsData?.offerAllowances?.[cardIndex]?.id
  );
  const isHeaderFlat =
    allowanceType === efConstants.ALLOWANCE_TYPES.HEADERFLAT.key;
  const {
    fields: { allowanceAmountPerItem, allowanceAmount },
    description = {},
  } = sectionConfiguration;
  const {
    HF_WHSE_DESC = "",
    HF_IF_WHSE_DESC = "",
    HF_IF_BASE_DESC = "",
  } = description;
  const fieldProp = isHfIfWhseCase ? allowanceAmount : allowanceAmountPerItem;
  const descText = isHfIfWhseCase
    ? isHeaderFlat
      ? HF_WHSE_DESC
      : HF_IF_WHSE_DESC
    : HF_IF_BASE_DESC;

  const getEventData = () => {
    return eventDetailsData;
  };
  const {
    itemCount,
    totalAmount,
    digitError,
    differentAmountFound,
    wareHouseCount,
    handleAmountChange,
  } = useHandleAmountFieldsChange({
    allowancesResp,
    allowanceType,
    formControls,
    initialValues: amountsInitialValueOnLoad,
    setAllowanceAmountData,
    isHeaderFlat,
    cardIndex,
    fieldProp,
    isHfIfWhseCase,
    isEditEnable,
    isAmtSavedInTemp
  });
  const renderAllowanceAmount = () => (
    <div
      className="relative max-w-[280px]"
      id="abs-base-HF-IF-WHSE-allowance-amount-span-one"
    >
      <AllowanceAmountField
        fieldProps={fieldProp}
        formControls={formControls}
        onChange={handleAmountChange}
        error={{ message: digitError }}
        allowanceAmountFields={allowanceAmountFields}
        getEventData={getEventData}
        searchId={searchId}
        baseId="abs-base-HF-IF-allowance-amount"
      />
      {differentAmountFound && (
        <span className="text-xs text-red-600">
          Different item Amounts Entered
        </span>
      )}
    </div>
  );
  const renderTopSection = (
    <TopAmountSection
      description={descText}
      baseId="abs-base-HF-IF-allowance-amount"
    />
  );
  const renderBottomSection = (
    <>
      <div
        className="mr-0 mb-5 mt-5 flex"
        id="abs-base-HF-IF-allowance-amount-dashed-vertical-divider-two"
      >
        {!isNational && (
          <div
            className="flex gap-3"
            id="abs-base-HF-IF-allowance-amount-dashed-vertical-divider-three"
          >
            {renderAllowanceAmount()}
            {dashedVerticalDivider}
            <HeaderItemInfo
              itemCount={itemCount}
              totalAmount={totalAmount}
              hasItemCount={!isZeroCost}
              isHfIfWhseCase={isHfIfWhseCase}
              wareHouseCount={wareHouseCount}
            />
            {dashedVerticalDivider}
            <div className="mr-2"></div>
          </div>
        )}
        {amtSubLabelDisplayVal && isNational &&  (
          <div className="flex gap-4">
            <AmountDisplay
              label={"Allowance Amount"}
              value={amtSubLabelDisplayVal}
              baseId="abs-basic-hf-if-amount"
              directDisplay={true}
            />
            {dashedVerticalDivider}
          <div className="mr-2"></div>
          </div>
        )}
        <UserActions
          actionsConfig={useMemo(() => {
            return {
              isEditEnable,
              isSectionCompleted,
              isFormDirty: formControls?.formState?.isDirty,
              baseId: "abs-basic-allowance-amount",
              onClick: editViewAllItems,
              sectionConfiguration,
              isInValidOffer,
              isHideFieldsForMainEntry: isNational,
            };
          }, [
            isEditEnable,
            isSectionCompleted,
            JSON.stringify(formControls?.formState?.isDirty),
            isInValidOffer,
            isNational,
          ])}
        />
      </div>
    </>
  );

  const renderHtml = (
    <div id="abs-base-HF-IF-allowance-amount-container-one">
      {renderTopSection}
      {renderBottomSection}
    </div>
  );
  return renderHtml;
}
export default memo(BaseHfIfWhse);
