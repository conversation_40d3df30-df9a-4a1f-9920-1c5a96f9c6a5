import React from "react";
import "@testing-library/jest-dom";
import { render, screen, fireEvent } from "@testing-library/react";
import AccordionAtom from "./accordion-container";

describe("AccordionAtom Component", () => {
  const mockDivision = { divisionId: "101", divisionName: "East Division" };

  it("renders the component correctly with division details", () => {
    render(<AccordionAtom division={mockDivision} isExpand={false} children={undefined} />);

    // Verify division text is present
    expect(screen.getByText("101 - East Division")).toBeInTheDocument();

    // Ensure accordion body content is not present initially
    expect(screen.queryByTestId("accordion-body")).not.toBeInTheDocument();
  });

  it("expands the accordion when isExpand is true initially", () => {
    render(
      <AccordionAtom division={mockDivision} isExpand={true}>
        <div data-testid="accordion-body">Accordion Content</div>
      </AccordionAtom>
    );

    // Verify that the accordion content is rendered
    expect(screen.getByTestId("accordion-body")).toBeInTheDocument();
  });

  it("toggles the accordion open and close on header click", () => {
    render(
      <AccordionAtom division={mockDivision} isExpand={false}>
        <div data-testid="accordion-body">Accordion Content</div>
      </AccordionAtom>
    );

    const header = screen.getByText("101 - East Division");

    // Ensure content is not present initially
    expect(screen.queryByTestId("accordion-body")).not.toBeInTheDocument();

    // Click to expand
    fireEvent.click(header);
    expect(screen.getByTestId("accordion-body")).toBeInTheDocument();

    // Click to collapse
    fireEvent.click(header);
    expect(screen.queryByTestId("accordion-body")).not.toBeInTheDocument();
  });

  it("renders child elements when expanded", () => {
    render(
      <AccordionAtom division={mockDivision} isExpand={true}>
        <div data-testid="child-content">Child Content</div>
      </AccordionAtom>
    );

    // Check if child content is rendered
    expect(screen.getByTestId("child-content")).toBeInTheDocument();
  });
});
