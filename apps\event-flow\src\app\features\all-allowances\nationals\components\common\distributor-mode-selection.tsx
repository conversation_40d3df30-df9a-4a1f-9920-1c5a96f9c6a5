import Radio from '@albertsons/uds/molecule/Radio';
import efConstants from 'apps/event-flow/src/app/shared/ef-constants/ef-constants';
import React from 'react'
type Props = {
  distMode: string;
  setDistMode?: (mode: string) => void;
  distConfig: any;
  isExternalVendor?: boolean;
  isBillingSection: boolean;
};
function DistributorModeSelection({
  distMode,
  setDistMode = () => null,
  distConfig,
  isExternalVendor = false,
  isBillingSection = false,
}: Props) {
  const disableLeadDistOption = (distOption: string) =>
    isBillingSection
      ? distConfig?.[distOption]?.disable
      : isExternalVendor &&
        distConfig?.[distOption]?.value === efConstants.LEAD_DIST_ONLY;
  return (
    <div className="eet py-5 border-b mx-5 mb-8">
      <div className="flex flex-col">
        <Radio.Group
          horizontal={true}
          value={distMode}
          onChange={(e) => setDistMode?.(e)}
          className="lead-distributor-radio-group"
        >
          {Object.keys(distConfig)?.map((distOption) => (
            <Radio
              label={distConfig[distOption]?.displayLabel}
              value={distConfig[distOption]?.value}
              key={distConfig[distOption]?.key}
              disabled={disableLeadDistOption(distOption)}
            />
          ))}
        </Radio.Group>
        <div className="flex justify-around">
          <div style={{ width: "41%" }} className="text-[#5A697B]">
            {distConfig?.LEAD_DIST_ONLY?.subText}
          </div>
          <div className="pl-[3.2rem] text-[#5A697B]" style={{ width: "43%" }}>
            {distConfig?.BILL_LEAD_DIST?.subText}
          </div>
        </div>
      </div>
    </div>
  );
}

export default React.memo(DistributorModeSelection);
