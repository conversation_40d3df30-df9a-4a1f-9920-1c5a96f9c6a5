import React, { useEffect, useState } from "react";
import { MessageSquare } from "lucide-react";
import { useGetCommentsDataQuery } from "../../../create-event/service/apis/event-api";
import { useParams } from "react-router-dom";
import {
    getLatestCommentByReduce,
    getLatestCommentsAndCount,
    isEventHaveCommentTask,
} from "./event-comments-collapsed.service";
import Tag from "@albertsons/uds/molecule/Tag";
import {
    getLoggedInUserType,
} from "@me-upp-js/utilities";
import { EUSER_ROLES } from "../event-action/event-action.model";
import { eventCommentsDataHandler } from "../../../create-event/service/slice/event-comments-slice";
import { useDispatch } from "react-redux";
import { useSelectorWrap } from "@me/data-rtk";
import EventCommentsSection from "../../event-comments-section";
import { DeepClone, EEVENT_STATUS, formatDateTimeWithZone, useGetQueryParams } from "@me/util-helpers";

import { useFormContext } from "react-hook-form";
import { RenderStates } from "@me/ui-render-states";
import { setIsFromCommentSection } from "../../slices/event-types-slice";
import { Info } from "lucide-react";
import { EVENT_COMMENTS } from "../../constants/event-comments-constants";
import { appConstants } from "@me/utils-root-props";
import { isUserComingFromTaskPage } from "../../event-comments-section-service";
import { getDecodedValue } from "@me-upp-js/features/event-history";

// Props interface
interface EventCommentSectionProps {
    eventID?: string;
    commentViewData?: any;
    setCommentOpen?: any;
}

const EventCommentSection: React.FunctionComponent<EventCommentSectionProps> = ({
    eventID,
    commentViewData,
    setCommentOpen,
}) => {

    const [comments, setComments] = useState([]);
    const [commentsCount, setCommentsCount] = useState(0);
    const [isEventCommentsOpen, setIsEventCommentsOpen] = useState(false);
    const { id } = useParams();
    const {
        queryParamValue: { taskType },
    } = useGetQueryParams([appConstants.TASK_TYPE_QUERY_PARAM]);
    const { getValues } = useFormContext();
    const { eventStatus } = getValues();

    const userRole = getLoggedInUserType();
    const dispatch = useDispatch();
    const { data: eventCommentsData } = useSelectorWrap("event_comments_data");
    const { data: eventDetailsData } = useSelectorWrap("event_details_data");
    const commentConfig = {
        External: {
            color: "#FFEDAC",
        },
        Internal: {
            color: "#BCDFFD",
        },
        "Billing Inquiry": {
            color: "#BCDFFD",
        },
    };

    const {
        isFetching: isCommentsDataloading,
        data: commentsData,
        refetch,
    } = useGetCommentsDataQuery(
        {
            URL_PARAM: [eventID || id],
        },
        { skip: !id }
    );

    useEffect(() => {
        commentViewData && setIsEventCommentsOpen(commentViewData);
    }, [commentViewData]);

    useEffect(() => {
        if (commentsData) {
            setCommentsData();
            setLatestCommentsAndCount();
        }
    }, [commentsData, eventDetailsData]);

    useEffect(() => {
        const isFromTaskPage = isUserComingFromTaskPage(taskType);
        isFromTaskPage && commentDrawer();
    }, [commentsData]);

    const setCommentsData = () => {
        const isCommentHaveTask = isEventHaveCommentTask(eventDetailsData),
            commentsDataFormed = isCommentHaveTask
                ? mapCommentsWithTasks(commentsData)
                : commentsData;
        dispatch(
            eventCommentsDataHandler({ eventCommentsData: commentsDataFormed })
        );
    };

    useEffect(() => {
        setLatestCommentsAndCount();
    }, [eventCommentsData]);

    const setLatestCommentsAndCount = () => {
        const { latestComments = [], commentsCount = 0 } =
            getLatestCommentsAndCount(eventCommentsData.eventCommentsData);
        setCommentsCount(commentsCount);
        setComments(latestComments);
    };

    const mapCommentsWithTasks = commentsData => {
        return commentsData?.map(comment =>
            EVENT_COMMENTS.TASKS_COMMENTS_CATEGORY?.includes(comment?.commentCategory)
                ? getMappedTaskData(comment)
                : comment
        );
    };

    const getMappedTaskData = comment => {
        const {
            planEventTasks = [],
            planEvent,
            eventStatus,
        } = eventDetailsData || {},
            tasksList =
                eventStatus === EEVENT_STATUS.DRAFT
                    ? planEventTasks
                    : planEvent?.planEventTasks;
        return {
            ...comment,
            eventComments: comment.eventComments.map(eventComment => ({
                ...eventComment,
                ...(tasksList?.find(
                    task => task?.planEventCommentId === eventComment?.id
                ) || {}),
            })),
        };
    };

    const Comment = ({ comment, children }) => {
        const messageText = getDecodedValue(comment?.messageText);
        return (
            <div className="flex flex-col gap-2 mx-[16px] my-[8px]">
                {children}
                <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#2b303c] break-words">
                    {messageText.split(/(?:\r\n|\r|\n)/g).map(line => {
                        return <div>{line}</div>;
                    })}
                </p>
                {comment?.type === "External" && comment?.sendBackWithComment ? (
                    <div className="flex items-center">
                        <Info color="#AB422D" size={15} />
                        <span className="text-[#AB422D] text-sm ml-1">
                            {EVENT_COMMENTS?.EVENT_SENT_BACK_FLAG}
                        </span>
                    </div>
                ) : null}
                <div className="flex items-center flex-grow-0 flex-shrink-0 text-xs text-left text-[#5a697b]">
                    <span>{`${comment?.createUserNm} on ${formatDateTimeWithZone(
                        comment?.lastUpdTs
                    )}`}</span>
                </div>
                <div className="flex border-t border-[#C8DAEB]" />
            </div>
        );
    };

    const addNewCommentFromSuccessfulPost = newCommentFromPost => {
        refetch();
        if (newCommentFromPost) {
            // const newEventComments = newCommentFromPost.eventComments;
            const newObj: any = new DeepClone(eventCommentsData).getDeepClone();
            const indexToReplace = newObj.eventCommentsData.findIndex(
                i => i.commentCategory === newCommentFromPost.commentCategory
            );
            if (indexToReplace < 0) {
                newObj.eventCommentsData.push(newCommentFromPost);
            } else {
                newObj.eventCommentsData[indexToReplace] = newCommentFromPost;
            }
            dispatch(eventCommentsDataHandler({ eventComments: [newObj] }));
        }
    };

    const TagComponent = ({ comment, color }) => {
        return (
            <Tag
                backgroundColor={color}
                textColor="#000000"
                className="font-bold border-r-8 [&>span]:text-[16px] [&>span]:leading-5 [&>span]:m-0 [&>span]:py-1 [&>span]:px-3"
                label={comment?.type}
            />
        );
    };
    const commentDrawer = () => {
        if (userRole === EUSER_ROLES.MERCHANT) {
            dispatch(
                setIsFromCommentSection({
                    isFromComment: true,
                    currentEventStatus: eventStatus,
                })
            );
            setIsEventCommentsOpen(true);
        } else if (userRole === EUSER_ROLES.VENDOR && eventStatus !== "Draft") {
            setIsEventCommentsOpen(true);
        } else if (
            userRole === EUSER_ROLES.VENDOR &&
            eventStatus === "Draft" &&
            window["OAM_REMOTE_USER"]?.toUpperCase() ===
            eventDetailsData?.createUser?.userId?.toUpperCase()
        ) {
            setIsEventCommentsOpen(true);
        }
    };

    return (
      <div id="abs-event-comment-section-container-div" className="pr-6">
        <div
          id="abs-event-comment-section-container-btn-div"
          className="flex gap-1"
          onClick={() => {
            commentDrawer();
          }}
        >
          <MessageSquare
            width={24}
            height={24}
            color="#1B6EBB"
            className="inline-block cursor-pointer"
          />
          <span className="gap-1 text-[#1b6ebb] items-center cursor-pointer abs-ef-event-status-badge">
            Comments
          </span>
        </div>
        {eventID ? (
          <EventCommentsSection
            eventId={eventID || ""}
            isEventCommentsOpen={isEventCommentsOpen}
            setIsEventCommentsOpen={setIsEventCommentsOpen}
            allCommentsList={eventCommentsData.eventCommentsData}
            isEventCommentsLoading={isCommentsDataloading}
            parentCommentUpdater={addNewCommentFromSuccessfulPost}
            setCommentOpen={setCommentOpen}
          />
        ) : null}
      </div>
    );
};

export default EventCommentSection;
