import _ from "lodash";
import moment from "moment";
import efConstants from "../../shared/ef-constants/ef-constants";
import { getDatesFromTempWork } from "../create-event/service/allowance/allowance-service";
import { getValidBillingInfo } from "./allowance-lead-distributors/billing-selection-utils";
import { updateKeysOnSwitch } from "./allowance-table/allowance-table-data-utils";
import { EUSER_ROLES, isNotDefaultDate } from "@me/util-helpers";

const { TRANSFORMED_VENDOR_KEY, VENDOR_DIST_CENTER_KEY, ALLOWANCE_TYPES } =
  efConstants;

export const getEmptyFields = ({ tableData: data }, isDistCenter = false) => {
  //Returns details of empty fields with itemId and vendor Id

  const checkForEmptyField = vendor => {
    const valTocheck = vendor.allowanceAmount;
    return valTocheck === null || valTocheck === "" || valTocheck === ".";
  };
  const getEmptyAllowancesByItemId = itemId => {
    return data
      .filter(item => item.itemId === itemId)
      .flatMap(item =>
        item.vendorDetails
          .filter(vendor => isDoValidate(vendor) && checkForEmptyField(vendor))
          .map(
            vendor =>
              vendor[
                isDistCenter ? VENDOR_DIST_CENTER_KEY : TRANSFORMED_VENDOR_KEY
              ]
          )
      );
  };

  const emptyFieldsDetails = {};

  data.forEach(item => {
    const emptyAllowances = getEmptyAllowancesByItemId(item.itemId);
    if (emptyAllowances.length > 0) {
      emptyFieldsDetails[item.itemId] = emptyAllowances;
    }
  });

  return { emptyFieldsDetails };
};

const isDoValidate = vendor => {
  return (
    (vendor?.leadDistributorInd ||
      vendor?.leadDistributorMode === efConstants.BILL_LEAD_DIST) &&
    vendor?.includeInd
  );
};

/**
 * this function is to generate the allowance Temp Object from TableData
 * @param param0
 * @returns
 */
export const generateAllowanceTempObject = ({
  tableData,
  dataFromAmountsAPI,
  switchValue,
  allowGroupConfigObj,
  headerOnlyAmtsArr,
  tempAllowItems,
  isAllowConvEnable,
  isNational = false,
}) => {
  if (!tableData.length) return;

  const {
    isSwitchingRequires,
    headerOnlyAmt,
    impactedFieldsOnTable = [],
    vendorGridCols: { switchColumnsCase } = { switchColumnsCase: null },
  } = allowGroupConfigObj || {};

  const checkIfUpdateRespOnSwitch = () => {
    return (
      isSwitchingRequires && switchColumnsCase?.[switchValue]?.isOnlyDisplay
    );
  };

  const getupdateKeysOnSwitch = itemObj => {
    if (checkIfUpdateRespOnSwitch()) {
      return updateKeysOnSwitch(itemObj);
    }
    return {};
  };

  const updateRootAllowanceObj = ({ allow, vendorIndex }) => {
    //Updates the root allowance obj
    if (headerOnlyAmt && headerOnlyAmt.headerOnlyAmtKey) {
      const allowCopy = JSON.parse(JSON.stringify(allow));
      allowCopy[headerOnlyAmt.headerOnlyAmtKey] =
        headerOnlyAmtsArr[vendorIndex];
      return allowCopy;
    }
    return allow;
  };
  const updateHeaderOnlyAmt = ({allow, items}) => {
    //Updates the header only amount
    if (headerOnlyAmt && headerOnlyAmt?.headerOnlyAmtKey) {
      const allowCopy = JSON.parse(JSON.stringify(allow));
      allowCopy[headerOnlyAmt.headerOnlyAmtKey] = items?.headerFlatAmt;
      return allowCopy;
    }
    return allow;
  }

  const getUpdatedItems = (tableData, vendorIndex) => {
    const updatedItems = tableData?.reduce(
      (resultArr: any = [], item, itemIndex) => {
        const isEmptyVendor =
          tableData[itemIndex]?.vendorDetails?.[vendorIndex]?.isEmptyVendor;
        if (!isEmptyVendor) {
          resultArr.push(getItemObject(item, vendorIndex, itemIndex));
        }
        return resultArr;
      },
      []
    );
    return updatedItems;
  };
  const allowancesData = dataFromAmountsAPI?.allowances?.map(
    (allow, vendorIndex: number) => {
      const items =
        tableData.find(row => !row.vendorDetails[vendorIndex]?.isEmptyVendor)
          ?.vendorDetails?.[vendorIndex] || {};

      return {
        ...(isNational ? updateHeaderOnlyAmt({ allow, items }) : updateRootAllowanceObj({ allow, vendorIndex })),
        allowanceBillingInfo: getValidBillingInfo(
          dataFromAmountsAPI?.allowances,
          vendorIndex,
          items,
          tableData
        ),
        leadDistributorInfos: items?.leadDistributorInfos || [],
        createAllowInd: items?.createAllowInd || false,
        includeInd: items?.includeInd || false,
        finalizedAmountsInd: true,
        leadDistributorInd: items?.leadDistributorInd || false,
        leadDistributorMode: items?.leadDistributorMode,
        allowanceItems: [...getUpdatedItems(tableData, vendorIndex)],
        ...(isAllowConvEnable
          ? { ...getDatesFromTempWork(tempAllowItems[vendorIndex]) }
          : {}),
      };
    }
  );

  return {
    ...dataFromAmountsAPI,
    allowances: allowancesData,
  };

  function getItemObject(item, vendorIndex, itemIndex) {
    const itemObj = dataFromAmountsAPI.allowances[
      vendorIndex
    ].allowanceItems.find(i => i.itemId === item.itemId);

    const updatedItemObj = {
      ...itemObj,
      ..._.pick(tableData[itemIndex].vendorDetails[vendorIndex], [
        ...impactedFieldsOnTable,
      ]),
    };

    return {
      ...updatedItemObj,
      allowanceAmount: Number(updatedItemObj.allowanceAmount) || 0,
      ...getupdateKeysOnSwitch(updatedItemObj),
    };
  }
};

export const generateAllowanceTempObjectForAllDiv = ({
  allDivisionsTableData,
  allDivisionAllowancesRespCopy,
  switchValue,
  allowGroupConfigObj,
  headerOnlyAmtsArr = [],
  allowName,
  isAllowConvEnable,
  allowanceTempWorkData,
}) => {
  return allDivisionAllowancesRespCopy?.map(
    (currentAllowRespObj) => {
      const tableData =
        allDivisionsTableData?.find(
          e => e?.divisionId === currentAllowRespObj?.divisionId
        )?.tableData || [];
      const currentTempObj = allowanceTempWorkData?.find(
        e => e?.divisionId === currentAllowRespObj?.divisionId
      );
      const tempAllowItems =
        Object.values(
          currentTempObj?.allowanceTypeSpecification?.[allowName]
            ?.allowancesMap || {}
        )?.[0] || [];
      return generateAllowanceTempObject({
        tableData,
        dataFromAmountsAPI: currentAllowRespObj,
        switchValue,
        allowGroupConfigObj,
        headerOnlyAmtsArr,
        tempAllowItems,
        isAllowConvEnable,
        isNational: true,
      });
    }
  );
};

export function toFixedIfNecessary(value) {
  return (Math.round(value * 100) / 100).toFixed(2);
}

export const isShowExcludeAllowCheckBox = (data, index) => {
  const vendorDetailsLength = data?.vendorDetails?.length || 0,
    isNotLeadDist = data?.vendorDetails?.[index]?.leadDistributorInd,
    leadDistMode = data?.vendorDetails?.[index]?.leadDistributorMode;

  return (
    vendorDetailsLength > 1 &&
    (!leadDistMode || (leadDistMode && !isNotLeadDist))
  );
};

export const check_isAnyColumnAllZero = (tableData, isDsdVendor = true) => {
  const _key = isDsdVendor ? "vendorNbr" : "distCenter";
  const isHeaderFlat =
    tableData?.[0]?.allowanceType === ALLOWANCE_TYPES.HEADERFLAT.key;
  const maximumAmountPerAllowance = {};
  tableData?.forEach(item => {
    item?.vendorDetails?.forEach(vendor => {
      maximumAmountPerAllowance[vendor?.[_key]] = vendor?.isEmptyVendor
        ? 0 + Number(maximumAmountPerAllowance[vendor?.[_key]])
        : vendor?.includeInd
        ? (Number(maximumAmountPerAllowance[vendor?.[_key]]) || 0) +
          Number(isHeaderFlat ? vendor?.headerFlatAmt : vendor?.allowanceAmount)
        : 1;
    });
  });
  return Object.entries(maximumAmountPerAllowance)
    ?.filter(entry => entry[1] === 0)
    ?.map(vendor => vendor?.[0]);
};

export const checkForEndDateIsBeforeCurrentDate = data => {
  const endDate = moment(data?.effectiveEndDate);
  return endDate?.isBefore(moment());
};

export const checkIsItemRemoved = data => {
  return (
    isNotDefaultDate(data?.effectiveEndDate) &&
    checkForEndDateIsBeforeCurrentDate(data)
  );
};

export const getMinValueFromTable = ({
  tableData,
  userRole,
  allowUnitCostKey,
  isSkipLessThanAmtValidation,
  ZERO_COST_TEXT
}) => {
  if (!tableData?.length) return Number.MAX_VALUE;
  return Math.min(
    ...(tableData || [])?.map(row => {
      const vendors =
        userRole === EUSER_ROLES?.MERCHANT
          ? row?.vendorDetails
          : row?.vendorDetails?.filter(
              row => row?.itemLevelModCommand !== ZERO_COST_TEXT
            );
      return Math.min(
        ...vendors.map(vendor =>
          vendor?.isEmptyVendor || isSkipLessThanAmtValidation
            ? Number.MAX_VALUE
            : vendor[allowUnitCostKey]
        )
      );
    })
  );
};
