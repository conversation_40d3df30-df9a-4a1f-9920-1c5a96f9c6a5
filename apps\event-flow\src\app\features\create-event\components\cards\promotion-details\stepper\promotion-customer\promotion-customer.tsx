import Button from "@albertsons/uds/molecule/Button";
import * as React from "react";
import { useFormContext } from "react-hook-form";
import { InputRadioButton } from "@me/input-fields";

interface IPromotionCustomerProps {
  saveAndContinueHandler?: any;
  stepperElement?: any;
  cardIndex?: any;
  cardItemIndex?: any;
}

const PromotionCustomer: React.FunctionComponent<IPromotionCustomerProps> = ({
  saveAndContinueHandler,
  stepperElement,
  cardItemIndex,
  cardIndex,
}) => {
  const {
    fields: { customers },
  } = stepperElement;
  const { getValues, setValue, register } = useFormContext();
  const promotionRegField = `promotionsLists[${cardIndex}].promotionsList[${cardItemIndex}]`;
  const promotion = getValues(promotionRegField);
  const dynamicRegisterField = `${promotionRegField}.${customers.registerField}`;
  const onCustomerSelectSave = () => {
    !getValues(dynamicRegisterField) &&
      setValue(dynamicRegisterField, customers.default);
    const eventVehicle = getValues("eventCreationVehicle");
    const promoVehicle = promotion?.vehicle;
    setValue(
      `${promotionRegField}.vehicle`,
      promoVehicle ? promoVehicle : eventVehicle
    );
    setValue(
      `${promotionRegField}.promoStartDate`,
      promotion?.promoStartDate
        ? promotion?.promoStartDate
        : eventVehicle.startDate
    );
    setValue(
      `${promotionRegField}.promoEndDate`,
      promotion?.promoEndDate ? promotion?.promoEndDate : eventVehicle.endDate
    );
    saveAndContinueHandler(1);
  };
  const onCustomerChange = ele => {
    // setIsCustomerSelected(true);
  };
  return (
    <>
      <div className="flex mb-4" id="promotion-customer-cont1">
        {customers ? (
          <InputRadioButton
            onChange={onCustomerChange}
            fieldProps={customers}
            dynamicRegisterField={dynamicRegisterField}
          />
        ) : null}
      </div>
      <div className="p-3 pl-[0px]" id="promotion-customer-cont2">
        <Button
          width={180}
          // disabled={isCustomerSelected}
          onClick={(event: any) => {
            event?.preventDefault();
            event?.stopPropagation();
            onCustomerSelectSave();
          }}
        >
          Save & Continue
        </Button>
      </div>
    </>
  );
};

export default PromotionCustomer;
