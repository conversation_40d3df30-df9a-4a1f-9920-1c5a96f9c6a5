import { nationalEventsFlagEnabled } from "@me-upp-js/utilities";
import efConstants from "../../shared/ef-constants/ef-constants";
import { EVENT_TYPE } from "../create-event/constants/constants";

export const getIsNationalEvent = (eventType: string) => {
  const { NATIONAL_EVENTS_ROLES } = efConstants;
  const permissionsData = localStorage.getItem("USER_ROLE_PERMISSIONS");
  const userDetails = permissionsData ? JSON.parse(permissionsData) : [];
  const isNationalEvent = ["NDP", "NAO"].includes(eventType);
  const isNationalEventsFlagEnabled = nationalEventsFlagEnabled();
  const isNDPAndFlagEnabled = isNationalEvent && isNationalEventsFlagEnabled;
  const permissionList = [
    ...new Set(userDetails?.flatMap(item => item.permissions)),
  ];

  const nationalRolesAvailable = NATIONAL_EVENTS_ROLES.some(role =>
    permissionList.includes(role)
  );
  const isNationalPermit = nationalRolesAvailable && isNationalEventsFlagEnabled;
  return {
    isNationalEvent,
    nationalRolesAvailable,
    nationalEventsFlagEnabled: isNationalEventsFlagEnabled,
    isNDPAndFlagEnabled,
    isNationalPermit,
    isNCDP: eventType === EVENT_TYPE.NCDP,
  };
};
