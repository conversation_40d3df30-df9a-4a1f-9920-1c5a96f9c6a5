//

.month-position {
  left: 290px !important;
}

.year-position {
  left: 403px !important;
}

.sw-calendarWrapper {
  .quarterEndDay,
  .periodEndDay,
  .holiday {
    border-width: 1px;
    border-style: solid;
    border-radius: 13px;
  }

  .periodEndDay {
    border-color: #000;
  }
  .quarterEndDay {
    background-color: #000;
    color: #fff;
  }
  .holidayDay {
    background-color: #c74e4e;
    color: #fff;
  }
  &.rmdp-border {
    border: 0;
  }
  .rmdp-week-day {
    font-size: 13px;
    color: #5a697b;
  }
  .rmdp-day-picker > div {
    &:first-child {
      margin-right: 20px !important;
    }
  }
  .rmdp-day,
  .rmdp-week-day {
    height: 32px;
    width: 32px;

    &.rmdp-range {
      color: #000;
      background-color: #e0edfb;
      box-shadow: none;

      &.start,
      &.end {
        background-color: #93cbfe;
      }
      &.start {
        border-top-left-radius: 3.11px;
        border-bottom-left-radius: 3.11px;
      }
      &.end {
        border-top-right-radius: 3.11px;
        border-bottom-right-radius: 3.11px;
      }
    }
  }
.rmdp-day.rmdp-today span {
  // background-color: #7fdbff !important;
      border-radius: 6%;
        font-weight: bold;
        background-color: white !important;
        color: rgb(27 110 187/var(--tw-bg-opacity)) !important;
}

  .rmdp-month-picker,
  .rmdp-year-picker {
    top: -10px;
    border: 1px solid cornflowerblue;
    .rmdp-day {
      width: auto;
      width: 100%;
      text-align: center;
    }
  }

  .rmdp-month-picker {
    width: 101px;

    left: 19px;

    .rmdp-ym {
      display: block;
      height: auto;
      .rmdp-day {
        display: inline-block;
        width: 83px;
        span {
          position: relative;
        }
      }
    }
  }
  .rmdp-year-picker {
    width: 120px;
    left: 132px;
  }
}

.rmdp-day-picker {
  height: 222px;
}

.rmdp-range-picker-footer {
  position: relative;
  text-align: center;
  font-size: 10px;
  display: flex !important;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  height: 60px;
  padding: 0;
  border-top: 0;
  margin-top: 10px;
  width: auto;
  padding-left: 0;
}

.rmdp-border-bottom {
  border-bottom: none !important;
}

.rmdp-range-picker-footer h6 {
  height: 0px !important;
}
.rmdp-header-values {
  .month-picker {
    width: 113px;
  }
}
.rmdp-header-values > span {
  padding: 0 5px !important;
}

.rmdp-header-values > div {
  padding: 0;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  background-color: white;
}

.rmdp-header-values > div > span {
  max-height: 10px;
  font-size: 10px;
  padding: 0;
}

.rmdp-cancel {
  display: none;
}