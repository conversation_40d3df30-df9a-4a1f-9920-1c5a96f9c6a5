import { useSelectorWrap } from "@me/data-rtk";

export const useGetSelectors = () => {

  const { data: allowanceForm } = useSelectorWrap("allowance_form_data") || {};
  const { productSources = [] } =
    useSelectorWrap("allowance_product_sources_rn")?.data || {};
  const allowanceTempWorkData = useSelectorWrap("allowance_temp_work")?.data
    ?.allowanceData || {};
  const { isAllowConvEnable } = useSelectorWrap("allow_conv_status_rn").data;
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
    const offerSectionsEnableConfig =
    useSelectorWrap("offer_sections_enable_config")?.data || {};

  return {
    allowanceForm,
    productSources,
    allowanceTempWorkData,
    isAllowConvEnable,
    eventDetailsData,
    offerSectionsEnableConfig
  };
};
