import { createGenericSlice } from "@me/data-rtk";
import { current } from "@reduxjs/toolkit";

export const childEventsDataSlice = createGenericSlice({
  name: "child_events_details_data",
  initialState: {
    status: "loading",
    data: {},
  },
})({
  setChildEventsDetailsData(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
  updateChildEventGridData(state, { payload }) {
    const { data } = current(state);
    state.data = updateData(data, payload);
  },
});

export const { setChildEventsDetailsData, updateChildEventGridData } =
  childEventsDataSlice.actions;

export const childOffersGridData = createGenericSlice({
  name: "child_offers_details_data",
  initialState: {
    status: "loading",
    data: {},
  },
})({
  setChildOffersGridData(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const { setChildOffersGridData } = childOffersGridData.actions;

export const childEventTabConfig = createGenericSlice({
  name: "child_events_tab_config_data",
  initialState: {
    status: "loading",
    data: {},
  },
})({
  setChildEventTabConfigData(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});
export const { setChildEventTabConfigData } = childEventTabConfig.actions;

export const selectedDivisionsSlice = createGenericSlice({
  name: "selected_child_events_data",
  initialState: {
    status: "loading",
    data: [] as any[],
  },
})({
toggleSelectedChildEventDivIds(state, { payload }) {
    const id: any = payload;
    if (state?.data?.includes(id)) {
      state.data = state.data.filter(item => item !== id);
    } else {
      state.data = [...(state.data as any[]), id];
    }
  },
  resetSelectedChildEventDivIds(state) {
    state.data = [];
  }
});
export const { toggleSelectedChildEventDivIds, resetSelectedChildEventDivIds } = selectedDivisionsSlice.actions;


export const refetchChild_EventSlice = createGenericSlice({
  name: "refetchChild_EventSlice",

  initialState: {
    status: "loading",
    data: {},
  },
})({
  doRefetchChildEventAapi(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
});

export const { doRefetchChildEventAapi } = refetchChild_EventSlice.actions;

const updateData = (data, payload) => {
  const childData = {
    ...data,
    eventDetails: data?.eventDetails?.map((event, index) => {
      if (index === payload.cardIndex) {
        return {
          ...event,
          events: event.events.map(payloadData => {
            if (payloadData?.rowIndex === payload.rowIndex) {
              return {
                ...payloadData,
                [payload.key]: payload.value,
              };
            } else {
              return {
                ...payloadData,
              };
            }
          }),
        };
      }
      return {
        ...event,
      };
    }),
  };
  return {
    ...childData,
  };
};
