import moment from "moment";
import {
  getAllowanceDraftCollections,
  isPendingCollectionUser,
} from "./event-hook-form-wrapper-service";
import { EEVENT_STATUS, EUSER_ROLES } from "@me/util-helpers";
import { getLoggedInUserType, getNationalOfferStatus } from "@me-upp-js/utilities";
import { EVENT_ACTION_LABELS } from "./components/event-action/event-action.model";
import efConstants from "../../shared/ef-constants/ef-constants";
import { getIsNationalEvent } from "./event-types-helper";

enum MODULES {
  EVENT = "EVENT",
  ALLOWANCE = "ALLOWANCE",
  PROMOTION = "PROMOTION",
}

export const getPendingModule = eventIndicators => {
  return Object.keys(eventIndicators).filter(key => {
    return eventIndicators[key];
  });
};

export const getIsPendingCollection = eventDetails => {
  const userType = getLoggedInUserType();

  const {
    eventDetailsEventInd,
    allowanceEventInd,
    promotionEventInd,
    otherDetailsChangedInd,
    pidDetailsEventInd,
    planEventPending,
    planEvent,
  } = eventDetails;
  const pidCheck = pidDetailsEventInd && userType === EUSER_ROLES?.VENDOR;
  const pendingModule = getPendingModule({
    EVENT: eventDetailsEventInd && (otherDetailsChangedInd || pidCheck),
    ALLOWANCE: allowanceEventInd,
    PROMOTION: promotionEventInd,
  });
  if (pendingModule.length) {
    let moduleData = {};
    switch (pendingModule[0]) {
      case MODULES.EVENT:
        moduleData = { ...planEventPending };
        break;
      case MODULES.PROMOTION:
        moduleData = [...(planEventPending?.promotionsList || [])];
        break;
      case MODULES.ALLOWANCE:
        moduleData = [...(planEventPending?.offerAllowances || [])];
        break;

      default:
        break;
    }

    return isPendingCollectionUser(moduleData);
  }

  const isDraftRecordPresent = loggedInUserDraftCollection(userType, planEvent);

  return isDraftRecordPresent;
};

const loggedInUserDraftCollection = (userType, planEvent) => {
  const promoDraft = planEvent?.promotionsList?.some(promo => {
    return (
      promo.promotionWorkflowStatus === EEVENT_STATUS?.DRAFT &&
      promo.updateUser?.type?.toUpperCase() === userType
    );
  });
  const allowanceDraft = planEvent?.offerAllowances?.some(offer => {
    return (
      offer.allowances?.[0]?.allowanceStatus === EEVENT_STATUS?.DRAFT &&
      offer.updateUser?.type?.toUpperCase() === userType
    );
  });
  return promoDraft || allowanceDraft;
};

export const getIsModifyOrCancelEvent = eventDetails => {
  const { planEvent } = eventDetails;
  return planEvent.planEventWorkFlowType;
};

export const latestUpdatedModule = eventDetails => {
  const updatedUserArr: any = [];
  const allowanceUpdatedUser = eventDetails?.offerAllowances?.length
    ? getLatestUpdatedUser(eventDetails?.offerAllowances)
    : {};
  if (eventDetails?.eventType.includes("DP")) {
    const promoUpdatedUser = eventDetails?.promotionsList?.length
      ? getLatestUpdatedUser(eventDetails?.promotionsList)
      : {};
    updatedUserArr.push(promoUpdatedUser?.updateUser);
  }
  updatedUserArr?.push(
    eventDetails?.updateUser,
    allowanceUpdatedUser?.updateUser
  );

  const hasAtLeastOneObject = updatedUserArr.some(
    element => typeof element === "object" && element !== null
  );

  const latestUpdatedUser = hasAtLeastOneObject
    ? getLatestUpdatedUser(updatedUserArr)
    : {};

  return latestUpdatedUser;
};

const getLatestUpdatedUser = data => {
  const latestUpdatedElement = data
    ?.filter(ele => ele)
    ?.reduce((latestUpdatedEle, ele) => {
      return moment(latestUpdatedEle?.createTs).unix() >
        moment(ele?.createTs).unix()
        ? latestUpdatedEle
        : ele;
    });
  return latestUpdatedElement;
};

export const isEventReturn = (
  eventIndicators,
  history,
  userType,
  key,
  eventStatus
) => {
  const {
    createUser: { type = "" },
    eventChanges,
  } = history?.[history?.length - 1] || {
    createUser: { type: "" },
    eventChanges: [],
  };
  const returnChangeObj = eventChanges?.find(eventChange => {
    return eventChange?.labelFieldName === key;
  });
  const isAnyIndicatorTrue = eventIndicators?.some(ind => ind);
  const isCurrentStatusUser = eventStatus
    ?.toUpperCase()
    ?.includes(userType?.toUpperCase());
  if (
    !isAnyIndicatorTrue &&
    key === EVENT_ACTION_LABELS.RETURN &&
    isCurrentStatusUser
  ) {
    return returnChangeObj ? !!Object.keys(returnChangeObj)?.length : false;
  }

  const isCreatedUser = userType?.toUpperCase() === type?.toUpperCase();
  if (toString.call(returnChangeObj) === "[object Object]") {
    const actions = {
      [EVENT_ACTION_LABELS.STATUS_UPDATE_WITH_COMMENT]: isCreatedUser,
      [EVENT_ACTION_LABELS.RETURN]: !isCreatedUser,
    };
    return actions[key];
  }
  return false;
};

export const isEventStatusAgreedPrev = (eventHistory, key) => {
  const lastHistoryObj = eventHistory?.length
    ? eventHistory[eventHistory?.length - 1]
    : {};
  if (Object.keys(lastHistoryObj).length) {
    const { eventChanges } = lastHistoryObj;
    const eventStatusChangeObj = eventChanges?.find(eventChange => {
      return eventChange?.labelFieldName === key;
    });
    return {
      isAgreed: eventStatusChangeObj
        ? [
          EEVENT_STATUS.AGREED,
          EEVENT_STATUS.ACTIVE,
          EEVENT_STATUS.EXECUTED,
        ].includes(eventStatusChangeObj?.beforeVal)
        : false,
      userType: eventStatusChangeObj
        ? eventStatusChangeObj?.createUser?.type
        : "NA",
    };
  } else
    return {
      isAgreed: false,
      userType: "NA",
    };
};

export const getIsPromoOnlyEditByMerchant = eventDetails => {
  const {
    allowanceEventInd = false,
    promotionEventInd = false,
    eventDetailsEventInd = false,
    otherDetailsChangedInd = false,
    planEvent,
  } = eventDetails;
  const pendingModule = getPendingModule({
    EVENT: eventDetailsEventInd && otherDetailsChangedInd,
    ALLOWANCE: allowanceEventInd,
    PROMOTION: promotionEventInd,
  });
  const planDraftAllowances = getAllowanceDraftCollections(
    planEvent?.offerAllowances,
    "allowances",
    "allowanceStatus"
  );
  const validPlanOffer = planEvent?.offerAllowances?.filter(
    offer =>
      ![EEVENT_STATUS?.REJECTED].includes(offer?.allowances[0]?.allowanceStatus)
  );
  if (pendingModule?.length) {
    if (
      pendingModule.includes(MODULES.PROMOTION) &&
      !pendingModule.includes(MODULES.ALLOWANCE) &&
      !planDraftAllowances?.length
      // validPlanOffer?.length
    )
      return false;
    else return true;
  } else if (planDraftAllowances?.length) return true;
  else return false;
};

export const isValidItemPresent = (items, statusKey, overrideStatusKey) => {
  const validItems = items?.filter(item => {
    const itemStatusKeyList = statusKey
      .split(".")
      .reduce((o, i) => o?.[i], item);
    const overRideKeyList = overrideStatusKey
      .split(".")
      .reduce((o, i) => o?.[i], item);
    return (
      !efConstants?.EXCLUDED_OFFER_PROMO_STATUS?.includes(itemStatusKeyList) &&
      !(
        overRideKeyList === EEVENT_STATUS?.CANCELED &&
        itemStatusKeyList === EEVENT_STATUS?.AGREED_PENDING
      )
    );
  });
  return validItems?.length > 0;
};

export const isValidItemsPresentForNationals = eventDetailsData => {
  const { isNDPAndFlagEnabled: isNational } = getIsNationalEvent(
    eventDetailsData?.eventType
  );
  const validItems = eventDetailsData?.offerAllowances?.filter(offer => {
    const { allowances = [], overrideAllowanceStatus } = offer || {};
    const statusToCheck = isNational
      ? offer?.allowances?.find(
          allow =>
            !efConstants?.EXCLUDED_OFFER_PROMO_STATUS.includes(
              allow?.allowanceStatus
            )
        )
        ? true
        : false
      : !efConstants?.EXCLUDED_OFFER_PROMO_STATUS.includes(
          allowances?.[0]?.allowanceStatus
        );
    const statusCheckForAgreedPending = isNational
      ? getNationalOfferStatus(allowances)
      : allowances?.[0]?.allowanceStatus;
    return (
      statusToCheck &&
      !(
        overrideAllowanceStatus === EEVENT_STATUS.CANCELED &&
        statusCheckForAgreedPending === EEVENT_STATUS.AGREED_PENDING
      )
    );
  });
  return validItems?.length > 0;
};

export const isEventSendBackWtComment = (eventHistory, key) => {
  const lastHistoryObj = eventHistory?.length
    ? eventHistory[eventHistory?.length - 1]
    : {};
  if (Object.keys(lastHistoryObj).length) {
    const { eventChanges } = lastHistoryObj;
    const eventStatusChangeObj = eventChanges?.find(eventChange => {
      return eventChange?.labelFieldName === key;
    });
    return !!eventStatusChangeObj?.beforeVal;
  } else return false;
};

export const filterHistory = (eventHistory, key) => {
  let sendBackWithCommentIndx = 0;

  for (let index = eventHistory.length - 1; index > 0; index--) {
    const eventStatusChangeObj = eventHistory?.[index]?.eventChanges?.find(
      eventChange => {
        return eventChange?.labelFieldName === key;
      }
    );
    if (eventStatusChangeObj && Object.keys(eventStatusChangeObj).length) {
      sendBackWithCommentIndx = index;
      break;
    }
  }
  if (!sendBackWithCommentIndx) return false;
  const trimmedHistory = eventHistory.slice(sendBackWithCommentIndx + 1);
  const isVendorUpdated = trimmedHistory?.length
    ? trimmedHistory?.every(
      item => item?.createUser?.type.toUpperCase() === EUSER_ROLES.VENDOR
    )
    : false;
  return isVendorUpdated;
};

export const isEventReturnOnAgreedPending = (
  eventIndicators,
  eventHistory,
  userRole,
  key,
  eventStatus
) => {
  const {
    createUser: { type = "" },
    eventChanges,
  } = eventHistory?.[eventHistory?.length - 1] || {
    createUser: { type: "" },
    eventChanges: [],
  };
  const returnChangeObj = eventChanges?.find(eventChange => {
    return eventChange?.labelFieldName === key;
  });
  const isAnyIndicatorTrue = eventIndicators?.some(ind => ind);
  if (
    isAnyIndicatorTrue &&
    returnChangeObj &&
    Object.keys(returnChangeObj).length
  ) {
    return {
      isReturnEvent: true,
      isReturnByCurrUser: userRole?.toUpperCase() === type?.toUpperCase(),
    };
  } else
    return {
      isReturnEvent: returnChangeObj
        ? !!Object.keys(returnChangeObj).length
        : false,
      isReturnByCurrUser:
        returnChangeObj && Object.keys(returnChangeObj).length
          ? userRole?.toUpperCase() === type?.toUpperCase()
          : false,
    };
};

export const isEventReturnFn = (currHistoryObj, key, userType) => {
  const {
    createUser: { type = "" },
    eventChanges,
  } = currHistoryObj || {
    createUser: { type: "" },
    eventChanges: [],
  };
  const returnChangeObj = eventChanges?.find(eventChange => {
    return eventChange?.labelFieldName === key;
  }),
    isCreatedUser = userType?.toUpperCase() === type?.toUpperCase();
  if (toString.call(returnChangeObj) === "[object Object]") {
    const actions = {
      [EVENT_ACTION_LABELS.RETURN]: {
        isReturnEvent: true,
        isReturnEventByCurrUser: isCreatedUser,
      },
    };
    return actions[key];
  }
  return {
    isReturnEvent: false,
    isReturnEventByCurrUser: false,
  };
};

export const isReturnOrLastUpdatedByCurrUser = (
  userType,
  eventDetailsWtPlanPending
) => {
  const {
    planEvent = {},
    planEventPending = {},
    planEventHistory = [],
    allowanceEventInd = false,
    eventDetailsEventInd = false,
    promotionEventInd = false,
    otherDetailsChangedInd = false,
  } = eventDetailsWtPlanPending;
  const historyDataLength = planEventHistory?.length;
  const isPendingDataEvent = [
    allowanceEventInd,
    eventDetailsEventInd && otherDetailsChangedInd,
    promotionEventInd,
  ].some(e => e);
  const isLastUpdatedByCurrUser = isPendingCollectionUser(
    isPendingDataEvent ? planEventPending : planEvent
  );

  if (historyDataLength > 1) {
    const lastHistoryObj = planEventHistory[historyDataLength - 1];

    const { isReturnEvent, isReturnEventByCurrUser } = isEventReturnFn(
      lastHistoryObj,
      EVENT_ACTION_LABELS.RETURN,
      userType
    );
    return { isReturnEvent, isReturnEventByCurrUser, isLastUpdatedByCurrUser };
  } else
    return {
      isReturnEvent: false,
      isReturnEventByCurrUser: false,
      isLastUpdatedByCurrUser: isLastUpdatedByCurrUser,
    };
};
