import { InputAutoComplete } from "@me/input-fields";
import { useGetStoreGroupsLazyQuery } from "apps/event-flow/src/app/graphql/generated/schema";
import { useCallback, useMemo, useState } from "react";
import { useFormContext } from "react-hook-form";
import { itemOrderAlphabeticalChecker } from "../../utility/utility";
import StoreGroupItemModal from "../../view-items/store-group-item-modal";
import { useSelectorWrap } from "@me/data-rtk";
import { validateNCDP } from "../../event-details-card-service";

function StoreGroupsField({
  showField,
  formFields,
  storeGroups,
  setStoreGroupFieldChanged,
}) {
  const {
    setValue,
    getValues,
    formState: { errors },
    trigger,
  } = useFormContext();
  const hasStoreGroupValues = storeGroup => storeGroup?.some(store => store);
  const [getStoreGroups] = useGetStoreGroupsLazyQuery();
  const [storeGroupFieldsData, setStoreGroupFieldsData] = useState({
    isViewStoresModalOpen: false,
  });

  const { data: eventTypeAndDivisionsData } = useSelectorWrap(
    "event_type_and_divisions"
  );
  const eventType = eventTypeAndDivisionsData?.eventType || "";

  const setViewStoresModalOpen = isOpen => {
    setStoreGroupFieldsData(prevState => {
      return {
        ...prevState,
        isViewStoresModalOpen: isOpen,
      };
    });
  };
  const onStoreGroupsChange = element => {
    const storeGrops = element?.map(item => {
      const countLabel = item?.name?.split("(");
      const name = countLabel?.[0];
      let count = countLabel?.[countLabel?.length - 1];
      count = count?.slice(0, count?.length - 1);
      return {
        name,
        count,
      };
    });
    if (storeGrops?.length > 1) {
      const selectedStoreGroup = storeGrops?.reduce((prev, curr) => {
        return itemOrderAlphabeticalChecker(prev, curr, "count");
      });
      // if the store group didn't change. don't change
      if (selectedStoreGroup?.name !== getValues("storeGroupName")) {
        setValue("storeGroupName", selectedStoreGroup?.name);
      }
    } else {
      setValue(
        "storeGroupName",
        storeGrops?.length ? storeGrops?.[0]?.name : ""
      );
    }
    setStoreGroupFieldChanged(true);
    Object.keys(errors).includes("storeGroups") && trigger(["storeGroups"]);
  };
  const createDefaultNameValue = (item, fieldProps) => {
    if (item?.storeGroupName && fieldProps.registerField === "storeGroups") {
      return `${item?.storeGroupName} (${item?.storeCount})`;
    } else {
      return item?.name || item?.[fieldProps.suggestionKey];
    }
  };
  const getDefaultValue = fieldProps => {
    const data = getValues(fieldProps?.registerField);
    const defaultValues = Array.isArray(data)
      ? data?.map(item => {
          return {
            ...item,
            id: item?.id || item?.[fieldProps.prop],
            name: createDefaultNameValue(item, fieldProps),
          };
        })
      : [];
    return defaultValues;
  };
  const getStoreGroupMemoDefaultValue = useMemo(() => {
    return getDefaultValue(storeGroups);
  }, [
    getValues("divisionIds"),
    getValues("planStoreGroupType"),
    formFields?.promoProductGroupName,
  ]);
  const getPlanStoreGroups = useCallback(() => {
    return storeGroups ? (
      <InputAutoComplete
        divisionId={formFields?.divisionId}
        groupInd={getValues("storeGroupType")}
        getAllQuery={getStoreGroups}
        searchQuery={getStoreGroups}
        fieldProps={storeGroups}
        props={{
          divisionId: formFields?.divisionId,
          groupInd: formFields?.groupInd,
        }}
        onChange={onStoreGroupsChange}
        defaultValue={getStoreGroupMemoDefaultValue}
        promoProductName={formFields?.promoProductGroupName}
        disabled={validateNCDP(eventType)}
      />
    ) : null;
  }, [
    getValues("storeGroupType"),
    getValues("divisionIds"),
    formFields?.promoProductGroupName,
  ]);
  return (
    <>
      <div className="w-1/4 abs-ef-store-groups-container">
        <div
          className={`${
            formFields?.disableStoreGroups
              ? "pointer-events-none disable-select"
              : ""
          }`}
        >
          {getPlanStoreGroups()}
        </div>
        {hasStoreGroupValues(getValues("storeGroups")) ? (
          <div className="flex justify-start items-start flex-grow-0 flex-shrink-0  gap-2.5">
            {showField ? (
              <div className="flex justify-start items-start flex-grow-0 flex-shrink-0  gap-2.5">
                <p
                  className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#1b6ebb] cursor-pointer"
                  onClick={() => setViewStoresModalOpen(true)}
                >
                  View Stores
                </p>
              </div>
            ) : null}
          </div>
        ) : null}
      </div>
      <StoreGroupItemModal
        isOpen={storeGroupFieldsData?.isViewStoresModalOpen}
        setOpen={setViewStoresModalOpen}
        storeGroups={getValues("storeGroups")}
      />
    </>
  );
}

export default StoreGroupsField;
