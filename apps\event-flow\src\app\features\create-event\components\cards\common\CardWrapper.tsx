import * as React from "react";
import "../card-height.scss";
import { useFormContext } from "react-hook-form";
import AllowanceCardContent from "../allowance/allowance-card-content";
import PromotionDetailsCardContent from "../promotion-details/promotion-details-card-content";
import { appConstants } from "@me/utils-root-props";
import OfferCard from "../offer/offer-card";

interface ICardWrapperProps {
  isNew?: any;
  cardIndex?: any;
  cardItemIndex?: any;
  cardConfiguration?: any;
  promotionIndex?: any;
  cardContainerData?: any;
  HeaderComponent?: any;
  ContentComponent?: any;
  step?: any;
  sectionIndex?: any;
  handleCancel?: any;
  isEditMode;
}

const CardWrapper: React.FunctionComponent<ICardWrapperProps> = ({
  cardConfiguration,
  cardIndex,
  cardItemIndex,
  isNew,
  handleCancel,
  isEditMode,
}) => {
  const { control, getValues } = useFormContext();

  return (
    <>
      <div
        id={`abs-preview-${cardConfiguration.key}-container-${cardIndex}-${cardItemIndex}`}
        key={`abs-preview-${cardConfiguration.key}-container-${cardIndex}-${cardItemIndex}`}
      >
        {cardConfiguration.key === "Allowance" ? (
          <>
            {appConstants.IS_ENABLE_REDESIGN_ALLOWANCE_CHANGES ? (
              <OfferCard cardIndex={cardIndex} cardItemIndex={cardItemIndex} />
            ) : (
              <AllowanceCardContent
                cardIndex={cardIndex}
                cardItemIndex={cardItemIndex}
                isNew={isNew}
                control={control}
                getValues={getValues}
              />
            )}
          </>
        ) : (
          <PromotionDetailsCardContent
            cardIndex={cardIndex}
            cardItemIndex={cardItemIndex}
            cardConfiguration={cardConfiguration}
            control={control}
            getValues={getValues}
            handleCancel={handleCancel}
            isEditMode={isEditMode}
          />
        )}
      </div>

      {/* <React.Suspense fallback={null}>
        <ContentComponent
          cardIndex={cardIndex}
          cardItemIndex={cardItemIndex}
          isNew={isNew}
          cardConfiguration={cardConfiguration}
          control={control}
          getValues={getValues}
          handleCancel={handleCancel}
          isEditMode={isEditMode}
        ></ContentComponent>
      </React.Suspense> */}
    </>
  );
};

export default CardWrapper;
