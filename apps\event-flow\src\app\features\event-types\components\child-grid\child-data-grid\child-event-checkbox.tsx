import Checkbox from "@albertsons/uds/molecule/Checkbox";
import { useSelectorWrap } from "@me/data-rtk";
import React from "react";
import { useDispatch } from "react-redux";
import { toggleSelectedChildEventDivIds } from "../slices/child-events-data-slice";
import { EEVENT_STATUS } from "@me/util-helpers";

function ChildEventCheckbox({ divisionId, eventStatus }) {
  const { data: selectedChildDivIds } = useSelectorWrap(
    "selected_child_events_data"
  );
  const dispatch = useDispatch();
  const handleCheckboxChange = (id: string | number) => {
    dispatch(toggleSelectedChildEventDivIds(id));
  };
  return (
    <Checkbox
      key={divisionId}
      checked={selectedChildDivIds?.includes(divisionId)}
      value={divisionId}
      onChange={() => handleCheckboxChange(divisionId)}
      className="px-3"
      disabled={
        [EEVENT_STATUS.CANCELED, EEVENT_STATUS.REJECTED]?.includes(
          eventStatus
        )
      }
    />
  );
}

export default ChildEventCheckbox;
