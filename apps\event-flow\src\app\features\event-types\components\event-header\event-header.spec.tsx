import React from "react";
import "@testing-library/jest-dom";
import { render, fireEvent, screen, act } from "@testing-library/react";
import { MemoryRouter } from "react-router-dom";
import { Provider } from "react-redux";
import configureMockStore from "redux-mock-store";
import EventHeader from "./event-header";
import userEvent from "@testing-library/user-event";
import { mockEventDetailsDataAllowanceDates } from "../../../create-event/components/cards/allowance/stepper/common-stepper/allowance-dates/allowance-dates-mock";
import * as rtk_tools from "@me/data-rtk";

const mockStore = configureMockStore();
const store = mockStore({});
const mockGetValues = {};
jest.mock("react-hook-form", () => ({
  ...jest.requireActual("react-hook-form"),
  useFormContext: () => ({
    getValues: reg => {
      return mockGetValues[reg] || "";
    },
    setValue: () => jest.fn(),
  }),
}));


describe("EventHeader", () =>  {
  jest
    .spyOn(rtk_tools, "useSelectorWrap")
    .mockImplementation(useSelectorName => {
      switch (useSelectorName) {
        case "event_details_data":
          return { data: mockEventDetailsDataAllowanceDates };

        default:
          return { data: "NA" };
          break;
      }
    });
  xit("should render the component with default props", async () => {
    const { getByText } = render(
      <Provider store={store}>
        <MemoryRouter>
          <EventHeader />
        </MemoryRouter>
      </Provider>
    );

    expect(getByText("BACK")).toBeInTheDocument();
    expect(getByText("New Event")).toBeInTheDocument();
    const breadcrumb = getByText("BACK");
    fireEvent.click(breadcrumb);
  });

  xit("should fire click event on breadcrumb", async () => {
    const { getByText } = render(
      <Provider store={store}>
        <MemoryRouter>
          <EventHeader />
        </MemoryRouter>
      </Provider>
    );

    expect(getByText("BACK")).toBeInTheDocument();
    const breadcrumb = getByText("BACK");
    fireEvent.click(breadcrumb);
  });

  it("removes event listeners on unmount", () => {
    // Spy on window.removeEventListener
    const removeEventListenerSpy = jest.spyOn(window, "removeEventListener");

    // Render the component
    const { unmount } = render(
      <Provider store={store}>
        <MemoryRouter>
          <EventHeader />
        </MemoryRouter>
      </Provider>
    );

    // Unmount the component
    unmount();

    expect(removeEventListenerSpy).not.toHaveBeenCalledWith(
      "onbeforeunload",
      expect.any(Function) // Expect any function to be passed
    );
  });
  it("renders event ID when eventId prop is provided", async () => {
    const { getByText } = render(
      <Provider store={store}>
        <MemoryRouter>
          <EventHeader eventId="12345" />
        </MemoryRouter>
      </Provider>
    );

    expect(getByText("Event")).toBeInTheDocument();
    // expect(getByText("ID# 12345")).toBeInTheDocument();
  });
  it("renders event status badge when eventStatus prop is provided", async () => {
    const { getByText } = render(
      <Provider store={store}>
        <MemoryRouter>
          <EventHeader eventStatus="New Event" />
        </MemoryRouter>
      </Provider>
    );

    expect(getByText("New Event")).toBeInTheDocument();
  });
  xit("calls backToDefaultTaskViewHandler when breadcrumb is clicked", async () => {
    const dispatchSpy = jest.spyOn(store, "dispatch");

    const { getByText } = render(
      <Provider store={store}>
        <MemoryRouter>
          <EventHeader />
        </MemoryRouter>
      </Provider>
    );

    const breadcrumb = getByText("BACK");
    fireEvent.click(breadcrumb);

    expect(dispatchSpy).toHaveBeenCalledTimes(13);
  });
});
