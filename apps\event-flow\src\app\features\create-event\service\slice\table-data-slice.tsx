import { createGenericSlice } from "@me/data-rtk";
import { current } from "@reduxjs/toolkit";
import {
  updateAllDivisions,
  updatedIncludeIndValInTable,
  updateDivisionWiseLeadDistributors,
  updateLeadDistributors,
  updateTableData,
  updateTableOnSwtich,
} from "../../../all-allowances/allowance-table/allowance-table-data-utils";

const initialState = {
  data: {
    tableData: [],
    filteredAndSortedIds: [],
    allDivisionsTableData: [],
    ndpConfigData: {
      selectedDivision: "",
      isNdpType: false,
    },
  },
};

export const tableDataSlice = createGenericSlice({
  name: "allowanceTableData_rn",
  initialState: {
    status: "loading",
    data: { ...initialState?.data },
  },
})({
  setTableDataAction: (state, { payload }) =>
    updateStateData(state, "tableData", payload),
  setNdpConfigData: (state, { payload }) =>
    updateStateData(state, "ndpConfigData", payload),
  setAllDivisionTableData: (state, { payload }) =>
    updateStateData(state, "allDivisionsTableData", payload),
  setFilteredAndSortedIdsAction: (state, { payload }) =>
    updateStateData(state, "filteredAndSortedIds", payload),

  updateIndividualItemAction: (state, { payload }) => {
    updateTableData(state, payload);
  },

  updateItemByItemAction: (state, { payload }) => {
    updateTableData(state, payload);
  },

  updateCaseAmountOnSwitch: (state, { payload }) => {
    updateTableOnSwtich(state, payload);
  },

  updateItemByDistributorAction: (state, { payload }) => {
    updateTableData(state, payload);
  },

  updateItemIfEmptyByDistributorAction: (state, { payload }) => {
    updateTableData(state, {
      ...payload,
      onlyUpdateEmptyAmount: true,
    });
  },

  updateAllItemsAction: (state, { payload }) => {
    updateTableData(state, payload);
  },

  updateAllDivisionsItemsAction: (state, { payload }) => {
    checkIfNdp(state?.data) &&
      updateAllDivisions(state, payload);
  },

  updateLeadDistributorsInfoAction: (state, { payload }) => {
    updateLeadDistributors(state, payload);
  },

  updateDivisionWiseLeadDistributorsInfoAction: (state, { payload }) => {
    checkIfNdp(state?.data) &&
      updateDivisionWiseLeadDistributors(state, payload);
  },

  updateIncludeIndicatorOnExclude: (state, { payload }) => {
    updateIncludeIndicator(state, payload);
  },
  resetTableDataSliceData: state => {
    state.data = { ...initialState.data };
  },
});

// Utility Functions
const checkIfNdp = state => state?.ndpConfigData?.isNdpType === true;
const updateStateData = (state, key, value) => {
  if (state?.data) state.data[key] = value;
};

const updateIncludeIndicator = (state, { excludedVendors, excludedByKey }) => {
  if (!state?.data?.tableData) return;
  const { tableData, allDivisionsTableData } = updatedIncludeIndValInTable({
    currentState: state?.data,
    excludedVendors,
    excludedByKey,
  });
  state.data.tableData = tableData;
  state.data.allDivisionsTableData = allDivisionsTableData;
};

// Additional Slice
export const isUpdateAllClickedSlice = createGenericSlice({
  name: "isUpdateAllClicked_rn",
  initialState: {
    status: "loading",
    data: { isUpdateAll: false },
  },
})({
  setIsUpdateAllChange: (state, { payload }) => {
    state.data = { ...state.data, ...payload };
    state.status = "finished";
  },
});

export const { setIsUpdateAllChange } = isUpdateAllClickedSlice.actions;
export const {
  setTableDataAction,
  updateIndividualItemAction,
  updateItemByItemAction,
  updateItemByDistributorAction,
  updateItemIfEmptyByDistributorAction,
  updateAllItemsAction,
  updateLeadDistributorsInfoAction,
  updateAllDivisionsItemsAction,
  setFilteredAndSortedIdsAction,
  updateCaseAmountOnSwitch,
  updateIncludeIndicatorOnExclude,
  setAllDivisionTableData,
  setNdpConfigData,
  updateDivisionWiseLeadDistributorsInfoAction,
  resetTableDataSliceData,
} = tableDataSlice.actions;
