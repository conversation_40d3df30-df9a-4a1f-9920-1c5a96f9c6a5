import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import React, { memo } from "react";
import {
  Multi_Vendor_AO_Alert_labels,
  Multi_Vendor_DP_Alert_labels,
} from "../../../../../constants/event-status/contsants";
import { eventTypes } from "apps/event-flow/src/app/features/event-types/constants/event-type-constant";
import Alert from "@albertsons/uds/molecule/Alert";
import { EVENT_TYPE } from "../../../../../constants/constants";

function MultivendorPPGInfoAlert({ formFields, eventType }) {
  const [DP, AO, NDP] = eventTypes;
  const multivendorPpgInfo = (
    <>
      <div
        className={`font-bold	text-base	text-[#1B6EBB] ${efConstants.componentClassName.MULTI_VENDOR_PPG_INFO}`}
      >
        {[DP, NDP, EVENT_TYPE.NCDP].includes(eventType)
          ? Multi_Vendor_DP_Alert_labels.TITLE
          : Multi_Vendor_AO_Alert_labels.TITLE}
      </div>
      <div className="text-[#1B6EBB] font-normal">
        {[DP, NDP, EVENT_TYPE.NCDP].includes(eventType)
          ? Multi_Vendor_DP_Alert_labels.MESSAGE
          : Multi_Vendor_AO_Alert_labels.MESSAGE}
      </div>
    </>
  );
  return (
    <Alert
      isOpen={formFields?.isMutliVendorPPG && formFields?.canCreateEvent}
      variant="informational"
      className="!justify-center w-full"
    >
      {multivendorPpgInfo}
    </Alert>
  );
}

export default memo(MultivendorPPGInfoAlert);
