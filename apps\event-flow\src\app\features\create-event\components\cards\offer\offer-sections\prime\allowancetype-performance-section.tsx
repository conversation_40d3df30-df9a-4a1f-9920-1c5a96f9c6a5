/*
 * Imports
 * move interfaces to seperate file
 * store functions - (selectors)
 * API calls declaration
 * constants
 * useState variables - try to combine and reduce number of declarations
 * useEffect
 * functions
 * Return UI field elements - create multiple functions to render fields
 */

import { useSelectorWrap } from "@me/data-rtk";
import { useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";
import LoadingSpinner from "../../../../../constants/LoadingSpinner/LoadingSpinner";
import {
  checkIsPerfChangable,
  formatAmount,
  formatTimestampToDate,
  getAllowanceKey,
  getAllowancePerfOption,
  getUniqueItems,
  isHfOrIfType,
  setProductSourceError,
} from "../../../../../service/allowance/allowance-service";
import { IOption } from "../../../../fields/allowance-atoms/input-select";
import {
  InputSelectAtom,
  InputTextAtom as InputText,
} from "../../../../fields/index";
import {
  IAllowanceTypePerformanceOption,
  IPerformanceProps,
} from "../../../allowance/stepper/allowance-type-performance/allowance-type-performance.model";
import {
  allowanceFormReset,
  allowancePerfConfigHandler,
  allowanceProductSources,
  resetIsOfferSectionUpdated,
  resetOfferAmountsData,
  setVendorsForAllowances,
} from "../../../../../service/slice/allowance-details-slice";
import { usePostAllowanceToBeCreatedMutation } from "../../../../../service/apis/allowance-api";
import { ALLOWANCE_TO_BE_CREATED_CD_MAPPER } from "../../../../../constants/fields/allowance/allowance-steps-config";
import { validateAmount } from "../../../../../hooks/allowance-amount-validations";
import { OFFER_FORM_FIELDS } from "../../offer-flow-config";
import {
  checkForDeletedDivisions,
  checkIsNationalEvent,
  getConvertedCreateInd,
  handleZindexForCard,
  isAllowanceToBeCreatedEditable,
  resetOfferDivisions,
  resetOfferSectionData,
} from "../../offer-service";
import { useFormContext } from "react-hook-form";
import useGetOfferSectionConfiguration from "../../hooks/useGetOfferSectionConfiguration";
import NationalHeaderFlatAmounts from "../../national/amounts/national-header-flat-amounts";

const AllowanceTypePerformace = ({
  formControls,
  cardIndex,
  cardItemIndex,
  sectionConfiguration,
  isEditEnable,
  allowanceRegField,
}) => {
  const dispatch = useDispatch();
  const { data: allowanceForm } = useSelectorWrap("allowance_form_data");
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { data: allowancePerfData } = useSelectorWrap(
    "allowance_type_performance_data"
  );
  const { data: allowanceTypeWithPerfData } = useSelectorWrap(
    "allowance_type_data"
  );
  const formContext = useFormContext();
  const { productSources = [] } =
    useSelectorWrap("allowance_product_sources_rn")?.data || {};
  const {
    data: { apiErrorsMsgs: rtkErrorsArr, showTokenError = false },
  } = useSelectorWrap("apiError_rn");
  const {
    offerDivisions = [],
    deletedDivisions = [],
    offerDivisonErrorData = [],
  } = useSelectorWrap("national_offer_divisions")?.data || {};
  const { allowanceData: tempworkData } =
    useSelectorWrap("allowance_temp_work")?.data || {};

  const {
      ALLOWANCE_TYPES,
      PRODUCT_SOURCE_INFO,
      ALLOWANCE_SCREEN_TYPES,
      BOTH_KEY,
    } = efConstants,
    { CASE, HEADERFLAT, ITEMFLAT, SCAN } = ALLOWANCE_TYPES,
    { AO, NDP } = ALLOWANCE_SCREEN_TYPES,
    { HEADER_FLAT, ITEM_FLAT, CASE: CASE_SOURCE } = PRODUCT_SOURCE_INFO,
    {
      id,
      eventType,
      eventCreationVehicle,
      planProductGroups,
      offerAllowances = [],
      divisionIds = [],
    } = eventDetailsData || {};

  const [
    postEventDetailsData,
    {
      isLoading: isProductSourceDataloading,
      data: productSourcesData,
      isError: isProductSourceError,
    },
  ] = usePostAllowanceToBeCreatedMutation();
  const { clearNextSectionsOnOptionChange } = useGetOfferSectionConfiguration({
    allowanceRegField,
  });

  const {
    allowanceType: allowanceTypeField,
    performance: performanceField,
    allowanceAmount: allowanceAmountField,
  } = sectionConfiguration?.fields?.ALLOWANCE_TYPE_PERFORMANCE || {};
  const sectionKey = sectionConfiguration?.key;
  const {
    AllowanceToBeCreatedRegField,
    perfConfigKey,
    createIndKey,
    allowanceToBeCreatedOptionKey,
    allowanceTypeChangeKey,
    performanceChangeKey,
    hfAmountChangeKey,
  } = OFFER_FORM_FIELDS;
  const allowanceFormData =
    allowanceForm?.allowanceFormData?.[allowanceRegField]?.allowancePrimeData;

  const {
    allowanceType: allowanceTypeDefaultValue,
    performance: performanceDefaultValue,
    overrideHeaderFlatAmt: headerFlatAmtDefaultValue,
    createInd,
    perfConfig,
  } = allowanceFormData || {};

  const defaultValues = {
    allowanceType: allowanceTypeDefaultValue,
    performance: performanceDefaultValue,
    overrideHeaderFlatAmt: formatAmount(headerFlatAmtDefaultValue),
    perfConfig,
    createInd,
  };

  const isPerfChangable = checkIsPerfChangable(createInd);
  const {
    register = () => null,
    control,
    getValues = () => null,
    setValue = () => null,
    reset = (values?: object) => null,
    clearErrors = () => null,
    trigger = () => null,
  } = formControls || {};

  const { allowanceType } = getValues() || {};

  const allowanceTypeName = useRef("");
  const count = useRef(0);
  const [defaultPerfConfig, setDefaultPerfConfig] = useState({
    defaultCreateInd: "",
    allowancePerformanceChildId: "",
    allowOnlyOverrideStoreGroupsInd: false,
  });
  const [allowanceTypeOptions, setAllowanceTypeOptions] = useState<
    IAllowanceTypePerformanceOption[]
  >([]);
  const [performanceOptions, setPerformanceOptions] = useState<
    { id?: string; performance?: string; item?: object }[]
  >([]);
  const [perfOptionsByEventType, setPerfOptionsByEventType] = useState([]);
  const [digitError, setDigitError] = useState("");

  const createIndByLocation = getConvertedCreateInd(
    defaultValues?.createInd,
    isEditEnable,
    productSources,
    offerAllowances?.[cardIndex] || {}
  );

  const allowCdMapper =
    ALLOWANCE_TO_BE_CREATED_CD_MAPPER?.[createIndByLocation];

  const isNationalEvent = checkIsNationalEvent(eventDetailsData?.eventType);

  useEffect(() => {
    allowanceFormData && setFormInitialData();
  }, [
    allowanceFormData?.allowanceType,
    allowanceFormData?.performance,
    allowanceFormData?.createInd,
  ]);

  useEffect(() => {
    const perfDefaultConfig =
      allowancePerfData?.performanceConfig?.[allowanceRegField];
    perfDefaultConfig && setDefaultPerfConfig(perfDefaultConfig);
  }, [allowancePerfData?.performanceConfig]);

  useEffect(() => {
    allowanceTypeWithPerfData && getAllowanceTypeOptions();
  }, [JSON.stringify(allowanceTypeWithPerfData)]);

  useEffect(() => {
    setPerfOptionsByEventType(
      allowancePerfData?.getAllowancePerformanceTypes?.filter(
        ({ eventTypes, deleteInd }) =>
          eventTypes?.includes(eventType) && deleteInd !== "Y"
      ) || []
    );
  }, [allowancePerfData]);

  useEffect(() => {
    if (
      allowanceTypeWithPerfData &&
      allowanceTypeName.current &&
      perfOptionsByEventType?.length &&
      !isProductSourceDataloading &&
      !count.current
    ) {
      count.current = 1;
      if (productSources?.length) {
        setPerfOptionsByAllowanceType(productSources, performanceDefaultValue);
      } else {
        setPerformanceOptions(getPerfOptionsByAllowanceType());
        setValue(performanceField.registerField, performanceDefaultValue);
      }
    }
  }, [
    allowanceTypeWithPerfData,
    perfOptionsByEventType,
    allowanceTypeName.current,
  ]);

  const getHfAmountForNationals = () => {
    return tempworkData?.length
      ? tempworkData?.map(
          data =>
            data?.allowanceTypeSpecification?.headerFlatAllow
              ?.overrideHeaderFlatAmt
        )
      : [];
  };

  const setFormInitialData = () => {
    allowanceTypeName.current = allowanceTypeDefaultValue;
    setValue(allowanceTypeField.registerField, defaultValues?.allowanceType);
    setValue(performanceField.registerField, defaultValues?.performance);
    setValue(
      allowanceAmountField.registerField,
      isNationalEvent
        ? getHfAmountForNationals()
        : defaultValues?.overrideHeaderFlatAmt
    );
    setValue(createIndKey, defaultValues?.createInd);
    setValue(perfConfigKey, defaultValues?.perfConfig);
  };

  const onSectionUpdate = () => {
    if (!isEditEnable) {
      if (
        isAllowanceToBeCreatedEditable(
          getAllowanceKey(allowanceTypeName.current?.toUpperCase()),
          productSources
        )
      ) {
        setValue(allowanceToBeCreatedOptionKey, null);
        setValue(AllowanceToBeCreatedRegField, "");
        setValue(createIndKey, "");
      }
      clearNextSectionsOnOptionChange(sectionKey);
      dispatch(allowanceFormReset());
      dispatch(resetOfferAmountsData());
    }
  };

  const saveProductSourcesDataToSlice = (value: string[] | [] = []) => {
    dispatch(allowanceProductSources({ productSources: value }));
  };

  useEffect(() => {
    if (isProductSourceError) {
      saveProductSourcesDataToSlice();
      setPerformanceOptions([]);
    }
  }, [isProductSourceError]);

  useEffect(() => {
    if (
      productSourcesData &&
      productSourcesData?.[0]?.productSources?.length === 0
    ) {
      setProductSourceError(dispatch, rtkErrorsArr, showTokenError);
    } else if (productSourcesData?.[0]?.productSources?.length) {
      const uniqueps = getUniqueItems(productSourcesData, "productSources");
      saveProductSourcesDataToSlice(uniqueps);
      setPerfOptionsByAllowanceType(uniqueps);
    }
  }, [productSourcesData]);

  const getAllowanceTypeOptions = () => {
    const allowanceTypeFilteredData =
      allowanceTypeWithPerfData?.getAllowancePerformancesByEventType?.map(
        ele => {
          const allowanceType = ele?.allowanceType || "";
          return {
            id: allowanceType,
            name: allowanceType,
            allowanceCd: ele?.allowancePerformances?.[0]?.allowanceCd || "",
          };
        }
      ) || [];
    const filteredAllowTypeByDisplayValue = filterAllowTyeBasedOnDisplayType(
      allowanceTypeFilteredData
    );
    setAllowanceTypeOptions(filteredAllowTypeByDisplayValue);
  };

  const filterAllowTyeBasedOnDisplayType = allowTypeData => {
    if (planProductGroups?.length && allowTypeData) {
      const isAnyDisplayType = planProductGroups?.some(
        promoObj => promoObj?.displayInd
      );
      return isAnyDisplayType
        ? filterScanFromAllowType(allowTypeData)
        : allowTypeData;
    }
    return allowTypeData;
  };

  const filterScanFromAllowType = allowTypeData => {
    return allowTypeData?.filter(
      allowTypeObj => allowTypeObj?.id !== SCAN.label
    );
  };

  const getDisabledPerfOptions = (perfOptions: object[] | [] = []) => {
    return perfOptions?.map(perfOption => {
      return {
        ...perfOption,
        disabled: !(
          perfOption?.["performanceConfig"]?.defaultCreateInd === createInd
        ),
      };
    });
  };

  const getSortedPerfOptions = (perfOptions: object[] | [] = []) => {
    let updatedPerfOptions =
      isEditEnable &&
      isPerfChangable &&
      isHfOrIfType(getAllowanceKey(defaultValues?.allowanceType?.toUpperCase()))
        ? getDisabledPerfOptions(perfOptions)
        : perfOptions;

    const prooductInfo =
      productSourcesData?.[0]?.productSources || productSources;
    const isCaseLabel = CASE.label === allowanceTypeName.current;
    const isSingleProductDSD =
      prooductInfo?.length === 1 && prooductInfo[0] === CASE_SOURCE.DSD.key;

    const modifiedCreateInd = isCaseLabel
      ? isEditEnable && createInd === CASE.createInd[0]
        ? CASE.createInd[2]
        : isSingleProductDSD || createIndByLocation === CASE.createInd[1]
        ? CASE.createInd[1]
        : CASE.createInd[2]
      : createInd;

    updatedPerfOptions =
      CASE.label === allowanceTypeName.current &&
      (isEditEnable || prooductInfo?.length === 1)
        ? updatedPerfOptions?.filter(
            option =>
              option?.performanceConfig?.defaultCreateInd === modifiedCreateInd
          )
        : updatedPerfOptions;
    return updatedPerfOptions?.sort((a: object, b: object) =>
      a?.["performance"]?.localeCompare(b?.["performance"])
    );
  };

  const getPerfOptionsByAllowanceType = () => {
    const perfOptions =
      perfOptionsByEventType?.filter(
        ({ allowanceType = "" }) => allowanceType === allowanceTypeName.current
      ) || [];
    return getSortedPerfOptions(perfOptions);
  };

  const getCreateInd = (productKey: string) => {
    return PRODUCT_SOURCE_INFO?.[
      getAllowanceKey(allowanceTypeName.current?.toUpperCase())
    ]?.[productKey]?.createIndex;
  };

  const getProductSourceData = async () => {
    setPerformanceOptions([]);
    const response = await postEventDetailsData({
      URL_PARAM: id,
      queryParams: {
        responseFilter: "allowMin",
      },
      division: offerDivisions?.length ? offerDivisions : divisionIds,
      promoStartDate: formatTimestampToDate(eventCreationVehicle?.startDate),
    });
    if (response?.error) {
      setValue(performanceField.registerField, "");
    }
  };

  const setPerfOptionsByAllowanceType = async (
    productSourcesInfo?: string[] | [],
    perfOptionValue?: string
  ) => {
    if (
      [HEADERFLAT.label, ITEMFLAT.label].includes(allowanceTypeName.current) &&
      [AO.key, NDP.key].includes(eventType) &&
      productSourcesInfo?.length === 1 &&
      productSourcesInfo[0] === ITEM_FLAT.DSD.key
    ) {
      const productKey = productSourcesInfo[0];
      const options = perfOptionsByEventType?.filter(
        option =>
          option?.["performanceConfig"]?.["defaultCreateInd"] ===
          getCreateInd(productKey)
      );
      setPerformanceOptions(getSortedPerfOptions(options));
      !perfOptionValue && setDefaultPerfOption({}, productKey);
    } else {
      let productKey = "";
      if (
        CASE.label === allowanceTypeName.current &&
        productSourcesInfo?.length
      ) {
        productKey = isEditEnable
          ? allowCdMapper?.offerKey
          : productSourcesInfo?.length === 1
          ? productSourcesInfo[0]
          : BOTH_KEY;
      }
      setPerformanceOptions(getPerfOptionsByAllowanceType());
      !perfOptionValue && setDefaultPerfOption({}, productKey);
    }
  };

  const getPerformanceOptionByName = (perfOptions, perfOptionName: string) => {
    const selectedPerfOption =
      perfOptions?.filter(
        option => option?.["performance"] === perfOptionName
      ) || [];

    if (selectedPerfOption.length) {
      const allowancePerformances = selectedPerfOption[0];
      return {
        performanceObj: {
          allowanceCd: allowancePerformances?.["allowanceCd"] || "",
          perfCode1: allowancePerformances?.["perfCode1"] || "",
          perfCode2: allowancePerformances?.["perfCode2"] || "",
          payType: allowancePerformances?.["payType"] || "",
          id: allowancePerformances?.["id"] || "",
        },
        perfName: allowancePerformances?.["performance"],
        perfConfig: allowancePerformances?.["performanceConfig"],
      };
    }
    return { performanceObj: null, perfName: "", perfConfig: {} };
  };

  const setDefaultPerfOption = (
    { id = "", allowanceCd = "" },
    productKey: string
  ) => {
    let defaultPerfObj: IPerformanceProps = {
      allowanceCd: allowanceCd,
      perfCode1: "",
      perfCode2: "",
      payType: id,
      id: "",
    };
    let perfLabel = "";
    const perfOptions = getPerfOptionsByAllowanceType();
    const { performanceObj, perfName, perfConfig } = getPerformanceOptionByName(
      perfOptions,
      getAllowancePerfOption(
        allowanceTypeName.current.toUpperCase(),
        eventType,
        getCreateInd(productKey)
      )
    );
    defaultPerfObj = performanceObj || defaultPerfObj;
    perfLabel = perfName || "";
    setDefaultPerfConfig(perfConfig);
    savePerfConfig(perfConfig);
    setValue(performanceField.registerField, perfLabel);
    setValue(perfConfigKey, defaultPerfObj);
    perfLabel && clearErrors([performanceField.registerField]);
  };

  const savePerfConfig = (value: object) => {
    !isEditEnable &&
      setValue(
        createIndKey,
        getConvertedCreateInd(
          value?.["defaultCreateInd"],
          isEditEnable,
          productSources,
          offerAllowances?.[cardIndex] || {}
        )
      );
    dispatch(
      allowancePerfConfigHandler({
        performanceConfig: { [allowanceRegField]: value },
      })
    );
  };

  const handlePerfOptions = () => {
    count.current = 1;
    if (
      [HEADERFLAT.label, ITEMFLAT.label, CASE.label].includes(
        allowanceTypeName.current
      )
    ) {
      getProductSourceData();
    } else {
      setPerformanceOptions(getPerfOptionsByAllowanceType());
      setDefaultPerfOption({}, "");
    }
  };

  const handleAllowanceTypeChange = (option: IOption) => {
    resetOfferSectionData(dispatch);
    dispatch(resetOfferAmountsData());
    dispatch(resetIsOfferSectionUpdated());
    reset?.();
    dispatch(setVendorsForAllowances({}));
    const allowanceValue = (option?.["name"] as string) || "";
    formContext?.setValue("isAllowanceChanged", true);
    setValue(allowanceTypeChangeKey, true);
    setValue(allowanceTypeField.registerField, allowanceValue);
    allowanceTypeName.current = allowanceValue;
    eventType === NDP.key &&
      resetOfferDivisions(divisionIds, allowanceValue, dispatch);
    handlePerfOptions();
  };

  const handlePerformanceChange = (option: IOption) => {
    onSectionUpdate();
    setValue(performanceChangeKey, true);
    if (option) {
      const {
        allowanceCd,
        perfCode1,
        perfCode2,
        payType,
        performance,
        performanceConfig,
      } = option as {
        allowanceCd: "";
        perfCode1: "";
        perfCode2: "";
        payType: "";
        performance: "";
        performanceConfig: {
          defaultCreateInd: "";
          allowOnlyOverrideStoreGroupsInd: false;
          allowancePerformanceChildId: "";
        };
      };
      const performanceObj = {
        allowanceCd: allowanceCd,
        perfCode1: perfCode1,
        perfCode2: perfCode2,
        payType: payType,
        id: option?.["id"] as string,
      };
      setValue(performanceField.registerField, performance);
      setValue(perfConfigKey, performanceObj);
      setDefaultPerfConfig(performanceConfig);
      savePerfConfig(performanceConfig);
    } else {
      const defPerfConfig = {
        defaultCreateInd: "",
        allowancePerformanceChildId: "",
        allowOnlyOverrideStoreGroupsInd: false,
      };
      setValue(performanceField.registerField, null);
      setValue(perfConfigKey, {
        allowanceCd: "",
        perfCode1: "",
        perfCode2: "",
        payType: "",
        id: "",
      });
      setDefaultPerfConfig(defPerfConfig);
      savePerfConfig(defPerfConfig);
    }
  };

  const checkAmountFieldCondition = () => {
    return (
      allowanceType &&
      allowanceTypeName.current === HEADERFLAT.label &&
      defaultPerfConfig?.defaultCreateInd === HEADER_FLAT.DSD.createIndex
    );
  };

  function onAmountChange(e) {
    setValue(hfAmountChangeKey, true);
    onSectionUpdate();
    const amount = validateAmount(e);
    amount && Number(amount) === 0
      ? setDigitError(allowanceAmountField?.errors?.digitError?.text)
      : setDigitError("");
    !isNationalEvent && setValue(allowanceAmountField.registerField, amount);
    trigger?.();
  }

  const allowanceTypeFieldContent = allowanceTypeField && (
    <div
      className="w-3/12"
      id="abs-allowance-type-performance-input-allowance-type-field"
    >
      <InputSelectAtom
        fieldProps={allowanceTypeField}
        register={register}
        control={control}
        options={allowanceTypeOptions}
        displayLabel={allowanceTypeField.displayLabel}
        disabled={!allowanceTypeOptions.length || isEditEnable}
        onChange={handleAllowanceTypeChange}
        onClick={handleZindexForCard}
      />
    </div>
  );

  const performanceFieldContent = performanceField && (
    <div
      className="w-4/12"
      id="abs-allowance-type-performance-input-performance-field"
    >
      <InputSelectAtom
        fieldProps={performanceField}
        register={register}
        control={control}
        options={performanceOptions}
        displayLabel={performanceField.displayLabel}
        disabled={
          !performanceOptions.length || (isEditEnable && !isPerfChangable)
        }
        onChange={handlePerformanceChange}
        onClick={handleZindexForCard}
      />
    </div>
  );
  const allowanceAmountFieldContent =
    allowanceAmountField && isNationalEvent ? (
      <NationalHeaderFlatAmounts
        register={register}
        control={control}
        setValue={setValue}
        trigger={trigger}
        allowanceAmountField={allowanceAmountField}
        onChange={onAmountChange}
        divisionIds={offerDivisions}
        hfAmountChangeKey={hfAmountChangeKey}
        onSectionUpdate={onSectionUpdate}
      />
    ) : (
      <div
        className="w-3/12"
        id="abs-allowance-type-performance-allowance-amount-field"
      >
        <InputText
          fieldProps={allowanceAmountField}
          register={register}
          control={control}
          onWheel={event => event.currentTarget.blur()}
          onChange={onAmountChange}
          onFocus={(value: string) => {
            setValue(allowanceAmountField.registerField, value);
          }}
          prefixValue={"$"}
          isCustomValidationEnabled={true}
          customValidation={{
            value: !!digitError,
            message: digitError,
          }}
        />
      </div>
    );

  const footerSubContent = isEditEnable && (
    <p
      className="text-sm text-left text-[#9D2210] my-[14px] italic"
      id="abs-allowance-type-performance-Allowance-type-text"
    >
      {isPerfChangable
        ? "Allowance type cannot be changed."
        : "Chosen Performance is not changeable.  Cancel this Offer and submit a new one if you need a different Performance Code for this Offer"}
    </p>
  );

  const bodyContent = (
    <>
      <div
        className="flex gap-3"
        id="abs-allowance-type-performance-loading-spinner-container"
      >
        <LoadingSpinner
          classname="!h-full !w-full rounded-md"
          isLoading={isProductSourceDataloading}
        />
        {allowanceTypeFieldContent}
        {performanceFieldContent}
        {!isNationalEvent &&
          checkAmountFieldCondition() &&
          allowanceAmountFieldContent}
      </div>
      <div className="text-md text-[#BF2912] font-semibold mb-3 my-2">
        {id &&
          isNationalEvent &&
          checkForDeletedDivisions(
            offerDivisions,
            deletedDivisions,
            allowanceType,
            offerDivisonErrorData
          )}
      </div>
      {isNationalEvent && checkAmountFieldCondition() && (
        <>
          {footerSubContent}
          <div className="w-full">{allowanceAmountFieldContent}</div>
        </>
      )}
    </>
  );

  return (
    <>
      {control && bodyContent}
      {!isNationalEvent && footerSubContent}
    </>
  );
};

export default AllowanceTypePerformace;
