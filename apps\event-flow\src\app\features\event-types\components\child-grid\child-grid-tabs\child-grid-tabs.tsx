import React, { memo, useEffect, useState } from "react";
import { CHILD_EVENTS_TABS, getSelectedTab } from "../child-events-config";
import { useDispatch } from "react-redux";
import { setChildEventTabConfigData } from "../slices/child-events-data-slice";
import { useSelectorWrap } from "@me/data-rtk";
import TabBar, { Tab } from "@albertsons/uds/molecule/TabBar";
import { childEventConstants } from "../child-events-constants";
import ChildGridCardWrapper from "../child-card-wrapper/child-grid-card-wrapper";

function ChildGridTabsSection() {
  const dispatch = useDispatch();
  const { data: selectedTab = { tabIndex: 0 } } = useSelectorWrap(
    "child_events_tab_config_data"
  );
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const [tabNumber, setTabNumber] = useState(0);
  const { CHILD_EVENT_TABLE_TITLE } = childEventConstants;
  const onChangeTabSelection = tabIndex => {
    setTabNumber(tabIndex);
  };

  const setTabConfig = (tabIndex = 0) => {
    dispatch(setChildEventTabConfigData(getSelectedTab(tabIndex)));
  };
  const checkIsDisabled = tab => {
    return tab?.key === "offers" ? !eventDetailsData?.offerAllowances?.length : false;
  }

  useEffect(() => {
    setTabConfig(tabNumber);
  }, [tabNumber]);

  return (
    <>
      <div className="font-bold text-2xl mt-5 leading-6 text-dark-text select-none whitespace-nowrap">
        {CHILD_EVENT_TABLE_TITLE}
      </div>
      <section>
        {selectedTab?.id ? (
          <TabBar
            selected={tabNumber}
            onChange={tabIndex => onChangeTabSelection(tabIndex)}
            className="mt-5"
          >
            {CHILD_EVENTS_TABS?.map(tab => (
              <Tab disabled={tab?.isDisabled || checkIsDisabled(tab)} key={tab?.key}>
                {tab?.label}
              </Tab>
            ))}
          </TabBar>
        ) : null}
      </section>
      {<ChildGridCardWrapper />}
    </>
  );
}

export default memo(ChildGridTabsSection);
