import "@testing-library/jest-dom";
import {
  getSteppers,
  getAllowanceTypes,
  getAllowanceMapKey,
  getAllowanceMapValue,
  getAllowanceCaseTypeValue,
  updateAllowanceFormValues,
  getAllowanceFormRegisterKey,
  generateOfferAndAllowanceName,
  formatTimestampToDate,
  getDefaultAllowanceType,
  getAllowanceSteppers,
  getAllowanceCreatedOptions,
  getAllowanceTempWorkDataReqBody,
  getProductSourceKey,
  getAllowanceTypeByPerformance,
  roundOfNumber,
  checkObjectHasKeys,
  getAllowanceKey,
  getUomFieldValue,
  handleSkipStep,
  getQueryParams,
  displayPromoDetailsBasedOnType,
  getOfferFormRegisterKey,
  getPerformanceRecordById,
  getAmtOrUomFromPlanOrPending,
  getAllowanceObjForAmt,
  getAllowAmtOrUom,
  isAmtUomSameInPlanAndPending,
  getStepsByAllowanceCreateInd,
  saveAllowancesItemReqBody,
  isCrossUserEditingCard,
  removeParams,
  getisValidDates,
  getOfferKey,
  getVehicleDatesMapData,
  getProductSourceByOfferKey,
  getInvalidDataObject,
  getValuetrimmed,
  validOfferList,
  handleRequireFieldsValidation,
  getAmountLevel,
  getUOMBasedOnType,
  getCreateIndByLocation,
  checkIsWarehouseItem,
  checkIsPaymentTypeDeduct,
} from "./allowance-service";
import efConstants from "../../../../shared/ef-constants/ef-constants";
import { FieldValues, UseFormGetValues } from "react-hook-form";

const responseData = {
  offerNumber: "0",
  allowances: [
    {
      performance: {
        allwPerfId: "1",
        allowanceCd: "C",
        performance: "Price/Ad/Display(88)",
        perfCode1: "C",
        perfCode2: "C",
      },
      vehicle: {
        vehicleNm: "Weekly Insert",
        vehicleType: {
          vehicleTypDesc: "description",
        },
      },
    },
  ],
};
const performance = {
  allowanceCd: "1",
  allwPerfId: "1",
};
const mockPlanItems = [
  {
    itemId: "2020028",
    primaryUpc: "002113030602",
    caseUpc: "1002113030602",
    itemUpcs: ["002113030602", "1002113030602"],
    effectiveEndDate: "0001-01-01",
    vendorPackConversionFactor: 1,
    packWhse: 12,
    ringType: 0,
    uom: "EA",
    allowanceItemComps: [
      {
        performance: {
          allowanceCd: "T",
          perfCode1: "20",
          perfCode2: "52",
          payType: "D",
          allwPerfId: "63a3a12743a6cee87995b834",
        },
        allowanceTypeStartDate: "2023-10-11",
        allowanceTypeEndDate: "2023-10-17",
        allowAmount: 0,
      },
    ],
  },
];
const planAllowances = [
  {
    id: "64b8c7c7bb19ce1061d9ca75",
    allowanceIdNbr: 700895301,
    vendorNbr: "",
    allowanceStatus: "Pending With Vendor",
    leadDistributorInfos: [],
    allowanceStartDate: "2023-10-11",
    allowanceEndDate: "2023-10-17",
    performStartDate: "2023-10-11",
    performEndDate: "2023-10-17",
    orderStartDate: "0001-01-01",
    orderEndDate: "0001-01-01",
    shipStartDate: "0001-01-01",
    shipEndDate: "0001-01-01",
    arrivalStartDate: "0001-01-01",
    arrivalEndDate: "0001-01-01",
    cancelledTs: "0001-01-03",
    processTs: "0001-01-03",
    lastApprovedDate: "0001-01-01",
    lastApprovedUserId: "",
    allowanceItems: [
      {
        itemId: "2020024",
        primaryUpc: "002113030601",
        caseUpc: "1002113030601",
        itemUpcs: ["002113030601", "1002113030601"],
        effectiveEndDate: "0001-01-01",
        vendorPackConversionFactor: 1,
        packWhse: 12,
        ringType: 0,
        uom: "EA",
        allowanceItemComps: [
          {
            performance: {
              allowanceCd: "T",
              perfCode1: "20",
              perfCode2: "52",
              payType: "D",
              allwPerfId: "63a3a12743a6cee87995b834",
            },
            allowanceTypeStartDate: "2023-10-11",
            allowanceTypeEndDate: "2023-10-17",
            allowAmount: 0,
          },
        ],
      },
      {
        itemId: "2020028",
        primaryUpc: "002113030602",
        caseUpc: "1002113030602",
        itemUpcs: ["002113030602", "1002113030602"],
        effectiveEndDate: "0001-01-01",
        vendorPackConversionFactor: 1,
        packWhse: 12,
        ringType: 0,
        uom: "EA",
        allowanceItemComps: [
          {
            performance: {
              allowanceCd: "T",
              perfCode1: "20",
              perfCode2: "52",
              payType: "D",
              allwPerfId: "63a3a12743a6cee87995b834",
            },
            allowanceTypeStartDate: "2023-10-11",
            allowanceTypeEndDate: "2023-10-17",
            allowAmount: 0,
          },
        ],
      },
      {
        itemId: "2020342",
        primaryUpc: "002113030605",
        caseUpc: "1002113030605",
        itemUpcs: ["002113030605", "1002113030605"],
        effectiveEndDate: "0001-01-01",
        vendorPackConversionFactor: 1,
        packWhse: 12,
        ringType: 0,
        uom: "EA",
        allowanceItemComps: [
          {
            performance: {
              allowanceCd: "T",
              perfCode1: "20",
              perfCode2: "52",
              payType: "D",
              allwPerfId: "63a3a12743a6cee87995b834",
            },
            allowanceTypeStartDate: "2023-10-11",
            allowanceTypeEndDate: "2023-10-17",
            allowAmount: 0,
          },
        ],
      },
      {
        itemId: "2020466",
        primaryUpc: "002113030654",
        caseUpc: "1002113030654",
        itemUpcs: ["002113030654", "1002113030654"],
        effectiveEndDate: "0001-01-01",
        vendorPackConversionFactor: 1,
        packWhse: 12,
        ringType: 0,
        uom: "EA",
        allowanceItemComps: [
          {
            performance: {
              allowanceCd: "T",
              perfCode1: "20",
              perfCode2: "52",
              payType: "D",
              allwPerfId: "63a3a12743a6cee87995b834",
            },
            allowanceTypeStartDate: "2023-10-11",
            allowanceTypeEndDate: "2023-10-17",
            allowAmount: 0,
          },
        ],
      },
      {
        itemId: "2022003",
        primaryUpc: "002113019884",
        caseUpc: "1002113019884",
        itemUpcs: ["002113019884", "1002113019884"],
        effectiveEndDate: "0001-01-01",
        vendorPackConversionFactor: 1,
        packWhse: 12,
        ringType: 0,
        uom: "EA",
        allowanceItemComps: [
          {
            performance: {
              allowanceCd: "T",
              perfCode1: "20",
              perfCode2: "52",
              payType: "D",
              allwPerfId: "63a3a12743a6cee87995b834",
            },
            allowanceTypeStartDate: "2023-10-11",
            allowanceTypeEndDate: "2023-10-17",
            allowAmount: 0,
          },
        ],
      },
    ],
    performance: {
      allowanceCd: "T",
      perfCode1: "20",
      perfCode2: "52",
      payType: "D",
      allwPerfId: "63a3a12743a6cee87995b834",
    },
    headerFlatAmt: 0,
    createTs: 1689831367045,
    createUserId: "PJAIN03",
    lastUpdTs: 1689831367045,
    lastUpdUserId: "PJAIN03",
    costAreaDesc: "",
    leadDistributorInd: true,
    includeInd: true,
  },
];
const pendingAllowances = [
  {
    id: "64b8c7c7bb19ce1061d9ca75",
    allowanceIdNbr: 700895301,
    vendorNbr: "",
    allowanceStatus: "Pending With Vendor",
    leadDistributorInfos: [],

    allowanceStartDate: "2023-10-11",
    allowanceEndDate: "2023-10-17",
    performStartDate: "2023-10-11",
    performEndDate: "2023-10-17",
    orderStartDate: "0001-01-01",
    orderEndDate: "0001-01-01",
    shipStartDate: "0001-01-01",
    shipEndDate: "0001-01-01",
    arrivalStartDate: "0001-01-01",
    arrivalEndDate: "0001-01-01",
    cancelledTs: "0001-01-03",
    processTs: "0001-01-03",
    lastApprovedDate: "0001-01-01",
    lastApprovedUserId: "",
    performance: {
      allowanceCd: "T",
      perfCode1: "20",
      perfCode2: "52",
      payType: "D",
      allwPerfId: "63a3a12743a6cee87995b834",
    },
    allowanceItems: [
      {
        itemId: "2020024",
        primaryUpc: "002113030601",
        caseUpc: "1002113030601",
        itemUpcs: ["002113030601", "1002113030601"],
        effectiveEndDate: "0001-01-01",
        vendorPackConversionFactor: 1,
        packWhse: 12,
        ringType: 0,
        uom: "EA",
        allowanceItemComps: [
          {
            performance: {
              allowanceCd: "T",
              perfCode1: "20",
              perfCode2: "52",
              payType: "D",
              allwPerfId: "63a3a12743a6cee87995b834",
            },
            allowanceTypeStartDate: "2023-10-11",
            allowanceTypeEndDate: "2023-10-17",
            allowAmount: 0,
          },
        ],
      },
      {
        itemId: "2020028",
        primaryUpc: "002113030602",
        caseUpc: "1002113030602",
        itemUpcs: ["002113030602", "1002113030602"],
        effectiveEndDate: "0001-01-01",
        vendorPackConversionFactor: 1,
        packWhse: 12,
        ringType: 0,
        uom: "EA",
        allowanceItemComps: [
          {
            performance: {
              allowanceCd: "T",
              perfCode1: "20",
              perfCode2: "52",
              payType: "D",
              allwPerfId: "63a3a12743a6cee87995b834",
            },
            allowanceTypeStartDate: "2023-10-11",
            allowanceTypeEndDate: "2023-10-17",
            allowAmount: 0,
          },
        ],
      },
      {
        itemId: "2020342",
        primaryUpc: "002113030605",
        caseUpc: "1002113030605",
        itemUpcs: ["002113030605", "1002113030605"],
        effectiveEndDate: "0001-01-01",
        vendorPackConversionFactor: 1,
        packWhse: 12,
        ringType: 0,
        uom: "EA",
        allowanceItemComps: [
          {
            performance: {
              allowanceCd: "T",
              perfCode1: "20",
              perfCode2: "52",
              payType: "D",
              allwPerfId: "63a3a12743a6cee87995b834",
            },
            allowanceTypeStartDate: "2023-10-11",
            allowanceTypeEndDate: "2023-10-17",
            allowAmount: 0,
          },
        ],
      },
      {
        itemId: "2020466",
        primaryUpc: "002113030654",
        caseUpc: "1002113030654",
        itemUpcs: ["002113030654", "1002113030654"],
        effectiveEndDate: "0001-01-01",
        vendorPackConversionFactor: 1,
        packWhse: 12,
        ringType: 0,
        uom: "EA",
        allowanceItemComps: [
          {
            performance: {
              allowanceCd: "T",
              perfCode1: "20",
              perfCode2: "52",
              payType: "D",
              allwPerfId: "63a3a12743a6cee87995b834",
            },
            allowanceTypeStartDate: "2023-10-11",
            allowanceTypeEndDate: "2023-10-17",
            allowAmount: 0,
          },
        ],
      },
      {
        itemId: "2022003",
        primaryUpc: "002113019884",
        caseUpc: "1002113019884",
        itemUpcs: ["002113019884", "1002113019884"],
        effectiveEndDate: "0001-01-01",
        vendorPackConversionFactor: 1,
        packWhse: 12,
        ringType: 0,
        uom: "EA",
        allowanceItemComps: [
          {
            performance: {
              allowanceCd: "T",
              perfCode1: "20",
              perfCode2: "52",
              payType: "D",
              allwPerfId: "63a3a12743a6cee87995b834",
            },
            allowanceTypeStartDate: "2023-10-11",
            allowanceTypeEndDate: "2023-10-17",
            allowAmount: 0,
          },
        ],
      },
    ],
    headerFlatAmt: 0,
    createTs: 1689831367045,
    createUserId: "PJAIN03",
    lastUpdTs: 1689831367045,
    lastUpdUserId: "PJAIN03",
    costAreaDesc: "",
    leadDistributorInd: true,
    includeInd: true,
  },
];
const mockPendingItem = [
  {
    itemId: "2020024",
    primaryUpc: "002113030601",
    caseUpc: "1002113030601",
    itemUpcs: ["002113030601", "1002113030601"],
    effectiveEndDate: "0001-01-01",
    vendorPackConversionFactor: 1,
    packWhse: 12,
    ringType: 0,
    uom: "EA",
    allowanceItemComps: [
      {
        performance: {
          allowanceCd: "T",
          perfCode1: "20",
          perfCode2: "52",
          payType: "D",
          allwPerfId: "63a3a12743a6cee87995b834",
        },
        allowanceTypeStartDate: "2023-10-11",
        allowanceTypeEndDate: "2023-10-17",
        allowAmount: 0,
      },
    ],
  },
];

const promoDetailsFields = [
  {
    label: "Promotion Type",
    key: "promotionType",
    formRegisterKey: "promotion.vehicleTypeOrCustomDate",
    historyMapperKey: "promotions.promoDetails.promotionType",
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    iSFieldsRelated: true,
    promoTypeVal: "CENT_OFF",
    showFields: ["NET_PRICE", "PERCENT_OFF"],
    conditionalShow: true,
  },
];
describe("Allowance Service", () => {
  it("should return default steppers for Scan allowance type", () => {
    const scanSteppers = [
      "Allowance Type & Performance",
      "Allowance to be Created",
      "Allowance Dates",
      "Allowance Amounts",
      "Default Billing Information",
    ];
    const stepper = getSteppers("DP", "SCAN", "");
    expect(stepper).toEqual(scanSteppers);
  });

  xit("should return correct steppers for SCAN allowance type with Separate Allowances By DSD Distributor", () => {
    const caseSteppers = [
      "Allowance Type & Performance",
      "Allowance to be Created",
      "DSD Case Allowance Dates",
      "DSD Case Allowance Amount",
      "Warehouse Case Allowance Dates",
      "Warehouse Case Allowance Amount",
      "DSD Default Billing Information",
      "Warehouse Default Billing Information",
    ];
    const stepper = getSteppers(
      "DP",
      "CASE",
      "Separate Allowances By DSD Distributor",
      ["DSD"]
    );
    expect(stepper).toEqual(caseSteppers);
  });

  it("should return correct steppers for Scan allowance type and allowance to be created type", () => {
    const scanAndBothAllowToBeCreatedSteppers = [
      "Allowance Type & Performance",
      "Allowance to be Created",
      "Allowance Dates",
      "Allowance Amounts",
      "Default Billing Information",
    ];
    const stepper = getSteppers(
      "DP",
      "SCAN",
      "One Allowance: Warehouse, DSD, or Combined"
    );
    expect(stepper).toEqual(scanAndBothAllowToBeCreatedSteppers);
  });

  it("getAllowanceMapKey should return the allowMapKey value from the ALLOWANCE_TYPES object based on the given key", () => {
    const key = "CASE";
    const expected = "caseAllow";
    const result = getAllowanceMapKey(key);
    expect(result).toEqual(expected);
  });

  it("getAllowanceMapKey should return an empty string if the key is not found in the ALLOWANCE_TYPES object", () => {
    const key = "null";
    const expected = "";
    const result = getAllowanceMapKey(key);
    expect(result).toEqual(expected);
  });

  it("getAllowanceMapValue function should return the value from the ALLOWANCE_TYPES object based on the given key", () => {
    const key = "CASE";
    const expected = "CASE";
    const result = getAllowanceMapValue(key);
    expect(result).toEqual(expected);
  });

  it("getAllowanceCaseTypeValue function should return the label from the ALLOWANCE_TYPES object based on the given key", () => {
    const key = "SCAN";
    const expected = "Scan";
    const result = getAllowanceCaseTypeValue(key);
    expect(result).toEqual(expected);
  });

  xit("getAllowanceCaseTypeValue function should return an empty string if the key is not found in the ALLOWANCE_TYPES object", () => {
    const key = "null";
    const expected = "";
    const result = getAllowanceCaseTypeValue(key);
    expect(result).toEqual(expected);
  });

  it("updateAllowanceFormValues function should update the value of the given allowanceRegField with the values from updateFormValuesObj", () => {
    const getValues = jest.fn().mockReturnValue({ Case: "1" });
    const setValue = jest.fn();
    const allowanceRegField = "allowanceRegField";
    const updateFormValuesObj = { Scan: "2" };
    updateAllowanceFormValues({
      getValues,
      allowanceRegField,
      setValue,
      updateFormValuesObj,
    });
    expect(setValue).toHaveBeenCalledWith(allowanceRegField, {
      Case: "1",
      Scan: "2",
    });
  });

  it("updateAllowanceFormValues function should set an empty object as the value of the given allowanceRegField if getValues returns null or undefined", () => {
    const getValues = jest.fn().mockReturnValue(undefined);
    const setValue = jest.fn();
    const allowanceRegField = "allowanceRegField";
    const updateFormValuesObj = { Scan: "2" };
    updateAllowanceFormValues({
      getValues,
      allowanceRegField,
      setValue,
      updateFormValuesObj,
    });
    expect(setValue).toHaveBeenCalledWith(allowanceRegField, {
      Scan: "2",
    });
  });

  it("updateAllowanceFormValues function should set an empty object as the value of the given allowanceRegField if updateFormValuesObj is null or undefined", () => {
    const getValues = jest.fn().mockReturnValue({ Case: "1" });
    const setValue = jest.fn();
    const allowanceRegField = "allowanceRegField";
    const updateFormValuesObj = {};
    updateAllowanceFormValues({
      getValues,
      allowanceRegField,
      setValue,
      updateFormValuesObj,
    });
    expect(setValue).toHaveBeenCalledWith(allowanceRegField, {
      Case: "1",
    });
  });

  it("getAllowanceFormRegisterKey function should return a string with the correct format based on the given cardIndex and cardItemIndex", () => {
    const cardIndex = 0;
    const cardItemIndex = 0;
    const expected = "offerAllowances[0].allowances[0]";
    const result = getAllowanceFormRegisterKey(cardIndex, cardItemIndex);
    expect(result).toEqual(expected);
  });

  it("returns correct offer and allowance name with vehicle name", () => {
    const result = generateOfferAndAllowanceName(responseData);
    expect(result).toBe(
      "Offer # 0 - Case - Price/Ad/Display(88) -  - description - Weekly Insert"
    );
  });

  it("formatTimestampToDate should return the formatted date with isMonthLongFormat as true", () => {
    const timestamp = 1621155151502;
    expect(formatTimestampToDate(timestamp, true)).toEqual("16 May, 2021");
  });

  it("formatTimestampToDate should return the formatted date with isMonthLongFormat as false", () => {
    const timestamp = 1621155151502;
    expect(formatTimestampToDate(timestamp, false)).toEqual("2021-05-16");
  });

  it("returns default allowance type", () => {
    const result = getDefaultAllowanceType();
    expect(result).toEqual("SCAN");
  });
  it("returns allowance steppers", () => {
    const result = getAllowanceSteppers();
    expect(result).toEqual([
      "Allowance Type & Performance",
      "Store Selection",
      "Allowance to be Created",
      "Allowance Dates",
      "DSD Allowance Dates",
      "Allowance Amounts",
      "DSD Case Allowance Dates",
      "DSD Case Allowance Amount",
      "DSD Scan Allowance Amount",
      "DSD Ship To Store Allowance Amount",
      "Default Billing Information",
      "DSD Default Billing Information",
      "Warehouse Case Allowance Dates",
      "Warehouse Case Allowance Amount",
      "Warehouse Scan Allowance Amount",
      "Warehouse Default Billing Information",
    ]);
  });

  it("When allowanceTypeName and productInfoSource are valid, it should return an array of options", () => {
    const expected = {
      name: "One Allowance: DSD Combined",
      key: "DSD_WHSE_RETAIL_DIVISION",
      registerField: "allowanceToBeCreated",
      disable: false,
      createIndex: "CC",
      text: "Select DSD Separate to create separate allowances that can be Billed for each Distributor or choose Combined to create only one Allowance.",
      options: [
        {
          name: "One Allowance: DSD Combined",
          key: "DSD_WHSE_RETAIL_DIVISION",
          routingKey: "DSD_WHSE_RETAIL_DIVISION",
          allowanceMap: {
            DSD_WHSE_RETAIL_DIVISION: [],
          },
          default: true,
          createIndex: "CC",
        },
        {
          name: "Separate Allowances By DSD Distributor",
          key: "DSD_LEAD_DISTRIBUTORS",
          routingKey: "DSD_LEAD_DISTRIBUTORS",
          allowanceMap: {
            DSD_LEAD_DISTRIBUTORS: [],
          },
          default: false,
          createIndex: "CD",
        },
      ],
    };
    const result = getAllowanceCreatedOptions("CASE", "DSD");
    expect(result).toEqual(expected);
  });

  it("When allowanceTypeName is invalid, it should return an empty array", () => {
    const expected = [];
    const result = getAllowanceCreatedOptions("", "DSD");
    expect(result).toEqual(expected);
  });

  it("When allowanceTypeName is valid and productInfoSource is not provided, it should return an empty array", () => {
    const expected = [];
    const result = getAllowanceCreatedOptions("CASE");
    expect(result).toEqual(expected);
  });

  it("get plan and pending value for amt", () => {
    const expectedValue = getAmtOrUomFromPlanOrPending(
      mockPlanItems,
      mockPendingItem,
      planAllowances,
      pendingAllowances,
      false,
      false,
      false
    );
    expect(expectedValue.planValue).toEqual("$0.00");
  });

  it("get plan and pending value for uom", function Wrsapper() {
    const expectedValue = getAmtOrUomFromPlanOrPending(
      mockPlanItems,
      mockPendingItem,
      planAllowances,
      pendingAllowances,
      false,
      false,
      true
    );
    expect(expectedValue.planValue).toEqual("EA");
  });

  it("When productInfoSource is undefined, it should return an empty array", () => {
    const expected = [];
    const result = getAllowanceCreatedOptions("CASE");
    expect(result).toEqual(expected);
  });

  it("When allowanceTypeName is valid but productInfoSource is invalid, it should return an empty array", () => {
    const expected = [];
    const result = getAllowanceCreatedOptions("CASE", "");
    expect(result).toEqual(expected);
  });
  it("get allow amt or uom value", function Wrsapper() {
    const mockGetValues = jest.fn().mockImplementation((key = null) => {
      if (key) {
        return [
          {
            id: "64b8c7c7bb19ce1061d9ca76",
            offerNumber: 7008953,
            origOfferNumber: 0,
            planEvent: "64b8c73b79b9016c8e9b4d00",
            createInd: "TC",
            minAggrAllowanceStartDate: "2023-10-11",
            maxAggrAllowanceEndDate: "2023-10-17",
            divisionIds: ["27"],
            allowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca75",
                allowanceIdNbr: 700895301,
                vendorNbr: "",
                allowanceStatus: "Pending With Vendor",
                leadDistributorInfos: [],
                vehicle: {
                  vehicleId: "63d4d1ec705a29397068dc5b",
                  vehicleNm: "27 Week 41 Insert 2023",
                  sourceVehicleSk: 42991,
                  startDate: "2023-10-11",
                  endDate: "2023-10-17",
                  vehicleType: {
                    vehicleTypeId: "636abba1b426ee543a94d3ac",
                    sourceVehicleTypeSk: 198,
                    vehicleTypNm: "insrt",
                    vehicleTypDesc: "Weekly Insert",
                  },
                },
                allowanceStartDate: "2023-10-11",
                allowanceEndDate: "2023-10-17",
                performStartDate: "2023-10-11",
                performEndDate: "2023-10-17",
                orderStartDate: "0001-01-01",
                orderEndDate: "0001-01-01",
                shipStartDate: "0001-01-01",
                shipEndDate: "0001-01-01",
                arrivalStartDate: "0001-01-01",
                arrivalEndDate: "0001-01-01",
                cancelledTs: "0001-01-03",
                processTs: "0001-01-03",
                lastApprovedDate: "0001-01-01",
                lastApprovedUserId: "",
                allowanceBillingInfo: {
                  absMerchVendor: "000504",
                  absVendorName: "BAY VALLEY FOODS LLC",
                  absVendorPaymentType: "Deduction",
                  acPayableVendorNbr: "010029",
                  acReceivableVendorNbr: "142798",
                  billingContactName: "",
                  billingContactEmail: "",
                  vendorComment: "",
                  vendorOfferTrackingNbr: "",
                },
                performance: {
                  allowanceCd: "T",
                  perfCode1: "20",
                  perfCode2: "52",
                  payType: "D",
                  allwPerfId: "63a3a12743a6cee87995b834",
                },
                allowanceItems: [
                  {
                    itemId: "2020024",
                    primaryUpc: "002113030601",
                    caseUpc: "1002113030601",
                    itemUpcs: ["002113030601", "1002113030601"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020028",
                    primaryUpc: "002113030602",
                    caseUpc: "1002113030602",
                    itemUpcs: ["002113030602", "1002113030602"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020342",
                    primaryUpc: "002113030605",
                    caseUpc: "1002113030605",
                    itemUpcs: ["002113030605", "1002113030605"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020466",
                    primaryUpc: "002113030654",
                    caseUpc: "1002113030654",
                    itemUpcs: ["002113030654", "1002113030654"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2022003",
                    primaryUpc: "002113019884",
                    caseUpc: "1002113019884",
                    itemUpcs: ["002113019884", "1002113019884"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                ],
                location: {
                  locationId: "639033d538196056762e6e28",
                  locationName: "27 - Seattle",
                  distCenter: "DDSE",
                  locationTypeCd: "D",
                  locationTypeCdEnum: "RETAIL_DIVISION",
                },
                headerFlatAmt: 0,
                createTs: 1689831367045,
                createUserId: "PJAIN03",
                lastUpdTs: 1689831367045,
                lastUpdUserId: "PJAIN03",
                costAreaDesc: "",
                leadDistributorInd: true,
                includeInd: true,
              },
            ],
            periscopeIds: ["891061"],
            allowanceChangeStatus: "UPDATED",
            createTempWorkAllowanceId: "64b8c73cbb19ce1061d9ca74",
            offerId: "64b8c7c7bb19ce1061d9ca76",
          },
        ];
      } else {
        return {
          planEvent: {
            offerAllowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca76",
                offerNumber: 7008953,
                origOfferNumber: 0,
                planEvent: "64b8c73b79b9016c8e9b4d00",
                createInd: "TC",
                minAggrAllowanceStartDate: "2023-10-11",
                maxAggrAllowanceEndDate: "2023-10-17",
                divisionIds: ["27"],
                allowances: [
                  {
                    id: "64b8c7c7bb19ce1061d9ca75",
                    allowanceIdNbr: 700895301,
                    vendorNbr: "",
                    allowanceStatus: "Pending With Vendor",
                    leadDistributorInfos: [],
                    vehicle: {
                      vehicleId: "63d4d1ec705a29397068dc5b",
                      vehicleNm: "27 Week 41 Insert 2023",
                      sourceVehicleSk: 42991,
                      startDate: "2023-10-11",
                      endDate: "2023-10-17",
                      vehicleType: {
                        vehicleTypeId: "636abba1b426ee543a94d3ac",
                        sourceVehicleTypeSk: 198,
                        vehicleTypNm: "insrt",
                        vehicleTypDesc: "Weekly Insert",
                      },
                    },
                    defaultAllowanceDates: {
                      allowanceStartDate: "2023-10-11",
                      allowanceEndDate: "2023-10-17",
                      performStartDate: "2023-10-11",
                      performEndDate: "2023-10-17",
                      orderStartDate: "0001-01-01",
                      orderEndDate: "0001-01-01",
                      shipStartDate: "0001-01-01",
                      shipEndDate: "0001-01-01",
                      arrivalStartDate: "0001-01-01",
                      arrivalEndDate: "0001-01-01",
                      overrideInd: false,
                      notPreSaved: false,
                    },
                    allowanceStartDate: "2023-10-11",
                    allowanceEndDate: "2023-10-17",
                    performStartDate: "2023-10-11",
                    performEndDate: "2023-10-17",
                    orderStartDate: "0001-01-01",
                    orderEndDate: "0001-01-01",
                    shipStartDate: "0001-01-01",
                    shipEndDate: "0001-01-01",
                    arrivalStartDate: "0001-01-01",
                    arrivalEndDate: "0001-01-01",
                    cancelledTs: "0001-01-03",
                    processTs: "0001-01-03",
                    lastApprovedDate: "0001-01-01",
                    lastApprovedUserId: "",
                    allowanceBillingInfo: {
                      absMerchVendor: "000504",
                      absVendorName: "BAY VALLEY FOODS LLC",
                      absVendorPaymentType: "Deduction",
                      acPayableVendorNbr: "010029",
                      acReceivableVendorNbr: "142798",
                      billingContactName: "",
                      billingContactEmail: "",
                      vendorComment: "",
                      vendorOfferTrackingNbr: "",
                    },
                    allowanceBillingInfos: [
                      {
                        vendorIds: [
                          {
                            vendorNbr: "000504",
                            vendorSubAccount: "005",
                            distCenter: "WANC",
                            fullVendorNbr: "000504-005",
                          },
                        ],
                        absMerchVendor: "000504-005",
                        absVendorName: "BAY VALLEY FOODS LLC",
                        absVendorPaymentType: "D",
                        acPayableVendorNbr: "010029",
                        acReceivableVendorNbr: "142798",
                        billingContactName: "AMY RASMUSSEN - VMI AN   ",
                        billingContactEmail: "<EMAIL>",
                        vendorBillingList: [
                          {
                            billingContactName: "AMY RASMUSSEN - VMI AN   ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "MARK WASHFORD            ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "SHERYL MIELKE TRADE PROMO",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "TINA KECKHAVER - CSR     ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "AMY RASMUSSEN - VMI AN   ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "MARK WASHFORD            ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "SHERYL MIELKE TRADE PROMO",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "TINA KECKHAVER - CSR     ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                        ],
                      },
                    ],
                    storeGroups: [
                      {
                        storeGroupId: "6453d8fa31c0c4e30d8f11d3",
                        sourceStoreGroupId: "EDM",
                        storeGroupName: "Seattle All Stores",
                        storeGroupType: {
                          groupType: "S",
                          storeGrpTypeName: "Division",
                          groupInd: "D",
                        },
                        divisionIds: ["27"],
                        storeCount: 220,
                      },
                    ],
                    performance: {
                      allowanceCd: "T",
                      perfCode1: "20",
                      perfCode2: "52",
                      payType: "D",
                      allwPerfId: "63a3a12743a6cee87995b834",
                    },
                    planProductGroups: [
                      {
                        planProductGroupId: "636bde8d9665d0440e006e46",
                        sourceProductGroupId: 185046,
                        name: "Signature SELECT® Crackers - 84972",
                        divisionId: "27",
                        smicGroupCode: 2,
                        smicCategoryCode: "0202",
                        supplier: {
                          supplierId: "7877",
                          supplierName: "ABS-ASMs",
                        },
                        itemCount: 5,
                        displayInd: false,
                        simsVendors: ["000504"],
                      },
                    ],
                    allowanceItems: [
                      {
                        itemId: "2020024",
                        primaryUpc: "002113030601",
                        caseUpc: "1002113030601",
                        itemUpcs: ["002113030601", "1002113030601"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020028",
                        primaryUpc: "002113030602",
                        caseUpc: "1002113030602",
                        itemUpcs: ["002113030602", "1002113030602"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020342",
                        primaryUpc: "002113030605",
                        caseUpc: "1002113030605",
                        itemUpcs: ["002113030605", "1002113030605"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020466",
                        primaryUpc: "002113030654",
                        caseUpc: "1002113030654",
                        itemUpcs: ["002113030654", "1002113030654"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2022003",
                        primaryUpc: "002113019884",
                        caseUpc: "1002113019884",
                        itemUpcs: ["002113019884", "1002113019884"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                    ],
                    location: {
                      locationId: "639033d538196056762e6e28",
                      locationName: "27 - Seattle",
                      distCenter: "DDSE",
                      locationTypeCd: "D",
                      locationTypeCdEnum: "RETAIL_DIVISION",
                    },
                    headerFlatAmt: 0,
                    createTs: 1689831367045,
                    createUserId: "PJAIN03",
                    lastUpdTs: 1689831367045,
                    lastUpdUserId: "PJAIN03",
                    costAreaDesc: "",
                    leadDistributorInd: true,
                    includeInd: true,
                  },
                ],
                periscopeIds: ["891061"],
                createUser: {
                  userId: "PJAIN03",
                  name: "Prayas Jain (Contractor)",
                  type: "Merchant",
                  userRoles: ["az-meupp-nonprod-promointeditor"],
                  createTs: "2023-07-20T05:36:07.044Z",
                },
                updateUser: {
                  userId: "PJAIN03",
                  name: "Prayas Jain (Contractor)",
                  type: "Merchant",
                  userRoles: ["az-meupp-nonprod-promointeditor"],
                  createTs: "2023-07-20T05:36:07.045Z",
                },
                allowanceChangeStatus: "UPDATED",
                createTempWorkAllowanceId: "64b8c73cbb19ce1061d9ca74",
                offerId: "64b8c7c7bb19ce1061d9ca76",
              },
            ],
          },
          planEventPending: {
            offerAllowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca76",
                offerNumber: 7008953,
                origOfferNumber: 0,
                planEvent: "64b8c73b79b9016c8e9b4d00",
                createInd: "TC",
                minAggrAllowanceStartDate: "2023-10-11",
                maxAggrAllowanceEndDate: "2023-10-17",
                divisionIds: ["27"],
                allowances: [
                  {
                    id: "64b8c7c7bb19ce1061d9ca75",
                    allowanceIdNbr: 700895301,
                    vendorNbr: "",
                    allowanceStatus: "Pending With Vendor",
                    leadDistributorInfos: [],
                    vehicle: {
                      vehicleId: "63d4d1ec705a29397068dc5b",
                      vehicleNm: "27 Week 41 Insert 2023",
                      sourceVehicleSk: 42991,
                      startDate: "2023-10-11",
                      endDate: "2023-10-17",
                      vehicleType: {
                        vehicleTypeId: "636abba1b426ee543a94d3ac",
                        sourceVehicleTypeSk: 198,
                        vehicleTypNm: "insrt",
                        vehicleTypDesc: "Weekly Insert",
                      },
                    },
                    allowanceStartDate: "2023-10-11",
                    allowanceEndDate: "2023-10-17",
                    performStartDate: "2023-10-11",
                    performEndDate: "2023-10-17",
                    orderStartDate: "0001-01-01",
                    orderEndDate: "0001-01-01",
                    shipStartDate: "0001-01-01",
                    shipEndDate: "0001-01-01",
                    arrivalStartDate: "0001-01-01",
                    arrivalEndDate: "0001-01-01",
                    cancelledTs: "0001-01-03",
                    processTs: "0001-01-03",
                    lastApprovedDate: "0001-01-01",
                    lastApprovedUserId: "",
                    performance: {
                      allowanceCd: "T",
                      perfCode1: "20",
                      perfCode2: "52",
                      payType: "D",
                      allwPerfId: "63a3a12743a6cee87995b834",
                    },
                    allowanceItems: [
                      {
                        itemId: "2020024",
                        primaryUpc: "002113030601",
                        caseUpc: "1002113030601",
                        itemUpcs: ["002113030601", "1002113030601"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020028",
                        primaryUpc: "002113030602",
                        caseUpc: "1002113030602",
                        itemUpcs: ["002113030602", "1002113030602"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020342",
                        primaryUpc: "002113030605",
                        caseUpc: "1002113030605",
                        itemUpcs: ["002113030605", "1002113030605"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020466",
                        primaryUpc: "002113030654",
                        caseUpc: "1002113030654",
                        itemUpcs: ["002113030654", "1002113030654"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2022003",
                        primaryUpc: "002113019884",
                        caseUpc: "1002113019884",
                        itemUpcs: ["002113019884", "1002113019884"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                    ],
                    location: {
                      locationId: "639033d538196056762e6e28",
                      locationName: "27 - Seattle",
                      distCenter: "DDSE",
                      locationTypeCd: "D",
                      locationTypeCdEnum: "RETAIL_DIVISION",
                    },
                    headerFlatAmt: 0,
                    createTs: 1689831367045,
                    createUserId: "PJAIN03",
                    lastUpdTs: 1689831367045,
                    lastUpdUserId: "PJAIN03",
                    costAreaDesc: "",
                    leadDistributorInd: true,
                    includeInd: true,
                  },
                ],
                allowanceChangeStatus: "CREATED",
              },
            ],
          },
        };
      }
    });
    const expectedValue = getAllowanceObjForAmt(mockGetValues, 7008953);
    expect(expectedValue.isHeaderFlat).toEqual(false);
  });
  it("get amount uom obj for plan and pending", () => {
    const mockGetValues = jest.fn().mockImplementation((key = null) => {
      if (key) {
        return [
          {
            id: "64b8c7c7bb19ce1061d9ca76",
            offerNumber: 7008953,
            origOfferNumber: 0,
            planEvent: "64b8c73b79b9016c8e9b4d00",
            createInd: "TC",
            minAggrAllowanceStartDate: "2023-10-11",
            maxAggrAllowanceEndDate: "2023-10-17",
            divisionIds: ["27"],
            allowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca75",
                allowanceIdNbr: 700895301,
                vendorNbr: "",
                allowanceStatus: "Pending With Vendor",
                leadDistributorInfos: [],
                vehicle: {
                  vehicleId: "63d4d1ec705a29397068dc5b",
                  vehicleNm: "27 Week 41 Insert 2023",
                  sourceVehicleSk: 42991,
                  startDate: "2023-10-11",
                  endDate: "2023-10-17",
                  vehicleType: {
                    vehicleTypeId: "636abba1b426ee543a94d3ac",
                    sourceVehicleTypeSk: 198,
                    vehicleTypNm: "insrt",
                    vehicleTypDesc: "Weekly Insert",
                  },
                },
                allowanceStartDate: "2023-10-11",
                allowanceEndDate: "2023-10-17",
                performStartDate: "2023-10-11",
                performEndDate: "2023-10-17",
                orderStartDate: "0001-01-01",
                orderEndDate: "0001-01-01",
                shipStartDate: "0001-01-01",
                shipEndDate: "0001-01-01",
                arrivalStartDate: "0001-01-01",
                arrivalEndDate: "0001-01-01",
                cancelledTs: "0001-01-03",
                processTs: "0001-01-03",
                lastApprovedDate: "0001-01-01",
                lastApprovedUserId: "",
                allowanceBillingInfo: {
                  absMerchVendor: "000504",
                  absVendorName: "BAY VALLEY FOODS LLC",
                  absVendorPaymentType: "Deduction",
                  acPayableVendorNbr: "010029",
                  acReceivableVendorNbr: "142798",
                  billingContactName: "",
                  billingContactEmail: "",
                  vendorComment: "",
                  vendorOfferTrackingNbr: "",
                },
                performance: {
                  allowanceCd: "T",
                  perfCode1: "20",
                  perfCode2: "52",
                  payType: "D",
                  allwPerfId: "63a3a12743a6cee87995b834",
                },
                allowanceItems: [
                  {
                    itemId: "2020024",
                    primaryUpc: "002113030601",
                    caseUpc: "1002113030601",
                    itemUpcs: ["002113030601", "1002113030601"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020028",
                    primaryUpc: "002113030602",
                    caseUpc: "1002113030602",
                    itemUpcs: ["002113030602", "1002113030602"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020342",
                    primaryUpc: "002113030605",
                    caseUpc: "1002113030605",
                    itemUpcs: ["002113030605", "1002113030605"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020466",
                    primaryUpc: "002113030654",
                    caseUpc: "1002113030654",
                    itemUpcs: ["002113030654", "1002113030654"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2022003",
                    primaryUpc: "002113019884",
                    caseUpc: "1002113019884",
                    itemUpcs: ["002113019884", "1002113019884"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                ],
                location: {
                  locationId: "639033d538196056762e6e28",
                  locationName: "27 - Seattle",
                  distCenter: "DDSE",
                  locationTypeCd: "D",
                  locationTypeCdEnum: "RETAIL_DIVISION",
                },
                headerFlatAmt: 0,
                createTs: 1689831367045,
                createUserId: "PJAIN03",
                lastUpdTs: 1689831367045,
                lastUpdUserId: "PJAIN03",
                costAreaDesc: "",
                leadDistributorInd: true,
                includeInd: true,
              },
            ],
            periscopeIds: ["891061"],
            allowanceChangeStatus: "UPDATED",
            createTempWorkAllowanceId: "64b8c73cbb19ce1061d9ca74",
            offerId: "64b8c7c7bb19ce1061d9ca76",
          },
        ];
      } else {
        return {
          planEvent: {
            offerAllowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca76",
                offerNumber: 7008953,
                origOfferNumber: 0,
                planEvent: "64b8c73b79b9016c8e9b4d00",
                createInd: "TC",
                minAggrAllowanceStartDate: "2023-10-11",
                maxAggrAllowanceEndDate: "2023-10-17",
                divisionIds: ["27"],
                allowances: [
                  {
                    id: "64b8c7c7bb19ce1061d9ca75",
                    allowanceIdNbr: 700895301,
                    vendorNbr: "",
                    allowanceStatus: "Pending With Vendor",
                    leadDistributorInfos: [],
                    vehicle: {
                      vehicleId: "63d4d1ec705a29397068dc5b",
                      vehicleNm: "27 Week 41 Insert 2023",
                      sourceVehicleSk: 42991,
                      startDate: "2023-10-11",
                      endDate: "2023-10-17",
                      vehicleType: {
                        vehicleTypeId: "636abba1b426ee543a94d3ac",
                        sourceVehicleTypeSk: 198,
                        vehicleTypNm: "insrt",
                        vehicleTypDesc: "Weekly Insert",
                      },
                    },
                    defaultAllowanceDates: {
                      allowanceStartDate: "2023-10-11",
                      allowanceEndDate: "2023-10-17",
                      performStartDate: "2023-10-11",
                      performEndDate: "2023-10-17",
                      orderStartDate: "0001-01-01",
                      orderEndDate: "0001-01-01",
                      shipStartDate: "0001-01-01",
                      shipEndDate: "0001-01-01",
                      arrivalStartDate: "0001-01-01",
                      arrivalEndDate: "0001-01-01",
                      overrideInd: false,
                      notPreSaved: false,
                    },
                    allowanceStartDate: "2023-10-11",
                    allowanceEndDate: "2023-10-17",
                    performStartDate: "2023-10-11",
                    performEndDate: "2023-10-17",
                    orderStartDate: "0001-01-01",
                    orderEndDate: "0001-01-01",
                    shipStartDate: "0001-01-01",
                    shipEndDate: "0001-01-01",
                    arrivalStartDate: "0001-01-01",
                    arrivalEndDate: "0001-01-01",
                    cancelledTs: "0001-01-03",
                    processTs: "0001-01-03",
                    lastApprovedDate: "0001-01-01",
                    lastApprovedUserId: "",
                    allowanceBillingInfo: {
                      absMerchVendor: "000504",
                      absVendorName: "BAY VALLEY FOODS LLC",
                      absVendorPaymentType: "Deduction",
                      acPayableVendorNbr: "010029",
                      acReceivableVendorNbr: "142798",
                      billingContactName: "",
                      billingContactEmail: "",
                      vendorComment: "",
                      vendorOfferTrackingNbr: "",
                    },
                    allowanceBillingInfos: [
                      {
                        vendorIds: [
                          {
                            vendorNbr: "000504",
                            vendorSubAccount: "005",
                            distCenter: "WANC",
                            fullVendorNbr: "000504-005",
                          },
                        ],
                        absMerchVendor: "000504-005",
                        absVendorName: "BAY VALLEY FOODS LLC",
                        absVendorPaymentType: "D",
                        acPayableVendorNbr: "010029",
                        acReceivableVendorNbr: "142798",
                        billingContactName: "AMY RASMUSSEN - VMI AN   ",
                        billingContactEmail: "<EMAIL>",
                        vendorBillingList: [
                          {
                            billingContactName: "AMY RASMUSSEN - VMI AN   ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "MARK WASHFORD            ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "SHERYL MIELKE TRADE PROMO",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "TINA KECKHAVER - CSR     ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "AMY RASMUSSEN - VMI AN   ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "MARK WASHFORD            ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "SHERYL MIELKE TRADE PROMO",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "TINA KECKHAVER - CSR     ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                        ],
                      },
                    ],
                    storeGroups: [
                      {
                        storeGroupId: "6453d8fa31c0c4e30d8f11d3",
                        sourceStoreGroupId: "EDM",
                        storeGroupName: "Seattle All Stores",
                        storeGroupType: {
                          groupType: "S",
                          storeGrpTypeName: "Division",
                          groupInd: "D",
                        },
                        divisionIds: ["27"],
                        storeCount: 220,
                      },
                    ],
                    performance: {
                      allowanceCd: "T",
                      perfCode1: "20",
                      perfCode2: "52",
                      payType: "D",
                      allwPerfId: "63a3a12743a6cee87995b834",
                    },
                    planProductGroups: [
                      {
                        planProductGroupId: "636bde8d9665d0440e006e46",
                        sourceProductGroupId: 185046,
                        name: "Signature SELECT® Crackers - 84972",
                        divisionId: "27",
                        smicGroupCode: 2,
                        smicCategoryCode: "0202",
                        supplier: {
                          supplierId: "7877",
                          supplierName: "ABS-ASMs",
                        },
                        itemCount: 5,
                        displayInd: false,
                        simsVendors: ["000504"],
                      },
                    ],
                    allowanceItems: [
                      {
                        itemId: "2020024",
                        primaryUpc: "002113030601",
                        caseUpc: "1002113030601",
                        itemUpcs: ["002113030601", "1002113030601"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020028",
                        primaryUpc: "002113030602",
                        caseUpc: "1002113030602",
                        itemUpcs: ["002113030602", "1002113030602"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020342",
                        primaryUpc: "002113030605",
                        caseUpc: "1002113030605",
                        itemUpcs: ["002113030605", "1002113030605"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020466",
                        primaryUpc: "002113030654",
                        caseUpc: "1002113030654",
                        itemUpcs: ["002113030654", "1002113030654"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2022003",
                        primaryUpc: "002113019884",
                        caseUpc: "1002113019884",
                        itemUpcs: ["002113019884", "1002113019884"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                    ],
                    location: {
                      locationId: "639033d538196056762e6e28",
                      locationName: "27 - Seattle",
                      distCenter: "DDSE",
                      locationTypeCd: "D",
                      locationTypeCdEnum: "RETAIL_DIVISION",
                    },
                    headerFlatAmt: 0,
                    createTs: 1689831367045,
                    createUserId: "PJAIN03",
                    lastUpdTs: 1689831367045,
                    lastUpdUserId: "PJAIN03",
                    costAreaDesc: "",
                    leadDistributorInd: true,
                    includeInd: true,
                  },
                ],
                periscopeIds: ["891061"],
                createUser: {
                  userId: "PJAIN03",
                  name: "Prayas Jain (Contractor)",
                  type: "Merchant",
                  userRoles: ["az-meupp-nonprod-promointeditor"],
                  createTs: "2023-07-20T05:36:07.044Z",
                },
                updateUser: {
                  userId: "PJAIN03",
                  name: "Prayas Jain (Contractor)",
                  type: "Merchant",
                  userRoles: ["az-meupp-nonprod-promointeditor"],
                  createTs: "2023-07-20T05:36:07.045Z",
                },
                allowanceChangeStatus: "UPDATED",
                createTempWorkAllowanceId: "64b8c73cbb19ce1061d9ca74",
                offerId: "64b8c7c7bb19ce1061d9ca76",
              },
            ],
          },
          planEventPending: {
            offerAllowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca76",
                offerNumber: 7008953,
                origOfferNumber: 0,
                planEvent: "64b8c73b79b9016c8e9b4d00",
                createInd: "TC",
                minAggrAllowanceStartDate: "2023-10-11",
                maxAggrAllowanceEndDate: "2023-10-17",
                divisionIds: ["27"],
                allowances: [
                  {
                    id: "64b8c7c7bb19ce1061d9ca75",
                    allowanceIdNbr: 700895301,
                    vendorNbr: "",
                    allowanceStatus: "Pending With Vendor",
                    leadDistributorInfos: [],
                    vehicle: {
                      vehicleId: "63d4d1ec705a29397068dc5b",
                      vehicleNm: "27 Week 41 Insert 2023",
                      sourceVehicleSk: 42991,
                      startDate: "2023-10-11",
                      endDate: "2023-10-17",
                      vehicleType: {
                        vehicleTypeId: "636abba1b426ee543a94d3ac",
                        sourceVehicleTypeSk: 198,
                        vehicleTypNm: "insrt",
                        vehicleTypDesc: "Weekly Insert",
                      },
                    },
                    allowanceStartDate: "2023-10-11",
                    allowanceEndDate: "2023-10-17",
                    performStartDate: "2023-10-11",
                    performEndDate: "2023-10-17",
                    orderStartDate: "0001-01-01",
                    orderEndDate: "0001-01-01",
                    shipStartDate: "0001-01-01",
                    shipEndDate: "0001-01-01",
                    arrivalStartDate: "0001-01-01",
                    arrivalEndDate: "0001-01-01",
                    cancelledTs: "0001-01-03",
                    processTs: "0001-01-03",
                    lastApprovedDate: "0001-01-01",
                    lastApprovedUserId: "",
                    performance: {
                      allowanceCd: "T",
                      perfCode1: "20",
                      perfCode2: "52",
                      payType: "D",
                      allwPerfId: "63a3a12743a6cee87995b834",
                    },
                    allowanceItems: [
                      {
                        itemId: "2020024",
                        primaryUpc: "002113030601",
                        caseUpc: "1002113030601",
                        itemUpcs: ["002113030601", "1002113030601"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020028",
                        primaryUpc: "002113030602",
                        caseUpc: "1002113030602",
                        itemUpcs: ["002113030602", "1002113030602"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020342",
                        primaryUpc: "002113030605",
                        caseUpc: "1002113030605",
                        itemUpcs: ["002113030605", "1002113030605"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020466",
                        primaryUpc: "002113030654",
                        caseUpc: "1002113030654",
                        itemUpcs: ["002113030654", "1002113030654"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2022003",
                        primaryUpc: "002113019884",
                        caseUpc: "1002113019884",
                        itemUpcs: ["002113019884", "1002113019884"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                    ],
                    location: {
                      locationId: "639033d538196056762e6e28",
                      locationName: "27 - Seattle",
                      distCenter: "DDSE",
                      locationTypeCd: "D",
                      locationTypeCdEnum: "RETAIL_DIVISION",
                    },
                    headerFlatAmt: 0,
                    createTs: 1689831367045,
                    createUserId: "PJAIN03",
                    lastUpdTs: 1689831367045,
                    lastUpdUserId: "PJAIN03",
                    costAreaDesc: "",
                    leadDistributorInd: true,
                    includeInd: true,
                  },
                ],
                allowanceChangeStatus: "CREATED",
              },
            ],
          },
        };
      }
    });
    const fiedlObj = {
      planValue: "EA",
      planEventPendingChanges: {
        id: "64b8fce8540fee7ade0dfcc8",
        eventId: "64b8c73b79b9016c8e9b4d00",
        eventIdNbr: 10012582,
        divisionIds: ["27"],
        createUser: {
          userId: "PJAIN03",
          name: "Prayas Jain (Contractor)",
          type: "Merchant",
          userRoles: ["az-meupp-nonprod-promointeditor"],
          createTs: "2023-07-20T05:33:47.941Z",
        },
        eventChanges: [
          {
            fieldPath: "/offerAllowances/0/allowanceChangeStatus",
            labelFieldName: "planevents.offerAllowances.allowanceChangeStatus",
            beforeVal: "UPDATED",
            afterVal: "CREATED",
          },
          {
            fieldPath: "/planEventWorkFlowType",
            labelFieldName: "planevents.planEventWorkFlowType",
            beforeVal: "Modify",
            afterVal: "NOT FOUND",
          },
        ],
        offerAllowanceChanges: [],
        promotionsChanges: [],
      },
      planEventHistory: [
        {
          id: "64b8c73b79b9016c8e9b4d01",
          eventId: "64b8c73b79b9016c8e9b4d00",
          eventIdNbr: 10012582,
          divisionIds: ["27"],
          createUser: {
            userId: "PJAIN03",
            name: "Prayas Jain (Contractor)",
            type: "Merchant",
            userRoles: ["az-meupp-nonprod-promointeditor"],
            createTs: "2023-07-20T05:33:47.941Z",
          },
          eventStatus: "Draft",
        },
        {
          id: "64b8c7f379b9016c8e9b4d04",
          eventId: "64b8c73b79b9016c8e9b4d00",
          eventIdNbr: 10012582,
          divisionIds: ["27"],
          createUser: {
            userId: "PJAIN03",
            name: "Prayas Jain (Contractor)",
            type: "Merchant",
            userRoles: ["az-meupp-nonprod-promointeditor"],
            createTs: "2023-07-20T05:36:51.497Z",
          },
          eventChanges: [
            {
              historyGroupId: "10012582",
              fieldPath: "/eventStatus",
              labelFieldName: "planevents.eventStatus",
              beforeVal: "Draft",
              afterVal: "Pending With Vendor",
            },
          ],
          offerAllowanceChanges: [
            {
              offerNumber: 7008953,
              changes: [
                {
                  historyGroupId: "700895301",
                  fieldPath: "/allowances/0/allowanceStatus",
                  labelFieldName: "offerallowances.allowances.allowanceStatus",
                  beforeVal: "Draft",
                  afterVal: "Pending With Vendor",
                },
              ],
              nonDisplayChanges: [],
            },
          ],
          promotionsChanges: [
            {
              promotionId: 10005194,
              changes: [
                {
                  historyGroupId: "10005194",
                  fieldPath: "/promotionWorkflowStatus",
                  labelFieldName: "promotions.promotionWorkflowStatus",
                  beforeVal: "Draft",
                  afterVal: "Pending With Vendor",
                },
              ],
              nonDisplayChanges: [],
            },
          ],
        },
      ],
      indicatorValue: false,
      planPendingObj: {
        id: "64b8c73b79b9016c8e9b4d00",
        planEventIdNbr: 10012582,
        name: "Signature SELECT® Crackers - 84972 - Seattle All uStores (220) - Weekly Insert - 27 Week 41 Insert 2023",
        divisionIds: ["27"],
        startDate: "2023-10-11",
        endDate: "2023-10-17",
        eventType: "DP",
        sourceEventType: "ECP",
        eventStatus: "Pending With Vendor",
        planProductGroups: [
          {
            planProductGroupId: "636bde8d9665d0440e006e46",
            sourceProductGroupId: 185046,
            name: "Signature SELECT® Crackers - 84972",
            divisionId: "27",
            smicGroupCode: 2,
            smicCategoryCode: "0202",
            supplier: {
              supplierId: "7877",
              supplierName: "ABS-ASMs",
            },
            itemCount: 5,
            displayInd: false,
            simsVendors: ["000504"],
          },
        ],
        pricing: [
          {
            planProductGroup: {
              planProductGroupId: "636bde8d9665d0440e006e46",
              sourceProductGroupId: 185046,
              name: "Signature SELECT® Crackers - 84972",
              divisionId: "27",
              smicGroupCode: 2,
              smicCategoryCode: "0202",
              supplier: {
                supplierId: "7877",
                supplierName: "ABS-ASMs",
              },
              itemCount: 5,
              displayInd: false,
              simsVendors: ["000504"],
            },
            quadrant: "",
            priceAmount: "",
            priceFactor: "0",
            priceLimitQty: "1",
            prcMtd: "",
            promoType: "BUY_ONE_GET_ONE",
          },
        ],
        storeGroups: [
          {
            storeGroupId: "6453d8fa31c0c4e30d8f11d3",
            sourceStoreGroupId: "EDM",
            storeGroupName: "Seattle All Stores",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Division",
              groupInd: "D",
            },
            divisionIds: ["27"],
            storeCount: 220,
          },
        ],
        offerAllowances: [
          {
            id: "64b8c7c7bb19ce1061d9ca76",
            offerNumber: 7008953,
            origOfferNumber: 0,
            planEvent: "64b8c73b79b9016c8e9b4d00",
            createInd: "TC",
            minAggrAllowanceStartDate: "2023-10-11",
            maxAggrAllowanceEndDate: "2023-10-17",
            divisionIds: ["27"],
            allowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca75",
                allowanceIdNbr: 700895301,
                vendorNbr: "",
                allowanceStatus: "Pending With Vendor",
                leadDistributorInfos: [],
                vehicle: {
                  vehicleId: "63d4d1ec705a29397068dc5b",
                  vehicleNm: "27 Week 41 Insert 2023",
                  sourceVehicleSk: 42991,
                  startDate: "2023-10-11",
                  endDate: "2023-10-17",
                  vehicleType: {
                    vehicleTypeId: "636abba1b426ee543a94d3ac",
                    sourceVehicleTypeSk: 198,
                    vehicleTypNm: "insrt",
                    vehicleTypDesc: "Weekly Insert",
                  },
                },
                defaultAllowanceDates: {
                  allowanceStartDate: "2023-10-11",
                  allowanceEndDate: "2023-10-17",
                  performStartDate: "2023-10-11",
                  performEndDate: "2023-10-17",
                  orderStartDate: "0001-01-01",
                  orderEndDate: "0001-01-01",
                  shipStartDate: "0001-01-01",
                  shipEndDate: "0001-01-01",
                  arrivalStartDate: "0001-01-01",
                  arrivalEndDate: "0001-01-01",
                  overrideInd: false,
                  notPreSaved: false,
                },
                allowanceStartDate: "2023-10-11",
                allowanceEndDate: "2023-10-17",
                performStartDate: "2023-10-11",
                performEndDate: "2023-10-17",
                orderStartDate: "0001-01-01",
                orderEndDate: "0001-01-01",
                shipStartDate: "0001-01-01",
                shipEndDate: "0001-01-01",
                arrivalStartDate: "0001-01-01",
                arrivalEndDate: "0001-01-01",
                cancelledTs: "0001-01-03",
                processTs: "0001-01-03",
                lastApprovedDate: "0001-01-01",
                lastApprovedUserId: "",
                allowanceBillingInfo: {
                  absMerchVendor: "000504",
                  absVendorName: "BAY VALLEY FOODS LLC",
                  absVendorPaymentType: "Deduction",
                  acPayableVendorNbr: "010029",
                  acReceivableVendorNbr: "142798",
                  billingContactName: "",
                  billingContactEmail: "",
                  vendorComment: "",
                  vendorOfferTrackingNbr: "",
                },
                allowanceBillingInfos: [
                  {
                    vendorIds: [
                      {
                        vendorNbr: "000504",
                        vendorSubAccount: "005",
                        distCenter: "WANC",
                        fullVendorNbr: "000504-005",
                      },
                    ],
                    absMerchVendor: "000504-005",
                    absVendorName: "BAY VALLEY FOODS LLC",
                    absVendorPaymentType: "D",
                    acPayableVendorNbr: "010029",
                    acReceivableVendorNbr: "142798",
                    billingContactName: "AMY RASMUSSEN - VMI AN   ",
                    billingContactEmail: "<EMAIL>",
                    vendorBillingList: [
                      {
                        billingContactName: "AMY RASMUSSEN - VMI AN   ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "MARK WASHFORD            ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "SHERYL MIELKE TRADE PROMO",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "TINA KECKHAVER - CSR     ",
                        billingContactEmail:
                          "<EMAIL>",
                      },
                      {
                        billingContactName: "AMY RASMUSSEN - VMI AN   ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "MARK WASHFORD            ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "SHERYL MIELKE TRADE PROMO",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "TINA KECKHAVER - CSR     ",
                        billingContactEmail:
                          "<EMAIL>",
                      },
                    ],
                  },
                ],
                storeGroups: [
                  {
                    storeGroupId: "6453d8fa31c0c4e30d8f11d3",
                    sourceStoreGroupId: "EDM",
                    storeGroupName: "Seattle All Stores",
                    storeGroupType: {
                      groupType: "S",
                      storeGrpTypeName: "Division",
                      groupInd: "D",
                    },
                    divisionIds: ["27"],
                    storeCount: 220,
                  },
                ],
                performance: {
                  allowanceCd: "T",
                  perfCode1: "20",
                  perfCode2: "52",
                  payType: "D",
                  allwPerfId: "63a3a12743a6cee87995b834",
                },
                planProductGroups: [
                  {
                    planProductGroupId: "636bde8d9665d0440e006e46",
                    sourceProductGroupId: 185046,
                    name: "Signature SELECT® Crackers - 84972",
                    divisionId: "27",
                    smicGroupCode: 2,
                    smicCategoryCode: "0202",
                    supplier: {
                      supplierId: "7877",
                      supplierName: "ABS-ASMs",
                    },
                    itemCount: 5,
                    displayInd: false,
                    simsVendors: ["000504"],
                  },
                ],
                allowanceItems: [
                  {
                    itemId: "2020024",
                    primaryUpc: "002113030601",
                    caseUpc: "1002113030601",
                    itemUpcs: ["002113030601", "1002113030601"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020028",
                    primaryUpc: "002113030602",
                    caseUpc: "1002113030602",
                    itemUpcs: ["002113030602", "1002113030602"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020342",
                    primaryUpc: "002113030605",
                    caseUpc: "1002113030605",
                    itemUpcs: ["002113030605", "1002113030605"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020466",
                    primaryUpc: "002113030654",
                    caseUpc: "1002113030654",
                    itemUpcs: ["002113030654", "1002113030654"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2022003",
                    primaryUpc: "002113019884",
                    caseUpc: "1002113019884",
                    itemUpcs: ["002113019884", "1002113019884"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                ],
                location: {
                  locationId: "639033d538196056762e6e28",
                  locationName: "27 - Seattle",
                  distCenter: "DDSE",
                  locationTypeCd: "D",
                  locationTypeCdEnum: "RETAIL_DIVISION",
                },
                headerFlatAmt: 0,
                createTs: 1689831367045,
                createUserId: "PJAIN03",
                lastUpdTs: 1689831367045,
                lastUpdUserId: "PJAIN03",
                costAreaDesc: "",
                leadDistributorInd: true,
                includeInd: true,
              },
            ],
            createUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:07.044Z",
            },
            updateUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:07.045Z",
            },
            allowanceChangeStatus: "CREATED",
          },
        ],
        promotionsList: [
          {
            id: "64b8c7efd6894d235b422e99",
            promotionId: 10005194,
            eventId: "64b8c73b79b9016c8e9b4d00",
            customerGroup: "ALL",
            vehicle: {
              id: "63d4d1ec705a29397068dc5b",
              vehicleNm: "27 Week 41 Insert 2023",
              sourceVehicleSk: 42991,
              vehicleTypNm: "insrt",
              startDate: "2023-10-11",
              endDate: "2023-10-17",
              vehicleType: {
                vehicleTypeId: "636abba1b426ee543a94d3ac",
                sourceVehicleTypeSk: 198,
                vehicleTypNm: "insrt",
                vehicleTypDesc: "Weekly Insert",
              },
            },
            promoStartDate: "2023-10-11",
            promoEndDate: "2023-10-17",
            promoDetails: {
              itemLimit: 1,
              minQuantity: 2,
              factor: 0,
              promotionType: "BUY_ONE_GET_ONE",
              regularPrice: 3,
              listCost: 2.5,
              listAgp: 20,
            },
            createUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:47.321Z",
            },
            updateUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:47.321Z",
            },
            promotionChangeStatus: "CREATE_PROMO",
            divisionIds: ["27"],
            promotionWorkflowStatus: "Pending With Vendor",
          },
        ],
        allowances: ["64b8c7c7bb19ce1061d9ca76"],
        promotions: ["64b8c7efd6894d235b422e99"],
        createUser: {
          userId: "PJAIN03",
          name: "Prayas Jain (Contractor)",
          type: "Merchant",
          userRoles: ["az-meupp-nonprod-promointeditor"],
          createTs: "2023-07-20T05:33:47.941Z",
        },
        updateUser: {
          userId: "PJAIN03",
          name: "Prayas Jain (Contractor)",
          type: "Merchant",
          userRoles: ["az-meupp-nonprod-promointeditor"],
          createTs: "2023-07-20T05:36:51.497Z",
        },
        eventCreationVehicle: {
          vehicleId: "63d4d1ec705a29397068dc5b",
          vehicleNm: "27 Week 41 Insert 2023",
          sourceVehicleSk: 42991,
          startDate: "2023-10-11",
          endDate: "2023-10-17",
          vehicleType: {
            vehicleTypeId: "636abba1b426ee543a94d3ac",
            sourceVehicleTypeSk: 198,
            vehicleTypNm: "insrt",
            vehicleTypDesc: "Weekly Insert",
          },
        },
        planEventWorkFlowType: "NOT FOUND",
        eventTypeEnum: "DP",
      },
      module: "allowance",
      historyMapperKey: "offerallowances.allowances.allowanceItems.uom",
      searchId: 7008953,
      allowId: "700895301",
      planEvent: {
        id: "64b8c73b79b9016c8e9b4d00",
        planEventIdNbr: 10012582,
        name: "Signature SELECT® Crackers - 84972 - Seattle All uStores (220) - Weekly Insert - 27 Week 41 Insert 2023",
        divisionIds: ["27"],
        startDate: "2023-10-11",
        endDate: "2023-10-17",
        eventType: "DP",
        sourceEventType: "ECP",
        eventStatus: "Pending With Vendor",
        planProductGroups: [
          {
            planProductGroupId: "636bde8d9665d0440e006e46",
            sourceProductGroupId: 185046,
            name: "Signature SELECT® Crackers - 84972",
            divisionId: "27",
            smicGroupCode: 2,
            smicCategoryCode: "0202",
            supplier: {
              supplierId: "7877",
              supplierName: "ABS-ASMs",
            },
            itemCount: 5,
            displayInd: false,
            simsVendors: ["000504"],
          },
        ],
        pricing: [
          {
            planProductGroup: {
              planProductGroupId: "636bde8d9665d0440e006e46",
              sourceProductGroupId: 185046,
              name: "Signature SELECT® Crackers - 84972",
              divisionId: "27",
              smicGroupCode: 2,
              smicCategoryCode: "0202",
              supplier: {
                supplierId: "7877",
                supplierName: "ABS-ASMs",
              },
              itemCount: 5,
              displayInd: false,
              simsVendors: ["000504"],
            },
            quadrant: "",
            priceAmount: "",
            priceFactor: "0",
            priceLimitQty: "1",
            prcMtd: "",
            promoType: "BUY_ONE_GET_ONE",
          },
        ],
        storeGroups: [
          {
            storeGroupId: "6453d8fa31c0c4e30d8f11d3",
            sourceStoreGroupId: "EDM",
            storeGroupName: "Seattle All Stores",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Division",
              groupInd: "D",
            },
            divisionIds: ["27"],
            storeCount: 220,
          },
        ],
        offerAllowances: [
          {
            id: "64b8c7c7bb19ce1061d9ca76",
            offerNumber: 7008953,
            origOfferNumber: 0,
            planEvent: "64b8c73b79b9016c8e9b4d00",
            createInd: "TC",
            minAggrAllowanceStartDate: "2023-10-11",
            maxAggrAllowanceEndDate: "2023-10-17",
            divisionIds: ["27"],
            allowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca75",
                allowanceIdNbr: 700895301,
                vendorNbr: "",
                allowanceStatus: "Pending With Vendor",
                leadDistributorInfos: [],
                vehicle: {
                  vehicleId: "63d4d1ec705a29397068dc5b",
                  vehicleNm: "27 Week 41 Insert 2023",
                  sourceVehicleSk: 42991,
                  startDate: "2023-10-11",
                  endDate: "2023-10-17",
                  vehicleType: {
                    vehicleTypeId: "636abba1b426ee543a94d3ac",
                    sourceVehicleTypeSk: 198,
                    vehicleTypNm: "insrt",
                    vehicleTypDesc: "Weekly Insert",
                  },
                },
                defaultAllowanceDates: {
                  allowanceStartDate: "2023-10-11",
                  allowanceEndDate: "2023-10-17",
                  performStartDate: "2023-10-11",
                  performEndDate: "2023-10-17",
                  orderStartDate: "0001-01-01",
                  orderEndDate: "0001-01-01",
                  shipStartDate: "0001-01-01",
                  shipEndDate: "0001-01-01",
                  arrivalStartDate: "0001-01-01",
                  arrivalEndDate: "0001-01-01",
                  overrideInd: false,
                  notPreSaved: false,
                },
                allowanceStartDate: "2023-10-11",
                allowanceEndDate: "2023-10-17",
                performStartDate: "2023-10-11",
                performEndDate: "2023-10-17",
                orderStartDate: "0001-01-01",
                orderEndDate: "0001-01-01",
                shipStartDate: "0001-01-01",
                shipEndDate: "0001-01-01",
                arrivalStartDate: "0001-01-01",
                arrivalEndDate: "0001-01-01",
                cancelledTs: "0001-01-03",
                processTs: "0001-01-03",
                lastApprovedDate: "0001-01-01",
                lastApprovedUserId: "",
                allowanceBillingInfo: {
                  absMerchVendor: "000504",
                  absVendorName: "BAY VALLEY FOODS LLC",
                  absVendorPaymentType: "Deduction",
                  acPayableVendorNbr: "010029",
                  acReceivableVendorNbr: "142798",
                  billingContactName: "",
                  billingContactEmail: "",
                  vendorComment: "",
                  vendorOfferTrackingNbr: "",
                },
                allowanceBillingInfos: [
                  {
                    vendorIds: [
                      {
                        vendorNbr: "000504",
                        vendorSubAccount: "005",
                        distCenter: "WANC",
                        fullVendorNbr: "000504-005",
                      },
                    ],
                    absMerchVendor: "000504-005",
                    absVendorName: "BAY VALLEY FOODS LLC",
                    absVendorPaymentType: "D",
                    acPayableVendorNbr: "010029",
                    acReceivableVendorNbr: "142798",
                    billingContactName: "AMY RASMUSSEN - VMI AN   ",
                    billingContactEmail: "<EMAIL>",
                    vendorBillingList: [
                      {
                        billingContactName: "AMY RASMUSSEN - VMI AN   ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "MARK WASHFORD            ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "SHERYL MIELKE TRADE PROMO",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "TINA KECKHAVER - CSR     ",
                        billingContactEmail:
                          "<EMAIL>",
                      },
                      {
                        billingContactName: "AMY RASMUSSEN - VMI AN   ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "MARK WASHFORD            ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "SHERYL MIELKE TRADE PROMO",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "TINA KECKHAVER - CSR     ",
                        billingContactEmail:
                          "<EMAIL>",
                      },
                    ],
                  },
                ],
                storeGroups: [
                  {
                    storeGroupId: "6453d8fa31c0c4e30d8f11d3",
                    sourceStoreGroupId: "EDM",
                    storeGroupName: "Seattle All Stores",
                    storeGroupType: {
                      groupType: "S",
                      storeGrpTypeName: "Division",
                      groupInd: "D",
                    },
                    divisionIds: ["27"],
                    storeCount: 220,
                  },
                ],
                performance: {
                  allowanceCd: "T",
                  perfCode1: "20",
                  perfCode2: "52",
                  payType: "D",
                  allwPerfId: "63a3a12743a6cee87995b834",
                },
                planProductGroups: [
                  {
                    planProductGroupId: "636bde8d9665d0440e006e46",
                    sourceProductGroupId: 185046,
                    name: "Signature SELECT® Crackers - 84972",
                    divisionId: "27",
                    smicGroupCode: 2,
                    smicCategoryCode: "0202",
                    supplier: {
                      supplierId: "7877",
                      supplierName: "ABS-ASMs",
                    },
                    itemCount: 5,
                    displayInd: false,
                    simsVendors: ["000504"],
                  },
                ],
                allowanceItems: [
                  {
                    itemId: "2020024",
                    primaryUpc: "002113030601",
                    caseUpc: "1002113030601",
                    itemUpcs: ["002113030601", "1002113030601"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020028",
                    primaryUpc: "002113030602",
                    caseUpc: "1002113030602",
                    itemUpcs: ["002113030602", "1002113030602"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020342",
                    primaryUpc: "002113030605",
                    caseUpc: "1002113030605",
                    itemUpcs: ["002113030605", "1002113030605"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020466",
                    primaryUpc: "002113030654",
                    caseUpc: "1002113030654",
                    itemUpcs: ["002113030654", "1002113030654"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2022003",
                    primaryUpc: "002113019884",
                    caseUpc: "1002113019884",
                    itemUpcs: ["002113019884", "1002113019884"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                ],
                location: {
                  locationId: "639033d538196056762e6e28",
                  locationName: "27 - Seattle",
                  distCenter: "DDSE",
                  locationTypeCd: "D",
                  locationTypeCdEnum: "RETAIL_DIVISION",
                },
                headerFlatAmt: 0,
                createTs: 1689831367045,
                createUserId: "PJAIN03",
                lastUpdTs: 1689831367045,
                lastUpdUserId: "PJAIN03",
                costAreaDesc: "",
                leadDistributorInd: true,
                includeInd: true,
              },
            ],
            periscopeIds: ["891061"],
            createUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:07.044Z",
            },
            updateUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:07.045Z",
            },
            allowanceChangeStatus: "UPDATED",
            createTempWorkAllowanceId: "64b8c73cbb19ce1061d9ca74",
            offerId: "64b8c7c7bb19ce1061d9ca76",
          },
        ],
        promotionsList: [
          {
            id: "64b8c7efd6894d235b422e99",
            promotionId: 10005194,
            eventId: "64b8c73b79b9016c8e9b4d00",
            customerGroup: "ALL",
            vehicle: {
              id: "63d4d1ec705a29397068dc5b",
              vehicleNm: "27 Week 41 Insert 2023",
              sourceVehicleSk: 42991,
              vehicleTypNm: "insrt",
              startDate: "2023-10-11",
              endDate: "2023-10-17",
              vehicleType: {
                vehicleTypeId: "636abba1b426ee543a94d3ac",
                sourceVehicleTypeSk: 198,
                vehicleTypNm: "insrt",
                vehicleTypDesc: "Weekly Insert",
              },
            },
            promoStartDate: "2023-10-11",
            promoEndDate: "2023-10-17",
            promoDetails: {
              itemLimit: 1,
              minQuantity: 2,
              factor: 0,
              promotionType: "BUY_ONE_GET_ONE",
              regularPrice: 3,
              listCost: 2.5,
              listAgp: 20,
            },
            createUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:47.321Z",
            },
            updateUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:47.321Z",
            },
            promotionChangeStatus: "CREATE_PROMO",
            divisionIds: ["27"],
            periscopePromoId: "891061",
            forecast: {
              id: "64b8c80266078d00271c0082",
              quadrant: "2",
              forecastSales: 8679,
              forecastUnits: 3729,
              forecastAgp: 19.8,
              markdown: 6633,
              incrementalUnits: 1743,
              incrementalSales: 0,
              incrementalAgp: 0,
              vendorFunding: 0,
              coverage: 0,
              periscopePromotionId: "891061",
              promotionObjectId: "64b8c7efd6894d235b422e99",
              planEvent: "64b8c73b79b9016c8e9b4d00",
              lastUpdated: "2023-07-20T05:37:06.592Z",
              lastUpdateBy: "meupp-periscope-bk-azr",
            },
            promotionWorkflowStatus: "Pending With Vendor",
          },
        ],
        allowances: ["64b8c7c7bb19ce1061d9ca76"],
        promotions: ["64b8c7efd6894d235b422e99"],
        createUser: {
          userId: "PJAIN03",
          name: "Prayas Jain (Contractor)",
          type: "Merchant",
          userRoles: ["az-meupp-nonprod-promointeditor"],
          createTs: "2023-07-20T05:33:47.941Z",
        },
        updateUser: {
          userId: "PJAIN03",
          name: "Prayas Jain (Contractor)",
          type: "Merchant",
          userRoles: ["az-meupp-nonprod-promointeditor"],
          createTs: "2023-07-20T05:36:51.497Z",
        },
        eventCreationVehicle: {
          vehicleId: "63d4d1ec705a29397068dc5b",
          vehicleNm: "27 Week 41 Insert 2023",
          sourceVehicleSk: 42991,
          startDate: "2023-10-11",
          endDate: "2023-10-17",
          vehicleType: {
            vehicleTypeId: "636abba1b426ee543a94d3ac",
            sourceVehicleTypeSk: 198,
            vehicleTypNm: "insrt",
            vehicleTypDesc: "Weekly Insert",
          },
        },
        forecast: {
          quadrant: "2",
          forecastSales: 8679,
          forecastUnits: 3729,
        },
        planEventWorkFlowType: "Modify",
        eventTypeEnum: "DP",
      },
      getValues: mockGetValues,
      allowPromoStatus: "Pending With Vendor",
      eventStatus: "Pending With Vendor",
    };

    const expectedValue = getAllowAmtOrUom("1", true, fiedlObj, false);
    expect(expectedValue).toEqual("EA");
  });
  it("get uom if event status is draft", () => {
    const mockGetValues = jest.fn().mockImplementation((key = null) => {
      if (key) {
        return [
          {
            id: "64b8c7c7bb19ce1061d9ca76",
            offerNumber: 7008953,
            origOfferNumber: 0,
            planEvent: "64b8c73b79b9016c8e9b4d00",
            createInd: "TC",
            minAggrAllowanceStartDate: "2023-10-11",
            maxAggrAllowanceEndDate: "2023-10-17",
            divisionIds: ["27"],
            allowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca75",
                allowanceIdNbr: 700895301,
                vendorNbr: "",
                allowanceStatus: "Pending With Vendor",
                leadDistributorInfos: [],
                vehicle: {
                  vehicleId: "63d4d1ec705a29397068dc5b",
                  vehicleNm: "27 Week 41 Insert 2023",
                  sourceVehicleSk: 42991,
                  startDate: "2023-10-11",
                  endDate: "2023-10-17",
                  vehicleType: {
                    vehicleTypeId: "636abba1b426ee543a94d3ac",
                    sourceVehicleTypeSk: 198,
                    vehicleTypNm: "insrt",
                    vehicleTypDesc: "Weekly Insert",
                  },
                },
                allowanceStartDate: "2023-10-11",
                allowanceEndDate: "2023-10-17",
                performStartDate: "2023-10-11",
                performEndDate: "2023-10-17",
                orderStartDate: "0001-01-01",
                orderEndDate: "0001-01-01",
                shipStartDate: "0001-01-01",
                shipEndDate: "0001-01-01",
                arrivalStartDate: "0001-01-01",
                arrivalEndDate: "0001-01-01",
                cancelledTs: "0001-01-03",
                processTs: "0001-01-03",
                lastApprovedDate: "0001-01-01",
                lastApprovedUserId: "",
                allowanceBillingInfo: {
                  absMerchVendor: "000504",
                  absVendorName: "BAY VALLEY FOODS LLC",
                  absVendorPaymentType: "Deduction",
                  acPayableVendorNbr: "010029",
                  acReceivableVendorNbr: "142798",
                  billingContactName: "",
                  billingContactEmail: "",
                  vendorComment: "",
                  vendorOfferTrackingNbr: "",
                },
                performance: {
                  allowanceCd: "T",
                  perfCode1: "20",
                  perfCode2: "52",
                  payType: "D",
                  allwPerfId: "63a3a12743a6cee87995b834",
                },
                allowanceItems: [
                  {
                    itemId: "2020024",
                    primaryUpc: "002113030601",
                    caseUpc: "1002113030601",
                    itemUpcs: ["002113030601", "1002113030601"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020028",
                    primaryUpc: "002113030602",
                    caseUpc: "1002113030602",
                    itemUpcs: ["002113030602", "1002113030602"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020342",
                    primaryUpc: "002113030605",
                    caseUpc: "1002113030605",
                    itemUpcs: ["002113030605", "1002113030605"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020466",
                    primaryUpc: "002113030654",
                    caseUpc: "1002113030654",
                    itemUpcs: ["002113030654", "1002113030654"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2022003",
                    primaryUpc: "002113019884",
                    caseUpc: "1002113019884",
                    itemUpcs: ["002113019884", "1002113019884"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                ],
                location: {
                  locationId: "639033d538196056762e6e28",
                  locationName: "27 - Seattle",
                  distCenter: "DDSE",
                  locationTypeCd: "D",
                  locationTypeCdEnum: "RETAIL_DIVISION",
                },
                headerFlatAmt: 0,
                createTs: 1689831367045,
                createUserId: "PJAIN03",
                lastUpdTs: 1689831367045,
                lastUpdUserId: "PJAIN03",
                costAreaDesc: "",
                leadDistributorInd: true,
                includeInd: true,
              },
            ],
            periscopeIds: ["891061"],
            allowanceChangeStatus: "UPDATED",
            createTempWorkAllowanceId: "64b8c73cbb19ce1061d9ca74",
            offerId: "64b8c7c7bb19ce1061d9ca76",
          },
        ];
      } else {
        return {
          planEvent: {
            offerAllowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca76",
                offerNumber: 7008953,
                origOfferNumber: 0,
                planEvent: "64b8c73b79b9016c8e9b4d00",
                createInd: "TC",
                minAggrAllowanceStartDate: "2023-10-11",
                maxAggrAllowanceEndDate: "2023-10-17",
                divisionIds: ["27"],
                allowances: [
                  {
                    id: "64b8c7c7bb19ce1061d9ca75",
                    allowanceIdNbr: 700895301,
                    vendorNbr: "",
                    allowanceStatus: "Pending With Vendor",
                    leadDistributorInfos: [],
                    vehicle: {
                      vehicleId: "63d4d1ec705a29397068dc5b",
                      vehicleNm: "27 Week 41 Insert 2023",
                      sourceVehicleSk: 42991,
                      startDate: "2023-10-11",
                      endDate: "2023-10-17",
                      vehicleType: {
                        vehicleTypeId: "636abba1b426ee543a94d3ac",
                        sourceVehicleTypeSk: 198,
                        vehicleTypNm: "insrt",
                        vehicleTypDesc: "Weekly Insert",
                      },
                    },
                    defaultAllowanceDates: {
                      allowanceStartDate: "2023-10-11",
                      allowanceEndDate: "2023-10-17",
                      performStartDate: "2023-10-11",
                      performEndDate: "2023-10-17",
                      orderStartDate: "0001-01-01",
                      orderEndDate: "0001-01-01",
                      shipStartDate: "0001-01-01",
                      shipEndDate: "0001-01-01",
                      arrivalStartDate: "0001-01-01",
                      arrivalEndDate: "0001-01-01",
                      overrideInd: false,
                      notPreSaved: false,
                    },
                    allowanceStartDate: "2023-10-11",
                    allowanceEndDate: "2023-10-17",
                    performStartDate: "2023-10-11",
                    performEndDate: "2023-10-17",
                    orderStartDate: "0001-01-01",
                    orderEndDate: "0001-01-01",
                    shipStartDate: "0001-01-01",
                    shipEndDate: "0001-01-01",
                    arrivalStartDate: "0001-01-01",
                    arrivalEndDate: "0001-01-01",
                    cancelledTs: "0001-01-03",
                    processTs: "0001-01-03",
                    lastApprovedDate: "0001-01-01",
                    lastApprovedUserId: "",
                    allowanceBillingInfo: {
                      absMerchVendor: "000504",
                      absVendorName: "BAY VALLEY FOODS LLC",
                      absVendorPaymentType: "Deduction",
                      acPayableVendorNbr: "010029",
                      acReceivableVendorNbr: "142798",
                      billingContactName: "",
                      billingContactEmail: "",
                      vendorComment: "",
                      vendorOfferTrackingNbr: "",
                    },
                    allowanceBillingInfos: [
                      {
                        vendorIds: [
                          {
                            vendorNbr: "000504",
                            vendorSubAccount: "005",
                            distCenter: "WANC",
                            fullVendorNbr: "000504-005",
                          },
                        ],
                        absMerchVendor: "000504-005",
                        absVendorName: "BAY VALLEY FOODS LLC",
                        absVendorPaymentType: "D",
                        acPayableVendorNbr: "010029",
                        acReceivableVendorNbr: "142798",
                        billingContactName: "AMY RASMUSSEN - VMI AN   ",
                        billingContactEmail: "<EMAIL>",
                        vendorBillingList: [
                          {
                            billingContactName: "AMY RASMUSSEN - VMI AN   ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "MARK WASHFORD            ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "SHERYL MIELKE TRADE PROMO",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "TINA KECKHAVER - CSR     ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "AMY RASMUSSEN - VMI AN   ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "MARK WASHFORD            ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "SHERYL MIELKE TRADE PROMO",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "TINA KECKHAVER - CSR     ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                        ],
                      },
                    ],
                    storeGroups: [
                      {
                        storeGroupId: "6453d8fa31c0c4e30d8f11d3",
                        sourceStoreGroupId: "EDM",
                        storeGroupName: "Seattle All Stores",
                        storeGroupType: {
                          groupType: "S",
                          storeGrpTypeName: "Division",
                          groupInd: "D",
                        },
                        divisionIds: ["27"],
                        storeCount: 220,
                      },
                    ],
                    performance: {
                      allowanceCd: "T",
                      perfCode1: "20",
                      perfCode2: "52",
                      payType: "D",
                      allwPerfId: "63a3a12743a6cee87995b834",
                    },
                    planProductGroups: [
                      {
                        planProductGroupId: "636bde8d9665d0440e006e46",
                        sourceProductGroupId: 185046,
                        name: "Signature SELECT® Crackers - 84972",
                        divisionId: "27",
                        smicGroupCode: 2,
                        smicCategoryCode: "0202",
                        supplier: {
                          supplierId: "7877",
                          supplierName: "ABS-ASMs",
                        },
                        itemCount: 5,
                        displayInd: false,
                        simsVendors: ["000504"],
                      },
                    ],
                    allowanceItems: [
                      {
                        itemId: "2020024",
                        primaryUpc: "002113030601",
                        caseUpc: "1002113030601",
                        itemUpcs: ["002113030601", "1002113030601"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020028",
                        primaryUpc: "002113030602",
                        caseUpc: "1002113030602",
                        itemUpcs: ["002113030602", "1002113030602"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020342",
                        primaryUpc: "002113030605",
                        caseUpc: "1002113030605",
                        itemUpcs: ["002113030605", "1002113030605"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020466",
                        primaryUpc: "002113030654",
                        caseUpc: "1002113030654",
                        itemUpcs: ["002113030654", "1002113030654"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2022003",
                        primaryUpc: "002113019884",
                        caseUpc: "1002113019884",
                        itemUpcs: ["002113019884", "1002113019884"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                    ],
                    location: {
                      locationId: "639033d538196056762e6e28",
                      locationName: "27 - Seattle",
                      distCenter: "DDSE",
                      locationTypeCd: "D",
                      locationTypeCdEnum: "RETAIL_DIVISION",
                    },
                    headerFlatAmt: 0,
                    createTs: 1689831367045,
                    createUserId: "PJAIN03",
                    lastUpdTs: 1689831367045,
                    lastUpdUserId: "PJAIN03",
                    costAreaDesc: "",
                    leadDistributorInd: true,
                    includeInd: true,
                  },
                ],
                periscopeIds: ["891061"],
                createUser: {
                  userId: "PJAIN03",
                  name: "Prayas Jain (Contractor)",
                  type: "Merchant",
                  userRoles: ["az-meupp-nonprod-promointeditor"],
                  createTs: "2023-07-20T05:36:07.044Z",
                },
                updateUser: {
                  userId: "PJAIN03",
                  name: "Prayas Jain (Contractor)",
                  type: "Merchant",
                  userRoles: ["az-meupp-nonprod-promointeditor"],
                  createTs: "2023-07-20T05:36:07.045Z",
                },
                allowanceChangeStatus: "UPDATED",
                createTempWorkAllowanceId: "64b8c73cbb19ce1061d9ca74",
                offerId: "64b8c7c7bb19ce1061d9ca76",
              },
            ],
          },
          planEventPending: {
            offerAllowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca76",
                offerNumber: 7008953,
                origOfferNumber: 0,
                planEvent: "64b8c73b79b9016c8e9b4d00",
                createInd: "TC",
                minAggrAllowanceStartDate: "2023-10-11",
                maxAggrAllowanceEndDate: "2023-10-17",
                divisionIds: ["27"],
                allowances: [
                  {
                    id: "64b8c7c7bb19ce1061d9ca75",
                    allowanceIdNbr: 700895301,
                    vendorNbr: "",
                    allowanceStatus: "Pending With Vendor",
                    leadDistributorInfos: [],
                    vehicle: {
                      vehicleId: "63d4d1ec705a29397068dc5b",
                      vehicleNm: "27 Week 41 Insert 2023",
                      sourceVehicleSk: 42991,
                      startDate: "2023-10-11",
                      endDate: "2023-10-17",
                      vehicleType: {
                        vehicleTypeId: "636abba1b426ee543a94d3ac",
                        sourceVehicleTypeSk: 198,
                        vehicleTypNm: "insrt",
                        vehicleTypDesc: "Weekly Insert",
                      },
                    },
                    allowanceStartDate: "2023-10-11",
                    allowanceEndDate: "2023-10-17",
                    performStartDate: "2023-10-11",
                    performEndDate: "2023-10-17",
                    orderStartDate: "0001-01-01",
                    orderEndDate: "0001-01-01",
                    shipStartDate: "0001-01-01",
                    shipEndDate: "0001-01-01",
                    arrivalStartDate: "0001-01-01",
                    arrivalEndDate: "0001-01-01",
                    cancelledTs: "0001-01-03",
                    processTs: "0001-01-03",
                    lastApprovedDate: "0001-01-01",
                    lastApprovedUserId: "",
                    performance: {
                      allowanceCd: "T",
                      perfCode1: "20",
                      perfCode2: "52",
                      payType: "D",
                      allwPerfId: "63a3a12743a6cee87995b834",
                    },
                    allowanceItems: [
                      {
                        itemId: "2020024",
                        primaryUpc: "002113030601",
                        caseUpc: "1002113030601",
                        itemUpcs: ["002113030601", "1002113030601"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020028",
                        primaryUpc: "002113030602",
                        caseUpc: "1002113030602",
                        itemUpcs: ["002113030602", "1002113030602"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020342",
                        primaryUpc: "002113030605",
                        caseUpc: "1002113030605",
                        itemUpcs: ["002113030605", "1002113030605"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020466",
                        primaryUpc: "002113030654",
                        caseUpc: "1002113030654",
                        itemUpcs: ["002113030654", "1002113030654"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2022003",
                        primaryUpc: "002113019884",
                        caseUpc: "1002113019884",
                        itemUpcs: ["002113019884", "1002113019884"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                    ],
                    location: {
                      locationId: "639033d538196056762e6e28",
                      locationName: "27 - Seattle",
                      distCenter: "DDSE",
                      locationTypeCd: "D",
                      locationTypeCdEnum: "RETAIL_DIVISION",
                    },
                    headerFlatAmt: 0,
                    createTs: 1689831367045,
                    createUserId: "PJAIN03",
                    lastUpdTs: 1689831367045,
                    lastUpdUserId: "PJAIN03",
                    costAreaDesc: "",
                    leadDistributorInd: true,
                    includeInd: true,
                  },
                ],
                allowanceChangeStatus: "CREATED",
              },
            ],
          },
        };
      }
    });
    const fiedlObj = {
      planValue: "EA",
      indicatorValue: false,
      planPendingObj: {
        id: "64b8c73b79b9016c8e9b4d00",
        planEventIdNbr: 10012582,
        name: "Signature SELECT® Crackers - 84972 - Seattle All uStores (220) - Weekly Insert - 27 Week 41 Insert 2023",
        divisionIds: ["27"],
        startDate: "2023-10-11",
        endDate: "2023-10-17",
        eventType: "DP",
        sourceEventType: "ECP",
        allowId: "700895301",
        eventStatus: "Pending With Vendor",
        planProductGroups: [
          {
            planProductGroupId: "636bde8d9665d0440e006e46",
            sourceProductGroupId: 185046,
            name: "Signature SELECT® Crackers - 84972",
            divisionId: "27",
            smicGroupCode: 2,
            smicCategoryCode: "0202",
            supplier: {
              supplierId: "7877",
              supplierName: "ABS-ASMs",
            },
            itemCount: 5,
            displayInd: false,
            simsVendors: ["000504"],
          },
        ],
        pricing: [
          {
            planProductGroup: {
              planProductGroupId: "636bde8d9665d0440e006e46",
              sourceProductGroupId: 185046,
              name: "Signature SELECT® Crackers - 84972",
              divisionId: "27",
              smicGroupCode: 2,
              smicCategoryCode: "0202",
              supplier: {
                supplierId: "7877",
                supplierName: "ABS-ASMs",
              },
              itemCount: 5,
              displayInd: false,
              simsVendors: ["000504"],
            },
            quadrant: "",
            priceAmount: "",
            priceFactor: "0",
            priceLimitQty: "1",
            prcMtd: "",
            promoType: "BUY_ONE_GET_ONE",
          },
        ],
        storeGroups: [
          {
            storeGroupId: "6453d8fa31c0c4e30d8f11d3",
            sourceStoreGroupId: "EDM",
            storeGroupName: "Seattle All Stores",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Division",
              groupInd: "D",
            },
            divisionIds: ["27"],
            storeCount: 220,
          },
        ],
        offerAllowances: [
          {
            id: "64b8c7c7bb19ce1061d9ca76",
            offerNumber: 7008953,
            origOfferNumber: 0,
            planEvent: "64b8c73b79b9016c8e9b4d00",
            createInd: "TC",
            minAggrAllowanceStartDate: "2023-10-11",
            maxAggrAllowanceEndDate: "2023-10-17",
            divisionIds: ["27"],
            allowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca75",
                allowanceIdNbr: 700895301,
                vendorNbr: "",
                allowanceStatus: "Pending With Vendor",
                leadDistributorInfos: [],
                vehicle: {
                  vehicleId: "63d4d1ec705a29397068dc5b",
                  vehicleNm: "27 Week 41 Insert 2023",
                  sourceVehicleSk: 42991,
                  startDate: "2023-10-11",
                  endDate: "2023-10-17",
                  vehicleType: {
                    vehicleTypeId: "636abba1b426ee543a94d3ac",
                    sourceVehicleTypeSk: 198,
                    vehicleTypNm: "insrt",
                    vehicleTypDesc: "Weekly Insert",
                  },
                },
                defaultAllowanceDates: {
                  allowanceStartDate: "2023-10-11",
                  allowanceEndDate: "2023-10-17",
                  performStartDate: "2023-10-11",
                  performEndDate: "2023-10-17",
                  orderStartDate: "0001-01-01",
                  orderEndDate: "0001-01-01",
                  shipStartDate: "0001-01-01",
                  shipEndDate: "0001-01-01",
                  arrivalStartDate: "0001-01-01",
                  arrivalEndDate: "0001-01-01",
                  overrideInd: false,
                  notPreSaved: false,
                },
                allowanceStartDate: "2023-10-11",
                allowanceEndDate: "2023-10-17",
                performStartDate: "2023-10-11",
                performEndDate: "2023-10-17",
                orderStartDate: "0001-01-01",
                orderEndDate: "0001-01-01",
                shipStartDate: "0001-01-01",
                shipEndDate: "0001-01-01",
                arrivalStartDate: "0001-01-01",
                arrivalEndDate: "0001-01-01",
                cancelledTs: "0001-01-03",
                processTs: "0001-01-03",
                lastApprovedDate: "0001-01-01",
                lastApprovedUserId: "",
                allowanceBillingInfo: {
                  absMerchVendor: "000504",
                  absVendorName: "BAY VALLEY FOODS LLC",
                  absVendorPaymentType: "Deduction",
                  acPayableVendorNbr: "010029",
                  acReceivableVendorNbr: "142798",
                  billingContactName: "",
                  billingContactEmail: "",
                  vendorComment: "",
                  vendorOfferTrackingNbr: "",
                },
                allowanceBillingInfos: [
                  {
                    vendorIds: [
                      {
                        vendorNbr: "000504",
                        vendorSubAccount: "005",
                        distCenter: "WANC",
                        fullVendorNbr: "000504-005",
                      },
                    ],
                    absMerchVendor: "000504-005",
                    absVendorName: "BAY VALLEY FOODS LLC",
                    absVendorPaymentType: "D",
                    acPayableVendorNbr: "010029",
                    acReceivableVendorNbr: "142798",
                    billingContactName: "AMY RASMUSSEN - VMI AN   ",
                    billingContactEmail: "<EMAIL>",
                    vendorBillingList: [
                      {
                        billingContactName: "AMY RASMUSSEN - VMI AN   ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "MARK WASHFORD            ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "SHERYL MIELKE TRADE PROMO",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "TINA KECKHAVER - CSR     ",
                        billingContactEmail:
                          "<EMAIL>",
                      },
                      {
                        billingContactName: "AMY RASMUSSEN - VMI AN   ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "MARK WASHFORD            ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "SHERYL MIELKE TRADE PROMO",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "TINA KECKHAVER - CSR     ",
                        billingContactEmail:
                          "<EMAIL>",
                      },
                    ],
                  },
                ],
                storeGroups: [
                  {
                    storeGroupId: "6453d8fa31c0c4e30d8f11d3",
                    sourceStoreGroupId: "EDM",
                    storeGroupName: "Seattle All Stores",
                    storeGroupType: {
                      groupType: "S",
                      storeGrpTypeName: "Division",
                      groupInd: "D",
                    },
                    divisionIds: ["27"],
                    storeCount: 220,
                  },
                ],
                performance: {
                  allowanceCd: "T",
                  perfCode1: "20",
                  perfCode2: "52",
                  payType: "D",
                  allwPerfId: "63a3a12743a6cee87995b834",
                },
                planProductGroups: [
                  {
                    planProductGroupId: "636bde8d9665d0440e006e46",
                    sourceProductGroupId: 185046,
                    name: "Signature SELECT® Crackers - 84972",
                    divisionId: "27",
                    smicGroupCode: 2,
                    smicCategoryCode: "0202",
                    supplier: {
                      supplierId: "7877",
                      supplierName: "ABS-ASMs",
                    },
                    itemCount: 5,
                    displayInd: false,
                    simsVendors: ["000504"],
                  },
                ],
                allowanceItems: [
                  {
                    itemId: "2020024",
                    primaryUpc: "002113030601",
                    caseUpc: "1002113030601",
                    itemUpcs: ["002113030601", "1002113030601"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020028",
                    primaryUpc: "002113030602",
                    caseUpc: "1002113030602",
                    itemUpcs: ["002113030602", "1002113030602"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020342",
                    primaryUpc: "002113030605",
                    caseUpc: "1002113030605",
                    itemUpcs: ["002113030605", "1002113030605"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020466",
                    primaryUpc: "002113030654",
                    caseUpc: "1002113030654",
                    itemUpcs: ["002113030654", "1002113030654"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2022003",
                    primaryUpc: "002113019884",
                    caseUpc: "1002113019884",
                    itemUpcs: ["002113019884", "1002113019884"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                ],
                location: {
                  locationId: "639033d538196056762e6e28",
                  locationName: "27 - Seattle",
                  distCenter: "DDSE",
                  locationTypeCd: "D",
                  locationTypeCdEnum: "RETAIL_DIVISION",
                },
                headerFlatAmt: 0,
                createTs: 1689831367045,
                createUserId: "PJAIN03",
                lastUpdTs: 1689831367045,
                lastUpdUserId: "PJAIN03",
                costAreaDesc: "",
                leadDistributorInd: true,
                includeInd: true,
              },
            ],
            createUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:07.044Z",
            },
            updateUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:07.045Z",
            },
            allowanceChangeStatus: "CREATED",
          },
        ],
        promotionsList: [
          {
            id: "64b8c7efd6894d235b422e99",
            promotionId: 10005194,
            eventId: "64b8c73b79b9016c8e9b4d00",
            customerGroup: "ALL",
            vehicle: {
              id: "63d4d1ec705a29397068dc5b",
              vehicleNm: "27 Week 41 Insert 2023",
              sourceVehicleSk: 42991,
              vehicleTypNm: "insrt",
              startDate: "2023-10-11",
              endDate: "2023-10-17",
              vehicleType: {
                vehicleTypeId: "636abba1b426ee543a94d3ac",
                sourceVehicleTypeSk: 198,
                vehicleTypNm: "insrt",
                vehicleTypDesc: "Weekly Insert",
              },
            },
            promoStartDate: "2023-10-11",
            promoEndDate: "2023-10-17",
            promoDetails: {
              itemLimit: 1,
              minQuantity: 2,
              factor: 0,
              promotionType: "BUY_ONE_GET_ONE",
              regularPrice: 3,
              listCost: 2.5,
              listAgp: 20,
            },
            createUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:47.321Z",
            },
            updateUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:47.321Z",
            },
            promotionChangeStatus: "CREATE_PROMO",
            divisionIds: ["27"],
            promotionWorkflowStatus: "Pending With Vendor",
          },
        ],
        allowances: ["64b8c7c7bb19ce1061d9ca76"],
        promotions: ["64b8c7efd6894d235b422e99"],
        createUser: {
          userId: "PJAIN03",
          name: "Prayas Jain (Contractor)",
          type: "Merchant",
          userRoles: ["az-meupp-nonprod-promointeditor"],
          createTs: "2023-07-20T05:33:47.941Z",
        },
        updateUser: {
          userId: "PJAIN03",
          name: "Prayas Jain (Contractor)",
          type: "Merchant",
          userRoles: ["az-meupp-nonprod-promointeditor"],
          createTs: "2023-07-20T05:36:51.497Z",
        },
        eventCreationVehicle: {
          vehicleId: "63d4d1ec705a29397068dc5b",
          vehicleNm: "27 Week 41 Insert 2023",
          sourceVehicleSk: 42991,
          startDate: "2023-10-11",
          endDate: "2023-10-17",
          vehicleType: {
            vehicleTypeId: "636abba1b426ee543a94d3ac",
            sourceVehicleTypeSk: 198,
            vehicleTypNm: "insrt",
            vehicleTypDesc: "Weekly Insert",
          },
        },
        planEventWorkFlowType: "NOT FOUND",
        eventTypeEnum: "DP",
      },
      module: "allowance",
      historyMapperKey: "offerallowances.allowances.allowanceItems.uom",
      searchId: 7008953,
      allowId: "700895301",
      planEvent: {
        id: "64b8c73b79b9016c8e9b4d00",
        planEventIdNbr: 10012582,
        name: "Signature SELECT® Crackers - 84972 - Seattle All uStores (220) - Weekly Insert - 27 Week 41 Insert 2023",
        divisionIds: ["27"],
        startDate: "2023-10-11",
        endDate: "2023-10-17",
        eventType: "DP",
        sourceEventType: "ECP",
        eventStatus: "Pending With Vendor",
        planProductGroups: [
          {
            planProductGroupId: "636bde8d9665d0440e006e46",
            sourceProductGroupId: 185046,
            name: "Signature SELECT® Crackers - 84972",
            divisionId: "27",
            smicGroupCode: 2,
            smicCategoryCode: "0202",
            supplier: {
              supplierId: "7877",
              supplierName: "ABS-ASMs",
            },
            itemCount: 5,
            displayInd: false,
            simsVendors: ["000504"],
          },
        ],
        pricing: [
          {
            planProductGroup: {
              planProductGroupId: "636bde8d9665d0440e006e46",
              sourceProductGroupId: 185046,
              name: "Signature SELECT® Crackers - 84972",
              divisionId: "27",
              smicGroupCode: 2,
              smicCategoryCode: "0202",
              supplier: {
                supplierId: "7877",
                supplierName: "ABS-ASMs",
              },
              itemCount: 5,
              displayInd: false,
              simsVendors: ["000504"],
            },
            quadrant: "",
            priceAmount: "",
            priceFactor: "0",
            priceLimitQty: "1",
            prcMtd: "",
            promoType: "BUY_ONE_GET_ONE",
          },
        ],
        storeGroups: [
          {
            storeGroupId: "6453d8fa31c0c4e30d8f11d3",
            sourceStoreGroupId: "EDM",
            storeGroupName: "Seattle All Stores",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Division",
              groupInd: "D",
            },
            divisionIds: ["27"],
            storeCount: 220,
          },
        ],
        offerAllowances: [
          {
            id: "64b8c7c7bb19ce1061d9ca76",
            offerNumber: 7008953,
            origOfferNumber: 0,
            planEvent: "64b8c73b79b9016c8e9b4d00",
            createInd: "TC",
            minAggrAllowanceStartDate: "2023-10-11",
            maxAggrAllowanceEndDate: "2023-10-17",
            divisionIds: ["27"],
            allowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca75",
                allowanceIdNbr: 700895301,
                vendorNbr: "",
                allowanceStatus: "Pending With Vendor",
                leadDistributorInfos: [],
                vehicle: {
                  vehicleId: "63d4d1ec705a29397068dc5b",
                  vehicleNm: "27 Week 41 Insert 2023",
                  sourceVehicleSk: 42991,
                  startDate: "2023-10-11",
                  endDate: "2023-10-17",
                  vehicleType: {
                    vehicleTypeId: "636abba1b426ee543a94d3ac",
                    sourceVehicleTypeSk: 198,
                    vehicleTypNm: "insrt",
                    vehicleTypDesc: "Weekly Insert",
                  },
                },
                defaultAllowanceDates: {
                  allowanceStartDate: "2023-10-11",
                  allowanceEndDate: "2023-10-17",
                  performStartDate: "2023-10-11",
                  performEndDate: "2023-10-17",
                  orderStartDate: "0001-01-01",
                  orderEndDate: "0001-01-01",
                  shipStartDate: "0001-01-01",
                  shipEndDate: "0001-01-01",
                  arrivalStartDate: "0001-01-01",
                  arrivalEndDate: "0001-01-01",
                  overrideInd: false,
                  notPreSaved: false,
                },
                allowanceStartDate: "2023-10-11",
                allowanceEndDate: "2023-10-17",
                performStartDate: "2023-10-11",
                performEndDate: "2023-10-17",
                orderStartDate: "0001-01-01",
                orderEndDate: "0001-01-01",
                shipStartDate: "0001-01-01",
                shipEndDate: "0001-01-01",
                arrivalStartDate: "0001-01-01",
                arrivalEndDate: "0001-01-01",
                cancelledTs: "0001-01-03",
                processTs: "0001-01-03",
                lastApprovedDate: "0001-01-01",
                lastApprovedUserId: "",
                allowanceBillingInfo: {
                  absMerchVendor: "000504",
                  absVendorName: "BAY VALLEY FOODS LLC",
                  absVendorPaymentType: "Deduction",
                  acPayableVendorNbr: "010029",
                  acReceivableVendorNbr: "142798",
                  billingContactName: "",
                  billingContactEmail: "",
                  vendorComment: "",
                  vendorOfferTrackingNbr: "",
                },
                allowanceBillingInfos: [
                  {
                    vendorIds: [
                      {
                        vendorNbr: "000504",
                        vendorSubAccount: "005",
                        distCenter: "WANC",
                        fullVendorNbr: "000504-005",
                      },
                    ],
                    absMerchVendor: "000504-005",
                    absVendorName: "BAY VALLEY FOODS LLC",
                    absVendorPaymentType: "D",
                    acPayableVendorNbr: "010029",
                    acReceivableVendorNbr: "142798",
                    billingContactName: "AMY RASMUSSEN - VMI AN   ",
                    billingContactEmail: "<EMAIL>",
                    vendorBillingList: [
                      {
                        billingContactName: "AMY RASMUSSEN - VMI AN   ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "MARK WASHFORD            ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "SHERYL MIELKE TRADE PROMO",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "TINA KECKHAVER - CSR     ",
                        billingContactEmail:
                          "<EMAIL>",
                      },
                      {
                        billingContactName: "AMY RASMUSSEN - VMI AN   ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "MARK WASHFORD            ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "SHERYL MIELKE TRADE PROMO",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "TINA KECKHAVER - CSR     ",
                        billingContactEmail:
                          "<EMAIL>",
                      },
                    ],
                  },
                ],
                storeGroups: [
                  {
                    storeGroupId: "6453d8fa31c0c4e30d8f11d3",
                    sourceStoreGroupId: "EDM",
                    storeGroupName: "Seattle All Stores",
                    storeGroupType: {
                      groupType: "S",
                      storeGrpTypeName: "Division",
                      groupInd: "D",
                    },
                    divisionIds: ["27"],
                    storeCount: 220,
                  },
                ],
                performance: {
                  allowanceCd: "T",
                  perfCode1: "20",
                  perfCode2: "52",
                  payType: "D",
                  allwPerfId: "63a3a12743a6cee87995b834",
                },
                planProductGroups: [
                  {
                    planProductGroupId: "636bde8d9665d0440e006e46",
                    sourceProductGroupId: 185046,
                    name: "Signature SELECT® Crackers - 84972",
                    divisionId: "27",
                    smicGroupCode: 2,
                    smicCategoryCode: "0202",
                    supplier: {
                      supplierId: "7877",
                      supplierName: "ABS-ASMs",
                    },
                    itemCount: 5,
                    displayInd: false,
                    simsVendors: ["000504"],
                  },
                ],
                allowanceItems: [
                  {
                    itemId: "2020024",
                    primaryUpc: "002113030601",
                    caseUpc: "1002113030601",
                    itemUpcs: ["002113030601", "1002113030601"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020028",
                    primaryUpc: "002113030602",
                    caseUpc: "1002113030602",
                    itemUpcs: ["002113030602", "1002113030602"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020342",
                    primaryUpc: "002113030605",
                    caseUpc: "1002113030605",
                    itemUpcs: ["002113030605", "1002113030605"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020466",
                    primaryUpc: "002113030654",
                    caseUpc: "1002113030654",
                    itemUpcs: ["002113030654", "1002113030654"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2022003",
                    primaryUpc: "002113019884",
                    caseUpc: "1002113019884",
                    itemUpcs: ["002113019884", "1002113019884"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                ],
                location: {
                  locationId: "639033d538196056762e6e28",
                  locationName: "27 - Seattle",
                  distCenter: "DDSE",
                  locationTypeCd: "D",
                  locationTypeCdEnum: "RETAIL_DIVISION",
                },
                headerFlatAmt: 0,
                createTs: 1689831367045,
                createUserId: "PJAIN03",
                lastUpdTs: 1689831367045,
                lastUpdUserId: "PJAIN03",
                costAreaDesc: "",
                leadDistributorInd: true,
                includeInd: true,
              },
            ],
            periscopeIds: ["891061"],
            createUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:07.044Z",
            },
            updateUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:07.045Z",
            },
            allowanceChangeStatus: "UPDATED",
            createTempWorkAllowanceId: "64b8c73cbb19ce1061d9ca74",
            offerId: "64b8c7c7bb19ce1061d9ca76",
          },
        ],
        promotionsList: [
          {
            id: "64b8c7efd6894d235b422e99",
            promotionId: 10005194,
            eventId: "64b8c73b79b9016c8e9b4d00",
            customerGroup: "ALL",
            vehicle: {
              id: "63d4d1ec705a29397068dc5b",
              vehicleNm: "27 Week 41 Insert 2023",
              sourceVehicleSk: 42991,
              vehicleTypNm: "insrt",
              startDate: "2023-10-11",
              endDate: "2023-10-17",
              vehicleType: {
                vehicleTypeId: "636abba1b426ee543a94d3ac",
                sourceVehicleTypeSk: 198,
                vehicleTypNm: "insrt",
                vehicleTypDesc: "Weekly Insert",
              },
            },
            promoStartDate: "2023-10-11",
            promoEndDate: "2023-10-17",
            promoDetails: {
              itemLimit: 1,
              minQuantity: 2,
              factor: 0,
              promotionType: "BUY_ONE_GET_ONE",
              regularPrice: 3,
              listCost: 2.5,
              listAgp: 20,
            },
            createUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:47.321Z",
            },
            updateUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:47.321Z",
            },
            promotionChangeStatus: "CREATE_PROMO",
            divisionIds: ["27"],
            periscopePromoId: "891061",
            forecast: {
              id: "64b8c80266078d00271c0082",
              quadrant: "2",
              forecastSales: 8679,
              forecastUnits: 3729,
              forecastAgp: 19.8,
              markdown: 6633,
              incrementalUnits: 1743,
              incrementalSales: 0,
              incrementalAgp: 0,
              vendorFunding: 0,
              coverage: 0,
              periscopePromotionId: "891061",
              promotionObjectId: "64b8c7efd6894d235b422e99",
              planEvent: "64b8c73b79b9016c8e9b4d00",
              lastUpdated: "2023-07-20T05:37:06.592Z",
              lastUpdateBy: "meupp-periscope-bk-azr",
            },
            promotionWorkflowStatus: "Pending With Vendor",
          },
        ],
        allowances: ["64b8c7c7bb19ce1061d9ca76"],
        promotions: ["64b8c7efd6894d235b422e99"],
        createUser: {
          userId: "PJAIN03",
          name: "Prayas Jain (Contractor)",
          type: "Merchant",
          userRoles: ["az-meupp-nonprod-promointeditor"],
          createTs: "2023-07-20T05:33:47.941Z",
        },
        updateUser: {
          userId: "PJAIN03",
          name: "Prayas Jain (Contractor)",
          type: "Merchant",
          userRoles: ["az-meupp-nonprod-promointeditor"],
          createTs: "2023-07-20T05:36:51.497Z",
        },
        eventCreationVehicle: {
          vehicleId: "63d4d1ec705a29397068dc5b",
          vehicleNm: "27 Week 41 Insert 2023",
          sourceVehicleSk: 42991,
          startDate: "2023-10-11",
          endDate: "2023-10-17",
          vehicleType: {
            vehicleTypeId: "636abba1b426ee543a94d3ac",
            sourceVehicleTypeSk: 198,
            vehicleTypNm: "insrt",
            vehicleTypDesc: "Weekly Insert",
          },
        },
        forecast: {
          quadrant: "2",
          forecastSales: 8679,
          forecastUnits: 3729,
        },
        planEventWorkFlowType: "Modify",
        eventTypeEnum: "DP",
      },
      getValues: mockGetValues,
      allowPromoStatus: "Draft",
      eventStatus: "Draft",
    };

    const expectedValue = getAllowAmtOrUom("1", true, fiedlObj, false);
    expect(expectedValue).toEqual("EA");
  });
  it("get  uom if indicator is true", () => {
    const mockGetValues = jest.fn().mockImplementation((key = null) => {
      if (key) {
        return [
          {
            id: "64b8c7c7bb19ce1061d9ca76",
            offerNumber: 7008953,
            origOfferNumber: 0,
            planEvent: "64b8c73b79b9016c8e9b4d00",
            createInd: "TC",
            minAggrAllowanceStartDate: "2023-10-11",
            maxAggrAllowanceEndDate: "2023-10-17",
            divisionIds: ["27"],
            allowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca75",
                allowanceIdNbr: 700895301,
                vendorNbr: "",
                allowanceStatus: "Pending With Vendor",
                leadDistributorInfos: [],
                vehicle: {
                  vehicleId: "63d4d1ec705a29397068dc5b",
                  vehicleNm: "27 Week 41 Insert 2023",
                  sourceVehicleSk: 42991,
                  startDate: "2023-10-11",
                  endDate: "2023-10-17",
                  vehicleType: {
                    vehicleTypeId: "636abba1b426ee543a94d3ac",
                    sourceVehicleTypeSk: 198,
                    vehicleTypNm: "insrt",
                    vehicleTypDesc: "Weekly Insert",
                  },
                },
                allowanceStartDate: "2023-10-11",
                allowanceEndDate: "2023-10-17",
                performStartDate: "2023-10-11",
                performEndDate: "2023-10-17",
                orderStartDate: "0001-01-01",
                orderEndDate: "0001-01-01",
                shipStartDate: "0001-01-01",
                shipEndDate: "0001-01-01",
                arrivalStartDate: "0001-01-01",
                arrivalEndDate: "0001-01-01",
                cancelledTs: "0001-01-03",
                processTs: "0001-01-03",
                lastApprovedDate: "0001-01-01",
                lastApprovedUserId: "",
                allowanceBillingInfo: {
                  absMerchVendor: "000504",
                  absVendorName: "BAY VALLEY FOODS LLC",
                  absVendorPaymentType: "Deduction",
                  acPayableVendorNbr: "010029",
                  acReceivableVendorNbr: "142798",
                  billingContactName: "",
                  billingContactEmail: "",
                  vendorComment: "",
                  vendorOfferTrackingNbr: "",
                },
                performance: {
                  allowanceCd: "T",
                  perfCode1: "20",
                  perfCode2: "52",
                  payType: "D",
                  allwPerfId: "63a3a12743a6cee87995b834",
                },
                allowanceItems: [
                  {
                    itemId: "2020024",
                    primaryUpc: "002113030601",
                    caseUpc: "1002113030601",
                    itemUpcs: ["002113030601", "1002113030601"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020028",
                    primaryUpc: "002113030602",
                    caseUpc: "1002113030602",
                    itemUpcs: ["002113030602", "1002113030602"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020342",
                    primaryUpc: "002113030605",
                    caseUpc: "1002113030605",
                    itemUpcs: ["002113030605", "1002113030605"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020466",
                    primaryUpc: "002113030654",
                    caseUpc: "1002113030654",
                    itemUpcs: ["002113030654", "1002113030654"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2022003",
                    primaryUpc: "002113019884",
                    caseUpc: "1002113019884",
                    itemUpcs: ["002113019884", "1002113019884"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                ],
                location: {
                  locationId: "639033d538196056762e6e28",
                  locationName: "27 - Seattle",
                  distCenter: "DDSE",
                  locationTypeCd: "D",
                  locationTypeCdEnum: "RETAIL_DIVISION",
                },
                headerFlatAmt: 0,
                createTs: 1689831367045,
                createUserId: "PJAIN03",
                lastUpdTs: 1689831367045,
                lastUpdUserId: "PJAIN03",
                costAreaDesc: "",
                leadDistributorInd: true,
                includeInd: true,
              },
            ],
            periscopeIds: ["891061"],
            allowanceChangeStatus: "UPDATED",
            createTempWorkAllowanceId: "64b8c73cbb19ce1061d9ca74",
            offerId: "64b8c7c7bb19ce1061d9ca76",
          },
        ];
      } else {
        return {
          planEvent: {
            offerAllowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca76",
                offerNumber: 7008953,
                origOfferNumber: 0,
                planEvent: "64b8c73b79b9016c8e9b4d00",
                createInd: "TC",
                minAggrAllowanceStartDate: "2023-10-11",
                maxAggrAllowanceEndDate: "2023-10-17",
                divisionIds: ["27"],
                allowances: [
                  {
                    id: "64b8c7c7bb19ce1061d9ca75",
                    allowanceIdNbr: 700895301,
                    vendorNbr: "",
                    allowanceStatus: "Pending With Vendor",
                    leadDistributorInfos: [],
                    vehicle: {
                      vehicleId: "63d4d1ec705a29397068dc5b",
                      vehicleNm: "27 Week 41 Insert 2023",
                      sourceVehicleSk: 42991,
                      startDate: "2023-10-11",
                      endDate: "2023-10-17",
                      vehicleType: {
                        vehicleTypeId: "636abba1b426ee543a94d3ac",
                        sourceVehicleTypeSk: 198,
                        vehicleTypNm: "insrt",
                        vehicleTypDesc: "Weekly Insert",
                      },
                    },
                    defaultAllowanceDates: {
                      allowanceStartDate: "2023-10-11",
                      allowanceEndDate: "2023-10-17",
                      performStartDate: "2023-10-11",
                      performEndDate: "2023-10-17",
                      orderStartDate: "0001-01-01",
                      orderEndDate: "0001-01-01",
                      shipStartDate: "0001-01-01",
                      shipEndDate: "0001-01-01",
                      arrivalStartDate: "0001-01-01",
                      arrivalEndDate: "0001-01-01",
                      overrideInd: false,
                      notPreSaved: false,
                    },
                    allowanceStartDate: "2023-10-11",
                    allowanceEndDate: "2023-10-17",
                    performStartDate: "2023-10-11",
                    performEndDate: "2023-10-17",
                    orderStartDate: "0001-01-01",
                    orderEndDate: "0001-01-01",
                    shipStartDate: "0001-01-01",
                    shipEndDate: "0001-01-01",
                    arrivalStartDate: "0001-01-01",
                    arrivalEndDate: "0001-01-01",
                    cancelledTs: "0001-01-03",
                    processTs: "0001-01-03",
                    lastApprovedDate: "0001-01-01",
                    lastApprovedUserId: "",
                    allowanceBillingInfo: {
                      absMerchVendor: "000504",
                      absVendorName: "BAY VALLEY FOODS LLC",
                      absVendorPaymentType: "Deduction",
                      acPayableVendorNbr: "010029",
                      acReceivableVendorNbr: "142798",
                      billingContactName: "",
                      billingContactEmail: "",
                      vendorComment: "",
                      vendorOfferTrackingNbr: "",
                    },
                    allowanceBillingInfos: [
                      {
                        vendorIds: [
                          {
                            vendorNbr: "000504",
                            vendorSubAccount: "005",
                            distCenter: "WANC",
                            fullVendorNbr: "000504-005",
                          },
                        ],
                        absMerchVendor: "000504-005",
                        absVendorName: "BAY VALLEY FOODS LLC",
                        absVendorPaymentType: "D",
                        acPayableVendorNbr: "010029",
                        acReceivableVendorNbr: "142798",
                        billingContactName: "AMY RASMUSSEN - VMI AN   ",
                        billingContactEmail: "<EMAIL>",
                        vendorBillingList: [
                          {
                            billingContactName: "AMY RASMUSSEN - VMI AN   ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "MARK WASHFORD            ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "SHERYL MIELKE TRADE PROMO",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "TINA KECKHAVER - CSR     ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "AMY RASMUSSEN - VMI AN   ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "MARK WASHFORD            ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "SHERYL MIELKE TRADE PROMO",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "TINA KECKHAVER - CSR     ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                        ],
                      },
                    ],
                    storeGroups: [
                      {
                        storeGroupId: "6453d8fa31c0c4e30d8f11d3",
                        sourceStoreGroupId: "EDM",
                        storeGroupName: "Seattle All Stores",
                        storeGroupType: {
                          groupType: "S",
                          storeGrpTypeName: "Division",
                          groupInd: "D",
                        },
                        divisionIds: ["27"],
                        storeCount: 220,
                      },
                    ],
                    performance: {
                      allowanceCd: "T",
                      perfCode1: "20",
                      perfCode2: "52",
                      payType: "D",
                      allwPerfId: "63a3a12743a6cee87995b834",
                    },
                    planProductGroups: [
                      {
                        planProductGroupId: "636bde8d9665d0440e006e46",
                        sourceProductGroupId: 185046,
                        name: "Signature SELECT® Crackers - 84972",
                        divisionId: "27",
                        smicGroupCode: 2,
                        smicCategoryCode: "0202",
                        supplier: {
                          supplierId: "7877",
                          supplierName: "ABS-ASMs",
                        },
                        itemCount: 5,
                        displayInd: false,
                        simsVendors: ["000504"],
                      },
                    ],
                    allowanceItems: [
                      {
                        itemId: "2020024",
                        primaryUpc: "002113030601",
                        caseUpc: "1002113030601",
                        itemUpcs: ["002113030601", "1002113030601"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020028",
                        primaryUpc: "002113030602",
                        caseUpc: "1002113030602",
                        itemUpcs: ["002113030602", "1002113030602"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020342",
                        primaryUpc: "002113030605",
                        caseUpc: "1002113030605",
                        itemUpcs: ["002113030605", "1002113030605"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020466",
                        primaryUpc: "002113030654",
                        caseUpc: "1002113030654",
                        itemUpcs: ["002113030654", "1002113030654"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2022003",
                        primaryUpc: "002113019884",
                        caseUpc: "1002113019884",
                        itemUpcs: ["002113019884", "1002113019884"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                    ],
                    location: {
                      locationId: "639033d538196056762e6e28",
                      locationName: "27 - Seattle",
                      distCenter: "DDSE",
                      locationTypeCd: "D",
                      locationTypeCdEnum: "RETAIL_DIVISION",
                    },
                    headerFlatAmt: 0,
                    createTs: 1689831367045,
                    createUserId: "PJAIN03",
                    lastUpdTs: 1689831367045,
                    lastUpdUserId: "PJAIN03",
                    costAreaDesc: "",
                    leadDistributorInd: true,
                    includeInd: true,
                  },
                ],
                periscopeIds: ["891061"],
                createUser: {
                  userId: "PJAIN03",
                  name: "Prayas Jain (Contractor)",
                  type: "Merchant",
                  userRoles: ["az-meupp-nonprod-promointeditor"],
                  createTs: "2023-07-20T05:36:07.044Z",
                },
                updateUser: {
                  userId: "PJAIN03",
                  name: "Prayas Jain (Contractor)",
                  type: "Merchant",
                  userRoles: ["az-meupp-nonprod-promointeditor"],
                  createTs: "2023-07-20T05:36:07.045Z",
                },
                allowanceChangeStatus: "UPDATED",
                createTempWorkAllowanceId: "64b8c73cbb19ce1061d9ca74",
                offerId: "64b8c7c7bb19ce1061d9ca76",
              },
            ],
          },
          planEventPending: {
            offerAllowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca76",
                offerNumber: 7008953,
                origOfferNumber: 0,
                planEvent: "64b8c73b79b9016c8e9b4d00",
                createInd: "TC",
                minAggrAllowanceStartDate: "2023-10-11",
                maxAggrAllowanceEndDate: "2023-10-17",
                divisionIds: ["27"],
                allowances: [
                  {
                    id: "64b8c7c7bb19ce1061d9ca75",
                    allowanceIdNbr: 700895301,
                    vendorNbr: "",
                    allowanceStatus: "Pending With Vendor",
                    leadDistributorInfos: [],
                    vehicle: {
                      vehicleId: "63d4d1ec705a29397068dc5b",
                      vehicleNm: "27 Week 41 Insert 2023",
                      sourceVehicleSk: 42991,
                      startDate: "2023-10-11",
                      endDate: "2023-10-17",
                      vehicleType: {
                        vehicleTypeId: "636abba1b426ee543a94d3ac",
                        sourceVehicleTypeSk: 198,
                        vehicleTypNm: "insrt",
                        vehicleTypDesc: "Weekly Insert",
                      },
                    },
                    allowanceStartDate: "2023-10-11",
                    allowanceEndDate: "2023-10-17",
                    performStartDate: "2023-10-11",
                    performEndDate: "2023-10-17",
                    orderStartDate: "0001-01-01",
                    orderEndDate: "0001-01-01",
                    shipStartDate: "0001-01-01",
                    shipEndDate: "0001-01-01",
                    arrivalStartDate: "0001-01-01",
                    arrivalEndDate: "0001-01-01",
                    cancelledTs: "0001-01-03",
                    processTs: "0001-01-03",
                    lastApprovedDate: "0001-01-01",
                    lastApprovedUserId: "",
                    performance: {
                      allowanceCd: "T",
                      perfCode1: "20",
                      perfCode2: "52",
                      payType: "D",
                      allwPerfId: "63a3a12743a6cee87995b834",
                    },
                    allowanceItems: [
                      {
                        itemId: "2020024",
                        primaryUpc: "002113030601",
                        caseUpc: "1002113030601",
                        itemUpcs: ["002113030601", "1002113030601"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020028",
                        primaryUpc: "002113030602",
                        caseUpc: "1002113030602",
                        itemUpcs: ["002113030602", "1002113030602"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020342",
                        primaryUpc: "002113030605",
                        caseUpc: "1002113030605",
                        itemUpcs: ["002113030605", "1002113030605"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020466",
                        primaryUpc: "002113030654",
                        caseUpc: "1002113030654",
                        itemUpcs: ["002113030654", "1002113030654"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2022003",
                        primaryUpc: "002113019884",
                        caseUpc: "1002113019884",
                        itemUpcs: ["002113019884", "1002113019884"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                    ],
                    location: {
                      locationId: "639033d538196056762e6e28",
                      locationName: "27 - Seattle",
                      distCenter: "DDSE",
                      locationTypeCd: "D",
                      locationTypeCdEnum: "RETAIL_DIVISION",
                    },
                    headerFlatAmt: 0,
                    createTs: 1689831367045,
                    createUserId: "PJAIN03",
                    lastUpdTs: 1689831367045,
                    lastUpdUserId: "PJAIN03",
                    costAreaDesc: "",
                    leadDistributorInd: true,
                    includeInd: true,
                  },
                ],
                allowanceChangeStatus: "CREATED",
              },
            ],
          },
        };
      }
    });
    const fiedlObj = {
      planValue: "EA",
      indicatorValue: true,
      planPendingObj: {
        id: "64b8c73b79b9016c8e9b4d00",
        planEventIdNbr: 10012582,
        name: "Signature SELECT® Crackers - 84972 - Seattle All uStores (220) - Weekly Insert - 27 Week 41 Insert 2023",
        divisionIds: ["27"],
        startDate: "2023-10-11",
        endDate: "2023-10-17",
        eventType: "DP",
        sourceEventType: "ECP",
        eventStatus: "Pending With Vendor",
        planProductGroups: [
          {
            planProductGroupId: "636bde8d9665d0440e006e46",
            sourceProductGroupId: 185046,
            name: "Signature SELECT® Crackers - 84972",
            divisionId: "27",
            smicGroupCode: 2,
            smicCategoryCode: "0202",
            supplier: {
              supplierId: "7877",
              supplierName: "ABS-ASMs",
            },
            itemCount: 5,
            displayInd: false,
            simsVendors: ["000504"],
          },
        ],
        pricing: [
          {
            planProductGroup: {
              planProductGroupId: "636bde8d9665d0440e006e46",
              sourceProductGroupId: 185046,
              name: "Signature SELECT® Crackers - 84972",
              divisionId: "27",
              smicGroupCode: 2,
              smicCategoryCode: "0202",
              supplier: {
                supplierId: "7877",
                supplierName: "ABS-ASMs",
              },
              itemCount: 5,
              displayInd: false,
              simsVendors: ["000504"],
            },
            quadrant: "",
            priceAmount: "",
            priceFactor: "0",
            priceLimitQty: "1",
            prcMtd: "",
            promoType: "BUY_ONE_GET_ONE",
          },
        ],
        storeGroups: [
          {
            storeGroupId: "6453d8fa31c0c4e30d8f11d3",
            sourceStoreGroupId: "EDM",
            storeGroupName: "Seattle All Stores",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Division",
              groupInd: "D",
            },
            divisionIds: ["27"],
            storeCount: 220,
          },
        ],
        offerAllowances: [
          {
            id: "64b8c7c7bb19ce1061d9ca76",
            offerNumber: 7008953,
            origOfferNumber: 0,
            planEvent: "64b8c73b79b9016c8e9b4d00",
            createInd: "TC",
            minAggrAllowanceStartDate: "2023-10-11",
            maxAggrAllowanceEndDate: "2023-10-17",
            divisionIds: ["27"],
            allowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca75",
                allowanceIdNbr: 700895301,
                vendorNbr: "",
                allowanceStatus: "Pending With Vendor",
                leadDistributorInfos: [],
                vehicle: {
                  vehicleId: "63d4d1ec705a29397068dc5b",
                  vehicleNm: "27 Week 41 Insert 2023",
                  sourceVehicleSk: 42991,
                  startDate: "2023-10-11",
                  endDate: "2023-10-17",
                  vehicleType: {
                    vehicleTypeId: "636abba1b426ee543a94d3ac",
                    sourceVehicleTypeSk: 198,
                    vehicleTypNm: "insrt",
                    vehicleTypDesc: "Weekly Insert",
                  },
                },
                defaultAllowanceDates: {
                  allowanceStartDate: "2023-10-11",
                  allowanceEndDate: "2023-10-17",
                  performStartDate: "2023-10-11",
                  performEndDate: "2023-10-17",
                  orderStartDate: "0001-01-01",
                  orderEndDate: "0001-01-01",
                  shipStartDate: "0001-01-01",
                  shipEndDate: "0001-01-01",
                  arrivalStartDate: "0001-01-01",
                  arrivalEndDate: "0001-01-01",
                  overrideInd: false,
                  notPreSaved: false,
                },
                allowanceStartDate: "2023-10-11",
                allowanceEndDate: "2023-10-17",
                performStartDate: "2023-10-11",
                performEndDate: "2023-10-17",
                orderStartDate: "0001-01-01",
                orderEndDate: "0001-01-01",
                shipStartDate: "0001-01-01",
                shipEndDate: "0001-01-01",
                arrivalStartDate: "0001-01-01",
                arrivalEndDate: "0001-01-01",
                cancelledTs: "0001-01-03",
                processTs: "0001-01-03",
                lastApprovedDate: "0001-01-01",
                lastApprovedUserId: "",
                allowanceBillingInfo: {
                  absMerchVendor: "000504",
                  absVendorName: "BAY VALLEY FOODS LLC",
                  absVendorPaymentType: "Deduction",
                  acPayableVendorNbr: "010029",
                  acReceivableVendorNbr: "142798",
                  billingContactName: "",
                  billingContactEmail: "",
                  vendorComment: "",
                  vendorOfferTrackingNbr: "",
                },
                allowanceBillingInfos: [
                  {
                    vendorIds: [
                      {
                        vendorNbr: "000504",
                        vendorSubAccount: "005",
                        distCenter: "WANC",
                        fullVendorNbr: "000504-005",
                      },
                    ],
                    absMerchVendor: "000504-005",
                    absVendorName: "BAY VALLEY FOODS LLC",
                    absVendorPaymentType: "D",
                    acPayableVendorNbr: "010029",
                    acReceivableVendorNbr: "142798",
                    billingContactName: "AMY RASMUSSEN - VMI AN   ",
                    billingContactEmail: "<EMAIL>",
                    vendorBillingList: [
                      {
                        billingContactName: "AMY RASMUSSEN - VMI AN   ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "MARK WASHFORD            ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "SHERYL MIELKE TRADE PROMO",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "TINA KECKHAVER - CSR     ",
                        billingContactEmail:
                          "<EMAIL>",
                      },
                      {
                        billingContactName: "AMY RASMUSSEN - VMI AN   ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "MARK WASHFORD            ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "SHERYL MIELKE TRADE PROMO",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "TINA KECKHAVER - CSR     ",
                        billingContactEmail:
                          "<EMAIL>",
                      },
                    ],
                  },
                ],
                storeGroups: [
                  {
                    storeGroupId: "6453d8fa31c0c4e30d8f11d3",
                    sourceStoreGroupId: "EDM",
                    storeGroupName: "Seattle All Stores",
                    storeGroupType: {
                      groupType: "S",
                      storeGrpTypeName: "Division",
                      groupInd: "D",
                    },
                    divisionIds: ["27"],
                    storeCount: 220,
                  },
                ],
                performance: {
                  allowanceCd: "T",
                  perfCode1: "20",
                  perfCode2: "52",
                  payType: "D",
                  allwPerfId: "63a3a12743a6cee87995b834",
                },
                planProductGroups: [
                  {
                    planProductGroupId: "636bde8d9665d0440e006e46",
                    sourceProductGroupId: 185046,
                    name: "Signature SELECT® Crackers - 84972",
                    divisionId: "27",
                    smicGroupCode: 2,
                    smicCategoryCode: "0202",
                    supplier: {
                      supplierId: "7877",
                      supplierName: "ABS-ASMs",
                    },
                    itemCount: 5,
                    displayInd: false,
                    simsVendors: ["000504"],
                  },
                ],
                allowanceItems: [
                  {
                    itemId: "2020024",
                    primaryUpc: "002113030601",
                    caseUpc: "1002113030601",
                    itemUpcs: ["002113030601", "1002113030601"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020028",
                    primaryUpc: "002113030602",
                    caseUpc: "1002113030602",
                    itemUpcs: ["002113030602", "1002113030602"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020342",
                    primaryUpc: "002113030605",
                    caseUpc: "1002113030605",
                    itemUpcs: ["002113030605", "1002113030605"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020466",
                    primaryUpc: "002113030654",
                    caseUpc: "1002113030654",
                    itemUpcs: ["002113030654", "1002113030654"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2022003",
                    primaryUpc: "002113019884",
                    caseUpc: "1002113019884",
                    itemUpcs: ["002113019884", "1002113019884"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                ],
                location: {
                  locationId: "639033d538196056762e6e28",
                  locationName: "27 - Seattle",
                  distCenter: "DDSE",
                  locationTypeCd: "D",
                  locationTypeCdEnum: "RETAIL_DIVISION",
                },
                headerFlatAmt: 0,
                createTs: 1689831367045,
                createUserId: "PJAIN03",
                lastUpdTs: 1689831367045,
                lastUpdUserId: "PJAIN03",
                costAreaDesc: "",
                leadDistributorInd: true,
                includeInd: true,
              },
            ],
            createUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:07.044Z",
            },
            updateUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:07.045Z",
            },
            allowanceChangeStatus: "CREATED",
          },
        ],
        promotionsList: [
          {
            id: "64b8c7efd6894d235b422e99",
            promotionId: 10005194,
            eventId: "64b8c73b79b9016c8e9b4d00",
            customerGroup: "ALL",
            vehicle: {
              id: "63d4d1ec705a29397068dc5b",
              vehicleNm: "27 Week 41 Insert 2023",
              sourceVehicleSk: 42991,
              vehicleTypNm: "insrt",
              startDate: "2023-10-11",
              endDate: "2023-10-17",
              vehicleType: {
                vehicleTypeId: "636abba1b426ee543a94d3ac",
                sourceVehicleTypeSk: 198,
                vehicleTypNm: "insrt",
                vehicleTypDesc: "Weekly Insert",
              },
            },
            promoStartDate: "2023-10-11",
            promoEndDate: "2023-10-17",
            promoDetails: {
              itemLimit: 1,
              minQuantity: 2,
              factor: 0,
              promotionType: "BUY_ONE_GET_ONE",
              regularPrice: 3,
              listCost: 2.5,
              listAgp: 20,
            },
            createUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:47.321Z",
            },
            updateUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:47.321Z",
            },
            promotionChangeStatus: "CREATE_PROMO",
            divisionIds: ["27"],
            promotionWorkflowStatus: "Pending With Vendor",
          },
        ],
        allowances: ["64b8c7c7bb19ce1061d9ca76"],
        promotions: ["64b8c7efd6894d235b422e99"],
        createUser: {
          userId: "PJAIN03",
          name: "Prayas Jain (Contractor)",
          type: "Merchant",
          userRoles: ["az-meupp-nonprod-promointeditor"],
          createTs: "2023-07-20T05:33:47.941Z",
        },
        updateUser: {
          userId: "PJAIN03",
          name: "Prayas Jain (Contractor)",
          type: "Merchant",
          userRoles: ["az-meupp-nonprod-promointeditor"],
          createTs: "2023-07-20T05:36:51.497Z",
        },
        eventCreationVehicle: {
          vehicleId: "63d4d1ec705a29397068dc5b",
          vehicleNm: "27 Week 41 Insert 2023",
          sourceVehicleSk: 42991,
          startDate: "2023-10-11",
          endDate: "2023-10-17",
          vehicleType: {
            vehicleTypeId: "636abba1b426ee543a94d3ac",
            sourceVehicleTypeSk: 198,
            vehicleTypNm: "insrt",
            vehicleTypDesc: "Weekly Insert",
          },
        },
        planEventWorkFlowType: "NOT FOUND",
        eventTypeEnum: "DP",
      },
      planEventPendingChanges: {
        id: "64b8fce8540fee7ade0dfcc8",
        eventId: "64b8c73b79b9016c8e9b4d00",
        eventIdNbr: 10012582,
        divisionIds: ["27"],
        createUser: {
          userId: "PJAIN03",
          name: "Prayas Jain (Contractor)",
          type: "Merchant",
          userRoles: ["az-meupp-nonprod-promointeditor"],
          createTs: "2023-07-20T05:33:47.941Z",
        },
        eventChanges: [
          {
            fieldPath: "/offerAllowances/0/allowanceChangeStatus",
            labelFieldName: "planevents.offerAllowances.allowanceChangeStatus",
            beforeVal: "UPDATED",
            afterVal: "CREATED",
          },
          {
            fieldPath: "/planEventWorkFlowType",
            labelFieldName: "planevents.planEventWorkFlowType",
            beforeVal: "Modify",
            afterVal: "NOT FOUND",
          },
        ],
        offerAllowanceChanges: [
          {
            offerNumber: 7008953,
            changes: [
              {
                historyGroupId: "700895301",
                fieldPath: "/allowances/0/allowanceStatus",
                labelFieldName: "offerallowances.allowances.allowanceItems.uom",
                beforeVal: "",
                afterVal: "EA",
              },
            ],
            nonDisplayChanges: [],
          },
        ],
        promotionsChanges: [],
      },
      module: "allowance",
      historyMapperKey: "offerallowances.allowances.allowanceItems.uom",
      allowId: "700895301",
      searchId: 7008953,
      planEvent: {
        id: "64b8c73b79b9016c8e9b4d00",
        planEventIdNbr: 10012582,
        name: "Signature SELECT® Crackers - 84972 - Seattle All uStores (220) - Weekly Insert - 27 Week 41 Insert 2023",
        divisionIds: ["27"],
        startDate: "2023-10-11",
        endDate: "2023-10-17",
        eventType: "DP",
        sourceEventType: "ECP",
        eventStatus: "Pending With Vendor",
        planProductGroups: [
          {
            planProductGroupId: "636bde8d9665d0440e006e46",
            sourceProductGroupId: 185046,
            name: "Signature SELECT® Crackers - 84972",
            divisionId: "27",
            smicGroupCode: 2,
            smicCategoryCode: "0202",
            supplier: {
              supplierId: "7877",
              supplierName: "ABS-ASMs",
            },
            itemCount: 5,
            displayInd: false,
            simsVendors: ["000504"],
          },
        ],
        pricing: [
          {
            planProductGroup: {
              planProductGroupId: "636bde8d9665d0440e006e46",
              sourceProductGroupId: 185046,
              name: "Signature SELECT® Crackers - 84972",
              divisionId: "27",
              smicGroupCode: 2,
              smicCategoryCode: "0202",
              supplier: {
                supplierId: "7877",
                supplierName: "ABS-ASMs",
              },
              itemCount: 5,
              displayInd: false,
              simsVendors: ["000504"],
            },
            quadrant: "",
            priceAmount: "",
            priceFactor: "0",
            priceLimitQty: "1",
            prcMtd: "",
            promoType: "BUY_ONE_GET_ONE",
          },
        ],
        storeGroups: [
          {
            storeGroupId: "6453d8fa31c0c4e30d8f11d3",
            sourceStoreGroupId: "EDM",
            storeGroupName: "Seattle All Stores",
            storeGroupType: {
              groupType: "S",
              storeGrpTypeName: "Division",
              groupInd: "D",
            },
            divisionIds: ["27"],
            storeCount: 220,
          },
        ],
        offerAllowances: [
          {
            id: "64b8c7c7bb19ce1061d9ca76",
            offerNumber: 7008953,
            origOfferNumber: 0,
            planEvent: "64b8c73b79b9016c8e9b4d00",
            createInd: "TC",
            minAggrAllowanceStartDate: "2023-10-11",
            maxAggrAllowanceEndDate: "2023-10-17",
            divisionIds: ["27"],
            allowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca75",
                allowanceIdNbr: 700895301,
                vendorNbr: "",
                allowanceStatus: "Pending With Vendor",
                leadDistributorInfos: [],
                vehicle: {
                  vehicleId: "63d4d1ec705a29397068dc5b",
                  vehicleNm: "27 Week 41 Insert 2023",
                  sourceVehicleSk: 42991,
                  startDate: "2023-10-11",
                  endDate: "2023-10-17",
                  vehicleType: {
                    vehicleTypeId: "636abba1b426ee543a94d3ac",
                    sourceVehicleTypeSk: 198,
                    vehicleTypNm: "insrt",
                    vehicleTypDesc: "Weekly Insert",
                  },
                },
                defaultAllowanceDates: {
                  allowanceStartDate: "2023-10-11",
                  allowanceEndDate: "2023-10-17",
                  performStartDate: "2023-10-11",
                  performEndDate: "2023-10-17",
                  orderStartDate: "0001-01-01",
                  orderEndDate: "0001-01-01",
                  shipStartDate: "0001-01-01",
                  shipEndDate: "0001-01-01",
                  arrivalStartDate: "0001-01-01",
                  arrivalEndDate: "0001-01-01",
                  overrideInd: false,
                  notPreSaved: false,
                },
                allowanceStartDate: "2023-10-11",
                allowanceEndDate: "2023-10-17",
                performStartDate: "2023-10-11",
                performEndDate: "2023-10-17",
                orderStartDate: "0001-01-01",
                orderEndDate: "0001-01-01",
                shipStartDate: "0001-01-01",
                shipEndDate: "0001-01-01",
                arrivalStartDate: "0001-01-01",
                arrivalEndDate: "0001-01-01",
                cancelledTs: "0001-01-03",
                processTs: "0001-01-03",
                lastApprovedDate: "0001-01-01",
                lastApprovedUserId: "",
                allowanceBillingInfo: {
                  absMerchVendor: "000504",
                  absVendorName: "BAY VALLEY FOODS LLC",
                  absVendorPaymentType: "Deduction",
                  acPayableVendorNbr: "010029",
                  acReceivableVendorNbr: "142798",
                  billingContactName: "",
                  billingContactEmail: "",
                  vendorComment: "",
                  vendorOfferTrackingNbr: "",
                },
                allowanceBillingInfos: [
                  {
                    vendorIds: [
                      {
                        vendorNbr: "000504",
                        vendorSubAccount: "005",
                        distCenter: "WANC",
                        fullVendorNbr: "000504-005",
                      },
                    ],
                    absMerchVendor: "000504-005",
                    absVendorName: "BAY VALLEY FOODS LLC",
                    absVendorPaymentType: "D",
                    acPayableVendorNbr: "010029",
                    acReceivableVendorNbr: "142798",
                    billingContactName: "AMY RASMUSSEN - VMI AN   ",
                    billingContactEmail: "<EMAIL>",
                    vendorBillingList: [
                      {
                        billingContactName: "AMY RASMUSSEN - VMI AN   ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "MARK WASHFORD            ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "SHERYL MIELKE TRADE PROMO",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "TINA KECKHAVER - CSR     ",
                        billingContactEmail:
                          "<EMAIL>",
                      },
                      {
                        billingContactName: "AMY RASMUSSEN - VMI AN   ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "MARK WASHFORD            ",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "SHERYL MIELKE TRADE PROMO",
                        billingContactEmail: "<EMAIL>",
                      },
                      {
                        billingContactName: "TINA KECKHAVER - CSR     ",
                        billingContactEmail:
                          "<EMAIL>",
                      },
                    ],
                  },
                ],
                storeGroups: [
                  {
                    storeGroupId: "6453d8fa31c0c4e30d8f11d3",
                    sourceStoreGroupId: "EDM",
                    storeGroupName: "Seattle All Stores",
                    storeGroupType: {
                      groupType: "S",
                      storeGrpTypeName: "Division",
                      groupInd: "D",
                    },
                    divisionIds: ["27"],
                    storeCount: 220,
                  },
                ],
                performance: {
                  allowanceCd: "T",
                  perfCode1: "20",
                  perfCode2: "52",
                  payType: "D",
                  allwPerfId: "63a3a12743a6cee87995b834",
                },
                planProductGroups: [
                  {
                    planProductGroupId: "636bde8d9665d0440e006e46",
                    sourceProductGroupId: 185046,
                    name: "Signature SELECT® Crackers - 84972",
                    divisionId: "27",
                    smicGroupCode: 2,
                    smicCategoryCode: "0202",
                    supplier: {
                      supplierId: "7877",
                      supplierName: "ABS-ASMs",
                    },
                    itemCount: 5,
                    displayInd: false,
                    simsVendors: ["000504"],
                  },
                ],
                allowanceItems: [
                  {
                    itemId: "2020024",
                    primaryUpc: "002113030601",
                    caseUpc: "1002113030601",
                    itemUpcs: ["002113030601", "1002113030601"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020028",
                    primaryUpc: "002113030602",
                    caseUpc: "1002113030602",
                    itemUpcs: ["002113030602", "1002113030602"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020342",
                    primaryUpc: "002113030605",
                    caseUpc: "1002113030605",
                    itemUpcs: ["002113030605", "1002113030605"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020466",
                    primaryUpc: "002113030654",
                    caseUpc: "1002113030654",
                    itemUpcs: ["002113030654", "1002113030654"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2022003",
                    primaryUpc: "002113019884",
                    caseUpc: "1002113019884",
                    itemUpcs: ["002113019884", "1002113019884"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                ],
                location: {
                  locationId: "639033d538196056762e6e28",
                  locationName: "27 - Seattle",
                  distCenter: "DDSE",
                  locationTypeCd: "D",
                  locationTypeCdEnum: "RETAIL_DIVISION",
                },
                headerFlatAmt: 0,
                createTs: 1689831367045,
                createUserId: "PJAIN03",
                lastUpdTs: 1689831367045,
                lastUpdUserId: "PJAIN03",
                costAreaDesc: "",
                leadDistributorInd: true,
                includeInd: true,
              },
            ],
            periscopeIds: ["891061"],
            createUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:07.044Z",
            },
            updateUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:07.045Z",
            },
            allowanceChangeStatus: "UPDATED",
            createTempWorkAllowanceId: "64b8c73cbb19ce1061d9ca74",
            offerId: "64b8c7c7bb19ce1061d9ca76",
          },
        ],
        promotionsList: [
          {
            id: "64b8c7efd6894d235b422e99",
            promotionId: 10005194,
            eventId: "64b8c73b79b9016c8e9b4d00",
            customerGroup: "ALL",
            vehicle: {
              id: "63d4d1ec705a29397068dc5b",
              vehicleNm: "27 Week 41 Insert 2023",
              sourceVehicleSk: 42991,
              vehicleTypNm: "insrt",
              startDate: "2023-10-11",
              endDate: "2023-10-17",
              vehicleType: {
                vehicleTypeId: "636abba1b426ee543a94d3ac",
                sourceVehicleTypeSk: 198,
                vehicleTypNm: "insrt",
                vehicleTypDesc: "Weekly Insert",
              },
            },
            promoStartDate: "2023-10-11",
            promoEndDate: "2023-10-17",
            promoDetails: {
              itemLimit: 1,
              minQuantity: 2,
              factor: 0,
              promotionType: "BUY_ONE_GET_ONE",
              regularPrice: 3,
              listCost: 2.5,
              listAgp: 20,
            },
            createUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:47.321Z",
            },
            updateUser: {
              userId: "PJAIN03",
              name: "Prayas Jain (Contractor)",
              type: "Merchant",
              userRoles: ["az-meupp-nonprod-promointeditor"],
              createTs: "2023-07-20T05:36:47.321Z",
            },
            promotionChangeStatus: "CREATE_PROMO",
            divisionIds: ["27"],
            periscopePromoId: "891061",
            forecast: {
              id: "64b8c80266078d00271c0082",
              quadrant: "2",
              forecastSales: 8679,
              forecastUnits: 3729,
              forecastAgp: 19.8,
              markdown: 6633,
              incrementalUnits: 1743,
              incrementalSales: 0,
              incrementalAgp: 0,
              vendorFunding: 0,
              coverage: 0,
              periscopePromotionId: "891061",
              promotionObjectId: "64b8c7efd6894d235b422e99",
              planEvent: "64b8c73b79b9016c8e9b4d00",
              lastUpdated: "2023-07-20T05:37:06.592Z",
              lastUpdateBy: "meupp-periscope-bk-azr",
            },
            promotionWorkflowStatus: "Pending With Vendor",
          },
        ],
        allowances: ["64b8c7c7bb19ce1061d9ca76"],
        promotions: ["64b8c7efd6894d235b422e99"],
        createUser: {
          userId: "PJAIN03",
          name: "Prayas Jain (Contractor)",
          type: "Merchant",
          userRoles: ["az-meupp-nonprod-promointeditor"],
          createTs: "2023-07-20T05:33:47.941Z",
        },
        updateUser: {
          userId: "PJAIN03",
          name: "Prayas Jain (Contractor)",
          type: "Merchant",
          userRoles: ["az-meupp-nonprod-promointeditor"],
          createTs: "2023-07-20T05:36:51.497Z",
        },
        eventCreationVehicle: {
          vehicleId: "63d4d1ec705a29397068dc5b",
          vehicleNm: "27 Week 41 Insert 2023",
          sourceVehicleSk: 42991,
          startDate: "2023-10-11",
          endDate: "2023-10-17",
          vehicleType: {
            vehicleTypeId: "636abba1b426ee543a94d3ac",
            sourceVehicleTypeSk: 198,
            vehicleTypNm: "insrt",
            vehicleTypDesc: "Weekly Insert",
          },
        },
        forecast: {
          quadrant: "2",
          forecastSales: 8679,
          forecastUnits: 3729,
        },
        planEventWorkFlowType: "Modify",
        eventTypeEnum: "DP",
      },
      getValues: mockGetValues,
      allowPromoStatus: "Pending With Vendor",
      eventStatus: "Pending With Vendor",
    };

    const expectedValue = getAllowAmtOrUom("1", true, fiedlObj, false);
    expect(expectedValue).toEqual("EA");
  });
  it("isAmtUomSameInPlanAndPending", () => {
    const mockGetValues = jest.fn().mockImplementation((key = null) => {
      if (key) {
        return [
          {
            id: "64b8c7c7bb19ce1061d9ca76",
            offerNumber: 7008953,
            origOfferNumber: 0,
            planEvent: "64b8c73b79b9016c8e9b4d00",
            createInd: "TC",
            minAggrAllowanceStartDate: "2023-10-11",
            maxAggrAllowanceEndDate: "2023-10-17",
            divisionIds: ["27"],
            allowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca75",
                allowanceIdNbr: 700895301,
                vendorNbr: "",
                allowanceStatus: "Pending With Vendor",
                leadDistributorInfos: [],
                vehicle: {
                  vehicleId: "63d4d1ec705a29397068dc5b",
                  vehicleNm: "27 Week 41 Insert 2023",
                  sourceVehicleSk: 42991,
                  startDate: "2023-10-11",
                  endDate: "2023-10-17",
                  vehicleType: {
                    vehicleTypeId: "636abba1b426ee543a94d3ac",
                    sourceVehicleTypeSk: 198,
                    vehicleTypNm: "insrt",
                    vehicleTypDesc: "Weekly Insert",
                  },
                },
                allowanceStartDate: "2023-10-11",
                allowanceEndDate: "2023-10-17",
                performStartDate: "2023-10-11",
                performEndDate: "2023-10-17",
                orderStartDate: "0001-01-01",
                orderEndDate: "0001-01-01",
                shipStartDate: "0001-01-01",
                shipEndDate: "0001-01-01",
                arrivalStartDate: "0001-01-01",
                arrivalEndDate: "0001-01-01",
                cancelledTs: "0001-01-03",
                processTs: "0001-01-03",
                lastApprovedDate: "0001-01-01",
                lastApprovedUserId: "",
                allowanceBillingInfo: {
                  absMerchVendor: "000504",
                  absVendorName: "BAY VALLEY FOODS LLC",
                  absVendorPaymentType: "Deduction",
                  acPayableVendorNbr: "010029",
                  acReceivableVendorNbr: "142798",
                  billingContactName: "",
                  billingContactEmail: "",
                  vendorComment: "",
                  vendorOfferTrackingNbr: "",
                },
                performance: {
                  allowanceCd: "T",
                  perfCode1: "20",
                  perfCode2: "52",
                  payType: "D",
                  allwPerfId: "63a3a12743a6cee87995b834",
                },
                allowanceItems: [
                  {
                    itemId: "2020024",
                    primaryUpc: "002113030601",
                    caseUpc: "1002113030601",
                    itemUpcs: ["002113030601", "1002113030601"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020028",
                    primaryUpc: "002113030602",
                    caseUpc: "1002113030602",
                    itemUpcs: ["002113030602", "1002113030602"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020342",
                    primaryUpc: "002113030605",
                    caseUpc: "1002113030605",
                    itemUpcs: ["002113030605", "1002113030605"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2020466",
                    primaryUpc: "002113030654",
                    caseUpc: "1002113030654",
                    itemUpcs: ["002113030654", "1002113030654"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                  {
                    itemId: "2022003",
                    primaryUpc: "002113019884",
                    caseUpc: "1002113019884",
                    itemUpcs: ["002113019884", "1002113019884"],
                    effectiveEndDate: "0001-01-01",
                    vendorPackConversionFactor: 1,
                    packWhse: 12,
                    ringType: 0,
                    uom: "EA",
                    allowanceItemComps: [
                      {
                        performance: {
                          allowanceCd: "T",
                          perfCode1: "20",
                          perfCode2: "52",
                          payType: "D",
                          allwPerfId: "63a3a12743a6cee87995b834",
                        },
                        allowanceTypeStartDate: "2023-10-11",
                        allowanceTypeEndDate: "2023-10-17",
                        allowAmount: 0,
                      },
                    ],
                  },
                ],
                location: {
                  locationId: "639033d538196056762e6e28",
                  locationName: "27 - Seattle",
                  distCenter: "DDSE",
                  locationTypeCd: "D",
                  locationTypeCdEnum: "RETAIL_DIVISION",
                },
                headerFlatAmt: 0,
                createTs: 1689831367045,
                createUserId: "PJAIN03",
                lastUpdTs: 1689831367045,
                lastUpdUserId: "PJAIN03",
                costAreaDesc: "",
                leadDistributorInd: true,
                includeInd: true,
              },
            ],
            periscopeIds: ["891061"],
            allowanceChangeStatus: "UPDATED",
            createTempWorkAllowanceId: "64b8c73cbb19ce1061d9ca74",
            offerId: "64b8c7c7bb19ce1061d9ca76",
          },
        ];
      } else {
        return {
          planEvent: {
            offerAllowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca76",
                offerNumber: 7008953,
                origOfferNumber: 0,
                planEvent: "64b8c73b79b9016c8e9b4d00",
                createInd: "TC",
                minAggrAllowanceStartDate: "2023-10-11",
                maxAggrAllowanceEndDate: "2023-10-17",
                divisionIds: ["27"],
                allowances: [
                  {
                    id: "64b8c7c7bb19ce1061d9ca75",
                    allowanceIdNbr: 700895301,
                    vendorNbr: "",
                    allowanceStatus: "Pending With Vendor",
                    leadDistributorInfos: [],
                    vehicle: {
                      vehicleId: "63d4d1ec705a29397068dc5b",
                      vehicleNm: "27 Week 41 Insert 2023",
                      sourceVehicleSk: 42991,
                      startDate: "2023-10-11",
                      endDate: "2023-10-17",
                      vehicleType: {
                        vehicleTypeId: "636abba1b426ee543a94d3ac",
                        sourceVehicleTypeSk: 198,
                        vehicleTypNm: "insrt",
                        vehicleTypDesc: "Weekly Insert",
                      },
                    },
                    defaultAllowanceDates: {
                      allowanceStartDate: "2023-10-11",
                      allowanceEndDate: "2023-10-17",
                      performStartDate: "2023-10-11",
                      performEndDate: "2023-10-17",
                      orderStartDate: "0001-01-01",
                      orderEndDate: "0001-01-01",
                      shipStartDate: "0001-01-01",
                      shipEndDate: "0001-01-01",
                      arrivalStartDate: "0001-01-01",
                      arrivalEndDate: "0001-01-01",
                      overrideInd: false,
                      notPreSaved: false,
                    },
                    allowanceStartDate: "2023-10-11",
                    allowanceEndDate: "2023-10-17",
                    performStartDate: "2023-10-11",
                    performEndDate: "2023-10-17",
                    orderStartDate: "0001-01-01",
                    orderEndDate: "0001-01-01",
                    shipStartDate: "0001-01-01",
                    shipEndDate: "0001-01-01",
                    arrivalStartDate: "0001-01-01",
                    arrivalEndDate: "0001-01-01",
                    cancelledTs: "0001-01-03",
                    processTs: "0001-01-03",
                    lastApprovedDate: "0001-01-01",
                    lastApprovedUserId: "",
                    allowanceBillingInfo: {
                      absMerchVendor: "000504",
                      absVendorName: "BAY VALLEY FOODS LLC",
                      absVendorPaymentType: "Deduction",
                      acPayableVendorNbr: "010029",
                      acReceivableVendorNbr: "142798",
                      billingContactName: "",
                      billingContactEmail: "",
                      vendorComment: "",
                      vendorOfferTrackingNbr: "",
                    },
                    allowanceBillingInfos: [
                      {
                        vendorIds: [
                          {
                            vendorNbr: "000504",
                            vendorSubAccount: "005",
                            distCenter: "WANC",
                            fullVendorNbr: "000504-005",
                          },
                        ],
                        absMerchVendor: "000504-005",
                        absVendorName: "BAY VALLEY FOODS LLC",
                        absVendorPaymentType: "D",
                        acPayableVendorNbr: "010029",
                        acReceivableVendorNbr: "142798",
                        billingContactName: "AMY RASMUSSEN - VMI AN   ",
                        billingContactEmail: "<EMAIL>",
                        vendorBillingList: [
                          {
                            billingContactName: "AMY RASMUSSEN - VMI AN   ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "MARK WASHFORD            ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "SHERYL MIELKE TRADE PROMO",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "TINA KECKHAVER - CSR     ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "AMY RASMUSSEN - VMI AN   ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "MARK WASHFORD            ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "KRISTIN_KARNITZ@BAYVALLEY",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "SHERYL MIELKE TRADE PROMO",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                          {
                            billingContactName: "TINA KECKHAVER - CSR     ",
                            billingContactEmail:
                              "<EMAIL>",
                          },
                        ],
                      },
                    ],
                    storeGroups: [
                      {
                        storeGroupId: "6453d8fa31c0c4e30d8f11d3",
                        sourceStoreGroupId: "EDM",
                        storeGroupName: "Seattle All Stores",
                        storeGroupType: {
                          groupType: "S",
                          storeGrpTypeName: "Division",
                          groupInd: "D",
                        },
                        divisionIds: ["27"],
                        storeCount: 220,
                      },
                    ],
                    performance: {
                      allowanceCd: "T",
                      perfCode1: "20",
                      perfCode2: "52",
                      payType: "D",
                      allwPerfId: "63a3a12743a6cee87995b834",
                    },
                    planProductGroups: [
                      {
                        planProductGroupId: "636bde8d9665d0440e006e46",
                        sourceProductGroupId: 185046,
                        name: "Signature SELECT® Crackers - 84972",
                        divisionId: "27",
                        smicGroupCode: 2,
                        smicCategoryCode: "0202",
                        supplier: {
                          supplierId: "7877",
                          supplierName: "ABS-ASMs",
                        },
                        itemCount: 5,
                        displayInd: false,
                        simsVendors: ["000504"],
                      },
                    ],
                    allowanceItems: [
                      {
                        itemId: "2020024",
                        primaryUpc: "002113030601",
                        caseUpc: "1002113030601",
                        itemUpcs: ["002113030601", "1002113030601"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020028",
                        primaryUpc: "002113030602",
                        caseUpc: "1002113030602",
                        itemUpcs: ["002113030602", "1002113030602"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020342",
                        primaryUpc: "002113030605",
                        caseUpc: "1002113030605",
                        itemUpcs: ["002113030605", "1002113030605"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020466",
                        primaryUpc: "002113030654",
                        caseUpc: "1002113030654",
                        itemUpcs: ["002113030654", "1002113030654"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2022003",
                        primaryUpc: "002113019884",
                        caseUpc: "1002113019884",
                        itemUpcs: ["002113019884", "1002113019884"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                    ],
                    location: {
                      locationId: "639033d538196056762e6e28",
                      locationName: "27 - Seattle",
                      distCenter: "DDSE",
                      locationTypeCd: "D",
                      locationTypeCdEnum: "RETAIL_DIVISION",
                    },
                    headerFlatAmt: 0,
                    createTs: 1689831367045,
                    createUserId: "PJAIN03",
                    lastUpdTs: 1689831367045,
                    lastUpdUserId: "PJAIN03",
                    costAreaDesc: "",
                    leadDistributorInd: true,
                    includeInd: true,
                  },
                ],
                periscopeIds: ["891061"],
                createUser: {
                  userId: "PJAIN03",
                  name: "Prayas Jain (Contractor)",
                  type: "Merchant",
                  userRoles: ["az-meupp-nonprod-promointeditor"],
                  createTs: "2023-07-20T05:36:07.044Z",
                },
                updateUser: {
                  userId: "PJAIN03",
                  name: "Prayas Jain (Contractor)",
                  type: "Merchant",
                  userRoles: ["az-meupp-nonprod-promointeditor"],
                  createTs: "2023-07-20T05:36:07.045Z",
                },
                allowanceChangeStatus: "UPDATED",
                createTempWorkAllowanceId: "64b8c73cbb19ce1061d9ca74",
                offerId: "64b8c7c7bb19ce1061d9ca76",
              },
            ],
          },
          planEventPending: {
            offerAllowances: [
              {
                id: "64b8c7c7bb19ce1061d9ca76",
                offerNumber: 7008953,
                origOfferNumber: 0,
                planEvent: "64b8c73b79b9016c8e9b4d00",
                createInd: "TC",
                minAggrAllowanceStartDate: "2023-10-11",
                maxAggrAllowanceEndDate: "2023-10-17",
                divisionIds: ["27"],
                allowances: [
                  {
                    id: "64b8c7c7bb19ce1061d9ca75",
                    allowanceIdNbr: 700895301,
                    vendorNbr: "",
                    allowanceStatus: "Pending With Vendor",
                    leadDistributorInfos: [],
                    vehicle: {
                      vehicleId: "63d4d1ec705a29397068dc5b",
                      vehicleNm: "27 Week 41 Insert 2023",
                      sourceVehicleSk: 42991,
                      startDate: "2023-10-11",
                      endDate: "2023-10-17",
                      vehicleType: {
                        vehicleTypeId: "636abba1b426ee543a94d3ac",
                        sourceVehicleTypeSk: 198,
                        vehicleTypNm: "insrt",
                        vehicleTypDesc: "Weekly Insert",
                      },
                    },
                    allowanceStartDate: "2023-10-11",
                    allowanceEndDate: "2023-10-17",
                    performStartDate: "2023-10-11",
                    performEndDate: "2023-10-17",
                    orderStartDate: "0001-01-01",
                    orderEndDate: "0001-01-01",
                    shipStartDate: "0001-01-01",
                    shipEndDate: "0001-01-01",
                    arrivalStartDate: "0001-01-01",
                    arrivalEndDate: "0001-01-01",
                    cancelledTs: "0001-01-03",
                    processTs: "0001-01-03",
                    lastApprovedDate: "0001-01-01",
                    lastApprovedUserId: "",
                    performance: {
                      allowanceCd: "T",
                      perfCode1: "20",
                      perfCode2: "52",
                      payType: "D",
                      allwPerfId: "63a3a12743a6cee87995b834",
                    },
                    allowanceItems: [
                      {
                        itemId: "2020024",
                        primaryUpc: "002113030601",
                        caseUpc: "1002113030601",
                        itemUpcs: ["002113030601", "1002113030601"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020028",
                        primaryUpc: "002113030602",
                        caseUpc: "1002113030602",
                        itemUpcs: ["002113030602", "1002113030602"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020342",
                        primaryUpc: "002113030605",
                        caseUpc: "1002113030605",
                        itemUpcs: ["002113030605", "1002113030605"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2020466",
                        primaryUpc: "002113030654",
                        caseUpc: "1002113030654",
                        itemUpcs: ["002113030654", "1002113030654"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                      {
                        itemId: "2022003",
                        primaryUpc: "002113019884",
                        caseUpc: "1002113019884",
                        itemUpcs: ["002113019884", "1002113019884"],
                        effectiveEndDate: "0001-01-01",
                        vendorPackConversionFactor: 1,
                        packWhse: 12,
                        ringType: 0,
                        uom: "EA",
                        allowanceItemComps: [
                          {
                            performance: {
                              allowanceCd: "T",
                              perfCode1: "20",
                              perfCode2: "52",
                              payType: "D",
                              allwPerfId: "63a3a12743a6cee87995b834",
                            },
                            allowanceTypeStartDate: "2023-10-11",
                            allowanceTypeEndDate: "2023-10-17",
                            allowAmount: 0,
                          },
                        ],
                      },
                    ],
                    location: {
                      locationId: "639033d538196056762e6e28",
                      locationName: "27 - Seattle",
                      distCenter: "DDSE",
                      locationTypeCd: "D",
                      locationTypeCdEnum: "RETAIL_DIVISION",
                    },
                    headerFlatAmt: 0,
                    createTs: 1689831367045,
                    createUserId: "PJAIN03",
                    lastUpdTs: 1689831367045,
                    lastUpdUserId: "PJAIN03",
                    costAreaDesc: "",
                    leadDistributorInd: true,
                    includeInd: true,
                  },
                ],
                allowanceChangeStatus: "CREATED",
              },
            ],
          },
        };
      }
    });
    const expectedValue = isAmtUomSameInPlanAndPending(
      mockGetValues,
      7008953,
      "offerallowances.allowances.allowanceItems.uom",
      "700895301",
      "uom"
    );
    expect(expectedValue).toEqual(true);
  });

  test("Test getStepsByAllowanceCreateInd with HEADERFLAT allowanceType", () => {
    const steppers = ["step1", "step2", "step3", "step4"];
    const allowanceScreenType = "type1";
    const allowanceType = efConstants.ALLOWANCE_TYPES.HEADERFLAT.key;
    const defaultCreateInd = "A";

    const result = getStepsByAllowanceCreateInd(
      steppers,
      allowanceScreenType,
      allowanceType,
      defaultCreateInd
    );
    expect(result).toEqual(["step1", "step2", "step3", "step4"]);
  });
  test("Test getStepsByAllowanceCreateInd with non-HEADERFLAT allowanceType", () => {
    const steppers = ["step1", "step2", "step3", "step4"];
    const allowanceType = "SOMETHING_ELSE";
    const defaultCreateInd = "B";

    const result = getStepsByAllowanceCreateInd(
      steppers,
      allowanceType,
      defaultCreateInd
    );

    // Assert the expected result
    expect(result).toEqual(["step1", "step2", "step3", "step4"]);
  });
  test("Test getStepsByAllowanceCreateInd with disabled allowanceScreenType", () => {
    const steppers = ["step1", "step2", "step3", "step4"];
    const allowanceScreenType = "type2";
    const allowanceType = efConstants.ALLOWANCE_TYPES.HEADERFLAT.key;
    const defaultCreateInd = "C";

    const result = getStepsByAllowanceCreateInd(
      steppers,
      allowanceScreenType,
      allowanceType,
      defaultCreateInd
    );

    // Assert the expected result (step3 and step4 are filtered out)
    expect(result).toEqual(["step1", "step2", "step3", "step4"]);
  });

  it("should return BOTH_KEY if there are no product sources", () => {
    const productSources: string[] = [];
    const sourceKey = getProductSourceKey(productSources);
    expect(sourceKey).toBe("BOTH");
  });

  it("should return the product source if there is only one", () => {
    const productSources = ["DSD"];
    const sourceKey = getProductSourceKey(productSources);
    expect(sourceKey).toBe("DSD");
  });

  it("should return BOTH_KEY if there are multiple product sources", () => {
    const productSources = ["DSD", "WAREHOUSE"];
    const sourceKey = getProductSourceKey(productSources);
    expect(sourceKey).toBe("BOTH");
  });
  it("should return a round of number", () => {
    const finalNumber = roundOfNumber(2.2433);
    expect(finalNumber).toBe(2.24);
  });
  it("should check for keys", () => {
    const objectLength = checkObjectHasKeys({ key1: "value1", key2: "value2" });
    expect(objectLength).toBe(2);
  });

  it("should get allowance key", () => {
    const allowanceKey = getAllowanceKey("CASE");
    expect(allowanceKey).toBe("CASE");
  });
  it("should get Uom field value for WE", () => {
    const uomFieldVal = getUomFieldValue("WE");
    expect(uomFieldVal).toBe("LB");
  });
  it("should get Uom field value for LB", () => {
    const uomFieldVal = getUomFieldValue("LB");
    expect(uomFieldVal).toBe("LB");
  });
  it("should handle skip step with SCAN key", () => {
    const result = handleSkipStep("SCAN", ["SCAN", "CASE"], "WAREHOUSE");
    expect(result).toBe(true);
  });
  it("should handle skip step with CASE key", () => {
    const result = handleSkipStep("CASE", ["CASE"], "WAREHOUSE");
    expect(result).toBe(true);
  });
  it("should handle skip step ", () => {
    const isTrue = handleSkipStep("SHIP_TO_STORE", ["SCAN", "CASE"], "DSD");
    expect(isTrue).toBe(true);
  });
  it("should handle skip step with SHIP_TO_STORE", () => {
    const isTrue = handleSkipStep("SHIP_TO_STORE", ["SCAN"], "DSD");
    expect(isTrue).toBe(false);
  });
  it("should check for default condition in hndleSkipStep method", () => {
    const defaultValue = handleSkipStep("HEADERFLAT", ["SCAN", "CASE"], "DSD");
    expect(defaultValue).toBe(false);
  });
  it("should get query params", () => {
    const params = getQueryParams();
    const finalResult = {
      isCancelled: null,
      isOfferViewMode: null,
      isSaved: null,
      offerId: null,
      offerKey: null,
      taskType: null,
      "isRedirectToMainEntry": null,
    };
    expect(params).toStrictEqual(finalResult);
  });

  it("should display promo details based on type", () => {
    const promoDetails = displayPromoDetailsBasedOnType(
      "CENT_OFF",
      promoDetailsFields,
      true
    );
    expect(promoDetails).toBe("Cents Off");
  });
  it("should get regester key", () => {
    const cardIndex = 0;
    const key = getOfferFormRegisterKey(cardIndex);
    expect(key).toBe("offerAllowances[0]");
  });
  it("should get performance record by id", () => {
    const allowanceData = {
      getAllowancePerformanceTypes: [
        {
          id: "1",
        },
      ],
    };

    const recordId = getPerformanceRecordById(performance, allowanceData);
    expect(recordId).toStrictEqual({ id: "1" });
  });
});

describe("Allowances Items Save Services Test Cases", () => {
  it("Show data for getAllowanceTempWorkDataReqBody", () => {
    const mockTempData = { allowanceData: "test" };
    const mockEventId = "641019c6b3c7e055b78cf285";
    Object.defineProperty(window, "location", {
      value: {
        search: `?eventId=${mockEventId}`,
      },
    });
    const expectedResult = [
      {
        URL_PARAM: mockEventId,
        queryParams: {},
      },
      {
        skip: false,
      },
    ];
    expect(getAllowanceTempWorkDataReqBody({ tempData: mockTempData })).toEqual(
      expectedResult
    );
  });
});

describe("getAllowanceTypes", () => {
  it("returns an array of allowance type values", () => {
    const result = getAllowanceTypes();
    expect(result).toEqual([
      "CASE",
      "SCAN",
      "SHIP_TO_STORE",
      "HEADER_FLAT",
      "ITEM_FLAT",
    ]);
  });
  it("returns correct allowances type for CASE allowanceCd", () => {
    const performanceProps = {
      allowanceCd: "C",
      perfCode1: "",
      perfCode2: "",
    };
    const result = getAllowanceTypeByPerformance(performanceProps);
    expect(result).toBe("Case");
  });
  it("returns correct allowance type for SCAN allowanceCd", () => {
    const performanceProps = {
      allowanceCd: "T",
      perfCode1: "",
      perfCode2: "",
    };
    const result = getAllowanceTypeByPerformance(performanceProps);
    expect(result).toBe("Scan");
  });
  it("returns correct allowance type for SHIPTOSTORE allowanceCd", () => {
    const performanceProps = {
      allowanceCd: "S",
      perfCode1: "",
      perfCode2: "",
    };
    const result = getAllowanceTypeByPerformance(performanceProps);
    expect(result).toBe("Ship To Store");
  });
  it("returns correct allowance type for HEADERFLAT allowanceCd", () => {
    const performanceProps = {
      allowanceCd: "A",
      perfCode1: "",
      perfCode2: "",
    };
    const result = getAllowanceTypeByPerformance(performanceProps);
    expect(result).toBe("");
  });
  it("returns correct allowance type for HEADERFLAT allowanceCd with perfCode1", () => {
    const performanceProps = {
      allowanceCd: "A",
      perfCode1: "someValue",
      perfCode2: "",
    };
    const result = getAllowanceTypeByPerformance(performanceProps);
    expect(result).toBe("Header Flat");
  });
  it("returns correct allowance type for HEADERFLAT allowanceCd with perfCode2", () => {
    const performanceProps = {
      allowanceCd: "A",
      perfCode1: "",
      perfCode2: "someValue",
    };
    const result = getAllowanceTypeByPerformance(performanceProps);
    expect(result).toBe("Item Flat");
  });
  it("returns empty string for unknown allowanceCd", () => {
    const performanceProps = {
      allowanceCd: "UNKNOWN",
      perfCode1: "",
      perfCode2: "someValue",
    };
    const result = getAllowanceTypeByPerformance(performanceProps);
    expect(result).toBe("");
  });

  xit("should return the expected object when isEditEnable is false", () => {
    const allowancesRespCopy = {
      offerAllowancesGroup: "DSD",
      eventId: "641019c6b3c7e055b78cf285",
      allowances: [{ id: 1 }, { id: 2 }],
    };
    const allowanceTempWorkData = {
      allowanceType: efConstants.ALLOWANCE_TYPES.HEADERFLAT.key,
      allowanceTypeSpecification: {
        type1: {
          allowancesMap: {
            group1: [{ id: 3 }, { id: 4 }],
          },
        },
      },
    };

    const result = saveAllowancesItemReqBody({
      allowancesRespCopy,
      allowanceTempWorkData,
    });

    expect(result).toEqual({
      URL_PARAM: "641019c6b3c7e055b78cf285",
      allowanceType: efConstants.ALLOWANCE_TYPES.HEADERFLAT.key,
      allowanceTypeSpecification: {
        type1: {
          allowancesMap: {
            group1: [{ id: 1 }, { id: 2 }],
          },
        },
      },
    });
  });

  describe("isCrossUserEditingCard", () => {
    it('should return true when eventStatus is "Agreed" and different users are editing', () => {
      const eventStatus = "Agreed";
      const allowanceData = {
        createUserId: "SPAGR00",
        lastUpdUserId: "SPAGR00",
      };
      localStorage.setItem("OAM_REMOTE_USER", JSON.stringify("RSANT00"));

      const result = isCrossUserEditingCard(eventStatus, allowanceData);

      expect(result).toBe(true);
    });

    it('should return false when eventStatus is not "Agreed"', () => {
      const eventStatus = "Draft";
      const allowanceData = {
        createUserId: "SPAGR00",
        lastUpdUserId: "SPAGR00",
      };
      localStorage.setItem("OAM_REMOTE_USER", JSON.stringify("SPAGR00"));

      const result = isCrossUserEditingCard(eventStatus, allowanceData);

      expect(result).toBe(false);
    });

    it("should return false when allowanceData is not provided", () => {
      const eventStatus = "Agreed";
      const allowanceData = null;
      localStorage.setItem("OAM_REMOTE_USER", JSON.stringify("SPAGR00"));

      const result = isCrossUserEditingCard(eventStatus, allowanceData);

      expect(result).toBe(false);
    });

    it("should return false when logged in user is the same as the editing user", () => {
      const eventStatus = "Agreed";
      const allowanceData = {
        createUserId: "SPAGR00",
        lastUpdUserId: "SPAGR00",
      };
      localStorage.setItem("OAM_REMOTE_USER", JSON.stringify("SPAGR00"));

      const result = isCrossUserEditingCard(eventStatus, allowanceData);

      expect(result).toBe(false);
    });
  });

  describe("removeParams", () => {
    it("should call window.history.pushState with the correct arguments", () => {
      const mockPushState = jest.fn();
      window.history.pushState = mockPushState;
      removeParams();

      expect(mockPushState).not.toHaveBeenCalled();
    });
  });

  describe("getisValidDates", () => {
    const offersData = [{ id: 1234 }];

    it("should return true for valid combination 1", () => {
      const allowanceType = "SCAN";
      const createInd = "TC";
      const offerId = 1234;
      const key = "ALLOW";

      const result = getisValidDates(
        allowanceType,
        createInd,
        offerId,
        offersData,
        key
      );

      expect(result).toBe(true);
    });

    it("should return true for valid combination 2", () => {
      const allowanceType = "CASE";
      const createInd = "TS";
      const offerId = 1234;
      const key = "DSD";

      const result = getisValidDates(
        allowanceType,
        createInd,
        offerId,
        offersData,
        key
      );

      expect(result).toBe(true);
    });

    it("should return true for valid combination 3", () => {
      const allowanceType = "CASE";
      const createInd = "CW";
      const offerId = 1234;
      const key = "WAREHOUSE";

      const result = getisValidDates(
        allowanceType,
        createInd,
        offerId,
        offersData,
        key
      );

      expect(result).toBe(true);
    });

    it("should return false for invalid combination 1", () => {
      const allowanceType = "SCAN";
      const createInd = "TC";
      const offerId = 3245;
      const key = "ALLOW";

      const result = getisValidDates(
        allowanceType,
        createInd,
        offerId,
        offersData,
        key
      );

      expect(result).toBe(false);
    });

    it("should return false for invalid combination 2", () => {
      const allowanceType = "CASE";
      const createInd = "LC";
      const offerId = 1234;
      const key = "DSD";

      const result = getisValidDates(
        allowanceType,
        createInd,
        offerId,
        offersData,
        key
      );

      expect(result).toBe(false);
    });
  });
});

describe("getOfferKey", () => {
  it("should return the default key for undefined createInd", () => {
    const defaultKey = "WAREHOUSE";
    const allowanceType = "CASE";

    const result = getOfferKey(defaultKey, allowanceType);

    expect(result).toBe(defaultKey);
  });

  it("should return the default key for createInd not in the mapper", () => {
    const defaultKey = "WAREHOUSE";
    const allowanceType = "HEADERFLAT";
    const createInd = "AO";

    const result = getOfferKey(defaultKey, allowanceType, createInd);

    expect(result).toBe(defaultKey);
  });

  it("should return the mapped offer key for HEADERFLAT and valid createInd", () => {
    const defaultKey = "WAREHOUSE";
    const allowanceType = "HEADERFLAT";
    const createInd = "TC";

    const result = getOfferKey(defaultKey, allowanceType, createInd);

    expect(result).toBe("WAREHOUSE");
  });

  it("should return the mapped offer key for ITEMFLAT and valid createInd", () => {
    const defaultKey = "DSD";
    const allowanceType = "ITEMFLAT";
    const createInd = "TS";

    const result = getOfferKey(defaultKey, allowanceType, createInd);

    expect(result).toBe("DSD");
  });

  it("should return the default key for HEADERFLAT with undefined createInd", () => {
    const defaultKey = "WAREHOUSE";
    const allowanceType = "HEADERFLAT";

    const result = getOfferKey(defaultKey, allowanceType);

    expect(result).toBe(defaultKey);
  });

  it("should return the default key for ITEMFLAT with undefined createInd", () => {
    const defaultKey = "WAREHOUSE";
    const allowanceType = "ITEMFLAT";

    const result = getOfferKey(defaultKey, allowanceType);

    expect(result).toBe(defaultKey);
  });
});

describe("getVehicleDatesMapData", () => {
  it("should return an empty object when vehicleRef is undefined", () => {
    const key = "WAREHOUSE";
    const data = {
      vehicleRef: undefined,
      dateRange: {
        startDate: "2023-09-01",
        endDate: "2023-09-10",
      },
    };

    const result = getVehicleDatesMapData(key, data);

    expect(result).toEqual({
      WAREHOUSE: {
        dateRange: {
          endDate: "2023-09-10",
          startDate: "2023-09-01",
        },
        vehicleId: undefined,
        vehicleRef: undefined,
      },
    });
  });

  it("should return the expected data with a valid vehicleRef and dateRange", () => {
    const key = "WAREHOUSE";
    const data = {
      vehicleRef: {
        vehicleId: "63d4d1ec705a29397068dc5b",
        startDate: "2023-09-01",
        endDate: "2023-09-10",
      },
      dateRange: {
        startDate: "2023-09-01",
        endDate: "2023-09-10",
      },
    };

    const result = getVehicleDatesMapData(key, data);

    expect(result).toEqual({
      WAREHOUSE: {
        vehicleId: "63d4d1ec705a29397068dc5b",
        vehicleRef: {
          vehicleId: "63d4d1ec705a29397068dc5b",
          startDate: "2023-09-01",
          endDate: "2023-09-10",
        },
        dateRange: {
          startDate: "2023-09-01",
          endDate: "2023-09-10",
        },
      },
    });
  });

  it("should merge with previousVehicleData when provided", () => {
    const key = "WAREHOUSE";
    const data = {
      vehicleRef: {
        vehicleId: "63d4d1ec705a29397068dc5b",
        startDate: "2023-09-01",
        endDate: "2023-09-10",
      },
      dateRange: {
        startDate: "2023-09-01",
        endDate: "2023-09-10",
      },
    };
    const previousVehicleData = {
      prevKey: {
        vehicleId: "63d4d1ec705a29397068dc5b",
        vehicleRef: {
          vehicleId: "63d4d1ec705a29397068dc5b",
          startDate: "2023-08-01",
          endDate: "2023-08-10",
        },
        dateRange: {
          startDate: "2023-08-01",
          endDate: "2023-08-10",
        },
      },
    };

    const result = getVehicleDatesMapData(key, data, previousVehicleData);

    expect(result).toEqual({
      prevKey: {
        vehicleId: "63d4d1ec705a29397068dc5b",
        vehicleRef: {
          vehicleId: "63d4d1ec705a29397068dc5b",
          startDate: "2023-08-01",
          endDate: "2023-08-10",
        },
        dateRange: {
          startDate: "2023-08-01",
          endDate: "2023-08-10",
        },
      },
      WAREHOUSE: {
        vehicleId: "63d4d1ec705a29397068dc5b",
        vehicleRef: {
          vehicleId: "63d4d1ec705a29397068dc5b",
          startDate: "2023-09-01",
          endDate: "2023-09-10",
        },
        dateRange: {
          startDate: "2023-09-01",
          endDate: "2023-09-10",
        },
      },
    });
  });
});

describe("getProductSourceByOfferKey", () => {
  it("should return empty productGroups and undefined allowanceToBeCraetedLabel for undefined offerGroupKey", () => {
    const result = getProductSourceByOfferKey();

    expect(result).toEqual({
      productGroups: [],
      allowanceToBeCraetedLabel: undefined,
    });
  });

  xit("should return productGroups and allowanceToBeCraetedLabel for a valid offerGroupKey and allowanceType", () => {
    const offerGroupKey = "WAREHOUSE";
    const allowanceType = "SCAN";

    const result = getProductSourceByOfferKey(offerGroupKey, allowanceType);

    expect(result).toEqual({
      productGroups: ["WAREHOUSE", "DSD"],
      allowanceToBeCraetedLabel: "One Allowance",
    });
  });

  it("should return productGroups for a valid offerGroupKey with undefined allowanceType", () => {
    const offerGroupKey = "WAREHOUSE";

    const result = getProductSourceByOfferKey(offerGroupKey);

    expect(result).toEqual({
      productGroups: [],
      allowanceToBeCraetedLabel: undefined,
    });
  });

  it("should return empty productGroups and undefined allowanceToBeCraetedLabel for an unknown offerGroupKey", () => {
    const offerGroupKey = "_";

    const result = getProductSourceByOfferKey(offerGroupKey);

    expect(result).toEqual({
      productGroups: [],
      allowanceToBeCraetedLabel: undefined,
    });
  });
});

const eventDetails = {
  offerAllowances: [
    {
      id: "offer1",
      allowances: [
        {
          performance: "performanceA",
        },
      ],
      createInd: "TC",
      offerNumber: "123",
    },
    {
      id: "offer2",
      allowances: [
        {
          performance: "performanceB",
        },
      ],
      createInd: "TS",
      offerNumber: "456",
    },
  ],
};

const invalidOffers = ["offer1", "offer2"];
const invalidPromos = ["promo1", "promo2"];

describe("getInvalidDataObject", () => {
  it("should correctly construct the invalid data object", () => {
    const result = getInvalidDataObject(
      eventDetails,
      invalidOffers,
      invalidPromos
    );

    expect(result).toEqual({
      offerAllowances: [
        {
          offerId: "offer1",
          allowanceType: "",
          offerNumber: "123",
          defaultInd: "TC",
          dates: {
            dsd: { isError: false },
            wareHouse: { isError: false },
            normal: { isError: false },
            isOpenCard: false,
          },
        },
        {
          offerId: "offer2",
          allowanceType: "",
          offerNumber: "456",
          defaultInd: "TS",
          dates: {
            dsd: { isError: false },
            wareHouse: { isError: false },
            normal: { isError: false },
            isOpenCard: false,
          },
        },
      ],
      promotions: [
        {
          promo1: { promoId: "promo1", isError: true, isOpenCard: false },
        },
        {
          promo2: { promoId: "promo2", isError: true, isOpenCard: false },
        },
      ],
      validOfferAllowance: true,
      validPromos: true,
    });
  });

  it("should return an empty object if invalidOffers and invalidPromos are empty", () => {
    const result = getInvalidDataObject(eventDetails, [], []);

    expect(result).toEqual({
      offerAllowances: [],
      promotions: [],
      validOfferAllowance: true,
      validPromos: true,
    });
  });

  it("should return validOfferAllowance and validPromos as false if invalidOffers and invalidPromos are empty", () => {
    const result = getInvalidDataObject(eventDetails, [], []);

    expect(result.validOfferAllowance).toBe(true);
    expect(result.validPromos).toBe(true);
  });
});

describe("getValuetrimmed", () => {
  it("should return an empty string for undefined input", () => {
    const result = getValuetrimmed();
    expect(result).toBe("");
  });

  it("should return an empty string for null input", () => {
    const result = getValuetrimmed(null);
    expect(result).toBe(undefined);
  });

  it("should return an empty string for undefined and unknown input", () => {
    const result = getValuetrimmed(undefined);
    expect(result).toBe("");
  });

  it("should trim a string with leading and trailing spaces", () => {
    const input = "   Trimmed Value   ";
    const result = getValuetrimmed(input);
    expect(result).toBe("Trimmed Value");
  });

  it("should return an empty string for a string with only spaces", () => {
    const input = "    ";
    const result = getValuetrimmed(input);
    expect(result).toBe("");
  });

  it("should return the input string when it has no leading/trailing spaces", () => {
    const input = "NoSpaces";
    const result = getValuetrimmed(input);
    expect(result).toBe("NoSpaces");
  });

  it("should return the input string when it contains spaces in the middle", () => {
    const input = "Middle Spaces Value";
    const result = getValuetrimmed(input);
    expect(result).toBe("Middle Spaces Value");
  });
});

describe("validOfferList", () => {
  it("should filter out offers with excluded allowance status", () => {
    const input = {
      offerAllowances: [
        {
          allowances: [
            {
              allowanceStatus: "Excluded",
            },
          ],
        },
        {
          allowances: [
            {
              allowanceStatus: "Accepted",
            },
          ],
        },
      ],
    };

    const result = validOfferList(input);

    expect(result).toHaveLength(2);
    expect(result[0].allowances[0].allowanceStatus).toBe("Excluded");
  });

  it("should filter out offers with canceled override status and agreed pending allowance status", () => {
    const input = {
      offerAllowances: [
        {
          allowances: [
            {
              allowanceStatus: "AgreedPending",
            },
          ],
          overrideAllowanceStatus: "Canceled",
        },
        {
          allowances: [
            {
              allowanceStatus: "AcceptedStatus",
            },
          ],
        },
      ],
    };

    const result = validOfferList(input);

    expect(result).toHaveLength(2);
    expect(result[0].allowances[0].allowanceStatus).toBe("AgreedPending");
  });

  it("should handle undefined and empty input gracefully", () => {
    let result = validOfferList(undefined);
    expect(result).toEqual(undefined);

    result = validOfferList({});
    expect(result).toEqual(undefined);
  });
});

describe("handleRequireFieldsValidation", () => {
  const mockGetValues = fieldValues => field => fieldValues[field];
  it("should return true when all required fields have values", () => {
    const requiredFields = ["field1", "field2", "field3"];
    const fieldValues = {
      field1: "value1",
      field2: "value2",
      field3: "value3",
    };
    const getValues = mockGetValues(
      fieldValues
    ) as UseFormGetValues<FieldValues>;

    const result = handleRequireFieldsValidation(getValues, requiredFields);

    expect(result).toBe(true);
  });

  it("should return false when at least one required field is empty", () => {
    const requiredFields = ["field1", "field2", "field3"];
    const fieldValues = {
      field1: "value1",
      field2: "",
      field3: "value3",
    };
    const getValues = mockGetValues(
      fieldValues
    ) as UseFormGetValues<FieldValues>;

    const result = handleRequireFieldsValidation(getValues, requiredFields);

    expect(result).toBe(false);
  });

  it("should return false when one required field is missing", () => {
    const requiredFields = ["field1", "field2", "field3"];
    const fieldValues = {
      field1: "value1",
      field3: "value3",
    };
    const getValues = mockGetValues(
      fieldValues
    ) as UseFormGetValues<FieldValues>;

    const result = handleRequireFieldsValidation(getValues, requiredFields);

    expect(result).toBe(false);
  });

  it("should return true when requiredFields array is empty", () => {
    const requiredFields = [];
    const fieldValues = {
      field1: "value1",
      field2: "value2",
    };
    const getValues = mockGetValues(
      fieldValues
    ) as UseFormGetValues<FieldValues>;

    const result = handleRequireFieldsValidation(getValues, requiredFields);

    expect(result).toBe(true);
  });

  it("should return true when all required fields are undefined", () => {
    const requiredFields = ["field1", "field2", "field3"];
    const fieldValues = {};
    const getValues = mockGetValues(
      fieldValues
    ) as UseFormGetValues<FieldValues>;

    const result = handleRequireFieldsValidation(getValues, requiredFields);

    expect(result).toBe(false);
  });
});

describe("getAmountLevel", () => {
  it("should return the amount level when amountInfo is not empty", () => {
    const amountInfo = ["Amount Level Value with Extra Words"];
    const result = getAmountLevel(amountInfo);
    expect(result).toBe("Amount Level Value with Extra");
  });

  it("should return undefined when amountInfo is an empty array", () => {
    const amountInfo = [];
    const result = getAmountLevel(amountInfo);
    expect(result).toBeUndefined();
  });

  it("should return undefined when amountInfo is an empty string", () => {
    const amountInfo = [""];
    const result = getAmountLevel(amountInfo);
    expect(result).toBe("");
  });

  it("should return undefined when amountInfo is undefined", () => {
    const result = getAmountLevel(undefined);
    expect(result).toBeUndefined();
  });

  it("should handle amountInfo with only one word", () => {
    const amountInfo = ["SingleWord"];
    const result = getAmountLevel(amountInfo);
    expect(result).toBe("SingleWord");
  });

  it("should handle amountInfo with two words", () => {
    const amountInfo = ["Two Words"];
    const result = getAmountLevel(amountInfo);
    expect(result).toBe("Two");
  });

  it("should handle amountInfo with two words and additional words", () => {
    const amountInfo = ["Two Words and More"];
    const result = getAmountLevel(amountInfo);
    expect(result).toBe("Two Words and");
  });
});

describe("getUOMBasedOnType", () => {
  const UOM_OPTIONS = {
    CASE: {
      isSwitching: false,
      defaultOptions: ["UOM1", "UOM2"],
    },
    SCAN: {
      isSwitching: true,
      switchCaseOptions: {
        Case1: ["UOM3"],
        Case2: ["UOM4"],
      },
    },
  };

  it("should return default options when isSwitching is false", () => {
    const allowanceType = "CASE";
    const switchValue = "Case1";
    const result = getUOMBasedOnType(allowanceType, UOM_OPTIONS, switchValue);
    expect(result).toEqual(["UOM1", "UOM2"]);
  });

  it("should return switch case options based on switchValue when isSwitching is true", () => {
    const allowanceType = "SCAN";
    const switchValue = "Case2";
    const result = getUOMBasedOnType(allowanceType, UOM_OPTIONS, switchValue);
    expect(result).toEqual(["UOM4"]);
  });

  it("should return an empty array when allowanceType is undefined", () => {
    const allowanceType = undefined;
    const switchValue = "Case1";
    const result = getUOMBasedOnType(allowanceType, UOM_OPTIONS, switchValue);
    expect(result).toEqual([]);
  });

  it("should return an empty array when allowanceType is not found in UOM_OPTIONS", () => {
    const allowanceType = "Unknown";
    const switchValue = "Case1";
    const result = getUOMBasedOnType(allowanceType, UOM_OPTIONS, switchValue);
    expect(result).toEqual(undefined);
  });
});

describe("checkIsWarehouseItem", () => {
  it('should return true if vendorNbr is empty and locationTypeCd is "D"', () => {
    const allowance = { vendorNbr: "", location: { locationTypeCd: "D" } };
    const result = checkIsWarehouseItem(allowance);
    expect(result).toBe(true);
  });

  it('should return true if vendorNbr is empty and locationTypeCd is "W"', () => {
    const allowance = { vendorNbr: "", location: { locationTypeCd: "W" } };
    const result = checkIsWarehouseItem(allowance);
    expect(result).toBe(true);
  });

  it('should return false if vendorNbr is not empty and locationTypeCd is "D"', () => {
    const allowance = {
      vendorNbr: "006446",
      location: { locationTypeCd: "D" },
    };
    const result = checkIsWarehouseItem(allowance);
    expect(result).toBe(false);
  });

  it('should return false if vendorNbr is not empty and locationTypeCd is "W"', () => {
    const allowance = {
      vendorNbr: "006446",
      location: { locationTypeCd: "W" },
    };
    const result = checkIsWarehouseItem(allowance);
    expect(result).toBe(false);
  });

  it('should return false if vendorNbr is empty and locationTypeCd is not "D" or "W"', () => {
    const allowance = { vendorNbr: "", location: { locationTypeCd: "X" } };
    const result = checkIsWarehouseItem(allowance);
    expect(result).toBe(false);
  });

  it('should return false if vendorNbr is not empty and locationTypeCd is not "D" or "W"', () => {
    const allowance = {
      vendorNbr: "006446",
      location: { locationTypeCd: "X" },
    };
    const result = checkIsWarehouseItem(allowance);
    expect(result).toBe(false);
  });

  it("should return false if allowance is not provided", () => {
    const result = checkIsWarehouseItem();
    expect(result).toBe(false);
  });

  it("should return false if location is not provided in the allowance", () => {
    const allowance = { vendorNbr: "", location: { locationTypeCd: "" } }; // location is missing
    const result = checkIsWarehouseItem(allowance);
    expect(result).toBe(false);
  });
});

describe("getCreateIndByLocation", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });
  jest.mock("./allowance-service", () => ({
    ...jest.requireActual("./allowance-service"),
    checkIsWarehouseItem: jest.fn(),
  }));

  it("should return createIndValue when createIndValue is not SCAN or CASE", () => {
    const offerInfo = {
      createInd: "CW",
      allowances: {
        0: {
          // Provide necessary mock data for allowances
        },
      },
    };

    const result = getCreateIndByLocation(offerInfo, 0);

    expect(result).toBe(offerInfo.createInd);
  });

  it("should return SCAN.createInd[0] when createIndValue is SCAN[1] and isWarehouseItem is true", () => {
    const offerInfo = {
      createInd: "TC",
      allowances: {
        0: {
          // Provide necessary mock data for allowances
        },
      },
    };

    const result = getCreateIndByLocation(offerInfo, 0);

    expect(result).toBe("TC");
  });

  it("should return SCAN.createInd[1] when createIndValue is SCAN[1] and isWarehouseItem is false", () => {
    const offerInfo = {
      createInd: "TS",
      allowances: {
        0: {
          // Provide necessary mock data for allowances
        },
      },
    };

    const result = getCreateIndByLocation(offerInfo, 0);

    expect(result).toBe("TS");
  });

  it("should return CASE.createInd[0] when createIndValue is CASE[2] and isWarehouseItem is true", () => {
    const offerInfo = {
      createInd: "CW",
      allowances: {
        0: {
          // Provide necessary mock data for allowances
        },
      },
    };

    const result = getCreateIndByLocation(offerInfo, 0);

    expect(result).toBe("CW");
  });

  it("should return CASE.createInd[1] when createIndValue is CASE[2] and isWarehouseItem is false", () => {
    const offerInfo = {
      createInd: "CD",
      allowances: {
        0: {
          // Provide necessary mock data for allowances
        },
      },
    };

    const result = getCreateIndByLocation(offerInfo, 0);

    expect(result).toBe("CD");
  });
});

describe("checkIsPaymentTypeDeduct", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return false when performance is Off Invoice (01)", () => {
    const result = checkIsPaymentTypeDeduct("Off Invoice (01)", {});
    expect(result).toBe(false);
  });

  it("should return true when performance is not Off Invoice (01) and absVendorPaymentType is D", () => {
    const allowance = {
      allowanceBillingInfo: { absVendorPaymentType: "D" },
    };
    const result = checkIsPaymentTypeDeduct("test perf", allowance);
    expect(result).toBe(true);
  });
});
