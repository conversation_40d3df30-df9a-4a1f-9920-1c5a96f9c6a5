import { useSelectorWrap } from "@me/data-rtk";
import react, { useEffect, useState } from "react";

function NationalOffersSummary() {
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const [offersSummaryData, setoffersSummaryData] = useState();

  useEffect(() => {
    console.log("eventDetailsData", eventDetailsData);
    const result = groupAllowancesByDivision(eventDetailsData);
    console.log("grouped", result);
  }, [eventDetailsData]);

  const groupAllowancesByDivision = planEvent => {
    const grouped = {};
    const offerAllowances = planEvent?.offerAllowances ?? [];

    console.log("Offer Allowances Length:", offerAllowances.length);
    if (!Array.isArray(offerAllowances)) {
      console.log("offerAllowances is not an array!");
      return [];
    }

    for (const entry of offerAllowances) {
      const allowances = entry?.allowances;
      if (!Array.isArray(allowances)) {
        console.log("No allowances array found in this entry:", entry);
        continue;
      }

      for (const allowance of allowances) {
        const divisionIds = allowance?.divisionIds;
        if (!Array.isArray(divisionIds) || divisionIds.length === 0) {
          console.log("No divisionIds found or not an array:", divisionIds);
          continue;
        }

        for (const divisionId of divisionIds) {
          if (!grouped[divisionId]) {
            grouped[divisionId] = [];
          }
          grouped[divisionId].push(allowance);
        }
      }
    }
    return Object.entries(grouped).map(([divisionId, allowances]) => ({
      divisionId,
      allowances,
    }));
  };

  return <div>hey bea</div>;
}

export default NationalOffersSummary;
