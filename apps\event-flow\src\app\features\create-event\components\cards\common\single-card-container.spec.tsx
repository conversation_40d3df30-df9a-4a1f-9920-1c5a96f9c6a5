import { render } from "@testing-library/react";
import "@testing-library/jest-dom";
import { FormProvider, useForm } from "react-hook-form";
import { Provider } from "react-redux";
import { app_store } from "@me/data-rtk";
import SingleCardContainer from "./single-card-container";
import * as selectors from "@me/data-rtk";
import { promo10006123 } from "../../../../../shared/event-flow-mocks/promotions-mock";

const Wrapper = props => {
  const formMethods = useForm<any>({
    name: "Daelmans Stroopwafels - 148569 - S - 2 - 27 GMHBC Sep Feature 2023",
    startDate: "2023-09-27",
    endDate: "2023-10-31",
    divisionIds: ["27"],
    divisions: [],
    offerAllowances: [
      {
        divisionIds: ["27"],
        periscopeIds: ["897205"],
      },
    ],
    promotionsList: [
      {
        periscopePromoId: "897198",
        forecast: {
          id: "64eceb49740df07e415b013e",
          quadrant: "3",
          forecastSales: 6111,
          forecastUnits: 1574,
          forecastAgp: -24,
          forecastAgpPercent: -0.39,
          markdown: 3972,
          markdownPercent: 50,
          incrementalUnits: 322,
          incrementalPercent: 25.77,
          incrementalSales: -1905,
          incrementalSalesPercent: -23.87,
          incrementalAgp: -2604,
          incrementalAgpPercent: -101.43,
          vendorFunding: 689,
          coverage: 17.35,
          coveragePercent: 17.35,
          periscopePromotionId: "897198",
          promotionObjectId: "64eceb0b752c0072378515f0",
          planEvent: "64eceaa21e739b08a1678161",
          lastUpdated: "2023-08-28T18:45:29.980Z",
          lastUpdateBy: "meupp-periscope-bk-azr",
        },
      },
    ],
    promotionsLists: [
      {
        promotionsList: [
          {
            periscopePromoId: "897198",
            forecast: {
              id: "64eceb49740df07e415b013e",
              quadrant: "3",
              forecastSales: 6111,
              forecastUnits: 1574,
              forecastAgp: -24,
              forecastAgpPercent: -0.39,
              markdown: 3972,
              markdownPercent: 50,
              incrementalUnits: 322,
              incrementalPercent: 25.77,
              incrementalSales: -1905,
              incrementalSalesPercent: -23.87,
              incrementalAgp: -2604,
              incrementalAgpPercent: -101.43,
              vendorFunding: 689,
              coverage: 17.35,
              coveragePercent: 17.35,
              periscopePromotionId: "897198",
              promotionObjectId: "64eceb0b752c0072378515f0",
              planEvent: "64eceaa21e739b08a1678161",
              lastUpdated: "2023-08-28T18:45:29.980Z",
              lastUpdateBy: "meupp-periscope-bk-azr",
            },
          },
        ],
      },
    ],
  });
  return (
    <Provider store={app_store}>
      <FormProvider {...formMethods}>{props.children}</FormProvider>
    </Provider>
  );
};

jest.mock("react-pdf", () => ({
  Document: jest.fn(({ children }) => children),
  Page: jest.fn(() => <div data-testid="mock-page"></div>),
  pdfjs: {
    GlobalWorkerOptions: {
      workerSrc: "",
    },
    version: "2.10.377",
  },
}));

describe("Periscope ID Test Suite", () => {
  class ResizeObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
  }
  beforeEach(() => {
    window.ResizeObserver = ResizeObserver as any;
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      switch (sel_name) {
        case "event_plan_card_update_details":
          return {
            data: {
              EVENT_PLAN_CARD: 0,
              isPromotionAdded: false,
            },
          };
        case "event_details_data": {
          return {
            data: {
              name: "Annies Homegrown Snacks Box - 84882 - S - 9 - 27 Week 50 Insert 2023",
              startDate: "2023-12-13",
              endDate: "2023-12-19",
              divisionIds: ["27"],
              divisions: [],
              planProductGroups: [
                {
                  planProductGroupId: "636bde8d9665d0440e006df8",
                  sourceProductGroupId: 184779,
                  name: "Annies Homegrown Snacks Box - 84882",
                  divisionId: "27",
                  smicGroupCode: 2,
                  smicCategoryCode: "0202",
                  supplier: {
                    supplierId: "515",
                    supplierName: "GENERAL MILLS",
                  },
                  itemCount: 9,
                  displayInd: false,
                  simsVendors: ["006446"],
                  simsSubAccntVendors: ["006446-001"],
                  negotiationSimsVendors: ["006446"],
                  unitType: 1,
                },
              ],
              storeGroups: [
                {
                  storeGroupId: "6453d8fa31c0c4e30d8f11d3",
                  sourceStoreGroupId: "EDM",
                  storeGroupName: "Seattle All Stores",
                  storeGroupType: {
                    groupType: "S",
                    storeGrpTypeName: "Division",
                    groupInd: "D",
                  },
                  divisionIds: ["27"],
                  storeCount: 220,
                },
              ],
              storeGroupType: "",
              allowance: [],
              isEventChanged: false,
              id: "650ab6854deca247948296c7",
              forecast: {
                forecastSales: 0,
                forecastUnits: 0,
                quadrant: "",
              },
              eventCreationVehicle: {
                vehicleId: "63d4d1d425fbce4fd8d3165f",
                vehicleNm: "27 Week 50 Insert 2023",
                sourceVehicleSk: 43000,
                startDate: "2023-12-13",
                endDate: "2023-12-19",
                vehicleType: {
                  vehicleTypeId: "636abba1b426ee543a94d3ac",
                  sourceVehicleTypeSk: 198,
                  vehicleTypNm: "insrt",
                  vehicleTypDesc: "Weekly Insert",
                },
              },
              planProductGroupPricing: {
                planProductGroup: {
                  planProductGroupId: "",
                  sourceProductGroupId: "",
                  name: "",
                  divisionId: "",
                  smicGroupCode: "",
                  smicCategoryCode: "",
                  supplier: {
                    id: "",
                    supplierId: "",
                    supplierName: "",
                  },
                },
                quadrant: "",
                priceAmount: "",
                priceFactor: "",
                priceUnit: "",
                priceLimitQty: "",
                forecastAmt: "",
                userName: "",
                supplier: "",
              },
              startWeekVehicle: "27 Week 50 Insert 2023",
              planStoreGroupType: {
                groupType: "S",
                storeGrpTypeName: "Division",
                groupInd: "D",
              },
              vehicleType: "Weekly Insert",
              eventStatus: "Draft",
              eventType: "DP",
              inValidPromotions: [],
              inValidAllowances: [],
              isChangeEventTypeVisible: false,
              planEventIdNbr: 10014676,
              sourceEventType: "ECP",
              pricing: [
                {
                  planProductGroup: {
                    planProductGroupId: "636bde8d9665d0440e006df8",
                    sourceProductGroupId: 184779,
                    name: "Annies Homegrown Snacks Box - 84882",
                    divisionId: "27",
                    smicGroupCode: 2,
                    smicCategoryCode: "0202",
                    supplier: {
                      supplierId: "515",
                      supplierName: "GENERAL MILLS",
                    },
                    itemCount: 9,
                    displayInd: false,
                    simsVendors: ["006446"],
                    simsSubAccntVendors: ["006446-001"],
                    negotiationSimsVendors: ["006446"],
                    unitType: 1,
                  },
                  quadrant: "",
                  priceAmount: "",
                  priceFactor: "0",
                  priceLimitQty: "1",
                  prcMtd: "",
                  promoType: "BUY_ONE_GET_ONE",
                },
              ],
              offerAllowances: [
                {
                  id: "650ab7299ccba9649e8e8795",
                  offerNumber: 7011588,
                  origOfferNumber: 0,
                  planEvent: "650ab6854deca247948296c7",
                  createInd: "TC",
                  minAggrAllowanceStartDate: "2023-12-13",
                  maxAggrAllowanceEndDate: "2023-12-19",
                  divisionIds: ["27"],
                  allowances: [
                    {
                      id: "650ab7299ccba9649e8e8794",
                      allowanceIdNbr: 701158801,
                      vendorNbr: "",
                      allowanceStatus: "Draft",
                      leadDistributorInfos: [],
                      vehicle: {
                        vehicleId: "63d4d1d425fbce4fd8d3165f",
                        vehicleNm: "27 Week 50 Insert 2023",
                        sourceVehicleSk: 43000,
                        startDate: "2023-12-13",
                        endDate: "2023-12-19",
                        vehicleType: {
                          vehicleTypeId: "636abba1b426ee543a94d3ac",
                          sourceVehicleTypeSk: 198,
                          vehicleTypNm: "insrt",
                          vehicleTypDesc: "Weekly Insert",
                        },
                      },
                      defaultAllowanceDates: {
                        allowanceStartDate: "2023-12-13",
                        allowanceEndDate: "2023-12-19",
                        performStartDate: "2023-12-13",
                        performEndDate: "2023-12-19",
                        orderStartDate: "0001-01-01",
                        orderEndDate: "0001-01-01",
                        shipStartDate: "0001-01-01",
                        shipEndDate: "0001-01-01",
                        arrivalStartDate: "0001-01-01",
                        arrivalEndDate: "0001-01-01",
                        overrideInd: false,
                        notPreSaved: false,
                      },
                      allowanceStartDate: "2023-12-13",
                      allowanceEndDate: "2023-12-19",
                      performStartDate: "2023-12-13",
                      performEndDate: "2023-12-19",
                      orderStartDate: "0001-01-01",
                      orderEndDate: "0001-01-01",
                      shipStartDate: "0001-01-01",
                      shipEndDate: "0001-01-01",
                      arrivalStartDate: "0001-01-01",
                      arrivalEndDate: "0001-01-01",
                      cancelledTs: "0001-01-03",
                      processTs: "0001-01-03",
                      lastApprovedDate: "0001-01-01",
                      lastApprovedUserId: "",
                      allowanceBillingInfo: {
                        vendorIds: [
                          {
                            vendorNbr: "006446",
                            vendorSubAccount: "001",
                            costArea: "4",
                            fullVendorNbr: "006446-001-4",
                          },
                          {
                            vendorNbr: "006446",
                            vendorSubAccount: "001",
                            costArea: "2",
                            fullVendorNbr: "006446-001-2",
                          },
                          {
                            vendorNbr: "006446",
                            vendorSubAccount: "001",
                            costArea: "1",
                            fullVendorNbr: "006446-001-1",
                          },
                        ],
                        absMerchVendor: "006446-001",
                        absVendorName: "KEHE DISTRIBUTORS",
                        absVendorPaymentType: "Deduct",
                        acPayableVendorNbr: "110056",
                        acReceivableVendorNbr: "082099",
                        suggestedVendorPaymentType: "",
                        suggestedAcPayableVendorNbr: "",
                        suggestedAcReceivableVendorNbr: "",
                        billingContactName: "<EMAIL>        ",
                        billingContactEmail: "<EMAIL>",
                        vendorComment: "",
                        vendorOfferTrackingNbr: "",
                        vendorBillingList: [
                          {
                            billingContactName: "<EMAIL>        ",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "BRANDON SWEET            ",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "<EMAIL>        ",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "<EMAIL>     ",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "<EMAIL>        ",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "BRANDON SWEET            ",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "<EMAIL>        ",
                            billingContactEmail: "<EMAIL>",
                          },
                          {
                            billingContactName: "<EMAIL>     ",
                            billingContactEmail: "<EMAIL>",
                          },
                        ],
                        vendorItemCount: 7,
                        source: "SIMS_VENDOR",
                      },
                      allowanceBillingInfos: [
                        {
                          vendorIds: [
                            {
                              vendorNbr: "006446",
                              vendorSubAccount: "001",
                              costArea: "4",
                              fullVendorNbr: "006446-001-4",
                            },
                            {
                              vendorNbr: "006446",
                              vendorSubAccount: "001",
                              costArea: "2",
                              fullVendorNbr: "006446-001-2",
                            },
                            {
                              vendorNbr: "006446",
                              vendorSubAccount: "001",
                              costArea: "1",
                              fullVendorNbr: "006446-001-1",
                            },
                          ],
                          absMerchVendor: "006446-001",
                          absVendorName: "KEHE DISTRIBUTORS",
                          absVendorPaymentType: "D",
                          acPayableVendorNbr: "110056",
                          acReceivableVendorNbr: "082099",
                          billingContactName: "<EMAIL>        ",
                          billingContactEmail: "<EMAIL>",
                          vendorComment: "",
                          vendorOfferTrackingNbr: "",
                          vendorBillingList: [
                            {
                              billingContactName: "<EMAIL>        ",
                              billingContactEmail: "<EMAIL>",
                            },
                            {
                              billingContactName: "BRANDON SWEET            ",
                              billingContactEmail: "<EMAIL>",
                            },
                            {
                              billingContactName: "<EMAIL>        ",
                              billingContactEmail: "<EMAIL>",
                            },
                            {
                              billingContactName: "<EMAIL>     ",
                              billingContactEmail: "<EMAIL>",
                            },
                            {
                              billingContactName: "<EMAIL>        ",
                              billingContactEmail: "<EMAIL>",
                            },
                            {
                              billingContactName: "BRANDON SWEET            ",
                              billingContactEmail: "<EMAIL>",
                            },
                            {
                              billingContactName: "<EMAIL>        ",
                              billingContactEmail: "<EMAIL>",
                            },
                            {
                              billingContactName: "<EMAIL>     ",
                              billingContactEmail: "<EMAIL>",
                            },
                          ],
                          vendorItemCount: 7,
                          source: "SIMS_VENDOR",
                        },
                      ],
                      storeGroups: [
                        {
                          storeGroupId: "6453d8fa31c0c4e30d8f11d3",
                          sourceStoreGroupId: "EDM",
                          storeGroupName: "Seattle All Stores",
                          storeGroupType: {
                            groupType: "S",
                            storeGrpTypeName: "Division",
                            groupInd: "D",
                          },
                          divisionIds: ["27"],
                          storeCount: 220,
                        },
                      ],
                      performance: {
                        allowanceCd: "T",
                        perfCode1: "20",
                        perfCode2: "52",
                        payType: "B",
                        allwPerfId: "63a3a12743a6cee87995b834",
                      },
                      planProductGroups: [
                        {
                          planProductGroupId: "636bde8d9665d0440e006df8",
                          sourceProductGroupId: 184779,
                          name: "Annies Homegrown Snacks Box - 84882",
                          divisionId: "27",
                          smicGroupCode: 2,
                          smicCategoryCode: "0202",
                          supplier: {
                            supplierId: "515",
                            supplierName: "GENERAL MILLS",
                          },
                          itemCount: 9,
                          displayInd: false,
                          simsVendors: ["006446"],
                          simsSubAccntVendors: ["006446-001"],
                          negotiationSimsVendors: ["006446"],
                          unitType: 1,
                        },
                      ],
                      allowanceItems: [
                        {
                          itemId: "2010289",
                          primaryUpc: "001356200018",
                          consumerUpc: "001356200018",
                          caseUpc: "0000000000000",
                          itemUpcs: ["0000000000000", "001356200018"],
                          consumerUpcs: [
                            {
                              upc: "001356200018",
                              rog: "SSPK",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356200018",
                              rog: "SSEA",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356200018",
                              rog: "SACG",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                          ],
                          effectiveEndDate: "0001-01-01",
                          vendorPackConversionFactor: 1,
                          packWhse: 12,
                          ringType: 0,
                          uom: "EA",
                          allowanceItemComps: [
                            {
                              performance: {
                                allowanceCd: "T",
                                perfCode1: "20",
                                perfCode2: "52",
                                payType: "B",
                                allwPerfId: "63a3a12743a6cee87995b834",
                              },
                              allowanceTypeStartDate: "2023-12-13",
                              allowanceTypeEndDate: "2023-12-19",
                              allowAmount: 0.3,
                            },
                          ],
                        },
                        {
                          itemId: "2020113",
                          primaryUpc: "001356200015",
                          consumerUpc: "001356200015",
                          caseUpc: "0001356200015",
                          itemUpcs: ["0001356200015", "001356200015"],
                          consumerUpcs: [
                            {
                              upc: "001356200015",
                              rog: "SSEA",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356200015",
                              rog: "SSPK",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356200015",
                              rog: "SACG",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                          ],
                          effectiveEndDate: "0001-01-01",
                          vendorPackConversionFactor: 1,
                          packWhse: 12,
                          ringType: 0,
                          uom: "EA",
                          allowanceItemComps: [
                            {
                              performance: {
                                allowanceCd: "T",
                                perfCode1: "20",
                                perfCode2: "52",
                                payType: "B",
                                allwPerfId: "63a3a12743a6cee87995b834",
                              },
                              allowanceTypeStartDate: "2023-12-13",
                              allowanceTypeEndDate: "2023-12-19",
                              allowAmount: 0.3,
                            },
                          ],
                        },
                        {
                          itemId: "2020118",
                          primaryUpc: "001356230215",
                          consumerUpc: "001356230215",
                          caseUpc: "0001356230215",
                          itemUpcs: ["0001356230215", "001356230215"],
                          consumerUpcs: [
                            {
                              upc: "001356230215",
                              rog: "SSPK",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356230215",
                              rog: "SSEA",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356230215",
                              rog: "SACG",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                          ],
                          effectiveEndDate: "0001-01-01",
                          vendorPackConversionFactor: 1,
                          packWhse: 12,
                          ringType: 0,
                          uom: "EA",
                          allowanceItemComps: [
                            {
                              performance: {
                                allowanceCd: "T",
                                perfCode1: "20",
                                perfCode2: "52",
                                payType: "B",
                                allwPerfId: "63a3a12743a6cee87995b834",
                              },
                              allowanceTypeStartDate: "2023-12-13",
                              allowanceTypeEndDate: "2023-12-19",
                              allowAmount: 0.3,
                            },
                          ],
                        },
                        {
                          itemId: "2020201",
                          primaryUpc: "001356210923",
                          consumerUpc: "001356210923",
                          caseUpc: "0001356210923",
                          itemUpcs: ["0001356210923", "001356210923"],
                          consumerUpcs: [
                            {
                              upc: "001356210923",
                              rog: "SSPK",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356210923",
                              rog: "SSEA",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356210923",
                              rog: "SACG",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                          ],
                          effectiveEndDate: "0001-01-01",
                          vendorPackConversionFactor: 1,
                          packWhse: 12,
                          ringType: 0,
                          uom: "EA",
                          allowanceItemComps: [
                            {
                              performance: {
                                allowanceCd: "T",
                                perfCode1: "20",
                                perfCode2: "52",
                                payType: "B",
                                allwPerfId: "63a3a12743a6cee87995b834",
                              },
                              allowanceTypeStartDate: "2023-12-13",
                              allowanceTypeEndDate: "2023-12-19",
                              allowAmount: 0.3,
                            },
                          ],
                        },
                        {
                          itemId: "2020228",
                          primaryUpc: "001356211645",
                          consumerUpc: "001356211645",
                          caseUpc: "0001356211645",
                          itemUpcs: ["0001356211645", "001356211645"],
                          consumerUpcs: [
                            {
                              upc: "001356211645",
                              rog: "SACG",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356211645",
                              rog: "SSEA",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                          ],
                          effectiveEndDate: "0001-01-01",
                          vendorPackConversionFactor: 1,
                          packWhse: 12,
                          ringType: 0,
                          uom: "EA",
                          allowanceItemComps: [
                            {
                              performance: {
                                allowanceCd: "T",
                                perfCode1: "20",
                                perfCode2: "52",
                                payType: "B",
                                allwPerfId: "63a3a12743a6cee87995b834",
                              },
                              allowanceTypeStartDate: "2023-12-13",
                              allowanceTypeEndDate: "2023-12-19",
                              allowAmount: 0.3,
                            },
                          ],
                        },
                        {
                          itemId: "2020393",
                          primaryUpc: "001356200053",
                          consumerUpc: "001356200053",
                          caseUpc: "0000000000000",
                          itemUpcs: ["0000000000000", "001356200053"],
                          consumerUpcs: [
                            {
                              upc: "001356200053",
                              rog: "SSPK",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356200053",
                              rog: "SACG",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356200053",
                              rog: "SSEA",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                          ],
                          effectiveEndDate: "0001-01-01",
                          vendorPackConversionFactor: 1,
                          packWhse: 12,
                          ringType: 0,
                          uom: "EA",
                          allowanceItemComps: [
                            {
                              performance: {
                                allowanceCd: "T",
                                perfCode1: "20",
                                perfCode2: "52",
                                payType: "B",
                                allwPerfId: "63a3a12743a6cee87995b834",
                              },
                              allowanceTypeStartDate: "2023-12-13",
                              allowanceTypeEndDate: "2023-12-19",
                              allowAmount: 0.3,
                            },
                          ],
                        },
                        {
                          itemId: "2020491",
                          primaryUpc: "001356211722",
                          consumerUpc: "001356211722",
                          caseUpc: "0001356211722",
                          itemUpcs: ["0001356211722", "001356211722"],
                          consumerUpcs: [
                            {
                              upc: "001356211722",
                              rog: "SSPK",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356211722",
                              rog: "SSEA",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                            {
                              upc: "001356211722",
                              rog: "SACG",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7.5 OZ ",
                            },
                          ],
                          effectiveEndDate: "0001-01-01",
                          vendorPackConversionFactor: 1,
                          packWhse: 12,
                          ringType: 0,
                          uom: "EA",
                          allowanceItemComps: [
                            {
                              performance: {
                                allowanceCd: "T",
                                perfCode1: "20",
                                perfCode2: "52",
                                payType: "B",
                                allwPerfId: "63a3a12743a6cee87995b834",
                              },
                              allowanceTypeStartDate: "2023-12-13",
                              allowanceTypeEndDate: "2023-12-19",
                              allowAmount: 0.3,
                            },
                          ],
                        },
                        {
                          itemId: "2020895",
                          primaryUpc: "001356230261",
                          consumerUpc: "001356230261",
                          caseUpc: "0001356230261",
                          itemUpcs: ["0001356230261", "001356230261"],
                          consumerUpcs: [
                            {
                              upc: "001356230261",
                              rog: "SSEA",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7 OZ   ",
                            },
                            {
                              upc: "001356230261",
                              rog: "SACG",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7 OZ   ",
                            },
                            {
                              upc: "001356230261",
                              rog: "SSPK",
                              primaryInd: true,
                              labelSize: "M",
                              packDesc: "012",
                              sizeDesc: "7 OZ   ",
                            },
                          ],
                          effectiveEndDate: "0001-01-01",
                          vendorPackConversionFactor: 1,
                          packWhse: 12,
                          ringType: 0,
                          uom: "EA",
                          allowanceItemComps: [
                            {
                              performance: {
                                allowanceCd: "T",
                                perfCode1: "20",
                                perfCode2: "52",
                                payType: "B",
                                allwPerfId: "63a3a12743a6cee87995b834",
                              },
                              allowanceTypeStartDate: "2023-12-13",
                              allowanceTypeEndDate: "2023-12-19",
                              allowAmount: 0.3,
                            },
                          ],
                        },
                      ],
                      location: {
                        locationId: "639033d538196056762e6e28",
                        locationName: "27 - Seattle",
                        distCenter: "DDSE",
                        locationTypeCd: "D",
                        locationTypeCdEnum: "RETAIL_DIVISION",
                      },
                      headerFlatAmt: 0,
                      createTs: 1695201065550,
                      createUserId: "RROUT04",
                      lastUpdTs: 1695201065550,
                      lastUpdUserId: "RROUT04",
                      costAreaDesc: "",
                      leadDistributorInd: true,
                      includeInd: true,
                      excludedItems: [
                        {
                          itemId: "2021027",
                          excludeInd: "NOT_FOUND",
                        },
                      ],
                      allowanceProcessStatus: "",
                      allowanceDateOffsets: {
                        allowanceTypes: ["SCAN", "HEADER_FLAT", "ITEM_FLAT"],
                        startDateOffset: 0,
                        endDateOffset: 0,
                        defaultOrderLeadTimeDays: 0,
                        defaultShipTransitDays: 0,
                      },
                    },
                  ],
                  periscopeIds: ["902333"],
                  createUser: {
                    userId: "RROUT04",
                    name: "Ravi Kiran Routhu (Contractor)",
                    type: "Merchant",
                    userRoles: [
                      "az-meupp-nonprod-promointeditor",
                      "az-meupp-nonprod-promoitintadmin",
                    ],
                    createTs: "2023-09-20T09:11:05.549Z",
                  },
                  updateUser: {
                    userId: "RROUT04",
                    name: "Ravi Kiran Routhu (Contractor)",
                    type: "Merchant",
                    userRoles: [
                      "az-meupp-nonprod-promointeditor",
                      "az-meupp-nonprod-promoitintadmin",
                    ],
                    createTs: "2023-09-20T09:11:05.550Z",
                  },
                  allowanceChangeStatus: "UPDATED",
                  createTempWorkAllowanceId: "650ab6e29ccba9649e8e8793",
                  offerId: "650ab7299ccba9649e8e8795",
                },
              ],
              promotionsList: [promo10006123],
              allowances: ["650ab7299ccba9649e8e8795"],
              promotions: ["650ab74a3b01e74b8d16ca09"],
              createUser: {
                userId: "RROUT04",
                name: "Ravi Kiran Routhu (Contractor)",
                type: "Merchant",
                userRoles: [
                  "az-meupp-nonprod-promointeditor",
                  "az-meupp-nonprod-promoitintadmin",
                ],
                createTs: "2023-09-20T09:08:21.408Z",
              },
              updateUser: {
                userId: "RROUT04",
                name: "Ravi Kiran Routhu (Contractor)",
                type: "Merchant",
                userRoles: [
                  "az-meupp-nonprod-promointeditor",
                  "az-meupp-nonprod-promoitintadmin",
                ],
                createTs: "2023-09-20T09:08:21.408Z",
              },
              planEventWorkFlowType: "NOT FOUND",
              eventTypeEnum: "DP",
              promotionsLists: [
                {
                  promotionsList: [promo10006123],
                },
              ],
              promoProductGroup: "Annies Homegrown Snacks Box - 84882",
              storeGroupName: "Seattle All Stores",
            },
          };
        }
        case "event_status_change_indicators":
          return {
            data: { isEventStatusChanged: false },
          };
        case "is_event_edit_enable": {
          return {
            data: {
              isEventCardOpen: {},
            },
          };
        }
        case "offer_card_configutation_rn": {
          return {
            data: {
              editCardConfig: {},
              openCardConfig: {},
            },
          };
        }
        case "promo_card_configutation_rn":
          return {
            data: {
              openCardConfig: {},
              editCardConfig: {},
              isAddNewPromo: {},
            },
          };
        case "by_pass_offer_allowance": {
          return {
            data: { isByPassOfferAllowanceSelected: false },
          };
        }
        case "promotion_edit_enable_configutation_rn":
          return {
            data: { isEditPromotion: {} },
          };
        case "promotion_regular_price_data":
          return {
            data: {
              minRegularPrice: 0,
              maxRegularPrice: 0,
              maxListCost: 0,
              minListCost: 0,
              maxListAGP: 0,
              minListAGP: 0,
            },
          };

        case "allowance_temp_work":
          return {
            data: {},
          };
        case "offer_sub_card_configutation_rn": {
          return {
            data: {
              offerSubCardConfig: {},
            },
          };
        }
        case "promo_sub_card_configutation_rn": {
          return {
            data: {
              promoSubCardConfig: {},
            },
          };
        }
        case "eventProgressConfigData_rn":
          return {
            data: {},
          };
        default:
          break;
      }
    });
  });
  it("should render single card container correctly", () => {
    render(
      <Wrapper>
        <SingleCardContainer
          cardContainerData={{
            id: "650ab7299ccba9649e8e8794",
            allowanceIdNbr: 701158801,
            vendorNbr: "",
            allowanceStatus: "Draft",
            leadDistributorInfos: [],
            vehicle: {
              vehicleId: "63d4d1d425fbce4fd8d3165f",
              vehicleNm: "27 Week 50 Insert 2023",
              sourceVehicleSk: 43000,
              startDate: "2023-12-13",
              endDate: "2023-12-19",
              vehicleType: {
                vehicleTypeId: "636abba1b426ee543a94d3ac",
                sourceVehicleTypeSk: 198,
                vehicleTypNm: "insrt",
                vehicleTypDesc: "Weekly Insert",
              },
            },
            defaultAllowanceDates: {
              allowanceStartDate: "2023-12-13",
              allowanceEndDate: "2023-12-19",
              performStartDate: "2023-12-13",
              performEndDate: "2023-12-19",
              orderStartDate: "0001-01-01",
              orderEndDate: "0001-01-01",
              shipStartDate: "0001-01-01",
              shipEndDate: "0001-01-01",
              arrivalStartDate: "0001-01-01",
              arrivalEndDate: "0001-01-01",
              overrideInd: false,
              notPreSaved: false,
            },
            allowanceStartDate: "2023-12-13",
            allowanceEndDate: "2023-12-19",
            performStartDate: "2023-12-13",
            performEndDate: "2023-12-19",
            orderStartDate: "0001-01-01",
            orderEndDate: "0001-01-01",
            shipStartDate: "0001-01-01",
            shipEndDate: "0001-01-01",
            arrivalStartDate: "0001-01-01",
            arrivalEndDate: "0001-01-01",
            cancelledTs: "0001-01-03",
            processTs: "0001-01-03",
            lastApprovedDate: "0001-01-01",
            lastApprovedUserId: "",
            allowanceBillingInfo: {
              vendorIds: [
                {
                  vendorNbr: "006446",
                  vendorSubAccount: "001",
                  costArea: "4",
                  fullVendorNbr: "006446-001-4",
                },
                {
                  vendorNbr: "006446",
                  vendorSubAccount: "001",
                  costArea: "2",
                  fullVendorNbr: "006446-001-2",
                },
                {
                  vendorNbr: "006446",
                  vendorSubAccount: "001",
                  costArea: "1",
                  fullVendorNbr: "006446-001-1",
                },
              ],
              absMerchVendor: "006446-001",
              absVendorName: "KEHE DISTRIBUTORS",
              absVendorPaymentType: "Deduct",
              acPayableVendorNbr: "110056",
              acReceivableVendorNbr: "082099",
              suggestedVendorPaymentType: "",
              suggestedAcPayableVendorNbr: "",
              suggestedAcReceivableVendorNbr: "",
              billingContactName: "<EMAIL>        ",
              billingContactEmail: "<EMAIL>",
              vendorComment: "",
              vendorOfferTrackingNbr: "",
              vendorBillingList: [
                {
                  billingContactName: "<EMAIL>        ",
                  billingContactEmail: "<EMAIL>",
                },
                {
                  billingContactName: "BRANDON SWEET            ",
                  billingContactEmail: "<EMAIL>",
                },
                {
                  billingContactName: "<EMAIL>        ",
                  billingContactEmail: "<EMAIL>",
                },
                {
                  billingContactName: "<EMAIL>     ",
                  billingContactEmail: "<EMAIL>",
                },
                {
                  billingContactName: "<EMAIL>        ",
                  billingContactEmail: "<EMAIL>",
                },
                {
                  billingContactName: "BRANDON SWEET            ",
                  billingContactEmail: "<EMAIL>",
                },
                {
                  billingContactName: "<EMAIL>        ",
                  billingContactEmail: "<EMAIL>",
                },
                {
                  billingContactName: "<EMAIL>     ",
                  billingContactEmail: "<EMAIL>",
                },
              ],
              vendorItemCount: 7,
              source: "SIMS_VENDOR",
            },
            allowanceBillingInfos: [
              {
                vendorIds: [
                  {
                    vendorNbr: "006446",
                    vendorSubAccount: "001",
                    costArea: "4",
                    fullVendorNbr: "006446-001-4",
                  },
                  {
                    vendorNbr: "006446",
                    vendorSubAccount: "001",
                    costArea: "2",
                    fullVendorNbr: "006446-001-2",
                  },
                  {
                    vendorNbr: "006446",
                    vendorSubAccount: "001",
                    costArea: "1",
                    fullVendorNbr: "006446-001-1",
                  },
                ],
                absMerchVendor: "006446-001",
                absVendorName: "KEHE DISTRIBUTORS",
                absVendorPaymentType: "D",
                acPayableVendorNbr: "110056",
                acReceivableVendorNbr: "082099",
                billingContactName: "<EMAIL>        ",
                billingContactEmail: "<EMAIL>",
                vendorComment: "",
                vendorOfferTrackingNbr: "",
                vendorBillingList: [
                  {
                    billingContactName: "<EMAIL>        ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "BRANDON SWEET            ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>        ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>     ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>        ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "BRANDON SWEET            ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>        ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>     ",
                    billingContactEmail: "<EMAIL>",
                  },
                ],
                vendorItemCount: 7,
                source: "SIMS_VENDOR",
              },
            ],
            storeGroups: [
              {
                storeGroupId: "6453d8fa31c0c4e30d8f11d3",
                sourceStoreGroupId: "EDM",
                storeGroupName: "Seattle All Stores",
                storeGroupType: {
                  groupType: "S",
                  storeGrpTypeName: "Division",
                  groupInd: "D",
                },
                divisionIds: ["27"],
                storeCount: 220,
              },
            ],
            performance: {
              allowanceCd: "T",
              perfCode1: "20",
              perfCode2: "52",
              payType: "B",
              allwPerfId: "63a3a12743a6cee87995b834",
            },
            planProductGroups: [
              {
                planProductGroupId: "636bde8d9665d0440e006df8",
                sourceProductGroupId: 184779,
                name: "Annies Homegrown Snacks Box - 84882",
                divisionId: "27",
                smicGroupCode: 2,
                smicCategoryCode: "0202",
                supplier: {
                  supplierId: "515",
                  supplierName: "GENERAL MILLS",
                },
                itemCount: 9,
                displayInd: false,
                simsVendors: ["006446"],
                simsSubAccntVendors: ["006446-001"],
                negotiationSimsVendors: ["006446"],
                unitType: 1,
              },
            ],
            allowanceItems: [
              {
                itemId: "2010289",
                primaryUpc: "001356200018",
                consumerUpc: "001356200018",
                caseUpc: "0000000000000",
                itemUpcs: ["0000000000000", "001356200018"],
                consumerUpcs: [
                  {
                    upc: "001356200018",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356200018",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356200018",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                ],
                effectiveEndDate: "0001-01-01",
                vendorPackConversionFactor: 1,
                packWhse: 12,
                ringType: 0,
                uom: "EA",
                allowanceItemComps: [
                  {
                    performance: {
                      allowanceCd: "T",
                      perfCode1: "20",
                      perfCode2: "52",
                      payType: "B",
                      allwPerfId: "63a3a12743a6cee87995b834",
                    },
                    allowanceTypeStartDate: "2023-12-13",
                    allowanceTypeEndDate: "2023-12-19",
                    allowAmount: 0.3,
                  },
                ],
              },
              {
                itemId: "2020113",
                primaryUpc: "001356200015",
                consumerUpc: "001356200015",
                caseUpc: "0001356200015",
                itemUpcs: ["0001356200015", "001356200015"],
                consumerUpcs: [
                  {
                    upc: "001356200015",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356200015",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356200015",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                ],
                effectiveEndDate: "0001-01-01",
                vendorPackConversionFactor: 1,
                packWhse: 12,
                ringType: 0,
                uom: "EA",
                allowanceItemComps: [
                  {
                    performance: {
                      allowanceCd: "T",
                      perfCode1: "20",
                      perfCode2: "52",
                      payType: "B",
                      allwPerfId: "63a3a12743a6cee87995b834",
                    },
                    allowanceTypeStartDate: "2023-12-13",
                    allowanceTypeEndDate: "2023-12-19",
                    allowAmount: 0.3,
                  },
                ],
              },
              {
                itemId: "2020118",
                primaryUpc: "001356230215",
                consumerUpc: "001356230215",
                caseUpc: "0001356230215",
                itemUpcs: ["0001356230215", "001356230215"],
                consumerUpcs: [
                  {
                    upc: "001356230215",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356230215",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356230215",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                ],
                effectiveEndDate: "0001-01-01",
                vendorPackConversionFactor: 1,
                packWhse: 12,
                ringType: 0,
                uom: "EA",
                allowanceItemComps: [
                  {
                    performance: {
                      allowanceCd: "T",
                      perfCode1: "20",
                      perfCode2: "52",
                      payType: "B",
                      allwPerfId: "63a3a12743a6cee87995b834",
                    },
                    allowanceTypeStartDate: "2023-12-13",
                    allowanceTypeEndDate: "2023-12-19",
                    allowAmount: 0.3,
                  },
                ],
              },
              {
                itemId: "2020201",
                primaryUpc: "001356210923",
                consumerUpc: "001356210923",
                caseUpc: "0001356210923",
                itemUpcs: ["0001356210923", "001356210923"],
                consumerUpcs: [
                  {
                    upc: "001356210923",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356210923",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356210923",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                ],
                effectiveEndDate: "0001-01-01",
                vendorPackConversionFactor: 1,
                packWhse: 12,
                ringType: 0,
                uom: "EA",
                allowanceItemComps: [
                  {
                    performance: {
                      allowanceCd: "T",
                      perfCode1: "20",
                      perfCode2: "52",
                      payType: "B",
                      allwPerfId: "63a3a12743a6cee87995b834",
                    },
                    allowanceTypeStartDate: "2023-12-13",
                    allowanceTypeEndDate: "2023-12-19",
                    allowAmount: 0.3,
                  },
                ],
              },
              {
                itemId: "2020228",
                primaryUpc: "001356211645",
                consumerUpc: "001356211645",
                caseUpc: "0001356211645",
                itemUpcs: ["0001356211645", "001356211645"],
                consumerUpcs: [
                  {
                    upc: "001356211645",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356211645",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                ],
                effectiveEndDate: "0001-01-01",
                vendorPackConversionFactor: 1,
                packWhse: 12,
                ringType: 0,
                uom: "EA",
                allowanceItemComps: [
                  {
                    performance: {
                      allowanceCd: "T",
                      perfCode1: "20",
                      perfCode2: "52",
                      payType: "B",
                      allwPerfId: "63a3a12743a6cee87995b834",
                    },
                    allowanceTypeStartDate: "2023-12-13",
                    allowanceTypeEndDate: "2023-12-19",
                    allowAmount: 0.3,
                  },
                ],
              },
              {
                itemId: "2020393",
                primaryUpc: "001356200053",
                consumerUpc: "001356200053",
                caseUpc: "0000000000000",
                itemUpcs: ["0000000000000", "001356200053"],
                consumerUpcs: [
                  {
                    upc: "001356200053",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356200053",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356200053",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                ],
                effectiveEndDate: "0001-01-01",
                vendorPackConversionFactor: 1,
                packWhse: 12,
                ringType: 0,
                uom: "EA",
                allowanceItemComps: [
                  {
                    performance: {
                      allowanceCd: "T",
                      perfCode1: "20",
                      perfCode2: "52",
                      payType: "B",
                      allwPerfId: "63a3a12743a6cee87995b834",
                    },
                    allowanceTypeStartDate: "2023-12-13",
                    allowanceTypeEndDate: "2023-12-19",
                    allowAmount: 0.3,
                  },
                ],
              },
              {
                itemId: "2020491",
                primaryUpc: "001356211722",
                consumerUpc: "001356211722",
                caseUpc: "0001356211722",
                itemUpcs: ["0001356211722", "001356211722"],
                consumerUpcs: [
                  {
                    upc: "001356211722",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356211722",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                  {
                    upc: "001356211722",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7.5 OZ ",
                  },
                ],
                effectiveEndDate: "0001-01-01",
                vendorPackConversionFactor: 1,
                packWhse: 12,
                ringType: 0,
                uom: "EA",
                allowanceItemComps: [
                  {
                    performance: {
                      allowanceCd: "T",
                      perfCode1: "20",
                      perfCode2: "52",
                      payType: "B",
                      allwPerfId: "63a3a12743a6cee87995b834",
                    },
                    allowanceTypeStartDate: "2023-12-13",
                    allowanceTypeEndDate: "2023-12-19",
                    allowAmount: 0.3,
                  },
                ],
              },
              {
                itemId: "2020895",
                primaryUpc: "001356230261",
                consumerUpc: "001356230261",
                caseUpc: "0001356230261",
                itemUpcs: ["0001356230261", "001356230261"],
                consumerUpcs: [
                  {
                    upc: "001356230261",
                    rog: "SSEA",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7 OZ   ",
                  },
                  {
                    upc: "001356230261",
                    rog: "SACG",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7 OZ   ",
                  },
                  {
                    upc: "001356230261",
                    rog: "SSPK",
                    primaryInd: true,
                    labelSize: "M",
                    packDesc: "012",
                    sizeDesc: "7 OZ   ",
                  },
                ],
                effectiveEndDate: "0001-01-01",
                vendorPackConversionFactor: 1,
                packWhse: 12,
                ringType: 0,
                uom: "EA",
                allowanceItemComps: [
                  {
                    performance: {
                      allowanceCd: "T",
                      perfCode1: "20",
                      perfCode2: "52",
                      payType: "B",
                      allwPerfId: "63a3a12743a6cee87995b834",
                    },
                    allowanceTypeStartDate: "2023-12-13",
                    allowanceTypeEndDate: "2023-12-19",
                    allowAmount: 0.3,
                  },
                ],
              },
            ],
            location: {
              locationId: "639033d538196056762e6e28",
              locationName: "27 - Seattle",
              distCenter: "DDSE",
              locationTypeCd: "D",
              locationTypeCdEnum: "RETAIL_DIVISION",
            },
            headerFlatAmt: 0,
            createTs: 1695201065550,
            createUserId: "RROUT04",
            lastUpdTs: 1695201065550,
            lastUpdUserId: "RROUT04",
            costAreaDesc: "",
            leadDistributorInd: true,
            includeInd: true,
            excludedItems: [
              {
                itemId: "2021027",
                excludeInd: "NOT_FOUND",
              },
            ],
            allowanceProcessStatus: "",
            allowanceDateOffsets: {
              allowanceTypes: ["SCAN", "HEADER_FLAT", "ITEM_FLAT"],
              startDateOffset: 0,
              endDateOffset: 0,
              defaultOrderLeadTimeDays: 0,
              defaultShipTransitDays: 0,
            },
          }}
          cardConfiguration={{
            section: "Allowance",
            key: "Allowance",
            title: "New Offer",
            offerTitle: "New Offer",
            isOpenCard: true,
            multiple: true,
            subtitle: "27 - Seattle | Start: Dec 13, 2023 | End: Dec 19, 2023",
            stepper: 0,
            allowanceTye: "Case",
            allowanceToBeCreated: "Both",
            fields: {
              steppers: [
                "Allowance Type & Performance",
                "Store Selection",
                "Allowance to be Created",
                "Allowance Dates",
                "DSD Allowance Dates",
                "Allowance Amounts",
                "DSD Case Allowance Dates",
                "DSD Case Allowance Amount",
                "DSD Scan Allowance Amount",
                "DSD Ship To Store Allowance Amount",
                "Default Billing Information",
                "DSD Default Billing Information",
                "Warehouse Case Allowance Dates",
                "Warehouse Case Allowance Amount",
                "Warehouse Scan Allowance Amount",
                "Warehouse Default Billing Information",
              ],
              "Allowance Type & Performance": {
                fields: {
                  allowanceType: {
                    label: "Allowance Type",
                    required: true,
                    registerField: "allowanceType",
                    registerKeyName: "performance.allowanceCd",
                    default: "Scan",
                    optionUrl: "allowanceType",
                    type: "select",
                    options: [],
                    displayLabel: "name",
                    apiUrl: "",
                    slice: "",
                    errors: {
                      required: {
                        text: "AllowanceType is Required",
                      },
                    },
                  },
                  performance: {
                    label: "Performance",
                    required: true,
                    default: "Price/Ad/Display",
                    registerField: "performance",
                    registerKeyName: "performance",
                    optionUrl: "performance",
                    options: [],
                    displayLabel: "performance",
                    type: "select",
                    apiUrl: "",
                    slice: "",
                    errors: {
                      required: {
                        text: "Performance is Required",
                      },
                    },
                  },
                  allowanceAmount: {
                    label: "Allowance Amount",
                    required: true,
                    registerField: "overrideHeaderFlatAmt",
                    registerKeyName: "overrideHeaderFlatAmt",
                    type: "number",
                    errors: {
                      required: {
                        text: "Allowance Amount is Required",
                      },
                      maxLength: {
                        length: 10,
                        text: "Max Length Reached",
                      },
                      formatError: {
                        pattern: {},
                        text: "Please enter valid Allowance Amount",
                      },
                    },
                  },
                  allowanceCreationVehicle: {
                    label: "AllowanceCreationVehicle",
                    required: true,
                    registerKeyName: "allowanceCreationVehicle",
                    registerField: "allowanceCreationVehicle",
                    options: [],
                  },
                  allowanceTypeAndPerformance: {
                    registerKeyName: "allowanceTypeAndPerformance",
                  },
                },
                allowanceTypeAndPerformance: [
                  "CASE",
                  "SCAN",
                  "SHIP_TO_STORE",
                  "HEADER_FLAT",
                  "ITEM_FLAT",
                ],
                allowanceToBeCreated: [
                  "Both",
                  "One Allowance: Warehouse, DSD, or Combined",
                  "Combined DSD",
                  "Separate Allowances By DSD Distributor",
                  "DSD Only",
                  "Warehouse Only",
                ],
                isOpen: true,
                label: "Allowance Type & Performance",
                disable: {
                  allowanceType: {
                    CASE: {
                      productSources: ["DSD", "WAREHOUSE"],
                    },
                    SCAN: {
                      productSources: ["WAREHOUSE", "BOTH"],
                    },
                    SHIP_TO_STORE: {
                      productSources: ["WAREHOUSE", "BOTH"],
                    },
                  },
                },
                allowanceScreenTypes: ["DP", "AO"],
                create: {
                  isEdit: false,
                  isView: false,
                  label: "Save & Continue",
                },
                edit: {
                  isEdit: true,
                  isView: true,
                  label: "Update & Continue",
                },
              },
              "Store Selection": {
                fields: {
                  planStoreGroupType: {
                    label: "Store Group Type",
                    required: true,
                    registerField: "planStoreGroupType",
                    registerKeyName: "performance.allowanceCd",
                    default: "Scan",
                    optionUrl: "planStoreGroupType",
                    type: "select",
                    options: [],
                    displayLabel: "name",
                    apiUrl: "",
                    slice: "",
                    errors: {
                      required: {
                        text: "Store Group Type is Required",
                      },
                    },
                  },
                  storeGroups: {
                    label: "Store Groups",
                    required: true,
                    default: "Price/Ad/Display",
                    registerField: "storeGroups",
                    registerKeyName: "performance",
                    optionUrl: "performance",
                    options: [],
                    displayLabel: "name",
                    type: "select",
                    apiUrl: "",
                    slice: "",
                    errors: {
                      required: {
                        text: "Store Group is Required",
                      },
                    },
                  },
                  allowanceStoreSelection: {
                    registerKeyName: "allowanceStoreSelection",
                  },
                },
                allowanceTypeAndPerformance: [
                  "SCAN",
                  "SHIP_TO_STORE",
                  "HEADER_FLAT",
                  "ITEM_FLAT",
                ],
                allowanceToBeCreated: [
                  "Both",
                  "One Allowance: Warehouse, DSD, or Combined",
                  "Combined DSD",
                  "Separate Allowances By DSD Distributor",
                ],
                isOpen: true,
                label: "Store Selection",
                disable: {
                  allowanceType: {
                    CASE: {
                      productSources: ["DSD", "WAREHOUSE"],
                    },
                    SCAN: {
                      productSources: ["WAREHOUSE", "BOTH"],
                    },
                    SHIP_TO_STORE: {
                      productSources: ["WAREHOUSE", "BOTH"],
                    },
                  },
                  createInd: ["AW", "LW"],
                  allowanceScreenTypes: ["AO"],
                },
                allowanceScreenTypes: ["AO"],
                create: {
                  isEdit: false,
                  isView: false,
                  label: "Save & Continue",
                },
                edit: {
                  isEdit: true,
                  isView: true,
                  label: "Update & Continue",
                },
              },
              "Allowance to be Created": {
                fields: {
                  allowanceToBeCreated: {
                    name: "",
                    key: "",
                    required: true,
                    registerField: "allowanceToBeCreated",
                    registerKeyName: "allowanceToBeCreated",
                    createIndex: "",
                    disable: true,
                    text: "Your selected Products and Store Group(s) are serviced by both DSD and Warehouse Items, if you wish to not create both Allowances change the default from Both. Be advised that this will result in some Store/Product promotions not being funded.",
                    options: [],
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "Allowance To Be Created is required",
                      },
                    },
                  },
                  allowanceSpecificFields: {
                    CASE: {
                      WAREHOUSE: {
                        name: "Warehouse Only",
                        key: "WAREHOUSE_DIST_CENTERS",
                        registerField: "allowanceToBeCreated",
                        createIndex: "CW",
                        disable: true,
                        text: "Your selected Products and Store Group(s) are serviced by both DSD and Warehouse Items, if you wish to not create both Allowances change the default from Both. Be advised that this will result in some Store/Product promotions not being funded.",
                        options: [
                          {
                            name: "Warehouse Only",
                            key: "WAREHOUSE_DIST_CENTERS",
                            routingKey: "WAREHOUSE_DIST_CENTERS",
                            default: true,
                            allowanceMap: {
                              WAREHOUSE_DIST_CENTERS: [],
                            },
                            createIndex: "CW",
                          },
                        ],
                      },
                      DSD: {
                        name: "DSD Only",
                        key: "DSD_LEAD_DISTRIBUTORS",
                        registerField: "allowanceToBeCreated",
                        disable: true,
                        createIndex: "CD",
                        text: "Your selected Products and Store Group(s) are serviced by both DSD and Warehouse Items, if you wish to not create both Allowances change the default from Both. Be advised that this will result in some Store/Product promotions not being funded.",
                        options: [
                          {
                            name: "DSD Only",
                            key: "DSD_LEAD_DISTRIBUTORS",
                            routingKey: "DSD_LEAD_DISTRIBUTORS",
                            allowanceMap: {
                              DSD_LEAD_DISTRIBUTORS: [],
                            },
                            default: true,
                            createIndex: "CD",
                          },
                        ],
                      },
                      BOTH: {
                        name: "Both",
                        key: "BOTH",
                        registerField: "allowanceToBeCreated",
                        disable: false,
                        text: "Your selected Products and Store Group(s) are serviced by both DSD and Warehouse Items, if you wish to not create both Allowances change the default from Both. Be advised that this will result in some Store/Product promotions not being funded.",
                        options: [
                          {
                            name: "Both",
                            key: "BOTH",
                            routingKey: "WAREHOUSE_DIST_CENTERS",
                            default: true,
                            allowanceMap: {
                              WAREHOUSE_DIST_CENTERS: [],
                              DSD_LEAD_DISTRIBUTORS: [],
                            },
                            createIndex: "CB",
                          },
                          {
                            name: "Warehouse Only",
                            key: "WAREHOUSE_DIST_CENTERS",
                            routingKey: "WAREHOUSE_DIST_CENTERS",
                            default: false,
                            allowanceMap: {
                              WAREHOUSE_DIST_CENTERS: [],
                            },
                            createIndex: "CW",
                          },
                          {
                            name: "DSD Only",
                            key: "DSD_LEAD_DISTRIBUTORS",
                            routingKey: "DSD_LEAD_DISTRIBUTORS",
                            allowanceMap: {
                              DSD_LEAD_DISTRIBUTORS: [],
                            },
                            default: false,
                            createIndex: "CD",
                          },
                        ],
                      },
                    },
                    SCAN: {
                      WAREHOUSE: {
                        name: "One Allowance: Warehouse, DSD, or Combined",
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        registerField: "allowanceToBeCreated",
                        createIndex: "TC",
                        disable: true,
                        text: "Select DSD Separate to create separate allowances that can be Billed for each Distributor or choose Combined to create only one Allowance.",
                        options: [
                          {
                            name: "One Allowance: Warehouse, DSD, or Combined",
                            key: "DSD_WHSE_RETAIL_DIVISION",
                            routingKey: "DSD_WHSE_RETAIL_DIVISION",
                            allowanceMap: {
                              DSD_WHSE_RETAIL_DIVISION: [],
                            },
                            default: false,
                            createIndex: "TC",
                          },
                          {
                            name: "Separate Allowances By DSD Distributor",
                            key: "DSD_LEAD_DISTRIBUTORS",
                            routingKey: "DSD_LEAD_DISTRIBUTORS",
                            allowanceMap: {
                              DSD_LEAD_DISTRIBUTORS: [],
                            },
                            default: false,
                            createIndex: "TS",
                          },
                        ],
                      },
                      DSD: {
                        name: "Separate Allowances By DSD Distributor",
                        key: "DSD_LEAD_DISTRIBUTORS",
                        registerField: "allowanceToBeCreated",
                        disable: false,
                        createIndex: "TS",
                        text: "Select DSD Separate to create separate allowances that can be Billed for each Distributor or choose Combined to create only one Allowance.",
                        options: [
                          {
                            name: "One Allowance: Warehouse, DSD, or Combined",
                            key: "DSD_WHSE_RETAIL_DIVISION",
                            routingKey: "DSD_WHSE_RETAIL_DIVISION",
                            allowanceMap: {
                              DSD_WHSE_RETAIL_DIVISION: [],
                            },
                            default: true,
                            createIndex: "TC",
                          },
                          {
                            name: "Separate Allowances By DSD Distributor",
                            key: "DSD_LEAD_DISTRIBUTORS",
                            routingKey: "DSD_LEAD_DISTRIBUTORS",
                            allowanceMap: {
                              DSD_LEAD_DISTRIBUTORS: [],
                            },
                            default: false,
                            createIndex: "TS",
                          },
                        ],
                      },
                      BOTH: {
                        name: "Both",
                        key: "BOTH",
                        registerField: "allowanceToBeCreated",
                        disable: false,
                        text: "Select DSD Separate to create separate allowances that can be Billed for each Distributor or choose Combined to create only one Allowance.",
                        options: [
                          {
                            name: "One Allowance: Warehouse, DSD, or Combined",
                            key: "DSD_WHSE_RETAIL_DIVISION",
                            routingKey: "DSD_WHSE_RETAIL_DIVISION",
                            default: false,
                            allowanceMap: {
                              DSD_WHSE_RETAIL_DIVISION: [],
                            },
                            createIndex: "TC",
                          },
                          {
                            name: "Separate Allowances By DSD Distributor",
                            key: "DSD_LEAD_DISTRIBUTORS",
                            routingKey: "DSD_LEAD_DISTRIBUTORS",
                            allowanceMap: {
                              DSD_WHSE_RETAIL_DIVISION: [],
                              DSD_LEAD_DISTRIBUTORS: [],
                            },
                            default: false,
                            createIndex: "TS",
                          },
                        ],
                      },
                    },
                    SHIP_TO_STORE: {
                      WAREHOUSE: {
                        name: "Combined DSD",
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        registerField: "allowanceToBeCreated",
                        createIndex: "SB",
                        disable: true,
                        text: "Select Separate Allowances By DSD Distributor to create separate Allowances that can be billed by Distributor, or choose Combined to create only one Allowance.",
                        options: [
                          {
                            name: "Combined DSD",
                            key: "DSD_WHSE_RETAIL_DIVISION",
                            routingKey: "DSD_WHSE_RETAIL_DIVISION",
                            allowanceMap: {
                              DSD_WHSE_RETAIL_DIVISION: [],
                            },
                            default: false,
                            createIndex: "SB",
                          },
                          {
                            name: "Separate Allowances By DSD Distributor",
                            key: "DSD_LEAD_DISTRIBUTORS",
                            routingKey: "DSD_LEAD_DISTRIBUTORS",
                            allowanceMap: {
                              DSD_LEAD_DISTRIBUTORS: [],
                            },
                            default: false,
                            createIndex: "SD",
                          },
                        ],
                      },
                      DSD: {
                        name: "Separate Allowances By DSD Distributor",
                        key: "DSD_LEAD_DISTRIBUTORS",
                        registerField: "allowanceToBeCreated",
                        disable: false,
                        createIndex: "SD",
                        text: "Select Separate Allowances By DSD Distributor to create separate Allowances that can be billed by Distributor, or choose Combined to create only one Allowance.",
                        options: [
                          {
                            name: "Combined DSD",
                            key: "DSD_WHSE_RETAIL_DIVISION",
                            routingKey: "DSD_WHSE_RETAIL_DIVISION",
                            allowanceMap: {
                              DSD_WHSE_RETAIL_DIVISION: [],
                            },
                            default: true,
                            createIndex: "SB",
                          },
                          {
                            name: "Separate Allowances By DSD Distributor",
                            key: "DSD_LEAD_DISTRIBUTORS",
                            routingKey: "DSD_LEAD_DISTRIBUTORS",
                            allowanceMap: {
                              DSD_LEAD_DISTRIBUTORS: [],
                            },
                            default: false,
                            createIndex: "SD",
                          },
                        ],
                      },
                      BOTH: {
                        name: "Both",
                        key: "BOTH",
                        registerField: "allowanceToBeCreated",
                        disable: false,
                        text: "Select Separate Allowances By DSD Distributor to create separate Allowances that can be billed by Distributor, or choose Combined to create only one Allowance.",
                        options: [
                          {
                            name: "Combined DSD",
                            key: "DSD_WHSE_RETAIL_DIVISION",
                            routingKey: "DSD_WHSE_RETAIL_DIVISION",
                            default: false,
                            allowanceMap: {
                              DSD_WHSE_RETAIL_DIVISION: [],
                            },
                            createIndex: "SB",
                          },
                          {
                            name: "Separate Allowances By DSD Distributor",
                            key: "DSD_LEAD_DISTRIBUTORS",
                            routingKey: "DSD_LEAD_DISTRIBUTORS",
                            allowanceMap: {
                              DSD_WHSE_RETAIL_DIVISION: [],
                              DSD_LEAD_DISTRIBUTORS: [],
                            },
                            default: false,
                            createIndex: "SD",
                          },
                        ],
                      },
                    },
                    "HEADER FLAT": [],
                    "ITEM FLAT": [],
                  },
                },
                isOpen: false,
                allowanceTypeAndPerformance: ["CASE", "SCAN", "SHIP_TO_STORE"],
                allowanceToBeCreated: [
                  "Both",
                  "One Allowance: Warehouse, DSD, or Combined",
                  "Combined DSD",
                  "Separate Allowances By DSD Distributor",
                  "DSD Only",
                  "Warehouse Only",
                ],
                label: "Allowance to be Created",
                allowanceScreenTypes: ["DP", "AO"],
                create: {
                  isEdit: false,
                  isView: false,
                  label: "Save & Continue",
                },
                edit: {
                  isEdit: true,
                  isView: true,
                  label: "Update & Continue",
                },
              },
              "Allowance Dates": {
                fields: {
                  allowanceSpecificFields: {
                    CASE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    SCAN: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    SHIP_TO_STORE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    HEADER_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    ITEM_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    DEFAULT: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      allowanceDatesData: {
                        registerKeyName:
                          "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  vehicleTypeOrCustomDate: {
                    label: "Vehicle Type/Custom Date",
                    required: true,
                    registerField: "allowance.vehicleTypeOrCustomDate",
                    gqlQueryConst: "allowance",
                    default: "Weekly Insert",
                    type: "select",
                    apiUrl: "",
                    slice: "",
                    displayLabel: "name",
                    options: [
                      {
                        id: 1,
                        name: "Weekly Insert",
                      },
                      {
                        id: 2,
                        name: "Monthly Insert",
                      },
                      {
                        id: 3,
                        name: "Yearly Insert",
                      },
                    ],
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "Vehicle Type/Custom Date is Required",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.vehicle.vehicleType.vehicleTypDesc",
                  },
                  year: {
                    label: "Year",
                    required: true,
                    registerField: "year",
                    default: "2023",
                    type: "select",
                    apiUrl: "",
                    slice: "",
                    displayLabel: "name",
                    options: [
                      {
                        name: 2021,
                        id: 1,
                      },
                      {
                        name: 2022,
                        id: 2,
                      },
                      {
                        name: 2023,
                        id: 3,
                      },
                      {
                        name: 2024,
                        id: 4,
                      },
                    ],
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "Year is Required",
                      },
                    },
                  },
                  startWeekOrVehicle: {
                    label: "Start Week/Vehicle",
                    required: true,
                    registerField: "startWeekOrVehicle",
                    optionUrl: "allowance.startWeek",
                    default: "Week 02 Insert 2023",
                    type: "select",
                    apiUrl: "",
                    slice: "",
                    displayLabel: "vehicleNm",
                    options: [
                      {
                        id: "",
                        vehicleNm: "",
                      },
                    ],
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "Start Week/Vehicle is Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.vehicle.vehicleNm",
                  },
                  vehicleStart: {
                    label: "Vehicle Start",
                    required: true,
                    disable: true,
                    registerField: "allowance.vehicleStart",
                    type: "date",
                    displayLabel: "name",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Allowance Start Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.vehicle.startDate",
                  },
                  vehicleEnd: {
                    label: "Vehicle End",
                    required: true,
                    disable: true,
                    registerField: "allowance.vehicleEnd",
                    type: "date",
                    displayLabel: "name",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Allowance End Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.vehicle.endDate",
                  },
                  orderStart: {
                    label: "Order Start",
                    required: true,
                    disable: true,
                    registerField: "allowance.orderStart",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Order start Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.orderStartDate",
                  },
                  orderEnd: {
                    label: "Order End",
                    required: true,
                    disable: true,
                    registerField: "allowance.orderEnd",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Order End Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.orderEndDate",
                  },
                  arrivalStart: {
                    label: "Arrival Start",
                    required: true,
                    disable: true,
                    registerField: "allowance.arrivalStart",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Arrival Start Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.arrivalStartDate",
                  },
                  arrivalEnd: {
                    label: "Arrival End",
                    required: true,
                    disable: true,
                    registerField: "allowance.arrivalEnd",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Arrival End Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.arrivalEndDate",
                  },
                  storeReceivingStart: {
                    label: "Store Receiving Start",
                    required: true,
                    disable: true,
                    registerField: "allowance.storeReceivingStart",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Store Receiving Start Required",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.storeReceivingStartDate",
                  },
                  storeReceivingEnd: {
                    label: "Store Receiving End",
                    required: true,
                    disable: true,
                    registerField: "allowance.storeReceivingEnd",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Store Receiving End Required",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.storeReceivingEndDate",
                  },
                  errors: {
                    EMPTY_ALLOWANCES:
                      "Invalid combination of Promo Product Groups and Store Groups. Please update Promo Product Groups or Store Groups to continue.",
                  },
                  allowanceCreationVehicle: {
                    registerKeyName: "allowanceCreationVehicle",
                  },
                  initialText: {
                    SCAN: {
                      DSD_WHSE_RETAIL_DIVISION: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                      DSD_LEAD_DISTRIBUTORS: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                    },
                    CASE: {
                      DSD_LEAD_DISTRIBUTORS: {
                        header:
                          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than that of the full Event. These dates will be used for O/I and/or Billing.",
                        bulletPointsText:
                          "Based on Albertsons Accounting Policies, the dates for DSD Case Allowances will be pre-calculated as:",
                        bulletPoints: [
                          "DSD Store Receiving Start: set to be 3 days prior to Vehicle Start",
                          "DSD Store Receiving End: set to match the Vehicle End",
                          "If an exception to these default dates is necessary due to either other Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
                        ],
                      },
                      WAREHOUSE_DIST_CENTERS: {
                        header:
                          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for O/I and/or Billing.",
                        bulletPointsText:
                          "Based on Albertsons Accounting Policies, the dates for Warehouse Case Allowances will be pre-calculated as:",
                        bulletPoints: [
                          "Order Start: Arrival Start Date - Lead Time* ",
                          "Order End: Arrival End Date - Lead Time*",
                          "Arrival Start: 10 days prior to Vehicle Start. 16 days in Northern California due to Hawaii, 21 days in Alaska.",
                          "Arrival End: Vehicle End date",
                        ],
                        footer:
                          "* As setup in the Albertsons buying system to represent total time in days from P.O. generation through warehouse receiving. If an exception to these default dates is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
                      },
                    },
                    SHIP_TO_STORE: {
                      DSD_WHSE_RETAIL_DIVISION: {
                        header:
                          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period shorter than the full Event.",
                        subHeader:
                          "Based on Albertsons Accounting Policies, the dates for Ship To Store Allowances are not tied only to the Vehicle / Performance.  These allowances are earned upon Store receiving and have no less than 7 days prior to the Performance start and 3 days after the end date.  14 Days prior for Displays.",
                        footer:
                          "If an exception to these defaults is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
                      },
                      DSD_LEAD_DISTRIBUTORS: {
                        header:
                          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period shorter than the full Event.",
                        subHeader:
                          "Based on Albertsons Accounting Policies, the dates for Ship To Store Allowances are not tied only to the Vehicle / Performance.  These allowances are earned upon Store receiving and have no less than 7 days prior to the Performance start and 3 days after the end date.  14 Days prior for Displays.",
                        footer:
                          "If an exception to these defaults is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
                      },
                    },
                    HEADER_FLAT: {
                      DSD_WHSE_RETAIL_DIVISION: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                      WAREHOUSE_DIST_CENTERS: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                    },
                    ITEM_FLAT: {
                      DSD_WHSE_RETAIL_DIVISION: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                      WAREHOUSE_DIST_CENTERS: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                    },
                  },
                },
                isOpen: false,
                offerTypeKey: "WAREHOUSE",
                allowanceTypeAndPerformance: [
                  "SCAN",
                  "SHIP_TO_STORE",
                  "HEADER_FLAT",
                  "ITEM_FLAT",
                ],
                allowanceToBeCreated: [
                  "Combined DSD",
                  "Both",
                  "One Allowance: Warehouse, DSD, or Combined",
                  "Warehouse Only",
                ],
                label: "Allowance Dates",
                allowanceScreenTypes: ["DP", "AO"],
                create: {
                  isEdit: false,
                  isView: false,
                  label: "Save & Continue",
                },
                edit: {
                  isEdit: true,
                  isView: false,
                  label: "Update & Continue",
                },
              },
              "DSD Allowance Dates": {
                fields: {
                  allowanceSpecificFields: {
                    CASE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    SCAN: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    SHIP_TO_STORE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    HEADER_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    ITEM_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    DEFAULT: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      allowanceDatesData: {
                        registerKeyName:
                          "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  vehicleTypeOrCustomDate: {
                    label: "Vehicle Type/Custom Date",
                    required: true,
                    registerField: "allowance.vehicleTypeOrCustomDate",
                    gqlQueryConst: "allowance",
                    default: "Weekly Insert",
                    type: "select",
                    apiUrl: "",
                    slice: "",
                    displayLabel: "name",
                    options: [
                      {
                        id: 1,
                        name: "Weekly Insert",
                      },
                      {
                        id: 2,
                        name: "Monthly Insert",
                      },
                      {
                        id: 3,
                        name: "Yearly Insert",
                      },
                    ],
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "Vehicle Type/Custom Date is Required",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.vehicle.vehicleType.vehicleTypDesc",
                  },
                  year: {
                    label: "Year",
                    required: true,
                    registerField: "year",
                    default: "2023",
                    type: "select",
                    apiUrl: "",
                    slice: "",
                    displayLabel: "name",
                    options: [
                      {
                        name: 2021,
                        id: 1,
                      },
                      {
                        name: 2022,
                        id: 2,
                      },
                      {
                        name: 2023,
                        id: 3,
                      },
                      {
                        name: 2024,
                        id: 4,
                      },
                    ],
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "Year is Required",
                      },
                    },
                  },
                  startWeekOrVehicle: {
                    label: "Start Week/Vehicle",
                    required: true,
                    registerField: "startWeekOrVehicle",
                    optionUrl: "allowance.startWeek",
                    default: "Week 02 Insert 2023",
                    type: "select",
                    apiUrl: "",
                    slice: "",
                    displayLabel: "vehicleNm",
                    options: [
                      {
                        id: "",
                        vehicleNm: "",
                      },
                    ],
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "Start Week/Vehicle is Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.vehicle.vehicleNm",
                  },
                  vehicleStart: {
                    label: "Vehicle Start",
                    required: true,
                    disable: true,
                    registerField: "allowance.vehicleStart",
                    type: "date",
                    displayLabel: "name",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Allowance Start Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.vehicle.startDate",
                  },
                  vehicleEnd: {
                    label: "Vehicle End",
                    required: true,
                    disable: true,
                    registerField: "allowance.vehicleEnd",
                    type: "date",
                    displayLabel: "name",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Allowance End Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.vehicle.endDate",
                  },
                  orderStart: {
                    label: "Order Start",
                    required: true,
                    disable: true,
                    registerField: "allowance.orderStart",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Order start Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.orderStartDate",
                  },
                  orderEnd: {
                    label: "Order End",
                    required: true,
                    disable: true,
                    registerField: "allowance.orderEnd",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Order End Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.orderEndDate",
                  },
                  arrivalStart: {
                    label: "Arrival Start",
                    required: true,
                    disable: true,
                    registerField: "allowance.arrivalStart",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Arrival Start Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.arrivalStartDate",
                  },
                  arrivalEnd: {
                    label: "Arrival End",
                    required: true,
                    disable: true,
                    registerField: "allowance.arrivalEnd",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Arrival End Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.arrivalEndDate",
                  },
                  storeReceivingStart: {
                    label: "Store Receiving Start",
                    required: true,
                    disable: true,
                    registerField: "allowance.storeReceivingStart",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Store Receiving Start Required",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.storeReceivingStartDate",
                  },
                  storeReceivingEnd: {
                    label: "Store Receiving End",
                    required: true,
                    disable: true,
                    registerField: "allowance.storeReceivingEnd",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Store Receiving End Required",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.storeReceivingEndDate",
                  },
                  errors: {
                    EMPTY_ALLOWANCES:
                      "Invalid combination of Promo Product Groups and Store Groups. Please update Promo Product Groups or Store Groups to continue.",
                  },
                  allowanceCreationVehicle: {
                    registerKeyName: "allowanceCreationVehicle",
                  },
                  initialText: {
                    SCAN: {
                      DSD_WHSE_RETAIL_DIVISION: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                      DSD_LEAD_DISTRIBUTORS: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                    },
                    CASE: {
                      DSD_LEAD_DISTRIBUTORS: {
                        header:
                          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than that of the full Event. These dates will be used for O/I and/or Billing.",
                        bulletPointsText:
                          "Based on Albertsons Accounting Policies, the dates for DSD Case Allowances will be pre-calculated as:",
                        bulletPoints: [
                          "DSD Store Receiving Start: set to be 3 days prior to Vehicle Start",
                          "DSD Store Receiving End: set to match the Vehicle End",
                          "If an exception to these default dates is necessary due to either other Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
                        ],
                      },
                      WAREHOUSE_DIST_CENTERS: {
                        header:
                          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for O/I and/or Billing.",
                        bulletPointsText:
                          "Based on Albertsons Accounting Policies, the dates for Warehouse Case Allowances will be pre-calculated as:",
                        bulletPoints: [
                          "Order Start: Arrival Start Date - Lead Time* ",
                          "Order End: Arrival End Date - Lead Time*",
                          "Arrival Start: 10 days prior to Vehicle Start. 16 days in Northern California due to Hawaii, 21 days in Alaska.",
                          "Arrival End: Vehicle End date",
                        ],
                        footer:
                          "* As setup in the Albertsons buying system to represent total time in days from P.O. generation through warehouse receiving. If an exception to these default dates is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
                      },
                    },
                    SHIP_TO_STORE: {
                      DSD_WHSE_RETAIL_DIVISION: {
                        header:
                          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period shorter than the full Event.",
                        subHeader:
                          "Based on Albertsons Accounting Policies, the dates for Ship To Store Allowances are not tied only to the Vehicle / Performance.  These allowances are earned upon Store receiving and have no less than 7 days prior to the Performance start and 3 days after the end date.  14 Days prior for Displays.",
                        footer:
                          "If an exception to these defaults is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
                      },
                      DSD_LEAD_DISTRIBUTORS: {
                        header:
                          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period shorter than the full Event.",
                        subHeader:
                          "Based on Albertsons Accounting Policies, the dates for Ship To Store Allowances are not tied only to the Vehicle / Performance.  These allowances are earned upon Store receiving and have no less than 7 days prior to the Performance start and 3 days after the end date.  14 Days prior for Displays.",
                        footer:
                          "If an exception to these defaults is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
                      },
                    },
                    HEADER_FLAT: {
                      DSD_WHSE_RETAIL_DIVISION: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                      WAREHOUSE_DIST_CENTERS: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                    },
                    ITEM_FLAT: {
                      DSD_WHSE_RETAIL_DIVISION: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                      WAREHOUSE_DIST_CENTERS: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                    },
                  },
                },
                isOpen: false,
                offerTypeKey: "DSD",
                allowanceTypeAndPerformance: ["SCAN", "SHIP_TO_STORE"],
                allowanceToBeCreated: [
                  "Separate Allowances By DSD Distributor",
                  "DSD Only",
                ],
                label: "Allowance Dates",
                allowanceScreenTypes: ["DP", "AO"],
                create: {
                  isEdit: false,
                  isView: false,
                  label: "Save & Continue",
                },
                edit: {
                  isEdit: true,
                  isView: false,
                  label: "Update & Continue",
                },
              },
              "DSD Case Allowance Dates": {
                fields: {
                  allowanceSpecificFields: {
                    CASE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    SCAN: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    SHIP_TO_STORE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    HEADER_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    ITEM_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    DEFAULT: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      allowanceDatesData: {
                        registerKeyName:
                          "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  vehicleTypeOrCustomDate: {
                    label: "Vehicle Type/Custom Date",
                    required: true,
                    registerField: "allowance.vehicleTypeOrCustomDate",
                    gqlQueryConst: "allowance",
                    default: "Weekly Insert",
                    type: "select",
                    apiUrl: "",
                    slice: "",
                    displayLabel: "name",
                    options: [
                      {
                        id: 1,
                        name: "Weekly Insert",
                      },
                      {
                        id: 2,
                        name: "Monthly Insert",
                      },
                      {
                        id: 3,
                        name: "Yearly Insert",
                      },
                    ],
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "Vehicle Type/Custom Date is Required",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.vehicle.vehicleType.vehicleTypDesc",
                  },
                  year: {
                    label: "Year",
                    required: true,
                    registerField: "year",
                    default: "2023",
                    type: "select",
                    apiUrl: "",
                    slice: "",
                    displayLabel: "name",
                    options: [
                      {
                        name: 2021,
                        id: 1,
                      },
                      {
                        name: 2022,
                        id: 2,
                      },
                      {
                        name: 2023,
                        id: 3,
                      },
                      {
                        name: 2024,
                        id: 4,
                      },
                    ],
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "Year is Required",
                      },
                    },
                  },
                  startWeekOrVehicle: {
                    label: "Start Week/Vehicle",
                    required: true,
                    registerField: "startWeekOrVehicle",
                    optionUrl: "allowance.startWeek",
                    default: "Week 02 Insert 2023",
                    type: "select",
                    apiUrl: "",
                    slice: "",
                    displayLabel: "vehicleNm",
                    options: [
                      {
                        id: "",
                        vehicleNm: "",
                      },
                    ],
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "Start Week/Vehicle is Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.vehicle.vehicleNm",
                  },
                  vehicleStart: {
                    label: "Vehicle Start",
                    required: true,
                    disable: true,
                    registerField: "allowance.vehicleStart",
                    type: "date",
                    displayLabel: "name",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Allowance Start Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.vehicle.startDate",
                  },
                  vehicleEnd: {
                    label: "Vehicle End",
                    required: true,
                    disable: true,
                    registerField: "allowance.vehicleEnd",
                    type: "date",
                    displayLabel: "name",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Allowance End Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.vehicle.endDate",
                  },
                  orderStart: {
                    label: "Order Start",
                    required: true,
                    disable: true,
                    registerField: "allowance.orderStart",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Order start Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.orderStartDate",
                  },
                  orderEnd: {
                    label: "Order End",
                    required: true,
                    disable: true,
                    registerField: "allowance.orderEnd",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Order End Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.orderEndDate",
                  },
                  arrivalStart: {
                    label: "Arrival Start",
                    required: true,
                    disable: true,
                    registerField: "allowance.arrivalStart",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Arrival Start Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.arrivalStartDate",
                  },
                  arrivalEnd: {
                    label: "Arrival End",
                    required: true,
                    disable: true,
                    registerField: "allowance.arrivalEnd",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Arrival End Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.arrivalEndDate",
                  },
                  storeReceivingStart: {
                    label: "Store Receiving Start",
                    required: true,
                    disable: true,
                    registerField: "allowance.storeReceivingStart",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Store Receiving Start Required",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.storeReceivingStartDate",
                  },
                  storeReceivingEnd: {
                    label: "Store Receiving End",
                    required: true,
                    disable: true,
                    registerField: "allowance.storeReceivingEnd",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Store Receiving End Required",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.storeReceivingEndDate",
                  },
                  errors: {
                    EMPTY_ALLOWANCES:
                      "Invalid combination of Promo Product Groups and Store Groups. Please update Promo Product Groups or Store Groups to continue.",
                  },
                  allowanceCreationVehicle: {
                    registerKeyName: "allowanceCreationVehicle",
                  },
                  initialText: {
                    SCAN: {
                      DSD_WHSE_RETAIL_DIVISION: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                      DSD_LEAD_DISTRIBUTORS: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                    },
                    CASE: {
                      DSD_LEAD_DISTRIBUTORS: {
                        header:
                          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than that of the full Event. These dates will be used for O/I and/or Billing.",
                        bulletPointsText:
                          "Based on Albertsons Accounting Policies, the dates for DSD Case Allowances will be pre-calculated as:",
                        bulletPoints: [
                          "DSD Store Receiving Start: set to be 3 days prior to Vehicle Start",
                          "DSD Store Receiving End: set to match the Vehicle End",
                          "If an exception to these default dates is necessary due to either other Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
                        ],
                      },
                      WAREHOUSE_DIST_CENTERS: {
                        header:
                          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for O/I and/or Billing.",
                        bulletPointsText:
                          "Based on Albertsons Accounting Policies, the dates for Warehouse Case Allowances will be pre-calculated as:",
                        bulletPoints: [
                          "Order Start: Arrival Start Date - Lead Time* ",
                          "Order End: Arrival End Date - Lead Time*",
                          "Arrival Start: 10 days prior to Vehicle Start. 16 days in Northern California due to Hawaii, 21 days in Alaska.",
                          "Arrival End: Vehicle End date",
                        ],
                        footer:
                          "* As setup in the Albertsons buying system to represent total time in days from P.O. generation through warehouse receiving. If an exception to these default dates is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
                      },
                    },
                    SHIP_TO_STORE: {
                      DSD_WHSE_RETAIL_DIVISION: {
                        header:
                          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period shorter than the full Event.",
                        subHeader:
                          "Based on Albertsons Accounting Policies, the dates for Ship To Store Allowances are not tied only to the Vehicle / Performance.  These allowances are earned upon Store receiving and have no less than 7 days prior to the Performance start and 3 days after the end date.  14 Days prior for Displays.",
                        footer:
                          "If an exception to these defaults is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
                      },
                      DSD_LEAD_DISTRIBUTORS: {
                        header:
                          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period shorter than the full Event.",
                        subHeader:
                          "Based on Albertsons Accounting Policies, the dates for Ship To Store Allowances are not tied only to the Vehicle / Performance.  These allowances are earned upon Store receiving and have no less than 7 days prior to the Performance start and 3 days after the end date.  14 Days prior for Displays.",
                        footer:
                          "If an exception to these defaults is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
                      },
                    },
                    HEADER_FLAT: {
                      DSD_WHSE_RETAIL_DIVISION: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                      WAREHOUSE_DIST_CENTERS: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                    },
                    ITEM_FLAT: {
                      DSD_WHSE_RETAIL_DIVISION: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                      WAREHOUSE_DIST_CENTERS: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                    },
                  },
                },
                isOpen: false,
                allowanceTypeAndPerformance: ["CASE"],
                allowanceToBeCreated: ["Both", "DSD Only"],
                label: "DSD Case Allowance Dates",
                offerTypeKey: "DSD",
                allowanceScreenTypes: ["DP", "AO"],
                create: {
                  isEdit: false,
                  isView: false,
                  label: "Save & Continue",
                },
                edit: {
                  isEdit: true,
                  isView: false,
                  label: "Update & Continue",
                },
              },
              "Warehouse Case Allowance Dates": {
                fields: {
                  allowanceSpecificFields: {
                    CASE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    SCAN: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    SHIP_TO_STORE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    HEADER_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    ITEM_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceDatesData: {
                          registerKeyName:
                            "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    DEFAULT: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      allowanceDatesData: {
                        registerKeyName:
                          "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  vehicleTypeOrCustomDate: {
                    label: "Vehicle Type/Custom Date",
                    required: true,
                    registerField: "allowance.vehicleTypeOrCustomDate",
                    gqlQueryConst: "allowance",
                    default: "Weekly Insert",
                    type: "select",
                    apiUrl: "",
                    slice: "",
                    displayLabel: "name",
                    options: [
                      {
                        id: 1,
                        name: "Weekly Insert",
                      },
                      {
                        id: 2,
                        name: "Monthly Insert",
                      },
                      {
                        id: 3,
                        name: "Yearly Insert",
                      },
                    ],
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "Vehicle Type/Custom Date is Required",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.vehicle.vehicleType.vehicleTypDesc",
                  },
                  year: {
                    label: "Year",
                    required: true,
                    registerField: "year",
                    default: "2023",
                    type: "select",
                    apiUrl: "",
                    slice: "",
                    displayLabel: "name",
                    options: [
                      {
                        name: 2021,
                        id: 1,
                      },
                      {
                        name: 2022,
                        id: 2,
                      },
                      {
                        name: 2023,
                        id: 3,
                      },
                      {
                        name: 2024,
                        id: 4,
                      },
                    ],
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "Year is Required",
                      },
                    },
                  },
                  startWeekOrVehicle: {
                    label: "Start Week/Vehicle",
                    required: true,
                    registerField: "startWeekOrVehicle",
                    optionUrl: "allowance.startWeek",
                    default: "Week 02 Insert 2023",
                    type: "select",
                    apiUrl: "",
                    slice: "",
                    displayLabel: "vehicleNm",
                    options: [
                      {
                        id: "",
                        vehicleNm: "",
                      },
                    ],
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "Start Week/Vehicle is Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.vehicle.vehicleNm",
                  },
                  vehicleStart: {
                    label: "Vehicle Start",
                    required: true,
                    disable: true,
                    registerField: "allowance.vehicleStart",
                    type: "date",
                    displayLabel: "name",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Allowance Start Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.vehicle.startDate",
                  },
                  vehicleEnd: {
                    label: "Vehicle End",
                    required: true,
                    disable: true,
                    registerField: "allowance.vehicleEnd",
                    type: "date",
                    displayLabel: "name",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Allowance End Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.vehicle.endDate",
                  },
                  orderStart: {
                    label: "Order Start",
                    required: true,
                    disable: true,
                    registerField: "allowance.orderStart",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Order start Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.orderStartDate",
                  },
                  orderEnd: {
                    label: "Order End",
                    required: true,
                    disable: true,
                    registerField: "allowance.orderEnd",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Order End Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.orderEndDate",
                  },
                  arrivalStart: {
                    label: "Arrival Start",
                    required: true,
                    disable: true,
                    registerField: "allowance.arrivalStart",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Arrival Start Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.arrivalStartDate",
                  },
                  arrivalEnd: {
                    label: "Arrival End",
                    required: true,
                    disable: true,
                    registerField: "allowance.arrivalEnd",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Arrival End Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.arrivalEndDate",
                  },
                  storeReceivingStart: {
                    label: "Store Receiving Start",
                    required: true,
                    disable: true,
                    registerField: "allowance.storeReceivingStart",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Store Receiving Start Required",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.storeReceivingStartDate",
                  },
                  storeReceivingEnd: {
                    label: "Store Receiving End",
                    required: true,
                    disable: true,
                    registerField: "allowance.storeReceivingEnd",
                    type: "date",
                    error: {
                      required: {
                        backgroundColor: "",
                        text: "Store Receiving End Required",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.storeReceivingEndDate",
                  },
                  errors: {
                    EMPTY_ALLOWANCES:
                      "Invalid combination of Promo Product Groups and Store Groups. Please update Promo Product Groups or Store Groups to continue.",
                  },
                  allowanceCreationVehicle: {
                    registerKeyName: "allowanceCreationVehicle",
                  },
                  initialText: {
                    SCAN: {
                      DSD_WHSE_RETAIL_DIVISION: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                      DSD_LEAD_DISTRIBUTORS: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                    },
                    CASE: {
                      DSD_LEAD_DISTRIBUTORS: {
                        header:
                          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than that of the full Event. These dates will be used for O/I and/or Billing.",
                        bulletPointsText:
                          "Based on Albertsons Accounting Policies, the dates for DSD Case Allowances will be pre-calculated as:",
                        bulletPoints: [
                          "DSD Store Receiving Start: set to be 3 days prior to Vehicle Start",
                          "DSD Store Receiving End: set to match the Vehicle End",
                          "If an exception to these default dates is necessary due to either other Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
                        ],
                      },
                      WAREHOUSE_DIST_CENTERS: {
                        header:
                          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for O/I and/or Billing.",
                        bulletPointsText:
                          "Based on Albertsons Accounting Policies, the dates for Warehouse Case Allowances will be pre-calculated as:",
                        bulletPoints: [
                          "Order Start: Arrival Start Date - Lead Time* ",
                          "Order End: Arrival End Date - Lead Time*",
                          "Arrival Start: 10 days prior to Vehicle Start. 16 days in Northern California due to Hawaii, 21 days in Alaska.",
                          "Arrival End: Vehicle End date",
                        ],
                        footer:
                          "* As setup in the Albertsons buying system to represent total time in days from P.O. generation through warehouse receiving. If an exception to these default dates is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
                      },
                    },
                    SHIP_TO_STORE: {
                      DSD_WHSE_RETAIL_DIVISION: {
                        header:
                          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period shorter than the full Event.",
                        subHeader:
                          "Based on Albertsons Accounting Policies, the dates for Ship To Store Allowances are not tied only to the Vehicle / Performance.  These allowances are earned upon Store receiving and have no less than 7 days prior to the Performance start and 3 days after the end date.  14 Days prior for Displays.",
                        footer:
                          "If an exception to these defaults is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
                      },
                      DSD_LEAD_DISTRIBUTORS: {
                        header:
                          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period shorter than the full Event.",
                        subHeader:
                          "Based on Albertsons Accounting Policies, the dates for Ship To Store Allowances are not tied only to the Vehicle / Performance.  These allowances are earned upon Store receiving and have no less than 7 days prior to the Performance start and 3 days after the end date.  14 Days prior for Displays.",
                        footer:
                          "If an exception to these defaults is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
                      },
                    },
                    HEADER_FLAT: {
                      DSD_WHSE_RETAIL_DIVISION: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                      WAREHOUSE_DIST_CENTERS: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                    },
                    ITEM_FLAT: {
                      DSD_WHSE_RETAIL_DIVISION: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                      WAREHOUSE_DIST_CENTERS: {
                        header:
                          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
                      },
                    },
                  },
                },
                isOpen: false,
                allowanceTypeAndPerformance: ["CASE"],
                allowanceToBeCreated: ["Both", "Warehouse Only"],
                label: "Warehouse Case Allowance Dates",
                offerTypeKey: "WAREHOUSE",
                allowanceScreenTypes: ["DP", "AO"],
                create: {
                  isEdit: false,
                  isView: false,
                  label: "Save & Continue",
                },
                edit: {
                  isEdit: true,
                  isView: false,
                  label: "Update & Continue",
                },
              },
              "Allowance Amounts": {
                fields: {
                  allowanceSpecificFields: {
                    CASE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    SCAN: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    SHIP_TO_STORE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    HEADER_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    ITEM_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    DEFAULT: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      allowanceAmountsData: {
                        registerKeyName:
                          "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  allowanceAmount: {
                    label: "Allowance Amount",
                    required: true,
                    registerField: "allowanceAmount",
                    type: "text",
                    errors: {
                      required: {
                        text: "Allowance Amount Required",
                        errorMessages: "Allowance Amount Required",
                      },
                      maxLength: {
                        length: 10,
                        text: "Max Length Reached",
                      },
                    },
                    tooltip:
                      "This is auto generated based on your selections.\n You can edit this if you want.",
                    mapperKey:
                      "offerallowances.allowances.allowanceItems.allowanceItemComps.allowAmount",
                  },
                  allowanceAmountPerItem: {
                    label: "Allowance Amount per Item",
                    required: true,
                    registerField: "allowanceAmount",
                    type: "text",
                    errors: {
                      required: {
                        text: "Allowance Amount Required per Item",
                        errorMessages: "Allowance Amount Required per Item",
                      },
                      maxLength: {
                        length: 10,
                        text: "Max Length Reached",
                      },
                      formatError: {
                        pattern: {},
                        text: "Please enter valid Allowance Amount per Item",
                      },
                    },
                    tooltip:
                      "This is auto generated based on your selections.\n You can edit this if you want.",
                    mapperKey:
                      "offerallowances.allowances.allowanceItems.allowanceItemComps.allowanceAmount",
                  },
                  uom: {
                    label: "UOM",
                    required: true,
                    registerField: "uom",
                    type: "select",
                    apiUrl: "",
                    slice: "",
                    displayLabel: "name",
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "UOM Required",
                        errorMessages: "UOM Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.allowanceItems.uom",
                  },
                  allowanceAmountsData: {
                    registerKeyName:
                      "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                  },
                },
                isOpen: false,
                allowanceTypeAndPerformance: [
                  "SCAN",
                  "SHIP_TO_STORE",
                  "HEADER_FLAT",
                  "ITEM_FLAT",
                ],
                allowanceToBeCreated: [
                  "One Allowance: Warehouse, DSD, or Combined",
                  "Combined DSD",
                ],
                label: "Allowance Amounts",
                offerTypeKey: "WAREHOUSE",
                allowanceScreenTypes: ["DP", "AO"],
                create: {
                  isEdit: false,
                  isView: false,
                  label: "Save & Continue",
                },
                edit: {
                  isEdit: true,
                  isView: false,
                  label: "Update & Continue",
                },
                disable: {
                  createInd: ["LC"],
                  allowanceScreenType: ["DP"],
                },
              },
              "DSD Case Allowance Amount": {
                fields: {
                  allowanceSpecificFields: {
                    CASE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    SCAN: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    SHIP_TO_STORE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    HEADER_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    ITEM_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    DEFAULT: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      allowanceAmountsData: {
                        registerKeyName:
                          "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  allowanceAmount: {
                    label: "Allowance Amount",
                    required: true,
                    registerField: "allowanceAmount",
                    type: "text",
                    errors: {
                      required: {
                        text: "Allowance Amount Required",
                        errorMessages: "Allowance Amount Required",
                      },
                      maxLength: {
                        length: 10,
                        text: "Max Length Reached",
                      },
                    },
                    tooltip:
                      "This is auto generated based on your selections.\n You can edit this if you want.",
                    mapperKey:
                      "offerallowances.allowances.allowanceItems.allowanceItemComps.allowAmount",
                  },
                  allowanceAmountPerItem: {
                    label: "Allowance Amount per Item",
                    required: true,
                    registerField: "allowanceAmount",
                    type: "text",
                    errors: {
                      required: {
                        text: "Allowance Amount Required per Item",
                        errorMessages: "Allowance Amount Required per Item",
                      },
                      maxLength: {
                        length: 10,
                        text: "Max Length Reached",
                      },
                      formatError: {
                        pattern: {},
                        text: "Please enter valid Allowance Amount per Item",
                      },
                    },
                    tooltip:
                      "This is auto generated based on your selections.\n You can edit this if you want.",
                    mapperKey:
                      "offerallowances.allowances.allowanceItems.allowanceItemComps.allowanceAmount",
                  },
                  uom: {
                    label: "UOM",
                    required: true,
                    registerField: "uom",
                    type: "select",
                    apiUrl: "",
                    slice: "",
                    displayLabel: "name",
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "UOM Required",
                        errorMessages: "UOM Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.allowanceItems.uom",
                  },
                  allowanceAmountsData: {
                    registerKeyName:
                      "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                  },
                },
                isOpen: false,
                allowanceTypeAndPerformance: ["CASE"],
                allowanceToBeCreated: ["Both", "DSD Only"],
                label: "DSD Case Allowance Amount",
                offerTypeKey: "DSD",
                allowanceScreenTypes: ["DP", "AO"],
                create: {
                  isEdit: false,
                  isView: false,
                  label: "Save & Continue",
                },
                edit: {
                  isEdit: true,
                  isView: false,
                  label: "Update & Continue",
                },
              },
              "DSD Scan Allowance Amount": {
                fields: {
                  allowanceSpecificFields: {
                    CASE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    SCAN: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    SHIP_TO_STORE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    HEADER_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    ITEM_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    DEFAULT: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      allowanceAmountsData: {
                        registerKeyName:
                          "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  allowanceAmount: {
                    label: "Allowance Amount",
                    required: true,
                    registerField: "allowanceAmount",
                    type: "text",
                    errors: {
                      required: {
                        text: "Allowance Amount Required",
                        errorMessages: "Allowance Amount Required",
                      },
                      maxLength: {
                        length: 10,
                        text: "Max Length Reached",
                      },
                    },
                    tooltip:
                      "This is auto generated based on your selections.\n You can edit this if you want.",
                    mapperKey:
                      "offerallowances.allowances.allowanceItems.allowanceItemComps.allowAmount",
                  },
                  allowanceAmountPerItem: {
                    label: "Allowance Amount per Item",
                    required: true,
                    registerField: "allowanceAmount",
                    type: "text",
                    errors: {
                      required: {
                        text: "Allowance Amount Required per Item",
                        errorMessages: "Allowance Amount Required per Item",
                      },
                      maxLength: {
                        length: 10,
                        text: "Max Length Reached",
                      },
                      formatError: {
                        pattern: {},
                        text: "Please enter valid Allowance Amount per Item",
                      },
                    },
                    tooltip:
                      "This is auto generated based on your selections.\n You can edit this if you want.",
                    mapperKey:
                      "offerallowances.allowances.allowanceItems.allowanceItemComps.allowanceAmount",
                  },
                  uom: {
                    label: "UOM",
                    required: true,
                    registerField: "uom",
                    type: "select",
                    apiUrl: "",
                    slice: "",
                    displayLabel: "name",
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "UOM Required",
                        errorMessages: "UOM Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.allowanceItems.uom",
                  },
                  allowanceAmountsData: {
                    registerKeyName:
                      "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                  },
                },
                isOpen: false,
                allowanceTypeAndPerformance: ["SCAN"],
                allowanceToBeCreated: [
                  "Both",
                  "Separate Allowances By DSD Distributor",
                ],
                label: "DSD Scan Allowance Amounts",
                offerTypeKey: "DSD",
                allowanceScreenTypes: ["DP", "AO"],
                create: {
                  isEdit: false,
                  isView: false,
                  label: "Save & Continue",
                },
                edit: {
                  isEdit: true,
                  isView: false,
                  label: "Update & Continue",
                },
              },
              "DSD Ship To Store Allowance Amount": {
                fields: {
                  allowanceSpecificFields: {
                    CASE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    SCAN: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    SHIP_TO_STORE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    HEADER_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    ITEM_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    DEFAULT: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      allowanceAmountsData: {
                        registerKeyName:
                          "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  allowanceAmount: {
                    label: "Allowance Amount",
                    required: true,
                    registerField: "allowanceAmount",
                    type: "text",
                    errors: {
                      required: {
                        text: "Allowance Amount Required",
                        errorMessages: "Allowance Amount Required",
                      },
                      maxLength: {
                        length: 10,
                        text: "Max Length Reached",
                      },
                    },
                    tooltip:
                      "This is auto generated based on your selections.\n You can edit this if you want.",
                    mapperKey:
                      "offerallowances.allowances.allowanceItems.allowanceItemComps.allowAmount",
                  },
                  allowanceAmountPerItem: {
                    label: "Allowance Amount per Item",
                    required: true,
                    registerField: "allowanceAmount",
                    type: "text",
                    errors: {
                      required: {
                        text: "Allowance Amount Required per Item",
                        errorMessages: "Allowance Amount Required per Item",
                      },
                      maxLength: {
                        length: 10,
                        text: "Max Length Reached",
                      },
                      formatError: {
                        pattern: {},
                        text: "Please enter valid Allowance Amount per Item",
                      },
                    },
                    tooltip:
                      "This is auto generated based on your selections.\n You can edit this if you want.",
                    mapperKey:
                      "offerallowances.allowances.allowanceItems.allowanceItemComps.allowanceAmount",
                  },
                  uom: {
                    label: "UOM",
                    required: true,
                    registerField: "uom",
                    type: "select",
                    apiUrl: "",
                    slice: "",
                    displayLabel: "name",
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "UOM Required",
                        errorMessages: "UOM Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.allowanceItems.uom",
                  },
                  allowanceAmountsData: {
                    registerKeyName:
                      "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                  },
                },
                isOpen: false,
                allowanceTypeAndPerformance: ["SHIP_TO_STORE"],
                allowanceToBeCreated: [
                  "Both",
                  "Separate Allowances By DSD Distributor",
                ],
                label: "DSD Ship To Store Allowance Amounts",
                offerTypeKey: "DSD",
                allowanceScreenTypes: ["DP", "AO"],
                create: {
                  isEdit: false,
                  isView: false,
                  label: "Save & Continue",
                },
                edit: {
                  isEdit: true,
                  isView: false,
                  label: "Update & Continue",
                },
              },
              "Warehouse Case Allowance Amount": {
                fields: {
                  allowanceSpecificFields: {
                    CASE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    SCAN: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    SHIP_TO_STORE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    HEADER_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    ITEM_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    DEFAULT: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      allowanceAmountsData: {
                        registerKeyName:
                          "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  allowanceAmount: {
                    label: "Allowance Amount",
                    required: true,
                    registerField: "allowanceAmount",
                    type: "text",
                    errors: {
                      required: {
                        text: "Allowance Amount Required",
                        errorMessages: "Allowance Amount Required",
                      },
                      maxLength: {
                        length: 10,
                        text: "Max Length Reached",
                      },
                    },
                    tooltip:
                      "This is auto generated based on your selections.\n You can edit this if you want.",
                    mapperKey:
                      "offerallowances.allowances.allowanceItems.allowanceItemComps.allowAmount",
                  },
                  allowanceAmountPerItem: {
                    label: "Allowance Amount per Item",
                    required: true,
                    registerField: "allowanceAmount",
                    type: "text",
                    errors: {
                      required: {
                        text: "Allowance Amount Required per Item",
                        errorMessages: "Allowance Amount Required per Item",
                      },
                      maxLength: {
                        length: 10,
                        text: "Max Length Reached",
                      },
                      formatError: {
                        pattern: {},
                        text: "Please enter valid Allowance Amount per Item",
                      },
                    },
                    tooltip:
                      "This is auto generated based on your selections.\n You can edit this if you want.",
                    mapperKey:
                      "offerallowances.allowances.allowanceItems.allowanceItemComps.allowanceAmount",
                  },
                  uom: {
                    label: "UOM",
                    required: true,
                    registerField: "uom",
                    type: "select",
                    apiUrl: "",
                    slice: "",
                    displayLabel: "name",
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "UOM Required",
                        errorMessages: "UOM Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.allowanceItems.uom",
                  },
                  allowanceAmountsData: {
                    registerKeyName:
                      "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                  },
                },
                isOpen: false,
                allowanceTypeAndPerformance: ["CASE"],
                allowanceToBeCreated: ["Both", "Warehouse Only"],
                label: "Warehouse Case Allowance Amount",
                offerTypeKey: "WAREHOUSE",
                allowanceScreenTypes: ["DP", "AO"],
                create: {
                  isEdit: false,
                  isView: false,
                  label: "Save & Continue",
                },
                edit: {
                  isEdit: true,
                  isView: false,
                  label: "Update & Continue",
                },
              },
              "Warehouse Scan Allowance Amount": {
                fields: {
                  allowanceSpecificFields: {
                    CASE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    SCAN: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    SHIP_TO_STORE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    HEADER_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    ITEM_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        allowanceAmountsData: {
                          registerKeyName:
                            "allowanceAmountsData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    DEFAULT: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      allowanceAmountsData: {
                        registerKeyName:
                          "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  allowanceAmount: {
                    label: "Allowance Amount",
                    required: true,
                    registerField: "allowanceAmount",
                    type: "text",
                    errors: {
                      required: {
                        text: "Allowance Amount Required",
                        errorMessages: "Allowance Amount Required",
                      },
                      maxLength: {
                        length: 10,
                        text: "Max Length Reached",
                      },
                    },
                    tooltip:
                      "This is auto generated based on your selections.\n You can edit this if you want.",
                    mapperKey:
                      "offerallowances.allowances.allowanceItems.allowanceItemComps.allowAmount",
                  },
                  allowanceAmountPerItem: {
                    label: "Allowance Amount per Item",
                    required: true,
                    registerField: "allowanceAmount",
                    type: "text",
                    errors: {
                      required: {
                        text: "Allowance Amount Required per Item",
                        errorMessages: "Allowance Amount Required per Item",
                      },
                      maxLength: {
                        length: 10,
                        text: "Max Length Reached",
                      },
                      formatError: {
                        pattern: {},
                        text: "Please enter valid Allowance Amount per Item",
                      },
                    },
                    tooltip:
                      "This is auto generated based on your selections.\n You can edit this if you want.",
                    mapperKey:
                      "offerallowances.allowances.allowanceItems.allowanceItemComps.allowanceAmount",
                  },
                  uom: {
                    label: "UOM",
                    required: true,
                    registerField: "uom",
                    type: "select",
                    apiUrl: "",
                    slice: "",
                    displayLabel: "name",
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "UOM Required",
                        errorMessages: "UOM Required",
                      },
                    },
                    mapperKey: "offerallowances.allowances.allowanceItems.uom",
                  },
                  allowanceAmountsData: {
                    registerKeyName:
                      "allowanceAmountsData.DSD_WHSE_RETAIL_DIVISION",
                  },
                },
                isOpen: false,
                allowanceTypeAndPerformance: [],
                allowanceToBeCreated: ["Both"],
                label: "Warehouse Scan Allowance Amounts",
                offerTypeKey: "WAREHOUSE",
                allowanceScreenTypes: ["DP", "AO"],
                create: {
                  isEdit: false,
                  isView: false,
                  label: "Save & Continue",
                },
                edit: {
                  isEdit: true,
                  isView: false,
                  label: "Update & Continue",
                },
              },
              "Default Billing Information": {
                fields: {
                  allowanceSpecificFields: {
                    CASE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    SCAN: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    SHIP_TO_STORE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    HEADER_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    ITEM_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    DEFAULT: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  suggestedVendorPaymentType: {
                    label: "Suggested Payment Type",
                    required: false,
                    registerField: "suggestedVendorPaymentType",
                    type: "select",
                    default: "Deduct",
                    displayLabel: "name",
                    options: [
                      {
                        name: "Select",
                        id: "",
                      },
                      {
                        name: "Deduct",
                        id: "Deduct",
                      },
                      {
                        name: "Invoice",
                        id: "Invoice",
                      },
                    ],
                    apiUrl: "",
                    slice: "",
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "ABS Vendor Payment Type is Required",
                      },
                    },
                    tooltip:
                      "This is auto generated based on your selections.\n You can edit this if you want.",
                    mapperKey:
                      "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
                  },
                  acApOrArNumber: {
                    label: "Suggested A/P or A/R Number",
                    required: false,
                    registerField: "acApOrArNumber",
                    type: "number",
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "A/P or A/R Vendor Number is Required",
                      },
                      formatError: {
                        pattern: {},
                        text: "Please enter valid A/P or A/R Vendor Number",
                      },
                      maxLength: {
                        backgroundColor: "",
                        text: "Max Length Reached",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.allowanceBillingInfo.acApOrArNumber",
                  },
                  vendorComment: {
                    label: "Vendor Comment",
                    required: false,
                    registerField: "vendorComment",
                    type: "text",
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "Vendor Comment is Required",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.allowanceBillingInfo.vendorComment",
                  },
                  vendorOfferTrackingNbr: {
                    label: "",
                    required: false,
                    registerField: "vendorOfferTrackingNbr",
                    type: "text",
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "Vendor Tracking Number is Required",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
                  },
                  billingInformationData: {
                    registerKeyName: "billingInformationData",
                  },
                },
                isOpen: false,
                allowanceTypeAndPerformance: [
                  "SCAN",
                  "SHIP_TO_STORE",
                  "HEADER_FLAT",
                  "ITEM_FLAT",
                ],
                allowanceToBeCreated: [
                  "One Allowance: Warehouse, DSD, or Combined",
                  "Combined DSD",
                ],
                label: "Default Billing Information",
                offerTypeKey: "WAREHOUSE",
                allowanceScreenTypes: ["DP", "AO"],
                create: {
                  isEdit: false,
                  isView: false,
                  label: "Save & Continue",
                },
                edit: {
                  isEdit: true,
                  isView: false,
                  label: "Update & Continue",
                },
              },
              "DSD Default Billing Information": {
                fields: {
                  allowanceSpecificFields: {
                    CASE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    SCAN: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    SHIP_TO_STORE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    HEADER_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    ITEM_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    DEFAULT: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  suggestedVendorPaymentType: {
                    label: "Suggested Payment Type",
                    required: false,
                    registerField: "suggestedVendorPaymentType",
                    type: "select",
                    default: "Deduct",
                    displayLabel: "name",
                    options: [
                      {
                        name: "Select",
                        id: "",
                      },
                      {
                        name: "Deduct",
                        id: "Deduct",
                      },
                      {
                        name: "Invoice",
                        id: "Invoice",
                      },
                    ],
                    apiUrl: "",
                    slice: "",
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "ABS Vendor Payment Type is Required",
                      },
                    },
                    tooltip:
                      "This is auto generated based on your selections.\n You can edit this if you want.",
                    mapperKey:
                      "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
                  },
                  acApOrArNumber: {
                    label: "Suggested A/P or A/R Number",
                    required: false,
                    registerField: "acApOrArNumber",
                    type: "number",
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "A/P or A/R Vendor Number is Required",
                      },
                      formatError: {
                        pattern: {},
                        text: "Please enter valid A/P or A/R Vendor Number",
                      },
                      maxLength: {
                        backgroundColor: "",
                        text: "Max Length Reached",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.allowanceBillingInfo.acApOrArNumber",
                  },
                  vendorComment: {
                    label: "Vendor Comment",
                    required: false,
                    registerField: "vendorComment",
                    type: "text",
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "Vendor Comment is Required",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.allowanceBillingInfo.vendorComment",
                  },
                  vendorOfferTrackingNbr: {
                    label: "",
                    required: false,
                    registerField: "vendorOfferTrackingNbr",
                    type: "text",
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "Vendor Tracking Number is Required",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
                  },
                  billingInformationData: {
                    registerKeyName: "billingInformationData",
                  },
                },
                isOpen: false,
                allowanceTypeAndPerformance: ["CASE", "SCAN", "SHIP_TO_STORE"],
                allowanceToBeCreated: [
                  "Both",
                  "Separate Allowances By DSD Distributor",
                  "DSD Only",
                ],
                label: "DSD Default Billing Information",
                offerTypeKey: "DSD",
                allowanceScreenTypes: ["DP", "AO"],
                create: {
                  isEdit: false,
                  isView: false,
                  label: "Save & Continue",
                },
                edit: {
                  isEdit: true,
                  isView: false,
                  label: "Update & Continue",
                },
              },
              "Warehouse Default Billing Information": {
                fields: {
                  allowanceSpecificFields: {
                    CASE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    SCAN: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    SHIP_TO_STORE: {
                      DSD: {
                        key: "DSD_LEAD_DISTRIBUTORS",
                        routeKey: "DSD_LEAD_DISTRIBUTORS",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                        },
                      },
                      WAREHOUSE: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                    },
                    HEADER_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    ITEM_FLAT: {
                      DSD: {
                        key: "DSD_WHSE_RETAIL_DIVISION",
                        routeKey: "DSD_WHSE_RETAIL_DIVISION",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                        },
                      },
                      WAREHOUSE: {
                        key: "WAREHOUSE_DIST_CENTERS",
                        routeKey: "WAREHOUSE_DIST_CENTERS",
                        billingInformationData: {
                          registerKeyName:
                            "billingInformationData.WAREHOUSE_DIST_CENTERS",
                        },
                      },
                    },
                    DEFAULT: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  suggestedVendorPaymentType: {
                    label: "Suggested Payment Type",
                    required: false,
                    registerField: "suggestedVendorPaymentType",
                    type: "select",
                    default: "Deduct",
                    displayLabel: "name",
                    options: [
                      {
                        name: "Select",
                        id: "",
                      },
                      {
                        name: "Deduct",
                        id: "Deduct",
                      },
                      {
                        name: "Invoice",
                        id: "Invoice",
                      },
                    ],
                    apiUrl: "",
                    slice: "",
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "ABS Vendor Payment Type is Required",
                      },
                    },
                    tooltip:
                      "This is auto generated based on your selections.\n You can edit this if you want.",
                    mapperKey:
                      "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
                  },
                  acApOrArNumber: {
                    label: "Suggested A/P or A/R Number",
                    required: false,
                    registerField: "acApOrArNumber",
                    type: "number",
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "A/P or A/R Vendor Number is Required",
                      },
                      formatError: {
                        pattern: {},
                        text: "Please enter valid A/P or A/R Vendor Number",
                      },
                      maxLength: {
                        backgroundColor: "",
                        text: "Max Length Reached",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.allowanceBillingInfo.acApOrArNumber",
                  },
                  vendorComment: {
                    label: "Vendor Comment",
                    required: false,
                    registerField: "vendorComment",
                    type: "text",
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "Vendor Comment is Required",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.allowanceBillingInfo.vendorComment",
                  },
                  vendorOfferTrackingNbr: {
                    label: "",
                    required: false,
                    registerField: "vendorOfferTrackingNbr",
                    type: "text",
                    errors: {
                      required: {
                        backgroundColor: "",
                        text: "Vendor Tracking Number is Required",
                      },
                    },
                    mapperKey:
                      "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
                  },
                  billingInformationData: {
                    registerKeyName: "billingInformationData",
                  },
                },
                isOpen: false,
                allowanceTypeAndPerformance: ["CASE"],
                allowanceToBeCreated: ["Both", "Warehouse Only"],
                label: "Warehouse Default Billing Information",
                offerTypeKey: "WAREHOUSE",
                allowanceScreenTypes: ["DP", "AO"],
                create: {
                  isEdit: false,
                  isView: false,
                  label: "Save & Continue",
                },
                edit: {
                  isEdit: true,
                  isView: false,
                  label: "Update & Continue",
                },
              },
            },
            headerButton: "New",
            nextLevel: true,
            cardLayout: true,
            cardFieldProp: "offerAllowances",
            cardFieldSubProp: "allowances",
            nextLevelLabel: "Skip to Promotion Details",
            allAnotherItem: "Add Another Allowance",
            addAnotherOfferAllowance: "Add Another Offer & Allowance",
            saveAndContinue: "Save & Continue",
            saveAndCreateAllowance: "Save & Create Allowance",
            permission: "PROMOTION_ALLOWANCE_MGMT_EDIT",
            module: "ALLOWANCE",
            previewModuleKey: "allowance",
          }}
          cardIndex={0}
          isNew={false}
          step={0}
          sectionIndex={1}
          isMultiple={true}
          cardItemIndex={0}
        />
      </Wrapper>
    );
  });
});
