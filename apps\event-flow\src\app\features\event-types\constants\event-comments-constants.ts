import { isMerchantLoggedIn } from "@me-upp-js/utilities";
import { History, X } from "lucide-react";
import { CommentCategory } from "../interfaces/plan-event-comment-interface";

export const EVENT_COMMENTS = {
  COMMENTS_SECTION_HEADER: "Event Comments",
  ADD_NEW: "Add New",
  VIEW_ALL: "View All",
  WIDTH: "700px",
  EXTERNAL: "External",
  INTERNAL: "Internal",
  BILLING_INQUIRY: "Billing Inquiry",
  COMMENT_TEXT_AREA_PLACEHOLDER: "Creating a draft. Please add details..",
  VENDOR_MERCHANT_TEXT_AREA_LABEL: "Vendor-Merchant Communications",
  INTERNAL_TEXT_AREA_LABEL: "Internal Instructions",
  BILLING_INQUIRY_TEXT_AREA_LABEL: "Adding details..",
  MERCHANT_TOOLTIP: "Vendor - Merchant Communications & Internal Instructions",
  VENDOR_MERCHANT_TOOLTIP:
    "Vendor Partners - anything typed in this box will generate a Task for your <PERSON> to review what you type!  Please only use to discuss this Event.  This text box is not to be used for any other purposes and it's contents will not be visible on the Allowance Agreement.",
  INTERNAL_TOOLTIP: "This comment will be visible to Albertsons team only",
  BILLING_INQUIRY_TOOLTIP: "Billing Inquiry Tooltip",
  ADD_COMMENT_BUTTON: "Add Comment",
  EXTERNAL_VENDOR_MAX_CHARACTER: 300,
  INTERNAL_MAX_CHARACTER: 300,
  BILLING_INQUIRY_MAX_CHARACTER: 300,
  SEND_EVENT_BACK: "Send Event Back",
  SEND_EVENT_BACK_HELPER_TEXT: `By sending Event back to the vendor, you won't be able to make
                any further edits. Provide instructions to the Vendor for
                neccessary changes to this Event.`,
  ADD_COMMENT_LABEL_FOR_SEND_TO_VENDOR: "Add Comment & Send To Vendor",
  EVENT_SENT_BACK_FLAG: "Event sent back with comment",
  DEFAULT_COMMENTS_TAB: 0,
  EVENT_COMMENTS_NOTE_TEXT: {
    text1: `Vendor Partners: Use this box only to 
          discuss this Event. `,
    text2: "Anything you type will create a Task for your Merchant to review. ",
    text3: "This will not appear on the Allowance Agreement.",
  },

  TASK_STATUS_COMMENT_MAPPER: {
    Completed: {
      linkText: "Add to Task List",
      taskStatusInApi: "Pending",
      Icon: History,
      actionButtonText: "Add to Task List",
    },
    Pending: {
      linkText: "Remove from Task List",
      taskStatusInApi: "Completed",
      Icon: X,
      actionButtonText: "Close Inquiry",
    },
  },
  TASK_COMMENTS_OPTIONS: ["AllowanceInquiry", "Comment"],
  TASKS_COMMENTS_CATEGORY: [
    CommentCategory.BILLING_INQUIRY,
    CommentCategory.EXTERNAL,
  ],
  TASK_COMMENTS_SECTION_MAPPER: {
    ALLOWANCE_INQUIRY: {
      commentDefaultTab: 2,
      commentCategory: CommentCategory.BILLING_INQUIRY,
      userTypeCheck: isMerchantLoggedIn,
      allowAll: false,
    },
    COMMENT: {
      commentDefaultTab: 0,
      commentCategory: CommentCategory.EXTERNAL,
      userTypeCheck: null,
      allowAll: true,
    },
  },
  COMMENTS_FOR_OFFER_PROMO_LIST_FEATURE: {
    LABEL: "Comments for",
  },
};
