import Divider from "@albertsons/uds/molecule/Divider";
import { DateFormat, dateFormat, editFieldHighlight } from "@me/util-helpers";
import {
  addDays,
  addMonths,
  compareAsc,
  isAfter,
  isBefore,
  isEqual,
} from "date-fns";
import moment from "moment";

export const getDateFormat = (date: string) => {
  return dateFormat(date, DateFormat["YYYY-MM-DD"]);
};

export function getMinMaxDates(datesArray, startDate, endDate) {
  datesArray?.sort(compareAsc);
  return datesArray;
}

export const convertToUTCandPush = date => {
  return new Date(date?.replace(/-/g, "/"));
};

export function getDate(date) {
  return moment(date).format("MM/DD/YY");
}

export function getStartDate(datesArray, startDate, endDate) {
  const array = getMinMaxDates(datesArray, startDate, endDate);
  return getDate(array[0]);
}
export function getEndDate(datesArray, startDate, endDate) {
  const array = getMinMaxDates(datesArray, startDate, endDate);

  return getDate(array[array.length - 1]);
}

export function addDaysHelper(date: string, days: number) {
  if (date === "") {
    return new Date();
  }
  const formatedDate = dateFormat(date, DateFormat["YYYY-MM-DD"]);
  const result = addDays(new Date(formatedDate.replace(/-/g, "/")), days);
  return dateFormat(result, DateFormat["YYYY-MM-DD"]);
}

export function addMonthsHelper(date: string, months = 6) {
  if (date === "") {
    return new Date();
  }
  const result = addMonths(new Date(date), months);
  return dateFormat(result, DateFormat["YYYY-MM-DD"]);
}

export function getLocation(string) {
  const locationName = string?.split("-");
  return locationName ? locationName[2] : "";
}

export const getVendorIndex = (
  key,
  datesArrayFromForm,
  keyName = "distCenter"
) => {
  let ind = 0;
  datesArrayFromForm.map((vendorFromForm, index) => {
    if (vendorFromForm?.[keyName] === key) {
      ind = index;
    }
    return vendorFromForm;
  });
  return ind;
};

export function addCaseWarehouseDates(vendors) {
  return vendors?.map(vendor => {
    return {
      distCenter: vendor?.distCenter,
      orderDates: {
        startDate:
          vendor?.orderStartDate &&
          dateFormat(vendor?.orderStartDate, DateFormat["YYYY-MM-DD"]),
        endDate:
          vendor?.orderEndDate &&
          dateFormat(vendor?.orderEndDate, DateFormat["YYYY-MM-DD"]),
      },
      arrivalDates: {
        startDate:
          vendor?.arrivalStartDate &&
          dateFormat(vendor?.arrivalStartDate, DateFormat["YYYY-MM-DD"]),
        endDate:
          vendor?.arrivalEndDate &&
          dateFormat(vendor?.arrivalEndDate, DateFormat["YYYY-MM-DD"]),
      },
    };
  });
}

export function getYearFromDate(date) {
  return new Date(date?.replace(/-/g, "/")).getFullYear();
}

export function getTimestamp(date?: string) {
  return date ? new Date(date).getTime() || 0 : 0;
}

export function checkInvalidDates(
  datesArray,
  isWarehouse = false,
  vehicleStart: string,
  vehicleEnd: string,
  typeKey = "vendorNbr"
) {
  const inValidVendors: Set<any> = new Set([]);
  let type = {};
  datesArray?.forEach(vendor => {
    if (!vendor?.[typeKey]) return;
    type = {
      ...type,
      [vendor?.[typeKey]]: {
        arrival: false,
        order: false,
        ship: false,
        vehicle: false,
        orderStart: false,
        orderEnd: false,
        ...(type[vendor?.[typeKey]] || {}),
      },
    };
    const arraivalStartDate = new Date(vendor?.arrivalStartDate),
      arraivalEndDate = new Date(vendor?.arrivalEndDate),
      orderStartDate = new Date(vendor?.orderStartDate),
      orderEndDate = new Date(vendor?.orderEndDate),
      vehicleStartDate = new Date(vehicleStart),
      vehicleEndDate = new Date(vehicleEnd);

    if (isAfter(arraivalStartDate, arraivalEndDate)) {
      inValidVendors.add(vendor?.[typeKey]);
      type[vendor?.[typeKey]][isWarehouse ? "arrival" : "ship"] = true;
    }
    if (isWarehouse && isAfter(orderStartDate, orderEndDate)) {
      inValidVendors.add(vendor?.[typeKey]);
      type[vendor?.[typeKey]].order = true;
    }
    if (
      (!isEqual(arraivalStartDate, vehicleEndDate) &&
        !isBefore(arraivalStartDate, vehicleEndDate)) ||
      (!isEqual(arraivalEndDate, vehicleStartDate) &&
        !isAfter(arraivalEndDate, vehicleStartDate))
    ) {
      inValidVendors.add(vendor?.[typeKey]);
      type[vendor?.[typeKey]].vehicle = true;
    }
    if (
      isWarehouse &&
      !isEqual(orderStartDate, arraivalStartDate) &&
      !isBefore(orderStartDate, arraivalStartDate)
    ) {
      inValidVendors.add(vendor?.[typeKey]);
      type[vendor?.[typeKey]].orderStart = true;
    }
    if (
      isWarehouse &&
      !isEqual(orderEndDate, arraivalEndDate) &&
      !isBefore(orderEndDate, arraivalEndDate)
    ) {
      inValidVendors.add(vendor?.[typeKey]);
      type[vendor?.[typeKey]].orderEnd = true;
    }
  });
  return { inValidVendors: [...inValidVendors], type };
}

export const checkIsValidDate = (date: string) => date && date !== "0001-01-01";

export function checkInvalidScanDates(
  datesArray,
  vehicleStart: string,
  vehicleEnd: string,
  typeKey = "vendorNbr"
) {
  const inValidVendors: Set<any> = new Set([]);
  let type = {};
  datesArray?.forEach(vendor => {
    if (!vendor?.[typeKey]) return;
    type = {
      ...type,
      [vendor?.[typeKey]]: {
        all: false,
        vehicle: false,
        ...(type[vendor?.[typeKey]] || {}),
      },
    };
    const startDate = new Date(
        checkIsValidDate(vendor?.orderStartDate)
          ? vendor?.orderStartDate
          : vehicleStart
      ),
      endDate = new Date(
        checkIsValidDate(vendor?.orderEndDate)
          ? vendor?.orderEndDate
          : vehicleEnd
      ),
      vehicleStartDate = new Date(vehicleStart),
      vehicleEndDate = new Date(vehicleEnd);
    if (isAfter(startDate, endDate)) {
      inValidVendors.add(vendor?.[typeKey]);
      type[vendor?.[typeKey]]["all"] = true;
    }
    if (
      (!isEqual(startDate, vehicleEndDate) &&
        !isBefore(startDate, vehicleEndDate)) ||
      (!isEqual(endDate, vehicleStartDate) &&
        !isAfter(endDate, vehicleStartDate))
    ) {
      inValidVendors.add(vendor?.[typeKey]);
      type[vendor?.[typeKey]].vehicle = true;
    }
  });
  return { inValidVendors: [...inValidVendors], type };
}

export const renderVendorDetails = ({
  vendorName = "",
  vendorNbr = "",
  costAreaDesc = "",
}) => (
  <div className="flex flex-grow-0 flex-shrink-0 w-full h-[45px] rounded bg-[#F1F4F9] border-[#c8daeb] mt-4">
    <div className="flex justify-start items-center opacity-80 gap-3 py-3">
      <div className="flex-grow-0 flex-shrink-0">
        <p className="flex text-base text-left text-[20px] text-[#0D2D49] pl-[10px] font-sans">
          <span className="font-bold pr-1">{vendorName}</span>
          <span>-</span>
          <span className="pl-1 pr-1">{vendorNbr}</span>
          <span className="pl-1 pr-1">{costAreaDesc}</span>
        </p>
      </div>
    </div>
  </div>
);

export const renderWarehouseDetails = (
  vendor,
  isS2sOffer = false,
  isScanOffer = false
) => (
  <div className="flex flex-grow-0 flex-shrink-0 w-full h-[45px] rounded bg-[#F1F4F9] border-[#c8daeb] mt-4">
    <div className="flex justify-start items-center opacity-80 gap-3 py-3">
      <div className="flex-grow-0 flex-shrink-0">
        <p className="flex text-base text-left text-[20px] text-[#0D2D49] pl-[10px] font-sans">
          <span className="font-bold pr-1">{vendor?.distCenter}</span>
          <span>-</span>
          <span className="pl-1 pr-1">{getLocation(vendor?.locationName)}</span>
          <span>Warehousing</span>
          {!isScanOffer && (
            <>
              <span>
                <Divider className="mx-2" height={24} color="#97c4ed" />
              </span>
              <span className="pl-0.5">Lead Time :</span>
              <span className="pl-1 text-[#355d83]">
                {`${Math.abs(
                  vendor?.allowanceDateOffsets?.[
                    isS2sOffer ? "startDateOffset" : "resolvedLeadTimeDays"
                  ]
                )} Days`}
              </span>
            </>
          )}
        </p>
      </div>
    </div>
  </div>
);

export const getFieldWithHighletedWrapper = (
  field,
  fieldProps,
  getEventData,
  searchId
) => {
  return (
    <div
      className={`${editFieldHighlight(
        fieldProps.mapperKey,
        getEventData,
        searchId,
        "allowance"
      )}`}
    >
      {field}
    </div>
  );
};

export const getUpdatedDatesArray = (
  vendorsArray: any,
  vehicleDates: { vehicleStart: string; vehicleEnd: string },
  isValidOffer: boolean,
  divisionId?: string
) => {
  const data = vendorsArray?.map(vendor => {
    const startDate =
      isValidOffer && checkIsValidDate(vendor?.["orderStartDate"])
        ? vendor?.["orderStartDate"]
        : vehicleDates?.vehicleStart;
    const endDate =
      isValidOffer && checkIsValidDate(vendor?.["orderEndDate"])
        ? vendor?.["orderEndDate"]
        : vehicleDates?.vehicleEnd;
    return {
      ...vendor,
      defaultAllowanceDates: {
        ...vendor?.["defaultAllowanceDates"],
        overrideInd: true,
      },
      allowanceStartDate: vehicleDates?.vehicleStart,
      allowanceEndDate: vehicleDates?.vehicleEnd,
      arrivalStartDate: startDate,
      arrivalEndDate: endDate,
      orderStartDate: startDate,
      orderEndDate: endDate,
      shipStartDate: startDate,
      shipEndDate: endDate,
      vehicleStartDate: vehicleDates?.vehicleStart,
      vehicleEndDate: vehicleDates?.vehicleEnd,
      divisionId,
    };
  });
  return data;
};

export function createDateKeyMapperObject(isStartDate: boolean, date: string) {
  const label = isStartDate ? "StartDate" : "EndDate";
  return {
    [`arrival${label}`]: date,
    [`order${label}`]: date,
    [`ship${label}`]: date,
  };
}

export const camelCaseVendorName = (name: string) => {
  if (!name) return "";
  return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase();
};
