import { useCallback, useEffect, useMemo, useState } from "react";
import SearchBox from "./SearchBox";
import SelectionItemsList from "./SelectionItemsList";
import SelectDeselectAllContainer from "./SelectDeselectAllContainer";
import { useDispatch } from "react-redux";
import { setSelectedStoreGroups } from "../../create-event/service/slice/event-detail-slice";
import { searchData, selectDeselectAll } from "../helper-functions-ef";
import { useFormContext } from "react-hook-form";

const SelectionListContainer = ({
  configObj,
  onItemSelection,
  dropDownData,
  selectedData,
}) => {
  const { uniqueId, showSearchBox, id, searchItemKey, showSelectAllFeature } =
    configObj ?? {};

  const dispatch = useDispatch();
  const { clearErrors } = useFormContext();
  const originalData = useMemo(() => dropDownData, [dropDownData]);

  const [updatedData, setUpdatedData] = useState<Record<string, any>[]>([]);
  const [searchedText, setSearchedText] = useState("");

  useEffect(() => {
    setUpdatedData(dropDownData);
  }, [dropDownData]);

  const filterListBySearch = useCallback(
    (searchedText = "") => {
      const searchedData = searchData(
        originalData,
        searchItemKey,
        searchedText
      );
      setUpdatedData(searchedData);
    },
    [originalData, searchItemKey]
  );

  const handleSelection = useCallback(
    (selectedItemIds, selectedItems) => {
      if (id === "storeGroup") {
        dispatch(setSelectedStoreGroups(selectedItemIds));
        onItemSelection(selectedItems);
      }
    },
    [id, dispatch, onItemSelection]
  );

  const handleSelectDeselectAll = useCallback(() => {
    clearErrors(id);
    const { selectedItemIds, selectedItems } = selectDeselectAll(
      originalData,
      selectedData,
      uniqueId
    );
    handleSelection(selectedItemIds, selectedItems);
  }, [clearErrors, id, originalData, selectedData, uniqueId, handleSelection]);

  const onChangeInput = useCallback(
    e => {
      clearErrors(id);
      const { checked, value } = e.target;
      const selectedItemIds = checked
        ? [...selectedData, value]
        : selectedData?.filter(e => e !== value);
      const selectedItems = updatedData?.filter(item =>
        selectedItemIds?.includes(item?.[uniqueId])
      );
      handleSelection(selectedItemIds, selectedItems);
    },
    [clearErrors, id, selectedData, updatedData, handleSelection, uniqueId]
  );

  return (
    <div id={`abs-selection-list-container-${id}`}>
      {dropDownData?.length > 0 && showSelectAllFeature && (
        <SelectDeselectAllContainer
          configObj={configObj}
          handleClick={handleSelectDeselectAll}
          isChecked={dropDownData?.length === selectedData?.length}
          partialClass={
            selectedData?.length > 0 &&
            dropDownData?.length > selectedData?.length
          }
          isDisabled={dropDownData?.length === 0}
        />
      )}
      {showSearchBox && dropDownData?.length > 0 && (
        <SearchBox
          filterListBySearch={filterListBySearch}
          searchPlaceholder={configObj?.searchPlaceholder}
          searchedText={searchedText}
          setSearchedText={setSearchedText}
        />
      )}
      <div className="min-h-[200px] flex w-full">
        {updatedData?.length > 0 ? (
          <SelectionItemsList
            configObj={configObj}
            updatedData={updatedData}
            onChangeInput={onChangeInput}
            selectedData={selectedData}
          />
        ) : (
          <span></span>
        )}
      </div>
    </div>
  );
};

export default SelectionListContainer;
