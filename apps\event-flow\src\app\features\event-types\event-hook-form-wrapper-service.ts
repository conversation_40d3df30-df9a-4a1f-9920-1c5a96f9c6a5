import { getLoggedInUserType, getNationalOfferStatus, sortListByKey } from "@me-upp-js/utilities";
import { EEVENT_STATUS } from "./components/event-action/event-action.model";
import moment from "moment";
import efConstants from "../../shared/ef-constants/ef-constants";
import { checkIsWarehouseItem } from "../create-event/service/allowance/allowance-service";
import { getIsNationalEvent } from "./event-types-helper";
import { updateHistory } from "libs/features/event-history/src/lib/event-history-utils";

export const getEventDetails = eventDetails => {
  const {
    eventDetailsEventInd,
    allowanceEventInd,
    promotionEventInd,
    otherDetailsChangedInd,
    pidDetailsEventInd,
    planEventHistory,
    planEventPendingChanges,
  } = eventDetails;
  let { planEvent, planEventPending } = eventDetails;
  planEvent = updatedPlanForAllowance(planEvent, eventDetails);
  planEventPending = updatedPlanForAllowance(planEventPending, eventDetails);
  let promotionsList = sortListByKey(planEvent?.promotionsList, "promotionId");
  let offerAllowances = sortListByKey(
    planEvent?.offerAllowances,
    "offerNumber"
  );
  if ([EEVENT_STATUS.DRAFT].includes(planEvent?.eventStatus)) {
    let event = planEvent;
    if (planEvent?.eventType === "NDP") {
      event = {
        ...event,
        offerAllowances:
          event.offerAllowances && planEvent?.eventType === "NDP"
            ? updateOfferForNationalEvent(event)
            : event.offerAllowances,
      };
      const newofferAllowances =
        offerAllowances && planEvent?.eventType === "NDP"
          ? updateOfferForNationalEvent(planEvent)
          : offerAllowances;
      event = {
        ...event,
        promotionsList,
        offerAllowances: newofferAllowances,
      };
    } else {
      event = {
        ...event,
        promotionsList,
        offerAllowances,
      };
    }
    return { updatedEventDetails: event };
  }
  const userType = getLoggedInUserType();
  let event = planEvent;

  const isEventUpdatedUser =
    eventDetailsEventInd && isPendingCollectionUser(planEventPending);
  if (isEventUpdatedUser) {
    event = planEventPending;
    event = { ...event, promotionsList, offerAllowances };
  }
  const { filteredOffers, filteredOfferIDsArray: allowances } =
    getUpdatedOfferAllownces(
      allowanceEventInd,
      planEvent,
      planEventPending,
      userType,
      planEventPendingChanges
    );
  offerAllowances = sortListByKey(filteredOffers, "offerNumber");
  event = { ...event, offerAllowances, allowances };
  const { filteredPromos, filteredPromoIDsArray: promotions } =
    getUpdatedPromotions(
      promotionEventInd,
      planEvent,
      planEventPending,
      userType
    );
  promotionsList = sortListByKey(filteredPromos, "promotionId");
  event = { ...event, promotionsList, promotions };
  if (planEvent?.eventType === "NDP") {
    event.offerAllowances =
      event.offerAllowances && planEvent?.eventType === "NDP"
        ? updateOfferForNationalEvent(event)
        : event.offerAllowances;
    const newPlanEvent = {
      ...planEvent,
      offerAllowances:
        planEvent?.eventType === "NDP"
          ? updateOfferForNationalEvent(planEvent)
          : planEvent?.offerAllowances,
    };
    const sortedPlanEvent = getSortedPlanEvent(newPlanEvent);
    const newPlanEventPending = {
      ...planEventPending,
      offerAllowances:
        planEventPending.offerAllowances && planEvent?.eventType === "NDP"
          ? formPendingChanges(planEvent, planEventPending)
          : planEventPending?.offerAllowances,
    };
    const newplanEventPendingChanges = {
      ...planEventPendingChanges,
      offerAllowanceChanges:
        planEventPendingChanges?.offerAllowanceChanges &&
          planEvent?.eventType === "NDP"
          ? updateOfferChanges(planEventPendingChanges?.offerAllowanceChanges)
          : planEventPendingChanges?.offerAllowanceChanges,
    };
    const newPlanEventHistory = planEventHistory?.map((item: any) => {
      return {
        ...item,
        offerAllowanceChanges:
          item?.offerAllowanceChanges && planEvent?.eventType === "NDP"
            ? updateHistory(item?.offerAllowanceChanges)
            : item?.offerAllowances,
      };
    });
    event = {
      ...event,
      planEvent: sortedPlanEvent,
      planEventPending: newPlanEventPending,
      eventDetailsEventInd,
      otherDetailsChangedInd,
      pidDetailsEventInd,
      allowanceEventInd,
      promotionEventInd,
      planEventHistory: newPlanEventHistory,
      planEventPendingChanges: newplanEventPendingChanges,
    };
  } else {
    const sortedPlanEvent = getSortedPlanEvent(planEvent);
    event = {
      ...event,
      planEvent: sortedPlanEvent,
      planEventPending,
      eventDetailsEventInd,
      otherDetailsChangedInd,
      pidDetailsEventInd,
      allowanceEventInd,
      promotionEventInd,
      planEventHistory,
      planEventPendingChanges
    };
  }
  return { updatedEventDetails: event };
};

const updateOfferForNationalEvent = (planEvent: any): any => {
  // form new offerallowances from array offers where group by offerNumber
  const offerAllowances = {};
  planEvent?.offerAllowances?.forEach(item => {
    if (!offerAllowances[item?.offerNumber]) {
      offerAllowances[item?.offerNumber] = [];
    }
    offerAllowances[item?.offerNumber]?.push(item);
  });

  const newOffers: any[] = [];
  // in offerAllowances object leave other fields of 1 object and merge allowances array to one object allowances and delete all other objects
  Object.values(offerAllowances).forEach((offers: any) => {
    if (offers.length > 0) {
      const baseOffer: any = { ...offers[0], allowances: [] };
      offers?.forEach(offer => {
        offer?.allowances?.forEach((allow: any) => {
          baseOffer?.allowances?.push({
            ...allow,
            divisionIds: offer?.divisionIds,
            offerAllowanceId: offer?.id,
          });
        });
      });
      newOffers.push(baseOffer);
    }
  });

  // sort newOffers.allowances by divisionIds
  newOffers?.forEach(offer => {
    offer.allowances = offer.allowances.sort((a, b) => {
      return a?.divisionIds?.[0] - b?.divisionIds?.[0];
    });
  });
  return newOffers;
};

const formPendingChanges = (planEvent: any, planEventPending: any): any => {
  // form new offerallowances array with all planEvent offerAllowances replaced with offerAllowances present in planEventPending
  const offerNumbers = planEventPending?.offerAllowances?.map(item => item.offerNumber);
  const planEventUpdated = planEvent?.offerAllowances?.map(item => offerNumbers?.includes(item.offerNumber) && item);
  const offerAllowances = planEventUpdated?.map(item => {
    const found = planEventPending?.offerAllowances?.find(
      entry => entry.id === item.id
    );
    return found ? { ...item, ...found } : item;
  });
  const newPlanEventPending = {
    ...planEventPending,
    offerAllowances: offerAllowances,
  };
  return updateOfferForNationalEvent(newPlanEventPending);
};

const updateOfferChanges = (planEventPending: any): any => {
  const offerAllowances = {};
  planEventPending?.forEach(item => {
    if (!offerAllowances[item?.offerNumber]) {
      offerAllowances[item?.offerNumber] = [];
    }
    offerAllowances[item?.offerNumber]?.push(item);
  });

  const newOffers: any[] = [];
  // in offerAllowances object leave other fields of 1 object and merge allowances array to one object allowances and delete all other objects
  Object.keys(offerAllowances)?.forEach((key, index) => {
    const newOffer = {
      ...(offerAllowances?.[key]?.[0] || {}),
      changes: [...(offerAllowances?.[key]?.[0]?.changes || [])],
    };
    offerAllowances?.[key]?.forEach((item, ind) => {
      if (ind > 0) {
        newOffer.changes = [
          ...(newOffer?.changes || []),
          ...(item?.changes || []),
        ];
      }
    });
    newOffers?.push(newOffer);
  });

  return newOffers;
};

const checkAllowanceIsExcluded = (
  planEventInfo,
  offerNumber,
  allowanceIdNbr
) => {
  let offerChanges = planEventInfo?.offerAllowanceChanges?.filter(
    record => record?.offerNumber === offerNumber
  );
  offerChanges = offerChanges?.[0]?.changes?.filter(
    ({ historyGroupId, labelFieldName, beforeVal, afterVal }) =>
      historyGroupId === allowanceIdNbr?.toString() &&
      labelFieldName === efConstants?.INCLUDEIND_LABEL &&
      beforeVal === "true" &&
      afterVal === "false"
  );
  return !!offerChanges?.length;
};

const checkIsValidUser = (allowanceStatus, offerNumber, planEventPending) => {
  const loggedinuser = getLoggedInUserType();
  return (
    efConstants.STATUS_USER_MAPPER?.[loggedinuser] === allowanceStatus ||
    ([
      EEVENT_STATUS.EXECUTED,
      EEVENT_STATUS.AGREED,
      EEVENT_STATUS.ACTIVE,
    ].includes(allowanceStatus) &&
      planEventPending?.offerAllowances
        ?.filter(offer => offer?.offerNumber === offerNumber)?.[0]
        ?.updateUser?.type?.toUpperCase() === loggedinuser)
  );
};

const getExcludeInfo = (
  planData,
  eventDetails,
  offerNumber,
  allowanceStatus,
  allowanceIdNbr
) => {
  const {
    allowanceEventInd = false,
    planEventHistory = [],
    planEventPending = {},
    planEventPendingChanges = {},
  } = eventDetails || {};
  if ([planData?.eventStatus, allowanceStatus].includes("Draft")) {
    return false;
  }
  return checkAllowanceIsExcluded(
    allowanceEventInd &&
      checkIsValidUser(allowanceStatus, offerNumber, planEventPending)
      ? planEventPendingChanges
      : planEventHistory[planEventHistory.length - 1],
    offerNumber,
    allowanceIdNbr
  );
};

const validateDistConditionExcludedInfo = (
  allowance,
  offerNumber,
  planData,
  eventDetails
) => {
  const {
    allowanceIdNbr = "",
    leadDistributorInd = false,
    includeInd = false,
    leadDistributorMode = "",
    allowanceStatus = "",
  } = allowance || {};
  if (
    (leadDistributorInd ||
      leadDistributorMode === efConstants?.BILL_LEAD_DIST ||
      checkIsWarehouseItem(allowance)) &&
    includeInd
  )
    return allowance;
  else if (!includeInd) {
    return {
      ...allowance,
      excludedTag: getExcludeInfo(
        planData,
        eventDetails,
        offerNumber,
        allowanceStatus,
        allowanceIdNbr
      ),
    };
  }
  return null;
};

export const updatedPlanForAllowance = (planData, eventDetails) => {
  if (!planData) return planData;
  const offerAllowances = planData?.offerAllowances?.reduce(
    (offersData, offer) => {
      const { offerNumber, allowances } = offer || {};
      const allowancesData = allowances?.reduce((allowancesData, allowance) => {
        const distAllowanceData = validateDistConditionExcludedInfo(
          allowance,
          offerNumber,
          planData,
          eventDetails
        );
        return distAllowanceData
          ? [...allowancesData, distAllowanceData]
          : allowancesData;
      }, []);
      return allowancesData?.length
        ? [
          ...offersData,
          {
            ...offer,
            allowances: allowancesData,
          },
        ]
        : offersData;
    },
    []
  );

  return {
    ...planData,
    offerAllowances,
  };
};

export const getSortedPlanEvent = planEvent => {
  const promotionsList = sortListByKey(
    planEvent?.promotionsList,
    "promotionId"
  );
  const offerAllowances = sortListByKey(
    planEvent?.offerAllowances,
    "offerNumber"
  );
  return {
    ...planEvent,
    promotionsList,
    offerAllowances,
  };
};
export const getUpdatedOfferAllownces = (
  allowanceEventInd,
  planEvent,
  planEventPending,
  userType,
  planEventPendingChanges
) => {
  if (allowanceEventInd) {
    let filteredOffers: any = [];
    let filteredOfferIDsArray: any = [];
    const eventType = planEvent?.eventType;
    const draftAllowancesListPlan = getAllowanceDraftCollections(
      planEvent?.offerAllowances,
      "allowances",
      "allowanceStatus"
    );
    const draftAllowancesListPlanPending = getAllowanceDraftCollections(
      planEventPending?.offerAllowances,
      "allowances",
      "allowanceStatus"
    );
    const isLastUpdatedUser = isPendingCollectionUser(
      planEventPending?.offerAllowances
    );
    const nonPendingOfferPlan = planEvent?.offerAllowances?.filter(
      planOffer =>
        !planEventPending?.offerAllowances?.some(
          pendingOffer => planOffer?.id === pendingOffer?.id
        )
    );
    const { isNDPAndFlagEnabled: isNational } = getIsNationalEvent(eventType);
    const nonPendingDraftOfferPlan = nonPendingOfferPlan.filter(offer =>
      isNational
        ? getNationalOfferStatus(offer?.allowances) !== EEVENT_STATUS.DRAFT
        : offer?.allowances[0]?.allowanceStatus !== EEVENT_STATUS.DRAFT
    );
    if (isLastUpdatedUser) {
      const draftOffersPlanByUser = draftAllowancesListPlan?.filter(
        planOffer =>
          !draftAllowancesListPlanPending?.some(
            draftOffer => planOffer.id === draftOffer.id
          )
      );

      filteredOffers = [
        ...planEventPending.offerAllowances,
        ...draftOffersPlanByUser,
        ...nonPendingDraftOfferPlan,
      ];
      filteredOfferIDsArray = getOfferAndPromoIDs(filteredOffers);
      return { filteredOffers, filteredOfferIDsArray };
    } else {
      const isDraftOfferUser = draftAllowancesListPlan.length
        ? isPendingCollectionUser(draftAllowancesListPlan)
        : false;
      if (isDraftOfferUser) {
        filteredOffers = [...planEvent.offerAllowances];
        filteredOfferIDsArray = getOfferAndPromoIDs(filteredOffers);
        return { filteredOffers, filteredOfferIDsArray };
      } else {
        const nonDraftOffers = planEvent?.offerAllowances?.filter(
          planOffer =>
            !draftAllowancesListPlan?.some(
              draftOffer => planOffer?.id === draftOffer?.id
            )
        );
        filteredOffers = [...nonDraftOffers];
        filteredOfferIDsArray = getOfferAndPromoIDs(filteredOffers);
        return { filteredOffers, filteredOfferIDsArray };
      }
    }
  } else {
    // Indicator is false, so we are checking planEvent obj.
    const filteredOfferAllowances = filterOfferAllowances(planEvent, userType);
    return filteredOfferAllowances;
  }
};

export const getUpdatedPromotions = (
  promotionEventInd,
  planEvent,
  planEventPending,
  userType
) => {
  if (promotionEventInd) {
    let filteredPromos: any = [];
    let filteredPromoIDsArray: any = [];
    const draftPromotionsListPlan = getPromoDraftCollections(
      planEvent.promotionsList,
      "promotionsList",
      "promotionWorkflowStatus",
      userType
    );
    const draftPromotionsListPlanPending = getPromoDraftCollections(
      planEventPending.promotionsList,
      "promotionsList",
      "promotionWorkflowStatus",
      userType
    );
    const nonPendingPromoPlan = planEvent?.promotionsList?.filter(
      planPromo =>
        !planEventPending?.promotionsList?.some(
          pendingPromo => planPromo?.id === pendingPromo?.id
        )
    );
    const nonPendingDraftPromoPlan = nonPendingPromoPlan.filter(
      promo => promo?.promotionWorkflowStatus !== EEVENT_STATUS.DRAFT
    );
    const isLastUpdatedUser = isPendingCollectionUser(
      planEventPending?.promotionsList
    );
    if (isLastUpdatedUser) {
      const draftPromosPlanByUser = draftPromotionsListPlan?.filter(
        planPromo =>
          !draftPromotionsListPlanPending?.some(
            draftPromo => planPromo?.id === draftPromo?.id
          )
      );
      filteredPromos = [
        ...planEventPending.promotionsList,
        ...draftPromosPlanByUser,
        ...nonPendingDraftPromoPlan,
      ];
      filteredPromoIDsArray = getOfferAndPromoIDs(filteredPromos);
      return { filteredPromos, filteredPromoIDsArray };
    } else {
      const isDraftPromoUser = draftPromotionsListPlan.length
        ? isPendingCollectionUser(draftPromotionsListPlan)
        : false;
      if (isDraftPromoUser) {
        filteredPromos = [...planEvent.promotionsList];
        filteredPromoIDsArray = getOfferAndPromoIDs(filteredPromos);
        return { filteredPromos, filteredPromoIDsArray };
      } else {
        const nonDraftPromos = planEvent?.promotionsList?.filter(
          planPromo =>
            !draftPromotionsListPlan?.some(
              draftPromo => planPromo?.id === draftPromo?.id
            )
        );
        filteredPromos = [...nonDraftPromos];
        filteredPromoIDsArray = getOfferAndPromoIDs(filteredPromos);
        return { filteredPromos, filteredPromoIDsArray };
      }
    }
  } else {
    const filteredPromotions = filterPromotions(planEvent, userType);
    return filteredPromotions;
  }
};

export const getOfferAndPromoIDs = data => {
  return data?.map(item => {
    return item?.id;
  });
};

export const getAllowanceDraftCollections = (data, moduleKey, statusKey) => {
  const draftCollections = data?.filter(offer => {
    return (
      offer?.[moduleKey]?.some(
        allow => allow?.[statusKey] === EEVENT_STATUS.DRAFT
      ) || offer?.[moduleKey][0]?.[statusKey] === EEVENT_STATUS.DRAFT
    );
  });
  return draftCollections;
};

export const getPromoDraftCollections = (
  data,
  moduleKey,
  statusKey,
  userType
) => {
  const draftCollections = data?.filter(promo => {
    return promo[statusKey] === EEVENT_STATUS.DRAFT;
  });
  return draftCollections;
};

export const filterPromotions = (planEvent, userType) => {
  let filteredPromos: any = [];
  let filteredPromoIDsArray: any = [];
  for (const promotion of planEvent?.promotionsList || []) {
    if (promotion?.promotionWorkflowStatus === EEVENT_STATUS.DRAFT) {
      if (promotion?.updateUser?.type?.toUpperCase() === userType) {
        filteredPromos = [...filteredPromos, promotion];
        filteredPromoIDsArray = getOfferAndPromoIDs(filteredPromos);
      }
    } else {
      filteredPromos = [...filteredPromos, promotion];
      filteredPromoIDsArray = getOfferAndPromoIDs(filteredPromos);
    }
  }
  return { filteredPromos, filteredPromoIDsArray };
};

export const filterOfferAllowances = (planEvent: any, userType) => {
  let filteredOffers: any = [];
  let filteredOfferIDsArray: any = [];
  const offerAllowances = planEvent?.offerAllowances;
  const eventType = planEvent?.eventType;
  const { isNDPAndFlagEnabled: isNational } = getIsNationalEvent(eventType);
  // for (const offer of offerAllowances) {
  offerAllowances?.forEach(offer => {
    //
    const isStatusDraft = isNational ?
      getNationalOfferStatus(offer?.allowances) === EEVENT_STATUS.DRAFT
      : offer?.allowances[0]?.allowanceStatus === EEVENT_STATUS.DRAFT;
    if (isStatusDraft) {
      if (offer?.updateUser?.type?.toUpperCase() === userType) {
        //If status is Draft and create by logged in User, update the
        filteredOffers = [...filteredOffers, offer];
        filteredOfferIDsArray = getOfferAndPromoIDs(filteredOffers);
      }
    } else {
      //Except Draft, all the offers are pushed into filteredOffers array
      filteredOffers = [...filteredOffers, offer];
      filteredOfferIDsArray = getOfferAndPromoIDs(filteredOffers);
    }
  });
  // }
  return { filteredOffers, filteredOfferIDsArray };
};

export const isPendingCollectionUser = data => {
  if (toString.call(data) === "[object Array]") {
    const latestUpdatedElement = data?.reduce((latestUpdatedEle, ele) => {
      return moment(latestUpdatedEle?.updateUser?.createTs) >
        moment(ele?.updateUser?.createTs)
        ? latestUpdatedEle
        : ele;
    });
    return latestUpdatedElement?.updateUser?.type?.toUpperCase() ===
      getLoggedInUserType()
      ? true
      : false;
  } else {
    if (data?.updateUser?.type?.toUpperCase() === getLoggedInUserType())
      return true;
    else return false;
  }
};

export const getUpdatedPromoWithPromoDetails = promotionsLists => {
  const [{ promotionsList }] = promotionsLists;
  const updatedPromos = promotionsList.map(promo => {
    let { promoDetails } = promo;
    promoDetails = {
      ...promoDetails,
      itemLimit:
        parseInt(promoDetails?.itemLimit) === 0 ? "" : promoDetails?.itemLimit,
    };
    promo = { ...promo, promoDetails };
    return promo;
  });
  return [{ promotionsList: updatedPromos }];
};
