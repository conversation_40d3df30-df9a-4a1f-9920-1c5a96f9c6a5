import {
  ApolloGateway,
  IntrospectAndCompose,
  RemoteGraphQLDataSource,
} from "@apollo/gateway";
import { ApolloServer } from "apollo-server-express";
import * as express from "express";
import path = require("path");
import cors = require("cors");
import fs = require("fs");
import actuator = require("express-actuator");
import compression = require("compression");
// import gitInfo = require("node-git-info");
import { expressMiddleware } from "@apollo/server/express4";

// Bootstrap the server
const bootstrap = async () => {
  const app = express();
  app.use(cors());
  app.use(compression());
  const appEnv = process.env["NX_REACT_APP_ENV"];

  const appRedirect = process.env["NX_REACT_AD_REDIRECT_URI"];
  // Constant to define the server context path
  const SERVER_SERVLET_CONTEXT_PATH = "/meupp";
  /** Reading the tag details from the git file and updating it in the actuator */
  // fs.readFile("apps/api/src/git-info.json", "utf8", (err, data) => {
  //   if (err) {
  //     console.error(err);
  //     return;
  //   }

  //   const gitInfo = JSON.parse(data);
  //   // console.log(gitInfo);
  // });
  const greeting = {
    message: `Welcome to shell! - env: ${appEnv}`,
  };

  app.get(SERVER_SERVLET_CONTEXT_PATH + "/api", (req, res) => {
    res.send(greeting);
  });
  app.get("/api/health", (req, res) => {
    res.send(greeting);
  });
  app.get(SERVER_SERVLET_CONTEXT_PATH + "/health", (req, res) => {
    res.send(greeting);
  });
  // app.use(
  //   actuator({
  //     basePath: `${SERVER_SERVLET_CONTEXT_PATH}/actuator`, // It will set /management/info instead of /info
  //     infoGitMode: "full", // the amount of git information you want to expose, 'simple' or 'full',
  //     infoBuildOptions: null, // extra information you want to expose in the build object. Requires an object.
  //     infoDateFormat: null, // by default, git.commit.time will show as is defined in git.properties. If infoDateFormat is defined, moment will format git.commit.time. See https://momentjs.com/docs/#/displaying/format/.
  //     customEndpoints: [], // array of custom endpoints
  //   })
  // );

  //Adding the apollo server and configuring the gateway
  const server = new ApolloServer({
    context: ({ req }) => ({
      req,
      /*
        this is the important bit:
        we extract the needed headers and then
        set up a customHeaders property on the context object
      */

      customHeaders: {
        headers: req.headers,
      },
    }),
    gateway: new ApolloGateway({
      supergraphSdl: new IntrospectAndCompose({
        subgraphs: [
          {
            name: "plan",
            url: `${appRedirect}plan/graphql`,
          },
          {
            name: "promotions",
            url: `${appRedirect}promotions/graphql`,
          },
          {
            name: "allowance",
            url: `${appRedirect}allowance/graphql`,
          },
          {
            name: "org",
            url: `${appRedirect}org/graphql`,
          },
        ],
      }),

      buildService({ url }) {
        return new RemoteGraphQLDataSource({
          url,
          willSendRequest: ({ request, context }) => {
            request.http.headers.set(
              "Authorization",
              context.customHeaders?.headers["authorization"]
            );
            request.http.headers.set(
              "cookie",
              context.customHeaders?.headers.cookie
            );
            request.http.headers.set(
              "oam_remote_user",
              context.customHeaders?.headers["oam_remote_user"]
            );
            request.http.headers.set(
              "ocp-Apim-Subscription-Key",
              context.customHeaders?.headers["ocp-apim-subscription-key"]
            );
            // console.log("Meupp ui headers => ", request.http.headers);
          },
        });
      },
    }),
  });

  app.use(
    `${SERVER_SERVLET_CONTEXT_PATH}/`,
    express.static(path.join(__dirname, "apps/shell"))
  );
  app.get(
    [
      "/promotion-management",
      "/meupp/promotion-management, /meupp/promotion-management/*",
    ],
    (req, res) => {
      app.use(
        "/meupp",
        express.static(path.join(__dirname, "..", "..", "apps/shell"))
      );
      res.sendFile(
        path.join(__dirname, "..", "..", "apps/shell/index.html"),
        function (err) {
          if (err) {
            // console.log({ err });
          }
        }
      );
    }
  );

  app.get(
    ["/events", "/events/*", "/meupp/events", "/meupp/events/*"],
    (req, res) => {
      app.use(
        "/meupp",
        express.static(path.join(__dirname, "..", "..", "apps/shell"))
      );
      res.sendFile(
        path.join(__dirname, "..", "..", "apps/shell/index.html"),
        function (err) {
          if (err) {
            // console.log({ err });
          }
        }
      );
    }
  );

  app.get(`${SERVER_SERVLET_CONTEXT_PATH}/config`, (req, res) => {
    res.header("Access-Control-Allow-Origin", "*");
    const config = {
      env: process.env["NX_REACT_APP_ENV"],
      ad_redirect_uri: process.env["NX_REACT_AD_REDIRECT_URI"],
      base_url: process.env["NX_REACT_API_BASE_URL"],
      graphql_url: process.env["NX_REACT_GRAPHQL_URL"],
      apim_url: process.env["NX_REACT_APIM_URL"],
      apim_subscription_key: process.env["NX_REACT_APIM_SUBSCRIPTION_KEY"],
      memsp_xapi_graphql: process.env["NX_REACT_MEMSP_XAPI_GRAPHQL"],
      vendor_qa2_apim_url: process.env["NX_REACT_APIM_VENDOR_URL"],
    };
    res.send(config);
  });

  // app.use("/", express.static(path.join(__dirname, "..", "..", "apps/shell")));
  app.use(
    `${SERVER_SERVLET_CONTEXT_PATH}/`,
    express.static(path.join(__dirname, "..", "..", "apps/shell"))
  );
  app.use(
    [
      "/promotion-management/src/assets",
      "/meupp/promotion-management/src/assets",
    ],
    express.static(path.join(__dirname, "apps/promotion-management/assets"))
  );
  app.use(
    ["/assets", "/meupp/assets"],
    express.static(path.join(__dirname, "apps/promotion-management/assets"))
  );
  app.use(
    ["/shell/src/assets", "/meupp/shell/src/assets"],
    express.static(path.join(__dirname, "..", "..", "apps/shell/assets"))
  );
  app.use(
    [
      "/promotion-management/",
      "/meupp/promotion-management/",
      "/meupp/promotion-management/*",
    ],
    express.static(path.join(__dirname, "apps/promotion-management"))
  );
  app.use(
    ["/event-flow/", "/meupp/event-flow/", "/meupp/event-flow/*"],
    express.static(path.join(__dirname, "apps/event-flow"))
  );

  //serving the gql endpoint from express
  await server.start();
  server.applyMiddleware({
    app,
    path: `${SERVER_SERVLET_CONTEXT_PATH}/upp/graphql`,
  });

  const gql_greeting = { message: "Welcome to meupp GQL Federation!" };
  app.get("/upp", (req, res) => {
    res.send(gql_greeting);
  });
  // app.use(cors()) // Use this after the variable declaration
  // console.log("===================ENV=================");
  //console.log(process.env)
  // console.log("===================ENV=================");
  const port = process.env.port || 3333;

  app.listen(port, () => {
    console.log(
      `Port number ${port} | Context Path : ${SERVER_SERVLET_CONTEXT_PATH}`
    );
    // console.log(
    //   `🚀 Server ready at http://localhost:3333${server.graphqlPath}`
    // );
    // console.log("ath", path.join(__dirname, "..", "..", "apps/shell"));
    // console.log(
    //   "Listening at the host and port http://localhost:" + port + "/api"
    // );
  });
  // server.on('error', console.error);
};
bootstrap().catch(error => {
  // should this be commented out too...?
  console.log(error);
});
