import React, { useCallback, useEffect, useRef } from "react";
import Card from "@albertsons/uds/molecule/Card";
import Stepper from "@albertsons/uds/molecule/Stepper";
import _ from "lodash";
import { Suspense, useState } from "react";
import {
  displayPricingLink,
  getSteppers,
  handlePromotionPricing,
} from "../../../service/promotion-details/promotion-details-service";
import {
  EVENT_PROMOTION_SECTION,
  EVENT_PROMOTION_WITH_OVERLAPS_SECTION,
  CANCEL_PROMO_CREATION_LABEL,
} from "../../../constants/promotion/layout/promotion-layout";
import PromotionSubLevelContainer from "./promotion-sub-level-container";
import { renderToString } from "react-dom/server";
import {
  useLazyGetForecastQuery,
  usePostOverlappingPromotionDataMutation,
  usePostPromotionDataMutation,
  usePutPromotionDataMutation,
} from "../../../service/apis/promotion-api";
import { formatTimestamp, getLoggedInUserType } from "@me-upp-js/utilities";
import { useDispatch } from "react-redux";
import {
  isAddPromoFn,
  postPromotionSuccessConfiguration,
  promotionDatesHandler,
  promotionDetailDataHandler,
  promotionForecastHandler,
  promotionOverlapsHandler,
  promotionPricingROGLevelHandler,
  promotionPricingSaveAllChangesHandler,
  regularPriceDetailDataHandler,
} from "../../../service/slice/promotion-details-slice";
import { SHOW_FIELDS } from "../../../constants/promotion/types";
import { RBAC } from "albertsons-react-rbac";
import "./promotion-details-card-content.scss";
import { Edit2 as EditIcon, Eye } from "lucide-react";
import { useSelectorWrap } from "@me/data-rtk";
import Button from "@albertsons/uds/molecule/Button";
import { useFormContext } from "react-hook-form";
import { RenderStates } from "@me/ui-render-states";
import { usePostPlanEventDataMutation } from "../../../service/apis/event-api";
import { EUSER_ROLES } from "../../../../event-types/components/event-action/event-action.model";
import { getAllowanceDraftCollections } from "../../../../event-types/event-hook-form-wrapper-service";
import { EEVENT_STATUS, useGetAppBasePath } from "@me/util-helpers";
import { ForecastData } from "./promotion-details-interface-props";
import efConstants from "../../../../../shared/ef-constants/ef-constants";
import { isAddingPromoConfig } from "../common/card-container-service";
import { promoCardConfiguration } from "../../../service/slice/allowance-details-slice";
import { PROMO_CREATE, PROMO_UPDATE } from "./promotion-details-model";
import { checkHiddenPrice } from "./promotion-details-helper";
import { appConstants } from "@me/utils-root-props";
import { useNavigate } from "react-router-dom";
import PricingLink from "./pricing-link";
import { PROMO_DETAILS_HIDDEN_PRICING_FEATURE } from "libs/utils/helpers/src/lib/constants/status-constants";
interface IPromotionDetailsCardContentProps {
  cardConfiguration?: any;
  route?: any;
  isNewAllowance?: any;
  cardIndex?: any;
  cardItemIndex?: any;
  control?: any;
  promotionIndex?: any;
  getValues?: any;
  // TODO removed after completion of saving promotion data
  setIsEdit?: any;
  handleCancel?: any;
  isEditMode?: any;
}

const PromotionDetailsCardContent: React.FunctionComponent<
  IPromotionDetailsCardContentProps
> = ({
  control,
  promotionIndex,
  route,
  cardIndex,
  cardItemIndex,
  cardConfiguration,
  getValues,
  handleCancel,
  // TODO removed after completion of saving promotion data
  setIsEdit = () => null,
  isEditMode,
}) => {
  const { data: eventData } = useSelectorWrap("event_details_data");
  const { isEventCardOpen } = useSelectorWrap("is_event_edit_enable").data;
  const {
    data: { isSaveAllChangesClicked },
  } = useSelectorWrap("promotion_pricing_save_all_changes_rn");

  const { data: promoPricingRogLevelData } = useSelectorWrap(
    "promotion_pricing_rog_level_rn"
  );
  const { data: promoHiddenPricingChecboxesStatus } = useSelectorWrap(
    "promotion_hidden_pricing_checkboxes_status_rn"
  );
  const { divisionIds = [], negotiationSimsVendors = [] } = { ...eventData };
  const dispatch = useDispatch();
  const [steppersShow, setSteppersShow] = useState<any>([]);
  const [step, setStep] = useState<number>(2);
  const [isPutOrPost, setIsPutOrPost] = useState(false);
  const [forecastAPICall, setForecastAPICall] = useState(false);
  const [isPutPostPromo, setIsPutPostPromo] = useState(false);
  const [isPromotionCreated, setIsPromotionCreated] = useState(false);
  const { promotionsLists } = getValues();
  const [getForecastCallLimit, setGetForecastCallLimit] = useState(5);
  const [forecastData, setForecastData] = useState<any>(null);
  const forecastApiTimer = useRef<any>(null);
  const { data: isToggleOn } = useSelectorWrap("hidden_price_rn");
  const { data: oldPromoDetailsData } = useSelectorWrap(
    "save_promo_details_rn"
  );
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  // const [getForecastData, setForecastDataDetails] = useState(
  //   getValues(
  //     `promotionsLists.[${cardIndex}].promotionsList[${cardItemIndex}].forecast`
  //   )
  // );
  const { data: forecastSliceData } = useSelectorWrap("promotion_forecast_rn");
  const { data: { isEditPromotion: { isEdit = {} } = {} } = {} } =
    useSelectorWrap("promotion_edit_enable_configutation_rn") || {};
  const [getForecastData, setForecastDataDetails] =
    useState<ForecastData | null>(
      getValues(
        `promotionsLists.[${cardIndex}].promotionsList[${cardItemIndex}].forecast`
      )
    );
  const [postOverlappingPromotionData, { isLoading: isPromoOverlapsLoading }] =
    usePostOverlappingPromotionDataMutation();
  const promotionRegField = `promotionsLists[${cardIndex}].promotionsList[${cardItemIndex}]`;
  const promoId = getValues(promotionRegField)?.id || 0;
  const promotionWorkflowStatus =
    getValues(promotionRegField)?.promotionWorkflowStatus || "";
  const createPricing = getValues(promotionRegField)?.createPricing || false;
  const eventStatus = eventData?.eventStatus || "";
  const {
    NEW_DIRECTION_FEATURES_FLAGS: {
      isDirectionalChangesEnable,
      ONE_PROMOTION: { disableVehicleStepper },
    },
  } = efConstants;
  const { hiddenPricingFeatureEnabled } = PROMO_DETAILS_HIDDEN_PRICING_FEATURE;
  const { id: eventId } = getValues();
  const [inValidPromos, setInvalidPromos] = useState<string[]>([]);
  const { setValue, formState } = useFormContext();

  const [formTouched, setFormTouched] = useState(false);
  useEffect(() => {
    setFormTouched(Object.keys(formState?.touchedFields).length > 0);
  }, [formState, control, getValues]);

  const [eventPromotionSection, setEventPromotionSection] = useState<any>(
    EVENT_PROMOTION_SECTION
  );
  const { basePath } = useGetAppBasePath(),
    navigate = useNavigate();
  useEffect(() => {
    if (eventData?.inValidPromotions?.length) {
      setInvalidPromos(eventData?.inValidPromotions);
    }
  }, [eventData?.inValidPromotions]);

  const saveAndContinueHandler = stepper => {
    setStep(stepper);
  };
  const [
    postPromotionData,
    { isLoading: isPostPromoDataloading, data: postPromoData },
  ] = usePostPromotionDataMutation();
  const {
    openCardConfig: promoOpenCardConfig = {},
    isAddNewPromo: newPromoAdd = {},
  } = useSelectorWrap("promo_card_configutation_rn").data || {};
  const [
    putPromotionData,
    { isLoading: isPutPromoDataloading, data: putPromoData },
  ] = usePutPromotionDataMutation();
  const promotionId =
    promotionsLists?.[cardIndex]?.promotionsList[cardItemIndex]?.id;
  const [getForecastDetails, { data: getForecast, isSuccess, isFetching }] =
    useLazyGetForecastQuery(
      {
        URL_PARAM: eventId,
      },
      {
        refetchOnMountOrArgChange: true,
        skip: !forecastAPICall,
      }
    );

  const [postPlanEventData, { data }] = usePostPlanEventDataMutation();

  const isMerchantOnAgreedStatus = async () => {
    const userType = getLoggedInUserType();
    const { allowanceEventInd, planEvent } = getValues();
    const planDraftAllowances = getAllowanceDraftCollections(
      planEvent?.offerAllowances,
      "allowances",
      "allowanceStatus"
    );
    if (
      (planEvent?.eventStatus === EEVENT_STATUS.AGREED ||
        planEvent?.eventStatus === EEVENT_STATUS.ACTIVE) &&
      !allowanceEventInd &&
      userType === EUSER_ROLES.MERCHANT &&
      !planDraftAllowances.length
    ) {
      await postPlanEventData({ eventId });
      return true;
    } else return null;
  };

  useEffect(() => {
    if (eventData.inValidPromotions.includes(promotionId) || isEventCardOpen) {
      setStep(isDirectionalChangesEnable || isEventCardOpen ? 2 : 1);
    }
  }, [eventData, isEventCardOpen]);

  useEffect(() => {
    if (!isFetching && getForecast) {
      setForecastDataDetails(getForecast);
      dispatch(promotionForecastHandler(getForecast));
      if (getForecast?.lastUpdated !== forecastData?.lastUpdated) {
        setIsPutOrPost(false);
        setForecastData(getForecast);
        setForecastAPICall(false);
        setGetForecastCallLimit(5);
        forecastApiTimer.current && clearTimeout(forecastApiTimer.current);
      }
    }
  }, [getForecast, isSuccess, isFetching]);

  useEffect(() => {
    if (isPromotionCreated) {
      // create promotion was success, so update the store
      dispatch(
        postPromotionSuccessConfiguration({ isPromotionSuccessConfig: true })
      );
      dispatch(
        promotionDetailDataHandler({
          isPromotionAdded: _.uniqueId("promotion"),
        })
      );
      setClose(true);
      setIsPromotionCreated(false);
      isPutPostPromo && setIsPutPostPromo(false);
    }
  }, [isPromotionCreated, isPutPostPromo]);

  const [isClose, setClose] = useState(false);

  const savePromotionHandler = async (closeEditModeOnSave = true) => {
    let promotionDetails;
    const { promotionsLists } = getValues();
    const id =
      promotionsLists[cardIndex]?.promotionsList[cardItemIndex]?.id ||
      postPromoData?.id;
    const promotionWorkflowStatus =
      promotionsLists[cardIndex]?.promotionsList[cardItemIndex]
        ?.promotionWorkflowStatus;
    const isPromoValid = ![
      EEVENT_STATUS.CANCELED,
      EEVENT_STATUS.REJECTED,
    ].includes(promotionWorkflowStatus);
    if (id && !putPromoData && !postPromoData && isPromoValid) {
      promotionDetails = await putPromotionData(
        updatePromoPayload(PROMO_UPDATE)
      );
    } else if (id && (putPromoData || postPromoData) && isPromoValid) {
      promotionDetails = await putPromotionData(
        updatePromoPayload(PROMO_UPDATE)
      );
    }
    // else if (id && putPromoData) {
    //   promotionDetails = await putPromotionData(updatePromoPayload());
    // }
    else if (!postPromoData && !id) {
      promotionDetails = await postPromotionData(
        createPromoPayload(PROMO_CREATE)
      );
    } else {
      promotionDetails = postPromoData;
    }
    if (promotionDetails?.data?.id || promotionDetails?.id) {
      setIsPutOrPost(true);
      await isMerchantOnAgreedStatus();
      setValue(
        `promotionsLists[${cardIndex}].promotionsList[${cardItemIndex}].promotionId`,
        promotionDetails?.data?.promotionId || promotionDetails?.id
      );
      if (closeEditModeOnSave) {
        setValue("isPromoChanged", false);
        dispatch(promotionDatesHandler({ isDatesUpdated: false }));
        dispatch(isAddingPromoConfig(false));
        const promoAddObj = { ...newPromoAdd };
        Object.keys(promoAddObj).forEach(key => {
          promoAddObj[key] = false;
        });
        dispatch(
          promoCardConfiguration({
            isAddNewPromo: promoAddObj,
          })
        );
        dispatch(isAddPromoFn({ isAddPromo: false }));
        dispatch(
          promotionDetailDataHandler({
            isPromotionAdded: _.uniqueId("promotion"),
          })
        );
        dispatch(promotionPricingROGLevelHandler({}));
        dispatch(
          promotionPricingSaveAllChangesHandler({
            isSaveAllChangesClicked: false,
          })
        );
        setIsPromotionCreated(true);
        setValue("isPromoChanged", false);
        setIsEdit(false);
        // setClose(true);
      }
      return promotionDetails;
    }
  };

  useEffect(() => {
    if (isClose) {
      handleCancel();
    }
  }, [isClose]);

  useEffect(() => {
    if (isSaveAllChangesClicked) {
      savePromotionHandler();
    }
  }, [isSaveAllChangesClicked]);

  const overlapsHandler = (promotionSections, isPromoOverlaps, overlapData) => {
    const shouldShowPromoDetails = hiddenPricingFeatureEnabled
      ? getLoggedInUserType() === EUSER_ROLES?.MERCHANT || !isEditMode
        ? true
        : checkHiddenPrice(
            eventDetailsData?.promotionsList?.[`${cardItemIndex}`]
          )
      : true;
    let updatedSteppers = shouldShowPromoDetails
      ? promotionSections?.steppers
      : promotionSections?.steppers?.slice(0, -1);
    let updatedPromotionSections = {
      ...promotionSections,
      steppers: updatedSteppers,
    };
    const updatedStepperData = eventDetailsData?.promotionsList?.length
      ? updatedPromotionSections
      : promotionSections;
    setSteppersShow(updatedStepperData?.steppers);
    setEventPromotionSection(updatedStepperData);
    dispatch(
      promotionOverlapsHandler({
        isPromotionOverlapsDataAvailable: isPromoOverlaps,
        promotionOverlapsData: overlapData,
      })
    );
  };

  const hasMounted = useRef(false);
  useEffect(() => {
    if (Object.values(promoOpenCardConfig).includes(true) && eventId) {
      const vendorNumList = JSON.parse(
        localStorage.getItem("vendorNumList") || "[]"
      );
      const userId = window["OAM_REMOTE_USER"] || "";
      const { promotionsLists, id: eventId, startDate, endDate } = getValues();
      const promotion =
        promotionsLists[cardIndex]?.promotionsList[cardItemIndex];
      const promoStartDate = promotion?.promoStartDate
        ? promotion?.promoStartDate
        : promotion?.vehicle?.startDate;
      const promoEndDate = promotion?.promoEndDate
        ? promotion?.promoEndDate
        : promotion?.vehicle?.endDate;
      const overlapsPayload = {
        eventId,
        promoStartDate: startDate || promoStartDate,
        promoEndDate: endDate || promoEndDate,
        userId,
        vendorIds: vendorNumList,
      };
      const fetchPromoOverlapsEvent = async () => {
        const { data: result } = await postOverlappingPromotionData({
          ...overlapsPayload,
        });
        if (!result?.length) {
          overlapsHandler(EVENT_PROMOTION_SECTION, false, []);
        } else {
          const updatedOverlapData = result?.filter(
            (promo: any) => promo.id !== promoId
          );
          updatedOverlapData?.length
            ? overlapsHandler(
                EVENT_PROMOTION_WITH_OVERLAPS_SECTION,
                true,
                updatedOverlapData
              )
            : overlapsHandler(EVENT_PROMOTION_SECTION, false, []);
        }
      };
      if (!hasMounted.current) {
        hasMounted.current = true;
        fetchPromoOverlapsEvent();
      }
    }
  }, [JSON.stringify(promoOpenCardConfig), promoId]);

  const saveAndGetForecastHandler = async () => {
    if (!isDirectionalChangesEnable) {
      setForecastDataDetails(null);
      const id =
        promotionsLists[cardIndex]?.promotionsList[cardItemIndex]?.id ||
        promotionsLists[cardIndex]?.promotionsList[cardItemIndex]?.promotionId;
      let promoId = "";
      const promoDetails = await savePromotionHandler(false);
      promoId = promoDetails?.postPromotionId || promoDetails?.data?.id;
      if (promoId || id) {
        setGetForecastCallLimit(5);
        setForecastAPICall(true);
      }
    } else {
      setGetForecastCallLimit(5);
      setForecastAPICall(true);
    }
  };
  useEffect(() => {
    if (
      forecastAPICall &&
      (putPromoData || postPromoData || isDirectionalChangesEnable)
    ) {
      pollingForecast();
    }
  }, [forecastAPICall, putPromoData, postPromoData, getForecastCallLimit]);

  const pollingForecast = useCallback(() => {
    if (!getForecastCallLimit) {
      setForecastAPICall(false);
      setGetForecastCallLimit(5);
    }
    if (
      (isPutOrPost || isDirectionalChangesEnable) &&
      getForecastCallLimit > 0
    ) {
      forecastApiTimer.current = setTimeout(() => {
        try {
          setGetForecastCallLimit(value => value - 1);
          const { periscopeDetails } = eventData;
          if (periscopeDetails?.length) {
            const [{ periscopeId }] = periscopeDetails;
            getForecastDetails({
              URL_PARAM: periscopeId,
            });
          }
        } catch (error) {
          console.error("error", error);
        }
      }, 5000);
    }
  }, [getForecast, getForecastCallLimit, isPutOrPost]);

  const buildPromoDetailsObj = (promotion, promotionType) => {
    const promotionTypeFields = SHOW_FIELDS[promotionType].fields;
    const promoDetails = {
      amount: promotion?.promoDetails?.amount,
      unitMeasure: promotion?.promoDetails?.unitMeasure,
      itemLimit: promotion?.promoDetails?.itemLimit
        ? parseInt(promotion?.promoDetails?.itemLimit, 10)
        : 0,
      minQuantity: parseInt(promotion?.promoDetails?.minQuantity, 10),
      factor: promotion?.promoDetails?.factor,
      comments: promotion?.promoDetails?.comments,
    };
    const keys = Object.keys(promotionTypeFields).filter(prop => {
      return promotionTypeFields[prop].show;
    });
    const uomIndex = keys.findIndex(key => key === "uom");
    if (uomIndex) keys[uomIndex] = "unitMeasure";
    const fieldsToReturn = _.pick(promoDetails, [...keys]);
    if (["CENT_OFF", "PERCENT_OFF"].includes(promotionType))
      return {
        ...fieldsToReturn,
        factor: SHOW_FIELDS[promotionType]?.fields?.factor?.defaultValue,
      };
    return fieldsToReturn;
  };

  const updatePromoPayload = param => {
    const {
      vehicleId,
      promoCreationVehicle,
      promoStartDate,
      promoEndDate,
      customerGroup,
      divisionIds,
      hiddenPrice,
      promoDetails,
      eventId,
      doNotPrice,
      adDetails,
      splitBIB,
      createPricing,
      hasPromoDetailsFieldsChanged,
    } = createPromoPayload(param);
    const id =
      promotionsLists[cardIndex]?.promotionsList[cardItemIndex].id ||
      postPromoData?.id;
    const promotionId =
      promotionsLists[cardIndex]?.promotionsList[cardItemIndex].promotionId;
    const periscopePromoId = getForecastData?.periscopePromotionId || "-1";
    const promotionChangeStatus = "";

    return {
      vehicleId,
      vehicle: promoCreationVehicle,
      promoStartDate,
      promoEndDate,
      customerGroup,
      divisionIds,
      promoDetails,
      eventId,
      id,
      promotionId,
      periscopePromoId,
      promotionChangeStatus,
      hiddenPrice,
      doNotPrice,
      adDetails,
      splitBIB,
      createPricing,
      hasPromoDetailsFieldsChanged,
    };
  };
  const createPromoPayload = isCreatePromo => {
    const {
      promotionsLists,
      id: eventId,
      divisionIds,
      startDate,
      endDate,
    } = getValues();
    const promotion = promotionsLists[cardIndex]?.promotionsList[cardItemIndex];
    const promoDetailsFromResponse =
      eventDetailsData?.promotionsList?.[`${cardItemIndex}`]?.promoDetails ||
      {};
    const { promoDetails: promoDetailsOfForm } = promotion;
    const promotionType = promotion?.promoDetails?.promotionType;
    const vehicleId = promotion.vehicle?.vehicleId || promotion.vehicle?.id;
    const promoStartDate = promotion.promoStartDate
      ? promotion.promoStartDate
      : promotion.vehicle?.startDate;
    const promoEndDate = promotion.promoEndDate
      ? promotion.promoEndDate
      : promotion.vehicle?.endDate;
    const promoCreationVehicle = promotion?.vehicle;
    const startDateConverted = formatTimestamp({
      timestamp:
        promotion?.vehicle?.vehicleType?.vehicleTypDesc === "Custom Date"
          ? promoStartDate
          : promotion?.vehicle?.startDate,
      isApi: true,
    });
    // TODO: REMOVE once date format change at API end
    //startDateConverted = new Date(startDateConverted).toISOString();
    const endDateConverted = formatTimestamp({
      timestamp:
        promotion?.vehicle?.vehicleType?.vehicleTypDesc === "Custom Date"
          ? promoEndDate
          : promotion?.vehicle?.endDate,
      isApi: true,
    });
    // TODO: REMOVE once date format change at API end
    //endDateConverted = new Date(endDateConverted).toISOString();
    promoCreationVehicle.startDate = startDateConverted;
    promoCreationVehicle.endDate = endDateConverted;
    const { createPricing } = promotion;
    const updatedPromoDetails = {
      ...buildPromoDetailsObj(promotion, promotionType),
      promotionType: promotionType,
      regularPrice: promotion?.promoDetails?.regularPrice,
      listCost: promotion?.promoDetails?.listCost,
      listAgp: promotion?.promoDetails?.listAgp,
    };
    let promoDetailsData = {
      ...updatedPromoDetails,
    };
    let hiddenPrice = {} || null;
    const { doNotPrice, adDetails, splitBIB, doNotShowPromoPrice } =
      promoHiddenPricingChecboxesStatus;
    if (
      hiddenPricingFeatureEnabled &&
      isCreatePromo !== PROMO_CREATE &&
      getLoggedInUserType() === EUSER_ROLES.MERCHANT &&
      doNotShowPromoPrice
    ) {
      promoDetailsData = oldPromoDetailsData;
      hiddenPrice = isSaveAllChangesClicked
        ? promoPricingRogLevelData?.suggested
        : updatedPromoDetails;
    }
    ["BUY_ONE_GET_ONE", "BUY_X_GET_ONE"].includes(
      promoDetailsOfForm?.promotionType
    ) && delete promoDetailsOfForm?.unitMeasure;

    const hasPromoDetailsFieldsChanged = !_.isEqual(promoDetailsFromResponse, {
      ...promoDetailsFromResponse,
      ...promoDetailsOfForm,
      ...updatedPromoDetails,
    });

    return {
      vehicleId,
      promoCreationVehicle,
      promoStartDate: startDateConverted,
      promoEndDate: endDateConverted,
      customerGroup: promotion?.customerGroup || "ALL",
      divisionIds,
      ...(hiddenPricingFeatureEnabled &&
        getLoggedInUserType() === EUSER_ROLES.MERCHANT &&
        isCreatePromo === PROMO_CREATE && {
          merchantExclusive: doNotShowPromoPrice,
        }),
      ...(hiddenPricingFeatureEnabled &&
        isCreatePromo !== PROMO_CREATE &&
        getLoggedInUserType() === EUSER_ROLES.MERCHANT &&
        doNotShowPromoPrice && {
          hiddenPrice,
        }),
      ...(isCreatePromo !== PROMO_CREATE && {
        createPricing,
      }),
      ...(isCreatePromo !== PROMO_CREATE &&
        getLoggedInUserType() === EUSER_ROLES.MERCHANT && {
          hasPromoDetailsFieldsChanged,
        }),
      doNotPrice,
      adDetails,
      splitBIB,
      promoDetails: promoDetailsData,
      eventId,
    };
  };

  React.useEffect(() => {
    const steppersShow: any = getSteppers();
    setSteppersShow(steppersShow);
  }, []);

  const isInvalidDate = inValidPromos?.includes(promoId);

  const editTag = stepperElement =>
    // if stepper is Customers, we should pass emprty Fragment. Else Edit icon will render by default
    // eslint-disable-next-line react/jsx-no-useless-fragment
    stepperElement.label == "Customers" ? (
      <></>
    ) : (
      <div
        className="flex items-center justify-end"
        id="abs-promotion-details-card-content-container"
      >
        <span className="text-light-text mr-5 font-semibold text-[#1B6EBB]">
          {isDirectionalChangesEnable && stepperElement.edit.isView ? (
            <>
              {" "}
              <Eye
                width={16}
                height={16}
                color="#1B6EBB"
                className="inline-block mb-[3px] mr-[8px]"
              />{" "}
              View
            </>
          ) : (
            <>
              <EditIcon
                width={16}
                height={16}
                color="#1B6EBB"
                className="inline-block mb-[3px] mr-[8px]"
              />
              Edit
            </>
          )}
        </span>
      </div>
    );
  const setStepOnCancel = () => {
    setStep(2);
  };

  const handleCancelPromoCreation = () => {
    setValue(
      `promotionsLists[${cardIndex}].promotionsList[${cardItemIndex}].promoDetails`,
      {
        ...promotionsLists[cardIndex].promotionsList[cardItemIndex]
          ?.promoDetails,
        promotionType: "",
      }
    );
    dispatch(isAddingPromoConfig(false));
    const isSinglePromo = promotionsLists?.[0]?.promotionsList?.length === 1;
    dispatch(
      promoCardConfiguration({
        isAddNewPromo: { 0: false },
        openCardConfig: isSinglePromo ? { 0: false } : promoOpenCardConfig,
      })
    );
  };

  const renderHtml = eventId ? (
    <Card.Content>
      <RBAC
        divisionIds={divisionIds}
        permissionsOnly={["PROMOTION_PROMO_MGMT_EDIT_ADD_PROMOTION"]}
        simsVendors={negotiationSimsVendors}
      >
        {/* <LoadingSpinner
          isLoading={
            !forecastAPICall
              ? isPostPromoDataloading || isPutPromoDataloading
              : false
          }
          classname="!w-full !h-full"
        /> */}
        <Stepper
          step={step}
          onChange={setStep}
          className="promotion-details-skip-customer"
          skippable
        >
          {steppersShow.map((stepper: any, index) => {
            const stepperElement = eventPromotionSection[stepper];
            const container = stepper
              .replace(/[&]/g, "")
              .replace(/ /g, "-")
              .replace(/--/g, "-")
              .toLowerCase();
            const Component = React.lazy(
              () => import(`./stepper/${container}/${container}`)
            );
            const sublabel = renderToString(
              <PromotionSubLevelContainer
                promotionIndex={promotionIndex}
                cardIndex={cardIndex}
                cardItemIndex={cardItemIndex}
                stepperIndex={index}
                step={step}
                getValues={getValues}
                container={container}
                isEdit={isEditMode}
              />
            );
            return (
              <Stepper.Step
                key={`${index}`}
                label={stepperElement.label}
                sublabel={step !== index ? sublabel : ""}
                tag={editTag(stepperElement)}
              >
                <div
                  id="abs-promotion-details-card-content-stepper-element-label"
                  className={`flex justify-start items-start flex-grow-0 flex-shrink-0 relative gap-2.5 p-4 pb-[0px] pr-[0px] bottom-[39px] ${stepperElement.label}`}
                >
                  <div
                    className="flex-grow-0 flex-shrink-0 w-full h-4 relative"
                    id="abs-promotion-details-card-content-stepper-element-label1"
                  >
                    <p
                      className="absolute mt-3 right-0 top-0 text-sm require-fields flex"
                      id="abs-promotion-details-card-content-stepper-element-text"
                    >
                      {displayPricingLink(
                        promotionWorkflowStatus,
                        eventStatus
                      ) &&
                        stepperElement.label === "Promo Details" && (
                          <PricingLink
                            promoId={promoId}
                            eventStatus={eventStatus}
                            navigate={navigate}
                            basePath={basePath}
                            handlePromotionPricing={handlePromotionPricing}
                            createPricing={createPricing}
                          />
                        )}
                      {getLoggedInUserType() === EUSER_ROLES.VENDOR ? (
                        <>
                          <span className="text-sm italic text-right text-[#bf2912]">
                            *
                          </span>
                          <span className="text-sm italic text-right text-black">
                            required fields
                          </span>
                        </>
                      ) : null}
                    </p>
                  </div>
                </div>
                <Suspense fallback={null}>
                  <Component
                    promotionIndex={promotionIndex}
                    control={control}
                    stepperElement={stepperElement}
                    cardIndex={cardIndex}
                    cardItemIndex={cardItemIndex}
                    isGetForecastLoading={forecastAPICall}
                    saveAndContinueHandler={saveAndContinueHandler}
                    savePromotionHandler={savePromotionHandler}
                    saveAndGetForecastHandler={saveAndGetForecastHandler}
                    inValidPromos={inValidPromos}
                    setInvalidPromos={setInvalidPromos}
                    isInvalidDate={isInvalidDate}
                    // TODO removed after completion of saving promotion data
                    setIsEdit={setIsEdit}
                    postPromoData={postPromoData}
                    putPromoData={putPromoData}
                    getForecastData={
                      isDirectionalChangesEnable
                        ? forecastSliceData
                        : getForecastData
                    }
                    setStep={setStepOnCancel}
                    isForecastFetching={isSuccess}
                    getForecastCallLimit={getForecastCallLimit}
                    isApiLoading={
                      isPostPromoDataloading ||
                      isPutPromoDataloading ||
                      isPromoOverlapsLoading
                    }
                  ></Component>
                </Suspense>
              </Stepper.Step>
            );
          })}
        </Stepper>
        {eventData.inValidPromotions.includes(promoId) &&
        !isDirectionalChangesEnable &&
        !isPostPromoDataloading ? (
          <Button
            width={188}
            className={"mt-5"}
            variant="primary"
            disabled={!!(isInvalidDate || !formTouched)}
            onClick={() => savePromotionHandler()}
          >
            Update Promotion
          </Button>
        ) : null}
        {eventId && !isEditMode && (
          <>
            <div
              className="flex-grow-0 flex-shrink-0 w-full h-px  bg-[#fdfdff] border-t border-r border-b-0 border-l border-[#c8daeb]"
              id="abs-promotion-details-card-content-cancel-prome-creation-cont"
            ></div>
            <div
              className="flex justify-start items-center mt-5"
              id="abs-promotion-details-card-content-cancel-prome-creation-label-cont"
            >
              <Button
                onClick={event => {
                  event?.preventDefault();
                  event?.stopPropagation();
                  handleCancelPromoCreation();
                }}
                variant="secondary"
                width={200}
              >
                <span className="font-medium">
                  {CANCEL_PROMO_CREATION_LABEL}
                </span>
              </Button>
            </div>
          </>
        )}
        {/* <div className="flex-grow-0 flex-shrink-0 w-full h-px  bg-[#fdfdff] border-t border-r border-b-0 border-l border-[#c8daeb]" id="abs-promotion-details-card-content-create-promotion1"></div>
      <Button variant="tertiary" className="whitespace-nowrap lg:w-[302px]">
        Create Promotion
      </Button>
      <Button variant="tertiary" className="whitespace-nowrap lg:w-[302px]">
        Save & Add Another Promotion
      </Button> */}
      </RBAC>
    </Card.Content>
  ) : null;

  const renderDetails = {
    isApiLoading:
      isPostPromoDataloading || isPutPromoDataloading || isPromoOverlapsLoading,

    isPageLevelSpinner: true,
    isRenderMainHtml: true,
    renderHtml,
  };

  return <RenderStates details={renderDetails} />;
};

export default PromotionDetailsCardContent;
