import moment from "moment";
import { DeepClone } from "@me/util-helpers";
import { EEVENT_STATUS, EUSER_ROLES } from "../event-action/event-action.model";
import { getLoggedInUserType } from "@me-upp-js/utilities";
import { EVENT_COMMENTS } from "../../constants/event-comments-constants";

const userRole = getLoggedInUserType();

export const getLatestCommentByReduce = comments => {
  if (!comments) return {};
  const latestComment = comments?.eventComments?.reduce(
    (latestComment, comment) => {
      return moment.utc(latestComment?.lastUpdTs) > moment.utc(comment?.lastUpdTs)
        ? latestComment
        : comment;
    }
  );
  const clonedLatestComment: any = new DeepClone(latestComment).getDeepClone();
  clonedLatestComment.type = comments?.commentCategory;
  const convertedDate = new Date(clonedLatestComment?.lastUpdTs);
  clonedLatestComment.lastUpdTs = moment.utc(convertedDate || moment().utc()).format(
    "MM/DD/YY  hh:mm A"
  );
  return clonedLatestComment;
};

export const isEventHaveCommentTask = eventDetailsData => {
  if (eventDetailsData) {
    const { planEventTasks = [], planEvent, eventStatus } = eventDetailsData,
      tasksList =
        eventStatus === EEVENT_STATUS.DRAFT
          ? planEventTasks
          : planEvent?.planEventTasks;
    return tasksList?.some(task =>
      EVENT_COMMENTS.TASK_COMMENTS_OPTIONS.includes(task?.subType)
    );
  }
};

export const getLatestCommentsAndCount = commentsData => {
  let latestComments: any = [];
  let commentsCount = 0;

  if (userRole === EUSER_ROLES.MERCHANT) {
    latestComments = commentsData?.map(comments => {
      commentsCount += comments?.eventComments?.length;
      return getLatestCommentByReduce(comments);
    });
  } else {
    const externalComments = commentsData?.find(
      comments => comments.commentCategory === "External"
    );
    const latestCommentObj = getLatestCommentByReduce(externalComments);
    if (Object.keys(latestCommentObj).length <= 0)
      return { latestComments, commentsCount };
    latestCommentObj.type = "External";

    latestComments.push(latestCommentObj);
    commentsCount = externalComments?.eventComments?.length;
  }

  return { latestComments, commentsCount };
};
