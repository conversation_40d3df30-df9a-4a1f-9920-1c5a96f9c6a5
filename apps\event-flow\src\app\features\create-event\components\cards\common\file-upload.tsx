import { useState, useEffect } from "react";
import FileUpload, {
  FileUploadStatus,
} from "@albertsons/uds/molecule/FileUpload";
import Input from "@albertsons/uds/molecule/Input";
import { Co<PERSON>, Trash } from "lucide-react";
import "./file-upload.scss";
import { deleteFile } from "../../../service/allowance/offer-file-fetch-service";
import Spinner from "@albertsons/uds/molecule/Spinner";
import {
  OFFER_ATTACHMENT_SIZE_ERROR_LABEL,
  OFFER_ATTACHMENT_SIZE_LIMIT_IN_BYTES,
  OFFER_ATTACHMENT_TYPES,
  OFFER_UPLOAD_API_BLOB_LABEL,
  OFFER_UPLOAD_API_LABEL_ONE,
} from "../../../constants/constants";

interface FileItem {
  file: File | null;
  status: "inProgress" | "uploaded" | "failed" | "success" | "replace";
  fileSizeError: boolean;
  fileName: string;
}

const FileUploadComponent = ({
  handleConfirm,
  setFilesUploadedData,
  fileNames,
  offerNumber,
  setDisableConfirmButton,
  handleFileDelete,
}) => {
  const [fileUploadSections, setFileUploadSections] = useState<FileItem[]>([]);
  const [isDeletePending, setIsDeletePending] = useState(false);

  useEffect(() => {
    if (fileNames?.length) {
      // Initialize with fileNames if they exist
      const initialFiles = fileNames.map(fileName => ({
        file: null,
        status: "uploaded",
        fileSizeError: false,
        fileName,
      }));
      setFileUploadSections(initialFiles);
    } else {
      // Set initial placeholder if fileNames is empty
      setFileUploadSections([
        { file: null, status: "uploaded", fileSizeError: false, fileName: "" },
      ]);
    }
  }, [fileNames]);

  const handleFileUpload = (files: File[] | FileList, index: number) => {
    setDisableConfirmButton(false);

    const updatedFiles = [...fileUploadSections];
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      let fileSizeError = false;

      if (file.size > OFFER_ATTACHMENT_SIZE_LIMIT_IN_BYTES) {
        fileSizeError = true;
        setDisableConfirmButton(true);
      }

      updatedFiles[index] = {
        file,
        status: fileSizeError ? "failed" : "inProgress",
        fileSizeError,
        fileName: file.name,
      };
    }

    setFileUploadSections(updatedFiles);
    setFilesUploadedData(updatedFiles);
  };

  const handleDelete = async (
    index: number,
    fileName: string,
    offerNumber: number
  ) => {
    try {
      setIsDeletePending(true);
      if (fileNames.includes(fileName)) {
        const url = `${OFFER_UPLOAD_API_LABEL_ONE}${offerNumber}${OFFER_UPLOAD_API_BLOB_LABEL}${fileName}`;
        await deleteFile(url);
      }
      const updatedFiles = [...fileUploadSections];
      updatedFiles.splice(index, 1);
      setFileUploadSections(updatedFiles);
      setFilesUploadedData(updatedFiles);
      setIsDeletePending(false);
      addAnotherFile();
      if (fileNames.includes(fileName)) {
        handleConfirm();
        handleFileDelete(fileName);
      }
    } catch (error) {
      console.error("Error deleting file:", error);
      setIsDeletePending(false);
    }
  };

  const addAnotherFile = () => {
    setFileUploadSections(prevFiles => [
      ...prevFiles,
      { file: null, status: "uploaded", fileSizeError: false, fileName: "" },
    ]);
  };

  return (
    <div className="file-upload-container">
      <div className="file-upload-section-container">
        {fileUploadSections.map((fileItem, index) => (
          <div key={index} className="file-upload-section my-4">
            <div className="flex mx-4">
              <div className="w-50 mr-4">
                <Input
                  className="w-20"
                  value={fileItem.fileName || ""}
                  name={`inputName-${index}`}
                  placeholder="File name"
                  wrapError
                  disabled={true}
                />
              </div>

              {!fileItem.fileName ? (
                <FileUpload
                  width={600}
                  onFileUpload={files => handleFileUpload(files, index)}
                  multiple={false}
                  allowedExtensions={OFFER_ATTACHMENT_TYPES}
                  className="offer-file-upload"
                />
              ) : (
                <div className="offer-file-upload-status-container">
                  <div
                    className={`offer-file-upload-status flex items-center ${
                      fileItem.fileSizeError ? "file-upload-error" : ""
                    }`}
                  >
                    <FileUploadStatus
                      id={`${index}`}
                      fileName={fileItem.fileName}
                      imageUrl={
                        fileItem.file
                          ? URL.createObjectURL(fileItem.file)
                          : undefined
                      }
                      fileSize={
                        fileItem.file
                          ? Math.round((fileItem.file.size / 1000000) * 10) / 10
                          : 0
                      }
                      uploadStatus={fileItem.status}
                      fileType={fileItem.file?.type || ""}
                      width={600}
                    />

                    <div
                      className="file-trash-icon mr-6 cursor-pointer"
                      onClick={() =>
                        handleDelete(index, fileItem.fileName, offerNumber)
                      }
                    >
                      <Trash
                        width={16}
                        height={16}
                        className="inline-block mb-[3px] mr-[8px]"
                      />
                    </div>
                  </div>
                  {fileItem.fileSizeError ? (
                    <span className="text-[#BF2912] text-[14px]">
                      {OFFER_ATTACHMENT_SIZE_ERROR_LABEL}
                    </span>
                  ) : null}
                </div>
              )}
            </div>
            {/* <hr className="my-4" /> */}
          </div>
        ))}
        {isDeletePending ? (
          <span className="ml-2">
            <Spinner size="xs" variant="solid" />
          </span>
        ) : null}
      </div>
      {/* Enable below code snippet to add another file functionality */}
      {/* <div className="flex justify-center">
        <span
          className="mr-2 font-semibold text-[#1B6EBB] cursor-pointer"
          onClick={addAnotherFile}
        >
          <Copy
            width={16}
            height={16}
            color="#1B6EBB"
            className="inline-block mb-[3px] mr-[8px]"
          />
          <span>Add Another</span>
        </span>
      </div> */}
    </div>
  );
};

export default FileUploadComponent;
