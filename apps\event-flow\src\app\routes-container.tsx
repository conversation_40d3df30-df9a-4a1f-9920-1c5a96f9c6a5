import { useSelectorWrap } from "@me/data-rtk";
import { SpinnerWithMask } from "@me/ui-spinner";
import { Outlet, Routes, Route } from "react-router-dom";
// import AllowancesContainer from "./features/all-allowances/all-allowances-container/all-allowances-container";
import EventCreationContainer from "./features/event-types/event-creation-container";
// import EventTypeContainer from "./features/event-types/event-types-container";
import efConstants from "./shared/ef-constants/ef-constants";
import { lazy } from "react";

const EventTypeContainer = lazy(
  () => import("./features/event-types/event-types-container")
);
// const EventCreationContainer = lazy(
//   () => import("./features/event-types/event-creation-container")
// );
const AllowancesContainer = lazy(
  () =>
    import(
      "./features/all-allowances/all-allowances-container/all-allowances-container"
    )
);
const PromotionPricingContainer = lazy(
  () =>
    import(
      "./features/create-event/components/cards/promotion-details/promotion-pricing-screen/promotion-pricing-container"
    )
);
const {
  ALLOWANCE_DASHBOARD_BASE_URL,
  PROTION_ROG_LEVEL_PRICING_FEATURE_FLAGS: { PROMOTION_PRICING_BASE_URL },
} = efConstants;
export const RoutesContainer = () => {
  const {
    data: { isDisplayPageLevelSpinner } = { isDisplayPageLevelSpinner: false },
  } = useSelectorWrap("displayPgLevelSpinnner_rn") || {};
  const pgSpinnerDetails = {
    isDisplay: true,
    classname: "",
    spinnerDetails: {},
  };
  return (
    <>
      {isDisplayPageLevelSpinner && (
        <SpinnerWithMask details={pgSpinnerDetails} />
      )}
      <Outlet />
      {/* async lazy() {
    let YourComponent = await import("./YourComponent");
    return { Component: YourComponent.default };
  }, */}
      {
        <Routes>
          <Route path="/" element={<EventTypeContainer />} />
          <Route path="/create" element={<EventCreationContainer />} />
          <Route path="/edit/:id" element={<EventCreationContainer />} />
          <Route
            path={`/${ALLOWANCE_DASHBOARD_BASE_URL}`}
            element={<AllowancesContainer />}
          />
          <Route
            path={`/${PROMOTION_PRICING_BASE_URL}`}
            element={<PromotionPricingContainer />}
          />
        </Routes>
      }
    </>
  );
};
