import { render, screen } from "@testing-library/react";
import OfferAmountSection from "./index";
import { useBaseAmountConfig } from "../../hooks/amounts/useBaseAmountConfig";
import "@testing-library/jest-dom";

jest.mock("../../hooks/amounts/useBaseAmountConfig");

jest.mock("../../../common/allowance-form-wrapper", () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="allowance-form-wrapper">{children}</div>
  ),
}));

jest.mock("../../../../../constants/LoadingSpinner/LoadingSpinner", () => ({
  __esModule: true,
  default: ({ isLoading }: { isLoading: boolean }) =>
    isLoading ? <div data-testid="loading-spinner">Loading...</div> : null,
}));

jest.mock("./base-allowance-amount", () => () => (
  <div data-testid="base-allowance-amount">BaseAmount Component</div>
));

jest.mock("./base-hf-if-whse-amount", () => () => (
  <div data-testid="base-hf-if-whse-amount">BaseHfIf Component</div>
));
describe("OfferAmountSection Component", () => {
  const defaultProps = {
    sectionKey: "testSection",
    cardIndex: 0,
    cardItemIndex: 0,
    isEditEnable: false,
  };

  const mockUseBaseAmountConfig = useBaseAmountConfig as jest.Mock;

  beforeEach(() => {
    mockUseBaseAmountConfig.mockReturnValue({
      isFetching: false,
      sectionConfiguration: { label: "Test Section" },
      allowanceTypeKey: "someType",
      isHfOrIf: false,
      isHfIfWhseCase: false,
      isZeroCost: false,
      allowancesResp: {},
      amountsInitialValueOnLoad: {},
      isSectionCompleted: false,
      amtSubLabelDisplayVal: "Test Label",
      handleSave: jest.fn(),
      editViewAllItems: jest.fn(),
      isAmtSavedInTemp: false,
    });
  });

  it("renders OfferAmountSection correctly", () => {
    render(<OfferAmountSection {...defaultProps} />);
    expect(screen.getByText("Test Section")).toBeInTheDocument();
    expect(screen.getByTestId("allowance-form-wrapper")).toBeInTheDocument();
  });

  it("renders LoadingSpinner when isFetching is true", () => {
    mockUseBaseAmountConfig.mockReturnValueOnce({
      ...mockUseBaseAmountConfig.mock.results[0].value,
      isFetching: true,
    });

    render(<OfferAmountSection {...defaultProps} />);
    expect(screen.getByTestId("loading-spinner")).toBeInTheDocument();
  });

  it("renders null when formControls is undefined", () => {
    render(<OfferAmountSection {...defaultProps} />);
    expect(screen.queryByTestId("base-allowance-amount")).toBeNull();
  });
    
});

describe("OfferAmountSection Component", () => {
  const defaultProps = {
    sectionKey: "testSection",
    cardIndex: 0,
    cardItemIndex: 0,
    isEditEnable: false,
  };

  const mockUseBaseAmountConfig = useBaseAmountConfig as jest.Mock;

  beforeEach(() => {
    mockUseBaseAmountConfig.mockReturnValue({
      isFetching: false,
      sectionConfiguration: { label: "Test Section" },
      allowanceTypeKey: "someType",
      isHfOrIf: true,
      isHfIfWhseCase: true,
      isZeroCost: false,
      allowancesResp: {},
      amountsInitialValueOnLoad: {},
      isSectionCompleted: false,
      amtSubLabelDisplayVal: "Test Label",
      handleSave: jest.fn(),
      editViewAllItems: jest.fn(),
      isAmtSavedInTemp: false,
    });
  });

    it("renders null when formControls is undefined", () => {
    render(<OfferAmountSection {...defaultProps} />);
    expect(screen.queryByTestId("base-hf-if-whse-amount")).toBeNull();
    });
});
