import { gerneratePeriscopePPGError } from "../../utility/utility";
import ErrorMessage from "../error-message";
const PeriscopePPGErrorField = ({ periscopeData }) => {
  const { ppgError, ppgInfo } = gerneratePeriscopePPGError(periscopeData);
  return (
    <>
      <ErrorMessage warnMessage={ppgError} fieldGroup={[]} showError={true} />
      <p className="mb-2">{ppgInfo}</p>
    </>
  );
};

export default PeriscopePPGErrorField;
