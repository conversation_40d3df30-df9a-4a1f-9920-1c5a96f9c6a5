import Card from "@albertsons/uds/molecule/Card";
import React, { memo } from "react";
import ChildGridCardHeader from "./child-grid-card-header";
import ChildDataGrid from "../child-data-grid/child-data-grid";

function ChildGridCard({ cardIndex, isFetching }) {
  return (
    <Card>
      <Card.Header>
        <ChildGridCardHeader cardIndex={cardIndex} />
      </Card.Header>
      <Card.Content>
        <ChildDataGrid cardIndex={cardIndex} isFetching={isFetching} />
      </Card.Content>
    </Card>
  );
}

export default memo(ChildGridCard);
