import { injectEndPointsWrapper } from "@me/data-rtk";
import { API_EVENT_URL } from "../api-url";

const apiObj = {
  endPointsData: [
    // allowance API
    {
      ep_url: API_EVENT_URL["ALLOWANCE_API"],
      ep_type: "GET",
      ep_name: "getAllowanceData",
    },
    {
      ep_url: API_EVENT_URL["ALLOWANCE_API"],
      ep_type: "POST",
      ep_name: "postAllowanceData",
    },
    {
      ep_url: API_EVENT_URL["ALLOWANCE_API"],
      ep_type: "PUT",
      ep_name: "putAllowanceData",
    },
    {
      ep_url: API_EVENT_URL["OFFER_AGREEMENT_API"],
      ep_type: "POST",
      ep_name: "getOfferAgreementPdfData",
    },
    // allowance start date post API
    {
      ep_url: API_EVENT_URL["ALLOWANCE_START_DATE_POST_API"],
      ep_type: "POST",
      ep_name: "postAllowanceStartDateData",
    },
    // allowance to be created
    {
      ep_url: API_EVENT_URL["ALLOWANCE_START_DATE_POST_API"],
      ep_type: "POST",
      ep_name: "postAllowanceToBeCreated",
    },
    // allowance temp work
    {
      ep_url: API_EVENT_URL["ALLOWANCE_TEMP_WORK_API"],
      ep_type: "GET",
      ep_name: "getAllowanceTempWorkData",
    },
    {
      ep_url: API_EVENT_URL["ALLOWANCE_TEMP_WORK_API"],
      ep_type: "POST",
      ep_name: "postAllowanceTempWorkData",
    },
    {
      ep_url: API_EVENT_URL["NATIONAL_ALLOWANCE_TEMP_WORK_API"],
      ep_type: "GET",
      ep_name: "getNationalAllowanceTempWorkData",
    },
    {
      ep_url: API_EVENT_URL["NATIONAL_ALLOWANCE_TEMP_WORK_API"],
      ep_type: "POST",
      ep_name: "postNationalAllowanceTempWorkData",
    },
    {
      ep_url: API_EVENT_URL["ALLOWANCE_EDIT_TEMPWORK_POST"],
      ep_type: "POST",
      ep_name: "postAllowanceEditTempWorkData",
    },
    {
      ep_url: API_EVENT_URL["ALLOWANCE_TEMPWORK_DELETE"],
      ep_type: "DELETE",
      ep_name: "deleteAllowanceTempWorkData",
    },
    {
      ep_url: API_EVENT_URL["NATIONAL_ALLOWANCE_TEMP_WORK_DELETE"],
      ep_type: "DELETE",
      ep_name: "deleteNationalAllowanceTempWorkData",
    },
    // allowance items
    {
      ep_type: "GET",
      ep_name: "getAllowancesItems",
      ep_url: API_EVENT_URL["ALLOWANCE_ITEMS"],
    },
    {
      ep_type: "GET",
      ep_name: "getNationalAllowancesItems",
      ep_url: API_EVENT_URL["NATIONAL_ALLOWANCE_ITEMS"],
    },
    {
      ep_type: "GET",
      ep_name: "getNationalAllowancesItemsDifference",
      ep_url: API_EVENT_URL["NATIONAL_ALLOWANCE_ITEMS_DIFFERENCE"],
    },
    // overlapping allowance data
    {
      ep_url: API_EVENT_URL["OVERLAPPING_ALLOWACE_DATA_GET"],
      ep_type: "GET",
      ep_name: "getOverlappingAllowance",
    },
    // allowance API for UOM in promo
    {
      ep_url: API_EVENT_URL["ALLOWANCE_API_FOR_UOM_IN_PROMO"],
      ep_type: "POST",
      ep_name: "postAllowanceForUomInPromo",
    },
    // vendor API
    {
      ep_type: "GET",
      ep_name: "getVendorData",
      ep_url: API_EVENT_URL["VENDOR_DATA"],
    },
    // offer allowance API (for creating allowance)
    {
      ep_url: API_EVENT_URL["OFFER_ALLOWANCE"],
      ep_type: "POST",
      ep_name: "postOfferAllowance",
    },
    {
      ep_url: API_EVENT_URL["NATIONAL_ALLOWANCE_EDIT_TEMPWORK_POST"],
      ep_type: "POST",
      ep_name: "postNationalOfferEdit",
    },
    {
      ep_url: API_EVENT_URL["NATIONAL_CREATE_OFFER_ALLOWANCE"],
      ep_type: "POST",
      ep_name: "postNationalOfferAllowance",
    },
    {
      ep_url: API_EVENT_URL["ALLOWANCE_UPDATE"],
      ep_type: "PUT",
      ep_name: "putOfferAllowance",
    },
    {
      ep_url: API_EVENT_URL["NATIONAL_UPDATE_OFFER_ALLOWANCE"],
      ep_type: "PUT",
      ep_name: "putNationalOfferUpdateAllowance",
    },
    {
      ep_url: API_EVENT_URL["ALLOWANCE_PUT_REJECT_CANCEL_API"],
      ep_type: "PUT",
      ep_name: "putAllowanceRejectCancelData",
    },
    {
      ep_url: API_EVENT_URL["ALLOWANCE_PENDING_CANCEL"],
      ep_type: "DELETE",
      ep_name: "deleteAllowancePendingCancelData",
    },
    {
      ep_url: API_EVENT_URL["NATIONAL_ALLOWANCE_PENDING_CANCEL"],
      ep_type: "DELETE",
      ep_name: "nationalDeleteAllowancePendingCancelData",
    },
    {
      ep_url: API_EVENT_URL["ALLOWANCE_DELETE"],
      ep_type: "DELETE",
      ep_name: "deleteOfferData",
    },
    {
      ep_url: API_EVENT_URL["ALLOWANCE_DELETE_BY_OFFER"],
      ep_type: "DELETE",
      ep_name: "deleteNationalOfferData",
    },
    {
      ep_url: API_EVENT_URL["NATIONAL_TEMPWORK_DELETE"],
      ep_type: "DELETE",
      ep_name: "deleteNationalTempworkData",
    },
    {
      ep_url: API_EVENT_URL["ALLOWANCE_ITEMS_DIFFERENCE"],
      ep_type: "GET",
      ep_name: "getAllowanceItemsDifference",
    },
    {
      ep_url: API_EVENT_URL["ALLOWANCE_ITEMS_DIFFERENCE"],
      ep_type: "GET",
      ep_name: "getNationalAllowanceItemsDifference",
    },
    {
      ep_url: API_EVENT_URL["OFFER_RESEND"],
      ep_type: "POST",
      ep_name: "postResendOffer",
    },
    {
      ep_url: API_EVENT_URL["OFFER_CREATE_UPDTAE"],
      ep_type: "POST",
      ep_name: "postOfferCreateUpdate",
    },
    {
      ep_url: API_EVENT_URL["OFFER_ATTACHMENT_UPLOAD"],
      ep_type: "POST",
      ep_name: "postOfferAttachmentUpload",
    },
    {
      ep_url: API_EVENT_URL["OFFER_ATTACHMENT_DOWNLOAD"],
      ep_type: "GET",
      ep_name: "getOfferAttachmentDownload",
    },
    {
      ep_url: API_EVENT_URL["OFFER_ATTACHMENT_DELETE"],
      ep_type: "DELETE",
      ep_name: "deleteOfferAttachment",
    },
  ],
};

//The naming for the  methods  decalered on the LHS would be derived from the `use${capitalizeFirstLetter(ep_name)}Mutation/Query`
export const {
  // allowance API
  useGetAllowanceDataQuery,
  usePostAllowanceDataMutation,
  usePutAllowanceDataMutation,
  useGetOfferAgreementPdfDataMutation,
  // allowance start date post API
  usePostAllowanceStartDateDataMutation,
  // allowance to be created
  usePostAllowanceToBeCreatedMutation,
  // allowance temp work
  useGetAllowanceTempWorkDataQuery,
  useLazyGetAllowanceTempWorkDataQuery,
  usePostAllowanceTempWorkDataMutation,
  useGetNationalAllowanceTempWorkDataQuery,
  useLazyGetNationalAllowanceTempWorkDataQuery,
  usePostNationalAllowanceTempWorkDataMutation,
  usePostAllowanceEditTempWorkDataMutation,
  useDeleteAllowanceTempWorkDataMutation,
  useDeleteNationalAllowanceTempWorkDataMutation,
  // allowance items
  useGetAllowancesItemsQuery,
  useLazyGetAllowancesItemsQuery,
  useGetNationalAllowancesItemsQuery,
  useLazyGetNationalAllowancesItemsQuery,
  useGetAllowanceItemsDifferenceQuery,
  useGetNationalAllowanceItemsDifferenceQuery,
  useLazyGetNationalAllowancesItemsDifferenceQuery,
  // overlapping allowance data
  useGetOverlappingAllowanceQuery,
  // allowance API for UOM in promo
  usePostAllowanceForUomInPromoMutation,
  // vendor API
  useGetVendorDataQuery,
  // offer allowance API (for creating allowance)
  usePostOfferAllowanceMutation,
  usePostNationalOfferAllowanceMutation,
  usePostNationalOfferEditMutation,
  usePutNationalOfferUpdateAllowanceMutation,
  usePutOfferAllowanceMutation,
  usePutAllowanceRejectCancelDataMutation,
  useDeleteAllowancePendingCancelDataMutation,
  useNationalDeleteAllowancePendingCancelDataMutation,
  useDeletePromotionPendingCancelDataMutation,
  useDeleteOfferDataMutation,
  useDeleteNationalOfferDataMutation,
  usePostResendOfferMutation,
  usePostOfferCreateUpdateMutation,
  usePostOfferAttachmentUploadMutation,
  useGetOfferAttachmentDownloadQuery,
  useDeleteOfferAttachmentMutation,
} = injectEndPointsWrapper(apiObj);
