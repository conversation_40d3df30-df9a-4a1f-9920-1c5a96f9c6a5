import { useEffect, useState } from "react";
import { Calendar, DateObject } from "albertsons-react-multi-date-picker";
import Footer from "albertsons-react-multi-date-picker/plugins/range_picker_footer";
import { Controller, useFormContext } from "react-hook-form";
import { useDispatch } from "react-redux";
import { formatDate } from "@me-upp-js/utilities";

import "./calendar-container.scss";
import { add, compareAsc } from "date-fns";
import { useSelectorWrap } from "@me/data-rtk";
import { getTimeObject } from "apps/event-flow/src/app/shared/helpers/event-flow-helpers";

function CustomDate(props: {
  setPeriodStart: React.Dispatch<React.SetStateAction<string>>;
  setPeriodEnd: React.Dispatch<React.SetStateAction<string>>;
  fieldProps: any;
  dynamicRegField?: any;
  handleApplyButton: any;
  minDate?: Date | string | number | DateObject;
  maxDate?: Date | string | number | DateObject;
  hideVehicles?: boolean;
}) {
  const dispatch = useDispatch();
  const { getValues, setValue, register, control } = useFormContext();
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");

  const periodEndDatesArr: any[] = [],
    quarterEndDatesArr: any[] = [],
    holidaysArr: any[] = [];

  const [selectedDateRange, setSelectedDateRange] = useState({
    startDate: "",
    endDate: "",
  });

  const startDate = eventDetailsData?.startDate || getValues("startDate");
  const endDate = getValues("endDate");
  const periscpeData = eventDetailsData?.dataFetchedFromPeriscope;

  useEffect(() => {
    setSelectedDateRange({
      startDate: startDate,
      endDate: endDate,
    });
  }, [startDate, endDate, periscpeData]);

  const specialDays = {
    periodEndDates: [periodEndDatesArr],
    quarterEndDates: [quarterEndDatesArr],
    holidayDates: [holidaysArr],
  };

  let currentDate;
  if (
    props?.minDate &&
    (props?.fieldProps?.gqlQueryConst === "allowance" ||
      props?.fieldProps?.gqlQueryConst === "promotions")
  ) {
    currentDate = new DateObject(props?.minDate);
  } else {
    currentDate = new DateObject(
      add(getTimeObject(), {
        weeks: 8,
      })
    );
  }

  const mapDaysCallback = ({ date }: any) => {
    return setPropsForMapDays(date);
  };

  const calendarProps = {
    numberOfMonths: 1,
    displayWeekNumbers: true,
    shadow: false,
    showOtherDays: false,
    buttons: false,
    range: true,
    value: [`${selectedDateRange?.startDate}`, `${selectedDateRange?.endDate}`],
    className: "sw-calendarWrapper",
    mapDays: mapDaysCallback,
  };

  const { registerField } = props.fieldProps;

  const setPropsForMapDays = (date: any) => {
    //Sets classes for special days
    const obj = {},
      specialDatesAvailability = {},
      currentMonthDate = date.format("YYYY-MM-DD"),
      dayTypesPrefixArr = ["periodEnd", "quarterEnd", "holiday"];

    dayTypesPrefixArr.forEach(e => {
      specialDatesAvailability[`${e}DatesAvailable`] = specialDays[
        `${e}Dates`
      ].filter((elem: any) => {
        const isSpecialDay = elem.includes(currentMonthDate);
        if (isSpecialDay) {
          obj["className"] = `${e}Day`;
        }
      });
    });

    return obj;
  };

  const handle_datesSelection = date => {
    date[0] = new DateObject(date[0]);
    if (
      props?.hideVehicles &&
      eventDetailsData?.startDate &&
      new Date(date[0]).getTime() !==
        new Date(eventDetailsData?.startDate).getTime()
    ) {
      date[0] = new DateObject(eventDetailsData?.startDate);
    }
    if (
      props?.hideVehicles &&
      compareAsc(formatDate(new Date(date[1])), formatDate(new Date())) < 0
    ) {
      date[1] = new DateObject(new Date());
    }
    // when both the dates are selected
    const fromDate = {
      day: date[0].day,
      month: date[0].month.number,
      year: date[0].year,
    };

    const toDate = {
      day: date[1]?.day,
      month: date[1]?.month.number,
      year: date[1]?.year,
    };
    setSelectedDateRange({
      startDate: `${fromDate?.year}-${fromDate?.month}-${fromDate?.day}`,
      endDate: `${toDate?.year}-${toDate?.month}-${toDate?.day}`,
    });
    props.handleApplyButton(false);

    if (date?.length === 2) {
      const to_millis = new Date(
          toDate["year"],
          toDate["month"] - 1,
          toDate["day"]
        ).getTime(), // to convert into milliseconds   ,
        from_millis = new Date(
          fromDate["year"],
          fromDate["month"] - 1,
          fromDate["day"]
        ).getTime();

      props.setPeriodEnd(to_millis?.toLocaleString()); //TODO: Dates are wrong in IST
      props.dynamicRegField && setValue(props.dynamicRegField, {});
      setValue(
        props.dynamicRegField
          ? `${props.dynamicRegField}.startDate`
          : "eventCreationVehicle.startDate",
        from_millis
      );
      setValue(
        props.dynamicRegField
          ? `${props.dynamicRegField}.endDate`
          : "eventCreationVehicle.endDate",
        to_millis
      );
      props.handleApplyButton(true);
    }
  };
  return (
    <div id="abs-custom-date-controller-cont">
      <Controller
        name={props.dynamicRegField ? props.dynamicRegField : registerField}
        control={control}
        render={({ field }) => (
          <Calendar
            {...calendarProps}
            // {...field}
            onChange={handle_datesSelection}
            minDate={props?.minDate}
            maxDate={add(new Date(selectedDateRange?.startDate || new Date()), {
              years: 2,
            })}
            currentDate={currentDate}
            plugins={[
              <Footer
                position="bottom"
                format="MM/DD/YY"
                names={{
                  selectedDates: "",
                  from: "",
                  to: "",
                  selectDate: "",
                  close: "close",
                  separator: "-",
                }}
              />,
            ]}
          />
        )}
      />
    </div>
  );
}

export default CustomDate;
