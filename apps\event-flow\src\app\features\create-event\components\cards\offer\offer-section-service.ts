import {
  checkIsStoreGroupSectionEnable,
  formatAmount,
  getAllowance<PERSON>apLabel,
  getValuetrimmed,
  isHfOrIfType,
  removeParams,
} from "../../../service/allowance/allowance-service";
import { getObjectKeys } from "../../../service/allowance/allowance-stepper-service";
import {
  FORM_SECTION_KEYS,
  OFFER_FORM_FIELDS,
  SECTION_KEYS,
} from "./offer-flow-config";
import efConstants from "../../../../../shared/ef-constants/ef-constants";
import { SHOW_ALLOWANCE_TO_BE_CREATED_OPTIONS } from "../../../constants/fields/allowance/field-allowance-to-be-created-section";
import {
  checkIsNationalEvent,
  getOfferMapKey,
  getStoregroupFormData,
} from "./offer-service";

export const setOfferConfigAndFormData = ({
  tempworkData,
  isEditEnable = false,
  offerSections,
  additionalDetails,
}) => {
  const {
    ALLOWANCE_SCREEN_TYPES,
    ALLOWANCE_TYPES,
    CREATEIND_ALLOWANCE_TO_BE_CREATED_OPTION_MAPPER,
  } = efConstants;
  const {
    ALLOWANCE_PRIME_SECTION,
    ALLOWANCE_STORE_SELECTION,
    ALLOWANCE_TO_BE_CREATED,
    ALLOWANCE_DATES,
    ALLOWANCE_AMOUNT,
    ALLOWANCE_BILLING,
  } = FORM_SECTION_KEYS;
  const { additionalDatesKey } = OFFER_FORM_FIELDS;

  const { eventType, createInd, allowancePerfData, queryParams } =
    additionalDetails;
  // const {  offerKey, taskType } = queryParams;
  let offerFormData = {};
  const offerSectionConfig = {};
  const allowanceType = tempworkData?.allowanceType;
  const isHfOrIf = isHfOrIfType(allowanceType);
  const isCancelled = queryParams?.isCancelled === "true";
  const isSaved = queryParams?.isSaved === "true";
  const isRedirectToMainEntry = queryParams?.isRedirectToMainEntry === "true";
  const isNationalEvent = checkIsNationalEvent(eventType);

  const getPerformanceById = (id: string) => {
    return allowancePerfData.find(perf => perf?.id === id);
  };

  const validateAllowanceTypeAndPerformance = (formKey: string) => {
    const { allowanceType, allowancePerformanceId, allowancePerformances } =
      tempworkData;
    if (allowanceType && allowancePerformanceId && createInd) {
      const perfOption = isEditEnable
        ? getPerformanceById(allowancePerformanceId)
        : allowancePerformances;

      offerFormData[formKey] = {
        allowanceType: getAllowanceMapLabel(allowanceType),
        createInd,
        performance: perfOption?.performance,
        perfConfig: perfOption,
      };
      return true;
    }
    return false;
  };

  const validateOverrideHeaderFlatAmt = (formKey: string) => {
    const { allowanceType, overrideHeaderFlatAmt } = tempworkData;
    if (allowanceType && overrideHeaderFlatAmt >= 0 && createInd) {
      offerFormData[formKey] = {
        ...offerFormData[formKey],
        overrideHeaderFlatAmt,
      };
      return true;
    }
    return false;
  };

  const validateStoreGroups = (formKey: string) => {
    const { overrideStoreGroups } = tempworkData;
    const { storeGroupId, storeGroupName, storeGroupType } =
      overrideStoreGroups?.[0] || {};
    if (storeGroupId && storeGroupName && storeGroupType) {
      offerFormData[formKey] = {
        ...offerFormData[formKey],
        [ALLOWANCE_STORE_SELECTION]: getStoregroupFormData(
          overrideStoreGroups,
          isEditEnable
        ),
      };
      return true;
    }
    return false;
  };

  const validateAllowanceToBecreated = (formKey: string) => {
    const {
      allowanceType,
      allowancesMap,
      productSources = [],
      offerAllowancesGroupInfoMap = {},
    } = tempworkData;
    const allowanceMapKeys = getObjectKeys(allowancesMap);
    if (allowanceType && allowanceMapKeys?.length && createInd) {
      const allowanceToBeCreatedKey =
        CREATEIND_ALLOWANCE_TO_BE_CREATED_OPTION_MAPPER?.[createInd];
      const availableOptions =
        SHOW_ALLOWANCE_TO_BE_CREATED_OPTIONS?.[allowanceType];

      const allowanceToBeCreatedOptionKey = getObjectKeys(
        availableOptions
      )?.find(option => availableOptions[option]?.createIndex === createInd);

      const allowanceToBeCreatedOption = availableOptions?.[
        allowanceToBeCreatedOptionKey || ""
      ]?.options?.find(option => option.key === allowanceToBeCreatedKey);

      offerFormData[formKey] = {
        ...offerFormData[formKey],
        allowanceToBeCreated: allowanceToBeCreatedKey,
        [ALLOWANCE_TO_BE_CREATED]: allowanceToBeCreatedOption,
        productSources,
        offerAllowancesGroupInfoMap,
      };
      return !!getObjectKeys(allowanceToBeCreatedOption).length;
    }
    return false;
  };

  const validateAllowanceDatesVehicle = ({
    formKey,
    offerMapKey,
  }: {
    formKey: string;
    offerMapKey: string;
  }) => {
    const { allowancesMap, vehicleDatesMap } = tempworkData;
    const vehicleDates = vehicleDatesMap?.[offerMapKey] || {};
    if (
      offerMapKey &&
      getObjectKeys(vehicleDates).length &&
      (isHfOrIf || allowancesMap?.[offerMapKey]?.length)
    ) {
      const currentOfferData = offerFormData[formKey];
      offerFormData = {
        ...offerFormData,
        [formKey]: {
          ...currentOfferData,
          [offerMapKey]: vehicleDates,
        },
        ...(!isHfOrIf && {
          additionalDates: {
            ...offerFormData?.[additionalDatesKey],
            [offerMapKey]: allowancesMap?.[offerMapKey],
          },
          isCancelled: {
            ...(offerFormData?.["isCancelled"] || {}),
            [offerMapKey]: isCancelled,
          },
        }),
      };
      return true;
    }
    return false;
  };

  const validateAllowanceCreationVehicle = () => {
    const formKey = ALLOWANCE_DATES;
    const { allowancesMap } = tempworkData;
    const offerMapKey = getObjectKeys(allowancesMap)?.[0];
    return validateAllowanceDatesVehicle({ formKey, offerMapKey });
  };

  const getValidateSubSections = () => {
    const conditionalSubSections: any = [];
    if (
      allowanceType === ALLOWANCE_TYPES.HEADERFLAT.key &&
      createInd !== ALLOWANCE_TYPES.HEADERFLAT.createInd[1]
    )
      conditionalSubSections.push(validateOverrideHeaderFlatAmt);
    if (
      eventType === ALLOWANCE_SCREEN_TYPES.AO.key &&
      allowanceType !== ALLOWANCE_TYPES.CASE.key &&
      checkIsStoreGroupSectionEnable(createInd)
    )
      conditionalSubSections.push(validateStoreGroups);
    if (!isHfOrIf) conditionalSubSections.push(validateAllowanceToBecreated);
    return [
      validateAllowanceTypeAndPerformance,
      ...conditionalSubSections,
      validateAllowanceCreationVehicle,
    ];
  };

  const offerPrimeSectionHandler = () => {
    const formKey = ALLOWANCE_PRIME_SECTION;
    const validateSubSections = getValidateSubSections();
    const subSectionsValidationResponse = validateSubSections?.map(
      validateSubSection => validateSubSection(formKey)
    );
    if (
      !isEditEnable &&
      isCancelled &&
      (isNationalEvent || isRedirectToMainEntry)
    )
      return false;
    return !subSectionsValidationResponse.includes(false);
  };

  const offerDateSectionHandler = (offerTypeKey: string) => {
    const formKey = ALLOWANCE_DATES;
    const offerMapKey = getOfferMapKey(createInd, offerTypeKey);
    return validateAllowanceDatesVehicle({ formKey, offerMapKey });
  };

  const offerAmountsSectionHandler = (offerTypeKey: string) => {
    const formKey = ALLOWANCE_AMOUNT;
    const { allowancesMap } = tempworkData;
    const offerMapKey = getOfferMapKey(createInd, offerTypeKey);
    if (offerMapKey && allowancesMap?.[offerMapKey]?.length) {
      const filteredAllowanceItems =
        allowancesMap?.[offerMapKey]?.filter(
          item => item?.includeInd && item?.allowanceItems?.length
        ) || [];

      const { finalizedAmountsInd = false, headerFlatAmt } =
        filteredAllowanceItems?.[0] || {};

      if (
        (!isEditEnable && !finalizedAmountsInd) ||
        !filteredAllowanceItems?.length
      ) {
        return false;
      }
      if (allowanceType === ALLOWANCE_TYPES.HEADERFLAT.key) {
        const headerFlatAmtValue = Number(headerFlatAmt);
        const isAmountValid =
          !isNaN(headerFlatAmtValue) && headerFlatAmtValue >= 0;
        if (!isAmountValid) return false;
        offerFormData[formKey] = {
          ...offerFormData[formKey],
          [offerMapKey]: {
            headerFlatAmtItems: allowancesMap?.[offerMapKey],
            headerFlatAmt,
          },
        };
      } else {
        offerFormData[formKey] = {
          ...offerFormData[formKey],
          [offerMapKey]: {
            allowanceAmount:
              formatAmount(
                filteredAllowanceItems?.[0]?.allowanceItems?.[0]?.allowanceAmount?.toString()
              ) || "",
            uom:
              filteredAllowanceItems?.[0]?.allowanceItems?.[0]?.allowUomType ||
              "",
            allowanceItems: filteredAllowanceItems,
            allowances: allowancesMap?.[offerMapKey],
          },
        };
      }
      return true;
    }
    return false;
  };

  const offerBillingSectionHandler = (offerTypeKey: string) => {
    const formKey = ALLOWANCE_BILLING;
    const { allowancesMap } = tempworkData;
    const offerMapKey = getOfferMapKey(createInd, offerTypeKey);
    const { allowanceBillingInfo } = allowancesMap?.[offerMapKey]?.[0] || {};
    if (
      getObjectKeys(allowanceBillingInfo).length &&
      allowanceBillingInfo?.suggestedVendorPaymentType !== undefined
    ) {
      const allowanceBillingInfoList = allowancesMap?.[offerMapKey]?.map(
        allow => {
          const {
            suggestedVendorPaymentType = "",
            suggestedAcPayableVendorNbr = "",
            suggestedAcReceivableVendorNbr = "",
          } = allow?.allowanceBillingInfo || {};
          return {
            ...allow?.allowanceBillingInfo,
            acApOrArNumber:
              suggestedVendorPaymentType === "Deduct"
                ? suggestedAcPayableVendorNbr
                : suggestedAcReceivableVendorNbr,
          };
        }
      );
      const {
        absMerchVendor,
        absVendorName,
        absVendorPaymentType,
        acPayableVendorNbr,
        acReceivableVendorNbr,
        billingContactName = "",
        billingContactEmail = "",
        vendorComment = "",
        vendorOfferTrackingNbr = "",
      } = allowanceBillingInfo;

      if (!absMerchVendor) return false;

      offerFormData[formKey] = {
        ...offerFormData[formKey],
        [offerMapKey]: {
          absMerchVendor: getValuetrimmed(absMerchVendor),
          absVendorName: getValuetrimmed(absVendorName),
          absVendorPaymentType,
          acPayableVendorNbr: getValuetrimmed(acPayableVendorNbr),
          acReceivableVendorNbr: getValuetrimmed(acReceivableVendorNbr),
          billingContactName,
          billingContactEmail,
          vendorComment,
          vendorOfferTrackingNbr: getValuetrimmed(vendorOfferTrackingNbr),
          allowanceBillingInfo: allowanceBillingInfoList,
        },
      };
      return true;
    }
    return false;
  };

  const getSectionHandler = (sectionKey: string) => {
    switch (sectionKey) {
      case SECTION_KEYS.ALLOWANCE_PRIME_SECTION:
        return offerPrimeSectionHandler;
      case SECTION_KEYS.ALLOWANCE_DATES:
        return offerDateSectionHandler;
      case SECTION_KEYS.ALLOWANCE_AMOUNT:
        return offerAmountsSectionHandler;
      case SECTION_KEYS.ALLOWANCE_BILLING:
        return offerBillingSectionHandler;
      default:
        return () => null;
    }
  };

  const getEditScrollIndex = () => {
    if (isCancelled && !isRedirectToMainEntry) return 1;
    else if (isSaved) return 2;
    return 0;
  };

  if (allowanceType) {
    let isNotActive = false;
    offerSections?.forEach((section, index: number) => {
      const { key, sectionKey = "", offerTypeKey = "" } = section;
      const handler = getSectionHandler(sectionKey);
      const isSectionActive =
        !isEditEnable && isNotActive ? false : handler(offerTypeKey);
      if (!isEditEnable) {
        // CREATE MODE
        offerSectionConfig[key] = {
          isActive: !isNotActive,
          scrollTo: !(isSectionActive || isNotActive),
        };
        isNotActive = !isSectionActive;
      } else {
        // EDIT MODE
        const scrollToIndex = getEditScrollIndex();
        offerSectionConfig[key] = {
          isActive: true,
          scrollTo: scrollToIndex === index,
        };
      }
    });
  }

  if (queryParams?.isCancelled || queryParams?.isSaved) {
    removeParams();
  }

  // console.log("offerFormData=>>", offerFormData);
  // console.log("offerSectionConfig=>>", offerSectionConfig);

  return { offerFormData, offerSectionConfig };
};
