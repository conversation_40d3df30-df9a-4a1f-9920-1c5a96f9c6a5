import { useEffect, useRef, useState } from "react";
import { useSelectorWrap } from "@me/data-rtk";
import {
  useGetVehicleByStartAndEndDateQuery,
  useGetVehicleByTypeAndYearQuery,
} from "../../../../../../../graphql/generated/schema";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";
import { getYearFromFormDate } from "../../../../../service/event-details/event-detail-service";
import { parseISO } from "date-fns";
import { InputSelectAtom } from "../../../../fields/index";
import VehicleTypes from "../../../layout/vehicle-types-custom-date";
import { getObjectKeys } from "../../../../../service/allowance/allowance-stepper-service";
import { DatePicker<PERSON>tom } from "../../../../fields/allowance-atoms";
import { checkIsNationalEvent } from "../../offer-service";
import {
  getAllowanceKey,
  isHfOrIfType,
} from "../../../../../service/allowance/allowance-service";

const BaseAllowanceDates = ({
  formControls,
  sectionConfiguration,
  isEditEnable = false,
  offerMapKey = "",
  allowanceRegField,
  vehicleFields,
  setVehicleFields,
}) => {
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { data: allowanceForm } = useSelectorWrap("allowance_form_data");

  const { eventCreationVehicle = {} } = eventDetailsData || {};
  const { startDate: eventStartDate = "", endDate: eventEndDate = "" } =
    eventCreationVehicle || {};
  const { NEW_DIRECTION_FEATURES_FLAGS } = efConstants,
    { isDirectionalChangesEnable } = NEW_DIRECTION_FEATURES_FLAGS;

  const {
    vehicleTypeOrCustomDate,
    year,
    startWeekOrVehicle,
    vehicleStart,
    vehicleEnd,
    vehicleRegField,
  } = sectionConfiguration?.fields || {};

  const {
    register = () => null,
    control,
    setValue = () => null,
    getValues = () => null,
  } = formControls || {};
  const formData = getValues() || {};
  const { allowanceType = "" } = formData || {};
  const allowanceTypeKey = getAllowanceKey(allowanceType?.toUpperCase());
  const isNationalEvent = checkIsNationalEvent(eventDetailsData?.eventType);
  const allowanceVehicleData =
    allowanceForm?.allowanceFormData?.[allowanceRegField]
      ?.allowanceCreationVehicle?.[offerMapKey];
  const isHfIfType = isHfOrIfType(allowanceTypeKey);
  const isVehicleDatesEnable = !isNationalEvent || isHfIfType;

  const [
    startWeekOrVehicleDropdownOptions,
    setStartWeekOrVehicleDropdownOptions,
  ] = useState<
    {
      id: string | null | object | undefined;
      vehicleNm: string | null | object | undefined;
      startDate: string;
      endDate: string;
    }[]
  >([]);
  const [yearOptions, setYearOptions] = useState<
    { name: string | null | undefined | number }[]
  >([]);
  const vehicleTypeID = useRef("");

  const isCustomDate = ["Custom Date", "CustomDate"].includes(
    vehicleFields.vehicleTabType
  );

  const { data: startWeekOrVehicleOptions, refetch: fetchVehicleOptions } =
    useGetVehicleByStartAndEndDateQuery({
      variables: {
        vehicleTypId: vehicleTypeID.current,
        startDate: eventStartDate,
        endDate: eventEndDate,
      },
      skip: isCustomDate || !vehicleTypeID.current,
    });

  const { data: startWeekOrVehicleCustomOptions, refetch: fetchCustomOptions } =
    useGetVehicleByTypeAndYearQuery({
      variables: { vehicleTypId: vehicleTypeID.current, year: null },
      skip: !isCustomDate || !vehicleTypeID.current,
    });

  // save startWeekOrVehicleOptions to local state
  useEffect(() => {
    const startWeekOrVehicleCustomOptionsData =
      startWeekOrVehicleCustomOptions?.getVehicleByTypeAndYear;
    let startWeekOrVehicleAPIResponse: any[] = [];

    // setIsVehicleTypeInvalid(
    //   !isCustomDate &&
    //     !startWeekOrVehicleOptions?.getVehicleByStartAndEndDate?.length
    // );

    if (isCustomDate) {
      startWeekOrVehicleAPIResponse =
        startWeekOrVehicleCustomOptionsData?.map(ele => {
          return {
            id: ele?.id,
            vehicleNm: ele?.vehicleNm,
            startDate: ele?.startDate,
            endDate: ele?.endDate,
          };
        }) || [];
    } else {
      startWeekOrVehicleAPIResponse =
        startWeekOrVehicleOptions?.getVehicleByStartAndEndDate?.map(ele => {
          return {
            id: ele?.id,
            vehicleNm: ele?.vehicleNm,
            startDate: ele?.startDate,
            endDate: ele?.endDate,
          };
        }) || [];
    }

    if (startWeekOrVehicleCustomOptionsData) {
      setVehicleFields({
        ...vehicleFields,
        vehicleId: startWeekOrVehicleCustomOptionsData?.[0]?.id || "",
      });
    }
    setStartWeekOrVehicleDropdownOptions(startWeekOrVehicleAPIResponse);
  }, [startWeekOrVehicleOptions, startWeekOrVehicleCustomOptions]);

  useEffect(() => {
    if (!allowanceVehicleData || isDirectionalChangesEnable) {
      setDefaultValuesOnStepLoad(eventCreationVehicle);
    } else if (allowanceVehicleData) {
      const { vehicleRef = {}, dateRange = {} } = allowanceVehicleData || {};
      const vehicleValue = {
        ...vehicleRef,
        startDate: dateRange?.startDate,
        endDate: dateRange?.endDate,
      };
      setDefaultValuesOnStepLoad(vehicleValue);
    }
  }, [JSON.stringify(allowanceVehicleData)]);

  useEffect(() => {
    if (!vehicleTypeID.current) {
      isCustomDate ? fetchCustomOptions() : fetchVehicleOptions();
    }
  }, [vehicleTypeID.current]);

  useEffect(() => {
    setValue(year?.registerField, yearOptions?.[0]?.name);
    setValue(`${vehicleRegField?.registerField}.year`, yearOptions?.[0]?.name);
  }, [yearOptions]);

  function setDefaultValuesOnStepLoad(vehicleData) {
    vehicleTypeID.current = vehicleData?.vehicleType?.vehicleTypeId;
    const vehicleObject = {
      ...vehicleFields,
      vehicleId: vehicleData?.vehicleId,
      vehicleNm: vehicleData?.vehicleNm,
      vehicleType: vehicleData?.vehicleType,
      vehicleTypeId: vehicleData?.vehicleType?.vehicleTypeId,
      vehicleTypeDesc: vehicleData?.vehicleType?.vehicleTypDesc,
      vehicleTabType: vehicleData?.vehicleType?.vehicleTypDesc,
      startDate: vehicleData?.startDate,
      endDate: vehicleData?.endDate,
      year: null,
    };
    setVehicleFields(vehicleObject);
    setValue(vehicleRegField?.registerField, vehicleObject);
    setValue(startWeekOrVehicle?.registerField, vehicleData?.vehicleNm);
    setValue(vehicleStart?.registerField, vehicleData?.startDate);
    setValue(vehicleEnd?.registerField, vehicleData?.endDate);
    saveYearOptions();
  }

  // save year options
  function saveYearOptions() {
    const getYearFromDate = date => {
      return new Date(date?.replace(/-/g, "/")).getFullYear();
    };

    let startYear = getYearFromDate(vehicleFields.startDate);
    let endYear = getYearFromDate(vehicleFields.endDate);

    if (!startYear) {
      startYear = isEditEnable
        ? getYearFromFormDate(
            parseISO(allowanceVehicleData?.dateRange?.startDate)
          )
        : getYearFromFormDate(parseISO(eventStartDate));
      endYear = isEditEnable
        ? getYearFromFormDate(
            parseISO(allowanceVehicleData?.dateRange?.endDate)
          )
        : getYearFromFormDate(parseISO(eventEndDate));
    }

    startYear === endYear
      ? setYearOptions([{ name: startYear }])
      : setYearOptions([{ name: startYear }, { name: endYear }]);

    return startYear;
  }

  const vehicleTypeOrCustomDateField = getObjectKeys(
    vehicleFields?.vehicleType || {}
  ) ? (
    <div className="w-[200px]" id="abs-base-allownace-dates-vehicle-type-cont">
      <VehicleTypes
        fieldProps={vehicleTypeOrCustomDate}
        dynamicRegField={vehicleRegField?.registerField}
        vehicleTypeProps={vehicleFields?.vehicleType}
        minDate={eventStartDate}
        maxDate={eventEndDate}
        isAllowanceForm={true}
        onChange={() => null}
        disabled={true}
      />
    </div>
  ) : null;

  const yearField = (
    <div className="max-w-[90px]" id="abs-base-allownace-dates-year-field-cont">
      <InputSelectAtom
        fieldProps={year}
        register={register}
        control={control}
        displayLabel={year.displayLabel}
        options={yearOptions}
        disabled={year?.disabled}
        onChange={() => null}
        className="!w-auto"
      />
    </div>
  );

  const startWeekOrVehicleField = (
    <div
      className="flex-1"
      id="abs-base-allownace-dates-input-select-atom-container"
    >
      <InputSelectAtom
        fieldProps={startWeekOrVehicle}
        register={register}
        onChange={() => null}
        control={control}
        displayLabel={startWeekOrVehicle.displayLabel}
        options={startWeekOrVehicleDropdownOptions}
        disabled={startWeekOrVehicle?.disabled}
        className={!isVehicleDatesEnable ? "!max-w-[200px]" : ""}
        parentClassName={!isVehicleDatesEnable ? "!max-w-[200px]" : ""}
      />
    </div>
  );

  const vehicleStartField = (
    <div
      className="flex-1"
      id="abs-base-allownace-dates-input-date-picker-UDS-container"
    >
      <DatePickerAtom
        control={control}
        register={register}
        fieldProps={vehicleStart}
        disabled={vehicleStart?.disabled}
      />
    </div>
  );

  const VehicleEndField = (
    <div
      className="flex-1"
      id="abs-base-allownace-dates-get-field-with-highleted-wrapper-cont-one"
    >
      <DatePickerAtom
        control={control}
        register={register}
        fieldProps={vehicleEnd}
        disabled={vehicleEnd?.disabled}
      />
    </div>
  );

  return (
    <div
      className="w-full gap-4 mt-3 flex inline-vehicle-info"
      id="abs-base-allownace-dates-vehicle-details"
    >
      {control && (
        <>
          {vehicleTypeOrCustomDateField}
          {yearField}
          {startWeekOrVehicleField}
          {isVehicleDatesEnable && (
            <>
              {vehicleStartField}
              {VehicleEndField}
            </>
          )}
        </>
      )}
    </div>
  );
};

export default BaseAllowanceDates;
