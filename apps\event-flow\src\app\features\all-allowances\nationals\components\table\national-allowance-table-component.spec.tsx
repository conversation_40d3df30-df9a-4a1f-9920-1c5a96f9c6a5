import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import NationalAllowanceTableComponent from "./national-allowance-table-component";
import { useSelectorWrap } from "@me/data-rtk";
import { getOAMRemoteUser, useUpcFormatter } from "@me/util-helpers";
import { Provider } from "react-redux";
import { app_store } from "@me/data-rtk"; // Import your Redux store
import { MemoryRouter } from "react-router-dom"; // Import MemoryRouter
import Table from "@albertsons/uds/molecule/Table";
import "@testing-library/jest-dom";

// Mock external hooks
jest.mock("@me/data-rtk", () => ({
  ...jest.requireActual("@me/data-rtk"),
  useSelectorWrap: jest.fn(),
}));

jest.mock("@me/util-helpers", () => ({
  ...jest.requireActual("@me/util-helpers"),
  getOAMRemoteUser: jest.fn().mockReturnValue({
    OAM_REMOTE_USER: "mockUser",
    USER_EMAIL: "<EMAIL>",
  }),
  useUpcFormatter: jest.fn(() => [
    "formattedUPC",
    jest.fn(upc => `formatted-${upc}`), // Ensure mock function formats UPC
  ]),
}));

jest.mock("@albertsons/uds/molecule/Table", () => ({
  __esModule: true,
  default: jest.fn(() => <div data-testid="table" />),
}));

// Sample mock data
const mockItems = [
  {
    itemId: "1",
    itemDescription: "Item 1",
    primaryUpc: "123456",
    itemUpcs: {},
    packWhse: 10,
    size: "M",
    vendorDetails: [{ isEmptyVendor: false }],
  },
  {
    itemId: "2",
    itemDescription: "Item 2",
    primaryUpc: "654321",
    itemUpcs: {},
    packWhse: 20,
    size: "L",
    vendorDetails: [{ isEmptyVendor: true }],
  },
];

describe("NationalAllowanceTableComponent", () => {
  beforeEach(() => {
    // Mock `useSelectorWrap` for different keys
    (useSelectorWrap as jest.Mock).mockImplementation(key => {
      switch (key) {
        case "selectedSwitchValue_rn":
          return { data: { selectedSwitch: "value" } };
        case "isUpdateAllowanceDistributorSelection_rn":
          return { data: { isAllowancefilterType: true } };
        case "national_divisions_config":
          return { data: { selectedDivisionData: { divisionId: 1 } } };
        case "isUpdateAllClicked_rn":
          return { data: { isUpdateAll: false } };
        case "leadDistributorsChanged_rn":
          return { data: { isLeadChange: false } };
        default:
          return { data: {} };
      }
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  // Wrap each test with the Redux Provider and app_store
  test("should render correctly", async () => {
    render(
      <Provider store={app_store}>
        <MemoryRouter>
          <NationalAllowanceTableComponent items={mockItems} />
        </MemoryRouter>
      </Provider>
    );

    // Wait for the table element to appear
    const table = await screen.findByTestId("table");

    expect(table).toBeInTheDocument(); // Now this will wait until the element is found
  });

  test("should format items correctly", () => {
    render(
      <Provider store={app_store}>
        <MemoryRouter>
          <NationalAllowanceTableComponent items={mockItems} />
        </MemoryRouter>
      </Provider>
    );
    expect(useUpcFormatter).toHaveBeenCalledTimes(mockItems.length);
  });

  test("should update itemObj when items change", async () => {
    const updatedItems = [
      {
        itemId: "3",
        itemDescription: "Updated Item",
        primaryUpc: "987654",
        itemUpcs: {},
        packWhse: 30,
        size: "S",
        vendorDetails: [{ isEmptyVendor: false }],
      },
    ];

    const { rerender } = render(
      <Provider store={app_store}>
        <MemoryRouter>
          <NationalAllowanceTableComponent items={mockItems} />
        </MemoryRouter>
      </Provider>
    );
    await waitFor(() => {
      rerender(
        <Provider store={app_store}>
          <MemoryRouter>
            <NationalAllowanceTableComponent items={updatedItems} />
          </MemoryRouter>
        </Provider>
      );
    });

    const table = screen.getByTestId("table");
    expect(table).toBeInTheDocument();
  });
  test("should pass formatted items to Table component", () => {
    render(
      <Provider store={app_store}>
        <MemoryRouter>
          <NationalAllowanceTableComponent items={mockItems} />
        </MemoryRouter>
      </Provider>
    );

    // Ensure Table was called with the correctly formatted items
    expect(Table).toHaveBeenCalledWith(
      expect.objectContaining({
        items: expect.arrayContaining([
          expect.objectContaining({
            itemId: "1",
            primaryUpc: "formatted-123456", // Expect formatted UPC to match the mock logic
          }),
        ]),
      }),
      {}
    );
  });

  test("should handle empty vendor details correctly", () => {
    const mockItemsWithEmptyVendor = [
      {
        itemId: "1",
        itemDescription: "Item with empty vendor",
        primaryUpc: "111111",
        itemUpcs: {},
        packWhse: 10,
        size: "M",
        vendorDetails: [{ isEmptyVendor: true }],
      },
    ];

    render(
      <Provider store={app_store}>
        <MemoryRouter>
          <NationalAllowanceTableComponent items={mockItemsWithEmptyVendor} />
        </MemoryRouter>
      </Provider>
    );

    const table = screen.getByTestId("table");
    expect(table).toBeInTheDocument();
  });
});
