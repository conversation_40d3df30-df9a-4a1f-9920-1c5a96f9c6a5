import { render } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { ALERT_VIEW_GRID_COLUMNS } from "./alerts-view-columns-config";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom";

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useFormContext: jest.fn(),
  useLocation: jest.fn(),
}));
jest.mock("react-router-dom", () => ({
  NavLink: jest.fn(({ to, className, id, children }) => (
    <a href={to} className={className} id={id} data-testid="nav-link">
      {children}
    </a>
  )),
}));
describe("ALERT_VIEW_GRID_COLUMNS", () => {
  it('displays correct information in the "Event" column', () => {
    const alertData = {
      id: "123",
      planEventIdNbr: "10122",
      eventName: "Test Event",
      taskId: "1",
    };
    const eventColumn: any = ALERT_VIEW_GRID_COLUMNS.find(
      column => column?.id === "event"
    );
    const { getByText } = render(<>{eventColumn?.value(alertData)}</>);

    expect(
      getByText(`${alertData?.planEventIdNbr} - ${alertData?.eventName}`)
    ).toBeInTheDocument();
  });

  it('displays correct information in the "Date" column', () => {
    const alertData = {
      startDate: "01/01/22",
      endDate: "01/10/22",
    };
    const dateColumn: any = ALERT_VIEW_GRID_COLUMNS.find(
      column => column.id === "date"
    );
    const { getByText } = render(<>{dateColumn.value(alertData)}</>);

    expect(
      getByText(`${alertData.startDate} - ${alertData.endDate}`)
    ).toBeInTheDocument();
  });

  it('displays correct information in the "Vendor" column', () => {
    const alertData = {
      simsTaskVendor: [
        {
          vendorId: "V123",
          vendorName: "Test Vendor",
        },
      ],
    };
    const vendorColumn: any = ALERT_VIEW_GRID_COLUMNS.find(
      column => column.id === "vendor"
    );
    const { getByText } = render(<>{vendorColumn.value(alertData)}</>);

    expect(
      getByText(
        `${alertData.simsTaskVendor[0].vendorId} ${alertData.simsTaskVendor[0].vendorName}`
      )
    ).toBeInTheDocument();
  });

  it('displays correct information in the "Status" column', () => {
    const alertData = {
      eventStatus: "Draft",
    };
    const statusColumn: any = ALERT_VIEW_GRID_COLUMNS.find(
      column => column.id === "type"
    );
    const { getByText } = render(<div>{statusColumn?.value(alertData)}</div>);

    expect(getByText(alertData.eventStatus)).toBeInTheDocument();
  });

  it('displays correct information in the "Alert Type" column', () => {
    const alertData = {
      subType: "Workflow",
    };
    const typeColumn: any = ALERT_VIEW_GRID_COLUMNS.find(
      column => column.id === "alertType"
    );
    const { getByText } = render(<div>{typeColumn?.value(alertData)}</div>);

    expect(getByText(alertData.subType)).toBeInTheDocument();
  });

  xit('displays correct information in the "action" column', () => {
    const typeColumn: any = ALERT_VIEW_GRID_COLUMNS.find(
      column => column.id === "actions"
    );
    const comp = render(<>{typeColumn?.value({})}</>);
    expect(comp).toBeTruthy();
  });

  it('handles empty event data in the "Event" column', () => {
    const alertData = {
      id: "123",
      planEventIdNbr: "",
      eventName: "",
      taskId: "1",
    };
    const eventColumn: any = ALERT_VIEW_GRID_COLUMNS.find(
      column => column?.id === "event"
    );
    const { getByText } = render(<>{eventColumn?.value(alertData)}</>);

    expect(getByText(`-`)).toBeTruthy();
  });
  it('displays multiple vendors correctly in the "Vendor" column', () => {
    const alertData = {
      simsTaskVendor: [
        { vendorId: "V123", vendorName: "Test Vendor 1" },
        { vendorId: "V456", vendorName: "Test Vendor 2" },
      ],
      taskId: "1",
    };
    const vendorColumn: any = ALERT_VIEW_GRID_COLUMNS.find(
      column => column.id === "vendor"
    );
    const { getByText } = render(<>{vendorColumn.value(alertData)}</>);

    expect(
      getByText("V123 Test Vendor 1, V456 Test Vendor 2")
    ).toBeInTheDocument();
  });
  it('displays tooltip for specific alert types in the "Type" column', () => {
    const alertData = {
      eventStatus: "Approved",
      subType: "DROPPED_ITEM",
      taskId: "1",
    };
    const typeColumn: any = ALERT_VIEW_GRID_COLUMNS.find(
      column => column.id === "type"
    );
    const { getByText } = render(<>{typeColumn.value(alertData)}</>);

    expect(getByText(alertData.eventStatus)).toBeInTheDocument();
    expect(getByText("Approved")).toBeInTheDocument();
  });
});
