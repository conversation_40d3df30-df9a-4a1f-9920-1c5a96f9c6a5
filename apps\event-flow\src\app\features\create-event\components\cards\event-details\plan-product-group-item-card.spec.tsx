import { render, screen } from "@testing-library/react";
import PlanProductGroupItemCard from "./plan-product-group-item-card";
import { FormWrapper } from "@me/util-form-wrapper";
import "@testing-library/jest-dom";

describe("Plan Product Group Test Suite", () => {
  class ResizeObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
  }
  beforeEach(() => {
    window.ResizeObserver = ResizeObserver as any;
  });
  it("Plan Product Group Item Card Component should get render successfully", () => {
    render(
      <FormWrapper>
        <PlanProductGroupItemCard
          promoProductGroup={{
            prodGrpid: "636bde8d9665d0440e006e42",
            productGroupName: "Simple Mills Soft Baked Cookies - 84964",
            productGroupItems: [
              {
                itemDescription: "SIMPLE MILLS COOKIES SOFT BAK CHC CHIP",
                cic: 2014079,
                primaryUpc: "007222080576",
                upcs: ["007222080576"],
                pack: 6,
                size: "6.2 OZ",
              },
            ],
            totalCount: 3,
          }}
          index={0}
        />
      </FormWrapper>
    );
    expect(
      screen.getByText("Simple Mills Soft Baked Cookies - 84964 (1 Items)")
    ).toBeInTheDocument();
    expect(
      screen.getByText("SIMPLE MILLS COOKIES SOFT BAK CHC CHIP")
    ).toBeInTheDocument();
    expect(screen.getByText("2014079")).toBeInTheDocument();
    expect(screen.getByText("0-0-72220-80576")).toBeInTheDocument();
    expect(screen.getByTestId("upcs-span")).toBeInTheDocument();
    expect(screen.getByText("6")).toBeInTheDocument();
    expect(screen.getByText("6.2 OZ")).toBeInTheDocument();
  });
});
