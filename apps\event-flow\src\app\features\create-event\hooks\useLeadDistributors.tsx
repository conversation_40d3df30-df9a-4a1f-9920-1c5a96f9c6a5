import { useSelectorWrap } from "@me/data-rtk";
import { useEffect, useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import {
  checkIfAnyExternalVendorPresent,
  createStepData,
  getFilteredLeads,
  getMainAndOtherVendors,
  getVendorWithDetails,
  isNationalType,
  updatedLeadDistSet,
} from "../../all-allowances/allowance-lead-distributors/billing-selection-utils";
import { isFeatureFlagEnabled } from "../../../../../../../libs/utilities/src/lib/utilities";
import { appConstants } from "@me/utils-root-props";
import {
  setDivisionWiseLeadDistData,
  setIsLeadDistributorError,
} from "../service/slice/lead-distributors-slice";

type Props = {
  leadOptions: string[];
  currentDivId?: string | number;
  isNdpType?: boolean;
};

export default function useLeadDistributors({
  leadOptions = [],
  currentDivId = "",
  isNdpType = false,
}: Props) {
  const dispatch = useDispatch();
  const renderOnMount = useRef(false);
  const prevLeadDistData = useRef<any>(null);

  // 🟢 State Management
  const [selectedList, setSelectedList] = useState<string[]>([]);
  const [leadDistSets, setLeadDistSets] = useState<any[]>([]);
  const [vendorData, setVendorData] = useState<any>({
    otherVendors: [],
    mainVendors: [],
    isAllExternal: false,
  });

  // 🟢 Feature Flags & Utility Variables
  const isMultiVendorFeatureEnabled = isFeatureFlagEnabled(
    appConstants.FEATURE_FLAGS.MULTI_VENDOR
  );
  const isNational = isNationalType(isNdpType);

  // 🟢 Fetch Data from Redux with Safe Destructuring
  const { data: { tableData = [], allDivisionsTableData = [] } = {} } =
    useSelectorWrap("allowanceTableData_rn") ?? {};
  const { data: { leadDistData = [] } = {} } =
    useSelectorWrap("initialLeadDistSelected_rn") ?? {};
  const { data: { leadDistMode = null, allDivisionLeadOptions = {} } = {} } =
    useSelectorWrap("leadDistributors_rn") ?? {};
  const { data: excludedVendorData = {} } =
    useSelectorWrap("excludedVendorForAllowance_rn") ?? {};

  // 🟢 Memoized Data Processing
  const selectedDivisionTableData = useMemo(() => {
    if (!isNational || !currentDivId) return {};
    return (
      allDivisionsTableData.find(
        tableDivisionObj => tableDivisionObj?.divisionId === currentDivId
      ) ?? {}
    );
  }, [isNational, currentDivId, allDivisionsTableData]);

  const excludedVendForDiv = useMemo(() => {
    return (
      excludedVendorData?.divisionWiseExcludedVendors?.[currentDivId] ?? {}
    );
  }, [excludedVendorData, currentDivId]);

  const processedLeadDistData = useMemo(() => {
    return isNational && currentDivId
      ? leadDistData?.[currentDivId] ?? []
      : leadDistData ?? [];
  }, [isNational, currentDivId, leadDistData]);

  // 🟢 Vendor Data Effect (Runs Once Unless Dependencies Change)
  useEffect(() => {
    if (!isMultiVendorFeatureEnabled) return;

    const result = getMainAndOtherVendors(
      isNational && selectedDivisionTableData?.tableData
        ? selectedDivisionTableData?.tableData
        : tableData,
      leadOptions
    ) ?? { otherVendors: [], mainVendors: [], isAllExternal: false };

    setVendorData(result);
  }, [
    isMultiVendorFeatureEnabled,
    isNational,
    JSON.stringify(selectedDivisionTableData?.tableData),
    JSON.stringify(tableData),
    JSON.stringify(leadOptions),
  ]);

  // 🟢 Effect: Set Selected Distributors on Load
  useEffect(() => {
    setSelectedDistOnLoad();
  }, [processedLeadDistData]);

  function setSelectedDistOnLoad() {
    if (processedLeadDistData?.length && leadDistMode) {
      const selectedVendorsIds = processedLeadDistData?.map(ee => ee?.id);
      setSelectedList([...(selectedVendorsIds || [])]);
      renderOnMount.current = true;
    } else if (isMultiVendorFeatureEnabled) {
      defaultSelectIfExternalPresent();
    }
  }

  const checkForExcludedVendor = item => {
    const excludedVendors =
      isNational && currentDivId
        ? excludedVendForDiv?.excludedVendors
        : excludedVendorData?.excludedVendors;
    return !excludedVendors?.includes(item);
  };

  function defaultSelectIfExternalPresent() {
    const vendorWithDetails = getVendorWithDetails(
      isNational ? selectedDivisionTableData?.tableData ?? [] : tableData ?? []
    );
    const isAnyExternalVendor =
      checkIfAnyExternalVendorPresent(vendorWithDetails);
    const validFirstVendor =
      currentDivId &&
      vendorData?.mainVendors?.find(item => checkForExcludedVendor(item));

    if (
      isAnyExternalVendor &&
      vendorData?.mainVendors?.length &&
      validFirstVendor
    ) {
      setSelectedList([validFirstVendor]);
    }
  }

  // 🟢 Effect: Update stepData when selectedList changes
  useEffect(() => {
    !renderOnMount.current && setStepOnSelectedListChanged();
    renderOnMount.current && setChildsOnLoad();
  }, [selectedList]);

  function setChildsOnLoad() {
    if (
      !processedLeadDistData?.length ||
      !leadDistMode ||
      !selectedList?.length
    )
      return;
    const selectedVendorsIds = processedLeadDistData?.map(ee => ee?.id);
    renderOnMount.current = false;
    const updatedChildData = selectedVendorsIds?.map(id => ({
      id,
      child:
        processedLeadDistData?.find(leadData => leadData?.id === id)?.child ??
        [],
    }));
    setLeadDistSets(updatedChildData);
  }

  function setStepOnSelectedListChanged() {
    if (!selectedList?.length) return;

    const filteredLeads = getFilteredLeads(
      leadOptions,
      selectedList,
      isNational && currentDivId ? excludedVendForDiv : excludedVendorData
    );
    const stepData = createStepData(selectedList, filteredLeads);
    setLeadDistSets(stepData ?? []);
  }

  // 🟢 Effect: Dispatch Updated Data
  useEffect(() => {
    if (!isNational || !currentDivId || !allDivisionLeadOptions?.[currentDivId]) return;

    dispatch(
      setDivisionWiseLeadDistData({
        [currentDivId]: {
          selectedList,
          stepData: leadDistSets?.filter(Boolean),
          isExternalVendor:
            isMultiVendorFeatureEnabled && vendorData?.otherVendors?.length > 0,
          isAllExternal: vendorData?.isAllExternal,
        },
      })
    );
  }, [selectedList, leadDistSets]);

  // 🟢 Actions
  function updateSelected({ id }: { id: string }) {
    if (!id) return;
    dispatch(setIsLeadDistributorError({ isLeadDistributorError: false }));
    setSelectedList(prevList =>
      prevList?.includes(id)
        ? prevList?.filter(item => id !== item)
        : [...prevList, id]
    );
  }

  function includeToVendor({
    id,
    direction,
  }: {
    id: string;
    direction: string;
  }) {
    if (!id || !direction || !selectedList?.length || !leadDistSets?.length)
      return;
    const updatedListSet = updatedLeadDistSet(leadDistSets, id, direction);
    updatedListSet && setLeadDistSets(updatedListSet);
  }

  return {
    selectedList,
    stepData: leadDistSets?.filter(item => !!item),
    updateSelected,
    includeToVendor,
    ...vendorData,
    isExternalVendor:
      isMultiVendorFeatureEnabled && vendorData?.otherVendors?.length > 0,
  };
}
