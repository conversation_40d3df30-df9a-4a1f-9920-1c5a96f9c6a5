import { FunctionComponent } from "react";
import DatePicker from "@albertsons/uds/molecule/DatePicker";
import { FormFieldError } from "@me/util-form-wrapper";
import { Control, useController, UseFormRegister } from "react-hook-form";
import { DateFormat, dateFormat } from "@me/util-helpers";
import classNames from "classnames";
import { getObjectValueFromString } from "@me-upp-js/utilities";
import './date-picker.scss';

interface IFieldProps {
  registerField: string;
  label: string;
  required: boolean;
  errors: object;
  addAsterisk?: boolean;
  isAllowFlow?: boolean;
  isUDSEnabled?: boolean;
}

interface CustomValidationProps {
  value: boolean;
  message: string;
}

interface IInputDatePickerProps {
  fieldProps: IFieldProps;
  register: UseFormRegister<any> | (() => void);
  control: Control<any, any> | undefined;
  key?: string | number;
  minDate?: string | Date | undefined;
  maxDate?: string | Date | undefined;
  disabled?: boolean;
  onChange?: (value: string) => void;
  customValidation?: CustomValidationProps;
  isCustomValidationEnabled?: boolean;
  showLabel?: boolean;
  isAccordian?: boolean;
}

const DatePickerAtom: FunctionComponent<IInputDatePickerProps> = ({
  fieldProps,
  register,
  control,
  key = 0,
  minDate,
  maxDate,
  disabled,
  onChange,
  customValidation = { value: true, message: "" },
  isCustomValidationEnabled = false,
  showLabel = true,
  isAccordian = false,
}) => {
  const {
    registerField,
    label,
    required,
    errors: errorMessages,
    addAsterisk = false,
    isAllowFlow = false,
    isUDSEnabled = false,
  } = fieldProps;
  const { field, formState } = useController({ name: registerField, control });
  const fieldErrorData = getObjectValueFromString(
    formState.errors,
    registerField
  );

  const handleDateChange = ([e]: Date[]) => {
    const formatedDate = dateFormat(e, DateFormat["YYYY-MM-DD"]);
    if (formatedDate !== "NaN-0NaN-0NaN") {
      onChange?.(formatedDate);
      field.onChange(formatedDate);
    }
  };

  const getDefaultValue = () => {
    if (field?.value) {
      const [year, month, day] =
        ((field?.value as string)?.split("-")?.map(Number) as number[]) || [];
      return [new Date(year, month - 1, day)] as Date[];
    }
    return undefined;
  };

  return (
    <div className="w-full" id="abs-input-date-picker-uds-main">
      {showLabel && (
        <div
          className={`flex gap-1 ${isAccordian ? "" : "font-bold"}`}
          id="abs-input-date-picker-uds-lable-d"
        >
          <p
            id="abs-input-date-picker-uds-label"
            className={isAccordian ? "pb-6" : ""}
          >
            {label}
          </p>
          {required || addAsterisk ? (
            <p className="text-sm text-left text-[#bf2912]">*</p>
          ) : null}
        </div>
      )}
      <div className="w-full" id="abs-input-date-picker-uds-controler">
        <div id="abs-input-date-picker-uds-error" className={"inputDatePicker"}>
          <DatePicker
            id={`abs-input-date-picker-uds-datepicker-${key}`}
            {...register?.(registerField, {
              required: {
                value: required,
                message: errorMessages?.["required"]?.text,
              },
              validate: value => {
                return (
                  !isCustomValidationEnabled ||
                  (value !== undefined && customValidation?.value) ||
                  customValidation?.message
                );
              },
            })}
            key={`${registerField}-${key}`}
            className={classNames("render", {
              "pointer-events-none !bg-[#EDEEEF]":
                disabled || (!isAllowFlow && !isUDSEnabled),
              "!w-full !min-w-[100px]": isAllowFlow,
              "date-no-min": true,
              "rounded border-[#BF2912] border-error border-2 w-full":
                fieldErrorData?.message,
            })}
            value={getDefaultValue()}
            onChange={handleDateChange}
            minDate={minDate}
            maxDate={maxDate}
            disabled={disabled}
            dateFormat="MM/DD/YY"
            placeholder="Select"
            configuration={
                         { firstDay: 0}
                        }
          />
        </div>
        {fieldErrorData?.message && <FormFieldError error={fieldErrorData} />}
      </div>
    </div>
  );
};

export default DatePickerAtom;
