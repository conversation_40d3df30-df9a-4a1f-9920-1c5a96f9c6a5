import { useSelectorWrap } from "@me/data-rtk";
import { InputSelect } from "@me/input-fields";
import React, { memo } from "react";
import { useFormContext } from "react-hook-form";
import { validateNCDP } from "../../event-details-card-service";

function YearField({
  year,
  formFields,
  setFormFields,
  setVehicleTypeFieldChanged,
}) {
  const { setValue, getValues } = useFormContext();
  const isCustomDate = ["Custom Date", "CustomDate"].includes(
    formFields?.vehicleTypeProps
  );

  const { data: eventTypeAndDivisionsData } = useSelectorWrap(
    "event_type_and_divisions"
  );
  const eventType = eventTypeAndDivisionsData?.eventType || "";

  const onYearChangeHandler = ele => {
    const year = ele?.name?.toString();
    setValue("eventCreationVehicle.year", year);
    setFormFields(prevState => {
      return {
        ...prevState,
        vehicleTypeId: getValues("eventCreationVehicle.vehicleType.id"),
        yearVal: year,
      };
    });
    setVehicleTypeFieldChanged(true);
  };
  return (
    <div className=" ">
      {year ? (
        <InputSelect
          onChange={onYearChangeHandler}
          fieldProps={year}
          disabled={validateNCDP(eventType) || isCustomDate}
          defaultValue={formFields?.yearVal}
        ></InputSelect>
      ) : null}
    </div>
  );
}

export default memo(YearField);
