import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import Disabled<PERSON>ield from './disabled-field';

describe('DisabledField Component', () => {
  const defaultProps = {
    label: 'Test Label',
    required: true,
    value: 'Sample Value',
    isSelect: false,
    prefix: '$',
    baseId: 'test-id',
  };

  test('renders label and required indicator', () => {
    render(<DisabledField {...defaultProps} />);

    // Verify label is rendered
    const labelElement = screen.getByText('Test Label');
    expect(labelElement).toBeInTheDocument();

    // Verify required indicator is rendered
    const requiredIndicator = screen.getByText('*');
    expect(requiredIndicator).toBeInTheDocument();
    expect(requiredIndicator).toHaveClass('text-sm text-left text-[#bf2912]');
  });

  test('renders value with prefix', () => {
    render(<DisabledField {...defaultProps} />);

    // Verify value is rendered with prefix
    const valueElement = screen.getByText('$Sample Value');
    expect(valueElement).toBeInTheDocument();
    expect(valueElement).toHaveClass('text-base font-bold text-left mt-1');
  });

  test('renders ChevronDown icon when isSelect is true', () => {
    const { container } = render(<DisabledField {...defaultProps} isSelect={true} />);

    // Verify ChevronDown icon is rendered
    const svgEl = container.querySelector(".lucide-chevron-down") as HTMLImageElement;
    expect(svgEl).toBeInTheDocument();
  });

  test('does not render ChevronDown icon when isSelect is false', () => {
    const {container} = render(<DisabledField {...defaultProps} isSelect={false} />);

    // Verify ChevronDown icon is not rendered
    const svgEl = container.querySelector(".lucide-chevron-down") as HTMLImageElement;
    // const chevronIcon = screen.queryby('lucide-chevron-down');
    expect(svgEl).not.toBeInTheDocument();
  });

  test('applies correct classes to value container', () => {
    render(<DisabledField {...defaultProps} />);

    // Verify value container has correct classes
    const valueContainer = screen.getByText('$Sample Value').closest('div');
    expect(valueContainer).toHaveClass('flex rounded bg-gray-205 h-[40px] text-disabled-text border border-[#a5a7ab] p-2 cursor-not-allowed items-center justify-between');
  });
});
