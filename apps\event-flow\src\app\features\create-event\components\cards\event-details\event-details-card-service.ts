import { EEVENT_STATUS, EUSER_ROLES, NEW_PPG_CHANGES } from "@me/util-helpers";
import { EOWN_BRAND_USER } from "../../../../event-types/components/event-action/event-action.model";
import { getIsNationalEvent } from "../../../../event-types/event-types-helper";
import { EVENT_TYPE } from "../../../constants/constants";

export const getIsMultiVendorEvent = (data, userRole) => {
  const multiVendorOrEevntCanCreate = {
    isMultiVendorEvent: false,
    canCreateEvent: true,
  };

  if (!NEW_PPG_CHANGES?.isMultiVendorPPGEnable) {
    return multiVendorOrEevntCanCreate;
  }

  if (!data?.length || !userRole) return multiVendorOrEevntCanCreate;

  const loggedInUserVendorIDs = JSON.parse(
    localStorage.getItem("vendorNum") || "[]"
  );
  const filterData = data?.map(item => {
    //filter 00011 and 00013 and let the user create events for these dummy vendors
    const filteredVendors = item?.simsVendors?.filter(
      vendorId => vendorId !== "000011" && vendorId !== "000013"
    );
    return { ...item, simsVendors: filteredVendors };
  });
  const multiVendorPPGs = filterData?.find(promoProductGroup => {
    return promoProductGroup?.simsVendors?.length > 1;
  });
  const simsVendorsList = filterData
    ?.map(data => {
      return data?.simsVendors;
    })
    ?.flat();
  const manufacturerSimsVendors = filterData
    ?.filter(ppg =>
      Object.prototype.hasOwnProperty.call(ppg, "manufacturerSimsVendors")
    )
    .map(data => {
      return data?.manufacturerSimsVendors;
    })
    ?.flat();
  const simsCheck = simsVendorsList?.every(item =>
    loggedInUserVendorIDs?.includes(item)
  );
  const mfgSimsCheck = manufacturerSimsVendors?.every(item =>
    loggedInUserVendorIDs.includes(item)
  );
  if (userRole === EUSER_ROLES.VENDOR) {
    const simsVendorsListSet = new Set(simsVendorsList.map(item => item));
    return {
      canCreateEvent:
        simsCheck ||
        (mfgSimsCheck &&
          manufacturerSimsVendors?.length &&
          simsVendorsList?.length === 1),
      isMultiVendorEvent: simsVendorsListSet.size > 1,
    };
  } else {
    if (!multiVendorPPGs) {
      const simsVendorsSet = new Set(data?.map(item => item?.simsVendors?.[0]));
      return {
        ...multiVendorOrEevntCanCreate,
        isMultiVendorEvent: simsVendorsSet.size > 1,
      };
    } else
      return {
        ...multiVendorOrEevntCanCreate,
        isMultiVendorEvent: true,
      };
  }
};

export const excludeOwnBrandsFromPPGs = ppgData => {
  const ownBrandPPGs = ppgData?.filter(
    ppg =>
      ppg?.negotiationSimsVendors?.length &&
      ppg?.negotiationSimsVendors?.includes(EOWN_BRAND_USER.ID) &&
      ppg?.simsVendors?.length &&
      !ppg?.simsVendors?.includes(EOWN_BRAND_USER.ID)
  );
  if (ownBrandPPGs?.length) {
    const OwnBrandsExcludedPPGs = ppgData?.filter(
      ppg => !ownBrandPPGs?.some(ownBrand => ppg?.id === ownBrand?.id)
    );
    return OwnBrandsExcludedPPGs;
  } else return ppgData;
};

const isRogAvailable = (obj, key, max) => {
  return Object.prototype.hasOwnProperty.call(obj, key) && obj[key] === max;
};

export const findMaxValue = (rogUnitTypes, upcMapperObj) => {
  const maxValueArr: number[] = Object.values(rogUnitTypes);
  const maxValue = Math.max(...maxValueArr);
  switch (true) {
    case isRogAvailable(rogUnitTypes, 1, maxValue):
      return {
        ...upcMapperObj[1],
        upcType: 1,
      };
    case isRogAvailable(rogUnitTypes, 2, maxValue):
      return {
        ...upcMapperObj[2],
        upcType: 2,
      };
    case isRogAvailable(rogUnitTypes, 3, maxValue):
      return {
        ...upcMapperObj[3],
        upcType: 3,
      };

    default:
      break;
  }
};

export const getTodaysDate = () => {
  return new Date().toJSON().slice(0, 10);
};
export const checkIsLastOfferOrPromotion = eventDetailsData => {
  const excludeStatuses = [EEVENT_STATUS?.REJECTED, EEVENT_STATUS?.CANCELED];
  const { offerAllowances = [], promotionsLists = [] } = eventDetailsData;
  const { isNDPAndFlagEnabled: isNational } = getIsNationalEvent(eventDetailsData?.eventType);
  const offerAllowancesStatusList = () => {
    return offerAllowances?.filter(offer =>
      isNational
        ? offer?.allowances?.some(
          allow => !excludeStatuses.includes(allow?.allowanceStatus)
        )
        : !excludeStatuses.includes(offer?.allowances[0]?.allowanceStatus)
    );
  };
  const promoStatusList = () => {
    const countPromotionCardStatus = promotionsLists[0]?.promotionsList;
    return countPromotionCardStatus?.filter(
      promotion => !excludeStatuses.includes(promotion.promotionWorkflowStatus)
    );
  };
  const filteredAllowData = offerAllowancesStatusList() || [];
  const filteredPormoData = promoStatusList() || [];
  return filteredAllowData.length + filteredPormoData.length === 1;
};

export const buildDivErrStr = (storeGroups, invalidDivIds) => {
  return storeGroups
    .filter(group =>
      group.divisionIds?.some(divId => invalidDivIds?.includes(divId))
    )
    .sort((a, b) => a.divisionIds[0] - b.divisionIds[0])
    .map(group => `${group.divisionIds[0]} - ${group.label.split(" ")[0]}`)
    .join(", ");
};

export const getUniqueInvalidDivisionIds = plannedProductGroups => {
  const isInvalidDivisionIds = plannedProductGroups?.filter(ppg => {
    return ppg?.plannedEventItems?.invalidDivisionIds;
  });
  if (!isInvalidDivisionIds?.length) return [];
  return [
    ...new Set(
      plannedProductGroups?.flatMap(
        item => item?.plannedEventItems?.invalidDivisionIds
      )
    ),
  ];
};
export const validateNCDP = (eventType) => eventType === EVENT_TYPE.NCDP;
