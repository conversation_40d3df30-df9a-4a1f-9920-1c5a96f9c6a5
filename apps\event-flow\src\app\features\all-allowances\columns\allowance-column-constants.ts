import { renderValue } from "../../create-event/components/cards/event-details/view-event-items-card-container";

const COMMON_COLS = [
  {
    id: "itemDescription",
    label: "Item Description",
    commonValueFunc: false,
    valueFunc: "getItemDesc",
    minWidth: "240px",
    sortable: true,
    sortableFunc: "sortByAlphabet",
    sortByKey: "itemDescription",
    align: "left",
    className: "itemDesc_sy",
  },
  {
    id: "cic",
    label: "CIC",
    value: renderValue?.bind(this, "itemId"),
    sortableFunc: "baseSortFunc",
    sortByKey: "itemId",
  },
  {
    id: "primaryUpc",
    label: "Primary UPC",
    value: renderValue?.bind(this, "primaryUpc"),
    align: "center",
    width: "150px",
    sortableFunc: "sort_RootKeys",
    sortByKey: "primaryUpc",
  },

  {
    id: "itemUpcs",
    label: "UPCs",
    valueFunc: "RenderUpcs",
    align: "center",
    width: "50px",
    sortableFunc: "sortByUpcsLength",
    sortByKey: "itemUpcs",
  },
  {
    id: "packWhse",
    label: "Pack",
    valueFunc: "showPckWhse",
    sortableFunc: "baseSortFunc",
    sortByKey: "packWhse",
    width: "50px",
    align: "center",
  },
  {
    id: "size",
    label: "Size",
    valueFunc: "renderSize",
    sortable: true,
    align: "center",
    sortableFunc: "sortSize",
    sortByKey: "size",
  },
];

export const S2S_DSD_WHSE_COLUMNS = [
  {
    id: "itemDescription",
    label: "Item Description",
    sortableFunc: "sortByAlphabet",
    sortByKey: "itemDescription",
    commonValueFunc: false,
    minWidth: "240px",
    valueFunc: "getItemDesc",
    sortable: true,
    align: "left",
    className: "itemDesc_sy",
  },
  {
    id: "cic",
    label: "CIC",
    value: renderValue?.bind(this, "itemId"),
    sortableFunc: "baseSortFunc",
    sortByKey: "itemId",
  },
  {
    id: "primaryUpc",
    label: "Primary UPC",
    value: renderValue?.bind(this, "primaryUpc"),
    align: "left",
    sortableFunc: "sort_RootKeys",
    sortByKey: "primaryUpc",
  },

  {
    id: "itemUpcs",
    label: "UPCs",
    valueFunc: "RenderUpcs",
    align: "left",
    sortableFunc: "sortByUpcsLength",
    sortByKey: "itemUpcs",
  },
  {
    id: "packWhse",
    label: "Pack",
    sortableFunc: "baseSortFunc",
    sortByKey: "packWhse",
    valueFunc: "showPckWhse",
    sortable: true,
    width: "50px",
    align: "left",
  },
  {
    id: "size",
    label: "Size",
    value: renderValue?.bind(this, "size"),
    align: "left",
    sortableFunc: "sortSize",
    sortByKey: "size",
  },
];

export const S2S_MULTI_DSD_COLUMNS = [
  {
    id: "itemDescription",
    labelFunc: "renderDescColumnHeader",
    sortableFunc: "sortByAlphabet",
    sortByKey: "itemDescription",
    commonValueFunc: false,
    minWidth: "240px",
    valueFunc: "getItemDesc",
    sortable: true,
    align: "left",
    className: "",
    pinned: false,
  },
  {
    id: "cic",
    label: "CIC",
    value: renderValue?.bind(this, "itemId"),
    sortableFunc: "baseSortFunc",
    sortByKey: "itemId",
  },
  {
    id: "primaryUpc",
    label: "Primary UPC",
    value: renderValue?.bind(this, "primaryUpc"),
    align: "left",
    sortableFunc: "sort_RootKeys",
    sortByKey: "primaryUpc",
  },

  {
    id: "itemUpcs",
    label: "UPCs",
    valueFunc: "RenderUpcs",
    align: "left",
    sortableFunc: "sortByUpcsLength",
    sortByKey: "itemUpcs",
  },
  {
    id: "packWhse",
    label: "Pack",
    sortableFunc: "baseSortFunc",
    sortByKey: "packWhse",
    valueFunc: "showPckWhse",
    sortable: true,
    width: "50px",
    align: "left",
  },
  {
    id: "size",
    label: "Size",
    valueFunc: "renderSize",
    sortable: true,
    align: "center",
    sortableFunc: "sortSize",
    sortByKey: "size",
  },
];

export const ITEM_FLAT_DSD_WHSE_COLUMNS = [
  {
    id: "itemDescription",
    label: "Item Description",
    sortableFunc: "sortByAlphabet",
    sortByKey: "itemDescription",
    commonValueFunc: false,
    minWidth: "240px",
    valueFunc: "getItemDesc",
    sortable: true,
    align: "left",
  },
  {
    id: "cic",
    label: "CIC",
    value: renderValue?.bind(this, "itemId"),
    sortableFunc: "baseSortFunc",
    sortByKey: "itemId",
  },
  {
    id: "primaryUpc",
    label: "Primary UPC",
    value: renderValue?.bind(this, "primaryUpc"),
    align: "left",
    sortableFunc: "sort_RootKeys",
    sortByKey: "primaryUpc",
  },

  {
    id: "itemUpcs",
    label: "UPCs",
    valueFunc: "RenderUpcs",
    align: "left",
    sortableFunc: "sortByUpcsLength",
    sortByKey: "itemUpcs",
  },
  {
    id: "packWhse",
    label: "Pack",
    value: renderValue?.bind(this, "packWhse"),
    sortableFunc: "baseSortFunc",
    sortByKey: "packWhse",
    align: "left",
  },
  {
    id: "size",
    label: "Size",
    value: renderValue?.bind(this, "size"),
    align: "left",
    sortableFunc: "sortSize",
    sortByKey: "size",
  },
  {
    id: "caseListCost",
    label: "Master Case Cost",
    commonLabelFunc: true,
    labelFunc: "getCommonLabelForColumn",
    commonValueFunc: true,
    valueKey: "caseListCost",
    valueFunc: "getListCostBasedOnVendor",
    sortableFunc: "sortVendorItems",
    sortByKey: "caseListCost",
    className: "py-1 breakLines_sy",
    align: "left",
  },
  {
    id: "allowanceAmt",
    label: "Allowance Amount",
    valueFunc: "renderAmountFields",
    sortable: false,
    align: "left",
  },
];

export const S2S_DSD_WHSE_RETAIL_DIVISION_CASE_VENDOR_COLS = [
  {
    id: "caseListCost",
    label: "Shipping Case Cost",
    sortableFunc: "sortVendorItems", // if you want to sort use this key and give your specifc function name
    sortByKey: "caseListCost", // key with which you want to sort
    commonValueFunc: true,
    valueFunc: "getListCostBasedOnVendor",
    valueKey: "caseListCost",
    className: "py-1 breakLines_sy",
    width: "50px",
    align: "left",
  },

  {
    id: "caseCostAllow",
    label: "Case Cost w/Allow",
    commonLabelFunc: true,
    labelFunc: "getCommonLabelForColumn",
    valueFunc: "getCaseCost_Allowance",
    width: "40px",
    sortableFunc: "sortVendorItems",
    sortByKey: "caseCostAllow",
  },
  {
    id: "allowanceAmt",
    label: "Allowance Amount",
    valueFunc: "renderAmountFields",
    sortable: false,
    align: "left",
  },
  {
    id: "newCaseListCostW_Allowance",
    label: "New Case Cost w/Allow",
    commonLabelFunc: true,
    labelFunc: "getCommonLabelForColumn",
    commonValueFunc: true,
    valueKey: "newCaseCostAllow",
    valueFunc: "getListCostBasedOnVendor",
    width: "150px",
    className: "itemDesc_sy",
    sortableFunc: "sortVendorItems",
    sortByKey: "newCaseCostAllow",
  },
];

export const S2S_DSD_WHSE_RETAIL_DIVISION_UNIT_VENDOR_COLS = [
  {
    id: "unitListCost",
    label: "Unit List Cost",
    sortableFunc: "sortVendorItems", // if you want to sort use this key and give your specifc function name
    sortByKey: "unitListCost", // key with which you want to sort
    commonValueFunc: true,
    commonLabelFunc: true,
    labelFunc: "getCommonLabelForColumn",
    valueFunc: "getListCostBasedOnVendor",
    valueKey: "unitListCost",
    className: "",
    width: "50px",
    align: "left",
  },

  {
    id: "unitCostAllow",
    label: "Unit Cost w/Allow",
    commonLabelFunc: true,
    labelFunc: "getCommonLabelForColumn",
    valueFunc: "getUnitCost_Allowance",
    width: "40px",
    sortableFunc: "sortVendorItems",
    sortByKey: "unitCostAllow",
  },
  {
    id: "allowanceAmt",
    label: "Allowance Amount",
    valueFunc: "renderAmountFields",
    sortable: false,
    align: "left",
  },
  {
    id: "newUnitListCostW_Allowance",
    label: "New Unit Cost w/Allow",
    commonLabelFunc: true,
    labelFunc: "getCommonLabelForColumn",
    commonValueFunc: true,
    valueKey: "newUnitCostAllow",
    valueFunc: "getListCostBasedOnVendor",
    width: "150px",
    sortableFunc: "sortVendorItems",
    sortByKey: "newUnitCostAllow",
  },
];
export const ALL_SUMMARY_VENDOR_COLS_COMMON = [
  {
    id: "allowanceAmt",
    label: "Allowance Amount",
    valueFunc: "renderAmountFields",
    sortable: false,
    align: "left",
  }
]
export const ALL_SUMMARY_VENDOR_COLS_CASE = [
  {
    id: "caseAmt",
    align: "left",
    labelFunc: "renderAmountColumnHeader",
    label: null,
    valueFunc: "renderAmountFieldsDSD",
  },
]
export const ALL_SUMMARY_VENDOR_COLS_IF = [
  {
    id: "itemFlatAmt",
    align: "left",
    labelFunc: "renderAmountColumnHeader",
    label: null,
    valueFunc: "renderAmountFieldsDSD",
  }
]

export const ALL_SUMMARY_ITEM_FLAT_DSD_WHSE_COLUMNS = [
  {
    id: "itemDescription",
    label: "Item Description",
    sortableFunc: "sortByAlphabet",
    sortByKey: "itemDescription",
    commonValueFunc: false,
    minWidth: "240px",
    valueFunc: "getItemDesc",
    sortable: true,
    align: "left",
  },
  {
    id: "cic",
    label: "CIC",
    value: renderValue?.bind(this, "itemId"),
    sortableFunc: "baseSortFunc",
    sortByKey: "itemId",
  },
  {
    id: "primaryUpc",
    label: "Primary UPC",
    value: renderValue?.bind(this, "primaryUpc"),
    align: "left",
    sortableFunc: "sort_RootKeys",
    sortByKey: "primaryUpc",
  },

  {
    id: "itemUpcs",
    label: "UPCs",
    valueFunc: "RenderUpcs",
    align: "left",
    sortableFunc: "sortByUpcsLength",
    sortByKey: "itemUpcs",
  },
  {
    id: "packWhse",
    label: "Pack",
    value: renderValue?.bind(this, "packWhse"),
    sortableFunc: "baseSortFunc",
    sortByKey: "packWhse",
    align: "left",
  },
  {
    id: "size",
    label: "Size",
    value: renderValue?.bind(this, "size"),
    align: "left",
    sortableFunc: "sortSize",
    sortByKey: "size",
  },
  {
    id: "allowanceAmt",
    label: "Allowance Amount",
    valueFunc: "renderAmountFields",
    sortable: false,
    align: "left",
  },
];
export const S2S_DSD_LEAD_DIST_CASE_VENDOR_COLS = [
  {
    id: "caseListCost",
    label: "Shipping Case Cost",
    commonLabelFunc: true,
    labelFunc: "getCommonLabelForColumn",
    sortableFunc: "sortVendorItems", // if you want to sort use this key and give your specifc function name
    sortByKey: "caseListCost", // key with which you want to sort
    commonValueFunc: true,
    valueFunc: "getListCostBasedOnVendor",
    valueKey: "caseListCost",
    className: "",
    width: "50px",
    align: "left",
  },

  {
    id: "caseCostAllow",
    label: "Case Cost w/Allow",
    commonLabelFunc: true,
    labelFunc: "getCommonLabelForColumn",
    valueFunc: "getCaseCost_Allowance",
    width: "40px",
    sortableFunc: "sortVendorItems",
    sortByKey: "caseCostAllow",
  },
  {
    id: "caseAmt",
    align: "left",
    labelFunc: "renderAmountColumnHeader",
    label: null,
    valueFunc: "renderAmountFieldsDSD",
    width: "110px",
  },
  {
    id: "newCaseListCostW_Allowance",
    label: "New Case Cost w/Allow",
    commonLabelFunc: true,
    labelFunc: "getCommonLabelForColumn",
    commonValueFunc: true,
    valueKey: "newCaseCostAllow",
    valueFunc: "getListCostBasedOnVendor",
    width: "150px",
    sortableFunc: "sortVendorItems",
    sortByKey: "newCaseCostAllow",
  },
];

export const S2S_DSD_LEAD_DIST_UNIT_VENDOR_COLS = [
  {
    id: "unitListCost",
    label: "Unit List Cost",
    sortableFunc: "sortVendorItems", // if you want to sort use this key and give your specifc function name
    sortByKey: "unitListCost", // key with which you want to sort
    commonValueFunc: true,
    commonLabelFunc: true,
    labelFunc: "getCommonLabelForColumn",
    valueFunc: "getListCostBasedOnVendor",
    valueKey: "unitListCost",
    className: "",
    width: "50px",
    align: "left",
  },

  {
    id: "unitCostAllow",
    label: "Unit Cost w/Allow",
    commonLabelFunc: true,
    labelFunc: "getCommonLabelForColumn",
    valueFunc: "getUnitCost_Allowance",
    width: "40px",
    sortableFunc: "sortVendorItems",
    sortByKey: "unitCostAllow",
  },
  {
    id: "unitAmt",
    align: "left",
    labelFunc: "renderAmountColumnHeader",
    label: null,
    valueFunc: "renderAmountFieldsDSD",
    width: "110px",
  },
  {
    id: "newUnitListCostW_Allowance",
    label: "New Unit Cost w/Allow",
    commonLabelFunc: true,
    labelFunc: "getCommonLabelForColumn",
    commonValueFunc: true,
    valueKey: "newUnitCostAllow",
    valueFunc: "getListCostBasedOnVendor",
    width: "150px",
    sortableFunc: "sortVendorItems",
    sortByKey: "newUnitCostAllow",
  },
];

export const CASE_WAREHSE_CASE_VENDOR_COLS = [
  {
    id: "caseListCost",
    label: "Master Case Cost",
    commonLabelFunc: true,
    labelFunc: "getCommonLabelForColumn",
    commonValueFunc: true,
    valueKey: "caseListCost",
    valueFunc: "getListCostBasedOnVendor",
    width: "50px",
    sortableFunc: "sortVendorItems",
    sortByKey: "caseListCost",
  },
  {
    id: "caseCostAllow",
    label: "Case Cost w/Allow",
    commonLabelFunc: true,
    labelFunc: "getCommonLabelForColumn",
    valueFunc: "getCaseCost_Allowance",
    width: "50px",
    sortableFunc: "sortVendorItems",
    sortByKey: "caseCostAllow",
  },
  {
    id: "caseAmt",
    align: "left",
    labelFunc: "renderAmountColumnHeader",
    label: null,
    valueFunc: "renderAmountFieldsDSD",
  },
  {
    id: "newCaseListCostW_Allowance",
    label: "New Case Cost w/Allow",
    commonLabelFunc: true,
    labelFunc: "getCommonLabelForColumn",
    commonValueFunc: true,
    valueKey: "newCaseCostAllow",
    valueFunc: "getListCostBasedOnVendor",
    width: "150px",
    sortableFunc: "sortVendorItems",
    sortByKey: "newCaseCostAllow",
  },
];
export const ITEM_FLAT_WAREHSE_VENDOR_COLS = [
  {
    id: "caseListCost",
    label: "Master Case Cost",
    commonLabelFunc: true,
    labelFunc: "getCommonLabelForColumn",
    commonValueFunc: true,
    valueKey: "caseListCost",
    valueFunc: "getCommonValueTextByKey",
    width: "50px",
    sortableFunc: "sortVendorItems",
    sortByKey: "caseListCost",
  },
  {
    id: "itemFlatAmt",
    align: "left",
    labelFunc: "renderAmountColumnHeader",
    label: null,
    valueFunc: "renderAmountFieldsDSD",
  },
];
export const ITEM_FLAT_WAREHSE_MAIN_COLS = [...COMMON_COLS];

export const CASE_WAREHSE_UNIT_VENDOR_COLS = [
  {
    id: "unitListCost",
    label: "Unit List Cost",
    sortable: true,
    commonLabelFunc: true,
    labelFunc: "getCommonLabelForColumn",
    commonValueFunc: true,
    valueKey: "unitListCost",
    valueFunc: "getListCostBasedOnVendor",
    sortableFunc: "sortVendorItems",
    sortByKey: "unitListCost",
  },
  {
    id: "unitCostAllow",
    label: "Unit Cost w/Allow",
    sortable: true,
    commonLabelFunc: true,
    labelFunc: "getCommonLabelForColumn",
    valueFunc: "getUnitCost_Allowance",
    sortableFunc: "sortVendorItems",
    sortByKey: "unitCostAllow",
  },
  {
    id: "unitAmt",
    align: "left",
    labelFunc: "renderAmountColumnHeader",
    label: null,
    valueFunc: "renderAmountFieldsDSD",
    width: "110px",
  },
  {
    id: "newUnitListCostW_Allowance",
    label: "New Unit Cost w/Allow",
    sortable: true,
    commonLabelFunc: true,
    labelFunc: "getCommonLabelForColumn",
    commonValueFunc: true,
    valueKey: "newUnitCostAllow",
    valueFunc: "getListCostBasedOnVendor",
    sortableFunc: "sortVendorItems",
    width: "150px",
    sortByKey: "newUnitCostAllow",
  },
];

export const HF_NON_GROUP_COLUMNS = [...COMMON_COLS];

export const HF_VENDORS_COLUMNS = [
  {
    id: "caseListCost",
    label: "Master Case Cost",
    commonLabelFunc: true,
    labelFunc: "getCommonLabelForColumn",
    commonValueFunc: true,
    valueKey: "caseListCost",
    valueFunc: "getCommonValueTextByKey",
    width: "50px",
    sortableFunc: "sortVendorItems",
    sortByKey: "caseListCost",
  },
  {
    id: "amt",
    label: "Allowance Amount",
    align: "left",
    width: "100px",
    labelFunc: "renderAmountColumnHeader",
    valueFunc: "noRender",
  },
];

export const CASE_WAREHSE_MAIN_COLUMNS = [...COMMON_COLS];
