import { render, screen } from "@testing-library/react";
import { renderTestWithProviders } from "apps/event-flow/main-test-utils";
import EventDetailsCardHeader from "./event-details-card-header";
import * as rtk_tools from "@me/data-rtk";
import { EventDetailsDataMock } from "./event-details-mocks";
import { setPermissionToLocalStorage } from "../../../../../../mocks/user-permissions-mock";
setPermissionToLocalStorage();
describe("Event Details Card Header Test Suite", () => {
  beforeEach(() => {
    jest
      .spyOn(rtk_tools, "useSelectorWrap")
      .mockReturnValue({ data: EventDetailsDataMock });
  });

  it("should render event details card header component", () => {
    const { baseElement } = renderTestWithProviders(
      <EventDetailsCardHeader editEventOnChange={jest.fn()} />
    );
    expect(baseElement).toBeTruthy();
  });

  xit("should contain correct fields in event details card header component", () => {
    renderTestWithProviders(
      <EventDetailsCardHeader editEventOnChange={jest.fn()} />
    );
    expect(screen.queryByText("Edit Event Details")).toBeTruthy();
  });
});
