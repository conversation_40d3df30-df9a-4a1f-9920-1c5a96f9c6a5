import { useState, useEffect, useMemo } from "react";
import { isEmpty } from "lodash";
import {
  getItemCount,
  getTotalAmountSum,
  getWarehouseCount,
  isAllowanceAmountCommon,
} from "../../../allowance/stepper/common-stepper/allowance-amount/allowance-amounts-services";
import {
  formatAmount,
  getUOMBasedOnType,
  getUOMFromNameOrValue,
  roundOfNumber,
} from "../../../../../service/allowance/allowance-service";
import useAllowanceAmountValidation, {
  validateAmount,
} from "../../../../../hooks/allowance-amount-validations";
import { EEVENT_STATUS } from "@me/util-helpers";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";
import { isHfOrIfType } from "../../../../../service/allowance/allowance-service";
import { IUseAllowanceAmountProps } from "../../offer-sections/amounts/shared/props-types";

export const useHandleAmountFieldsChange = ({
  allowancesResp,
  allowanceType,
  formControls,
  initialValues,
  setAllowanceAmountData,
  allowanceAmountFields,
  setAllowanceAmountFields,
  isHeaderFlat = false,
  isHfIfWhseCase = false,
  cardIndex = 0,
  cardItemIndex = 0,
  searchId,
  eventDetailsData,
  fieldProp,
  isAmtSavedInTemp,
  isEditEnable
}: IUseAllowanceAmountProps) => {
  const [itemCount, setItemCount] = useState(0);
  const [totalAmount, setTotalAmount] = useState(0);
  const [wareHouseCount, setWareHouseCount] = useState(0);
  const [digitError, setDigitError] = useState("");
  const [differentAmountFound, setDifferentAmountFound] = useState(false);
  const { setValue } = formControls || {};

  const allowanceStatus =
    eventDetailsData?.offerAllowances?.[cardIndex]?.allowances?.[cardItemIndex]
      ?.allowanceStatus;
  const eventStatus = eventDetailsData?.planEvent?.eventStatus;

  const { SWITCH_OPTIONS, UOM_OPTIONS } = efConstants;
  const isHfIfType = useMemo(
    () => isHfOrIfType(allowanceType || ""),
    [allowanceType]
  );

  // Memoized values
  const defaultToggleValue = useMemo(
    () => SWITCH_OPTIONS?.[allowanceType]?.defaultToggleValue || "",
    [allowanceType]
  );

  const [switchEnabled, setSwitchEnabled] = useState(
    defaultToggleValue === "Unit"
  );
  const switchValue = switchEnabled ? "Unit" : "Case";
  const areInputsDisabled = useMemo(
    () => defaultToggleValue !== switchValue,
    [defaultToggleValue, switchValue]
  );

  const switchObj = useMemo(
    () => SWITCH_OPTIONS?.[allowanceType]?.dataMapping?.[switchValue],
    [allowanceType, switchValue]
  );

  // Allowance summary data
  const { cost, costAllow } = useMemo(
    () => allowancesResp?.summary?.[switchObj?.key] || {},
    [allowancesResp, switchObj]
  );

  const { calcError, validateNewAmount, clearError } = useAllowanceAmountValidation({
    maxLimit: roundOfNumber(costAllow),
  });

  useEffect(() => {
    if (!allowancesResp) return;
    handleFieldsOnLoad();
  }, [allowancesResp]);

  useEffect(() => {
    if(!isEditEnable && !isAmtSavedInTemp) {
      !isHfIfType && clearError();
      if(isHfIfType) {
        setDifferentAmountFound(false);
        setTotalAmount(0);
      }
    }
  },[isAmtSavedInTemp, isEditEnable])

  const handleFieldsOnLoad = () =>
    isHfIfType || isHfIfWhseCase ? setHfIfAmountOnLoad() : setBaseAmtOnLoad();

  const handleAmountChange = value => {
    isHfIfType || isHfIfWhseCase
      ? handleHfIfAmtChange(value)
      : handleBaseAmountChange(value);
  };

  const handleUOMChange = val => {
    if (
      searchId &&
      ![allowanceStatus, eventStatus].includes(EEVENT_STATUS.DRAFT)
    ) {
      setAllowanceAmountFields({
        ...allowanceAmountFields,
        uomChange: true,
      });
    }
    setAllowanceAmountData(prev => ({ ...prev, uom: val.name }));
    setValue("uom", val.name, { shouldDirty: true });
  };
  const handleHfIfAmtChange = value => {
    const amount = validateAmount(value);
    setDigitError(
      amount && Number(amount) === 0 ? fieldProp?.errors?.digitError?.text : ""
    );
    setValue(fieldProp?.registerField, amount, { shouldDirty: true });
    setAllowanceAmountData(prev => ({
      ...prev,
      [fieldProp?.registerField]: amount,
    }));
    setTotalAmount(
      isHfIfWhseCase
        ? getTotalAmountSum(allowancesResp?.allowances, isHeaderFlat, amount)
        : (parseFloat(amount) || 0) * itemCount
    );
    setDifferentAmountFound(false);
  };

  const handleBaseAmountChange = value => {
    // Base allowance amount validation
    const { amount } = validateNewAmount(value);
    amount &&
      Number(amount) === 0 ?
      setDigitError(fieldProp?.errors?.digitError?.text) : setDigitError("");

    if (
      searchId &&
      ![allowanceStatus, eventStatus].includes(EEVENT_STATUS.DRAFT)
    ) {
      setAllowanceAmountFields({
        ...allowanceAmountFields,
        allowanceAmountChange: true,
      });
    }

    setValue(fieldProp?.registerField, amount, { shouldDirty: true });
    setAllowanceAmountData(prev => ({
      ...prev,
      [fieldProp?.registerField]: amount,
      finalAmountVal: costAllow - parseFloat(amount),
    }));
  };

  const calculateFinalCost = amount => {
    const multiplier = areInputsDisabled
      ? allowanceType === "SCAN"
        ? allowancesResp?.summary?.packRetail
        : 1 / allowancesResp?.summary?.packRetail
      : 1;
    const totalAmount = roundOfNumber(amount * multiplier);
    return roundOfNumber(costAllow) - (isNaN(totalAmount) ? 0 : totalAmount);
  };
  const setHfIfAmountOnLoad = () => {
    setItemCount(getItemCount(allowancesResp?.allowances));
    setTotalAmount(
      !isEditEnable && !isAmtSavedInTemp
        ? 0
        : getTotalAmountSum(allowancesResp?.allowances, isHeaderFlat)
    );
    isHfIfWhseCase &&
      setWareHouseCount(getWarehouseCount(allowancesResp?.allowances));
    if (isEmpty(initialValues)) return;
    const isCommon = isAllowanceAmountCommon(allowancesResp?.allowances, isHeaderFlat);
    setDifferentAmountFound(!isCommon);
    const amount = isCommon
      ? initialValues?.allowanceAmount || initialValues?.headerFlatAmt
      : "";
    setAllowanceAmountData({
      allowanceAmount: isHfIfWhseCase ? amount : formatAmount(amount),
    });
    setValue(fieldProp?.registerField, amount);
  };

  const setBaseAmtOnLoad = () => {
    if (!isEmpty(initialValues)) {
      initializeBaseAllowAmount(false);
      return;
    }
    // Default initialization
    initializeBaseAllowAmount(true);
  };

  const initializeBaseAllowAmount = isDefault => {
    const prevAmount = "";
    const prevUom =
      allowancesResp?.allowances?.[0]?.allowanceItems?.[0]?.allowUomType || "";
    const toSetUom = getUOMFromNameOrValue({
      value: isDefault ? prevUom : initialValues?.uom || "",
    })?.name;
    const toSetAllowAmt = isDefault
      ? prevAmount
      : initialValues?.allowanceAmount;
    setAllowanceAmountData({
      ...initialValues,
      allowanceAmount: toSetAllowAmt,
      uom: toSetUom,
      finalAmountKey:
        defaultToggleValue === "Case" ? "newCaseCostAllow" : "newUnitCostAllow",
      finalAmountVal:
        costAllow -
        (isDefault ? parseFloat(prevAmount) : initialValues?.allowanceAmount),
    });
  };
  return {
    // State
    itemCount,
    totalAmount,
    wareHouseCount,
    digitError,
    differentAmountFound,
    calcError,
    switchEnabled,
    setSwitchEnabled,
    switchValue,
    areInputsDisabled,
    switchObj,
    cost,
    costAllow,
    defaultToggleValue,

    // Handlers
    handleAmountChange,
    handleUOMChange,
    calculateFinalCost,

    // UOM helpers
    getUOMOptions: () =>
      getUOMBasedOnType(allowanceType, UOM_OPTIONS, switchValue),
  };
};
