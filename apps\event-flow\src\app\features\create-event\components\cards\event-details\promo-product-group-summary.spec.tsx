import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { Provider } from "react-redux";
import configureMockStore from "redux-mock-store";
import PromoProductGroupSummary from "./promo-product-group-summary";
import "@testing-library/jest-dom";
import { validateNCDP } from "./event-details-card-service";

// Mock dependencies
jest.mock("./event-details-card-service", () => ({
  validateNCDP: jest.fn(),
}));

jest.mock("../../../service/event-details/event-detail-service", () => ({
  formatCicPpgDisplayName: jest.fn((ppg) => `Formatted: ${ppg.name}`),
}));

const mockStore = configureMockStore();

describe("PromoProductGroupSummary", () => {
  const mockDeleteSelectedPPG = jest.fn();

  const mockPlanProductGroups = [
    { name: "CIC Product Group 1" },
    { name: "Product Group 2" },
  ];

  let store;

  beforeEach(() => {
    jest.clearAllMocks();
    store = mockStore({
      event_type_and_divisions: {
        eventType: "someEventType",
      },
    });
  });

  it("renders the product groups with their names", () => {
    (validateNCDP as jest.Mock).mockReturnValue(false);

    render(
      <Provider store={store}>
        <PromoProductGroupSummary
          planProductGroups={mockPlanProductGroups}
          deleteSelectedPPG={mockDeleteSelectedPPG}
          isAllowanceOrPromotion={false}
        />
      </Provider>
    );

    expect(screen.getByText("CIC Product Group 1")).toBeInTheDocument();
    expect(screen.getByText("Formatted: Product Group 2")).toBeInTheDocument();
  });

  it("disables interaction when validateNCDP returns true", () => {
  (validateNCDP as jest.Mock).mockReturnValue(true);

  render(
    <Provider store={store}>
      <PromoProductGroupSummary
        planProductGroups={mockPlanProductGroups}
        deleteSelectedPPG={mockDeleteSelectedPPG}
        isAllowanceOrPromotion={false}
      />
    </Provider>
  );

  // Use a more robust check for the className
  const productGroupDivs = screen.getAllByText((_, element) => {
    return typeof element?.className === "string" && element.className.includes("pointer-events-none");
  });

  expect(productGroupDivs.length).toBe(2); // Both product groups should be disabled
});

  it("calls deleteSelectedPPG when delete icon is clicked and isAllowanceOrPromotion is false", () => {
    (validateNCDP as jest.Mock).mockReturnValue(false);

    render(
      <Provider store={store}>
        <PromoProductGroupSummary
          planProductGroups={mockPlanProductGroups}
          deleteSelectedPPG={mockDeleteSelectedPPG}
          isAllowanceOrPromotion={false}
        />
      </Provider>
    );

    const deleteIcons = document.querySelectorAll("svg.lucide-x");
    fireEvent.click(deleteIcons[0]);

    expect(mockDeleteSelectedPPG).toHaveBeenCalledWith(mockPlanProductGroups[0]);
  });

 it("does not call deleteSelectedPPG when isAllowanceOrPromotion is true", () => {
  (validateNCDP as jest.Mock).mockReturnValue(false);

  render(
    <Provider store={store}>
      <PromoProductGroupSummary
        planProductGroups={mockPlanProductGroups}
        deleteSelectedPPG={mockDeleteSelectedPPG}
        isAllowanceOrPromotion={true}
      />
    </Provider>
  );

  // Use querySelectorAll to find the delete icons by their class
  const deleteIcons = document.querySelectorAll("svg.lucide-x");
  fireEvent.click(deleteIcons[0]);

  // Ensure deleteSelectedPPG is not called
  expect(mockDeleteSelectedPPG).not.toHaveBeenCalled();
});

  it("renders the delete icon for each product group", () => {
  (validateNCDP as jest.Mock).mockReturnValue(false);

  render(
    <Provider store={store}>
      <PromoProductGroupSummary
        planProductGroups={mockPlanProductGroups}
        deleteSelectedPPG={mockDeleteSelectedPPG}
        isAllowanceOrPromotion={false}
      />
    </Provider>
  );

  const deleteIcons = document.querySelectorAll("svg.lucide-x");
  expect(deleteIcons.length).toBe(2);
});
});
