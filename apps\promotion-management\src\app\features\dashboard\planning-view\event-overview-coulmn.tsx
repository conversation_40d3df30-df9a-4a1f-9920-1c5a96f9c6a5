import React, { useState, useRef } from "react";
import { useDispatch } from "react-redux";
import { useSelectorWrap } from "@me/data-rtk";
import { toggleSelectedEvent } from "@me/promotion-management/events-container/event-task-feature-slice";
import pmConstants from "../../../shared/pm-constants/pm-constants";
import { StoreGroupHoverContent } from "@me/ui-store-group-hover-content";
import { showAddEventCTA } from "@me-upp-js/utilities";
import Checkbox from "@albertsons/uds/molecule/Checkbox";
import { NavLink } from "react-router-dom";
import { EEVENT_STATUS } from "@me/util-helpers";
import {
  dateRange,
  getStoreGroupName,
  onPopperBlur,
  storeGrpFormedList,
} from "./planning-view-filter-config";
import Popper from "@albertsons/uds/molecule/Popper";
import { eventTypes } from "apps/event-flow/src/app/features/event-types/constants/event-type-constant";
import { EVENT_TYPE } from "../../../constants";
import SkeletonLoader from "@albertsons/uds/molecule/SkeletonLoader";
import RenderEventNamePopover from "./render-event-name-popover";

const EventOverviewColumn = props => {
  const { task, isLoading } = props;
  const dispatch = useDispatch();
  const storeGroupRef = useRef<HTMLDivElement>(null);
  const [storeGroupOpen, setStoreGroupOpen] = useState<boolean>(false);
  const { data: selectedEvents } =
    useSelectorWrap("setSelectedEvents_rn") || {};
  const handleCheckboxChange = (id: string) => {
    dispatch(toggleSelectedEvent(id));
  };
  const [NDP] = eventTypes.map(event => event?.eventType);
  return (
    <div
      className={`flex gap-3 align-top p-3 ${pmConstants.componentClassName.EVENT_OVERVIEW_COLUMN}`}
    >
      {!isLoading ? (
        <SkeletonLoader height={10} width={20} />
      ) : showAddEventCTA(task?.divisionIds, task?.negotiationSimsVendors) ? (
        <Checkbox
          className="mt-[2px]"
          checked={selectedEvents.includes(task?.id)}
          onChange={() => handleCheckboxChange(task?.id)}
          disabled={
            task?.eventType === EVENT_TYPE.NCDP || task?.eventType === "NDP"
          }
        />
      ) : (
        <span className="ml-5"></span>
      )}
      <div className="w-full">
        {!isLoading ? (
          <SkeletonLoader height={"100%"} width={"100%"} />
        ) : (
          <div className="flex flex-col justify-start gap-1 overflow-hidden">
            {/* <div className="justify-start  relative gap-2"> */}
            <NavLink
              to={`events/edit/${task?.id}`}
              // onClick={(e) => e?.preventDefault()}
              className={`flex font-bold text-left leading-4 truncate overflow-hidden ${
                task?.eventStatus === EEVENT_STATUS.REMOVED
                  ? "pointer-events-none cursor-not-allowed opacity-50"
                  : ""
              }`}
              data-testid="link-by-product-d"
              id={`abs-events-row-container-event-link-${task?.id}`}
            >
              <RenderEventNamePopover task={task} isLoading={isLoading} />
            </NavLink>
            {task?.eventType === EVENT_TYPE.NDP &&
            task?.divisionIds?.length > 1 ? (
              <div className="flex justify-start items-center gap-">
                <span className="text-sm text-left text-[#2b303c]">
                  <b>{`Divisions (`}</b>
                </span>
                <span
                  className="self-center text-sm font-semibold text-left text-[#1b6ebb] cursor-pointer"
                  ref={storeGroupRef}
                  onClick={() => setStoreGroupOpen(true)}
                  id={`abs-events-row-container-store-group-${task?.id}`}
                >
                  <p>
                    <b>{task?.divisionIds?.length}</b>
                  </p>
                </span>
                <span className="text-sm text-left text-[#2b303c]">
                  <b>{`)`}</b>
                </span>
              </div>
            ) : (
              <div className="flex justify-start items-center gap-2">
                <span className="text-sm text-left text-[#2b303c]">
                  <b>
                    {getStoreGroupName(task)} {""}
                  </b>
                </span>
                <span
                  className="self-center text-sm font-semibold text-left text-[#1b6ebb] cursor-pointer"
                  ref={storeGroupRef}
                  onClick={() => setStoreGroupOpen(true)}
                  id={`abs-events-row-container-store-group-${task?.id}`}
                >
                  {task?.storeGroupDTO?.length > 1 && <p>More</p>}
                </span>
              </div>
            )}
            <Popper
              anchor={storeGroupRef}
              open={storeGroupOpen}
              onBlur={e => onPopperBlur(e, storeGroupRef, setStoreGroupOpen)}
              autoFocus={true}
              zIndex={10}
            >
              <StoreGroupHoverContent
                storeGroups={storeGrpFormedList(task) || []}
                popoverCloser={() => setStoreGroupOpen(false)}
                isSortGroups={true}
              />
            </Popper>
          </div>
        )}
      </div>
    </div>
  );
};

export default EventOverviewColumn;
