import {
  checkEmptyValueForEvent,
  numberOnlyStringCheck,
} from "@me-upp-js/utilities";
import { DEFAULT_EVENT_STATUS_VALUE } from "apps/promotion-management/src/app/config/dashboard-config";
import { ALLOWANCE_HEADER_STATUS_OPTION } from "../../allowance-view/allowance-view-filter-config";
import { filterDetails } from "@me/promotion-management/events-container/event-status-helper";

export function getDefaultValuesForEventStatusFilter() {
  const values_from_local_storage = localStorage.getItem("eventStatusData"),
  isValidStatus = values_from_local_storage && JSON.parse(values_from_local_storage)?.length > 0;
  return isValidStatus
    ? JSON.parse(values_from_local_storage)
    : DEFAULT_EVENT_STATUS_VALUE;
}

export function getDefaultValuesForOfferStatusFilter() {
  const values_from_local_storage = localStorage?.getItem("offerStatusData"),
  isValidStatus = values_from_local_storage && JSON.parse(values_from_local_storage)?.length > 0;
  return isValidStatus
    ? JSON.parse(values_from_local_storage)
    : ALLOWANCE_HEADER_STATUS_OPTION;
}


export function setEventStatusToLocalStorage(eventStatus: any[]) {
  localStorage.setItem("eventStatusData", JSON.stringify(eventStatus));
}
export function setOfferStatusToLocalStorage(offerStatus: any[]) {
  localStorage.setItem("offerStatusData", JSON.stringify(offerStatus));
}

export const convertToNumber = (value, isSpaceAllowed, isList) => {
  let err;
  if (isList) {
    err = !numberOnlyStringCheck(value, isSpaceAllowed);
  } else {
    value = Number(value);
    err = isNaN(value);
  }
  if (err) {
    throw new Error("Please check your input!");
  }
  return value;
};

export const processSearchText = ({ searchText, searchFilterOption }) => {
  const { isList, isSpaceAllowed, isOnlyNumber, isNumber, eventKey } = searchFilterOption;
  let val = searchText;
  if (isOnlyNumber) {
    val = convertToNumber(val, isSpaceAllowed, isList);
  }

  if (isList) {
    val = val
      ?.trim()
      ?.split(/(?:,| )+/)
      .filter(item => item?.trim())
      ?.map(item => (isNumber ? Number(item) : eventKey ==="planEventIdNbrs" ? String(item?.split("-")?.[0]): String(item)));
    
    if (eventKey === "planEventIdNbrs"){
        val = Array.from(new Set(val));
      }

  } else if (!isOnlyNumber) {
    const checkValue = checkEmptyValueForEvent(val);
    if (!checkValue) {
      throw new Error("Please check your input!");
    }
    return val.split(",").map(item => item.trim());
  }
  return val;
};

export const syncEventStatusOnViews = (key, value) => {
  if (key === "event-status") {
    const formOfferStatsVal = formedOfferStatus(value);
    setEventStatusToLocalStorage(value);
    setOfferStatusToLocalStorage(formOfferStatsVal);
  }
  if (key === "allowanceStatus") {
    setOfferStatusToLocalStorage(value);
    const mainFilters = filterDetails?.length
      ? filterDetails
      : getDefaultValuesForEventStatusFilter();
    const formEventStatsVal = Array.from(formEventStatus(mainFilters, value)); // Convert iterable iterator to array
    setEventStatusToLocalStorage(formEventStatsVal);
  }
};
const formedOfferStatus = value =>
  value?.map(e => ({ label: e?.name, isChecked: e?.checked }));
const formEventStatus = (mainFilters, value) =>
  new Map(
    [
      ...(mainFilters || []),
      ...(value?.map(e => ({ name: e?.label, checked: e?.isChecked })) || []),
    ].map(c => [c?.name, c])
  )?.values() || [];
