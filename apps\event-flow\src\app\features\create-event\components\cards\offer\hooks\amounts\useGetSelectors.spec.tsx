import { renderHook } from "@testing-library/react";
import { useGetSelectors } from "./useGetSelectors";
import { useSelectorWrap } from "@me/data-rtk";

jest.mock("@me/data-rtk", () => ({
  useSelectorWrap: jest.fn(),
}));

describe("useGetSelectors hook", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return default values when selectors return undefined", () => {
    (useSelectorWrap as jest.Mock).mockImplementation((key) => {
      switch (key) {
        case "allowance_form_data":
          return {};
        case "allowance_product_sources_rn":
          return {};
        case "allowance_temp_work":
          return {};
        case "allow_conv_status_rn":
          return { data: {} };
        case "event_details_data":
          return {};
        case "offer_sections_enable_config":
          return {};
        default:
          return {};
      }
    });

    const { result } = renderHook(() => useGetSelectors());

    expect(result.current.allowanceForm).toBeUndefined();
    expect(result.current.productSources).toEqual([]);
    expect(result.current.allowanceTempWorkData).toEqual({});
    expect(result.current.isAllowConvEnable).toBeUndefined();
    expect(result.current.eventDetailsData).toBeUndefined();
    expect(result.current.offerSectionsEnableConfig).toEqual({});
  });

  it("should return correct values from selectors", () => {
    (useSelectorWrap as jest.Mock).mockImplementation((key) => {
      switch (key) {
        case "allowance_form_data":
          return { data: { formKey: "testValue" } };
        case "allowance_product_sources_rn":
          return { data: { productSources: ["Source1", "Source2"] } };
        case "allowance_temp_work":
          return { data: { allowanceData: { tempKey: "tempValue" } } };
        case "allow_conv_status_rn":
          return { data: { isAllowConvEnable: true } };
        case "event_details_data":
          return { data: { eventKey: "eventValue" } };
        case "offer_sections_enable_config":
          return { data: { sectionEnabled: true } };
        default:
          return {};
      }
    });

    const { result } = renderHook(() => useGetSelectors());

    expect(result.current.allowanceForm).toEqual({ formKey: "testValue" });
    expect(result.current.productSources).toEqual(["Source1", "Source2"]);
    expect(result.current.allowanceTempWorkData).toEqual({ tempKey: "tempValue" });
    expect(result.current.isAllowConvEnable).toBe(true);
    expect(result.current.eventDetailsData).toEqual({ eventKey: "eventValue" });
    expect(result.current.offerSectionsEnableConfig).toEqual({ sectionEnabled: true });
  });
});
