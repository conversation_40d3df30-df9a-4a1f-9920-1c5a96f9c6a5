import * as React from "react";
import Checkbox from "@albertsons/uds/molecule/Checkbox";

const SelectDeselectAllContainer: React.FunctionComponent<any> = ({
  configObj,
  handleClick,
  isChecked,
  partialClass,
  isDisabled,
}) => {
  const { selectDeselectLabel, id } = configObj;

  return (
    <label className="text-[#5A697B] flex flex-grow-0 flex-shrink-0 relative gap-3">
      <Checkbox
        data-testid="selection-list-select-all"
        id={`selection-list-select-all-${id}`}
        value={"all"}
        checked={isChecked}
        indeterminate={partialClass}
        disabled={isDisabled}
        className={`w-4 h-5 font-bold`}
        onClick={handleClick}
        aria-label={selectDeselectLabel}
      />
      <span className="text-dark-text">
        {selectDeselectLabel ?? "Select/Deselect All"}
      </span>
    </label>
  );
};

export default React.memo(SelectDeselectAllContainer);