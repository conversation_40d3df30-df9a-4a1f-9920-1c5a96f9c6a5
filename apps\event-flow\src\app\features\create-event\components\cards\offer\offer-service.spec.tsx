// File: __tests__/your-file-name.test.ts

import {
  buildOverRideArray,
  checkAmountRefreshCondition,
  checkElementRendered,
  checkIsNationalEvent,
  getBatchPayloadByDivisions,
  getConvertedCreateInd,
  getDatesVehicleForTemp,
  getDefaultPerfOption,
  getOfferMapKey,
  getPayloadForAllowanceItems,
  getPerfOptionById,
  getStoregroupFormData,
  getUpdatedAllowanceTempData,
  getUpdatedPerfValues,
  handleZindexForCard,
  isAllowanceToBeCreatedEditable,
  multiplier,
  postBatchPayloadByDivisions,
  resetOfferSectionData,
  scrollToActiveSection,
  scrollToSection,
  setFocusedSection,
} from "./offer-service";

describe('Utility Functions Test', () => {
  it('should return offerMapKey from createInd', () => {
    expect(getOfferMapKey("createIndExample")).toBeUndefined();
  });

  it('should return correct multiplier', () => {
    expect(multiplier({ areInputsDisabled: true, allowancesResp: { summary: { packRetail: 10 } }, allowanceType: "SCAN" })).toBe(10);
    expect(multiplier({ areInputsDisabled: true, allowancesResp: { summary: { packRetail: 2 } }, allowanceType: "OTHER" })).toBe(0.5);
    expect(multiplier({ areInputsDisabled: false, allowancesResp: {}, allowanceType: "SCAN" })).toBe(1);
  });

  it('should check amount refresh condition', () => {
    expect(checkAmountRefreshCondition("someCreateInd", "someAllowanceType")).toBe(false);
  });

  it('should build override array', () => {
    const result = buildOverRideArray(
      { storeGroups: [{ id: '1', name: 'Store 1' }], storeGroupType: { id: 'sgt1', name: 'Type 1' } },
      ['div1']
    );
    expect(result.length).toBe(1);
    expect(result[0]).toHaveProperty('storeGroupId');
  });

  it('should get store group form data', () => {
    const result = getStoregroupFormData([{ storeGroupId: '1', storeGroupName: 'SG1', storeCount: 5, storeGroupType: { groupInd: 'gi1', storeGrpTypeName: 'SGT1' } }], true);
    expect(result.storeGroups[0].name).toContain('Stores');
  });

  it('should dispatch reset actions', () => {
    const dispatch = jest.fn();
    resetOfferSectionData(dispatch);
    expect(dispatch).toHaveBeenCalledTimes(5);
  });

  it('should set focused section when active and scrollTo true', () => {
    const setActiveSection = jest.fn();
    const offerSectionsEnableConfig = { testKey: { isActive: true, scrollTo: true } };
    setFocusedSection({ key: 'testKey', sectionKey: 'section1' }, offerSectionsEnableConfig, setActiveSection);
    expect(setActiveSection).toHaveBeenCalledWith('section1');
  });

  it('should scroll to active section if section exists', () => {
    const scrollToSectionMock = jest.fn();
    const sectionRefs = { current: { section1: document.createElement('div') } };
    expect(scrollToActiveSection(sectionRefs, 'section1')).toBeUndefined();
  });

  it('should handle scroll to section', () => {
    document.body.innerHTML = `
      <div class="wrapper-container"></div>
      <div id="abs-event-header-container" style="height: 50px;"></div>
    `;
    const elemToScroll = document.createElement('div');
    document.body.appendChild(elemToScroll);
    scrollToSection(elemToScroll);
  });

  it('should get converted createInd', () => {
    const createIndMock = ['value1', 'value2', 'value3']; // Replace with the actual array or mock data
    expect(getConvertedCreateInd(createIndMock[0], false, ['DSD'], {})).toBe(createIndMock[0]);
  });

  it('should get payload for allowance items', () => {
    Object.defineProperty(window, 'location', {
      writable: true,
      value: { search: "?eventId=1&group=A" },
    });
    const result = getPayloadForAllowanceItems(false, "tempWorkId", {});
    expect(result.length).toBe(2);
  });

  it('should handle z-index on card click', () => {
    const event = { stopPropagation: jest.fn(), target: document.createElement('div') };
    event.target.classList.add('z-index-dynamic-class');
    handleZindexForCard(event);
    window.dispatchEvent(new MouseEvent('click'));
  });

  it('should get default performance option', () => {
    const perfOptions = [
      {
        eventTypes: ['TYPE'],
        deleteInd: 'N',
        performanceConfig: { defaultCreateInd: '' },
        performance: 'somePerformance'
      }
    ];
    expect(getDefaultPerfOption('SCAN', perfOptions, 'TYPE', '')).toEqual({});
  });

  it('should get performance option by id', () => {
    const perfOptions = [{ id: '1', performance: 'Perf1' }];
    expect(getPerfOptionById(perfOptions, '1')).toEqual({ id: '1', performance: 'Perf1' });
  });

  it('should get updated performance values', () => {
    expect(getUpdatedPerfValues('SCAN', 'SCAN', 'EVENT', 'CREATE', false, {}, {}, [], [])).toEqual({
      allowancePerformances: {},
      offerAllowancesGroupInfoMap: {}
    });
  });

  it('should check allowance to be created editable', () => {
    expect(isAllowanceToBeCreatedEditable('SCAN', ['DSD'])).toBeTruthy();
  });

  it('should check is national event', () => {
    expect(checkIsNationalEvent('SOME_EVENT')).toBe(false);
  });

  it('should get updated allowance temp data', () => {
    expect(getUpdatedAllowanceTempData([{}], 'NORMAL_EVENT')).toEqual([{}]);
  });

  it('should get batch payload by divisions', () => {
    const payload = { key: 'value' };
    const result = getBatchPayloadByDivisions(payload, ['1', '2', '3', '4']);
    expect(result.length).toBe(2);
  });

  it('should get post batch payload by divisions', () => {
    const payload = [{}, {}, {}, {}];
    const result = postBatchPayloadByDivisions(payload, ['1', '2', '3', '4']);
    expect(result.length).toBe(2);
  });

  it('should get dates vehicle for temp', () => {
    const vehicle = {
      startDate: '2025-04-27',
      endDate: '2025-04-28',
      vehicleId: 'V1',
      vehicleNm: 'Truck',
      vehicleType: { vehicleTypeId: 'VT1', vehicleTypDesc: 'Truck Type' },
      year: 2025,
    };
    expect(getDatesVehicleForTemp(vehicle, 'offerMapKey')).toBeDefined();
  });
});
