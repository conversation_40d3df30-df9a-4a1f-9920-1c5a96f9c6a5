import { InputMultiSelect } from "@me/input-fields";
import { useGetStoreGroupsLazyQuery } from "apps/event-flow/src/app/graphql/generated/schema";
import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import { useFormContext } from "react-hook-form";
import {
  itemOrder<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  setNdVendorDivisions,
} from "../../utility/utility";
import StoreGroupItemModal from "../../view-items/store-group-item-modal";
// import groupInd_constants from "../../../../shared/ef-constants/ef-constants";
import groupInd_constants from "../../../../../../../shared/ef-constants/ef-constants";
import {
  setStoreGroupDivisions,
  setStoreGroupsData,
} from "../../../../../service/slice/event-detail-slice";
import { useDispatch } from "react-redux";
import InfoGraphics from "apps/event-flow/src/app/features/event-types/components/info-graphics/info-graphics";
import { INFO_GRAPHICS, getLoggedInUserType } from "@me-upp-js/utilities";
import { useParams } from "react-router-dom";
import { useSelectorWrap } from "@me/data-rtk";
import MultiSelectContainer from "apps/event-flow/src/app/features/common/multi-select-container/SelectionListContainer";
import SelectionListContainer from "apps/event-flow/src/app/features/common/multi-select-container/SelectionListContainer";
import { multiSelectConfigObj } from "apps/event-flow/src/app/features/common/constants/constants";
import { truncate } from "fs";
import SelectionDropdown from "apps/event-flow/src/app/features/common/multi-select-container/SelectionDropdown";

function StoreGroupsFieldMD({
  showField,
  formFields,
  storeGroups: fieldProps,
  setStoreGroupFieldChanged,
  storeGroupFieldChanged,
}) {
  const {
    setValue,
    getValues,
    formState: { errors },
    trigger,
  } = useFormContext();
  const {
    registerField,
    label,
    placeHolder,
    notFoundText,
    suggestionKey,
    notSameUpcTypeErrorMsg,
    multiVendorErrorMsg,
    searchQueryName,
    getQueryName,
    required,
  } = fieldProps;
  const [filteredStoreGrp, setFilteredStoreGrp] = useState([]);
  const dispatch = useDispatch();
  const { id: eventId } = useParams();
  const hasStoreGroupValues = storeGroup => storeGroup?.some(store => store);
  const userType = getLoggedInUserType();
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { data: eventTypeAndDivisionsData } = useSelectorWrap(
    "event_type_and_divisions"
  );

  const { vendorNumbers, divisions } = setNdVendorDivisions();
  const eventType =
    eventTypeAndDivisionsData?.eventType || eventDetailsData?.eventType;

  const buildVariablesforAPI = () => {
    let promoProductGroupIds = [];
    const planProductGroups = getValues("planProductGroups");
    promoProductGroupIds = planProductGroups?.map(productGroup => {
      return productGroup?.id || productGroup?.planProductGroupId;
    });
    return {
      filter: {
        divisionIds: ["NDP", "NAO"].includes(eventType)
          ? divisions
          : getValues("divisionIds"),
        storeGroupName: "",
        productGroups: promoProductGroupIds,
        groupInd: getValues("storeGroupType")
          ? getValues("storeGroupType")
          : formFields?.groupInd || groupInd_constants?.DEFAULT_GROUP_IND,
      },
    };
  };
  const [getStoreGroups, { data: storeGroupsData }] =
    useGetStoreGroupsLazyQuery();

  const [storeGroupFieldsData, setStoreGroupFieldsData] = useState({
    isViewStoresModalOpen: false,
  });
  const setViewStoresModalOpen = isOpen => {
    setStoreGroupFieldsData(prevState => {
      return {
        ...prevState,
        isViewStoresModalOpen: isOpen,
      };
    });
  };
  const setFilterDivsFromStoreGrps = storeGroups => {
    const storeGroupDivisions = [
      ...new Set(storeGroups?.flatMap(item => item.divId || item.divisionIds)),
    ];
    dispatch(setStoreGroupDivisions({ storeGroupDivisions }));
  };
  const onStoreGroupsChange = element => {
    setFilterDivsFromStoreGrps(element);
    setValue(registerField, element);
    const storeGrops = element?.map(item => {
      const countLabel = item?.name?.split("(");
      const name = countLabel?.[0];
      let count = countLabel?.[countLabel?.length - 1];
      count = count?.slice(0, count?.length - 1);
      return {
        name,
        count,
      };
    });
    if (storeGrops?.length > 1) {
      const selectedStoreGroup = storeGrops?.reduce((prev, curr) => {
        return itemOrderAlphabeticalChecker(prev, curr, "count");
      });
      // if the store group didn't change. don't change
      if (selectedStoreGroup?.name !== getValues("storeGroupName")) {
        setValue("storeGroupName", selectedStoreGroup?.name);
      }
    } else {
      setValue(
        "storeGroupName",
        storeGrops?.length ? storeGrops?.[0]?.name : ""
      );
    }
    setStoreGroupFieldChanged(true);
    Object.keys(errors).includes("storeGroups") && trigger(["storeGroups"]);
  };
  const createDefaultNameValue = (item, storeGroups) => {
    if (item?.storeGroupName && registerField === "storeGroups") {
      return `${item?.storeGroupName} (${item?.storeCount})`;
    } else {
      return item?.name || item?.[storeGroups.suggestionKey];
    }
  };
  const getDefaultValue = storeGroups => {
    const prevVal = getValues(registerField);
    const data = prevVal?.length ? prevVal : filteredStoreGrp;
    const defaultValues = Array.isArray(data)
      ? data?.map(item => {
          return {
            ...item,
            id: item?.id || item?.[storeGroups.prop],
            key: item?.id || item?.[storeGroups.prop],
            label: createDefaultNameValue(item, storeGroups),
            name: createDefaultNameValue(item, storeGroups),
          };
        })
      : [];
    setValue(registerField, defaultValues);
    setFilterDivsFromStoreGrps(defaultValues);
    return defaultValues;
  };
  const getStoreGroupMemoDefaultValue = useCallback(() => {
    return getDefaultValue(fieldProps);
  }, [
    getValues("storeGroupType"),
    formFields?.promoProductGroupName,
    getValues("planProductGroups"),
    JSON.stringify(filteredStoreGrp),
  ]);

  const customValidatorForStoreGrps = useCallback(() => {
    const selectedStoreGroups = getValues(registerField);
    return selectedStoreGroups?.length >= 1;
  }, []);
  const configObj = multiSelectConfigObj?.find(e => {
    return e?.id === "storeGroup";
  });
  const getPlanStoreGroups = useCallback(() => {
    const defaultVal = getDefaultValue(fieldProps);

    return fieldProps ? (
      <InputMultiSelect
        storeGroups={filteredStoreGrp}
        onChangeHandler={onStoreGroupsChange}
        defaultValue={defaultVal}
        validationFn={customValidatorForStoreGrps}
        errorMessage={"Store Groups required"}
        registerField={registerField}
      />
    ) : null;
  }, [
    getValues("storeGroups"),
    getValues("storeGroupType"),
    formFields.promoProductGroupName,
    JSON.stringify(getValues("planProductGroups")),
    JSON.stringify(filteredStoreGrp),
  ]);

  const buildStoreGroupsObject = storeGroups => {
    const storeGroupArray = storeGroups?.reduce((acc, ele) => {
      if (ele.divisionIds[0] !== "98") {
        const label = `${ele.storeGroupName} (${ele.storeIds.length})`;
        const { id, divisionIds } = ele;
        acc.push({
          label,
          name: label,
          id,
          key: id,
          divisionIds,
        });
      }
      return acc;
    }, []);
    setFilteredStoreGrp(storeGroupArray);
    dispatch(setStoreGroupsData(storeGroupArray));
    getStoreGroupMemoDefaultValue();
    return { data: { storeGroupsWithCount: [...(storeGroupArray || [])] } };
  };

  useEffect(() => {
    getStoreGroups({
      variables: { ...buildVariablesforAPI() },
      fetchPolicy: "no-cache",
    });
  }, [formFields]);

  useEffect(() => {
    if (storeGroupsData?.getStoreGroups?.length) {
      buildStoreGroupsObject(storeGroupsData?.getStoreGroups);
    }
  }, [JSON.stringify(storeGroupsData)]);

  return (
    <>
      <div className="w-1/4 abs-ef-store-groups-container">
        <div className="flex" id="abs-input-auto-complete-div1">
          <div
            className="flex font-bold gap-1"
            id="abs-input-auto-complete-field"
          >
            <p id="abs-input-auto-complete-label">{label}</p>
            {required ? (
              <p
                className="text-sm text-left text-[#bf2912]"
                id="abs-input-auto-complete-star"
              >
                *
              </p>
            ) : null}

            <span
              className="flex items-center pl-1 pointer-events-auto"
              id="abs-input-auto-complete-info-graphics"
            >
              <InfoGraphics
                anchor="top"
                variant="light"
                registerField={
                  INFO_GRAPHICS?.EVENT_DETAILS_INFO[`${registerField}`]
                }
                classNameInfo="flex-col m-4 text-xs text-[#5a697b]"
                hoverContent={
                  INFO_GRAPHICS?.EVENT_DETAILS_INFO[`${registerField}`]
                    ?.INFO_GRAPHICS_LABEL
                }
                size="14"
              />
            </span>
          </div>
        </div>
        <div
          className={`${
            formFields?.disableStoreGroups
              ? "pointer-events-none disable-select"
              : ""
          }`}
        >
          <SelectionDropdown
            configObj={configObj || {}}
            disabled={!storeGroupFieldChanged}
            onItemSelection={onStoreGroupsChange}
            validationFn={customValidatorForStoreGrps}
          />
        </div>
        {hasStoreGroupValues(getValues("storeGroups")) ? (
          <div className="flex justify-start items-start flex-grow-0 flex-shrink-0  gap-2.5">
            {showField ? (
              <div className="flex justify-start items-start flex-grow-0 flex-shrink-0  gap-2.5">
                <p
                  className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#1b6ebb] cursor-pointer"
                  onClick={() => setViewStoresModalOpen(true)}
                >
                  View Stores
                </p>
              </div>
            ) : null}
          </div>
        ) : null}
      </div>
      <StoreGroupItemModal
        isOpen={storeGroupFieldsData?.isViewStoresModalOpen}
        setOpen={setViewStoresModalOpen}
        storeGroups={getValues("storeGroups")}
      />
    </>
  );
}

export default memo(StoreGroupsFieldMD);
