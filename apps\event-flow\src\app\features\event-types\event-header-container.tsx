import React, { useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import { useDispatch } from "react-redux";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { eventDetailsDataHandler } from "../create-event/service/slice/event-detail-slice";
import EventHeader from "./components/event-header/event-header";
import EventCommentSection from "./components/event-comments/event-comment-section";
import { eventTypeAbbreviation } from "./constants/event-type-abbrevations";
import { useGetAppBasePath } from "@me/util-helpers";
import { useSelectorWrap } from "@me/data-rtk";
import efConstants from "../../shared/ef-constants/ef-constants";
import Menu from "@albertsons/uds/molecule/Menu";
import EventAction from "./components/event-action/event-action";
import { useGetAllEventCommentsDataQuery } from "./event-comments-section-service";
import { getLoggedInUserType } from "@me-upp-js/utilities";
import { EVENT_STATUS as eventStatusData } from "../create-event/constants/event-status/event-status";
import EventProgressService from "./components/event-progress/event-progress-service";
import EventToggleService from "./components/event-progress/event-toggle-service";
import { setEventProgressConfigData } from "./slices/event-types-slice";
import { LoadBaseEventWorkFlowData } from "./components/event-status-buttons/event-buttons";
import { RBAC } from "albertsons-react-rbac";
import EventHistorySection from "./components/event-histroy/event-history-section";

const EventHeaderContainer: React.FunctionComponent = () => {
  const location = useLocation();
  const state: any = location.state || {};
  const { getValues } = useFormContext();

  const { planEventIdNbr: eventId, eventStatus, eventType } = getValues();
  const userRole = getLoggedInUserType();
  const [commentOpen, setCommentOpen] = useState<boolean>(false);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { basePath } = useGetAppBasePath();
  const { data: planAndPendingData } = useSelectorWrap("plan_event_indicators");
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const {
    data: { allowanceData },
  } = useSelectorWrap("allowance_temp_work");
  const { divisionIds = [], negotiationSimsVendors = [] } = {
    ...eventDetailsData,
  };
  const [eventStatusFormData, setEventStatusFormData] = useState<unknown>({
    ...eventStatusData,
  });
  const [displayActions, setDisplayActions] = useState(true);
  const [scrolled, setScrolled] = useState(false);
  const [scrolledMore, setScrolledMore] = useState(false);
  const { id } = useParams();
  // allEventComments being returned as {all_comments:[{}], id, commentsLoading:boolean}
  const {
    data: allEventComments,
    isFetching: isAllEventsLoading,
    isSuccess: isAllEventsSuccess,
  } = useGetAllEventCommentsDataQuery(
    {
      URL_PARAM: id,
    },
    {
      skip: !id,
    }
  );
  useEffect(() => {
    // when location state and values.eventType are undefined, navigate user to choose event type
    if (state.type === undefined && eventType === undefined) {
      navigate(`${basePath}/events`);
    }
  }, [eventType, navigate, state.type]);

  useEffect(() => {
    const handleScroll = () => {
      const mainContainer = document.getElementById(
        "abs-event-creation-container"
      )?.parentElement;

      if (!mainContainer) return;

      if (mainContainer.scrollTop > 100) {
        setScrolled(true);
        setScrolledMore(true);
      } else if (mainContainer.scrollTop < 5) {
        setScrolled(false);
        setScrolledMore(false);
      }
    };

    window.addEventListener("scroll", handleScroll, true);
    return () => window.removeEventListener("scroll", handleScroll, true);
  }, []);

  const { data: eventProgressData, status } = useSelectorWrap(
    "event_progress_data"
  );

  const eventTypeName = state.type || eventTypeAbbreviation[eventType] || "";

  const eventProgressService = EventProgressService(
    eventType,
    eventProgressData,
    eventDetailsData,
    userRole
  );

  LoadBaseEventWorkFlowData.init(
    eventProgressService,
    eventDetailsData,
    planAndPendingData,
    allowanceData
  );

  const eventToggleService = EventToggleService(
    eventProgressService,
    eventDetailsData,
    eventStatusData,
    userRole,
    allowanceData,
    eventStatus,
    planAndPendingData
  );

  useEffect(() => {
    const data = eventToggleService.handleToggleDisable(eventStatusFormData);
    dispatch(setEventProgressConfigData(data));
    setEventStatusFormData(data);
    setDisplayActions(
      eventStatus !== "Rejected" &&
        !!data?.[eventStatus]?.USERS?.[userRole]?.buttons?.length
    );
  }, [JSON.stringify(eventDetailsData), eventProgressData, allowanceData]);

  return (
    <div
      className={`
      fixed bg-white max-w-[calc(100%-64px)] shadow-lg z-[9] top-[65px] px-[20px] -ml-[20px] flex justify-between w-full pt-[5px] ${efConstants.componentClassName.EVENT_HEADER_CONTAINER}`}
      id="abs-event-header-container"
    >
      <EventHeader
        eventId={eventId}
        eventStatus={eventStatus}
        promoProductGroup={eventDetailsData?.promoProductGroup}
        eventType={eventType}
        eventTypeName={eventTypeName}
        isChangeEventTypeVisible={eventDetailsData.isChangeEventTypeVisible}
        divisionIds={divisionIds}
        negotiationSimsVendors={negotiationSimsVendors}
        state={state}
        scrolledMore={scrolledMore}
        parentEvent={eventDetailsData?.parentEvent}
      />

      {eventId ? (
        <div
          className="flex items-center gap-4"
          id="abs-event-header-container-event-type-div"
        >
          {displayActions && (
            <EventAction
              userRole={userRole}
              eventStatus={eventStatus}
              eventStatusData={eventStatusFormData}
              eventId={eventDetailsData?.id}
              setCommentOpen={setCommentOpen}
              modifyOrCancel={
                eventDetailsData?.planEvent?.planEventWorkFlowType
              }
            />
          )}

          <EventCommentSection
            eventID={eventDetailsData?.id}
            setCommentOpen={setCommentOpen}
            commentViewData={commentOpen}
          />
          <RBAC
            divisionIds={divisionIds}
            permissionsOnly={["PROMOTION_HISTORY_VIEW"]}
            simsVendors={negotiationSimsVendors}
          >
            <EventHistorySection />
          </RBAC>
        </div>
      ) : null}
    </div>
  );
};

export default EventHeaderContainer;
