import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { useFormContext } from "react-hook-form";
import { useSelectorWrap } from "@me/data-rtk";
import { InputSelect } from "@me/input-fields";
import YearField from "./year-field";
import "@testing-library/jest-dom";

jest.mock("react-hook-form", () => ({
  useFormContext: jest.fn(),
}));

jest.mock("@me/data-rtk", () => ({
  useSelectorWrap: jest.fn(),
}));

jest.mock("@me/input-fields", () => ({
  InputSelect: jest.fn(({ onChange, fieldProps, disabled, defaultValue }) => (
    <select
      data-testid="input-select"
      disabled={disabled}
      defaultValue={defaultValue}
      onChange={(e) => onChange({ name: e.target.value })}
    >
      {fieldProps?.options?.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  )),
}));

describe("YearField", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock useSelectorWrap to return a default value
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: {
        eventTypeAndDivisionsData: {
          eventType: "someEventType",
        },
      },
    });

    // Mock useFormContext
    (useFormContext as jest.Mock).mockReturnValue({
      setValue: jest.fn(),
      getValues: jest.fn(() => "vehicleTypeId123"),
    });
  });

  it("renders the YearField component with year options", () => {
    const mockSetFormFields = jest.fn();
    const mockSetVehicleTypeFieldChanged = jest.fn();

    const yearData = {
      name: "year",
      options: [
        { value: "2022", label: "2022" },
        { value: "2023", label: "2023" },
      ],
    };

    render(
      <YearField
        year={yearData}
        formFields={{ yearVal: "2022", vehicleTypeProps: "Car" }}
        setFormFields={mockSetFormFields}
        setVehicleTypeFieldChanged={mockSetVehicleTypeFieldChanged}
      />
    );

    const selectElement = screen.getByTestId("input-select");
    expect(selectElement).toBeInTheDocument();
    expect(selectElement).toHaveValue("2022");
  });

  it("calls setValue and setFormFields when year is changed", () => {
    const mockSetValue = jest.fn();
    const mockSetFormFields = jest.fn();
    const mockSetVehicleTypeFieldChanged = jest.fn();

    (useFormContext as jest.Mock).mockReturnValue({
      setValue: mockSetValue,
      getValues: jest.fn(() => "vehicleTypeId123"),
    });

    const yearData = {
      name: "year",
      options: [
        { value: "2022", label: "2022" },
        { value: "2023", label: "2023" },
      ],
    };

    render(
      <YearField
        year={yearData}
        formFields={{ yearVal: "2022", vehicleTypeProps: "Car" }}
        setFormFields={mockSetFormFields}
        setVehicleTypeFieldChanged={mockSetVehicleTypeFieldChanged}
      />
    );

    const selectElement = screen.getByTestId("input-select");
    fireEvent.change(selectElement, { target: { value: "2023" } });

    expect(mockSetValue).toHaveBeenCalledWith("eventCreationVehicle.year", "2023");
    expect(mockSetVehicleTypeFieldChanged).toHaveBeenCalledWith(true);
  });

  it('disables the year dropdown if vehicleTypeProps is "Custom Date" or "CustomDate"', () => {
    const mockSetFormFields = jest.fn();
    const mockSetVehicleTypeFieldChanged = jest.fn();

    const yearData = {
      name: "year",
      options: [
        { value: "2022", label: "2022" },
        { value: "2023", label: "2023" },
      ],
    };

    render(
      <YearField
        year={yearData}
        formFields={{ yearVal: "2022", vehicleTypeProps: "Custom Date" }}
        setFormFields={mockSetFormFields}
        setVehicleTypeFieldChanged={mockSetVehicleTypeFieldChanged}
      />
    );

    const selectElement = screen.getByTestId("input-select");
    expect(selectElement).toBeDisabled();
  });

  it("does not disable the year dropdown for other vehicle types", () => {
    const mockSetFormFields = jest.fn();
    const mockSetVehicleTypeFieldChanged = jest.fn();

    const yearData = {
      name: "year",
      options: [
        { value: "2022", label: "2022" },
        { value: "2023", label: "2023" },
      ],
    };

    render(
      <YearField
        year={yearData}
        formFields={{ yearVal: "2022", vehicleTypeProps: "Car" }}
        setFormFields={mockSetFormFields}
        setVehicleTypeFieldChanged={mockSetVehicleTypeFieldChanged}
      />
    );

    const selectElement = screen.getByTestId("input-select");
    expect(selectElement).not.toBeDisabled();
  });

  it("renders null if no year data is provided", () => {
    const mockSetFormFields = jest.fn();
    const mockSetVehicleTypeFieldChanged = jest.fn();

    render(
      <YearField
        year={null}
        formFields={{ yearVal: "2022", vehicleTypeProps: "Car" }}
        setFormFields={mockSetFormFields}
        setVehicleTypeFieldChanged={mockSetVehicleTypeFieldChanged}
      />
    );

    const selectElement = screen.queryByTestId("input-select");
    expect(selectElement).toBeNull();
  });
});