import {
  isPendingCollectionUser,
  getEventDetails,
  filterOfferAllowances,
  filterPromotions,
  getPromoDraftCollections,
  getAllowanceDraftCollections,
  getSortedPlanEvent,
  getUpdatedPromotions,
  getUpdatedOfferAllownces,
} from "./event-hook-form-wrapper-service";
import moment from "moment";
import * as MEUPP_UTILS from "@me-upp-js/utilities";
import {
  mockDraftDataAllowance,
  mockDraftDataAllowanceMerchant,
  mockDraftDataAllowanceNonDraft,
  mockDraftDataPromotionDraft,
  mockDraftDataPromotionDraftMerchant,
  mockDraftDataPromotionNonDraft,
  promoDraftCollectionsData,
  allowanceDraftCollectionsData,
  sortedPlanEventData,
  updatedPromotionsAllowanceData1,
  updatedPromotionsAllowanceData2,
} from "./event-hook-form-wrapper-service-mock-data";
import { promo10003136 } from "../../shared/event-flow-mocks/promotions-mock";

jest.spyOn(MEUPP_UTILS, "getLoggedInUserType").mockReturnValue("VENDOR");
// Define some test data
const mockDraftData = {
  eventDetailsEventInd: false,
  otherDetailsChangedInd: false,
  allowanceEventInd: false,
  promotionEventInd: false,
  planEvent: {
    id: "6448ee5453eea24e88fba49b",
    planEventIdNbr: 10006146,
    name: "Knudsen Cottage Cheese 16 OZ - Norcal California All Stores  -Teste5",
    divisionIds: ["25"],
    startDate: 1690351200000,
    endDate: 1690869600000,
    eventType: "DP",
    sourceEventType: "ECP",
    eventStatus: "Draft",
    promotionsList: [promo10003136],
    promotions: ["6448ee8d4440742b1218498f"],
    createUser: {
      userId: "RROUT04",
      name: "Ravi Kiran Routhu",
      type: "Merchant",
      userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
      createTs: "2023-04-26T09:26:44.140Z",
    },
    updateUser: {
      userId: "RROUT04",
      name: "Ravi Kiran Routhu",
      type: "Merchant",
      userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
      createTs: "2023-04-26T09:26:44.140Z",
    },
    planEventWorkFlowType: "NOT FOUND",
    eventTypeEnum: "DP",
  },
  planEventPending: null,
  planEventHistory: null,
  planEventPendingChanges: null,
};

const mockPendingData = {
  eventDetailsEventInd: true,
  allowanceEventInd: false,
  promotionEventInd: true,
  planEvent: {
    id: "6448ee5453eea24e88fba49b",
    planEventIdNbr: 10006146,
    name: "Knudsen Cottage Cheese 16 OZ - Norcal California All Stores  -Teste52",
    divisionIds: ["25"],
    startDate: "2023-07-26",
    endDate: "2023-08-01",
    eventType: "DP",
    sourceEventType: "ECP",
    eventStatus: "Pending With Vendor",
    promotionsList: [promo10003136],
    promotions: ["6448ee8d4440742b1218498f"],
    createUser: {
      userId: "RROUT04",
      name: "Ravi Kiran Routhu",
      type: "Merchant",
      userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
      createTs: "2023-04-26T09:26:44.140Z",
    },
    updateUser: {
      userId: "RROUT04",
      name: "Ravi Kiran Routhu",
      type: "Merchant",
      userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
      createTs: "2023-04-26T09:27:59.873Z",
    },
    planEventWorkFlowType: "Modify",
    eventTypeEnum: "DP",
  },
  planEventPending: {
    id: "6448ee5453eea24e88fba49b",
    planEventIdNbr: 10006146,
    name: "Knudsen Cottage Cheese 16 OZ - Norcal California All Stores  -Teste5",
    divisionIds: ["25"],
    startDate: 1690351200000,
    endDate: 1690869600000,
    eventType: "DP",
    sourceEventType: "ECP",
    eventStatus: "Pending With Vendor",
    promotionsList: [promo10003136],
    promotions: ["6448ee8d4440742b1218498f"],
    createUser: {
      userId: "RROUT04",
      name: "Ravi Kiran Routhu",
      type: "Merchant",
      userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
      createTs: "2023-04-26T09:26:44.140Z",
    },
    updateUser: {
      userId: "SPOTH03",
      name: "Sridhar pothanaveni",
      type: "Vendor",
      userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
      createTs: "2023-05-10T07:01:07.184Z",
    },
    planEventWorkFlowType: "NOT FOUND",
    eventTypeEnum: "DP",
  },
  planEventHistory: [
    {
      id: "6448ee9f53eea24e88fba4a0",
      eventId: "6448ee5453eea24e88fba49b",
      eventIdNbr: 10006146,
      divisionIds: ["25"],
      eventChanges: [
        {
          fieldPath: "/eventStatus",
          labelFieldName: "planevents.eventStatus",
          beforeVal: "Draft",
          afterVal: "Pending With Vendor",
        },
        {
          fieldPath: "/updateUser/createTs",
          labelFieldName: "planevents.updateUser.createTs",
          beforeVal: "Apr 26, 2023, 3:26:44 AM",
          afterVal: "Apr 26, 2023, 3:27:59 AM",
        },
      ],
      offerAllowanceChanges: [
        {
          offerNumber: 7002309,
          changes: [
            {
              fieldPath: "/createUser/createTs",
              labelFieldName: "offerallowances.createUser.createTs",
              beforeVal: "Apr 26, 2023, 3:27:24 AM",
              afterVal: "Apr 26, 2023, 3:27:59 AM",
            },
            {
              fieldPath: "/updateUser/createTs",
              labelFieldName: "offerallowances.updateUser.createTs",
              beforeVal: "Apr 26, 2023, 3:27:24 AM",
              afterVal: "Apr 26, 2023, 3:27:59 AM",
            },
          ],
        },
      ],
      promotionsChanges: [
        {
          promotionId: 10003136,
          changes: [
            {
              fieldPath: "/createUser/createTs",
              labelFieldName: "promotions.createUser.createTs",
              beforeVal: "Apr 26, 2023, 3:27:41 AM",
              afterVal: "Apr 26, 2023, 3:27:59 AM",
            },
            {
              fieldPath: "/updateUser/createTs",
              labelFieldName: "promotions.updateUser.createTs",
              beforeVal: "Apr 26, 2023, 3:27:41 AM",
              afterVal: "Apr 26, 2023, 3:27:59 AM",
            },
          ],
        },
      ],
    },
  ],
  planEventPendingChanges: {
    id: "645b6d2cd992dc3c6a48a6b7",
    eventId: "6448ee5453eea24e88fba49b",
    eventIdNbr: 10006146,
    divisionIds: ["25"],
    createUser: {
      userId: "RROUT04",
      name: "Ravi Kiran Routhu",
      type: "Merchant",
      userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
      createTs: "2023-04-26T09:26:44.140Z",
    },
    eventChanges: [
      {
        fieldPath: "/name",
        labelFieldName: "planevents.name",
        beforeVal:
          "Knudsen Cottage Cheese 16 OZ - Norcal California All Stores  -Teste52",
        afterVal:
          "Knudsen Cottage Cheese 16 OZ - Norcal California All Stores  -Teste5",
      },
      {
        fieldPath: "/startDate",
        labelFieldName: "planevents.startDate",
        beforeVal: "Jul 25, 2023, 6:00:00 PM",
        afterVal: "Jul 26, 2023, 12:00:00 AM",
      },
      {
        fieldPath: "/endDate",
        labelFieldName: "planevents.endDate",
        beforeVal: "Jul 31, 2023, 6:00:00 PM",
        afterVal: "Aug 1, 2023, 12:00:00 AM",
      },
      {
        fieldPath: "/pricing/0/priceFactor",
        labelFieldName: "planevents.pricing.priceFactor",
        beforeVal: "2",
        afterVal: "1",
      },
      {
        fieldPath: "/promotionsList/0/promoDetails/factor",
        labelFieldName: "planevents.promotionsList.promoDetails.factor",
        beforeVal: "1",
        afterVal: "2",
      },
      {
        fieldPath: "/promotionsList/0/updateUser/userId",
        labelFieldName: "planevents.promotionsList.updateUser.userId",
        beforeVal: "RROUT04",
        afterVal: "SPOTH03",
      },
      {
        fieldPath: "/promotionsList/0/updateUser/name",
        labelFieldName: "planevents.promotionsList.updateUser.name",
        beforeVal: "Ravi Kiran Routhu",
        afterVal: "Sridhar pothanaveni",
      },
      {
        fieldPath: "/promotionsList/0/updateUser/type",
        labelFieldName: "planevents.promotionsList.updateUser.type",
        beforeVal: "Merchant",
        afterVal: "Vendor",
      },
      {
        fieldPath: "/promotionsList/0/updateUser/createTs",
        labelFieldName: "planevents.promotionsList.updateUser.createTs",
        beforeVal: "Apr 26, 2023, 3:27:59 AM",
        afterVal: "May 10, 2023, 12:50:55 AM",
      },
      {
        fieldPath: "/updateUser/userId",
        labelFieldName: "planevents.updateUser.userId",
        beforeVal: "RROUT04",
        afterVal: "SPOTH03",
      },
      {
        fieldPath: "/updateUser/name",
        labelFieldName: "planevents.updateUser.name",
        beforeVal: "Ravi Kiran Routhu",
        afterVal: "Sridhar pothanaveni",
      },
      {
        fieldPath: "/updateUser/type",
        labelFieldName: "planevents.updateUser.type",
        beforeVal: "Merchant",
        afterVal: "Vendor",
      },
      {
        fieldPath: "/updateUser/createTs",
        labelFieldName: "planevents.updateUser.createTs",
        beforeVal: "Apr 26, 2023, 3:27:59 AM",
        afterVal: "May 10, 2023, 1:01:07 AM",
      },
      {
        fieldPath: "/planEventWorkFlowType",
        labelFieldName: "planevents.planEventWorkFlowType",
        beforeVal: "Modify",
        afterVal: "NOT FOUND",
      },
    ],
    offerAllowanceChanges: [
      {
        offerNumber: 7002309,
        changes: [],
      },
    ],
    promotionsChanges: [promo10003136],
  },
};

const vendorEventPendingCollection = {
  id: "6448ee5453eea24e88fba49b",
  planEventIdNbr: 10006146,
  name: "Knudsen Cottage Cheese 16 OZ - Norcal California All Stores  -Teste5",
  divisionIds: ["25"],
  startDate: 1690351200000,
  endDate: 1690869600000,
  eventType: "DP",
  sourceEventType: "ECP",
  eventStatus: "Pending With Vendor",
  promotionsList: [promo10003136],
  allowances: ["6448ee7c8e888413588ddabb"],
  promotions: ["6448ee8d4440742b1218498f"],
  createUser: {
    userId: "RROUT04",
    name: "Ravi Kiran Routhu",
    type: "Merchant",
    userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
    createTs: "2023-04-26T09:26:44.140Z",
  },
  updateUser: {
    userId: "SPOTH03",
    name: "Sridhar pothanaveni",
    type: "Vendor",
    userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
    createTs: "2023-05-10T07:01:07.184Z",
  },
  planEventWorkFlowType: "NOT FOUND",
  eventTypeEnum: "DP",
};
const vendorPromoPendingCollectionData = [
  promo10003136,
  {
    id: "6448ee8d4440742b1218498f",
    promotionId: 10003137,
    eventId: "6448ee5453eea24e88fba49b",
    customerGroup: "ALL",
    vehicle: {
      id: "636b042c50f8fb41c21b8350",
      vehicleNm: "25 Week 30 Insert 2023",
      sourceVehicleSk: 41942,
      vehicleTypNm: "insrt",
      startDate: "2023-07-26",
      endDate: "2023-08-01",
      vehicleType: {
        vehicleTypeId: "636abba1b426ee543a94d3a8",
        sourceVehicleTypeSk: 111,
        vehicleTypNm: "insrt",
        vehicleTypDesc: "Weekly Insert",
      },
    },
    promoStartDate: "2023-07-26",
    promoEndDate: "2023-08-01",
    promoDetails: {
      amount: 0,
      unitMeasure: "Each",
      itemLimit: 0,
      minQuantity: 1,
      factor: 2,
      promotionType: "NET_PRICE",
      regularPrice: 0,
      listCost: 0,
      listAgp: 0,
    },
    createUser: {
      userId: "RROUT04",
      name: "Ravi Kiran Routhu",
      type: "Merchant",
      userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
      createTs: "2023-04-26T09:27:58.673Z",
    },
    updateUser: {
      userId: "SPOTH03",
      name: "Sridhar pothanaveni",
      type: "Vendor",
      userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
      createTs: "2023-05-10T06:55:54.255Z",
    },
    promotionChangeStatus: "CREATE_PROMO",
    divisionIds: ["25"],
  },
  {
    id: "6448ee8d4440742b1218498f",
    promotionId: 10003137,
    eventId: "6448ee5453eea24e88fba49b",
    customerGroup: "ALL",
    vehicle: {
      id: "636b042c50f8fb41c21b8350",
      vehicleNm: "25 Week 30 Insert 2023",
      sourceVehicleSk: 41942,
      vehicleTypNm: "insrt",
      startDate: "2023-07-26",
      endDate: "2023-08-01",
      vehicleType: {
        vehicleTypeId: "636abba1b426ee543a94d3a8",
        sourceVehicleTypeSk: 111,
        vehicleTypNm: "insrt",
        vehicleTypDesc: "Weekly Insert",
      },
    },
    promoStartDate: "2023-07-26",
    promoEndDate: "2023-08-01",
    promoDetails: {
      amount: 0,
      unitMeasure: "Each",
      itemLimit: 0,
      minQuantity: 1,
      factor: 2,
      promotionType: "NET_PRICE",
      regularPrice: 0,
      listCost: 0,
      listAgp: 0,
    },
    createUser: {
      userId: "RROUT04",
      name: "Ravi Kiran Routhu",
      type: "Merchant",
      userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
      createTs: "2023-04-26T09:27:59.673Z",
    },
    updateUser: {
      userId: "SPOTH03",
      name: "Sridhar pothanaveni",
      type: "Vendor",
      userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
      createTs: "2023-05-10T06:50:55.255Z",
    },
    promotionChangeStatus: "CREATE_PROMO",
    divisionIds: ["25"],
  },
];
describe("isPendingCollectionUser", () => {
  test("returns true when data is an array and the latest element's updateUser type matches the logged in user type", () => {
    jest.spyOn(MEUPP_UTILS, "getLoggedInUserType").mockReturnValue("VENDOR");
    expect(isPendingCollectionUser(vendorPromoPendingCollectionData)).toBe(
      true
    );
  });

  test("returns false when data is an array and the latest element's updateUser type does not match the logged in user type", () => {
    jest.spyOn(MEUPP_UTILS, "getLoggedInUserType").mockReturnValue("MERCHANT");
    expect(isPendingCollectionUser(vendorPromoPendingCollectionData)).toBe(
      false
    );
  });

  test("returns true when data is an object and the updateUser type matches the logged in user type", () => {
    jest.spyOn(MEUPP_UTILS, "getLoggedInUserType").mockReturnValue("VENDOR");
    expect(isPendingCollectionUser(vendorEventPendingCollection)).toBe(true);
  });

  test("returns false when data is an object and the updateUser type does not match the logged in user type", () => {
    jest.spyOn(MEUPP_UTILS, "getLoggedInUserType").mockReturnValue("MERCHANT");
    expect(isPendingCollectionUser(vendorEventPendingCollection)).toBe(false);
  });
  test("test for filterOfferAllowances with Draft status", () => {
    jest.spyOn(MEUPP_UTILS, "getLoggedInUserType").mockReturnValue("VENDOR");
    const filterData = filterOfferAllowances(mockDraftDataAllowance, "VENDOR");
    expect(filterData.filteredOffers).toMatchObject(
      mockDraftDataAllowance.offerAllowances
    );
  });
  test("test for filterOfferAllowances with Draft status as Merchant updated", () => {
    jest.spyOn(MEUPP_UTILS, "getLoggedInUserType").mockReturnValue("VENDOR");
    const filterData = filterOfferAllowances(
      mockDraftDataAllowanceMerchant,
      "VENDOR"
    );
    expect(filterData.filteredOffers.length).toBeFalsy();
  });
  test("test for filterOfferAllowances with Non Draft status as Merchant updated", () => {
    jest.spyOn(MEUPP_UTILS, "getLoggedInUserType").mockReturnValue("VENDOR");
    const filterData = filterOfferAllowances(
      mockDraftDataAllowanceNonDraft,
      "VENDOR"
    );
    expect(filterData.filteredOffers.length).toBe(1);
  });
  test("test for filterPromotions with Draft status ", () => {
    jest.spyOn(MEUPP_UTILS, "getLoggedInUserType").mockReturnValue("VENDOR");
    const filterData = filterPromotions(mockDraftDataPromotionDraft, "VENDOR");
    expect(filterData.filteredPromos.length).toBe(1);
  });
  test("test for filterPromotions with Draft status as Merchant updated ", () => {
    jest.spyOn(MEUPP_UTILS, "getLoggedInUserType").mockReturnValue("VENDOR");
    const filterData = filterPromotions(
      mockDraftDataPromotionDraftMerchant,
      "VENDOR"
    );
    expect(filterData.filteredPromos.length).toBeFalsy();
  });
  test("test for filterPromotions with Non Draft status ", () => {
    jest.spyOn(MEUPP_UTILS, "getLoggedInUserType").mockReturnValue("VENDOR");
    const filterData = filterPromotions(
      mockDraftDataPromotionNonDraft,
      "VENDOR"
    );
    expect(filterData.filteredPromos.length).toBe(1);
  });
  test("test for getPromoDraftCollections with Draft status ", () => {
    const filterData = getPromoDraftCollections(
      promoDraftCollectionsData,
      "promotionsList",
      "promotionWorkflowStatus",
      "userType"
    );
    expect(filterData.length).toBe(1);
  });
  test("test for getAllowanceDraftCollections with Draft status ", () => {
    const filterData = getAllowanceDraftCollections(
      allowanceDraftCollectionsData,
      "allowances",
      "allowanceStatus"
    );
    expect(filterData.length).toBe(1);
  });
  test("test for getSortedPlanEvent", () => {
    const filterData = getSortedPlanEvent(sortedPlanEventData);
    expect(filterData.offerAllowances[0].offerNumber).toBe(7003771);
    expect(filterData.promotionsList[0].promotionId).toBe(10003136);
  });
});
describe("Event hook form service methods", () => {
  test("for getUpdatedPromotions method case 1", () => {
    const filterData = getUpdatedPromotions(
      true,
      updatedPromotionsAllowanceData1.planEvent,
      updatedPromotionsAllowanceData1.planEventPending,
      "vendor"
    );
    expect(filterData.filteredPromos.length).toBe(2);
  });
  test("for getUpdatedPromotions method case 2", () => {
    const filterData = getUpdatedPromotions(
      true,
      updatedPromotionsAllowanceData2.planEvent,
      updatedPromotionsAllowanceData2.planEventPending,
      "vendor"
    );

    expect(filterData.filteredPromos.length).toBeFalsy();
  });
  test("for getUpdatedPromotions method case 3", () => {
    const filterData = getUpdatedPromotions(
      false,
      updatedPromotionsAllowanceData1.planEvent,
      updatedPromotionsAllowanceData1.planEventPending,
      "vendor"
    );
    expect(filterData.filteredPromos.length).toBeFalsy();
  });
  test("for getUpdatedOfferAllownces method case 1", () => {
    const filterData = getUpdatedOfferAllownces(
      true,
      updatedPromotionsAllowanceData1.planEvent,
      updatedPromotionsAllowanceData1.planEventPending,
      "vendor"
    );
    expect(filterData.filteredOfferIDsArray.length).toBe(1);
  });
  test("for getUpdatedOfferAllownces method case 2", () => {
    const filterData = getUpdatedOfferAllownces(
      false,
      updatedPromotionsAllowanceData1.planEvent,
      updatedPromotionsAllowanceData1.planEventPending,
      "vendor"
    );
    expect(filterData.filteredOfferIDsArray.length).toBeFalsy();
  });
  test("for getUpdatedOfferAllownces method case 2", () => {
    const filterData = getUpdatedOfferAllownces(
      true,
      updatedPromotionsAllowanceData2.planEvent,
      updatedPromotionsAllowanceData2.planEventPending,
      "vendor"
    );
    expect(filterData.filteredOfferIDsArray.length).toBeFalsy();
  });
});

describe("getEventDetails", () => {
  const planEvent = {
    id: "6448ee5453eea24e88fba49b",
    planEventIdNbr: 10006146,
    name: "Knudsen Cottage Cheese 16 OZ - Norcal California All Stores  -Teste5",
    divisionIds: ["25"],
    startDate: 1690351200000,
    endDate: 1690869600000,
    eventType: "DP",
    sourceEventType: "ECP",
    eventStatus: "Draft",
    promotionsList: [promo10003136],
    promotions: ["6448ee8d4440742b1218498f"],
    createUser: {
      userId: "RROUT04",
      name: "Ravi Kiran Routhu",
      type: "Merchant",
      userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
      createTs: "2023-04-26T09:26:44.140Z",
    },
    updateUser: {
      userId: "SPOTH03",
      name: "Sridhar pothanaveni",
      type: "Vendor",
      userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
      createTs: "2023-05-10T07:01:07.184Z",
    },
    planEventWorkFlowType: "NOT FOUND",
    eventTypeEnum: "DP",
  };

  it("should return the updated plan event details when event is in draft status", () => {
    const result = getEventDetails(mockDraftData);
    result.updatedEventDetails.promotionsList[0].createUser = {
      userId: "RROUT04",
      name: "Ravi Kiran Routhu",
      type: "Merchant",
      userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
      createTs: "2023-04-26T09:27:59.873Z",
    };
    result.updatedEventDetails.createUser = {
      userId: "RROUT04",
      name: "Ravi Kiran Routhu",
      type: "Merchant",
      userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
      createTs: "2023-04-26T09:26:44.140Z",
    };
    result.updatedEventDetails.updateUser = {
      userId: "SPOTH03",
      name: "Sridhar pothanaveni",
      type: "Vendor",
      userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
      createTs: "2023-05-10T07:01:07.184Z",
    };
    expect(result).toEqual({ updatedEventDetails: planEvent });
  });

  xit("should return the updated event details when all event indicators are true", () => {
    const result = getEventDetails(mockPendingData);
    expect(result).toEqual(planEvent);
  });

  it("should return the updated event details when promotion event indicator is true", () => {
    const expectedObj = {
      id: "6448ee5453eea24e88fba49b",
      planEventIdNbr: 10006146,
      name: "Knudsen Cottage Cheese 16 OZ - Norcal California All Stores  -Teste5",
      divisionIds: ["25"],
      startDate: 1690351200000,
      endDate: 1690869600000,
      eventType: "DP",
      sourceEventType: "ECP",
      eventStatus: "Pending With Vendor",
      promotionsList: [promo10003136],
      promotions: ["6448ee8d4440742b1218498f"],
      createUser: {
        userId: "RROUT04",
        name: "Ravi Kiran Routhu",
        type: "Merchant",
        userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
        createTs: "2023-04-26T09:26:44.140Z",
      },
      updateUser: {
        userId: "SPOTH03",
        name: "Sridhar pothanaveni",
        type: "Vendor",
        userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
        createTs: "2023-05-10T07:01:07.184Z",
      },
      planEventWorkFlowType: "NOT FOUND",
      eventTypeEnum: "DP",
    };

    const result = getEventDetails(mockPendingData);
    expect(result).toMatchObject({ updatedEventDetails: expectedObj });
  });
});
