import { renderHook, act } from "@testing-library/react";
import useLeadDistributors from "./useLeadDistributors";
import { Provider } from "react-redux";
import { app_store } from "@me/data-rtk";
import * as selectors from "@me/data-rtk";
import { getMainAndOtherVendors } from '../../all-allowances/allowance-lead-distributors/billing-selection-utils';

describe("useLeadDistributors", () => {
  const wrapper = ({ children }) => (
    <Provider store={app_store}>{children}</Provider>
  );
  beforeEach(() => {
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      switch (sel_name) {
        case "initialLeadDistSelected_rn":
          return {
            data: {
              leadDistData: [],
            },
          };
          case "excludedVendorForAllowance_rn":
          return {
            data: {
              excludedVendors: [],
            },
          }
          case "allowanceTableData_rn":
            return {
              data: {
                tableData: []
              }
            }
        case "leadDistributors_rn":
          return {
            data: {
              leadDistMode: null,
            },
          };
          return {
            data: {
              0: { vendorNbr: "123", isExclude: true },
              excludedVendors: ["123"],
            },
          };
        default:
          break;
      }
    });
  });
  it("should update selected list correctly", () => {
    const { result } = renderHook(
      () => {
        return useLeadDistributors({
          leadOptions: ["option1", "option2", "option3"],
        });
      },
      { wrapper }
    );
    expect(result.current.selectedList).toEqual([]);

    act(() => {
      result.current.updateSelected({ id: "option1" });
    });
    expect(result.current.selectedList).toEqual(["option1"]);

    act(() => {
      result.current.updateSelected({ id: "option2" });
    });
    expect(result.current.selectedList).toEqual(["option1", "option2"]);

    act(() => {
      result.current.updateSelected({ id: "option1" });
    });
    expect(result.current.selectedList).toEqual(["option2"]);
  });
  xit("should move child from one set to another correctly", () => {
    const { result } = renderHook(() =>
      useLeadDistributors({ leadOptions: ["option1", "option2", "option3"] })
    );
    expect(result.current.selectedList).toEqual([]);

    act(() => {
      result.current.updateSelected({ id: "option1" });
      result.current.updateSelected({ id: "option2" });
    });
    expect(result.current.selectedList).toEqual(["option1", "option2"]);

    act(() => {
      result.current.includeToVendor({ id: "option2", direction: "DOWN" });
    });
    expect(result.current.stepData).toEqual([
      { id: "option1", child: ["option3"] },
      { id: "option2", child: ["option2"] },
    ]);

    act(() => {
      result.current.includeToVendor({ id: "option2", direction: "UP" });
    });
    expect(result.current.stepData).toEqual([
      { id: "option1", child: ["option3", "option2"] },
    ]);

    act(() => {
      result.current.includeToVendor({ id: "option1", direction: "DOWN" });
    });
    expect(result.current.stepData).toEqual([
      { id: "option1", child: ["option3"] },
      { id: "option2", child: [] },
    ]);

    act(() => {
      result.current.includeToVendor({ id: "option2", direction: "DOWN" });
    });
    expect(result.current.stepData).toEqual([
      { id: "option1", child: ["option3"] },
      { id: "option2", child: ["option2"] },
    ]);
  });
});
