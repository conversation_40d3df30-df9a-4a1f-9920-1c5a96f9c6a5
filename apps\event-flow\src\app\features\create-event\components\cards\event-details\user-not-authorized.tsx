import { useNavigate } from "react-router-dom";
import { useGetAppBasePath } from "@me/util-helpers";
import WarningModal from "../../../../event-types/components/event-header/warning-modal";

const UserNotAuthorized = isOpen => {
  const navigate = useNavigate();
  const { basePath } = useGetAppBasePath();
  const navigateToTaskView = () => {
    navigate(`${basePath}/`);
  };
  return (
    <WarningModal
      onChange={navigateToTaskView}
      isOpen={isOpen}
      setOpen={() => {}}
      warningTitle="The user isn't Authorized!"
      warningBodyText="The user does not have permissions to proceed."
      warningLabel="Return to Task View?"
      showCancelBtn={false}
      cancelButtonLabel="No, Continue Editing"
      confirmButtonLabel="Yes, Return to Task View"
      onCloseHandler={navigateToTaskView}
    />
  );
};
export default UserNotAuthorized;
