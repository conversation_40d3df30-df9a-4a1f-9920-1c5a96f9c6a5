import { InputSelect } from "@me/input-fields";
import { memo } from "react";
import { useFormContext } from "react-hook-form";
import { getDateInTimeFormat } from "../../../../../service/event-details/event-detail-service";
import { parseISO } from "date-fns";
import { useLocation, useParams } from "react-router-dom";
import _ from "lodash";
import { EEVENT_STATUS } from "@me/util-helpers";
import { useSelectorWrap } from "@me/data-rtk";
import { useGetVehicleByTypeAndYearLazyQuery } from "apps/event-flow/src/app/graphql/generated/schema";
import { getFieldNameValue } from "../../utility/utility";
import { validateNCDP } from "../../event-details-card-service";

function StartWeekVehicle({
  startWeek,
  formFields,
  setFormFields,
  show,
  setVehicleTypeFieldChanged,
}) {
  const { setValue, getValues, clearErrors } = useFormContext();
  const location = useLocation();
  const state: any = location.state || {};
  const isCustomDate = ["Custom Date", "CustomDate"].includes(
    formFields?.vehicleTypeProps
  );

  const { data: eventTypeAndDivisionsData } = useSelectorWrap(
    "event_type_and_divisions"
  );
  const eventType = eventTypeAndDivisionsData?.eventType || "";

  const vehicleTypeId =
    getValues("eventCreationVehicle.vehicleType.vehicleTypeId") ||
    getValues("eventCreationVehicle.vehicleType.id");
  const [getVehicle, { data: vehiclesData }] =
    useGetVehicleByTypeAndYearLazyQuery({
      variables: {
        vehicleTypId: vehicleTypeId,
        year: getValues("eventCreationVehicle.year"),
      },
    });
  const startWeekData = startWeek;
  startWeekData.tabType = formFields?.vehicleTypeProps;
  startWeekData.options =
    (vehiclesData &&
      vehiclesData?.getVehicleByTypeAndYear?.map(vehicle => ({
        id: vehicle?.id,
        name: vehicle?.vehicleNm,
        ...vehicle,
      }))) ||
    startWeek.options;
  const { id: eventId } = useParams();
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const promoActiveStatus = () => {
    const promotionsList =
      eventDetailsData?.promotionsLists?.[0]?.promotionsList;
    return promotionsList?.some(
      promotion => promotion?.promotionWorkflowStatus === EEVENT_STATUS?.ACTIVE
    );
  };
  const findDefaultIndex = items => {
    return items?.findIndex(item =>
      !getValues("startWeekVehicle")
        ? item?.isDefault
        : item?.vehicleNm === getValues("startWeekVehicle")
    );
  };

  const findElement = event => {
    return (event.target as HTMLElement).closest(".z-index-dynamic-class");
  };

  const addClassToElement = elm => {
    if (!elm?.classList?.contains("z-[9]")) {
      elm?.classList?.add("z-[9]", "z-index-added");
    }
  };

  const findGridDiv = () => {
    return document.querySelector(
      ".inputSelectstartWeekVehicle [aria-label=grid]"
    );
  };

  const scrollToElement = (gridDiv, indexDefault, items) => {
    const height = 46;
    gridDiv.scrollTop = gridDiv.scrollTop + (indexDefault * (height - 4));
  };

  const findDeep = (node, text) => {
    if (node?.textContent === text) return node;
    let result = null;
    for (let i = 0; node && i < node?.childNodes?.length; i++) {
      result = findDeep(node?.childNodes?.[i], text);
      if (result) break;
    }
    return result;
  };

  const disconnectObserverIfTargetFound = (
    gridDiv,
    indexDefault,
    items,
    observer
  ) => {
    const targetNode = findDeep(gridDiv, items?.[indexDefault]?.name);
    if (targetNode) {
      gridDiv.scrollTop = targetNode.offsetTop;
      observer.disconnect();
    }
  };
  const addClickToStartWeek = (event, items) => {
    event?.stopPropagation();
    const indexDefault = findDefaultIndex(items);
    if (indexDefault == -1) return;
    let scrollCompleted = false;
    const observer = new MutationObserver(() => {
      if (scrollCompleted) {
        observer.disconnect();
        return;
      }
      const gridDiv = findGridDiv();
      if (gridDiv) {
        scrollToElement(gridDiv, indexDefault, items);
        disconnectObserverIfTargetFound(gridDiv, indexDefault, items, observer);
        scrollCompleted = true;
      }
    });
    observer.observe(document, { childList: true, subtree: true });
  };

  const onVehicleStartWeekHandler = ele => {
    setValue(startWeek.registerField, ele);
    if (!isCustomDate) {
      setFormFields(prevState => {
        return {
          ...prevState,
          customStartDate: ele?.startDate,
          customEndDate: ele?.endDate,
        };
      });
      // setCustomStartDate(ele?.startDate);
      // setCustomEndDate(ele?.endDate);
      setValue("startDate", ele?.startDate);
      setValue("endDate", ele?.endDate);
    } else {
      const startEventDate = getValues("startDate");
      const endEvenetDate = getValues("endDate");
      setFormFields(prevState => {
        return {
          ...prevState,
          customStartDate: startEventDate,
          customEndDate: endEvenetDate,
        };
      });
      // setCustomStartDate(startEventDate);
      // setCustomEndDate(endEvenetDate);
      setValue("startDate", startEventDate);
      setValue("endDate", endEvenetDate);
      setValue(
        "eventCreationVehicle.startDate",
        getDateInTimeFormat(startEventDate)
      );
      setValue(
        "eventCreationVehicle.endDate",
        getDateInTimeFormat(endEvenetDate)
      );
    }
    setValue("startWeekVehicle", ele?.vehicleNm);
    const getTheStartYear = getValues("startDate");
    const yearValue = getTheStartYear ? parseISO(getTheStartYear) : new Date();
    setFormFields(prevState => {
      return { ...prevState, yearVal: yearValue?.getFullYear().toString() };
    });
    // setYearVal(yearValue.getFullYear().toString());

    setValue("eventCreationVehicle.year", yearValue?.getFullYear()?.toString());
    const formEventName = getFieldNameValue(state, eventDetailsData, getValues);
    eventId && setValue("name", formEventName || eventDetailsData?.name);
    setVehicleTypeFieldChanged(true);
    clearErrors(["startDate", "endDate", "name", "startWeekVehicle"]);
  };
  return (
    <div className="component-scroll w-3/12 start-week-container">
      {startWeek ? (
        <InputSelect
          onClick={addClickToStartWeek}
          onChange={onVehicleStartWeekHandler}
          fieldProps={startWeekData}
          disabled={validateNCDP(eventType) || show}
          props={formFields?.vehicleTypeId}
          enableStartWeekVehicle={formFields?.enableStartWeekVehicle}
          hideVehicles={!formFields?.isPastDate && promoActiveStatus()}
          isNCDP={validateNCDP(eventType)}
        ></InputSelect>
      ) : null}
    </div>
  );
}

export default memo(StartWeekVehicle);
