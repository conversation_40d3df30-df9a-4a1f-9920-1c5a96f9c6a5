import { FieldValues, UseFormGetValues } from "react-hook-form";
import efConstants from "../../../../shared/ef-constants/ef-constants";
import { IPerformanceProps } from "../../components/cards/allowance/stepper/allowance-type-performance/allowance-type-performance.model";
import {
  ALLOWANCE_TO_BE_CREATED_CD_MAPPER,
  EVENT_ALLOWANCE,
} from "../../constants/fields/allowance/allowance-steps-config";
import { SHOW_ALLOWANCE_TO_BE_CREATED_OPTIONS } from "../../constants/fields/allowance/field-allowance-to-be-created";
import { PROMOTION_TYPES } from "../../constants/promotion/types";
import { setAllowanceFormInfo } from "../slice/allowance-details-slice";
import { getAllowTypeConfig } from "../../../all-allowances/all-allowances-container/text-mapping";
import { getLoggedInUserType } from "@me/util-helpers";
import { EEVENT_STATUS } from "../../../event-types/components/event-action/event-action.model";
import { getObjectKeys } from "./allowance-stepper-service";
import { setApiErrorResponse } from "@me/data-rtk";
import { getIsNationalEvent } from "../../../event-types/event-types-helper";
import { getNationalOfferStatus } from "@me-upp-js/utilities";

interface IUpdateAllowanceFormProps {
  getValues: (regField: string) => object;
  allowanceRegField: string;
  setValue: (regField: string, tempObj: object) => void;
  updateFormValuesObj: object;
}

const {
  ALLOWANCE_TYPES,
  PRODUCT_SOURCE_DATA,
  PRODUCT_SOURCE_INFO,
  DEFAULT_ALLOWANCE_CREATED_TYPE,
  DEFAULT_PRODUCT_SOURCE,
  BOTH_KEY,
  UOM_VALUES,
  ALLOWANCE_SCREEN_TYPES,
  UOM_KEY_VALUE_MAPPER,
  OFFER_ALLOWANCE_KEY_MAPPING,
  EXCLUDED_OFFER_PROMO_STATUS,
} = efConstants;
const { SCAN, CASE, SHIPTOSTORE, HEADERFLAT, ITEMFLAT } = ALLOWANCE_TYPES;

export const getAllowanceTypes = () => {
  return Object.keys(ALLOWANCE_TYPES).map(key => ALLOWANCE_TYPES[key].key);
};

export const getAllowaceTypeByCreateInd = (createInd: string) => {
  return ALLOWANCE_TYPES?.[
    Object.keys(ALLOWANCE_TYPES).find(key =>
      ALLOWANCE_TYPES[key].createInd?.includes(createInd)
    ) || ""
  ]?.key;
};

const removeSpace = (key: string) => key?.split(" ")?.join("") || "";

const removeUnderscore = (key: string) => key?.split("_")?.join("") || "";

export const getAllowanceMapKey = (key: string) => {
  return ALLOWANCE_TYPES?.[removeUnderscore(key)]?.allowMapKey || "";
};

export const getAllowanceMapValue = (key: string) => {
  return ALLOWANCE_TYPES?.[removeUnderscore(key)]?.value || "";
};

export const getAllowanceMapLabel = (key: string) => {
  return ALLOWANCE_TYPES?.[removeUnderscore(key)]?.label || "";
};

export const getAllowanceKey = (key: string) => {
  return ALLOWANCE_TYPES?.[removeSpace(key)]?.key || "";
};

const getDefaultCreateInd = (allowanceType: string) => {
  return ALLOWANCE_TYPES?.[removeSpace(allowanceType)]?.createInd?.[0];
};

export const getAllowancePerfOption = (
  allowanceType: string,
  eventType: string,
  createInd?: string
) => {
  return (
    ALLOWANCE_TYPES?.[removeSpace(allowanceType)]?.defaultPerfOption?.[
      eventType
    ]?.[createInd || getDefaultCreateInd(allowanceType)] || ""
  );
};

export const getAllowanceCaseTypeValue = (key: string) => {
  return ALLOWANCE_TYPES?.[removeUnderscore(key)]?.label || "";
};

export const updateAllowanceFormValues = ({
  getValues = () => {
    return {};
  },
  allowanceRegField,
  setValue,
  updateFormValuesObj,
}: IUpdateAllowanceFormProps) => {
  setValue(allowanceRegField, {
    ...(getValues(allowanceRegField) || {}),
    ...(updateFormValuesObj || {}),
  });
};
export const hideDependingFieldOnValue = ({
  fields,
  val,
  isCurrent,
  dependingFields,
}) => {
  if (val && isCurrent) {
    dependingFields?.forEach(dependFieldObj => {
      const dependentFieldObj = fields?.find(
        fieldObj => fieldObj.key === dependFieldObj.key
      );
      if (dependentFieldObj) {
        dependentFieldObj.conditionalShow = true;
        if (dependFieldObj?.value === val) {
          dependentFieldObj.conditionalShow = true;
        } else {
          dependentFieldObj.conditionalShow = false;
        }
      }
    });
  }
};
export const getAllowanceFormRegisterKey = (
  cardIndex: number,
  cardItemIndex: number
) => `offerAllowances[${cardIndex}].allowances[${cardItemIndex}]`;

export const getOfferFormRegisterKey = (cardIndex: number) =>
  `offerAllowances[${cardIndex}]`;

export const getAllowanceTypeByPerformance = (
  performance: IPerformanceProps
) => {
  const { allowanceCd, perfCode1, perfCode2 } = performance || {};
  switch (allowanceCd) {
    case CASE.allowanceCd:
      return CASE.label;

    case SCAN.allowanceCd:
      return SCAN.label;

    case SHIPTOSTORE.allowanceCd:
      return SHIPTOSTORE.label;

    case HEADERFLAT.allowanceCd:
      if (perfCode1?.trim()) return HEADERFLAT.label;
      else if (perfCode2?.trim()) return ITEMFLAT.label;
      return "";

    default:
      return "";
  }
};

export const getPerformanceRecordById = (
  performance: IPerformanceProps,
  allowanceData
) => {
  return allowanceData?.getAllowancePerformanceTypes?.filter(
    option => option?.id === performance?.allwPerfId
  )?.[0];
};

const getAmountItemsKey = (isCreate = false, isHeaderFlat = false) => {
  return isHeaderFlat ? "headerFlatAmt" : isCreate ? "allowanceAmount" : null;
};

const getItemsFromAllowane = allowances => {
  return allowances?.reduce((itemsData = [], item) => {
    return [...itemsData, ...(item?.allowanceItems || [])];
  }, []);
};

const getCombinedAllowanceItems = allowances => {
  return allowances?.length
    ? allowances.reduce((itemsData = [], item) => {
        return [...itemsData, ...(item?.allowanceItems || [])];
      }, [])
    : [];
};

export const getOfferAllowanceAmount = (
  allowanceType,
  allowances,
  isHeaderFlat
) => {
  if (!allowances?.length) return null;
  allowances = allowances?.filter(allowance => allowance?.includeInd);
  return getAllowanceAmountsSubHeaderValue(
    allowanceType,
    isHeaderFlat ? allowances : getCombinedAllowanceItems(allowances),
    false
  );
};

export const generateOfferAndAllowanceName = responseData => {
  const allowanceType = getAllowanceTypeByPerformance(
      responseData?.allowances?.[0]?.performance
    ),
    performanceType =
      responseData?.allowances?.[0]?.performance?.performance || "";
  const header: string[] = [
    `Offer # ${responseData?.offerNumber} - ${allowanceType} - ${performanceType} -`,
  ];
  const allowances = responseData?.allowances;
  const vehicleDesc = allowances?.[0]?.vehicle?.vehicleType?.vehicleTypDesc;
  const vehilcleNm = allowances?.[0]?.vehicle?.vehicleNm;
  const isHeaderFlat = allowanceType === HEADERFLAT.label;
  header.push(
    getOfferAllowanceAmount(allowanceType, allowances, isHeaderFlat) || ""
  );

  if (vehilcleNm && vehicleDesc) {
    header.push(`- ${vehicleDesc} - ${vehilcleNm}`);
  }
  return header.join(" ");
};

export const getAllowAmtLabel = (
  allowanceType,
  allowances,
  isHeaderFlat,
  isCreate
) => {
  if (!allowances?.length) return null;
  allowances = allowances?.filter(allowance => allowance?.includeInd);
  return getAllowanceAmountsSubHeaderValue(
    allowanceType,
    isHeaderFlat ? allowances : getCombinedAllowanceItems(allowances),
    isCreate
  );
};

export const getAllowAmtLabelAcrossDiv = ({
  allowanceType,
  isHeaderFlat,
  isCreate,
  allDivAllowResp,
}) => {
  const mergedAllowances = allDivAllowResp?.flatMap(
    ({ allowances }) =>
      allowances?.filter(allowance => allowance?.includeInd) || []
  );
  return getAllowAmtLabel(
    allowanceType,
    mergedAllowances,
    isHeaderFlat,
    isCreate
  );
};

export const formatTimestampToDate = (timestamp, isMonthLongFormat = false) => {
  const date = new Date(timestamp);
  let month = (date.getMonth() + 1).toString();
  month = month.length > 1 ? month : `0${month}`;
  const day =
    date.getDate().toString().length > 1
      ? date.getDate()
      : `0${date.getDate()}`;
  return isMonthLongFormat
    ? `${day} ${date.toLocaleString("default", {
        month: "long",
      })}, ${date.getFullYear()}`
    : `${date.getFullYear()}-${month}-${day}`;
};

export const getDefaultAllowanceType = () => {
  return SCAN.value;
};

export const getAllowanceSteppers = () => {
  return EVENT_ALLOWANCE.steppers;
};

export const getUniqueUomTypes = (allowanceItems, uomKey) => {
  return allowanceItems?.length
    ? allowanceItems?.reduce((uomItems, item) => {
        uomItems[item?.[uomKey]] = [...(uomItems[item?.[uomKey]] || []), item];
        return uomItems;
      }, {})
    : [];
};

export const getAllowanceMinMaxAmount = (
  allowanceItems,
  isCreate = false,
  isHeaderFlat = false
) => {
  const key = getAmountItemsKey(isCreate, isHeaderFlat);
  const allowanceAmountsData: number[] = allowanceItems?.map((item: any) =>
    key ? item?.[key] || 0 : item?.allowanceItemComps?.[0]?.allowAmount || 0
  );

  const maxAmount = Math.max(...allowanceAmountsData),
    minAmount = Math.min(...allowanceAmountsData);

  if (minAmount === maxAmount) return `$${formatAmount(maxAmount)}`;
  return `$${formatAmount(minAmount)} - $${formatAmount(maxAmount)}`;
};

export const getAmountLevel = (amountInfo: string[] | [] = []) => {
  const amountLevel = amountInfo?.[0]?.split(" ");
  if (amountLevel?.length > 2) {
    amountLevel?.pop();
    return amountLevel?.join(" ");
  }
  return amountLevel?.[0];
};

export const getAllowanceAmountsSubHeaderValue = (
  allowanceType = "",
  allowanceItems,
  isCreate = false,
  isUomVisable = true
) => {
  if (!allowanceItems?.length) return "";
  const { HEADERFLAT, ITEMFLAT } = ALLOWANCE_TYPES;
  const isHeaderFlat = [HEADERFLAT.label, HEADERFLAT.key].includes(
    allowanceType
  );

  //Generates amount sub header for IF & HF
  if (isHeaderFlat || [ITEMFLAT.key, ITEMFLAT.label].includes(allowanceType)) {
    return getAllowanceMinMaxAmount(allowanceItems, isCreate, isHeaderFlat);
  }

  //Generates amount sub header for SCAN, CASE & S2S
  const uomKey = isCreate ? "allowUomType" : "uom";
  const uniqueItemsByUom = getUniqueUomTypes(allowanceItems, uomKey);

  const amountInfo = getObjectKeys(uniqueItemsByUom)?.map(uomKey => {
    return `${getAllowanceMinMaxAmount(
      uniqueItemsByUom?.[uomKey],
      isCreate,
      isHeaderFlat
    )} ${UOM_KEY_VALUE_MAPPER?.[uomKey]}`;
  });

  //concat and return the result based on isUomVisable and amountInfo
  const sublevelValue =
    amountInfo.length <= 1
      ? isUomVisable
        ? amountInfo?.[0]
        : getAmountLevel(amountInfo)
      : amountInfo.join(" and ");

  return sublevelValue;
};

export const getAllowanceCreatedOptions = (
  allowanceTypeName: string,
  productInfoSource?: string
) => {
  if (
    allowanceTypeName &&
    getAllowanceTypes().includes(allowanceTypeName) &&
    productInfoSource
  ) {
    return SHOW_ALLOWANCE_TO_BE_CREATED_OPTIONS[allowanceTypeName][
      productInfoSource
    ];
  }
  return [];
};

export const handleRequireFieldsValidation = (
  getValues: UseFormGetValues<FieldValues>,
  requiredFields: string[]
) => {
  const values = requiredFields?.find((field: string) => {
    if (getValues(field)?.toString()) return false;
    return true;
  });
  return !values?.length;
};

export const getProductSourceKey = (productSources: string[]) => {
  return productSources?.length
    ? productSources.length > 1
      ? BOTH_KEY
      : productSources?.[0] || ""
    : BOTH_KEY;
};

//filter steps based on selected performance option createInd value
export const getStepsByAllowanceCreateInd = (
  steppers: string[],
  allowanceScreenType: string,
  allowanceType: string,
  defaultCreateInd?: string
) => {
  if (HEADERFLAT.key === allowanceType)
    return steppers.filter(
      (step: string) =>
        !EVENT_ALLOWANCE[step]?.disable?.allowanceScreenType?.includes(
          allowanceScreenType
        ) &&
        !EVENT_ALLOWANCE[step]?.disable?.createInd?.includes(defaultCreateInd)
    );
  else
    return steppers.filter(
      (step: string) =>
        !EVENT_ALLOWANCE[step]?.disable?.createInd?.includes(defaultCreateInd)
    );
};

//get steps based on product source
const getStepsByProductSource = (
  productSourceType: string,
  steppers: string[]
) => {
  return steppers.filter((step: string) =>
    EVENT_ALLOWANCE[step].allowanceToBeCreated.includes(
      PRODUCT_SOURCE_DATA?.[productSourceType].value
    )
  );
};

//filter steps based on selected allowance created option
const getStepsByAllowanceCreatedType = (
  allowanceCreatedType: string,
  steppers: string[]
) => {
  return steppers.filter((step: string) =>
    EVENT_ALLOWANCE[step].allowanceToBeCreated.includes(allowanceCreatedType)
  );
};

const getSteppersBySelectedAllowanceType = (
  allowanceType: string,
  allowanceCreatedType: string,
  steps: string[],
  productSource?: string[]
) => {
  const productSourceConfigKeys = PRODUCT_SOURCE_INFO?.[allowanceType];
  if (
    allowanceCreatedType &&
    allowanceCreatedType === productSourceConfigKeys?.WAREHOUSE?.value
  ) {
    //filter steps based on allowance created
    return getStepsByProductSource(
      DEFAULT_PRODUCT_SOURCE?.[allowanceType],
      steps
    );
  }

  //filter steps based on product source and also allowance created if present
  if (productSource?.length === 1) {
    return getStepsByProductSource(productSource[0], steps);
  } else if (
    productSource?.length === 2 &&
    productSource.includes(productSourceConfigKeys.WAREHOUSE.key) &&
    productSource.includes(productSourceConfigKeys.DSD.key)
  ) {
    return getStepsByProductSource(BOTH_KEY, steps);
  }

  //get default steps if product source is empty
  if (allowanceCreatedType) {
    return getStepsByAllowanceCreatedType(allowanceCreatedType, steps);
  } else {
    return getStepsByProductSource(
      DEFAULT_PRODUCT_SOURCE?.[allowanceType],
      steps
    );
  }
};

export const getSteppersByAllowanceType = (
  steps: string[],
  allowanceType: string,
  allowanceCreatedType: string,
  allowanceScreenType: string,
  productSource?: string[],
  defaultCreateInd?: string
) => {
  if (!allowanceCreatedType)
    allowanceCreatedType =
      DEFAULT_ALLOWANCE_CREATED_TYPE?.[allowanceType] || "";

  if ([HEADERFLAT.key, ITEMFLAT.key].includes(allowanceType)) {
    return getStepsByAllowanceCreateInd(
      steps,
      allowanceScreenType,
      allowanceType,
      defaultCreateInd
    );
  } else if (allowanceType === SCAN.key) {
    return getSteppersBySelectedAllowanceType(
      allowanceType,
      allowanceCreatedType,
      steps,
      productSource
    );
  } else if (
    [CASE.key, SHIPTOSTORE.key].includes(allowanceType) &&
    allowanceCreatedType
  ) {
    return getStepsByAllowanceCreatedType(allowanceCreatedType, steps);
  } else {
    return getStepsByProductSource(
      DEFAULT_PRODUCT_SOURCE?.[allowanceType],
      steps
    );
  }
};

function getStepsByAllowanceScreenAndType(
  allowanceType: string,
  allowanceScreenType: string
) {
  return EVENT_ALLOWANCE.steppers.filter(step => {
    const { allowanceTypeAndPerformance = [], allowanceScreenTypes = [] } =
      EVENT_ALLOWANCE[step];
    return (
      allowanceTypeAndPerformance.includes(allowanceType) &&
      allowanceScreenTypes.includes(allowanceScreenType)
    );
  });
}

//get steppers based on allowance type, allowance created type and product source
export const getSteppers = (
  allowanceScreenType = ALLOWANCE_SCREEN_TYPES.DP.key,
  allowanceType: string,
  allowanceCreatedType: string,
  productSource?: string[],
  defaultCreateInd?: string
) => {
  //filter steps based on allowanceType and allowanceScreenType
  let steps = getStepsByAllowanceScreenAndType(
    allowanceType,
    allowanceScreenType
  );

  steps = getSteppersByAllowanceType(
    steps,
    allowanceType,
    allowanceCreatedType,
    allowanceScreenType,
    productSource,
    defaultCreateInd
  );
  return steps;
};

export const getUniqueItems = (data: { key: string }[], key: string) => {
  const items = data.flatMap((item: { key: string }) => item?.[key]);
  return [...new Set(items)];
};

export const getUomFieldValue = (value: string) => {
  return value === UOM_VALUES.WEIGHT ? UOM_VALUES.DEFAULT_LABEL : value;
};

export const handleSkipStep = (
  allowanceType: string,
  productSources: string[],
  productSource: string
) => {
  switch (allowanceType) {
    case CASE.key:
      return (
        productSources.length === 1 &&
        productSource !== PRODUCT_SOURCE_INFO?.[allowanceType]?.DSD?.key
      );

    case SCAN.key:
    case SHIPTOSTORE.key:
      return !(
        productSources.length === 1 &&
        productSource === PRODUCT_SOURCE_INFO?.[allowanceType]?.DSD?.key
      );

    default:
      return false;
  }
};

export const getVehicleDatesMapData = (
  key: string,
  { vehicleRef, dateRange },
  previousVehicleData = {}
) => {
  const { startDate, endDate } = vehicleRef || {};

  return {
    ...previousVehicleData,
    [key]: {
      vehicleId: vehicleRef?.vehicleId,
      vehicleRef,
      dateRange: dateRange || {
        startDate,
        endDate,
      },
    },
  };
};

export const getProductSourceByOfferKey = (
  offerGroupKey = "",
  allowanceType = ""
) => {
  const { productGroups = [], allowanceToBeCraetedOption = "" } =
    OFFER_ALLOWANCE_KEY_MAPPING?.[offerGroupKey] || {};
  return {
    productGroups,
    allowanceToBeCraetedLabel: allowanceToBeCraetedOption?.[allowanceType],
  };
};

export const payload_AllowancesItemsQuery_entry = (
  isSummary = false,
  tempWorkAllowanceId: string
) => {
  const queryString = window.location.search;
  const urlParams = new URLSearchParams(queryString);
  return [
    {
      URL_PARAMS: [urlParams.get("eventId"), urlParams.get("group")],
    },
    {
      refetchOnMountOrArgChange: true,
      skip: !urlParams.get("eventId") || isSummary || !tempWorkAllowanceId,
    },
  ];
};

export const getAllowanceTempWorkDataReqBody = ({ tempData }) => {
  const queryString = window.location.search;
  const urlParams = new URLSearchParams(queryString);
  const eventId = urlParams.get("eventId");

  return [
    {
      URL_PARAM: eventId,
      queryParams: {},
    },
    {
      skip: !(eventId || tempData?.allowanceData),
    },
  ];
};

export const saveAllowancesItemReqBody = ({
  allowancesRespCopy,
  allowanceTempWorkData,
  isEditEnable = false,
}) => {
  const { offerAllowancesGroup, eventId } = allowancesRespCopy,
    { allowanceType } = allowanceTempWorkData,
    type = efConstants.ALLOWANCES_TYPE_MAPPNG[allowanceType];

  allowanceTempWorkData["allowanceTypeSpecification"][type]["allowancesMap"][
    offerAllowancesGroup
  ] = allowancesRespCopy?.allowances;

  return {
    URL_PARAM: eventId,
    ...allowanceTempWorkData,
  };
};

export function formatAmount(val: any, decimalPlaces = 2) {
  if (isNaN(val) || val === null) return "";
  return parseFloat(val).toFixed(decimalPlaces);
}

export const sortAllowanceById = allowances => {
  const sortedAllow = allowances?.sort(
    (a, b) => a?.allowanceIdNbr - b?.allowanceIdNbr
  );
  return sortedAllow;
};

/**
 *
 * @param allowanceType SCAN | CASE | ....
 * @param UOM_OPTIONS
 * @param switchValue Unit | Case
 * @returns  UOM Options depends on allowance type.
 *  For Example, In SCAN there is no switching so get default UOM Options from configuration
 * But In CASE, UOM options depend on switch value selected so this returns the UOM options
 */
export function getUOMBasedOnType(
  allowanceType: any,
  UOM_OPTIONS: any,
  switchValue: string
) {
  if (allowanceType) {
    const { isSwitching, defaultOptions, switchCaseOptions } =
      UOM_OPTIONS?.[allowanceType] || {};
    return !isSwitching ? defaultOptions : switchCaseOptions[switchValue];
  }
  return [];
}

/**
 *
 * @param allowanceTempWorkData
 * @returns return the allowance type
 */
export function getAllowanceTypeFromTempWork(allowanceTempWorkData) {
  return allowanceTempWorkData.data?.allowanceData?.allowanceType || SCAN.key;
}

/**
 *
 * @returns
 * This function based on allowance type and switching case returns the key to check for minValue
 * For Scan it is unitListCost
 * For Case its depend on swith,  either caseListCost or unitListCost based on switch Value
 */
export function getMinListCostKey({
  allowanceType,
  SWITCH_OPTIONS,
  switchValue,
  allowanceTempWorkData,
}) {
  const allowData = allowanceTempWorkData?.data?.allowanceData;
  const tempData =
    allowData && Array.isArray(allowData) ? allowData?.[0] : allowData;
  const defaultKey = getAllowTypeConfig({
    allowanceTempWorkData: tempData,
  })?.newCostCalculateKey;

  if (allowanceType) {
    const { isSwitching, switchOptions } =
      SWITCH_OPTIONS?.[allowanceType] || {};
    return !isSwitching
      ? defaultKey
      : switchOptions[switchValue?.selectedSwitch]?.allowanceAllowKey;
  }
  return defaultKey;
}

export function getUOMFromNameOrValue({ name = "", value = "" }) {
  const key = name ? "name" : "value";
  const uomValueObj: any = Object.entries(UOM_KEY_VALUE_MAPPER)
    .map(([value, name]) => ({ value, name }))
    .find(option => option[key] === (name || value));
  return uomValueObj;
}
export const getAllowAmtOrUom = (
  param,
  isCurrent,
  fieldObj,
  isUomVisible = false
) => {
  const loggedinuser = getLoggedInUserType();
  if (!fieldObj) return "";
  const {
    eventStatus,
    indicatorValue,
    historyMapperKey,
    searchId,
    planEventPendingChanges,
    allowPromoStatus,
    getValues,
    allowId,
  } = fieldObj || {};
  const {
    itemsFromPlan,
    allowObjFromPlan,
    allowObjFromPending,
    isHeaderFlat,
    itemsFromPending,
    allowanceType,
    isNational = false,
  } = getAllowanceObjForAmt(getValues, searchId, allowId);

  if (param && eventStatus === EEVENT_STATUS.DRAFT) {
    return getValueForAmtUom(
      itemsFromPlan,
      getCurrentAllowObj(allowObjFromPlan, allowId),
      isHeaderFlat,
      historyMapperKey?.includes("uom"),
      isUomVisible,
      allowanceType
    );
  }

  if (
    param &&
    indicatorValue &&
    (efConstants.STATUS_USER_MAPPER?.[loggedinuser] === allowPromoStatus ||
      [
        EEVENT_STATUS.DRAFT,
        EEVENT_STATUS.EXECUTED,
        EEVENT_STATUS.AGREED,
        EEVENT_STATUS.AGREED_PENDING,
        EEVENT_STATUS.ACTIVE,
      ].includes(allowPromoStatus))
  ) {
    const planPendinChngObj =
      planEventPendingChanges?.offerAllowanceChanges?.find(
        obj => obj?.offerNumber === searchId
      );
    const isKeyExist = planPendinChngObj?.changes?.find(
      changeObj => changeObj?.labelFieldName === historyMapperKey
    );
    const lastUpdateUserFromPendingChange = planPendinChngObj?.changes?.find(
      changeObj =>
        changeObj?.labelFieldName === "offerallowances.updateUser.type"
    );
    const { afterVal: latestLastUpdateUserType } =
      lastUpdateUserFromPendingChange || {};
    const lastUpdateUserType = allowObjFromPending?.updateUser?.type,
      lastUpdateUserToCheck = isNational
        ? latestLastUpdateUserType || lastUpdateUserType
        : lastUpdateUserType,
      isSameUserUpdate =
        lastUpdateUserToCheck?.toLowerCase() === loggedinuser?.toLowerCase();
    if (isHeaderFlat && !isSameUserUpdate) {
      return getValueForAmtUom(
        isCurrent ? itemsFromPlan : itemsFromPending,
        getCurrentAllowObj(
          isCurrent ? allowObjFromPlan : allowObjFromPending,
          allowId
        ),
        isHeaderFlat,
        historyMapperKey?.includes("uom"),
        isUomVisible,
        allowanceType
      );
    }

    if (isKeyExist) {
      const items = isCurrent ? itemsFromPending : itemsFromPlan;
      const offerAllowances = isCurrent
        ? allowObjFromPending
        : allowObjFromPlan;
      return getValueForAmtUom(
        items,
        getCurrentAllowObj(offerAllowances, allowId),
        isHeaderFlat,
        historyMapperKey?.includes("uom"),
        isUomVisible,
        allowanceType
      );
    }
  }
  return getValueForAmtUom(
    isCurrent ? itemsFromPlan : itemsFromPending,
    getCurrentAllowObj(
      isCurrent ? allowObjFromPlan : allowObjFromPending,
      allowId
    ),
    isHeaderFlat,
    historyMapperKey?.includes("uom"),
    isUomVisible,
    allowanceType
  );
};
const getCurrentAllowObj = (offerAllow, allowId) => {
  const currentAllowObj = offerAllow?.allowances?.find(
    allowObj => allowObj.allowanceIdNbr == allowId
  );
  return currentAllowObj ? [currentAllowObj] : offerAllow;
};
export const getAllowanceObjForAmt = (getValues, searchId, allowId = null) => {
  const { planEvent, planEventPending } = getValues();
  const curentRenderOfferObj = getValues(`offerAllowances`)?.find(
      obj => obj?.offerNumber === searchId
    ),
    { isNDPAndFlagEnabled: isNational } = getIsNationalEvent(
      planEvent?.eventType
    ),
    performanceValue = curentRenderOfferObj?.allowances[0]?.performance,
    allowanceType = getAllowanceTypeByPerformance(performanceValue),
    isHeaderFlat = allowanceType === HEADERFLAT.label,
    allowObjFromPlan =
      planEvent?.offerAllowances?.find(obj => obj?.offerNumber === searchId) ||
      curentRenderOfferObj,
    allowObjFromPending = planEventPending?.offerAllowances?.find(
      obj => obj?.offerNumber === searchId
    ),
    itemsFromPlan = getItemsFromAllowObject(
      allowObjFromPlan?.allowances,
      allowId
    ),
    itemsFromPending = getItemsFromAllowObject(
      allowObjFromPending?.allowances,
      allowId
    );

  return {
    itemsFromPlan,
    allowObjFromPlan,
    allowObjFromPending,
    isHeaderFlat,
    itemsFromPending,
    allowanceType,
    isNational,
  };
};
const getItemsFromAllowObject = (allowances, allowId) => {
  const selectedAlloCardObj = allowances?.find(
    allowObj => allowObj.allowanceIdNbr == allowId
  );
  return selectedAlloCardObj?.allowanceItems || [];
};
export const isAmtUomSameInPlanAndPending = (
  getValues,
  searchId,
  fieldKey,
  key,
  allowId
) => {
  const {
      itemsFromPlan,
      allowObjFromPlan,
      allowObjFromPending,
      allowanceType,
      itemsFromPending,
    } = getAllowanceObjForAmt(getValues, searchId, allowId),
    { planEventPendingChanges, planEventHistory } = getValues(),
    planPendinChngObj = planEventPendingChanges?.offerAllowanceChanges?.find(
      obj => obj?.offerNumber === searchId
    ),
    currentHistoryObj = planEventHistory?.[
      planEventHistory?.length - 1
    ]?.offerAllowanceChanges?.find(
      histObj => histObj?.offerNumber === searchId
    ),
    // isKeyExistInHistOrPendingChng = checkKeyFromPendingOrHistory(
    //   true,
    //   [fieldKey],
    //   planPendinChngObj?.changes,
    //   currentHistoryObj?.changes,
    //   allowId
    // ),
    { planValue, pendingValue } = getAmtOrUomFromPlanOrPending(
      itemsFromPlan,
      itemsFromPending,
      allowObjFromPlan?.allowances,
      allowObjFromPending?.allowances,
      allowanceType === HEADERFLAT.label,
      false,
      key === "uom",
      allowanceType
    );
  return planValue === pendingValue;
};

export const getAmtOrUomFromPlanOrPending = (
  planItems,
  pendingItems,
  planAllow,
  pendingAllow,
  isHeaderFlat,
  isUomVisible,
  isUom,
  allowanceType = ""
) => {
  return {
    planValue: getValueForAmtUom(
      planItems,
      planAllow,
      isHeaderFlat,
      isUom,
      isUomVisible,
      allowanceType
    ),
    pendingValue: getValueForAmtUom(
      pendingItems,
      pendingAllow,
      isHeaderFlat,
      isUom,
      isUomVisible,
      allowanceType
    ),
  };
};

export const getValueForAmtUom = (
  items,
  allowances,
  isHeaderFlat,
  isUom,
  isUomVisible,
  allowanceType = ""
) => {
  return isUom
    ? getUomKeys(items)
    : getAllowanceAmountsSubHeaderValue(
        allowanceType,
        isHeaderFlat ? allowances : items,
        false,
        isUomVisible
      );
};
const getUomKeys = items => {
  const selectedUomObj = getUniqueUomTypes(items, "uom");
  return selectedUomObj && toString?.call(selectedUomObj) === "[object Object]"
    ? Object.keys(selectedUomObj)
        ?.map(uomKey => efConstants.UOM_KEY_VALUE_MAPPER[uomKey])
        ?.join(", ")
    : "";
};

export const checkObjectHasKeys = (value: object | null | undefined) => {
  return Object.keys(value || {}).length;
};

export function roundOfNumber(value) {
  return Math.round(value * 100) / 100;
}

export const displayPromoDetailsBasedOnType = (
  type,
  promoDetailsFields,
  isCurrent
) => {
  if (isCurrent) {
    promoDetailsFields[0].promoTypeVal = type;
    promoDetailsFields?.forEach(element => {
      const showFields = element?.showFields;
      element.conditionalShow = true;

      if (showFields?.length && !showFields.includes(type)) {
        element.conditionalShow = false;
      }
    });
  } else {
    promoDetailsFields[0].prevPromoTypeVal = type;
  }
  return PROMOTION_TYPES[type];
};

const isStatusAgreedActive = eventStatus => {
  return [EEVENT_STATUS.ACTIVE, EEVENT_STATUS.AGREED].includes(eventStatus);
};
export const isCrossUserEditingCard = (eventStatus, allowanceData) => {
  const isValidStatusToCheck = isStatusAgreedActive(eventStatus);
  if (isValidStatusToCheck && allowanceData) {
    const { createUserId, lastUpdUserId } = allowanceData,
      tempUserId = lastUpdUserId || createUserId,
      loggedInUserId = localStorage.getItem("OAM_REMOTE_USER"),
      getLoggedInUserId = loggedInUserId ? JSON.parse(loggedInUserId) : "";
    return tempUserId && getLoggedInUserId
      ? tempUserId?.toLowerCase() !== getLoggedInUserId?.toLowerCase()
      : false;
  }
  return false;
};
export const saveAllowanceFormData = ({
  dispatch,
  key,
  allowanceRegField,
  value,
  isPreviousDataRequired = true,
  previousData = null,
}: {
  dispatch: any;
  key: string;
  allowanceRegField: string;
  value: object;
  isPreviousDataRequired?: boolean;
  previousData?: object | null;
}) => {
  dispatch(
    setAllowanceFormInfo({
      allowanceFormData: {
        [allowanceRegField]: {
          ...(isPreviousDataRequired ? previousData : {}),
          [key]: value,
        },
      },
    })
  );
};

export const getOfferKey = (
  defaultKey: string,
  allowanceType: string,
  createInd?: string
) => {
  if (
    (createInd && [HEADERFLAT.key, ITEMFLAT.key].includes(allowanceType)) ||
    createInd === CASE.createInd[3]
  ) {
    return (
      ALLOWANCE_TO_BE_CREATED_CD_MAPPER?.[createInd]?.offerKey || defaultKey
    );
  }
  return defaultKey;
};

export const isHfOrIfType = (allowanceType: string, key = "key") => {
  return [ITEMFLAT?.[key], HEADERFLAT?.[key]].includes(allowanceType);
};

export const isHfOrIfTypeValue = (allowanceType: string, value = "value") => {
  return [ITEMFLAT?.[value], HEADERFLAT?.[value]].includes(
    allowanceType.toUpperCase()
  );
};

export const getQueryParams = () => {
  const params: any = {
    isCancelled: null,
    isSaved: null,
    offerKey: null,
    offerId: null,
    taskType: null,
    isOfferViewMode: null,
    isRedirectToMainEntry: null,
  };
  const searchParams = new URLSearchParams(window.location.search);
  searchParams.forEach(function (value, key) {
    params[key] = value;
  });
  return params;
};

export const removeParams = () => {
  if (
    window?.history?.pushState &&
    window?.location?.pathname?.split("/")?.includes("edit")
  ) {
    const { protocol, host, pathname } = window.location,
      newUrl = `${protocol}//${host}${pathname}`;
    window.history.pushState({ path: newUrl }, "", newUrl);
  }
};

export const getInvalidDataObject = (
  eventDetails,
  invalidOffers,
  invalidPromos
) => {
  const output = {
    offerAllowances: [] as any[],
    promotions: [] as any[],
    validOfferAllowance: true,
    validPromos: true,
  };
  for (const offer of invalidOffers) {
    const offerId = offer;

    const allowanceType = getAllowanceTypeByPerformance(
      eventDetails?.["offerAllowances"]?.find(item => item?.id === offerId)?.[
        "allowances"
      ]?.[0]?.performance
    ).toUpperCase();
    const createInd = eventDetails?.["offerAllowances"]?.find(
      item => item?.id === offerId
    )?.createInd;
    const offerNumber = eventDetails?.["offerAllowances"]?.find(
      item => item?.id === offerId
    ).offerNumber;
    const allowanceObject = {
      offerId,
      allowanceType: allowanceType,
      offerNumber,
      defaultInd: createInd,
      dates: {
        dsd: {
          isError: getisValidDates(
            allowanceType,
            createInd,
            offerId,
            eventDetails?.["offerAllowances"],
            "DSD"
          ),
        },
        wareHouse: {
          isError: getisValidDates(
            allowanceType,
            createInd,
            offerId,
            eventDetails?.["offerAllowances"],
            "WAREHOUSE"
          ),
        },
        normal: {
          isError: getisValidDates(
            allowanceType,
            createInd,
            offerId,
            eventDetails?.["offerAllowances"],
            "ALLOW"
          ),
        },
        isOpenCard: false,
      },
    };

    output.offerAllowances.push(allowanceObject);
  }

  for (const promotion of invalidPromos) {
    const promoId = promotion;
    const isError = true;

    const promotionObject = {
      [promoId]: {
        promoId,
        isError,
        isOpenCard: false,
      },
    };

    output.promotions.push(promotionObject);
  }

  return output;
};
export const validOfferList = eventDetailsData => {
  const { isNDPAndFlagEnabled: isNational } = getIsNationalEvent(
    eventDetailsData?.eventType
  );
  return eventDetailsData?.offerAllowances?.filter(offer => {
    const { allowances = [], overrideAllowanceStatus } = offer || {};
    const statusToCheck = isNational
      ? offer?.allowances?.find(
          allow => !EXCLUDED_OFFER_PROMO_STATUS.includes(allow?.allowanceStatus)
        )
        ? true
        : false
      : !EXCLUDED_OFFER_PROMO_STATUS.includes(allowances?.[0]?.allowanceStatus);
    const statusCheckForAgreedPending = isNational
      ? getNationalOfferStatus(allowances)
      : allowances?.[0]?.allowanceStatus;
    return (
      statusToCheck &&
      !(
        overrideAllowanceStatus === EEVENT_STATUS.CANCELED &&
        statusCheckForAgreedPending === EEVENT_STATUS.AGREED_PENDING
      )
    );
  });
};
export const getisValidDates = (
  allowanceType,
  createInd,
  offerId,
  offersData,
  key
) => {
  if (offersData?.find(item => item.id === offerId)) {
    if (
      [
        CASE.value,
        SCAN.value,
        SHIPTOSTORE.value,
        HEADERFLAT.value,
        ITEMFLAT.value,
      ].includes(allowanceType) &&
      [
        CASE.createInd[3],
        SCAN.createInd[0],
        SHIPTOSTORE.createInd[0],
        ...ITEMFLAT.createInd,
        ...HEADERFLAT.createInd,
      ].includes(createInd) &&
      key === "ALLOW"
    ) {
      return true;
    } else if (
      [CASE.value, SCAN.value, SHIPTOSTORE.value].includes(allowanceType) &&
      [
        CASE.createInd[1],
        CASE.createInd[2],
        SCAN.createInd[1],
        SHIPTOSTORE.createInd[1],
      ].includes(createInd) &&
      key === PRODUCT_SOURCE_DATA.DSD.key
    ) {
      return true;
    } else if (
      [CASE.value].includes(allowanceType) &&
      [CASE.createInd[0], CASE.createInd[2]].includes(createInd) &&
      key === PRODUCT_SOURCE_DATA.WAREHOUSE.key
    ) {
      return true;
    } else {
      return false;
    }
  } else {
    return false;
  }
};

export const setProductSourceError = (
  dispatch,
  prevRtkErrorsArr,
  showTokenError
) => {
  dispatch(
    setApiErrorResponse({
      apiErrorsMsgs: [
        ...new Set([
          ...prevRtkErrorsArr,
          "Invalid combination of Promo Product Groups and Store Groups. Please update Promo Product Groups or Store Groups to continue.",
        ]),
      ],
      displayMessage: "Something went wrong!",
      showTokenError,
    })
  );
};

export const checkIsWarehouseItem = (
  allowance = { vendorNbr: "", location: { locationTypeCd: "" } }
) => {
  const { vendorNbr, location } = allowance;
  return vendorNbr === "" && ["D", "W"].includes(location?.locationTypeCd);
};

export function getCreateIndByLocation(offerInfo, allowIndex) {
  const createIndValue = offerInfo?.createInd;
  if (createIndValue === SCAN.createInd[1]) {
    return checkIsWarehouseItem(offerInfo?.allowances?.[allowIndex])
      ? SCAN.createInd[0]
      : createIndValue;
  } else if (createIndValue === CASE.createInd[2]) {
    return checkIsWarehouseItem(offerInfo?.allowances?.[allowIndex])
      ? CASE.createInd[0]
      : CASE.createInd[1];
  }
  return createIndValue;
}

export const getValuetrimmed = (
  value: string | null | undefined | unknown = ""
) => {
  return value?.toString()?.trim();
};

export const getDatesFromTempWork = tempAllowData => {
  const {
    orderEndDate,
    orderStartDate,
    performEndDate,
    performStartDate,
    shipEndDate,
    shipStartDate,
    allowanceStartDate,
    allowanceStatus,
    arrivalEndDate,
    arrivalStartDate,
    allowanceEndDate,
  } = tempAllowData || {};
  return {
    orderEndDate,
    orderStartDate,
    performEndDate,
    performStartDate,
    shipEndDate,
    shipStartDate,
    allowanceStartDate,
    allowanceStatus,
    arrivalEndDate,
    arrivalStartDate,
    allowanceEndDate,
  };
};

export const getDsdFundingViewSearchParams = ({
  cardIndex,
  offerAllowances,
  promotionsLists,
  isAllowanceCard,
  isPromoCard,
}) => {
  const searchKey = isAllowanceCard ? "offerId" : isPromoCard ? "promoId" : "";
  let searchValue = "";
  const offerObj = isAllowanceCard && offerAllowances?.[cardIndex];
  const promoObj =
    isPromoCard && promotionsLists?.[cardIndex]?.promotionsList?.[0];

  const { offerNumber = "" } = offerObj || {};
  searchValue = offerNumber;
  if (!searchValue && promoObj) {
    const { promotionId = "" } = promoObj || {};
    searchValue = promotionId;
  }
  return { key: searchKey, value: searchValue };
};

export const checkIsPerfChangable = (createInd: string) => {
  return ![HEADERFLAT.createInd[1], ITEMFLAT.createInd[1]].includes(createInd);
};

export const checkIsStoreGroupSectionEnable = (createInd: string) => {
  return ![HEADERFLAT.createInd[1], ITEMFLAT.createInd[1]].includes(createInd);
};

export const checkIsPaymentTypeDeduct = (perfCode: string, allowance: any) => {
  if (perfCode === "Off Invoice (01)") return false;
  return ["D", "Deduct"].includes(
    allowance?.allowanceBillingInfo?.absVendorPaymentType
  );
};

export const handleZeroCostVendors = allowances => {
  return allowances?.every(allowance => {
    return allowance?.allowanceItems?.some(
      item => item?.modCommand === efConstants.ZERO_COST_TEXT
    );
  });
};
export const checkForZeroCost = divAllowResp => {
  const isAnyZeroCost =
    Array.isArray(divAllowResp) &&
    divAllowResp?.find(allowRespObj => {
      return handleZeroCostVendors(allowRespObj?.allowances || []);
    });
  return isAnyZeroCost ? true : false;
};

export const checkIsAllowanceTypeChanged = (tempData, eventDetailsData) => {
  let offerIndex = 0;
  const isAllowanceUpdated =
    !!tempData?.offerNumber &&
    eventDetailsData?.offerAllowances?.filter((item, index) => {
      if (item?.offerNumber === tempData?.offerNumber) {
        offerIndex = index;
        return (
          getAllowaceTypeByCreateInd(item?.createInd) !==
          tempData?.allowanceType
        );
      }
      return false;
    })?.length;
  return { isAllowanceUpdated, offerIndex };
};
