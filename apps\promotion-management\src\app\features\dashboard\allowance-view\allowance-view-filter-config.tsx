import { useState } from "react";
import {
  DASHBOARD_SLICE_CONFIG,
  DASHBOARD_VIEW_KEY_MAPPER,
  EXPORTABLE_COLUMNS_ARRAY,
} from "apps/promotion-management/src/app/config/dashboard-config";
import { ExportBtn } from "../../../shared/ui/atoms";
import DashboardSearch from "../common/components/dashboard-search";
import { HeaderItem } from "../common/components/render-header-item";
import AllowanceViewColumns from "./components/allowance-view-columns";
import { PlanningPropsFromGlobalHeader } from "../planning-view/planning-view-filter-config";
import { ExportModal } from "../../../shared/ui/molecules";
import { setAllowancesFilterData } from "../../../library/data-access/dashboard";
import FilterDropdownContainer from "@me/promotion-management/facet-filters/components/filter-dropdown-container/filter-dropdown-container";
import { STATUS_CONSTANTS } from "@me/util-helpers";
import { useSelectorWrap } from "@me/data-rtk";
import { generateCommonPayLoadForDashBoard } from "../common/utils/common-view-api-service";
import { useGetAllowancesExportDataMutation } from "../../../library/data-service/dashboard/dashboard-service";
import { getCurrentDateAndTime } from "@me-upp-js/utilities";
import { appConstants } from "@me/utils-root-props";
import { getDefaultExportOptionsByView } from "@me/promotion-management/common/helpers";

export const ALLOWANCE_HEADER_STATUS_OPTION = [
  { label: STATUS_CONSTANTS.STATUS.AGREED.text, key: "1", isChecked: true },
  {
    label: STATUS_CONSTANTS.STATUS["AGREED-PENDING"].filtertext,
    key: "2",
    isChecked: true,
  },
  { label: STATUS_CONSTANTS.STATUS.DRAFT.text, key: "3", isChecked: true },
  {
    label: STATUS_CONSTANTS.STATUS["PENDING WITH MERCHANT"].filtertext,
    key: "4",
    isChecked: true,
  },
  {
    label: STATUS_CONSTANTS.STATUS["PENDING WITH VENDOR"].filtertext,
    key: "5",
    isChecked: true,
  },
  { label: STATUS_CONSTANTS.STATUS.ACTIVE.text, key: "6", isChecked: true },
  { label: STATUS_CONSTANTS.STATUS.EXECUTED.text, key: "7", isChecked: true },
  { label: STATUS_CONSTANTS.STATUS.CANCELED.text, key: "8", isChecked: true },
  { label: STATUS_CONSTANTS.STATUS.REJECTED.text, key: "9", isChecked: true },
];

export const ALLOWANCE_HEADER_TYPE_OPTION = [
  { label: "Case", key: "1", isChecked: true },
  { label: "Header Flat", key: "2", isChecked: true },
  { label: "Item Flat", key: "3", isChecked: true },
  { label: "Scan", key: "4", isChecked: true },
  { label: "Ship to Store", key: "5", isChecked: true },
];

export const ALLOWANCE_HEADER_PERFORMANCE_OPTION = [
  { label: "Price / Ad / Display (88)", key: "1", isChecked: true },
  { label: "4U Event (52)", key: "2", isChecked: true },
  { label: "Cupon/Grand Opening (75)", key: "3", isChecked: true },
  { label: "Discontinued (66)", key: "4", isChecked: true },
  { label: "Fuel Rewards (77)", key: "5", isChecked: true },
  { label: "Liquor Only (38)", key: "6", isChecked: true },
];

export const ALLOWANCE_HEADER_VEHICLE_TYPE_OPTION = [
  { label: "Disco", key: "Disco", isChecked: true },
  { label: "Friday ROP", key: "FridayROP", isChecked: true },
  {
    label: "GO/COS/Opp Vehicle",
    key: "GOCOSOppVehicle",
    isChecked: true,
  },
  { label: "Other", key: "Other", isChecked: true },
  { label: "Promo Cycle", key: "PromoCycle", isChecked: true },
  { label: "Savings Guide", key: "SavingsGuide", isChecked: true },
  { label: "Sunday Insert", key: "SundayInsert", isChecked: true },
  { label: "Weekly Insert", key: "WeeklyInsert", isChecked: true },
];

export const ExportContent = () => {
  const dashboardKey = DASHBOARD_VIEW_KEY_MAPPER.ALLOWANCE_VIEW_KEY;
  const { allowanceView } = DASHBOARD_SLICE_CONFIG;
  const { data: allowanesFilterData } =
    useSelectorWrap(allowanceView.FILTER_SLICE_KEY) || {};
  const { data: globalHeaderData } = useSelectorWrap("eventCalendarPayload_rn");
  const { data: performanceApiData } = useSelectorWrap(
    "performance_api_data_rn"
  );
  const { data: allowanesList } =
    useSelectorWrap(allowanceView.SLICE_KEY) || {};
  const [options, setOptions] = useState(
    getDefaultExportOptionsByView(dashboardKey)
  );
  const [isOpened, setIsOpened] = useState(false);
  const [error, setError] = useState("");

  const [postAllowanceExportData, { isLoading }] =
    useGetAllowancesExportDataMutation();

  const selectedOptions = options?.filter(({ visible }) => visible);

  const onExport = () => {
    setIsOpened(isOpened => !isOpened);
  };

  const handleExport = async () => {
    try {
      setError("");
      const payload = {
        offerPlanEventFilter: generateCommonPayLoadForDashBoard({
          globalHeaderData,
          viewLevelFilterData: allowanesFilterData,
          paging: null,
          propsFromGlobal: ALLOWANCES_PROPS_FROM_GLOBAL_HEADER,
          view: "Allowances",
          performanceApiData,
        }),
        exportColumns: selectedOptions?.map(({ key }) => key),
      };
      await postAllowanceExportData(JSON.stringify(payload))
        .then(async response => {
          if (response?.data) {
            const fileURL = window.URL.createObjectURL(response?.data);
            const link = document.createElement("a");
            link.href = fileURL;
            link.setAttribute(
              "download",
              `allowance_export_${getCurrentDateAndTime()}.xlsx`
            );
            document.body.appendChild(link);
            link.click();
            link?.parentNode?.removeChild(link);
            URL.revokeObjectURL(fileURL);
            setIsOpened(false);
          } else {
            setError(appConstants.EXPORT_ERROR.message);
          }
        })
        .catch(err => {
          setError(err);
        });
    } catch (error) {
      setError(error as string);
    }
  };

  return (
    <div className="flex gap-3 items-center">
      <ExportModal
        options={options}
        setOptions={setOptions}
        selectedOptions={selectedOptions}
        isOpened={isOpened}
        setIsOpened={setIsOpened}
        headerTitle={"Export Allowances"}
        error={error}
        setError={setError}
        handleExport={handleExport}
        isLoading={isLoading}
        view={DASHBOARD_VIEW_KEY_MAPPER.ALLOWANCE_VIEW_KEY}
      />
      <ExportBtn
        onExportClick={onExport}
        disabled={!allowanesList?.offerPlanEvents?.length}
      />
    </div>
  );
};

export const ALLOWANCE_SUB_HEADER_CONFIG: HeaderItem[] = [
  {
    key: "search-box",
    type: "custom-component",
    customComponent: (
      <DashboardSearch
        action={setAllowancesFilterData}
        sliceKey={DASHBOARD_SLICE_CONFIG.allowanceView.FILTER_SLICE_KEY}
        viewType={DASHBOARD_VIEW_KEY_MAPPER.ALLOWANCE_VIEW_KEY}
      />
    ),
  },
  {
    key: "divider-1",
    type: "divider",
  },
  {
    key: "allowanceStatus",
    type: "multi-select-with-apply",
    label: "Status",
    extraConfig: {
      placeHolder: "All",
      variant: "tertiary",
      width: 200,
      itemsKey: "label",
    },
  },
  {
    key: "allowanceFiilters",
    type: "custom-component",
    label: "Offer Status",
    customComponent: (
      <div className="flex gap-3 items-center">
        <FilterDropdownContainer />
      </div>
    ),
  },
  {
    key: "white-space",
    type: "white-space",
  },
  {
    key: "columns",
    type: "custom-component",
    customComponent: (
      <div className="flex gap-3 items-center">
        <AllowanceViewColumns />
        <ExportContent />
      </div>
    ),
  },
];

export const ALLOWANCES_PROPS_FROM_GLOBAL_HEADER: PlanningPropsFromGlobalHeader =
  {
    required: ["startDate", "endDate", "divisionIds", "timeScale"],
    optional: [
      "smicCategoryCodes",
      "smicGroupCodes",
      "vehicleTypes",
      "asmNamesOrCategorGroupCodes",
      "productSelectionType",
      "allowanceTypes",
      "planEventSortType",
      "supplierCodes",
      "promoProductGroups",
      "banners",
      "dateFilterType",
      "offerPlanEventProjectionType",
      "eventTypes",
    ],
  };

export const ALLOWANCES_EVENT_STATUS_TYPES = [
  "Draft",
  "Pending With Merchant",
  "Pending With Vendor",
  "Agreed",
  "Agreed-Pending",
  "Active",
  "Executed",
  "Rejected",
];

export const ALLOWANCE_DEFAULT_DATE_TYPE = "EVENT_DATES";
