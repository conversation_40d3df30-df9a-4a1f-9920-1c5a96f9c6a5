import Card from "@albertsons/uds/molecule/Card/Card";
import Search from "@albertsons/uds/molecule/Search/Search";
import Table from "@albertsons/uds/molecule/Table";
import { Column } from "@albertsons/uds/molecule/Table/Table.types";
import clsx from "clsx";
import { ChevronRight as ChevronRightIcon } from "lucide-react";
import { useState } from "react";
import { useUpcFormatter } from "@me/util-helpers";
import {sortViewItems} from "../../../../../shared/helpers/event-flow-helpers";
import efConstants from "../../../../../shared/ef-constants/ef-constants";
import Tooltip from "@albertsons/uds/molecule/Tooltip";

interface PromoProductGroups {
  itemDescription: string;
  cic: number;
  primaryUpc: string;
  upcs: string[];
  pack: number;
  size: string;
}

const PlanProductGroupItemCard = ({ promoProductGroup, index }) => {
  const [isOpen, setOpen] = useState(index === 0);
  const [formattedUPC, formatUPC] = useUpcFormatter();
  const columns: Column<PromoProductGroups>[] = [
    {
      id: `itemDescription_${index}`,
      label: "Item Description",
      value: "itemDescription",
      sortable: true,
      pinned: true,
    },
    {
      id: `cic_${index}`,
      label: "CIC",
      value: "cic",
      sortable: true,
    },
    {
      id: `primaryUpc_${index}`,
      label: "Primary UPC",
      value: item => <span>{formatUPC(item?.primaryUpc)}</span>,
      sortable: true,
    },
    {
      id: `upcs_${index}`,
      label: "UPCs",
      value: d => (
        <div className="text-center">
          <Tooltip zIndex={100} anchor="bottom" showDelay={300}>
            <Tooltip.Popover>
              <div className="grid grid-cols-1 w-full">
                {d?.upcs?.map((cur_upc, key) => (
                  <div className="m-1" key={key}>
                    {cur_upc}
                  </div>
                ))}
              </div>
            </Tooltip.Popover>
            <div className="w-full h-full cursor-pointer text-[#1B6EBB]">
              {d?.upcs?.length}
            </div>
          </Tooltip>
        </div>
      ),
      sortable: (m1: PromoProductGroups, m2: PromoProductGroups) =>
        m1.upcs.length
          .toLocaleString()
          .localeCompare(m2.upcs.length.toLocaleString()),
    },
    {
      id: `pack_${index}`,
      label: "Pack",
      value: "pack",
      sortable: true,
    },
    {
      id: `size_${index}`,
      label: "Size",
      value: "size",
      sortable: true,
    },
  ];

  const productGroupItemsLength =
    promoProductGroup?.productGroupItems?.length || 0;

  const { NORECORDS_FOUND_LABEL } = efConstants;

  const filter = (
    <div className="flex items-center">
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="flex-grow-0 flex-shrink-0 w-6 h-6 relative"
        preserveAspectRatio="xMidYMid meet"
        data-testid="filter-svg"
      >
        <path
          d="M10 18H14V16H10V18ZM3 6V8H21V6H3ZM6 13H18V11H6V13Z"
          fill="#1B6EBB"
        ></path>
      </svg>
      <p className="flex-grow-0 flex-shrink-0 text-base font-semibold text-center text-[#1b6ebb] ml-[10px]">
        Filters
      </p>
    </div>
  );

  const searchInput = (
    <div className="w-[264px] ml-[16px]">
      <Search onChange={() => {}} />
    </div>
  );

  const tableHeader = (
    <div className="flex justify-end items-center mb-[12px]">
      {filter}
      {searchInput}
    </div>
  );

  const headerTitle = (
    <div className="flex items-center justify-start w-full">
      <span className="text-base text-dark-text mr-5 select-none">
        {promoProductGroup?.productGroupName}&nbsp; (
        {promoProductGroup?.productGroupItems?.length} Items)
      </span>
    </div>
  );

  const chevronIcon = (
    <div className="flex flex-col items-center justify-center min-w-[32px] w-[32px] min-h-[32px] h-[32px]">
      <ChevronRightIcon
        height={12}
        strokeWidth={1}
        color={"#5a697b"}
        className={clsx("min-w-[24px] min-h-[24px] transition duration-200", {
          "rotate-90": isOpen,
        })}
      />
    </div>
  );

  const headerDivClass = clsx(
    "flex items-center w-full h-[52px] -mb-[1px] px-4 cursor-pointer bg-blue-307",
    {
      "border-b border-gray-204": isOpen,
    }
  );

  const cardHeader = (
    <Card.Header>
      <div onClick={() => setOpen(!isOpen)} className={headerDivClass}>
        {chevronIcon}
        {headerTitle}
      </div>
    </Card.Header>
  );

  const table = (
    <Table
      id={`table_${promoProductGroup?.productGroupName?.replace(/ /g, "_")}`}
      itemKey="itemDescription"
      items={sortViewItems(promoProductGroup?.productGroupItems, "cic")}
      columns={columns}
      noHeader={true}
      overflow={true}
      options={{
        heading: {
          columns: {
            truncate: true,
          },
        },
        footer: {
          label: "Items",
          pagination: {
            showButtons: true,
          },
        },
      }}
      className="component-scroll"
    />
  );

  const cardContent = (
    <Card.Content>
      <div
        className={`font-[14px] font-normal text-sm leading-4 mx-[-24px] ${
          productGroupItemsLength ? "mt-[-24px] mb-[-32px]" : "mt-0 mb-0"
        }`}
      >
        {/* {tableHeader} */}
        {!productGroupItemsLength ? NORECORDS_FOUND_LABEL : table}
      </div>
    </Card.Content>
  );

  return (
    <Card
      collapsible
      isOpen={isOpen}
      onChange={() => setOpen(!isOpen)}
      className="min-w-[600px] mb-2"
      customHeader
      zIndex={10 - index}
    >
      {cardHeader}
      {cardContent}
    </Card>
  );
};
export default PlanProductGroupItemCard;
