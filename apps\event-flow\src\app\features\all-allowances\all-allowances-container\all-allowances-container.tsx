import { useSelectorWrap } from "@me/data-rtk";
import _ from "lodash";
import { useEffect, useRef } from "react";
import { useDispatch } from "react-redux";
import {
  allowanceTempWorkHandler,
  allowanceTempWorkReset,
} from "../../create-event/service/slice/allowance-temp-work-slice";
import AllAllowancesTopRight from "../all-allowances-top-right";
import TableTopAllowancesContainer from "../allowance-table-top/table-top-allowances-container";
import { AllowancesTable } from "../allowance-table/allowances-table";
import AllowancesFiltersSelection from "../allowances-filters-container/allowances-filters-selection";
import { getAllowTypeConfig, textMapping } from "./text-mapping";
import { setAllowanceTableColsGroupConfig } from "../../create-event/service/slice/allowances-dashboard-slices";
import {
  useDeleteAllowancePendingCancelDataMutation,
  useDeleteAllowanceTempWorkDataMutation,
  useDeleteNationalAllowanceTempWorkDataMutation,
  useGetAllowanceTempWorkDataQuery,
} from "../../create-event/service/apis/allowance-api";
import { useGetPlanEventDataQuery } from "../../create-event/service/apis/event-api";
import { eventDetailsDataHandler } from "../../create-event/service/slice/event-detail-slice";
import { getAllowanceTypeByPerformance } from "../../create-event/service/allowance/allowance-service";
import { useNavigate } from "react-router-dom";
import { useGetAppBasePath, useGetQueryParams } from "@me/util-helpers";
import { RenderStates } from "@me/ui-render-states";
import efConstants from "../../../shared/ef-constants/ef-constants";
import {
  resetTableDataSliceData,
} from "../../create-event/service/slice/table-data-slice";
import {
  allowanceFormReset,
  offerCardConfiguration,
  resetIsOfferSectionUpdated,
  resetOfferAmountsData,
  setAllowTypeChange,
} from "../../create-event/service/slice/allowance-details-slice";
import { appConstants } from "@me/utils-root-props";
import {
  isAllowanceFeatureEnabled,
  getLoggedInUserFromStorage,
} from "@me-upp-js/utilities";
import AllowanceMainEntryTable from "../allowance-table/allowance-main-entry-table";
import DivisionsList from "../nationals/components/divisions/divisions-list";
import NationalOfferMainEntry from "../nationals/components/table/national-offer-main-entry";
import { resetSlicesForMainEntry } from "../nationals/service/table-service";
import { isNationalType } from "../allowance-lead-distributors/billing-selection-utils";
import NationalAllAllowancesTopRight from "../nationals/components/table/national-all-allowances-top-right";

export default function AllowancesContainer() {
  // data retrieval from url query
  const {
    queryParamValue: { isSummary, eventId, offerAllowancesId, isNdpType },
  } = useGetQueryParams(["eventId", "isSummary", "offerAllowancesId", "isNdpType",]);
  const navigate = useNavigate();
  const isNational = isNationalType(isNdpType === "true");
  const { basePath } = useGetAppBasePath();
  const { ALLOWANCE_TYPE_NAME_MAPPER } = efConstants;
  const updateAllRef = useRef<HTMLElement>(null);
  const leadDistRef = useRef<HTMLElement>(null);
  let {
    data: { allowanceData: allowanceTempWorkData },
  } = useSelectorWrap("allowance_temp_work");
  allowanceTempWorkData =
    isNational && Array.isArray(allowanceTempWorkData)
      ? allowanceTempWorkData?.[0]
      : allowanceTempWorkData;
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { data: allowGroupConfigObj } = useSelectorWrap(
    "allowanceTableColsGroupConfig_rn"
  );
  const [deleteAllowanceTempWorkData] =
    useDeleteAllowanceTempWorkDataMutation();
  const [deleteNationalAllowanceTempWorkData] =
    useDeleteNationalAllowanceTempWorkDataMutation();
  const [deleteAllowancePendingCancelData] =
    useDeleteAllowancePendingCancelDataMutation();

  const clearTableSlices = () => {
    resetSlicesForMainEntry({ dispatch });
  };

  useEffect(() => {
    !appConstants.IS_ENABLE_REDESIGN_ALLOWANCE_CHANGES && clearTableSlices();
    localStorage.setItem(
      `${appConstants.RESTRICT_NAV_STORAGE_NAME}${sessionStorage.getItem(
        appConstants.SESSION_STORAGE_RESTRICTED_APP_KEY_NAME
      )}`,
      "true"
    );
    return () => {
      appConstants.IS_ENABLE_REDESIGN_ALLOWANCE_CHANGES && clearTableSlices();
    };
  }, []);

  const { data: eventDataResp, isFetching: isEventDataFetched } =
    useGetPlanEventDataQuery(eventId, {
      skip: eventDetailsData?.id || !isSummary,
    });
  const dispatch = useDispatch();

  // allowance temp api call
  // auto cached when directly navigated from edit event
  // called on refresh
  const {
    data: allowanceTempDataFromGet,
    isFetching: isTempworkGetCreateLoaded,
    isError: isTempworkGetCreateError,
  } = useGetAllowanceTempWorkDataQuery(
    {
      URL_PARAM: eventId,
      queryParams: {},
    },
    {
      skip: !eventId || allowanceTempWorkData || isSummary || isNational,
    }
  );

  const clearTempworkStoreData = () => {
    dispatch(
      offerCardConfiguration({
        editCardConfig: {},
        openCardConfig: {},
      })
    );
    dispatch(allowanceTempWorkReset());
    dispatch(resetOfferAmountsData());
    dispatch(resetIsOfferSectionUpdated());
    dispatch(allowanceFormReset());
  };

  const handleBeforeUnloadOnMainEntry = () => {
    localStorage.removeItem(
      `${appConstants.RESTRICT_NAV_STORAGE_NAME}${sessionStorage.getItem(
        appConstants.SESSION_STORAGE_RESTRICTED_APP_KEY_NAME
      )}`
    );
    deleteTempWorkData();
    if (isAllowanceFeatureEnabled) {
      dispatch(
        setAllowTypeChange({
          isAllowanceTypeChanged: {},
        })
      );
    }
  };
  const deleteTempWorkData = () => {
    const tempWorkAllowanceId =
      allowanceTempWorkData?.tempWorkAllowanceId || "";
    const offerAllowancesId = allowanceTempWorkData?.offerAllowancesId || "";
    const lastUpdUserId =
      sessionStorage.getItem("TEMPWORK_USER") ||
      allowanceTempWorkData?.lastUpdUserId?.toUppercase() ||
      "";
    const loggedInUser = getLoggedInUserFromStorage();
    if (tempWorkAllowanceId && lastUpdUserId === loggedInUser) {
      !isNational && deleteAllowanceTempWorkData({
        URL_PARAM: tempWorkAllowanceId,
      });
      isNational && deleteTempDataForNational();
      offerAllowancesId &&
        deleteAllowancePendingCancelData({
          URL_PARAM: offerAllowancesId,
        });
      clearTempworkStoreData();
    }
  };

  const deleteTempDataForNational = async () => {
    if(isNational) {
      await deleteNationalAllowanceTempWorkData({
        URL_PARAM: eventId,
      });
    }
  }

  const redirectToEvent = () => {
    clearTempworkStoreData();
    sessionStorage.removeItem("TEMPWORK_ID");
    sessionStorage.removeItem("OFFER_ID");
    localStorage.removeItem(
      `${appConstants.RESTRICT_NAV_STORAGE_NAME}${sessionStorage.getItem(
        appConstants.SESSION_STORAGE_RESTRICTED_APP_KEY_NAME
      )}`
    );
    navigate(`${basePath}/events/edit/${eventId}`);
  };

  // on allowanceTempWorkData API call update allowance_temp_work slice
  useEffect(() => {
    if (allowanceTempWorkData || !allowanceTempDataFromGet || isSummary || isNational) return;
    dispatch(
      allowanceTempWorkHandler({
        allowanceData: allowanceTempDataFromGet,
        isTempLoaded: true,
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [allowanceTempDataFromGet]);

  useEffect(() => {
    const isSummaryParsed = isSummary ? JSON.parse(isSummary) : false;
    if (
      !isSummaryParsed &&
      (_.isEmpty(allowanceTempWorkData) ||
        (isNational && Array.isArray(allowanceTempWorkData) &&
          !allowanceTempWorkData?.length)) &&
      ((!isTempworkGetCreateLoaded && _.isEmpty(allowanceTempDataFromGet)) ||
        isTempworkGetCreateError)
    ) {
      redirectToEvent();
    }
  }, [
    JSON.stringify(allowanceTempWorkData),
    JSON.stringify(allowanceTempDataFromGet),
    isTempworkGetCreateLoaded,
  ]);

  useEffect(() => {
    isSummary && !isEventDataFetched && setAllowGroupConfigOnSummary();
  }, [eventDataResp, isEventDataFetched, eventDetailsData?.id]);

  useEffect(() => {
    if (
      !_.isEmpty(allowanceTempWorkData) ||
      (isNational &&
        Array.isArray(allowanceTempWorkData) &&
        allowanceTempWorkData?.length) ||
      !_.isEmpty(allowanceTempDataFromGet)
    ) {
      window.addEventListener("beforeunload", handleBeforeUnloadOnMainEntry);
    }

    !isSummary && setAllowTableGrpConfig(allowanceTempWorkData);
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnloadOnMainEntry);
    };
  }, [
    JSON.stringify(allowanceTempWorkData),
    JSON.stringify(allowanceTempDataFromGet),
  ]);

  const setAllowTableGrpConfig = tempWorkData => {
    if (tempWorkData) {
      const allowGroupConfigObj = getAllowTypeConfig({
        allowanceTempWorkData: tempWorkData,
      });
      dispatch(setAllowanceTableColsGroupConfig(allowGroupConfigObj));
    }
  };
  useEffect(() => {
    // added this way as in test cases its failing continously
    updateAllRef?.current?.scrollIntoView &&
      updateAllRef?.current?.scrollIntoView();
    leadDistRef?.current?.scrollIntoView &&
      leadDistRef?.current?.scrollIntoView();
  }, []);

  const setAllowGroupConfigOnSummary = () => {
    if (eventDataResp && !eventDetailsData?.id) {
      const {
        planEvent: { id, offerAllowances },
      } = eventDataResp;
      const offerAllowObjIndex = offerAllowances.findIndex(
        offerObj => offerObj?.id === offerAllowancesId
      );
      const allowTypeLabel = getAllowanceTypeByPerformance(
          offerAllowances[offerAllowObjIndex]?.allowances[0]?.performance
        ),
        allowanceType = ALLOWANCE_TYPE_NAME_MAPPER[allowTypeLabel];
      dispatch(
        eventDetailsDataHandler({ ...eventDataResp, allowanceType, id })
      );
      setAllowTableGrpConfig({ allowanceType });
    }
  };

  const leftPodTopSection = () => {
    const allowTempData =
      isSummary && eventDetailsData?.id
        ? { allow_type: eventDetailsData?.allowanceType }
        : { allowanceTempWorkData };
    const { allowanceTypeDisplay, groupText, description } =
      textMapping(allowTempData);
    return (
      <div
        className={`flex flex-col justify-start items-star ${efConstants.componentClassName.ALL_ALLOWANCES_CONTAINER}`}
      >
        <p className="flex-grow-0 flex-shrink-0 text-2xl text-left">
          <span className="flex-grow-0 flex-shrink-0 text-2xl font-bold text-left text-[#2b303c] pr-2">
            Allowance Amounts
          </span>
          <span className="flex-grow-0 flex-shrink-0 text-2xl text-left text-[#2b303c] pr-2">
            |
          </span>
          <span className="flex-grow-0 flex-shrink-0 text-2xl font-bold text-left text-[#2b303c]"></span>
          <span className="flex-grow-0 flex-shrink-0 text-2xl font-bold text-left text-[#5a697b]">
            {allowanceTypeDisplay}{" "}
            {groupText && (
              <>
                <span className="px-1">-</span> {groupText}
              </>
            )}
          </span>
        </p>
        <p className="flex-grow-0 flex-shrink-0 text-base text-left text-[#2b303c]">
          {isSummary ? null : description}
        </p>
      </div>
    );
  };
  const TopRightComponent = isNational ? NationalAllAllowancesTopRight : AllAllowancesTopRight;
  function topSection() {
    return (
      <section
        className="px-[20px] py-[24px] flex border-solid border-b-[1px] border-[#C8DAEB] fixed bg-white items-center abs-all-allowances-top-section"
        style={{ zIndex: 1 }}
      >
        <div className="w-2/3" id="abs-all-allowances-container-is-summary-div">
          {(allowanceTempWorkData || isSummary) && leftPodTopSection()}
        </div>
        <div className="w-1/3 justify-self-end">
          {isSummary ? (
            SummaryBackLink
          ) : (
            <TopRightComponent
              offerId={allowanceTempWorkData?.offerAllowancesId}
              isDsd={allowGroupConfigObj?.showLeadDistributorSection}
            />
          )}
        </div>
      </section>
    );
  }
  const returnToAllowDetails = () => {
    dispatch(resetTableDataSliceData());
    navigate(
      `${basePath}/events/edit/${eventId}?isOfferViewMode=${offerAllowancesId}`
    );
  };
  const SummaryBackLink = (
    <div className="flex justify-end items-end gap-4">
      <p className="text-base font-semibold text-center text-[#1b6ebb]">
        <a
          className="cursor-pointer"
          data-testid="return-allow-details"
          onClick={() => returnToAllowDetails()}
        >
          <span> Close & Return to Allowance Details</span>
        </a>
      </p>
    </div>
  );
  const renderHtml = (
    <section className="flex flex-col p-0 main-container">
      <div className="bg-white">
        {topSection()}
        <div>
          {allowGroupConfigObj?.showFilterTabsSection && !isSummary ? (
            <section ref={leadDistRef} className="px-[20px] pt-32 relative">
              <AllowancesFiltersSelection
                allowanceCreatedTypeConfig={allowGroupConfigObj}
              />
            </section>
          ) : null}
        </div>
        <section
          ref={updateAllRef}
          className={`px-[20px] py-[24px] ${
            allowGroupConfigObj?.showFilterTabsSection && !isSummary
              ? ""
              : "pt-32"
          } flex justify-between relative`}
        >
          <TableTopAllowancesContainer />
        </section>
        {isNdpType === "true" && !isSummary && (
          <div className="bg-blue-306 flex items-center mb-4 mx-4 text-[#1b6ebb] w-fit">
            <span>{efConstants.AMOUNTS_POWERLINE_TEXT}</span>
          </div>
        )}
        {!isSummary && isNdpType === "true" && <DivisionsList />}
      </div>
      <section className={`px-[20px] table-container`}>
        {appConstants.IS_ENABLE_REDESIGN_ALLOWANCE_CHANGES ? (
          <>
            {isNdpType === "true" ? (
              <NationalOfferMainEntry />
            ) : (
              <AllowanceMainEntryTable />
            )}
          </>
        ) : (
          <AllowancesTable />
        )}
      </section>
    </section>
  );
  const renderDetails = {
    isApiLoading:
      isSummary && !eventDetailsData?.id
        ? isEventDataFetched
        : isTempworkGetCreateLoaded
        ? isTempworkGetCreateLoaded
        : false,
    isPageLevelSpinner: true,
    isRenderMainHtml:
      isSummary && !eventDetailsData?.id ? !isEventDataFetched : true,
    renderHtml: renderHtml,
  };
  return isSummary && !eventDetailsData?.id ? (
    <RenderStates details={renderDetails} />
  ) : (
    renderHtml
  );
}
