import { formatAmount } from "apps/event-flow/src/app/features/create-event/service/allowance/allowance-service";
import { editFieldHighlight } from "@me/util-helpers";
import classNames from "classnames";
import { ChevronDown } from "lucide-react";

export function getItemCount(allowances: any[]) {
  const itemIdList = allowances?.flatMap(allowance =>
    allowance?.allowanceItems?.map(item => item.itemId)
  );
  return new Set(itemIdList)?.size;
}

export function getTotalAmountSum(
  allowances: any[],
  isHeader = false,
  givenAmount?
) {
  if (givenAmount === "") return 0;
  let sum = 0;
  allowances?.forEach(allowance => {
    if (isHeader) {
      sum += parseFloat(givenAmount) || parseFloat(allowance?.headerFlatAmt);
    } else
      allowance.allowanceItems.forEach(item => {
        sum += parseFloat(givenAmount) || parseFloat(item?.allowanceAmount);
      });
  });
  return sum;
}

export function getWarehouseCount(allowances: any[]) {
  return allowances?.length;
}

export function renderField({
  label,
  value,
  icon = "",
  prefix = "$",
  formatValue = true,
}) {
  return (
    <div id="abs-allowance-amounts-services-label-container">
      <p
        className="text-sm font-bold text-left text-[#2b303c]"
        id="abs-allowance-amounts-services-label-text1"
      >
        {label}
      </p>
      <p
        className="text-base font-bold text-left text-[#033b69] mt-2 flex items-center"
        id="abs-allowance-amounts-services-fotmat-value"
      >
        <span>{`${prefix}${formatValue ? formatAmount(value) : value}`}</span>
        {icon}
      </p>
    </div>
  );
}

export function renderDummyComponent(
  label,
  required,
  value,
  isSelect,
  prefix = ""
) {
  return (
    <div id="abs-allowance-amounts-services-container">
      <div
        className="flex font-bold gap-1"
        id="abs-allowance-amounts-services-label"
      >
        <p id="abs-allowance-amounts-services-label-text">{label}</p>
        {required ? (
          <p
            className="text-sm text-left text-[#bf2912]"
            id="abs-allowance-amounts-services-required"
          >
            *
          </p>
        ) : null}
      </div>
      <div
        className="flex rounded bg-gray-205 h-[40px] border border-[#ff0000] cursor-pointer p-2 items-center justify-between"
        id="abs-allowance-amounts-services-text-container"
      >
        <p
          className={classNames({
            "text-base text-left mt-1": true,
            "": !isSelect,
          })}
          id="abs-allowance-amounts-services-value"
        >
          {prefix}
          {value}
        </p>
        {isSelect && <ChevronDown />}
      </div>
    </div>
  );
}

export const getFieldWithHighletedWrapper = (
  field,
  fieldProps,
  allowanceAmountFields,
  getEventData,
  searchId
) => {
  const borderBlueLight = `borderBlueLight`;
  const fieldPath = fieldProps.registerField.split(".");
  const fieldLen = fieldPath.length;
  const label = fieldPath[fieldLen - 1];
  return (
    <div
      className={`${
        allowanceAmountFields[`${label}Change`]
          ? borderBlueLight
          : editFieldHighlight(
              fieldProps.mapperKey,
              getEventData,
              searchId,
              "allowance"
            )
      }`}
      id="abs-allowance-amounts-services-allowance-amount-fields"
    >
      {field}
    </div>
  );
};

export const dashedVerticalDivider = (
  <div
    className="border-r-[1px] border-dashed border-[#c8daeb]"
    id="abs-allowance-amounts-services-dashed-vertical-divider"
  />
);

export function isAllowanceAmountCommon(allowances, isHeader = false) {
  if (!allowances?.length) return true;

  const allowanceValue = isHeader
    ? allowances?.[0]?.headerFlatAmt
    : allowances?.[0]?.allowanceItems[0]?.allowanceAmount;

  for (const allowance of allowances) {
    if (isHeader) {
      if (allowanceValue !== allowance?.headerFlatAmt) return false;
    } else {
      for (const item of allowance?.allowanceItems) {
        if (allowanceValue !== item?.allowanceAmount) return false;
      }
    }
  }

  return true;
}
