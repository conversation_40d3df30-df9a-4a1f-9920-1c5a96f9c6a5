import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import {
  analyticsLinkClickEvent,
  batchSetValueFields,
  checkIfAnyNotValidFields,
  checkItContainsDigit,
  compareFieldValuesAndSetValue,
  disableStoreGroupTypeFn,
  EVENT_DATES_MEMO_DEPENDENCIES,
  excludeStatuses,
  getEventDefaultState,
  getFieldNameValue,
  invalidPeriscope<PERSON>he<PERSON>,
  isPeriscopeInputValid,
  itemOrder<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  nameChanged<PERSON>and<PERSON>,
  nameChangeMemoDependencies,
  onChangeDateAndNameHandler,
  PPG_MEMO_DEPENDENCIES,
  preparePlanEventPaylod,
  promoStatusList,
  renderPastEventDateWarning,
  resetRegisterFields,
  resetValuesMemoDependency,
  shouldUpdateName,
  START_WEEK_MEMO_DEPENDENCIES,
  STORE_GROUP_TYPE_MEMO_DEPENDENCIES,
  STORE_GROUPS_MEMO_DEPENDENCIES,
  updateAllowanceOnlyEventName,
  updateEventName,
  VEHICLE_TYPE_MEMO_DEPENDENCIES,
  getPPGItemIds,
  gerneratePeriscopePPGError,
  isPeriscopeValidFromApi,
  periscopeFieldEnableStatus,
} from "./utility";
import _ from "lodash";
import { EEVENT_STATUS } from "@me/util-helpers";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";

describe("itemOrderAlphabeticalChecker", () => {
  it("should return the previous element when previousElem criteria is greater than currentElem", () => {
    const previousElem = { criteria: "10", name: "Zebra" };
    const currentElem = { criteria: "5", name: "Apple" };
    const criteria = "criteria";

    const result = itemOrderAlphabeticalChecker(
      previousElem,
      currentElem,
      criteria
    );

    expect(result).toBe(previousElem);
  });

  it("should return the previous element when previousElem and currentElem have the same criteria and previousElem name comes alphabetically before currentElem", () => {
    const previousElem = { criteria: "5", name: "Apple" };
    const currentElem = { criteria: "5", name: "Zebra" };
    const criteria = "criteria";

    const result = itemOrderAlphabeticalChecker(
      previousElem,
      currentElem,
      criteria
    );

    expect(result).toBe(previousElem);
  });

  it("should return the current element when previousElem and currentElem have the same criteria and previousElem name comes alphabetically after currentElem", () => {
    const previousElem = { criteria: "5", name: "Zebra" };
    const currentElem = { criteria: "5", name: "Apple" };
    const criteria = "criteria";

    const result = itemOrderAlphabeticalChecker(
      previousElem,
      currentElem,
      criteria
    );

    expect(result).toBe(currentElem);
  });

  it("should return the current element when previousElem criteria is less than currentElem", () => {
    const previousElem = { criteria: "3", name: "Banana" };
    const currentElem = { criteria: "5", name: "Apple" };
    const criteria = "criteria";

    const result = itemOrderAlphabeticalChecker(
      previousElem,
      currentElem,
      criteria
    );

    expect(result).toBe(currentElem);
  });

  it("should return the previous element when previousElem and currentElem have the same criteria and same name", () => {
    const previousElem = { criteria: "5", name: "Apple" };
    const currentElem = { criteria: "5", name: "Apple" };
    const criteria = "criteria";

    const result = itemOrderAlphabeticalChecker(
      previousElem,
      currentElem,
      criteria
    );

    expect(result).toStrictEqual(previousElem);
  });
});

describe("Memo Dependency Functions", () => {
  it("should return correct dependencies for EVENT_DATES_MEMO_DEPENDENCIES", () => {
    const formFields = {
      customStartDate: "2024-01-01",
      customEndDate: "2024-12-31",
      isDivisionChanged: true,
    };

    const result = EVENT_DATES_MEMO_DEPENDENCIES(formFields);

    expect(result).toEqual(["2024-01-01", "2024-12-31", true]);
  });

  it("should handle missing values in EVENT_DATES_MEMO_DEPENDENCIES gracefully", () => {
    const formFields = {};

    const result = EVENT_DATES_MEMO_DEPENDENCIES(formFields);

    expect(result).toEqual([undefined, undefined, undefined]);
  });

  it("should return correct dependencies for START_WEEK_MEMO_DEPENDENCIES", () => {
    const formFields = {
      enableStartWeekVehicle: true,
      vehicleTypeId: "vehicle123",
      isPastDate: false,
      vehicleTypeProps: "SUV",
      yearVal: "2024",
    };

    const result = START_WEEK_MEMO_DEPENDENCIES(formFields);

    expect(result).toEqual([true, "vehicle123", false, "SUV", "2024"]);
  });

  it("should handle missing values in START_WEEK_MEMO_DEPENDENCIES gracefully", () => {
    const formFields = {};

    const result = START_WEEK_MEMO_DEPENDENCIES(formFields);

    expect(result).toEqual([
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
    ]);
  });

  it("should return correct dependencies for VEHICLE_TYPE_MEMO_DEPENDENCIES", () => {
    const formFields = {
      isDivisionChanged: false,
      vehicleTypeProps: "Truck",
      isPastDate: true,
      vehicleTypeId: "vehicle567",
    };

    const result = VEHICLE_TYPE_MEMO_DEPENDENCIES(formFields);

    expect(result).toEqual([false, "Truck", true, "vehicle567"]);
  });

  it("should handle missing values in VEHICLE_TYPE_MEMO_DEPENDENCIES gracefully", () => {
    const formFields = {};

    const result = VEHICLE_TYPE_MEMO_DEPENDENCIES(formFields);

    expect(result).toEqual([undefined, undefined, undefined, undefined]);
  });

  it("should return correct dependencies for STORE_GROUPS_MEMO_DEPENDENCIES", () => {
    const formFields = {
      groupInd: true,
      promoProductGroupName: "Group A",
      divisionId: "div123",
      disableStoreGroups: false,
    };

    const result = STORE_GROUPS_MEMO_DEPENDENCIES(formFields);

    expect(result).toEqual([true, "Group A", "div123", false]);
  });

  it("should handle missing values in STORE_GROUPS_MEMO_DEPENDENCIES gracefully", () => {
    const formFields = {};

    const result = STORE_GROUPS_MEMO_DEPENDENCIES(formFields);

    expect(result).toEqual([undefined, undefined, undefined, undefined]);
  });

  it("should return correct dependencies for PPG_MEMO_DEPENDENCIES", () => {
    const formFields = {
      divisionId: "div123",
      promoProductGroupName: "Promo A",
      isCICModalOpen: true,
      cicsErrorMessage: "Error occurred",
      unresolvedItemsError: false,
    };

    const result = PPG_MEMO_DEPENDENCIES(formFields);

    expect(result).toEqual([
      "div123",
      "Promo A",
      true,
      "Error occurred",
      false,
    ]);
  });

  it("should handle missing values in PPG_MEMO_DEPENDENCIES gracefully", () => {
    const formFields = {};

    const result = PPG_MEMO_DEPENDENCIES(formFields);

    expect(result).toEqual([
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
    ]);
  });

  it("should return correct dependencies for STORE_GROUP_TYPE_MEMO_DEPENDENCIES", () => {
    const formFields = {
      groupInd: false,
      disableStoreGroupType: true,
      enableStoreGroupType: false,
    };

    const result = STORE_GROUP_TYPE_MEMO_DEPENDENCIES(formFields);

    expect(result).toEqual([false, true, false]);
  });

  it("should handle missing values in STORE_GROUP_TYPE_MEMO_DEPENDENCIES gracefully", () => {
    const formFields = {};

    const result = STORE_GROUP_TYPE_MEMO_DEPENDENCIES(formFields);

    expect(result).toEqual([undefined, undefined, undefined]);
  });
});

describe("getFieldNameValue", () => {
  // Mocked data for EVENT_TYPES and getValues function
  const EVENT_TYPES = {
    event_abbreviations: {
      eventA: "eventA_type",
      eventB: "eventB_type",
    },
    eventA_type: {
      generate_event_name: ["field1", "field2"],
    },
    eventB_type: {
      generate_event_name: ["field3"],
    },
  };

  const getValues = jest.fn().mockImplementation(field => {
    const mockValues = {
      promoProductGroup: "Value 1",
      startWeekVehicle: "Value 2",
    };
    return mockValues[field];
  });

  beforeEach(() => {
    // Clear mocks before each test to avoid interference
    // _.isEmpty.mockClear();
    getValues.mockClear();
  });

  it("should return event name based on state.eventType when provided", () => {
    const state = {
      eventType: "division_promotion",
    };

    const eventDetailsData = {
      eventType: "DP",
    };
    // _.isEmpty.mockReturnValue(false); // state.eventType is not empty

    const result = getFieldNameValue(state, eventDetailsData, getValues);

    // The eventType is 'eventA' so it should fetch values for field1 and field2
    expect(result).toBe("Value 1 - Value 2");
    expect(getValues).toHaveBeenCalledWith("promoProductGroup");
    expect(getValues).toHaveBeenCalledWith("startWeekVehicle");
  });

  it("should return event name based on eventDetailsData.eventType when state.eventType is empty", () => {
    const state = {};

    const eventDetailsData = {
      eventType: "DP",
    };
    const result = getFieldNameValue(state, eventDetailsData, getValues);

    expect(result).toBe("Value 1 - Value 2");
  });

  it("should handle undefined or null state gracefully", () => {
    const result = getFieldNameValue(null, {}, getValues);
    expect(result).toBe(undefined);
  });
});

describe("disableStoreGroupTypeFn", () => {
  it("should return true when eventId is provided and offerAllowances or promotionsList is not empty", () => {
    const eventId = 1; // truthy value
    const eventDetailsData = {
      offerAllowances: [1, 2], // not empty
      promotionsList: [], // empty
    };
    const defaultValues = {}; // default value not relevant in this case

    const result = disableStoreGroupTypeFn(
      eventId,
      eventDetailsData,
      defaultValues
    );

    // It should return true because offerAllowances is not empty
    expect(result).toBe(true);
  });

  it("should return false when eventId is provided and both offerAllowances and promotionsList are empty", () => {
    const eventId = 1;
    const eventDetailsData = {
      offerAllowances: [], // empty
      promotionsList: [], // empty
    };
    const defaultValues = {};

    const result = disableStoreGroupTypeFn(
      eventId,
      eventDetailsData,
      defaultValues
    );

    // It should return false because both offerAllowances and promotionsList are empty
    expect(result).toBe(false);
  });

  it("should return false when eventId is falsy and planProductGroups has items", () => {
    const eventId = null; // falsy value
    const eventDetailsData = {}; // eventDetailsData not relevant in this case
    const defaultValues = {
      planProductGroups: ["group1"], // not empty
    };

    const result = disableStoreGroupTypeFn(
      eventId,
      eventDetailsData,
      defaultValues
    );

    // It should return false because planProductGroups is not empty
    expect(result).toBe(false);
  });

  it("should return true when eventId is falsy and planProductGroups is empty", () => {
    const eventId = null; // falsy value
    const eventDetailsData = {}; // eventDetailsData not relevant in this case
    const defaultValues = {
      planProductGroups: [], // empty
    };

    const result = disableStoreGroupTypeFn(
      eventId,
      eventDetailsData,
      defaultValues
    );

    // It should return true because planProductGroups is empty
    expect(result).toBe(true);
  });

  it("should return true when eventId is falsy and planProductGroups is undefined", () => {
    const eventId = null; // falsy value
    const eventDetailsData = {}; // eventDetailsData not relevant in this case
    const defaultValues = {
      planProductGroups: undefined, // undefined
    };

    const result = disableStoreGroupTypeFn(
      eventId,
      eventDetailsData,
      defaultValues
    );

    // It should return true because planProductGroups is undefined
    expect(result).toBe(true);
  });
});

describe("getEventDefaultState", () => {
  const getValuesMock = jest.fn();
  const eventDetailsData = {}; // Mock event details data if needed
  const eventId = 123; // Example event ID
  beforeEach(() => {
    // Clear all mocks before each test to avoid interference
    localStorage.clear();
    getValuesMock.mockClear();
  });

  it("should return default state with values from localStorage and getValues", () => {
    // Mocking localStorage.getItem
    const userFilters = {
      divisionIds: ["division-1"],
    };
    global.localStorage.setItem("USER_FILTERS", JSON.stringify(userFilters));

    // Mocking getValues to return predefined values
    getValuesMock.mockImplementation(field => {
      const mockValues = {
        divisionIds: ["division-2"],
        storeGroupType: "store-group-type",
        eventCreationVehicle: {
          year: 2023,
          startDate: "2023-01-01",
          endDate: "2023-12-31",
        },
        vehicleType: "Custom Date",
        planProductGroups: ["group-1"],
        periscopeDetails: [{ periscopeId: "123" }],
        id: "1234",
        endDate: "2023-12-31",
        startDate: "2023-01-01",
      };
      return field ? mockValues[field] : mockValues;
    });

    const defaultState = getEventDefaultState(
      getValuesMock,
      eventDetailsData,
      eventId
    );

    expect(defaultState.isDivisionChanged).toBe(false);
    expect(defaultState.divisionId).toBe("division-2"); // From getValues mock
    expect(defaultState.groupInd).toBe("store-group-type"); // From getValues mock
    expect(defaultState.count).toBe(0);
    expect(defaultState.yearVal).toBe(2023); // From getValues mock
    expect(defaultState.customEndDate).toBe("2023-12-31"); // From getValues mock
    expect(defaultState.customStartDate).toBe("2023-01-01"); // From getValues mock
    expect(defaultState.vehicleTypeId).toBe("");
    expect(defaultState.vehicleTypeProps).toBe("Custom Date"); // From getValues mock
    expect(defaultState.disableStoreGroupType).toBe(false); // From disableStoreGroupTypeFn mock
    expect(defaultState.isMutliVendorPPG).toBe(false);
    expect(defaultState.canCreateEvent).toBe(true);
    expect(defaultState.periscopeValid).toBe(true);
    expect(defaultState.isPastDate).toBe(false);
    expect(defaultState.enableStartWeekVehicle).toBe(false);
    expect(defaultState.enableStoreGroupType).toBe(false); // From getValues mock
    expect(defaultState.disableStoreGroups).toBe(false); // From disableStoreGroupTypeFn mock
    expect(defaultState.promoProductGroupName).toBe("");
    expect(defaultState.periscopeIdState).toBe("123"); // From getValues mock
    expect(defaultState.warningCount).toBe(0);
    expect(defaultState.isUpdateBtnDisabled).toBe("1234"); // Because getValues("id") is truthy
    expect(defaultState.cicsErrorMessage).toBe("");
    expect(defaultState.unresolvedItemsError).toBe(false);
    expect(defaultState.isCICModalOpen).toBe(false);
    expect(defaultState.eventError).toBe(false);
  });

  it("should return default state with values from localStorage when getValues returns undefined", () => {
    // Mocking localStorage.getItem
    const userFilters = {
      divisionIds: ["division-1"],
    };
    global.localStorage.setItem("USER_FILTERS", JSON.stringify(userFilters));

    // Mocking getValues to return undefined
    getValuesMock.mockImplementation(() => undefined);

    const defaultState = getEventDefaultState(
      getValuesMock,
      eventDetailsData,
      eventId
    );

    expect(defaultState.divisionId).toBe("division-1"); // From localStorage
  });

  it("should return default state with empty divisionId if localStorage has no USER_FILTERS", () => {
    // Clear any previous localStorage
    global.localStorage.clear();

    // Mocking getValues to return mock values
    getValuesMock.mockImplementation(field => {
      const mockValues = {
        divisionIds: [],
      };
      return field ? mockValues[field] : mockValues;
    });

    const defaultState = getEventDefaultState(
      getValuesMock,
      eventDetailsData,
      eventId
    );

    expect(defaultState.divisionId).toBe(""); // No USER_FILTERS in localStorage
  });

  xit('should return correct state when vehicleType is "Custom Date"', () => {
    const userFilters = {
      divisionIds: ["division-1"],
    };
    global.localStorage.setItem("USER_FILTERS", JSON.stringify(userFilters));

    // Mocking getValues to return "Custom Date" for vehicleType
    getValuesMock.mockImplementation(field => {
      const mockValues = {
        vehicleType: "Custom Date", // "Custom Date" should trigger the custom date logic
        startDate: "2023-01-01",
        endDate: "2023-12-31",
      };
      return mockValues[field];
    });

    const defaultState = getEventDefaultState(
      getValuesMock,
      eventDetailsData,
      eventId
    );

    expect(defaultState.customStartDate).toBe("2023-01-01"); // Custom start date
    expect(defaultState.customEndDate).toBe("2023-12-31"); // Custom end date
  });

  xit('should return correct state when vehicleType is not "Custom Date"', () => {
    const userFilters = {
      divisionIds: ["division-1"],
    };
    global.localStorage.setItem("USER_FILTERS", JSON.stringify(userFilters));

    // Mocking getValues to return a non "Custom Date" vehicleType
    getValuesMock.mockImplementation(field => {
      const mockValues = {
        vehicleType: "Car", // Not "Custom Date"
        startDate: "2023-01-01",
        endDate: "2023-12-31",
        "eventCreationVehicle.endDate": "2023-12-31",
      };
      return mockValues[field];
    });

    // Mocking disableStoreGroupTypeFn

    const defaultState = getEventDefaultState(
      getValuesMock,
      eventDetailsData,
      eventId
    );

    expect(defaultState.customStartDate).toBe("2023-12-31"); // Default to eventCreationVehicle.endDate
    expect(defaultState.customEndDate).toBe("2023-12-31"); // Default to eventCreationVehicle.endDate
  });
});

describe("checkItContainsDigit", () => {
  let setFormFieldsMock;

  beforeEach(() => {
    setFormFieldsMock = jest.fn();
  });

  it("should return the correct length of periscopeIds and update periscopeValid to true when PID contains digits", () => {
    const PID = "12345";

    // Call the function
    const result = checkItContainsDigit(PID, setFormFieldsMock);

    // It should return the length of the periscopeIds array, which is 1
    expect(result).toBe(1);

    // It should update the periscopeValid state to true since the PID matches the regex
    expect(setFormFieldsMock).toHaveBeenCalled();
  });

  it("should return 0 and update periscopeValid to false when PID does not contain only digits", () => {
    const PID = "abc123";

    // Call the function
    const result = checkItContainsDigit(PID, setFormFieldsMock);

    // It should return 0 because PID does not match the regex
    expect(result).toBe(0);

    // It should update the periscopeValid state to false
    expect(setFormFieldsMock).toHaveBeenCalled();
  });

  it("should return 0 and update periscopeValid to false when PID is an empty string", () => {
    const PID = "";

    // Call the function
    const result = checkItContainsDigit(PID, setFormFieldsMock);

    // It should return 0 because PID is an empty string
    expect(result).toBe(0);

    // It should update the periscopeValid state to false
    expect(setFormFieldsMock).toHaveBeenCalled();
  });

  it("should return 0 and update periscopeValid to false when PID is undefined", () => {
    const PID = undefined;

    // Call the function
    const result = checkItContainsDigit(PID, setFormFieldsMock);

    // It should return 0 because PID is undefined
    expect(result).toBe(0);

    // It should update the periscopeValid state to false
    expect(setFormFieldsMock).toHaveBeenCalled();
  });
});

describe("preparePlanEventPaylod", () => {
  let data;
  let formFields;
  let getValues;

  beforeEach(() => {
    // Initialize mocks
    data = {
      planProductGroups: [{ id: "123" }, { planProductGroupId: "456" }],
      storeGroups: [{ id: "789" }, { storeGroupId: "101" }],
      divisionIds: ["div1", "div2"],
    };

    formFields = {
      periscopeIdState: "periscope123",
    };

    getValues = jest
      .fn()
      .mockReturnValueOnce("2023-01-01")
      .mockReturnValueOnce("2023-12-31"); // Mock return values for startDate and endDate
  });

  it("should map data to EventGroupFilter structure correctly", () => {
    // Call the function with the mock data
    data = {
      planProductGroups: [{ id: "123" }, { planProductGroupId: "456" }],
      storeGroups: [
        { id: "789", divisionIds: [1, 2] },
        { storeGroupId: "101" },
      ],
      divisionIds: ["div1", "div2"],
    };
    const result = preparePlanEventPaylod(
      data,
      formFields,
      getValues,
      "NA",
      [1, 2]
    );
    expect(result).toEqual({
      divisionIds: ["div1", "div2"],
      endDate: "2023-12-31",
      periscopeIds: ["periscope123"],
      promoProductGroups: ["123", "456"],
      startDate: "2023-01-01",
      storedIds: ["789"],
    });
    // Verify that getValues was called twice for 'startDate' and 'endDate'
    expect(getValues).toHaveBeenCalledWith("startDate");
    expect(getValues).toHaveBeenCalledWith("endDate");
  });

  it("should handle missing periscopeIdState correctly", () => {
    // Modify formFields to have no periscopeIdState
    formFields.periscopeIdState = null;

    const result = preparePlanEventPaylod(data, formFields, getValues);

    // Verify periscopeIds is an empty array when periscopeIdState is missing
    expect(result.periscopeIds).toEqual([]);
  });

  it("should handle missing or undefined planProductGroups and storeGroups correctly", () => {
    // Remove planProductGroups and storeGroups from data
    data.planProductGroups = undefined;
    data.storeGroups = undefined;

    const result = preparePlanEventPaylod(data, formFields, getValues);

    // Verify empty arrays are returned for promoProductGroups and storedIds
    expect(result.promoProductGroups).toEqual(undefined);
    expect(result.storedIds).toEqual(undefined);
  });

  it("should handle empty values in planProductGroups and storeGroups arrays correctly", () => {
    // Set planProductGroups and storeGroups to empty arrays
    data.planProductGroups = [];
    data.storeGroups = [];

    const result = preparePlanEventPaylod(data, formFields, getValues);

    // Verify empty arrays are returned
    expect(result.promoProductGroups).toEqual([]);
    expect(result.storedIds).toEqual([]);
  });
});

describe("invalidPeriscopeChecker", () => {
  let setDisplayPeriscopeErrorsMock;
  let setLabelsMock;
  let itemData;

  beforeEach(() => {
    setDisplayPeriscopeErrorsMock = jest.fn();
    setLabelsMock = jest.fn();

    // Mock data for itemData
    itemData = {
      data: {
        periscopeDetails: [
          {
            periscopeMessages: ["ITEMS_NOT_MATCH", "INVALID_PID"],
          },
          {
            periscopeMessages: ["DATES_NOT_MATCH"],
          },
        ],
      },
    };
  });

  it("should map error messages to labels and update display errors correctly", () => {
    // Call the function
    const result = invalidPeriscopeChecker(
      itemData,
      setDisplayPeriscopeErrorsMock,
      setLabelsMock
    );

    // Verify the return value is the correct number of errors
    expect(result).toBe(3);

    // Verify that setLabels was called with the first periscope message details
    expect(setLabelsMock).toHaveBeenCalledWith([
      "ITEMS_NOT_MATCH",
      "INVALID_PID",
    ]);

    // Verify that setDisplayPeriscopeErrors was called with the correct labels
    expect(setDisplayPeriscopeErrorsMock).toHaveBeenCalledWith([
      "Item(s) are not matching with associated PID details.",
      "This PID is invalid",
      "Dates are not matching with associated PID details.",
    ]);
  });

  it("should return 0 and not call setDisplayPeriscopeErrors when there are no error messages", () => {
    // Modify itemData to have no periscopeMessages
    itemData.data.periscopeDetails = [
      { periscopeMessages: [] },
      { periscopeMessages: [] },
    ];

    // Call the function
    const result = invalidPeriscopeChecker(
      itemData,
      setDisplayPeriscopeErrorsMock,
      setLabelsMock
    );

    // It should return 0 because there are no error messages
    expect(result).toBe(0);

    // Verify that setDisplayPeriscopeErrors was not called
    expect(setDisplayPeriscopeErrorsMock).toHaveBeenCalledTimes(2);
    expect(setDisplayPeriscopeErrorsMock).toHaveBeenLastCalledWith([]);
  });

  it("should return 0 and not call setDisplayPeriscopeErrors if there are no periscopeDetails in the itemData", () => {
    // Modify itemData to have no periscopeDetails
    itemData.data.periscopeDetails = undefined;

    // Call the function
    const result = invalidPeriscopeChecker(
      itemData,
      setDisplayPeriscopeErrorsMock,
      setLabelsMock
    );

    // It should return 0 because there are no periscope details
    expect(result).toBe(0);

    // Verify that setDisplayPeriscopeErrors was not called
    expect(setDisplayPeriscopeErrorsMock).not.toHaveBeenCalled();
  });
});

describe("shouldUpdateName", () => {
  let getValuesMock;
  let formFields;

  beforeEach(() => {
    // Initialize the mock for getValues
    getValuesMock = jest.fn();

    // Initialize formFields with a default count value
    formFields = {
      count: 0,
    };
  });

  it("should return true when all conditions are met", () => {
    // Mock getValues to return truthy values for the required fields
    getValuesMock
      .mockReturnValueOnce("somePromoProductGroup") // promoProductGroup
      .mockReturnValueOnce("someVehicleType") // vehicleType
      .mockReturnValueOnce(undefined); // startWeekVehicle is not checked here

    // Call the function with the mock data
    const result = shouldUpdateName(getValuesMock, formFields);

    // Verify the result is true
    expect(result).toBe(true);
  });

  it("should return false when promoProductGroup is falsy and vehicle type is truthy", () => {
    // Mock getValues to return falsy for promoProductGroup
    getValuesMock
      .mockReturnValueOnce("") // promoProductGroup is falsy
      .mockReturnValueOnce("someVehicleType") // vehicleType is truthy
      .mockReturnValueOnce(undefined); // startWeekVehicle

    const result = shouldUpdateName(getValuesMock, formFields);

    expect(result).toStrictEqual("");
  });

  it("should return false when vehicleType and startWeekVehicle are both falsy", () => {
    // Mock getValues to return falsy for both vehicleType and startWeekVehicle
    getValuesMock
      .mockReturnValueOnce("somePromoProductGroup") // promoProductGroup is truthy
      .mockReturnValueOnce("") // vehicleType is falsy
      .mockReturnValueOnce(""); // startWeekVehicle is falsy

    const result = shouldUpdateName(getValuesMock, formFields);

    expect(result).toStrictEqual("");
  });

  it("should return false when formFields.count is not 0", () => {
    // Modify formFields to have count other than 0
    formFields.count = 1;

    // Mock getValues to return truthy for the required fields
    getValuesMock
      .mockReturnValueOnce("somePromoProductGroup") // promoProductGroup is truthy
      .mockReturnValueOnce("someVehicleType") // vehicleType is truthy
      .mockReturnValueOnce(undefined); // startWeekVehicle is not needed

    const result = shouldUpdateName(getValuesMock, formFields);

    expect(result).toBe(false);
  });

  it("should return false when promoProductGroup is falsy, even if other conditions are met", () => {
    // Modify the mock data so that promoProductGroup is falsy
    getValuesMock
      .mockReturnValueOnce("") // promoProductGroup is falsy
      .mockReturnValueOnce("someVehicleType") // vehicleType is truthy
      .mockReturnValueOnce(undefined); // startWeekVehicle

    const result = shouldUpdateName(getValuesMock, formFields);

    expect(result).toStrictEqual("");
  });
});

describe("updateEventName", () => {
  let getValuesMock;
  let setValueMock;
  let eventDetailsData;
  let state;

  beforeEach(() => {
    // Mock getValues and setValue
    getValuesMock = jest.fn();
    setValueMock = jest.fn();

    // Mock default values for state and eventDetailsData
    state = {
      someState: "someValue", // Example state
    };

    eventDetailsData = {
      name: "Event A", // Example event details data
      eventType: "DP",
    };

    // Default mock for getValues
    getValuesMock.mockReturnValue("Event B");
  });

  it("should update the event name if eventDetailsData.name is different from the form name", () => {
    // Call the function with mocked data
    updateEventName({
      eventName: { registerField: "eventNameField" },
      eventDetailsData,
      state,
      getValues: getValuesMock,
      setValue: setValueMock,
    });

    // Verify that setValue was called with the correct field and value
    expect(setValueMock).toHaveBeenCalledWith(
      "eventNameField",
      "Event B - Event B"
    );
  });
  it("should update the event name if eventDetailsData.name is same from the form name", () => {
    getValuesMock.mockReturnValue("Event A");
    // Call the function with mocked data
    updateEventName({
      eventName: { registerField: "eventNameField" },
      eventDetailsData,
      state,
      getValues: getValuesMock,
      setValue: setValueMock,
    });

    // Verify that setValue was called with the correct field and value
    expect(setValueMock).not.toHaveBeenCalledWith(
      "eventNameField",
      "Event B - Event B"
    );
  });
});

describe("isPeriscopeInputValid", () => {
  let setFormFieldsMock;
  let formFields;

  // Mocking checkItContainsDigit
  // jest.mock('./utility', () => ({
  //   ...jest.requireActual("./utility"),
  //   checkItContainsDigit: jest.fn(),
  // }));

  beforeEach(() => {
    // Mock setFormFields function
    setFormFieldsMock = jest.fn();

    // Default formFields object
    formFields = {
      periscopeIdState: "123456",
    };
  });

  it("should return true when periscopeIdState is falsy", () => {
    formFields.periscopeIdState = "";
    const result = isPeriscopeInputValid({
      formFields,
      setFormFields: setFormFieldsMock,
    });
    expect(result).toBe(true);
  });

  it("should return true when periscopeIdState is valid (checkItContainsDigit returns true)", () => {
    const result = isPeriscopeInputValid({
      formFields,
      setFormFields: setFormFieldsMock,
    });
    expect(result).toBe(true);
  });

  it("should return false when periscopeIdState is invalid (checkItContainsDigit returns false)", () => {
    formFields = {
      periscopeIdState: "agv",
    };
    // Call the function with invalid periscopeIdState
    const result = isPeriscopeInputValid({
      formFields,
      setFormFields: setFormFieldsMock,
    });

    // Verify the result is false
    expect(result).toBe(false);
  });
});

describe("promoStatusList", () => {
  it("should return true when all promotions have a status in excludeStatuses", () => {
    const eventDetailsData = {
      promotionsLists: [
        {
          promotionsList: [
            { promotionWorkflowStatus: "Rejected" },
            { promotionWorkflowStatus: "Canceled" },
            { promotionWorkflowStatus: "Active" },
          ],
        },
      ],
    };

    const result = promoStatusList({ eventDetailsData });
    expect(result).toBe(true); // Expect true since all statuses are in excludeStatuses
  });

  it("should return false when at least one promotion has a status not in excludeStatuses", () => {
    const eventDetailsData = {
      promotionsLists: [
        {
          promotionsList: [
            { promotionWorkflowStatus: "Rejected" },
            { promotionWorkflowStatus: "Pending" }, // This status is not in excludeStatuses
          ],
        },
      ],
    };

    const result = promoStatusList({ eventDetailsData });
    expect(result).toBe(false); // Expect false since 'PENDING' is not in excludeStatuses
  });

  it("should return true when promotionsList is empty but valid structure exists", () => {
    const eventDetailsData = {
      promotionsLists: [{ promotionsList: [] }],
    };

    const result = promoStatusList({ eventDetailsData });
    expect(result).toBe(true); // Expect true because there are no promotions to invalidate the check
  });

  it("should return false when some promotions do not have a promotionWorkflowStatus field", () => {
    const eventDetailsData = {
      promotionsLists: [
        {
          promotionsList: [
            { promotionWorkflowStatus: "Rejected" },
            { promotionWorkflowStatus: "Active" },
            {}, // No promotionWorkflowStatus field
          ],
        },
      ],
    };

    const result = promoStatusList({ eventDetailsData });
    expect(result).toBe(false); // Expect false because one of the promotions is missing the status
  });
});

describe("updateAllowanceOnlyEventName", () => {
  let setValueMock;
  let eventName;
  let eventDetailsData;
  let state;
  let getValuesMock;

  beforeEach(() => {
    // Mock setValue function
    setValueMock = jest.fn();

    // Mock getValues function
    getValuesMock = jest.fn().mockReturnValueOnce("Updated Event Name");

    // Dummy values
    eventName = { registerField: "event_name" };
    eventDetailsData = {
      eventType: "DP",
      eventName: "Test Event",
    };
    state = null;
  });

  it("should update event name when formEventName is truthy", () => {
    // Call the function
    updateAllowanceOnlyEventName({
      eventName,
      eventDetailsData,
      state,
      getValues: getValuesMock,
      setValue: setValueMock,
    });

    // Ensure setValue is called with correct arguments
    expect(setValueMock).toHaveBeenCalledWith(
      eventName.registerField,
      "Updated Event Name"
    );
  });

  it("should not update event name when formEventName is falsy", () => {
    eventDetailsData = {};
    state = null;

    // Call the function
    updateAllowanceOnlyEventName({
      eventName,
      eventDetailsData,
      state,
      getValues: getValuesMock,
      setValue: setValueMock,
    });

    // Ensure setValue is NOT called
    expect(setValueMock).not.toHaveBeenCalled();
  });

  it("should not update event name when eventDetailsData or state is empty", () => {
    // Test with empty eventDetailsData and state
    eventDetailsData = {};
    state = {};

    updateAllowanceOnlyEventName({
      eventName,
      eventDetailsData,
      state,
      getValues: getValuesMock,
      setValue: setValueMock,
    });

    // Ensure setValue is called
    expect(setValueMock).not.toHaveBeenCalledWith(
      eventName.registerField,
      "Updated Event Name"
    );
  });
});

describe("onChangeDateAndNameHandler", () => {
  let setValueMock;
  let setFormFieldsMock;
  let getValuesMock;
  let element;

  beforeEach(() => {
    // Mock setValue and setFormFields
    setValueMock = jest.fn();
    setFormFieldsMock = jest.fn();

    // Mock getValues function
    getValuesMock = jest.fn().mockReturnValue(false);

    // Mock the element object with a default value
    element = {
      target: {
        value: "Test Event",
      },
    };
  });
  afterEach(() => {
    jest.clearAllMocks();
  });

  xit("should set eventError to true when element.target.value is empty", () => {
    // Simulate element.value being empty
    element.target.value = "";

    onChangeDateAndNameHandler({
      element,
      getValues: getValuesMock,
      setValue: setValueMock,
      setFormFields: setFormFieldsMock,
    });

    // Check that setFormFields was called with eventError set to true
    expect(setFormFieldsMock).toHaveBeenCalled();
  });

  xit("should not set eventError when element.target.value is not empty", () => {
    // Simulate element.value being non-empty
    element.target.value = "Some Event";
    const getValuesMock = jest.fn(field => {
      if (field === "name") return "Some Event"; // or any valid string
      return "";
    });
    onChangeDateAndNameHandler({
      element,
      getValues: getValuesMock,
      setValue: setValueMock,
      setFormFields: setFormFieldsMock,
    });

    // Check that setFormFields was called with eventError set to false
    expect(setFormFieldsMock).toHaveBeenCalled();
  });

  it("should call setValue to update name when name does not match element.target.value", () => {
    // Mock getValues to return a name that does not match the element.value
    const getValuesMock = jest.fn().mockImplementation(field => {
      const mockValues = {
        name: "Old Event",
        dataFetchedFromPeriscope: false,
      };
      return mockValues[field];
    });

    onChangeDateAndNameHandler({
      element,
      getValues: getValuesMock,
      setValue: setValueMock,
      setFormFields: setFormFieldsMock,
    });

    // Check that setValue was called to update the name
    expect(setValueMock).toHaveBeenCalledWith("name", "Test Event");
  });

  it("should not call setValue to update name when name already matches element.target.value", () => {
    // Mock getValues to return the same name as element.target.value
    getValuesMock.mockReturnValue("Test Event");

    onChangeDateAndNameHandler({
      element,
      getValues: getValuesMock,
      setValue: setValueMock,
      setFormFields: setFormFieldsMock,
    });

    // Check that setValue was NOT called
    expect(setValueMock).not.toHaveBeenCalled();
  });
});

describe("nameChangedHandler", () => {
  let setValueMock;
  let getValuesMock;
  let stateMock;
  let formFieldsMock;
  let eventDetailsDataMock;
  let eventNameMock;
  let allowanceOnlyEventMock;

  beforeEach(() => {
    // Mock functions
    setValueMock = jest.fn();
    getValuesMock = jest.fn();
    stateMock = {}; // Provide any mock state if necessary
    formFieldsMock = { count: 0 }; // Simulate the valid form fields
    eventDetailsDataMock = {}; // Provide event details data if necessary
    eventNameMock = { registerField: "name" };
    allowanceOnlyEventMock = {}; // Simulate with or without allowanceOnlyEvent
  });

  it("should call updateAllowanceOnlyEventName when allowanceOnlyEvent is true and all required fields are valid", () => {
    // Mocking shouldUpdateName to return true (valid form fields)
    // require('./utility').shouldUpdateName.mockReturnValue(true);
    (getValuesMock as jest.Mock)
      .mockReturnValueOnce("Event A")
      .mockReturnValueOnce("Event B");

    // Simulating allowanceOnlyEvent to be true
    allowanceOnlyEventMock.isAllowanceOnlyEventType = true;

    // Call the handler
    nameChangedHandler({
      state: stateMock,
      getValues: getValuesMock,
      setValue: setValueMock,
      eventName: eventNameMock,
      eventDetailsData: eventDetailsDataMock,
      formFields: formFieldsMock,
      allowanceOnlyEvent: allowanceOnlyEventMock,
    });
  });

  it("should call updateEventName when allowanceOnlyEvent is false and all required fields are valid", () => {
    // Simulating allowanceOnlyEvent to be false (undefined or false)
    allowanceOnlyEventMock.isAllowanceOnlyEventType = false;
    (getValuesMock as jest.Mock)
      .mockReturnValueOnce("Event A")
      .mockReturnValueOnce("Event B");
    // Call the handler
    nameChangedHandler({
      state: stateMock,
      getValues: getValuesMock,
      setValue: setValueMock,
      eventName: eventNameMock,
      eventDetailsData: eventDetailsDataMock,
      formFields: formFieldsMock,
      allowanceOnlyEvent: allowanceOnlyEventMock,
    });
  });
});

describe("Utility functions", () => {
  let getValuesMock;

  beforeEach(() => {
    // Mock getValues to return specific values for various keys
    getValuesMock = jest.fn();
  });

  describe("resetValuesMemoDependency", () => {
    it("should return the correct dependencies when called", () => {
      // Mocking getValues to return values for different keys
      getValuesMock.mockImplementation(key => {
        switch (key) {
          case "divisionIds":
            return [1, 2];
          case "planProductGroups":
            return ["PPG1", "PPG2"];
          case "planStoreGroupType":
            return "GroupType";
          case "storeGroups":
            return ["Store1", "Store2"];
          case "eventCreationVehicle.vehicleType.vehicleTypeId":
            return "vehicleTypeId";
          case "eventCreationVehicle.year":
            return "2023";
          case "eventCreationVehicle.startDate":
            return "2023-01-01";
          case "eventCreationVehicle.endDate":
            return "2023-12-31";
          case "name":
            return "Event Name";
          case "periscopeFormField":
            return "periscopeField";
          default:
            return undefined;
        }
      });

      // Call the function with the mock getValues
      const result = resetValuesMemoDependency(getValuesMock);

      // Check that the result contains the expected values
      expect(result).toEqual([
        [1, 2], // divisionIds
        ["PPG1", "PPG2"], // planProductGroups
        "GroupType", // planStoreGroupType
        ["Store1", "Store2"], // storeGroups
        "vehicleTypeId", // eventCreationVehicle.vehicleType.vehicleTypeId
        "2023", // eventCreationVehicle.year
        "2023-01-01", // eventCreationVehicle.startDate
        "2023-12-31", // eventCreationVehicle.endDate
        "Event Name", // name
        "periscopeField", // periscopeFormField
      ]);
    });
  });

  describe("nameChangeMemoDependencies", () => {
    it("should return the correct dependencies for name change", () => {
      // Mocking getValues to return values for specific keys
      getValuesMock.mockImplementation(key => {
        switch (key) {
          case "promoProductGroup":
            return "PromoGroup1";
          case "storeGroupName":
            return "StoreGroup1";
          case "vehicleType":
            return "VehicleType1";
          case "startWeekVehicle":
            return "StartWeekVehicle1";
          default:
            return undefined;
        }
      });

      // Call the function with the mock getValues
      const result = nameChangeMemoDependencies(getValuesMock);

      // Check that the result contains the expected values
      expect(result).toEqual([
        "PromoGroup1", // promoProductGroup
        "StoreGroup1", // storeGroupName
        "VehicleType1", // vehicleType
        "StartWeekVehicle1", // startWeekVehicle
      ]);
    });
  });
});

describe("compareFieldValuesAndSetValue", () => {
  let getValuesMock;
  let setFormFieldsMock;
  let eventDetailsDataMock;

  beforeEach(() => {
    // Mocking getValues to return values for various form fields
    getValuesMock = jest.fn();
    setFormFieldsMock = jest.fn();
    eventDetailsDataMock = {
      divisionIds: [1, 2],
      planProductGroups: ["PPG1", "PPG2"],
      planStoreGroupType: "GroupType",
      eventCreationVehicle: {
        vehicleType: "Car",
        startDate: "2023-01-01",
        endDate: "2023-12-31",
      },
      name: "Event Name",
      periscopeDetails: [{ periscopeId: "123" }],
      storeGroups: ["Store1"],
      eventType: "DP", // or some other value based on your scenario
    };
  });

  it("should call setFormFields when values differ", () => {
    // Mocking getValues to simulate current form field values
    getValuesMock.mockImplementation(key => {
      switch (key) {
        case "divisionIds":
          return [1, 2];
        case "planProductGroups":
          return ["PPG1", "PPG2"];
        case "planStoreGroupType":
          return "GroupType";
        case "eventCreationVehicle.vehicleType":
          return "Car";
        case "eventCreationVehicle.startDate":
          return "2023-01-01";
        case "eventCreationVehicle.endDate":
          return "2023-12-31";
        case "name":
          return "Event Name";
        case "periscopeFormField":
          return "123"; // Match the periscopeId
        default:
          return undefined;
      }
    });

    // Call the function with the mock data
    compareFieldValuesAndSetValue(
      eventDetailsDataMock,
      getValuesMock,
      setFormFieldsMock
    );

    // Assert that setFormFields was called with the expected values
    expect(setFormFieldsMock).toHaveBeenCalledWith(
      expect.any(Function) // Ensure it's called with a function to update state
    );

    // Retrieve the function passed to setFormFields
    const setStateFunction = setFormFieldsMock.mock.calls[0][0];

    // Call the state function with a mock state
    const newState = setStateFunction({ isUpdateBtnDisabled: true });

    // Check if the expected state was returned
    expect(newState).toEqual({ isUpdateBtnDisabled: false });
  });

  it("should call setFormFields when storeGroups differ for DP event type", () => {
    // Simulate a case where storeGroups change
    getValuesMock.mockImplementation(key => {
      switch (key) {
        case "divisionIds":
          return [1, 2];
        case "planProductGroups":
          return ["PPG1", "PPG2"];
        case "planStoreGroupType":
          return "GroupType";
        case "eventCreationVehicle.vehicleType":
          return "Car";
        case "eventCreationVehicle.startDate":
          return "2023-01-01";
        case "eventCreationVehicle.endDate":
          return "2023-12-31";
        case "name":
          return "Event Name";
        case "periscopeFormField":
          return "123"; // Match the periscopeId
        case "storeGroups":
          return ["Store1", "Store2"]; // Simulating the change in storeGroups
        default:
          return undefined;
      }
    });

    // Modify eventDetailsData to have the storeGroups field for DP event type
    eventDetailsDataMock.eventType = "DP"; // This should trigger storeGroups comparison

    // Call the function with the mock data
    compareFieldValuesAndSetValue(
      eventDetailsDataMock,
      getValuesMock,
      setFormFieldsMock
    );

    // Assert that setFormFields was called
    expect(setFormFieldsMock).toHaveBeenCalledWith(
      expect.any(Function) // Ensure it's called with a function to update state
    );
  });
});

describe("checkIfAnyNotValidFields", () => {
  it("should return true if any field is invalid", () => {
    const watchAll = [
      "", // watchAll[0] should be invalid (empty string)
      undefined, // watchAll[1] should be invalid (undefined)
      [], // watchAll[2] should be invalid (empty array)
      undefined, // watchAll[3] should be invalid (undefined)
      undefined, // watchAll[4] should be invalid (undefined)
      "", // watchAll[5] should be invalid (empty string)
      "", // watchAll[6] should be invalid (empty string)
    ];

    const result = checkIfAnyNotValidFields(watchAll);

    expect(result).toBe(false); // should return false
  });

  it("should return false if all fields are valid", () => {
    const watchAll = [
      "valid", // watchAll[0] is valid (non-empty string)
      "valid", // watchAll[1] is valid (defined)
      ["item"], // watchAll[2] is valid (non-empty array)
      "valid", // watchAll[3] is valid (defined)
      "valid", // watchAll[4] is valid (defined)
      "valid", // watchAll[5] is valid (non-empty string)
      "valid", // watchAll[6] is valid (non-empty string)
    ];

    const result = checkIfAnyNotValidFields(watchAll);

    expect(result).toBe(true); // should return false because all values are valid
  });

  it("should return true if watchAll[0] is empty string", () => {
    const watchAll = [
      "", // watchAll[0] is invalid (empty string)
      "valid", // other fields are valid
      ["valid"],
      "valid",
      "valid",
      "valid",
      "valid",
    ];

    const result = checkIfAnyNotValidFields(watchAll);

    expect(result).toBe(true); // should return true because watchAll[0] is invalid
  });

  it("should return true if watchAll[2] is an empty array", () => {
    const watchAll = [
      "valid", // other fields are valid
      "valid",
      [], // watchAll[2] is invalid (empty array)
      "valid",
      "valid",
      "valid",
      "valid",
    ];

    const result = checkIfAnyNotValidFields(watchAll);

    expect(result).toBe(true); // should return true because watchAll[2] is an empty array
  });

  it("should return true if watchAll[5] is an empty string", () => {
    const watchAll = [
      "valid", // other fields are valid
      "valid",
      ["valid"],
      "valid",
      "valid",
      "", // watchAll[5] is invalid (empty string)
      "valid",
    ];

    const result = checkIfAnyNotValidFields(watchAll);

    expect(result).toBe(true); // should return true because watchAll[5] is an empty string
  });

  it("should return true if watchAll[6] is an empty string", () => {
    const watchAll = [
      "valid", // other fields are valid
      "valid",
      ["valid"],
      "valid",
      "valid",
      "valid",
      "", // watchAll[6] is invalid (empty string)
    ];

    const result = checkIfAnyNotValidFields(watchAll);

    expect(result).toBe(true); // should return true because watchAll[6] is an empty string
  });

  it("should return false if no fields are invalid", () => {
    const watchAll = [
      "valid", // valid
      "valid", // valid
      ["valid"], // valid
      "valid", // valid
      "valid", // valid
      "valid", // valid
      "valid", // valid
    ];

    const result = checkIfAnyNotValidFields(watchAll);

    expect(result).toBe(true); // should return true because no invalid values
  });
});

describe("resetRegisterFields", () => {
  it("should call setValue for each field with resetValue", () => {
    // Mock the setValue function
    const setValue = jest.fn();

    // Define a mock targetFields object
    const targetFields = {
      field1: {
        registerField: "field1",
        resetValue: "reset1",
      },
      field2: {
        registerField: "field2",
        resetValue: "reset2",
      },
    };

    // Call the function
    resetRegisterFields(targetFields, setValue);

    // Assertions to verify that setValue is called with the correct arguments
    expect(setValue).toHaveBeenCalledWith("field1", "reset1");
    expect(setValue).toHaveBeenCalledWith("field2", "reset2");

    // Verify that setValue was called exactly twice
    expect(setValue).toHaveBeenCalledTimes(2);
  });

  it("should not call setValue if targetFields is empty", () => {
    // Mock the setValue function
    const setValue = jest.fn();

    // Define an empty targetFields object
    const targetFields = {};

    // Call the function
    resetRegisterFields(targetFields, setValue);

    // Assert that setValue was not called
    expect(setValue).not.toHaveBeenCalled();
  });

  it("should not call setValue for fields without resetValue", () => {
    // Mock the setValue function
    const setValue = jest.fn();

    // Define a mock targetFields object with a field without resetValue
    const targetFields = {
      field1: {
        registerField: "field1",
        resetValue: "reset1",
      },
      field2: {
        registerField: "field2", // No resetValue here
      },
    };

    // Call the function
    resetRegisterFields(targetFields, setValue);

    // Assert that setValue was called only for field1
    expect(setValue).toHaveBeenCalledWith("field1", "reset1");
    expect(setValue).not.toHaveBeenCalledWith("field2");
  });

  it("should not call setValue if targetFields is undefined", () => {
    // Mock the setValue function
    const setValue = jest.fn();

    // Call the function with undefined targetFields
    resetRegisterFields(undefined, setValue);

    // Assert that setValue was not called
    expect(setValue).not.toHaveBeenCalled();
  });
});

describe("batchSetValueFields", () => {
  it("should call setValue for each field in valueFields with the targetField value", () => {
    // Mock the setValue function
    const setValue = jest.fn();

    // Define valueFields and targetField
    const valueFields = ["field1", "field2", "field3"];
    const targetField = "targetValue";

    // Call the function
    batchSetValueFields(valueFields, targetField, setValue);

    // Assertions to verify that setValue is called with the correct arguments
    expect(setValue).toHaveBeenCalledWith("field1", "targetValue");
    expect(setValue).toHaveBeenCalledWith("field2", "targetValue");
    expect(setValue).toHaveBeenCalledWith("field3", "targetValue");

    // Verify that setValue was called exactly 3 times (once for each field)
    expect(setValue).toHaveBeenCalledTimes(3);
  });

  it("should not call setValue if valueFields is empty", () => {
    // Mock the setValue function
    const setValue = jest.fn();

    // Define an empty valueFields array
    const valueFields = [];
    const targetField = "targetValue";

    // Call the function
    batchSetValueFields(valueFields, targetField, setValue);

    // Assert that setValue was not called
    expect(setValue).not.toHaveBeenCalled();
  });

  it("should not call setValue if valueFields is undefined", () => {
    // Mock the setValue function
    const setValue = jest.fn();

    // Define undefined valueFields
    const valueFields: any = undefined;
    const targetField = "targetValue";

    // Call the function
    batchSetValueFields(valueFields, targetField, setValue);

    // Assert that setValue was not called
    expect(setValue).not.toHaveBeenCalled();
  });

  it("should not call setValue if valueFields is null", () => {
    // Mock the setValue function
    const setValue = jest.fn();

    // Define null valueFields
    const valueFields: any = null;
    const targetField = "targetValue";

    // Call the function
    batchSetValueFields(valueFields, targetField, setValue);

    // Assert that setValue was not called
    expect(setValue).not.toHaveBeenCalled();
  });
});

describe("analyticsLinkClickEvent", () => {
  let mockGetValues;
  let mockSetLinkClickEvents;

  beforeEach(() => {
    // Clear mocks before each test
    mockGetValues = jest.fn();
    mockSetLinkClickEvents = jest.fn();

    // Mock the window object and its properties
    global.window["AB"] = {
      DATALAYER: {
        setLinkClickEvents: mockSetLinkClickEvents,
      },
    };
  });

  it("should call setLinkClickEvents when all conditions are met", () => {
    // Mock the eventType and getValues
    const eventType = "DP"; // eventType that should match the DP value
    mockGetValues.mockReturnValueOnce(undefined); // getValues("id") should return falsy

    // Define a mock eventTypes array with the first element matching the eventType
    global.eventTypes = [{ eventType: "DP" }];

    // Call the function
    analyticsLinkClickEvent(eventType, mockGetValues);

    // Assertions
    expect(mockSetLinkClickEvents).toHaveBeenCalledWith(
      "promotions:create-event"
    );
  });

  it("should not call setLinkClickEvents if eventType does not match DP", () => {
    const eventType = "Non-DP"; // eventType that does not match "DP"
    mockGetValues.mockReturnValueOnce(undefined); // getValues("id") should return falsy

    // Define a mock eventTypes array with the first element not matching the eventType
    global.eventTypes = [{ eventType: "OtherEvent" }];

    // Call the function
    analyticsLinkClickEvent(eventType, mockGetValues);

    // Assertions
    expect(mockSetLinkClickEvents).not.toHaveBeenCalled();
  });

  it('should not call setLinkClickEvents if getValues("id") is truthy', () => {
    const eventType = "DP"; // eventType that should match the DP value
    mockGetValues.mockReturnValueOnce("123"); // getValues("id") should return a truthy value

    // Define a mock eventTypes array with the first element matching the eventType
    global.eventTypes = [{ eventType: "DP" }];

    // Call the function
    analyticsLinkClickEvent(eventType, mockGetValues);

    // Assertions
    expect(mockSetLinkClickEvents).not.toHaveBeenCalled();
  });

  it("should not call setLinkClickEvents if setLinkClickEvents is not a function", () => {
    const eventType = "DP"; // eventType that should match the DP value
    mockGetValues.mockReturnValueOnce(undefined); // getValues("id") should return falsy

    // Mock window object where setLinkClickEvents is not a function
    global.window["AB"]["DATALAYER"].setLinkClickEvents = undefined;

    // Call the function
    analyticsLinkClickEvent(eventType, mockGetValues);

    // Assertions
    expect(mockSetLinkClickEvents).not.toHaveBeenCalled();
  });
  describe("PID Utility Functions test suite", () => {
    efConstants.PERISCOPE_ERRORS = {
      INVALID_PID: "INVALID_PID",
      INVALID_PID_KEY: "INVALID_PID_KEY",
      PID_NOT_VALIDATED: "PID_NOT_VALIDATED",
      EXTRA_FIELD: "EXTRA_FIELD",
      ITEM_DATE_INVALID_MESSAGE: "ITEM_DATE_INVALID_MESSAGE",
      PPG_NOT_FOUND:
        "Promo Product Group match cannot be found for entered PID",
      PPG_NOT_FOUND_KEY: "PPG_NOT_FOUND_KEY",
      ITEMS: "Items",
    };

    describe("getPPGItemIds", () => {
      it("should return a comma-separated string of item IDs", () => {
        const items = [{ itemId: "1" }, { itemId: "2" }, { itemId: "3" }];
        const result = getPPGItemIds(items);
        expect(result).toBe("1, 2, 3");
      });

      it("should return an empty string if no items are provided", () => {
        const items = [];
        const result = getPPGItemIds(items);
        expect(result).toBe("");
      });
    });

    describe("gerneratePeriscopePPGError", () => {
      it("should generate a periscope PPG error message", () => {
        const periscopeData = [
          {
            periscopePlanProductGroup: {
              planProductGroupName: "",
              sourceProductGroupId: "123456",
              periscopeItems: [{ itemId: "1" }, { itemId: "2" }],
            },
          },
        ];
        const { ppgError } = gerneratePeriscopePPGError(periscopeData);
        const value =
          "Promo Product Group match cannot be found for entered PID";
        expect(ppgError).toBe(value);
      });
    });

    describe("isPeriscopeValidFromApi", () => {
      it("should return true if periscope data does not contain INVALID_PID_KEY", () => {
        const periscopeData = [
          {
            periscopeMessages: ["VALID_MESSAGE"],
          },
        ];

        const result = isPeriscopeValidFromApi(periscopeData);
        expect(result).toBe(true);
      });

      it("should return false if periscope data contains INVALID_PID_KEY", () => {
        const periscopeData = [
          {
            periscopeMessages: ["INVALID_PID_KEY"],
          },
        ];
        const result = isPeriscopeValidFromApi(periscopeData);
        expect(result).toBe(false);
      });
    });

    describe("periscopeFieldEnableStatus", () => {
      it("should return piDButtonEnable as true if event status is DRAFT and no allowances or promotions", () => {
        const getValues = () => ({
          eventStatus: EEVENT_STATUS.DRAFT,
          offerAllowances: [],
          promotionsList: [],
        });
        const result = periscopeFieldEnableStatus(getValues);
        expect(result.piDButtonEnable).toBe(true);
      });

      it("should return piDButtonEnable as false if event status is not DRAFT", () => {
        const getValues = () => ({
          eventStatus: EEVENT_STATUS.EXECUTED,
          offerAllowances: [],
          promotionsList: [],
        });
        const result = periscopeFieldEnableStatus(getValues);
        expect(result.piDButtonEnable).toBe(false);
      });

      it("should return piDButtonEnable as false if event status is DRAFT but there are allowances or promotions", () => {
        const getValues = () => ({
          eventStatus: EEVENT_STATUS.DRAFT,
          offerAllowances: [{ id: 1 }],
          promotionsList: [{ id: 1 }],
        });
        const result = periscopeFieldEnableStatus(getValues);
        expect(result.piDButtonEnable).toBe(false);
      });
    });
  });
});
