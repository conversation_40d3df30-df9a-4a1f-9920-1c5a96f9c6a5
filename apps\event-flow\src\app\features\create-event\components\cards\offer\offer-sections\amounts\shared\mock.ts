export const allowResp = {
    "eventId": "67a3296ebe79106712285ac6",
    "offerAllowancesGroup": "DSD_WHSE_RETAIL_DIVISION",
    "summary": {
        "itemAmountsCouldBeSummarized": true,
        "allowanceType": "SCAN",
        "vendorPackConversionFactor": 1,
        "packRetail": 6,
        "allowanceAmount": 0,
        "allowUomType": "EA",
        "allowUomTypes": [
            "EA"
        ],
        "unitNetCosts": {
            "netCostType": "UNIT",
            "cost": 4.8200,
            "costAllow": 4.8200,
            "initialAllowAmt": 0,
            "newCostAllow": 4.8200
        },
        "masterCaseNetCosts": {
            "netCostType": "MASTER_CASE",
            "cost": 28.92,
            "costAllow": 28.92,
            "initialAllowAmt": 0,
            "newCostAllow": 28.92
        },
        "shipCaseNetCosts": {
            "netCostType": "SHIP_CASE",
            "cost": 28.92,
            "costAllow": 28.92,
            "initialAllowAmt": 0,
            "newCostAllow": 28.92
        },
        "modCommand": "NONE"
    },
    "allowances": [
        {
            "allowanceIdNbr": 1,
            "vendorNbr": "",
            "costAreaDesc": "",
            "defaultAllowanceDates": {
                "allowanceStartDate": "2025-04-30",
                "allowanceEndDate": "2025-05-06",
                "performStartDate": "2025-04-30",
                "performEndDate": "2025-05-06",
                "orderStartDate": "0001-01-01",
                "orderEndDate": "0001-01-01",
                "shipStartDate": "0001-01-01",
                "shipEndDate": "0001-01-01",
                "arrivalStartDate": "0001-01-01",
                "arrivalEndDate": "0001-01-01",
                "overrideInd": false,
                "notPreSaved": false
            },
            "allowanceStartDate": "2025-04-30",
            "allowanceEndDate": "2025-05-06",
            "performStartDate": "2025-04-30",
            "performEndDate": "2025-05-06",
            "orderStartDate": "2025-04-30",
            "orderEndDate": "2025-05-06",
            "shipStartDate": "2025-04-30",
            "shipEndDate": "2025-05-06",
            "arrivalStartDate": "2025-04-30",
            "arrivalEndDate": "2025-05-06",
            "vehicleId": "66f1eded1d792055ff84c77a",
            "createInd": "TC",
            "locationId": "639033d538196056762e6e28",
            "locationName": "27 - Seattle",
            "distCenter": "DDSE",
            "locationTypeCd": "D",
            "allowanceBillingInfo": {
                "vendorIds": [
                    {
                        "vendorNbr": "022953",
                        "vendorSubAccount": "001",
                        "distCenter": "WAUB",
                        "fullVendorNbr": "022953-001"
                    }
                ],
                "absMerchVendor": "022953-001",
                "absVendorName": "CHOBANI INC",
                "absVendorPaymentType": "D",
                "acPayableVendorNbr": "501080",
                "acReceivableVendorNbr": "150938",
                "billingContactName": "MARY INNES               ",
                "billingContactEmail": "MARY.INNES@CHOBANI.COM_2/26/18",
                "vendorComment": "",
                "vendorOfferTrackingNbr": "",
                "vendorBillingList": [
                    {
                        "billingContactName": "MARY INNES               ",
                        "billingContactEmail": "MARY.INNES@CHOBANI.COM_2/26/18"
                    },
                    {
                        "billingContactName": "MISY ADOFF               ",
                        "billingContactEmail": "<EMAIL>"
                    },
                    {
                        "billingContactName": "MARY INNES_- ****CSR     ",
                        "billingContactEmail": "<EMAIL>"
                    },
                    {
                        "billingContactName": "MARY INNES               ",
                        "billingContactEmail": "MARY.INNES@CHOBANI.COM_2/26/18"
                    },
                    {
                        "billingContactName": "MISY ADOFF               ",
                        "billingContactEmail": "<EMAIL>"
                    },
                    {
                        "billingContactName": "MARY INNES_- ****CSR     ",
                        "billingContactEmail": "<EMAIL>"
                    }
                ],
                "vendorItemCount": 5,
                "vendorItemCountsSet": [
                    {
                        "vendorDsdWhseId": {
                            "vendorNbr": "022953",
                            "vendorSubAccount": "001",
                            "distCenter": "WAUB",
                            "vendorRank": "A.WHSE",
                            "wimsSubVend": "2701",
                            "fullVendorNbr": "022953-001-WAUB|2701",
                            "valid": true
                        },
                        "itemIdSet": [
                            "********",
                            "********",
                            "********",
                            "********",
                            "********"
                        ],
                        "vendorDsdWhseItemCount": 5
                    },
                    {
                        "vendorDsdWhseId": {
                            "vendorNbr": "022953",
                            "vendorSubAccount": "001",
                            "distCenter": "WSPK",
                            "vendorRank": "A.WHSE",
                            "wimsSubVend": "2704",
                            "fullVendorNbr": "022953-001-WSPK|2704",
                            "valid": true
                        },
                        "itemIdSet": [
                            "********",
                            "********",
                            "********",
                            "********",
                            "********"
                        ],
                        "vendorDsdWhseItemCount": 5
                    },
                    {
                        "vendorDsdWhseId": {
                            "vendorNbr": "022953",
                            "vendorSubAccount": "001",
                            "distCenter": "WANC",
                            "vendorRank": "A.WHSE",
                            "wimsSubVend": "2703",
                            "fullVendorNbr": "022953-001-WANC|2703",
                            "valid": true
                        },
                        "itemIdSet": [
                            "********",
                            "********"
                        ],
                        "vendorDsdWhseItemCount": 2
                    }
                ],
                "source": "SIMS_VENDOR",
                "matched": "SIMS_ITEM_VENDOR",
                "orgAbsVendorPaymentType": "D",
                "orgAcPayableVendorNbr": "501080",
                "orgAcReceivableVendorNbr": "150938"
            },
            "allowanceBillingInfos": [
                {
                    "vendorIds": [
                        {
                            "vendorNbr": "022953",
                            "vendorSubAccount": "001",
                            "distCenter": "WAUB",
                            "fullVendorNbr": "022953-001"
                        }
                    ],
                    "absMerchVendor": "022953-001",
                    "absVendorName": "CHOBANI INC",
                    "absVendorPaymentType": "D",
                    "acPayableVendorNbr": "501080",
                    "acReceivableVendorNbr": "150938",
                    "billingContactName": "MARY INNES               ",
                    "billingContactEmail": "MARY.INNES@CHOBANI.COM_2/26/18",
                    "vendorComment": "",
                    "vendorOfferTrackingNbr": "",
                    "vendorBillingList": [
                        {
                            "billingContactName": "MARY INNES               ",
                            "billingContactEmail": "MARY.INNES@CHOBANI.COM_2/26/18"
                        },
                        {
                            "billingContactName": "MISY ADOFF               ",
                            "billingContactEmail": "<EMAIL>"
                        },
                        {
                            "billingContactName": "MARY INNES_- ****CSR     ",
                            "billingContactEmail": "<EMAIL>"
                        },
                        {
                            "billingContactName": "MARY INNES               ",
                            "billingContactEmail": "MARY.INNES@CHOBANI.COM_2/26/18"
                        },
                        {
                            "billingContactName": "MISY ADOFF               ",
                            "billingContactEmail": "<EMAIL>"
                        },
                        {
                            "billingContactName": "MARY INNES_- ****CSR     ",
                            "billingContactEmail": "<EMAIL>"
                        }
                    ],
                    "vendorItemCount": 5,
                    "vendorItemCountsSet": [
                        {
                            "vendorDsdWhseId": {
                                "vendorNbr": "022953",
                                "vendorSubAccount": "001",
                                "distCenter": "WAUB",
                                "vendorRank": "A.WHSE",
                                "wimsSubVend": "2701",
                                "fullVendorNbr": "022953-001-WAUB|2701",
                                "valid": true
                            },
                            "itemIdSet": [
                                "********",
                                "********",
                                "********",
                                "********",
                                "********"
                            ],
                            "vendorDsdWhseItemCount": 5
                        },
                        {
                            "vendorDsdWhseId": {
                                "vendorNbr": "022953",
                                "vendorSubAccount": "001",
                                "distCenter": "WSPK",
                                "vendorRank": "A.WHSE",
                                "wimsSubVend": "2704",
                                "fullVendorNbr": "022953-001-WSPK|2704",
                                "valid": true
                            },
                            "itemIdSet": [
                                "********",
                                "********",
                                "********",
                                "********",
                                "********"
                            ],
                            "vendorDsdWhseItemCount": 5
                        },
                        {
                            "vendorDsdWhseId": {
                                "vendorNbr": "022953",
                                "vendorSubAccount": "001",
                                "distCenter": "WANC",
                                "vendorRank": "A.WHSE",
                                "wimsSubVend": "2703",
                                "fullVendorNbr": "022953-001-WANC|2703",
                                "valid": true
                            },
                            "itemIdSet": [
                                "********",
                                "********"
                            ],
                            "vendorDsdWhseItemCount": 2
                        }
                    ],
                    "source": "SIMS_VENDOR",
                    "matched": "SIMS_ITEM_VENDOR",
                    "orgAbsVendorPaymentType": "D",
                    "orgAcPayableVendorNbr": "501080",
                    "orgAcReceivableVendorNbr": "150938"
                }
            ],
            "allowanceDateOffsets": {
                "allowanceTypes": [
                    "SCAN",
                    "HEADER_FLAT",
                    "ITEM_FLAT"
                ],
                "startDateOffset": 0,
                "endDateOffset": 0,
                "defaultOrderLeadTimeDays": 0,
                "defaultShipTransitDays": 0,
                "resolvedLeadTimeDays": 0,
                "resolvedShipTransitDays": 0,
                "vendorIdWimsSubVend": {
                    "vendorId": {
                        "vendorNbr": "022953",
                        "vendorSubAccount": "001",
                        "fullVendorNbr": "022953-001"
                    },
                    "wimsSubVendNumber": "2703",
                    "eventNegotiationSource": "SIMS_VENDOR",
                    "wimsSubVend": {
                        "wimSubVendor": "2703",
                        "leadTime": 2.0,
                        "leadTimedays": 14,
                        "transitTimedays": 2
                    }
                }
            },
            "leadDistributorInfos": [],
            "createAllowInd": true,
            "allowanceItems": [
                {
                    "itemId": "********",
                    "itemDescription": "CHOBANI GRK YGRT VAN NONFAT ALL NATURAL",
                    "primaryUpc": "************",
                    "consumerUpc": "************",
                    "caseUpc": "*************",
                    "itemUpcs": [
                        "************",
                        "*************"
                    ],
                    "consumerUpcs": [
                        {
                            "upc": "************",
                            "rog": "SACG",
                            "primaryInd": true,
                            "labelSize": "M",
                            "packDesc": "006",
                            "sizeDesc": "32 OZ  "
                        },
                        {
                            "upc": "************",
                            "rog": "SSPK",
                            "primaryInd": true,
                            "labelSize": "M",
                            "packDesc": "006",
                            "sizeDesc": "32 OZ  "
                        },
                        {
                            "upc": "************",
                            "rog": "SSEA",
                            "primaryInd": true,
                            "labelSize": "M",
                            "packDesc": "006",
                            "sizeDesc": "32 OZ  "
                        }
                    ],
                    "effectiveStartDate": "0001-01-01",
                    "effectiveEndDate": "9999-01-01",
                    "allowanceType": "SCAN",
                    "packWhse": 6,
                    "ringType": 0,
                    "size": "32.0 OZ",
                    "vendorPackConversionFactor": 1,
                    "unitNetCosts": {
                        "netCostType": "UNIT",
                        "cost": 4.8200,
                        "costAllow": 4.8200,
                        "initialAllowAmt": 0,
                        "newCostAllow": 4.8200
                    },
                    "masterCaseNetCosts": {
                        "netCostType": "MASTER_CASE",
                        "cost": 28.92,
                        "costAllow": 28.92,
                        "initialAllowAmt": 0,
                        "newCostAllow": 28.92
                    },
                    "shipCaseNetCosts": {
                        "netCostType": "SHIP_CASE",
                        "cost": 28.92,
                        "costAllow": 28.92,
                        "initialAllowAmt": 0,
                        "newCostAllow": 28.92
                    },
                    "allowanceAmount": 0,
                    "allowUomType": "EA",
                    "allowUomTypes": [
                        "EA"
                    ],
                    "overlaps": {
                        "offerAllowAmounts": [],
                        "unitizedOverlaps": {
                            "netCostType": "UNIT",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        },
                        "shipCaseOverlaps": {
                            "netCostType": "SHIP_CASE",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        },
                        "masterCaseOverlaps": {
                            "netCostType": "MASTER_CASE",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        }
                    },
                    "bindMetrics": {
                        "group": "AMOUNTS",
                        "mode": "AMOUNTS_DEFAULT_BIND",
                        "bind": true
                    },
                    "modCommand": "NONE"
                },
                {
                    "itemId": "********",
                    "itemDescription": "CHOBANI GRK YGRT STRAWBERRY NON FAT",
                    "primaryUpc": "089470001026",
                    "consumerUpc": "089470001026",
                    "caseUpc": "1089470001026",
                    "itemUpcs": [
                        "089470001026",
                        "1089470001026"
                    ],
                    "consumerUpcs": [
                        {
                            "upc": "089470001026",
                            "rog": "SSEA",
                            "primaryInd": true,
                            "labelSize": "M",
                            "packDesc": "006",
                            "sizeDesc": "32 OZ  "
                        },
                        {
                            "upc": "089470001026",
                            "rog": "SSPK",
                            "primaryInd": true,
                            "labelSize": "M",
                            "packDesc": "006",
                            "sizeDesc": "32 OZ  "
                        },
                        {
                            "upc": "089470001026",
                            "rog": "SACG",
                            "primaryInd": true,
                            "labelSize": "M",
                            "packDesc": "006",
                            "sizeDesc": "32 OZ  "
                        }
                    ],
                    "effectiveStartDate": "0001-01-01",
                    "effectiveEndDate": "9999-01-01",
                    "allowanceType": "SCAN",
                    "packWhse": 6,
                    "ringType": 0,
                    "size": "32.0 OZ",
                    "vendorPackConversionFactor": 1,
                    "unitNetCosts": {
                        "netCostType": "UNIT",
                        "cost": 4.8200,
                        "costAllow": 4.8200,
                        "initialAllowAmt": 0,
                        "newCostAllow": 4.8200
                    },
                    "masterCaseNetCosts": {
                        "netCostType": "MASTER_CASE",
                        "cost": 28.92,
                        "costAllow": 28.92,
                        "initialAllowAmt": 0,
                        "newCostAllow": 28.92
                    },
                    "shipCaseNetCosts": {
                        "netCostType": "SHIP_CASE",
                        "cost": 28.92,
                        "costAllow": 28.92,
                        "initialAllowAmt": 0,
                        "newCostAllow": 28.92
                    },
                    "allowanceAmount": 0,
                    "allowUomType": "EA",
                    "allowUomTypes": [
                        "EA"
                    ],
                    "overlaps": {
                        "offerAllowAmounts": [],
                        "unitizedOverlaps": {
                            "netCostType": "UNIT",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        },
                        "shipCaseOverlaps": {
                            "netCostType": "SHIP_CASE",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        },
                        "masterCaseOverlaps": {
                            "netCostType": "MASTER_CASE",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        }
                    },
                    "bindMetrics": {
                        "group": "AMOUNTS",
                        "mode": "AMOUNTS_DEFAULT_BIND",
                        "bind": true
                    },
                    "modCommand": "NONE"
                },
                {
                    "itemId": "********",
                    "itemDescription": "CHOBANI GRK YGRT PLAIN 5% FAT WHOLE MILK",
                    "primaryUpc": "089470001043",
                    "consumerUpc": "089470001043",
                    "caseUpc": "1089470001043",
                    "itemUpcs": [
                        "089470001043",
                        "1089470001043"
                    ],
                    "consumerUpcs": [
                        {
                            "upc": "089470001043",
                            "rog": "SSEA",
                            "primaryInd": true,
                            "labelSize": "M",
                            "packDesc": "006",
                            "sizeDesc": "32 OZ  "
                        },
                        {
                            "upc": "089470001043",
                            "rog": "SACG",
                            "primaryInd": true,
                            "labelSize": "M",
                            "packDesc": "006",
                            "sizeDesc": "32 OZ  "
                        },
                        {
                            "upc": "089470001043",
                            "rog": "SSPK",
                            "primaryInd": true,
                            "labelSize": "M",
                            "packDesc": "006",
                            "sizeDesc": "32 OZ  "
                        }
                    ],
                    "effectiveStartDate": "0001-01-01",
                    "effectiveEndDate": "9999-01-01",
                    "allowanceType": "SCAN",
                    "packWhse": 6,
                    "ringType": 0,
                    "size": "32.0 OZ",
                    "vendorPackConversionFactor": 1,
                    "unitNetCosts": {
                        "netCostType": "UNIT",
                        "cost": 4.8200,
                        "costAllow": 4.8200,
                        "initialAllowAmt": 0,
                        "newCostAllow": 4.8200
                    },
                    "masterCaseNetCosts": {
                        "netCostType": "MASTER_CASE",
                        "cost": 28.92,
                        "costAllow": 28.92,
                        "initialAllowAmt": 0,
                        "newCostAllow": 28.92
                    },
                    "shipCaseNetCosts": {
                        "netCostType": "SHIP_CASE",
                        "cost": 28.92,
                        "costAllow": 28.92,
                        "initialAllowAmt": 0,
                        "newCostAllow": 28.92
                    },
                    "allowanceAmount": 0,
                    "allowUomType": "EA",
                    "allowUomTypes": [
                        "EA"
                    ],
                    "overlaps": {
                        "offerAllowAmounts": [],
                        "unitizedOverlaps": {
                            "netCostType": "UNIT",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        },
                        "shipCaseOverlaps": {
                            "netCostType": "SHIP_CASE",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        },
                        "masterCaseOverlaps": {
                            "netCostType": "MASTER_CASE",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        }
                    },
                    "bindMetrics": {
                        "group": "AMOUNTS",
                        "mode": "AMOUNTS_DEFAULT_BIND",
                        "bind": true
                    },
                    "modCommand": "NONE"
                },
                {
                    "itemId": "********",
                    "itemDescription": "CHOBANI GRK YGRT NONFAT PLAIN",
                    "primaryUpc": "089470001013",
                    "consumerUpc": "089470001013",
                    "caseUpc": "0089470001013",
                    "itemUpcs": [
                        "0089470001013",
                        "089470001013"
                    ],
                    "consumerUpcs": [
                        {
                            "upc": "089470001013",
                            "rog": "SACG",
                            "primaryInd": true,
                            "labelSize": "M",
                            "packDesc": "006",
                            "sizeDesc": "32 OZ  "
                        },
                        {
                            "upc": "089470001013",
                            "rog": "SSEA",
                            "primaryInd": true,
                            "labelSize": "M",
                            "packDesc": "006",
                            "sizeDesc": "32 OZ  "
                        },
                        {
                            "upc": "089470001013",
                            "rog": "SSPK",
                            "primaryInd": true,
                            "labelSize": "M",
                            "packDesc": "006",
                            "sizeDesc": "32 OZ  "
                        }
                    ],
                    "effectiveStartDate": "0001-01-01",
                    "effectiveEndDate": "9999-01-01",
                    "allowanceType": "SCAN",
                    "packWhse": 6,
                    "ringType": 0,
                    "size": "32.0 OZ",
                    "vendorPackConversionFactor": 1,
                    "unitNetCosts": {
                        "netCostType": "UNIT",
                        "cost": 4.8200,
                        "costAllow": 4.8200,
                        "initialAllowAmt": 0,
                        "newCostAllow": 4.8200
                    },
                    "masterCaseNetCosts": {
                        "netCostType": "MASTER_CASE",
                        "cost": 28.92,
                        "costAllow": 28.92,
                        "initialAllowAmt": 0,
                        "newCostAllow": 28.92
                    },
                    "shipCaseNetCosts": {
                        "netCostType": "SHIP_CASE",
                        "cost": 28.92,
                        "costAllow": 28.92,
                        "initialAllowAmt": 0,
                        "newCostAllow": 28.92
                    },
                    "allowanceAmount": 0,
                    "allowUomType": "EA",
                    "allowUomTypes": [
                        "EA"
                    ],
                    "overlaps": {
                        "offerAllowAmounts": [],
                        "unitizedOverlaps": {
                            "netCostType": "UNIT",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        },
                        "shipCaseOverlaps": {
                            "netCostType": "SHIP_CASE",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        },
                        "masterCaseOverlaps": {
                            "netCostType": "MASTER_CASE",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        }
                    },
                    "bindMetrics": {
                        "group": "AMOUNTS",
                        "mode": "AMOUNTS_DEFAULT_BIND",
                        "bind": true
                    },
                    "modCommand": "NONE"
                },
                {
                    "itemId": "********",
                    "itemDescription": "CHOBANI NF ZR SUG VANILLA YGRT",
                    "primaryUpc": "081829001857",
                    "consumerUpc": "081829001857",
                    "caseUpc": "1081829001857",
                    "itemUpcs": [
                        "081829001857",
                        "1081829001857"
                    ],
                    "consumerUpcs": [
                        {
                            "upc": "081829001857",
                            "rog": "SSEA",
                            "primaryInd": true,
                            "labelSize": "M",
                            "packDesc": "006",
                            "sizeDesc": "32 OZ  "
                        },
                        {
                            "upc": "081829001857",
                            "rog": "SSPK",
                            "primaryInd": true,
                            "labelSize": "M",
                            "packDesc": "006",
                            "sizeDesc": "32 OZ  "
                        },
                        {
                            "upc": "081829001857",
                            "rog": "SACG",
                            "primaryInd": true,
                            "labelSize": "M",
                            "packDesc": "006",
                            "sizeDesc": "32 OZ  "
                        }
                    ],
                    "effectiveStartDate": "0001-01-01",
                    "effectiveEndDate": "9999-01-01",
                    "allowanceType": "SCAN",
                    "packWhse": 6,
                    "ringType": 0,
                    "size": "32.0 OZ",
                    "vendorPackConversionFactor": 1,
                    "unitNetCosts": {
                        "netCostType": "UNIT",
                        "cost": 4.8200,
                        "costAllow": 4.8200,
                        "initialAllowAmt": 0,
                        "newCostAllow": 4.8200
                    },
                    "masterCaseNetCosts": {
                        "netCostType": "MASTER_CASE",
                        "cost": 28.92,
                        "costAllow": 28.92,
                        "initialAllowAmt": 0,
                        "newCostAllow": 28.92
                    },
                    "shipCaseNetCosts": {
                        "netCostType": "SHIP_CASE",
                        "cost": 28.92,
                        "costAllow": 28.92,
                        "initialAllowAmt": 0,
                        "newCostAllow": 28.92
                    },
                    "allowanceAmount": 0,
                    "allowUomType": "EA",
                    "allowUomTypes": [
                        "EA"
                    ],
                    "overlaps": {
                        "offerAllowAmounts": [],
                        "unitizedOverlaps": {
                            "netCostType": "UNIT",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        },
                        "shipCaseOverlaps": {
                            "netCostType": "SHIP_CASE",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        },
                        "masterCaseOverlaps": {
                            "netCostType": "MASTER_CASE",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        }
                    },
                    "bindMetrics": {
                        "group": "AMOUNTS",
                        "mode": "AMOUNTS_DEFAULT_BIND",
                        "bind": true
                    },
                    "modCommand": "NONE"
                }
            ],
            "headerFlatAmt": 0,
            "allowanceStatus": "Draft",
            "storeGroups": [],
            "leadDistributorInd": false,
            "includeInd": true,
            "finalizedAmountsInd": false,
            "dateBindMetrics": {
                "group": "DATES",
                "mode": "DATES_DEFAULT_BIND",
                "bindValues": {
                    "srcDates": {
                        "allow": {
                            "startDate": "2025-04-30",
                            "endDate": "2025-05-06"
                        },
                        "order": {
                            "startDate": "2025-04-30",
                            "endDate": "2025-05-06"
                        },
                        "ship": {
                            "startDate": "2025-04-30",
                            "endDate": "2025-05-06"
                        },
                        "arrival": {
                            "startDate": "2025-04-30",
                            "endDate": "2025-05-06"
                        }
                    },
                    "dstDates": {
                        "allow": {
                            "startDate": "2025-04-30",
                            "endDate": "2025-05-06"
                        },
                        "order": {
                            "startDate": "0001-01-01",
                            "endDate": "0001-01-01"
                        },
                        "ship": {
                            "startDate": "0001-01-01",
                            "endDate": "0001-01-01"
                        },
                        "arrival": {
                            "startDate": "0001-01-01",
                            "endDate": "0001-01-01"
                        }
                    }
                },
                "bind": true
            },
            "excludedItems": [],
            "allowanceProcessStatus": "",
            "allowDownstreamStatus": {}
        }
    ],
    "offerAllowanceOverlapResults": {
        "offerAllowanceOverlaps": [],
        "nonDisplayableOffers": [],
        "cancelledOffers": []
    }
}
export const ifResp = {
    "eventId": "679c91fd84c85a257caf2afa",
    "offerAllowancesGroup": "DSD_WHSE_RETAIL_DIVISION",
    "summary": {
        "itemAmountsCouldBeSummarized": true,
        "allowanceType": "ITEM_FLAT",
        "vendorPackConversionFactor": 1,
        "packRetail": 6,
        "allowanceAmount": 0,
        "allowUomType": "CA",
        "allowUomTypes": [
            "CA"
        ],
        "unitNetCosts": {
            "netCostType": "UNIT",
            "cost": 1.1233,
            "costAllow": 1.1233,
            "initialAllowAmt": 0,
            "newCostAllow": 1.1233
        },
        "masterCaseNetCosts": {
            "netCostType": "MASTER_CASE",
            "cost": 6.7400,
            "costAllow": 6.7400,
            "initialAllowAmt": 0,
            "newCostAllow": 6.7400
        },
        "shipCaseNetCosts": {
            "netCostType": "SHIP_CASE",
            "cost": 6.7400,
            "costAllow": 6.7400,
            "initialAllowAmt": 0,
            "newCostAllow": 6.7400
        },
        "modCommand": "NONE"
    },
    "allowances": [
        {
            "allowanceIdNbr": 1,
            "vendorNbr": "",
            "costAreaDesc": "",
            "defaultAllowanceDates": {
                "allowanceStartDate": "2025-04-07",
                "allowanceEndDate": "2025-06-29",
                "performStartDate": "2025-04-07",
                "performEndDate": "2025-06-29",
                "orderStartDate": "0001-01-01",
                "orderEndDate": "0001-01-01",
                "shipStartDate": "0001-01-01",
                "shipEndDate": "0001-01-01",
                "arrivalStartDate": "0001-01-01",
                "arrivalEndDate": "0001-01-01",
                "overrideInd": false,
                "notPreSaved": false
            },
            "allowanceStartDate": "2025-04-07",
            "allowanceEndDate": "2025-06-29",
            "performStartDate": "2025-04-07",
            "performEndDate": "2025-06-29",
            "orderStartDate": "0001-01-01",
            "orderEndDate": "0001-01-01",
            "shipStartDate": "0001-01-01",
            "shipEndDate": "0001-01-01",
            "arrivalStartDate": "0001-01-01",
            "arrivalEndDate": "0001-01-01",
            "vehicleId": "674f54a229642902f9ffd00e",
            "createInd": "AC",
            "locationId": "639033d538196056762e6e28",
            "locationName": "27 - Seattle",
            "distCenter": "DDSE",
            "locationTypeCd": "D",
            "allowanceBillingInfo": {
                "vendorIds": [
                    {
                        "vendorNbr": "021069",
                        "vendorSubAccount": "004",
                        "costArea": "1",
                        "fullVendorNbr": "**********-1"
                    }
                ],
                "absMerchVendor": "**********",
                "absVendorName": "MCLANE COMPANY",
                "absVendorPaymentType": "I",
                "acPayableVendorNbr": "178362",
                "acReceivableVendorNbr": "130868",
                "billingContactName": "<EMAIL>      ",
                "billingContactEmail": "<EMAIL>",
                "vendorBillingList": [
                    {
                        "billingContactName": "<EMAIL>      ",
                        "billingContactEmail": "<EMAIL>"
                    },
                    {
                        "billingContactName": "<EMAIL>    ",
                        "billingContactEmail": "<EMAIL>"
                    },
                    {
                        "billingContactName": "<EMAIL>      ",
                        "billingContactEmail": "<EMAIL>"
                    },
                    {
                        "billingContactName": "<EMAIL>    ",
                        "billingContactEmail": "<EMAIL>"
                    }
                ],
                "vendorItemCount": 2,
                "vendorItemCountsSet": [
                    {
                        "vendorDsdWhseId": {
                            "vendorNbr": "021069",
                            "vendorSubAccount": "004",
                            "costArea": "1",
                            "vendorRank": "B.DSD",
                            "fullVendorNbr": "**********-1",
                            "valid": true
                        },
                        "itemIdSet": [
                            "2100043",
                            "2100044"
                        ],
                        "vendorDsdWhseItemCount": 2
                    }
                ],
                "source": "SIMS_VENDOR",
                "matched": "SIMS_ITEM_VENDOR",
                "orgAbsVendorPaymentType": "I",
                "orgAcPayableVendorNbr": "178362",
                "orgAcReceivableVendorNbr": "130868"
            },
            "allowanceBillingInfos": [
                {
                    "vendorIds": [
                        {
                            "vendorNbr": "021069",
                            "vendorSubAccount": "004",
                            "costArea": "1",
                            "fullVendorNbr": "**********-1"
                        }
                    ],
                    "absMerchVendor": "**********",
                    "absVendorName": "MCLANE COMPANY",
                    "absVendorPaymentType": "I",
                    "acPayableVendorNbr": "178362",
                    "acReceivableVendorNbr": "130868",
                    "billingContactName": "<EMAIL>      ",
                    "billingContactEmail": "<EMAIL>",
                    "vendorBillingList": [
                        {
                            "billingContactName": "<EMAIL>      ",
                            "billingContactEmail": "<EMAIL>"
                        },
                        {
                            "billingContactName": "<EMAIL>    ",
                            "billingContactEmail": "<EMAIL>"
                        },
                        {
                            "billingContactName": "<EMAIL>      ",
                            "billingContactEmail": "<EMAIL>"
                        },
                        {
                            "billingContactName": "<EMAIL>    ",
                            "billingContactEmail": "<EMAIL>"
                        }
                    ],
                    "vendorItemCount": 2,
                    "vendorItemCountsSet": [
                        {
                            "vendorDsdWhseId": {
                                "vendorNbr": "021069",
                                "vendorSubAccount": "004",
                                "costArea": "1",
                                "vendorRank": "B.DSD",
                                "fullVendorNbr": "**********-1",
                                "valid": true
                            },
                            "itemIdSet": [
                                "2100043",
                                "2100044"
                            ],
                            "vendorDsdWhseItemCount": 2
                        }
                    ],
                    "source": "SIMS_VENDOR",
                    "matched": "SIMS_ITEM_VENDOR",
                    "orgAbsVendorPaymentType": "I",
                    "orgAcPayableVendorNbr": "178362",
                    "orgAcReceivableVendorNbr": "130868"
                }
            ],
            "allowanceDateOffsets": {
                "allowanceTypes": [
                    "SCAN",
                    "HEADER_FLAT",
                    "ITEM_FLAT"
                ],
                "startDateOffset": 0,
                "endDateOffset": 0,
                "defaultOrderLeadTimeDays": 0,
                "defaultShipTransitDays": 0,
                "resolvedLeadTimeDays": 0,
                "resolvedShipTransitDays": 0
            },
            "leadDistributorInfos": [],
            "createAllowInd": true,
            "allowanceItems": [
                {
                    "itemId": "2100043",
                    "itemDescription": "CHEEZ IT CRACKERS CHEDDAR JACK          ",
                    "primaryUpc": "002410020362",
                    "consumerUpc": "002410020362",
                    "caseUpc": "0000000000000",
                    "itemUpcs": [
                        "0000000000000",
                        "002410020362"
                    ],
                    "consumerUpcs": [
                        {
                            "upc": "002410020362",
                            "rog": "SSEA",
                            "primaryInd": true,
                            "labelSize": "M",
                            "packDesc": "006",
                            "sizeDesc": "3 OZ   "
                        }
                    ],
                    "effectiveStartDate": "0001-12-28",
                    "effectiveEndDate": "9999-01-01",
                    "allowanceType": "ITEM_FLAT",
                    "packWhse": 6,
                    "ringType": 0,
                    "size": "3.0 OZ",
                    "vendorPackConversionFactor": 1,
                    "unitNetCosts": {
                        "netCostType": "UNIT",
                        "cost": 1.1233,
                        "costAllow": 1.1233,
                        "initialAllowAmt": 0,
                        "newCostAllow": 1.1233
                    },
                    "masterCaseNetCosts": {
                        "netCostType": "MASTER_CASE",
                        "cost": 6.7400,
                        "costAllow": 6.7400,
                        "initialAllowAmt": 0,
                        "newCostAllow": 6.7400
                    },
                    "shipCaseNetCosts": {
                        "netCostType": "SHIP_CASE",
                        "cost": 6.7400,
                        "costAllow": 6.7400,
                        "initialAllowAmt": 0,
                        "newCostAllow": 6.7400
                    },
                    "allowanceAmount": 0,
                    "allowUomType": "CA",
                    "allowUomTypes": [
                        "CA"
                    ],
                    "overlaps": {
                        "offerAllowAmounts": [],
                        "unitizedOverlaps": {
                            "netCostType": "UNIT",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        },
                        "shipCaseOverlaps": {
                            "netCostType": "SHIP_CASE",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        },
                        "masterCaseOverlaps": {
                            "netCostType": "MASTER_CASE",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        }
                    },
                    "modCommand": "NONE"
                },
                {
                    "itemId": "2100044",
                    "itemDescription": "CHEEZ IT CRCKR HOT AND SPICY W/TOBASCO  ",
                    "primaryUpc": "002410037277",
                    "consumerUpc": "002410037277",
                    "caseUpc": "0000000000000",
                    "itemUpcs": [
                        "0000000000000",
                        "002410037277"
                    ],
                    "consumerUpcs": [
                        {
                            "upc": "002410037277",
                            "rog": "SSEA",
                            "primaryInd": true,
                            "labelSize": "M",
                            "packDesc": "006",
                            "sizeDesc": "3 OZ   "
                        }
                    ],
                    "effectiveStartDate": "0001-12-28",
                    "effectiveEndDate": "9999-01-01",
                    "allowanceType": "ITEM_FLAT",
                    "packWhse": 6,
                    "ringType": 0,
                    "size": "3.0 OZ",
                    "vendorPackConversionFactor": 1,
                    "unitNetCosts": {
                        "netCostType": "UNIT",
                        "cost": 1.1233,
                        "costAllow": 1.1233,
                        "initialAllowAmt": 0,
                        "newCostAllow": 1.1233
                    },
                    "masterCaseNetCosts": {
                        "netCostType": "MASTER_CASE",
                        "cost": 6.7400,
                        "costAllow": 6.7400,
                        "initialAllowAmt": 0,
                        "newCostAllow": 6.7400
                    },
                    "shipCaseNetCosts": {
                        "netCostType": "SHIP_CASE",
                        "cost": 6.7400,
                        "costAllow": 6.7400,
                        "initialAllowAmt": 0,
                        "newCostAllow": 6.7400
                    },
                    "allowanceAmount": 0,
                    "allowUomType": "CA",
                    "allowUomTypes": [
                        "CA"
                    ],
                    "overlaps": {
                        "offerAllowAmounts": [],
                        "unitizedOverlaps": {
                            "netCostType": "UNIT",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        },
                        "shipCaseOverlaps": {
                            "netCostType": "SHIP_CASE",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        },
                        "masterCaseOverlaps": {
                            "netCostType": "MASTER_CASE",
                            "convertedAllowanceAmounts": [],
                            "scanAllow": 0,
                            "caseAllow": 0,
                            "shipAllow": 0,
                            "allowSum": 0
                        }
                    },
                    "modCommand": "NONE"
                }
            ],
            "headerFlatAmt": 0,
            "allowanceStatus": "Draft",
            "storeGroups": [],
            "leadDistributorInd": false,
            "includeInd": true,
            "finalizedAmountsInd": false,
            "excludedItems": [],
            "allowanceProcessStatus": "",
            "allowDownstreamStatus": {}
        }
    ],
    "offerAllowanceOverlapResults": {
        "offerAllowanceOverlaps": [],
        "nonDisplayableOffers": [],
        "cancelledOffers": []
    }
}
