import React, { useRef, useState } from "react";
import Divider from "@albertsons/uds/molecule/Divider";
import Popper from "@albertsons/uds/molecule/Popper";
import EventDetailStoreGroupHoverContent from "../../../../../create-event/components/cards/event-details/event-details-store-groups-hover-content";
import { EEVENT_STATUS } from "@me/util-helpers";

function StoreGroupCell({ eventDetails }) {
  const [storeGroupOpen, setStoreGroupOpen] = useState(false);
  const storeGroupRef = useRef(null);
  const isNotValidStatus = [EEVENT_STATUS.CANCELED, EEVENT_STATUS.REJECTED]?.includes(eventDetails?.eventStatus);

  const onPopperBlur = (ev, ref, cb) => {
    if (ev.relatedTarget !== ref.current?.firstChild) {
      cb(false);
    }
  };

  if (isNotValidStatus) {
    return <div className="text-sm px-3 bg-[#f9d3d345] h-full items-center"></div>;
  }

  return (
    <div className="text-sm px-3 red flex">
      {eventDetails?.["storeGroups"]?.[0]?.storeGroupName}
      {eventDetails?.["storeGroups"]?.length > 1 && (
        <>
          <Divider height={15} className="inline-block mx-2" color="#C8DAEB" />
          <span
            className="self-center text-sm font-semibold text-left text-[#1b6ebb] cursor-pointer"
            ref={storeGroupRef}
            onClick={() => setStoreGroupOpen(true)}
          >
            <p>More</p>
          </span>
          <Popper
            anchor={storeGroupRef}
            open={storeGroupOpen}
            onBlur={e => onPopperBlur(e, storeGroupRef, setStoreGroupOpen)}
            autoFocus={true}
            zIndex={10}
          >
            <EventDetailStoreGroupHoverContent
              storeGroups={eventDetails?.["storeGroups"]}
              popoverCloser={() => setStoreGroupOpen(false)}
            />
          </Popper>
        </>
      )}
    </div>
  );
}

export default StoreGroupCell;
