import { useFormContext } from "react-hook-form";
import { RBAC } from "albertsons-react-rbac";
// eslint-disable-next-line @nrwl/nx/enforce-module-boundaries
import { useDispatch } from "react-redux";
import StepperPreviewContainer from "libs/preview-card/src/lib/preview-card-container/preview-card-container";
// eslint-disable-next-line @nrwl/nx/enforce-module-boundaries
import PreviewCardFields from "libs/preview-card/src/lib/preview-card-fields/preview-card-fields";
import { PROMOTION_TYPES } from "../../../../../constants/promotion/types";
import {
  HIDDEN_PRICE,
  HIDDEN_PRICE_TEXT,
  PROMO_DETAILS_HIDDEN_PRICING_FEATURE,
  SUGGESTED,
  createFieldsMapper,
  createPromoDetailsMapper,
  createPromoDetailsSummary,
  getConstRegKey,
  getLoggedInUserType,
  showEdited<PERSON>abel,
  useGetAppBasePath,
} from "@me/util-helpers";
import {
  displayPricingLink,
  handlePromotionPricing,
} from "../../../../../service/promotion-details/promotion-details-service";
import { useNavigate } from "react-router-dom";
import { useSelectorWrap } from "@me/data-rtk";
import PricingLink from "../../pricing-link";
import { useEffect } from "react";
import { promotionTypeHandler } from "../../../../../service/slice/promotion-details-slice";

interface PromotionFields {
  promotionType: string;
  unitMeasure: string;
  factor: string;
  itemLimit: string;
  comments: string;
  amount: string;
  minQuantity: string;
  baseKey: string;
}

const PromotionDetailsPreview: React.FunctionComponent<any> = ({
  previewConfigObj,
  allowStepperType,
  stepperIndex,
  steppersData,
  offerIndex,
  allowIndex,
  childClassName,
}) => {
  const dispatch = useDispatch();
  const { getValues } = useFormContext();
  const { basePath } = useGetAppBasePath(),
    navigate = useNavigate();
  const { fieldsHistoryKeys = null } = previewConfigObj;
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const promotionData = eventDetailsData?.promotionsList?.[`${allowIndex}`];
  const divisionIds = eventDetailsData?.planEvent?.divisionIds;
  const negotiationSimsVendors = eventDetailsData?.planEvent?.negotiationSimsVendors;
  const { hiddenPricingFeatureEnabled } = PROMO_DETAILS_HIDDEN_PRICING_FEATURE;

  const isHiddenPriceAvailable =
    hiddenPricingFeatureEnabled &&
    getLoggedInUserType() === "MERCHANT" &&
    promotionData?.hasOwnProperty(HIDDEN_PRICE_TEXT)
      ? "hiddenPrice"
      : "promoDetails";
  const regKey = getConstRegKey("promotion", offerIndex, allowIndex, getValues),
    promoRegisterField = `${regKey}.${isHiddenPriceAvailable}`,
    promotionWorkflowStatus = getValues(`${regKey}.promotionWorkflowStatus`),
    createPricing = getValues(`${regKey}.createPricing`),
    promoId = getValues(`${regKey}.id`);
  const eventStatus = getValues("eventStatus"),
    id = getValues("id");

  useEffect(() => {
    const promoValues = getValues(promoRegisterField);
    dispatch(
      promotionTypeHandler({
        promoType: promoValues?.promotionType,
      })
    );
  }, []);

  const promoRegisterFieldForPromoDetails = `${regKey}.promoDetails`;
  const promoPricingElement = () => {
    return displayPricingLink(promotionWorkflowStatus, eventStatus) ? (
      <RBAC
        divisionIds={divisionIds}
        permissionsOnly={["PROMOTION_PROMO_MGMT_EDIT_ADD_PROMOTION"]}
        simsVendors={negotiationSimsVendors}
      >
        <PricingLink
          promoId={promoId}
          eventStatus={eventStatus}
          navigate={navigate}
          basePath={basePath}
          handlePromotionPricing={handlePromotionPricing}
          createPricing={createPricing}
        />
      </RBAC>
    ) : null;
  };

  const fieldsMapper = createFieldsMapper(promoRegisterField);
  const promoDetailsFieldsMapper = createFieldsMapper(
    promoRegisterFieldForPromoDetails
  );

  const promoDetailsHiddenPriceMapper = createPromoDetailsMapper(
    fieldsMapper,
    getValues
  );
  const promoDetailsFieldMapper = createPromoDetailsMapper(
    promoDetailsFieldsMapper,
    getValues
  );

  const getPreviewFields = (
    <PreviewCardFields
      previewConfigObj={previewConfigObj}
      fieldsMapper={fieldsMapper}
      module="promotion"
      getValues={getValues}
      cardIndex={offerIndex}
      cardItemIndex={allowIndex}
      getCustomPlanValue={null}
      className={childClassName}
    ></PreviewCardFields>
  );

  const getPromoDetailSummary = () => {
    const promoType = PROMOTION_TYPES[getValues(fieldsMapper.promotionType)];
    const promoDetailsHiddenPriceSummary = createPromoDetailsSummary(
      promoDetailsHiddenPriceMapper
    );
    const defaultPromoType =
      PROMOTION_TYPES[getValues(promoDetailsFieldsMapper.promotionType)];
    const promoSummary = createPromoDetailsSummary(promoDetailsFieldMapper);
    if (isHiddenPriceAvailable === HIDDEN_PRICE_TEXT) {
      return (
        <>
          <span className="pr-[15px]">
            {SUGGESTED} {promoSummary[defaultPromoType]}
          </span>

          <span>
            {HIDDEN_PRICE} {promoDetailsHiddenPriceSummary[promoType]}
          </span>
        </>
      );
    }
    return promoSummary[promoType];
  };
  const isShowEdited = showEditedLabel(
    fieldsHistoryKeys,
    getValues,
    offerIndex,
    allowIndex,
    "promotion"
  );

  return (
    <StepperPreviewContainer
      stepperPreviewHeaderLabel={previewConfigObj?.title}
      stepperPreviewHeaderValue={getPromoDetailSummary()}
      steppersData={steppersData}
      stepperIndex={stepperIndex}
      isShowEdited={isShowEdited}
      promoPricingElement={promoPricingElement}
      promotionData={promotionData}
    >
      {getPreviewFields}
    </StepperPreviewContainer>
  );
};

export default PromotionDetailsPreview;
