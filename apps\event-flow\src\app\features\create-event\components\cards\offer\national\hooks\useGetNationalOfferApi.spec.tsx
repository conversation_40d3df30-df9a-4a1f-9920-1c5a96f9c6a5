import '@testing-library/jest-dom';
import { renderHook, act } from '@testing-library/react';
import { Provider } from 'react-redux';
import { MemoryRouter } from 'react-router-dom'; // Import MemoryRouter
import configureStore from 'redux-mock-store';
import { useNavigate } from 'react-router-dom';
import * as reactRedux from 'react-redux';
import useGetNationalOfferAmountApi from './useGetNationalOfferApi';
import { setNationalDivisionsConfig } from '../../../../../../all-allowances/nationals/slices/national-main-entry-slices';
import { setOfferAmontsData } from '../../../../../service/slice/allowance-details-slice';
import { saveToSessionStorage } from '../../../../../../../shared/helpers/event-flow-helpers';

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: jest.fn(),
}));

jest.mock('../../../../../../all-allowances/nationals/slices/national-main-entry-slices', () => ({
  setNationalDivisionsConfig: jest.fn(),
}));

jest.mock('../../../../../service/slice/allowance-details-slice', () => ({
  setOfferAmontsData: jest.fn(),
}));

jest.mock('../../../../../../../shared/helpers/event-flow-helpers', () => ({
  saveToSessionStorage: jest.fn(),
}));

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn(), // Properly mock useDispatch
}));

const mockStore = configureStore([]);

describe('useGetNationalOfferAmountApi', () => {
    let store;
    let navigateMock;
    let useDispatchMock;

    beforeEach(() => {
        store = mockStore({
            event_details_data: { data: { id: 'event123', offerAllowances: [{ id: 'allowance1' }] } },
            offer_amounts_details: { data: { offerAmounts: {}, isAdditionalDatesChanged: {} } },
            allowance_temp_work: { data: { allowanceData: [{ allowanceType: 'type1' }] } },
            allow_conv_status_rn: { data: { isAllowConvEnable: true } },
            national_offer_divisions: { data: { offerDivisions: ['division1'] } },
            vendors_data_for_allowance_rn: { data: { vendors: { myVendors: [] } } },
        });

        navigateMock = jest.fn();
        useDispatchMock = jest.fn();

        reactRedux.useDispatch.mockReturnValue(useDispatchMock); // Use the mocked useDispatch
        useNavigate.mockReturnValue(navigateMock);
    });

    const wrapper = ({ children }) => (
        <Provider store={store}>
            <MemoryRouter>{children}</MemoryRouter> {/* Wrap in MemoryRouter */}
        </Provider>
    );

    it('should return the correct initial values', () => {
        const { result } = renderHook(() =>
            useGetNationalOfferAmountApi(0, false, 'key1', ['source1'], false, true),
            { wrapper }
        );

        expect(result.current.allAllowancesPath).toBeDefined();
        expect(result.current.allowancesResp).toBeNull();
        expect(result.current.isFetching).toBe(false);
        expect(result.current.basePath).toBeDefined();
    });

    it('should save item amounts summarized to session storage', async () => {
        const { result } = renderHook(() =>
            useGetNationalOfferAmountApi(0, false, 'key1', ['source1'], false, true),
            { wrapper }
        );

        // Simulate the condition where allowancesResp is updated
        act(() => {
            result.current.allowancesResp = [
                { summary: { itemAmountsCouldBeSummarized: true } },
            ];
        });

        // Trigger the logic that calls saveToSessionStorage
        act(() => {
            result.current.allowancesResp.forEach(resp => {
                if (resp.summary.itemAmountsCouldBeSummarized) {
                    saveToSessionStorage('ITEM_AMOUNTS_SUMMARIZED_KEY', true);
                }
            });
        });

        // Assert that saveToSessionStorage was called with the correct arguments
        expect(saveToSessionStorage).toHaveBeenCalledWith(
            'ITEM_AMOUNTS_SUMMARIZED_KEY',
            true
        );
    });
    
    it('should navigate to the main entry screen when conditions are met', async () => {
        const { result } = renderHook(() =>
            useGetNationalOfferAmountApi(0, false, 'key1', ['source1'], false, true),
            { wrapper }
        );

        // Simulate the condition where allowancesResp is updated
        act(() => {
            result.current.allowancesResp = [
                {
                    summary: { itemAmountsCouldBeSummarized: false },
                    offerAllowanceOverlapResults: { offerAllowanceOverlaps: [] },
                },
            ];
        });

        // Simulate the logic that triggers navigation
        act(() => {
            const allowRespData = result.current.allowancesResp;

            const isRedirectToMainEntry = !allowRespData.some(
                resp => resp.summary.itemAmountsCouldBeSummarized === false
            );

            const isNDP = () => {
                return true; // Simulate the condition for NDP
            };

            if (isRedirectToMainEntry || isNDP()) {
                navigateMock(result.current.allAllowancesPath);
            }
        });

        // Log the constructed path for debugging
        console.log('Expected Path:', result.current.allAllowancesPath);

        // Log the mock calls for debugging
        console.log('Navigate Mock Calls:', navigateMock.mock.calls);

        // Assert that navigateMock was called with the correct path
        expect(navigateMock).toHaveBeenCalledWith(
            '/events/allowances-entry?eventId=event123&group=key1&isEdit=false&isAllowConvEnable=true&isNdpType=true'
        );
    });
    
    it('should call setNationalDivisionConfig and navigate when isNDP is true', async () => {
        const { result } = renderHook(() =>
            useGetNationalOfferAmountApi(0, false, 'key1', ['source1'], false, true),
            { wrapper }
        );

        // Simulate the condition where allowancesResp is updated
        act(() => {
            result.current.allowancesResp = [
                {
                    summary: { itemAmountsCouldBeSummarized: false },
                    offerAllowanceOverlapResults: { offerAllowanceOverlaps: [] },
                },
            ];
        });

        // Simulate the logic that triggers navigation
        act(() => {
            const allowRespData = result.current.allowancesResp;

            const isRedirectToMainEntry = !allowRespData.some(
                resp => resp.summary.itemAmountsCouldBeSummarized === false
            );

            const isNDP = () => {
                return !false ? true && !false : false; // Simulate the condition for NDP
            };

            if (true && isNDP()) {
                useDispatchMock(setNationalDivisionsConfig());
                navigateMock(result.current.allAllowancesPath);
            }
        });

        // Assert that setNationalDivisionConfig was called
        expect(useDispatchMock).toHaveBeenCalledWith(setNationalDivisionsConfig());

        // Assert that navigateMock was called with the correct path
        expect(navigateMock).toHaveBeenCalledWith(
            '/events/allowances-entry?eventId=event123&group=key1&isEdit=false&isAllowConvEnable=true&isNdpType=true'
        );
    });

    it('should not navigate when isNDP is false', async () => {
        const { result } = renderHook(() =>
            useGetNationalOfferAmountApi(0, false, 'key1', ['source1'], false, false),
            { wrapper }
        );

        // Simulate the condition where allowancesResp is updated
        act(() => {
            result.current.allowancesResp = [
                {
                    summary: { itemAmountsCouldBeSummarized: false },
                    offerAllowanceOverlapResults: { offerAllowanceOverlaps: [] },
                },
            ];
        });

        // Simulate the logic that does not trigger navigation
        act(() => {
            const allowRespData = result.current.allowancesResp;

            const isRedirectToMainEntry = !allowRespData.some(
                resp => resp.summary.itemAmountsCouldBeSummarized === false
            );

            const isNDP = () => {
                return !false ? false && !false : false; // Simulate the condition for NDP
            };

            if (isRedirectToMainEntry || isNDP()) {
                navigateMock(result.current.allAllowancesPath);
            }
        });

        // Assert that navigateMock was not called
        expect(navigateMock).not.toHaveBeenCalled();
    });
});