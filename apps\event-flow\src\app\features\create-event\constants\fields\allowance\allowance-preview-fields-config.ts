import { formateVehicleDate } from "../../../service/event-details/event-detail-service";
import {
  displayPromoDetailsBasedOnType,
  getAllowAmtOrUom,
  hideDependingFieldOnValue,
} from "../../../service/allowance/allowance-service";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import { parseISO } from "date-fns";

const getYearOnly = param => (param ? parseISO(param).getFullYear() : "");
const suggestedPaymentTypeFieldMapper = [
  {
    key: "suggestedAcPayableVendorNbr",
    value: "Deduct",
  },
  {
    key: "suggestedAcReceivableVendorNbr",
    value: "Invoice",
  },
];
const paymentTypeFieldMapper = [
  {
    key: "acPayableVendorNbr",
    value: "Deduct",
  },
  {
    key: "acReceivableVendorNbr",
    value: "Invoice",
  },
];
const PROMO_TYPE_DELIMETER_MAPPER = {
  NET_PRICE: {
    prefix: "$",
    suffix: "",
  },
  BUY_ONE_GET_ONE: {
    prefix: "$",
    suffix: "",
  },
  BUY_X_GET_ONE: {
    prefix: "$",
    suffix: "",
  },
  CENT_OFF: {
    prefix: "$",
    suffix: "",
  },
  PERCENT_OFF: {
    prefix: "",
    suffix: "%",
  },
  UN_SUPPORTED: {
    prefix: "",
    suffix: "",
  },
};
// allowance dates will take the required fields from below depending on the allowance Type.
export const ALLOWANCE_DATES_PREVIEW_FIELDS = [
  {
    label: "Vehicle Type/Custom Date",
    key: "vehicleTypeOrCustomDate",
    formRegisterKey: "allowance.vehicleTypeOrCustomDate",
    historyMapperKey:
      "offerallowances.allowances.vehicle.vehicleType.vehicleTypDesc",
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    conditionalShow: true,
  },
  {
    label: "Year",
    key: "year",
    formRegisterKey: "",
    historyMapperKey: "offerallowances.allowances.vehicle.year",
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    callback: getYearOnly,
    conditionalShow: true,
  },
  {
    label: "Start Week/Vehicle",
    key: "startWeekOrVehicle",
    formRegisterKey: "",
    historyMapperKey: "offerallowances.allowances.vehicle.vehicleNm",
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    conditionalShow: true,
  },
  {
    label: "Vehicle Start",
    key: "vehicleStart",
    formRegisterKey: "",
    historyMapperKey: "offerallowances.allowances.allowanceStartDate",
    formatAsVehicleDate: true,
    isLeft: false,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    callback: val => (val ? formateVehicleDate(val) : ""),
    conditionalShow: true,
  },
  {
    label: "Vehicle End",
    key: "vehicleEnd",
    formRegisterKey: "",
    historyMapperKey: "offerallowances.allowances.allowanceEndDate",
    formatAsVehicleDate: true,
    isLeft: false,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    callback: val => (val ? formateVehicleDate(val) : ""),
    conditionalShow: true,
  },
  {
    label: "Allowance Start Date",
    key: "arrivalStart",
    formRegisterKey: "",
    historyMapperKey: "offerallowances.allowances.arrivalStartDate",
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    callback: (val, isCurrent, formValues) =>
      val ? formateVehicleDate(val, isCurrent, formValues) : "",
    conditionalShow: true,
  },
  {
    label: "Allowance End Date",
    key: "arrivalEnd",
    formRegisterKey: "",
    historyMapperKey: "offerallowances.allowances.arrivalEndDate",
    isLeft: false,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    callback: (val, isCurrent, formValues) =>
      val ? formateVehicleDate(val, isCurrent, formValues) : "",
    conditionalShow: true,
  },
  {
    label: "Order Start",
    key: "orderStart",
    formRegisterKey: "",
    historyMapperKey: "offerallowances.allowances.orderStartDate",
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    callback: val => (val ? formateVehicleDate(val) : ""),
    conditionalShow: true,
  },
  {
    label: "Order End",
    key: "orderEnd",
    formRegisterKey: "",
    historyMapperKey: "offerallowances.allowances.orderEndDate",
    isLeft: false,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    callback: val => (val ? formateVehicleDate(val) : ""),
    conditionalShow: true,
  },
  {
    label: "Arrival Start",
    key: "arrivalStart",
    formRegisterKey: "",
    historyMapperKey: "offerallowances.allowances.arrivalStartDate",
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    callback: val => (val ? formateVehicleDate(val) : ""),
    conditionalShow: true,
  },
  {
    label: "Arrival End",
    key: "arrivalEnd",
    formRegisterKey: "",
    historyMapperKey: "offerallowances.allowances.arrivalEndDate",
    isLeft: false,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    callback: val => (val ? formateVehicleDate(val) : ""),
    conditionalShow: true,
  },
  {
    label: "Shipment Start",
    key: "arrivalStart",
    formRegisterKey: "",
    historyMapperKey: "offerallowances.allowances.arrivalStartDate",
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    conditionalShow: true,
    callback: val => (val ? formateVehicleDate(val) : ""),
  },
  {
    label: "Shipment End",
    key: "arrivalEnd",
    formRegisterKey: "",
    historyMapperKey: "offerallowances.allowances.arrivalEndDate",
    isLeft: false,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    callback: val => (val ? formateVehicleDate(val) : ""),
    conditionalShow: true,
  },
];
export const ALLOW_DATE_HEADER_FIELDS = {
  vehicleStart: {
    key: "vehicleStart",
    historyMapperKey: "offerallowances.allowances.allowanceStartDate",
    callback: val => (val ? formateVehicleDate(val) : ""),
  },
  vehicleEnd: {
    key: "vehicleEnd",
    historyMapperKey: "offerallowances.allowances.allowanceEndDate",
    callback: val => (val ? formateVehicleDate(val) : ""),
  },
};
export const ALLOW_HEADERFLAT_HEADER_FIELD = {
  headerFlatAmt: {
    key: "headerFlatAmt",
    historyMapperKey: "offerallowances.allowances.headerFlatAmt",
    callback: (param, isCurrent, fieldObj) =>
      getAllowAmtOrUom(param, isCurrent, fieldObj),
  },
  performanceType: {
    key: "performanceType",
    historyMapperKey: "offerallowances.allowances.performance.performance",
  },
};
export const ALLOW_AMOUNT_HEADER_FIELDS = {
  allowanceAmount: {
    key: "allowanceAmount",
    historyMapperKey:
      "offerallowances.allowances.allowanceItems.allowanceItemComps.allowAmount",
    allowTypeHistoryKeyMapper: {
      [efConstants.ALLOWANCE_TYPES.HEADERFLAT.label]:
        "offerallowances.allowances.headerFlatAmt",
    },
    callback: (param, isCurrent, fieldObj, isUomVisible) =>
      getAllowAmtOrUom(param, isCurrent, fieldObj, isUomVisible),
  },
  uom: {
    key: "uom",
    historyMapperKey: "offerallowances.allowances.allowanceItems.uom",
    callback: (param, isCurrent, fieldObj) =>
      getAllowAmtOrUom(param, isCurrent, fieldObj),
  },
};
export const PROMO_DATE_HEADER_FIELDS = {
  vehicleStart: {
    key: "vehicleStart",
    historyMapperKey: "promotions.promoStartDate",
    callback: val => (val ? formateVehicleDate(val) : ""),
  },
  vehicleEnd: {
    key: "vehicleEnd",
    historyMapperKey: "promotions.promoEndDate",
    callback: val => (val ? formateVehicleDate(val) : ""),
  },
};
export const ALLOWANCE_AMNTS_HISTORY_KEYS = [
  "offerallowances.allowances.allowanceItems.allowanceItemComps.allowAmount",
  "offerallowances.allowances.allowanceItems.uom",
  "offerallowances.allowances.headerFlatAmt",
];
export const ALLOWANCE_DATES_HISTORY_KEYS = [
  "offerallowances.allowances.vehicle.vehicleType.vehicleTypDesc",
  "offerallowances.allowances.vehicle.year",
  "offerallowances.allowances.vehicle.vehicleNm",
  "offerallowances.allowances.vehicle.startDate",
  "offerallowances.allowances.vehicle.endDate",
  "offerallowances.allowances.arrivalStartDate",
  "offerallowances.allowances.arrivalEndDate",
  "offerallowances.allowances.allowanceStartDate",
  "offerallowances.allowances.allowanceEndDate",
  "offerallowances.allowances.orderStartDate",
  "offerallowances.allowances.orderEndDate",
];

export const WHSE_ALLOW_DATES_HISTORY_KEYS = [...ALLOWANCE_DATES_HISTORY_KEYS];

export const DEFAULT_BILLING_INFO_HIST_KEYS = [
  "offerallowances.allowances.allowanceBillingInfo.absVendorPaymentType",
  "offerallowances.allowances.allowanceBillingInfo.absVendorName",
  "offerallowances.allowances.allowanceBillingInfo.absMerchVendor",
  "offerallowances.allowances.allowanceBillingInfo.billingName",
  "offerallowances.allowances.allowanceBillingInfo.acPayableVendorNbr",
  "offerallowances.allowances.allowanceBillingInfo.acReceivableVendorNbr",
  "offerallowances.allowances.allowanceBillingInfo.vendorComment",
  "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
  "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
  "offerallowances.allowances.allowanceBillingInfo.suggestedAcPayableVendorNbr",
  "offerallowances.allowances.allowanceBillingInfo.suggestedAcReceivableVendorNbr",
];

export const ALLOWANCE_TOTAL_KEYS = [
  "offerallowances.allowances.performance.performance",
  ...ALLOWANCE_AMNTS_HISTORY_KEYS,
  ...ALLOWANCE_DATES_HISTORY_KEYS,
  ...DEFAULT_BILLING_INFO_HIST_KEYS,
];

export const PROMO_DETAILS_HISTORY_KEYS = [
  "promotions.promoDetails.promotionType",
  "promotions.promoDetails.factor",
  "promotions.promoDetails.amount",
  "promotions.promoDetails.unitMeasure",
  "promotions.promoDetails.itemLimit",
  "promotions.promoDetails.minQuantity",
  "promotions.promoDetails.comments",
];
export const PROMOTION_DATES_HISTORY_KEYS = [
  "promotions.promoEndDate",
  "promotions.promoStartDate",
  "promotions.vehicle.vehicleNm",
  "promotion.vehicleTypeOrCustomDate",
  "promotions.vehicle.vehicleType.vehicleTypDesc",
];

export const PROMO_TOTAL_KEYS = [
  ...PROMO_DETAILS_HISTORY_KEYS,
  ...PROMOTION_DATES_HISTORY_KEYS,
];
export const ALLOW_HEADERFLAT_HEADER_HISTORY_KEYS = [
  "offerallowances.allowances.headerFlatAmt",
  "offerallowances.allowances.performance.performance",
];
export const ALLOWANCE_HEADER_FLAT_AMOUNT = [
  {
    label: "Allowance Amount",
    key: "headerFlatAmt",
    isCustomPlanValue: false,
    formRegisterKey: "allowance.allowanceAmount",
    historyMapperKey: "offerallowances.allowances.headerFlatAmt",
    allowTypeHistoryKeyMapper: {
      [efConstants.ALLOWANCE_TYPES.HEADERFLAT.label]:
        "offerallowances.allowances.headerFlatAmt",
    },
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    className: "flex flex-row space-x-2",

    callback: (param, isCurrent, fieldObj) =>
      getAllowAmtOrUom(param, isCurrent, fieldObj),
    // param ? `${!param?.toString()?.includes("$") ? `$${param}` : param}` : `$0`,
  },
  {
    label: "Performance Type",
    key: "performanceType",
    isCustomPlanValue: false,
    formRegisterKey: "offerallowances.allowances.performance.performanc",
    historyMapperKey: "offerallowances.allowances.performance.performance",
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    className: "flex flex-row space-x-2",
  },
];
export const ALLOWANCE_AMOUNTS_PREVIEW_FIELDS = [
  {
    label: "Allowance Amount",
    key: "allowanceAmount",
    isCustomPlanValue: false,
    formRegisterKey: "allowance.allowanceAmount",
    historyMapperKey:
      "offerallowances.allowances.allowanceItems.allowanceItemComps.allowAmount",
    allowTypeHistoryKeyMapper: {
      [efConstants.ALLOWANCE_TYPES.HEADERFLAT.label]:
        "offerallowances.allowances.headerFlatAmt",
    },
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    className: "flex flex-row space-x-2",

    callback: (param, isCurrent, fieldObj) =>
      getAllowAmtOrUom(param, isCurrent, fieldObj),
    // param ? `${!param?.toString()?.includes("$") ? `$${param}` : param}` : `$0`,
  },
  {
    label: "UOM",
    key: "uom",
    formRegisterKey: "",
    isCustomPlanValue: false,
    historyMapperKey: "offerallowances.allowances.allowanceItems.uom",
    formatAsVehicleDate: true,
    isLeft: false,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    className: "flex flex-row space-x-2",
    callback: (param, isCurrent, fieldObj) =>
      getAllowAmtOrUom(param, isCurrent, fieldObj),
  },
];
export const ALLOWANCE_DEFAULT_BILLING_PREVIEW_FIELDS = [
  {
    label: "ABS Vendor Payment Type",
    key: "absVendorPaymentType",
    formRegisterKey: "",
    historyMapperKey:
      "offerallowances.allowances.allowanceBillingInfo.absVendorPaymentType",
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    callback: (val, isCurrent) => {
      hideDependingFieldOnValue({
        fields: ALLOWANCE_DEFAULT_BILLING_PREVIEW_FIELDS,
        val,
        isCurrent,
        dependingFields: paymentTypeFieldMapper,
      });
      return val;
    },
    conditionalShow: true,
    className: "flex flex-row space-x-2",
  },
  {
    label: "ABS Vendor Name",
    key: "absVendorName",
    formRegisterKey: "",
    historyMapperKey:
      "offerallowances.allowances.allowanceBillingInfo.absVendorName",
    formatAsVehicleDate: false,
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    className: "flex flex-row space-x-2",
  },
  {
    label: "ABS Merch Vendor",
    key: "absMerchVendor",
    formRegisterKey: "",
    historyMapperKey:
      "offerallowances.allowances.allowanceBillingInfo.absMerchVendor",
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    className: "flex flex-row space-x-2",
    strikeOff: false,
  },
  {
    label: "Vendor Name" ,
    key: "billingName",
    formRegisterKey: "",
    historyMapperKey:
      "offerallowances?.allowance?.allowanceBillingInfo?.billingName",
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    className: "flex flex-row space-x-2",
    strikeOff: false,
    conditionalShow: true,
  },
  {
    label: "A/P Vendor Number",
    key: "acPayableVendorNbr",
    formRegisterKey: "",
    historyMapperKey:
      "offerallowances.allowances.allowanceBillingInfo.acPayableVendorNbr",
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    className: "flex flex-row space-x-2",
    strikeOff: false,
    conditionalShow: true,
  },
  {
    label: "A/R Vendor Number",
    key: "acReceivableVendorNbr",
    formRegisterKey: "",
    historyMapperKey:
      "offerallowances.allowances.allowanceBillingInfo.acReceivableVendorNbr",
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    className: "flex flex-row space-x-2",
    strikeOff: false,
    conditionalShow: true,
  },
  {
    label: "Suggested ABS Vendor Payment Type",
    key: "suggestedVendorPaymentType",
    formRegisterKey: "",
    historyMapperKey:
      "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
    isLeft: false,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    className: "flex flex-row space-x-2",
    checkNoValue: true,
    callback: (val, isCurrent) => {
      // hideDependingFieldOnValue({
      //   fields: ALLOWANCE_DEFAULT_BILLING_PREVIEW_FIELDS,
      //   val,
      //   isCurrent,
      //   dependingFields: suggestedPaymentTypeFieldMapper,
      // });
      return val;
    },
    conditionalShow: true,
    strikeOff: false,
  },
  {
    label: "Suggested A/P Vendor Number",
    key: "suggestedAcPayableVendorNbr",
    formRegisterKey: "",
    historyMapperKey:
      "offerallowances.allowances.allowanceBillingInfo.suggestedAcPayableVendorNbr",
    isLeft: false,
    prevValue: "",
    nextValue: "",
    className: "flex flex-row space-x-2",
    displayColor: "",
    strikeOff: false,
    checkNoValue: true,
    conditionalShow: true,
  },
  {
    label: "Suggested A/R Vendor Number",
    key: "suggestedAcReceivableVendorNbr",
    formRegisterKey: "",
    historyMapperKey:
      "offerallowances.allowances.allowanceBillingInfo.suggestedAcReceivableVendorNbr",
    isLeft: false,
    className: "flex flex-row space-x-2",
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    checkNoValue: true,
    conditionalShow: true,
  },
  {
    label: "Vendor Comment",
    key: "vendorComment",
    formRegisterKey: "",
    historyMapperKey:
      "offerallowances.allowances.allowanceBillingInfo.vendorComment",
    isLeft: false,
    className: "flex flex-row space-x-2",
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    checkNoValue: true,
    conditionalShow: true,
  },
  {
    label: "Vendor Tracking Number",
    key: "vendorOfferTrackingNbr",
    formRegisterKey: "",
    historyMapperKey:
      "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
    isLeft: false,
    className: "flex flex-row space-x-2",
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    checkNoValue: true,
    conditionalShow: true,
  },
];

export const ALLOW_STORE_SELECTION_PREVIEW_FIELDS = [
  {
    label: "Store Group Type",
    key: "planStoreGroupType",
    formRegisterKey: "planStoreGroupType",
    historyMapperKey: "allowance.planStoreGroupType", // need to change based on be key,
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
  },
  {
    label: "Store Groups",
    key: "storeGroups",
    formRegisterKey: "storeGroups",
    historyMapperKey: "allowance.storeGroups",
    formatAsVehicleDate: true,
    isLeft: false,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
  },
];

export const PROMOTION_DATES_PREVIEW_FIELDS = [
  {
    label: "Vehicle Type/Custom Date",
    key: "vehicleTypeOrCustomDate",
    formRegisterKey: "promotion.vehicleTypeOrCustomDate",
    historyMapperKey: "promotions.vehicle.vehicleType.vehicleTypDesc", // need to change based on be key,
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    conditionalShow: true,
  },
  {
    label: "Year",
    key: "year",
    formRegisterKey: "",
    historyMapperKey: "promotion.vehicleTypeOrCustomDate",
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    callback: getYearOnly,
    conditionalShow: true,
  },
  {
    label: "Start Week/Vehicle",
    key: "startWeekOrVehicle",
    formRegisterKey: "",
    historyMapperKey: "promotions.vehicle.vehicleNm",
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    conditionalShow: true,
  },
  {
    label: "Vehicle Start",
    key: "vehicleStart",
    formRegisterKey: "",
    historyMapperKey: "promotions.promoStartDate",
    formatAsVehicleDate: true,
    isLeft: false,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    callback: val => (val ? formateVehicleDate(val) : ""),
    conditionalShow: true,
  },
  {
    label: "Vehicle End",
    key: "vehicleEnd",
    formRegisterKey: "",
    historyMapperKey: "promotions.promoEndDate",
    formatAsVehicleDate: true,
    isLeft: false,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    callback: val => (val ? formateVehicleDate(val) : ""),
    conditionalShow: true,
  },
];
export const PROMO_DETAILS_PREVIEW_FIELDS = [
  {
    label: "Promotion Type",
    key: "promotionType",
    formRegisterKey: "promotion.vehicleTypeOrCustomDate",
    historyMapperKey: "promotions.promoDetails.promotionType", // need to change based on be key,
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    callback: (val, isCurrent) =>
      val
        ? displayPromoDetailsBasedOnType(
            val,
            PROMO_DETAILS_PREVIEW_FIELDS,
            isCurrent
          )
        : "",
    iSFieldsRelated: true,
    promoTypeVal: "",
    prevPromoTypeVal: "",
  },
  {
    label: "Factor",
    key: "factor",
    formRegisterKey: "",
    historyMapperKey: "promotions.promoDetails.factor",
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    className: "flex flex-row space-x-2",
    showFields: ["NET_PRICE"],
    conditionalShow: true,
  },
  {
    label: "Amount",
    key: "amount",
    formRegisterKey: "",
    historyMapperKey: "promotions.promoDetails.amount",
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    className: "flex flex-row space-x-2",
    callback: (param, isCurrent) => getPromoAmount(param, isCurrent),
    showFields: ["NET_PRICE", "CENT_OFF", "PERCENT_OFF"],
    conditionalShow: true,
  },
  {
    label: "UOM",
    key: "unitMeasure",
    formRegisterKey: "",
    historyMapperKey: "promotions.promoDetails.unitMeasure",
    formatAsVehicleDate: true,
    isLeft: false,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    showFields: ["NET_PRICE", "CENT_OFF", "PERCENT_OFF"],
    conditionalShow: true,
  },
  {
    label: "Item Limit",
    key: "itemLimit",
    formRegisterKey: "",
    historyMapperKey: "promotions.promoDetails.itemLimit",
    formatAsVehicleDate: true,
    isLeft: false,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    className: "flex flex-row space-x-2",
    showFields: [
      "NET_PRICE",
      "BUY_ONE_GET_ONE",
      "BUY_X_GET_ONE",
      "CENT_OFF",
      "PERCENT_OFF",
    ],
    conditionalShow: true,
  },
  {
    label: "Minimum Quantity",
    key: "minQuantity",
    formRegisterKey: "",
    historyMapperKey: "promotions.promoDetails.minQuantity",
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    className: "flex flex-row space-x-2",
    showFields: ["NET_PRICE", "BUY_X_GET_ONE"],
    conditionalShow: true,
  },
  {
    label: "Comments",
    key: "comments",
    formRegisterKey: "",
    historyMapperKey: "promotions.promoDetails.comments",
    isLeft: true,
    prevValue: "",
    nextValue: "",
    displayColor: "",
    strikeOff: false,
    className: "flex flex-row space-x-2",
    showFields: ["UN_SUPPORTED"],
    conditionalShow: true,
  },
];

const getPromoAmount = (val, isCurrent) => {
  const formatValWTwoDec = val ? parseFloat(val)?.toFixed(2) : 0;
  const { promoTypeVal, prevPromoTypeVal } =
    PROMO_DETAILS_PREVIEW_FIELDS?.[0] || {};
  const promoTypeSel = isCurrent
    ? promoTypeVal
    : prevPromoTypeVal || promoTypeVal;
  const { prefix = "", suffix = "" } = promoTypeSel
    ? PROMO_TYPE_DELIMETER_MAPPER?.[promoTypeSel] ?? {}
    : {};
  return val ? `${prefix}${formatValWTwoDec}${suffix}` : "";
};
