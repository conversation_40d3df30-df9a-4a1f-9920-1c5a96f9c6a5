import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import MultiSelectDropDown from "./multi-selected";

// Mock Checkbox as a simple input checkbox
jest.mock("@albertsons/uds/molecule/Checkbox", () => (props) => (
  <input
    type="checkbox"
    checked={props.checked}
    onClick={props.onClick}
    data-testid={props.id || "checkbox"}
  />
));

// Mock Popper to simply render its children when open is true.
jest.mock("@albertsons/uds/molecule/Popper", () => ({ open, onBlur, children }) =>
  open ? (
    <div data-testid="popper" onBlur={onBlur}>
      {children}
    </div>
  ) : null
);

describe("MultiSelectDropDown", () => {
  const initialValues = [
    { name: "option1", divisionId: "div1", divisionName: "Division 1", checked: false },
    { name: "option2", divisionId: "div2", divisionName: "Division 2", checked: false },
  ];

  it("renders dropdown with initial text 'Select' when no option is checked", () => {
    render(
      <MultiSelectDropDown values={initialValues} setValues={jest.fn()} />
    );
    expect(screen.getByText("Select")).toBeInTheDocument();
    // Instead of expecting a role "button", we check for the container by its id.
    expect(document.getElementById("abs-multi-select-dropdown0")).toBeInTheDocument();
  });

  it("toggles open state when dropdown container is clicked", () => {
    render(
      <MultiSelectDropDown values={initialValues} setValues={jest.fn()} />
    );
    // The dropdown container is a div with id "abs-multi-select-dropdown0"
    const dropdownContainer = screen.getByText(/Select/);
    // Initially, Popper should not be rendered.
    expect(screen.queryByTestId("popper")).not.toBeInTheDocument();

    // Click the dropdown to open it.
    fireEvent.click(dropdownContainer);
    expect(screen.getByTestId("popper")).toBeInTheDocument();
  });

  it("calls setValues with all options toggled when clicking 'Select / Unselect All'", () => {
    const setValuesMock = jest.fn();
    // Start with all options unchecked.
    render(
      <MultiSelectDropDown values={initialValues} setValues={setValuesMock} />
    );

    // Open dropdown
    fireEvent.click(screen.getByText(/Select/));
    // The "Select / Unselect All" checkbox is the first checkbox inside the Popper.
    const selectAllCheckbox = screen.getByTestId("abs-multi-select-dropdown-select-all");
    // Click the select all checkbox.
    fireEvent.click(selectAllCheckbox);

    // Expect setValuesMock to be called with updated values where checked is toggled to true.
    expect(setValuesMock).toHaveBeenCalledWith(
      initialValues.map(val => ({ ...val, checked: true }))
    );
  });

  it("toggles an individual option when its checkbox is clicked", () => {
    const setValuesMock = jest.fn();
    // Start with both options unchecked.
    const values = [
      { name: "option1", divisionId: "div1", divisionName: "Division 1", checked: false },
      { name: "option2", divisionId: "div2", divisionName: "Division 2", checked: false },
    ];
    render(
      <MultiSelectDropDown values={values} setValues={setValuesMock} />
    );

    // Open dropdown
    fireEvent.click(screen.getByText(/Select/));
    // Get the checkbox for the first option.
    const firstOptionCheckbox = screen.getByTestId("abs-multi-select-dropdown-select-0");
    // Click the first option.
    fireEvent.click(firstOptionCheckbox);

    // setValues should be called with the first option toggled to true.
    const expectedValues = [
      { name: "option1", divisionId: "div1", divisionName: "Division 1", checked: true },
      { name: "option2", divisionId: "div2", divisionName: "Division 2", checked: false },
    ];
    expect(setValuesMock).toHaveBeenCalledWith(expectedValues);
  });

  it("updates dropdown text to show count of selected items", () => {
    // Create values with one option already checked.
    const values = [
      { name: "option1", divisionId: "div1", divisionName: "Division 1", checked: true },
      { name: "option2", divisionId: "div2", divisionName: "Division 2", checked: false },
    ];
    render(
      <MultiSelectDropDown values={values} setValues={jest.fn()} />
    );
    expect(screen.getByText("Selected (1)")).toBeInTheDocument();
  });
});
