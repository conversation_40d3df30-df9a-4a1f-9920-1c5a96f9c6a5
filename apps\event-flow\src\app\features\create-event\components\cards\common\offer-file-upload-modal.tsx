import Modal from "@albertsons/uds/molecule/Modal";
import Button from "@albertsons/uds/molecule/Button";
import FileUploadComponent from "./file-upload";
import { useState } from "react";
import {
  updateEventDetailsDataForAttachments,
  uploadFile,
} from "../../../service/allowance/offer-file-fetch-service";
import Spinner from "@albertsons/uds/molecule/Spinner";
import { useSelectorWrap } from "@me/data-rtk";
import { useDispatch } from "react-redux";
import {
  CANCEL_BUTTON_LABEL,
  CONFIRM_BUTTON_LABEL,
} from "../../../constants/constants";
import { offerCardConfiguration } from "../../../service/slice/allowance-details-slice";

interface FileItem {
  file: File;
  status: "inProgress" | "uploaded" | "failed" | "success" | "replace";
}

const OfferFileUploadModal = ({
  isFileUploadModalOpen,
  setIsFileUploadModalOpen,
  offerNumber,
  fileNames,
  cardIndex,
}) => {
  const { data: eventDetailsData } =
    useSelectorWrap("event_details_data") || {};
  const { editCardConfig = {}, openCardConfig = {} } =
    useSelectorWrap("offer_card_configutation_rn").data || {};
  const dispatch = useDispatch();
  const [filesUploadedData, setFilesUploadedData] = useState<FileItem[]>([]);
  const [isUploadPending, setIsUploadPending] = useState(false);
  const [disbaleConfirmButton, setDisableConfirmButton] = useState(true);
  const handleClose = () => {
    setTimeout(() => {
      dispatch(
        offerCardConfiguration({
          editCardConfig,
          openCardConfig: { ...openCardConfig, [cardIndex]: true },
        })
      );
    }, 100);
    setIsFileUploadModalOpen(false);
    setDisableConfirmButton(false);
  };
  const handleConfirm = async () => {
    if (!filesUploadedData?.length) {
      return;
    }
    try {
      setIsFileUploadModalOpen(false);
      setIsUploadPending(true);
      const response = await uploadFile(
        filesUploadedData[0].file,
        `/meupp/allowance/offer/${offerNumber}/blob/upload`
      );
      if (!response.ok) {
        throw new Error(`File upload failed with status: ${response.status}`);
      }
      updateEventDetailsDataForAttachments(
        dispatch,
        eventDetailsData,
        offerNumber,
        filesUploadedData[0].file.name,
        "UPLOAD"
      );
      setTimeout(() => {
        dispatch(
          offerCardConfiguration({
            editCardConfig,
            openCardConfig: { ...openCardConfig, [cardIndex]: true },
          })
        );
      }, 100);
      handleClose();
      setIsUploadPending(false);
    } catch (error) {
      setIsFileUploadModalOpen(false);
      console.error("File upload failed from modal:::", error);
      setIsUploadPending(false);
      handleClose();
    }
  };
  const handleFileDelete = async (fileName: string) => {
    updateEventDetailsDataForAttachments(
      dispatch,
      eventDetailsData,
      offerNumber,
      fileName,
      "DELETE"
    );
    setTimeout(() => {
      dispatch(
        offerCardConfiguration({
          editCardConfig,
          openCardConfig: { ...openCardConfig, [cardIndex]: true },
        })
      );
    }, 100);
  };

  return (
    <>
      <Modal
        isOpen={isFileUploadModalOpen}
        onClose={() => setIsFileUploadModalOpen(false)}
      >
        <div className="text-center select-none font-bold text-[28px] mt-6 mb-2">
          Offer Attachments
        </div>
        <hr />
        <div className="text-center select-none text-xl my-4">
          <FileUploadComponent
            handleConfirm={handleConfirm}
            setFilesUploadedData={setFilesUploadedData}
            fileNames={fileNames}
            offerNumber={offerNumber}
            setDisableConfirmButton={setDisableConfirmButton}
            handleFileDelete={handleFileDelete}
          />
        </div>
        <hr />
        <div className="flex items-center justify-center w-full my-8">
          <Button
            width={200}
            size="lg"
            className="mr-2 whitespace-nowrap"
            variant="secondary"
            onClick={handleClose}
          >
            {CANCEL_BUTTON_LABEL}
          </Button>
          <Button
            width={200}
            size="lg"
            className="ml-2 whitespace-nowrap"
            onClick={handleConfirm}
            disabled={disbaleConfirmButton}
          >
            {CONFIRM_BUTTON_LABEL}
          </Button>
        </div>
      </Modal>
      {isUploadPending ? (
        <span className="ml-2">
          <Spinner size="xs" variant="solid" />
        </span>
      ) : null}
    </>
  );
};

export default OfferFileUploadModal;
