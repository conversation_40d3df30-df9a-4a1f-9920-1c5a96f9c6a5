import { useSelectorWrap } from "@me/data-rtk";
import {
  ALLOWANCE_PREVIEW_DEFAULT_STEPS,
  ALLOWANCE_TO_BE_CREATED_CD_MAPPER,
  PREVIEW_WORKFLOW_RULES,
} from "../../../../constants/fields/allowance/allowance-steps-config";
import {
  getSteppers,
  getAllowanceFormRegisterKey,
  getAllowanceTypeByPerformance,
  getCreateIndByLocation,
} from "../../../../service/allowance/allowance-service";
import { Suspense, lazy, useState, useEffect, memo } from "react";
import { useFormContext } from "react-hook-form";
import efConstants from "../../../../../../shared/ef-constants/ef-constants";
import Divider from "@albertsons/uds/molecule/Divider";
import AllowanceUpdatesPreview from "./steppers/allowance-updates-preview";
import { appConstants } from "@me/utils-root-props";

const AllowancePreviewWrapper: React.FunctionComponent<any> = ({
  offerIndex,
  allowIndex,
}) => {
  const [previewSteppers, setPreviewSteppers] = useState<string[] | []>([]);
  const { ALLOWANCE_TYPE_NAME_MAPPER, ALLOWANCE_TYPES } = efConstants;
  const { CASE, SCAN, SHIPTOSTORE } = ALLOWANCE_TYPES;
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { data: allowanceAdditionalInfo } = useSelectorWrap(
    "allowance_stepper_additional_details"
  );
  const allowanceRegisterField = getAllowanceFormRegisterKey(
    offerIndex,
    allowIndex
  );
  const productInfoSource =
    allowanceAdditionalInfo?.productSources?.[allowanceRegisterField];
  const { getValues } = useFormContext();

  const performRegField = `${allowanceRegisterField}.performance`;
  const createInd = getCreateIndByLocation(
    getValues(`offerAllowances[${offerIndex}]`),
    allowIndex
  );
  const allowanceType = getAllowanceTypeByPerformance(
    getValues(performRegField)
  );

  useEffect(() => {
    let steppersShowData: string[] = getSteppers(
      eventDetailsData?.eventType,
      ALLOWANCE_TYPE_NAME_MAPPER[allowanceType],
      ALLOWANCE_TO_BE_CREATED_CD_MAPPER[createInd]?.value || "",
      productInfoSource,
      createInd
    );
    steppersShowData = [
      ...steppersShowData,
      ...ALLOWANCE_PREVIEW_DEFAULT_STEPS,
    ];
    setPreviewSteppers(steppersShowData);
  }, [
    eventDetailsData?.eventType,
    allowanceType,
    createInd,
    productInfoSource,
    ALLOWANCE_TYPE_NAME_MAPPER,
  ]);

  const { ALLOWANCE } = PREVIEW_WORKFLOW_RULES;

  const getVendorInformationSection = () => {
    if (
      [CASE.createInd[1], SCAN.createInd[1], SHIPTOSTORE.createInd[1]].includes(
        createInd
      )
    ) {
      const allowanceInfo = getValues(
        `offerAllowances[${offerIndex}].allowances[${allowIndex}]`
      );
      return (
        <>
          <div
            id="abs-allowance-preview-wrapper-vendor-info"
            className="flex justify-between w-full"
          >
            <div
              className="flex"
              id="abs-allowance-preview-wrapper-vendor-info-vendor"
            >
              <span
                id="abs-allowance-preview-wrapper-vendor-info-label"
                className="text-[#2B303C] mr-5 text-[15px] font-semibold select-none"
              >
                Vendor Information
              </span>
              <Divider className="mx-3" height={24} color="#C8DAEB" />
              <span
                id="abs-allowance-preview-wrapper-vendor-info-details"
                className="text-[#5A697B] mr-5 text-[14px]  select-none"
              >
                {`${allowanceInfo?.vendorName || ""} ${
                  allowanceInfo?.vendorName ? "-" : ""
                } ${allowanceInfo?.vendorNbr}${
                  allowanceInfo?.costAreaDesc ? "-" : ""
                } ${allowanceInfo?.costAreaDesc || ""}`}
              </span>
            </div>
          </div>
          <div
            id="abs-allowance-preview-wrapper-vendor-info-divider-line"
            className="flex flex-grow-0 flex-shrink-0 justify-center w-full h-px  bg-[#fdfdff] border-t border-r border-b-0 border-l border-[#c8daeb] my-3"
          ></div>
        </>
      );
    }
    return null;
  };

  return (
    <div id="abs-allowance-preview-wrapper" className="flex flex-col">
      {getVendorInformationSection()}
      {previewSteppers?.map((allowStepper, i) => {
        if (!ALLOWANCE[allowStepper]) {
          return null;
        }
        const configObj: any = ALLOWANCE[allowStepper];
        const previewTitle = appConstants.IS_ENABLE_REDESIGN_ALLOWANCE_CHANGES
          ? configObj?.redesignPreviewTitle
          : allowStepper;
        const Component = lazy(
          () => import(`./steppers/${configObj?.previewCard}-preview`)
        );
        return (
          <Suspense fallback={null} key={allowStepper}>
            <Component
              previewConfigObj={configObj}
              allowStepperType={previewTitle}
              stepperIndex={i}
              steppersData={previewSteppers}
              offerIndex={offerIndex}
              allowIndex={allowIndex}
              childClassName={`ml-3 ${
                i + 1 !== previewSteppers.length ? "mb-5" : ""
              }`}
            />
          </Suspense>
        );
      })}
      {/* to add the agreed Info */}
      <AllowanceUpdatesPreview
        offerIndex={offerIndex}
        allowIndex={allowIndex}
      />
    </div>
  );
};

export default memo(AllowancePreviewWrapper);
