import {
  checkForZeroCost,
  getAllowAmtLabel,
  getAllowAmtLabelAcrossDiv,
  getQueryParams,
  getUOMFromNameOrValue,
  handleZeroCostVendors,
} from "../../../../../service/allowance/allowance-service";
import { useGetAmountDerivedValues } from "./useGetAmountDerivedValues";
import { useGetSelectors } from "./useGetSelectors";
import { usePutOfferAllowanceMutation } from "../../../../../service/apis/allowance-api";
import { useAllowanceService } from "./useAllowanceAmountService";
import useOfferAmountApi from "./useOfferAmountApi";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";
import { useEffect } from "react";
import useAllowTempworkUpdate from "../../../../../hooks/useAllowTempworkUpdate";
import useGetNationalOfferApi from "../../national/hooks/useGetNationalOfferApi";
import { getIsNationalEvent } from "../../../../../../event-types/event-types-helper";

export const useBaseAmountConfig = ({
  sectionKey,
  cardIndex,
  cardItemIndex,
  isEditEnable,
  allowanceAmountData,
  setAllowanceAmountData,
  isFormValid,
  formControls,
}) => {
  const { taskType } = getQueryParams();
  // Configuration and data hooks
  const {
    allowanceTempWorkData,
    productSources,
    allowanceForm,
    eventDetailsData,
    isAllowConvEnable,
    offerSectionsEnableConfig,
  } = useGetSelectors();
  const { isNDPAndFlagEnabled: isNational } = getIsNationalEvent(eventDetailsData?.eventType);
  const {
    sectionConfiguration,
    mapperKey,
    allowanceTypeKey,
    isHfOrIf,
    isHfIfWhseCase,
    tempAllowItems,
    offerNumber,
    allowanceRegField,
    allowanceFormData,
    amountsInitialValueOnLoad,
    allowName,
  } = useGetAmountDerivedValues({
    sectionKey,
    allowanceForm,
    allowanceTempWorkData,
    cardIndex,
    cardItemIndex,
    isEditEnable,
    isNational
  });
  const isAmtSavedInTemp = tempAllowItems?.[0]?.finalizedAmountsInd;
  const isAmtSectionPassed =
    !offerSectionsEnableConfig?.[sectionKey]?.scrollTo && isAmtSavedInTemp;
  const isHf = allowanceTypeKey === efConstants.ALLOWANCE_TYPES.HEADERFLAT.key;

  useEffect(() => {
    // Reset the form values when finalized flag is false and create mode
    if (!isAmtSavedInTemp && !isEditEnable) {
      const defaultUom = tempAllowItems?.[0]?.allowanceItems?.[0]?.allowUomType;
      const toSetUom = getUOMFromNameOrValue({
        value: defaultUom || "",
      })?.name;
      setAllowanceAmountData({
        ...allowanceAmountData,
        allowanceAmount: "",
        finalAmountVal: 0,
      });
      formControls?.reset?.({
        allowanceAmount: "",
        ...(!isHfOrIf && { uom: toSetUom }),
      });
    }
  }, [isAmtSavedInTemp, isEditEnable, isHfOrIf]);

  // API hooks
  const { saveAllowanceAmountSectionData, isLoading: isSaveAmountsLoading } =
    useAllowTempworkUpdate();
  const useApiHook = isNational ? useGetNationalOfferApi : useOfferAmountApi;
  const { allAllowancesPath, allowancesResp, isFetching } = useApiHook(
    cardIndex,
    isEditEnable,
    mapperKey,
    productSources,
    isAmtSectionPassed,
    isNational
  );
  const [putOfferAllowance] = usePutOfferAllowanceMutation();

  // Services
  const {
    processAllowanceData,
    saveAllowanceDataInForm,
    handlePostSave,
    editViewAllItemsClick,
  } = useAllowanceService(sectionKey, allowanceRegField);

  const handleSave = async formValues => {
    if (isNational) return;
    const { reset } = formControls || {};
    const { allowanceAmount } = allowanceAmountData;
    if (!isFormValid || isNaN(allowanceAmount) || Number(allowanceAmount) <= 0)
      return;
    const isInValidOffer = eventDetailsData?.inValidAllowances?.includes(
      eventDetailsData?.offerAllowances?.[cardIndex]?.id
    );
    const processedData = processAllowanceData({
      allowanceAmountData,
      allowancesResp,
      isEditEnable,
      isAllowConvEnable,
      allowanceTypeKey,
      tempAllowItems,
    });
    const result = await saveAllowanceAmountSectionData({
      allowances: processedData?.allowances,
      allowName,
      mapperKey,
    });
    if (result?.data) {
      const updatedFormValues = saveAllowanceDataInForm({
        allowanceAmountData,
        allowanceTypeKey,
        mapperKey,
        allowanceRegField,
        allowanceFormData,
        allowances: processedData?.allowances,
        isEditEnable,
      });

      await handlePostSave({
        isEditEnable,
        eventDetailsData,
        allowanceTempWorkData,
        putOfferAllowance,
        taskType,
        offerNumber,
        mapperKey,
        updatedFormValues,
        isInValidOffer
      });
      reset?.(formValues);
    }
  };
  const editViewAllItems = () => editViewAllItemsClick(allAllowancesPath);
  const isDisplaySubLabel = !isEditEnable && !isAmtSavedInTemp ? false : true;
  const subLabelAmtForMainEntryCase = isNational
    ? isDisplaySubLabel
      ? getAllowAmtLabelAcrossDiv({
          allowanceType: allowanceTypeKey,
          isHeaderFlat: isHf,
          isCreate: true,
          allDivAllowResp: Array.isArray(allowancesResp) ? allowancesResp : [],
        })
      : ""
    : allowancesResp?.summary?.itemAmountsCouldBeSummarized === false &&
      isDisplaySubLabel
    ? getAllowAmtLabel(
        allowanceTypeKey,
        allowancesResp?.allowances || [],
        isHf,
        true
      )
    : "";
  return {
    isFetching: isFetching || isSaveAmountsLoading,
    sectionConfiguration,
    allowanceTypeKey,
    isHfOrIf,
    isHfIfWhseCase,
    isZeroCost: isNational
      ? checkForZeroCost(allowancesResp)
      : handleZeroCostVendors(allowancesResp?.allowances || []),
    allowancesResp,
    handleSave,
    editViewAllItems,
    amountsInitialValueOnLoad,
    isSectionCompleted: isAmtSectionPassed,
    amtSubLabelDisplayVal: subLabelAmtForMainEntryCase || "",
    isAmtSavedInTemp,
  };
};
