import { Upload, Copy, Download } from "lucide-react";
import { useState } from "react";
import OfferFileUploadModal from "./offer-file-upload-modal";
import { downloadFile } from "../../../service/allowance/offer-file-fetch-service";

import Spinner from "@albertsons/uds/molecule/Spinner";
import {
  OFFER_UPLOAD_API_LABEL_ONE,
  OFFER_UPLOAD_API_LABEL_TWO,
} from "../../../constants/constants";

const OfferFileUploadContainer = ({
  label,
  isEdit,
  isEditDisabled,
  offerNumber,
  fileNames,
  cardIndex,
}) => {
  const [isFileUploadModalOpen, setIsFileUploadModalOpen] = useState(false);
  const [isDownloadPending, setIsDownloadPending] = useState(false);
  const handleFileUpload = e => {
    e?.stopPropagation();
    setIsFileUploadModalOpen(true);
  };
  const handleDownload = async e => {
    setIsDownloadPending(true);
    e?.stopPropagation();
    const url = `${OFFER_UPLOAD_API_LABEL_ONE}${offerNumber}${OFFER_UPLOAD_API_LABEL_TWO}${fileNames[0]}`;
    await downloadFile(url, fileNames[0]);
    setIsDownloadPending(false);
  };

  return !isEdit && offerNumber ? (
    <>
      {fileNames?.length ? (
        <div className="flex ml-auto cursor-pointer">
          <span
            className={`mr-2 font-semibold text-[#1B6EBB] ${
              isEdit ? "opacity-40 pointer-events-none" : ""
            } ${isEditDisabled ? "opacity-40 pointer-events-none" : ""}`}
            onClick={e => {
              handleFileUpload(e);
            }}
          >
            <span>
              <Copy
                width={16}
                height={16}
                color="#1B6EBB"
                className="inline-block mb-[3px] mr-[8px]"
              />
            </span>
            <span className="pr-2">Manage</span>
          </span>
          <span
            className="pl-2 border-l border-l-[#1B6EBB]"
            onClick={e => {
              handleDownload(e);
            }}
          >
            <Download
              width={16}
              height={16}
              color="#1B6EBB"
              className="inline-block mb-[3px]"
            />
          </span>
        </div>
      ) : (
        <div className="flex ml-auto">
          <span
            className={`mr-2 font-semibold text-[#1B6EBB] ${
              isEdit ? "opacity-40 pointer-events-none" : ""
            } ${isEditDisabled ? "opacity-40 pointer-events-none" : ""}`}
            onClick={e => {
              handleFileUpload(e);
            }}
          >
            <Upload
              width={16}
              height={16}
              color="#1B6EBB"
              className="inline-block mb-[3px] mr-[8px]"
            />
            <span>{label}</span>
          </span>
        </div>
      )}
      <OfferFileUploadModal
        isFileUploadModalOpen={isFileUploadModalOpen}
        setIsFileUploadModalOpen={setIsFileUploadModalOpen}
        offerNumber={offerNumber}
        fileNames={fileNames}
        cardIndex={cardIndex}
      />
      {isDownloadPending ? (
        <span className="ml-2">
          <Spinner size="xs" variant="solid" />
        </span>
      ) : null}
    </>
  ) : null;
};

export default OfferFileUploadContainer;
