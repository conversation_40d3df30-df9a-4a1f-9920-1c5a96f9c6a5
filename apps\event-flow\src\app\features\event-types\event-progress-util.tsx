const event = {
  sectionName: "Event Details",
  fillName: "Event Details",
  selectedSection: "Allowance",
  sectionCheck: true,
  invalidSection: false,
  index: 0,
};
const allowance = {
  sectionName: "Allowance",
  fillName: "Allowance",
  selectedSection: "Promotion",
  sectionCheck: true,
  invalidSection: false,
  index: 1,
};
const promotion = {
  sectionName: "Promotion",
  fillName: "Promotion",
  selectedSection: "",
  sectionCheck: true,
  invalidSection: false,
  index: 2,
};

export const eventProgressCheck = (data, config) => {
  const eventConfigName = config;

  const eventProgressSectionData = {
    division_promotion: [
      { ...event, sectionCheck: data.id },
      {
        ...allowance,
        sectionCheck: data?.offerAllowances?.length > 0,
        invalidSection: data?.inValidAllowances?.length > 0,
      },
      {
        ...promotion,
        sectionCheck: data?.promotionsList?.length > 0,
        invalidSection: data?.inValidPromotions?.length > 0,
      },
    ],

    national_division_promotion: [
      { ...event, sectionCheck: data.id },
      {
        ...allowance,
        sectionCheck: data?.offerAllowances?.length > 0,
        invalidSection: data?.inValidAllowances?.length > 0,
      },
      {
        ...promotion,
        sectionCheck: data?.promotionsList?.length > 0,
        invalidSection: data?.inValidPromotions?.length > 0,
      },
    ],

    allowance_only: [
      { ...event, sectionCheck: data.id },
      {
        ...allowance,
        sectionCheck: data?.offerAllowances?.length > 0,
        index: 1,
        invalidSection: data?.inValidAllowances?.length > 0,
      },
    ],

    national_division_allowance_only: [
      { ...event, sectionCheck: data.id },
      {
        ...allowance,
        sectionCheck: data?.offerAllowances?.length > 0,
        index: 1,
        invalidSection: data?.inValidAllowances?.length > 0,
      },
    ],

    promotion_only: [
      { ...event },
      {
        ...promotion,
        sectionCheck: data?.promotionsList?.length > 0,
        index: 1,
        invalidSection: data?.inValidPromotions?.length > 0,
      },
    ],
  };

  return eventProgressSectionData?.[eventConfigName];
};
