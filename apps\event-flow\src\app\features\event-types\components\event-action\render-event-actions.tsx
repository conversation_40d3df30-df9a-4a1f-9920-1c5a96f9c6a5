import { Fragment, useCallback, useEffect, useRef, useState } from "react";
import { RBAC } from "albertsons-react-rbac";
import { Button } from "@albertsons/uds/molecule/Button";
import RequestForChangeModal from "./request-for-change-modal";
import { usePostEventCommentsDataMutation } from "../../event-comments-section-service";
import { EEVENT_STATUS, EUSER_ROLES } from "./event-action.model";
import {
  CommentCategory,
  IPlanEventCommentDTO,
} from "../../interfaces/plan-event-comment-interface";
import { useGetCommentsDataQuery } from "../../../create-event/service/apis/event-api";
import { CTAs_Keys } from "../../../create-event/constants/event-status/contsants";
import { usePostEventComments } from "./useEventComments";
import { ChevronDown } from "lucide-react";
import { useClickOutsideHook } from "@me/util-helpers";
import { EVENT_TYPE } from "../../../create-event/constants/constants";

const RenderEventActions = (
  eventActionService,
  eventStatusData,
  eventStatus,
  userRole,
  planEvent,
  shouldRenderAction,
  divisionIds,
  doDisableCtsBtn,
  checkForHiddenPriceOrROG,
  handleChangeStatus,
  planEventHistory,
  isApiLoading,
  negotiationSimsVendors,
  eventId,
  eventType
) => {
  const [disableCtsBtn, setDisableCtsBtn] = useState(true);
  const [isRequestForChangePopupOpen, setIsRequestForChangePopupOpen] =
    useState(false);
  const [commentValue, setCommentValue] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [manuallTrigger, setManualTrigger] = useState(false);

  const dropdownRef = useRef<HTMLDivElement>(null);
  const { isClickOutside } = useClickOutsideHook(dropdownRef);

  const { postComment } = usePostEventComments(
    eventId,
    userRole,
    eventStatus,
    CommentCategory?.EXTERNAL,
    false,
    EEVENT_STATUS.AGREED_PENDING,
    {},
    true
  );

  const handleChange = useCallback(
    (event, key) => {
      if (
        userRole === EUSER_ROLES?.VENDOR &&
        [
          EEVENT_STATUS?.AGREED,
          EEVENT_STATUS?.AGREED_PENDING,
          EEVENT_STATUS.ACTIVE,
          EEVENT_STATUS.EXECUTED,
        ].includes(eventStatus) &&
        key === CTAs_Keys?.SEND_TO_MERCHANT
      ) {
        setIsRequestForChangePopupOpen(true);
      } else {
        handleEventStatusAction(event, key);
        setIsRequestForChangePopupOpen(false);
      }
      setManualTrigger(false);
      setIsDropdownOpen(false);
    },
    [eventStatus, userRole]
  );

  useEffect(() => {
    setTimeout(() => {
      setDisableCtsBtn(false);
    }, 500);
  }, []);

  useEffect(() => {
    if (isClickOutside) {
      setIsDropdownOpen(false);
      setManualTrigger(true);
    }
  }, [isClickOutside]);

  const handleEventStatusAction = (event, key) => {
    setDisableCtsBtn(true);
    handleChangeStatus(event, key);
    setTimeout(() => {
      setDisableCtsBtn(false);
    }, 500);
  };

  const confrimBtnHandler = async (e, comment) => {
    setCommentValue(comment);
    setIsRequestForChangePopupOpen(false);
    const a = await postComment(comment);

    handleEventStatusAction(e, CTAs_Keys?.SEND_TO_MERCHANT);
  };
  useEffect(() => {
    setDisableCtsBtn(isApiLoading);
  }, [isApiLoading]);

  const handleDropdownClick = () => {
    setIsDropdownOpen(!isDropdownOpen);
    setManualTrigger(true);
  };
  const handleHover = () => {
    setIsDropdownOpen(true);
    setManualTrigger(true);
  };
  useEffect(() => {
    const handleClickOutside = event => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
        setManualTrigger(true);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    setManualTrigger(false);
  }, [handleChange]);

  useEffect(() => {
    if (!manuallTrigger) {
      const buttonsData =
        eventStatusData?.[eventStatus]?.USERS?.[userRole]?.buttons;
      const ActiveValue = buttonsData?.some(
        ({ key, disabled }) =>
          (key === "SEND_TO_VENDOR" || key === "SEND_TO_MERCHANT") && disabled
      );
      if (
        checkForHiddenPriceOrROG("SEND_TO_VENDOR") ||
        checkForHiddenPriceOrROG("SEND_TO_MERCHANT") ||
        ActiveValue
      ) {
        setIsDropdownOpen(false);
      } else {
        setIsDropdownOpen(true);
      }
    }
  }, [
    checkForHiddenPriceOrROG,
    eventStatus,
    eventStatusData,
    manuallTrigger,
    userRole,
    handleChange,
  ]);

  useEffect(() => {
    setManualTrigger(false);
  }, [
    checkForHiddenPriceOrROG("SEND_TO_VENDOR"),
    checkForHiddenPriceOrROG("SEND_TO_MERCHANT"),
  ]);

  return (
    <div
      className="relative inline-block text-left pr-6"
      ref={dropdownRef}
      onMouseEnter={handleHover}
    >
      <button
        type="button"
        id="abs-dropdown-btn"
        className="bg-[#1B6EBB] !bg-[#1B6EBB] text-white px-4 py-2 rounded-md shadow-md flex items-center disabled:bg-gray-400 disabled:cursor-not-allowed"
        onClick={handleDropdownClick}
        disabled={disableCtsBtn}
      >
        {"Actions"}
        <ChevronDown
          width={24}
          height={24}
          className="inline-block text-white pl-2"
        />
      </button>
      {isDropdownOpen && (
        <div
          id="abs-dropdown-btn"
          className="absolute right-[20px] mt-[2px] min-w-max bg-white border border-gray-300 rounded-lg shadow-lg z-10"
        >
          <ul className="py-2 px-2 w-full mr-[6em]">
            {eventStatusData?.[eventStatus]?.USERS?.[userRole]?.buttons?.map(
              ({ label, variant, disabled, key, validate }) => {
                const ctaKey = key.split("_")[0];
                const validButton = eventActionService.checkIsValid(
                  validate,
                  label,
                  planEvent,
                  userRole,
                  planEventHistory,
                  eventStatus
                );
                return (
                  shouldRenderAction(label) && (
                    <li key={key}>
                      <RBAC
                        divisionIds={divisionIds}
                        permissionsOnly={[
                          `PROMOTION_EVENT_MGMT_EDIT_PROGRESS_${ctaKey}`,
                        ]}
                        key={key}
                        simsVendors={negotiationSimsVendors}
                      >
                        {validButton ? (
                          <button
                            //variant={variant}
                            key={key}
                            className={`block w-full font-bold text-left px-4 py-3 text-[#1B6EBB] hover:bg-blue-100 rounded-lg disabled:text-gray-400 disabled:cursor-not-allowed disabled:hover:bg-transparent`}
                            disabled={
                              disableCtsBtn ||
                              disabled ||
                              checkForHiddenPriceOrROG(key)
                            }
                            onClick={e => handleChange(e, key)}
                          >
                            {label}
                          </button>
                        ) : null}
                      </RBAC>
                    </li>
                  )
                );
              }
            )}
          </ul>
        </div>
      )}
      {/* Render the modal outside the loop */}
      {isRequestForChangePopupOpen && (
        <RequestForChangeModal
          isRequestForChangePopupOpen={isRequestForChangePopupOpen}
          setIsRequestForChangePopupOpen={setIsRequestForChangePopupOpen}
          confrimBtnHandler={confrimBtnHandler}
        />
      )}
    </div>
  );
};

export default RenderEventActions;
