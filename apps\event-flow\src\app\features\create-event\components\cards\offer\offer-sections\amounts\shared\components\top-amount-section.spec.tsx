import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import TopAmountSection from './top-amount-section';

describe('TopAmountSection Component', () => {
  test('renders the description correctly', () => {
    const descriptionText = "This is a test description";
    const baseId = "testBaseId";

    render(<TopAmountSection description={descriptionText} baseId={baseId} />);

    // Verify that the description is rendered with the correct text
    const descriptionElement = screen.getByText(descriptionText);
    expect(descriptionElement).toBeInTheDocument();
  });

  test('renders children when provided', () => {
    const descriptionText = "Another test description";
    const baseId = "testBaseId";
    const childElementText = "Child Component";

    render(
      <TopAmountSection description={descriptionText} baseId={baseId}>
        <div data-testid="child-element">{childElementText}</div>
      </TopAmountSection>
    );

    // Verify that the child element is rendered
    const childElement = screen.getByTestId('child-element');
    expect(childElement).toBeInTheDocument();
    expect(childElement).toHaveTextContent(childElementText);
  });

  test('has the correct baseId applied to the description', () => {
    const descriptionText = "Description with baseId";
    const baseId = "customBase";

    render(<TopAmountSection description={descriptionText} baseId={baseId} />);

    // Verify that the id is applied correctly
    const descriptionElement = screen.getByText(descriptionText);
    expect(descriptionElement.closest('p')).toHaveAttribute('id', `${baseId}-description`);
  });
});
