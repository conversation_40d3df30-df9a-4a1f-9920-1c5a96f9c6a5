import React from "react";
import { render, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { useDispatch } from "react-redux";
import { useSelectorWrap } from "@me/data-rtk";
import { CHILD_EVENTS_TABS, getSelectedTab } from "../child-events-config";
import { setChildEventTabConfigData } from "../slices/child-events-data-slice";
import ChildEventsTabsSection from "./child-grid-tabs";
import {
  useLazyGetChildEventDetailsQuery,
  usePostPlanItemsMutation,
} from "../../../../create-event/service/apis/event-api";

jest.mock("react-redux", () => ({
  useDispatch: jest.fn(),
}));
jest.mock("react-pdf", () => ({
  Document: ({ children }) => <div>{children}</div>,
  Page: () => <div>Mocked Page</div>,
  pdfjs: {
    GlobalWorkerOptions: { workerSrc: "" },
  },
}));
jest.mock("../../../../create-event/service/apis/event-api", () => ({
  useLazyGetChildEventDetailsQuery: () => [jest.fn()],
  usePostPlanItemsMutation: () => [jest.fn()],
}));

jest.mock("@me/data-rtk", () => ({
  useSelectorWrap: jest.fn(),
  injectEndPointsWrapper: jest.fn(() => ({
    useGetEventDataQuery: jest.fn(() => Promise.resolve({ data: [] })),
    usePostEventDataMutation: jest.fn(() => Promise.resolve({})),
  })),
  createGenericSlice: jest.fn(() => () => ({
    actions: {
      eventDetailsDataHandler: jest.fn(),
      resetEventDetailsHandler: jest.fn(),
    },
  })),
}));

jest.mock("../child-events-config", () => ({
  CHILD_EVENTS_TABS: [
    { key: "tab1", label: "Tab 1", isDisabled: false },
    { key: "tab2", label: "Tab 2", isDisabled: false },
  ],
  getSelectedTab: jest.fn(),
}));

jest.mock("../slices/child-events-data-slice", () => ({
  setChildEventTabConfigData: jest.fn(),
}));

jest.mock("../child-grid-wrapper/child-grid-wrapper", () =>
  jest.fn(() => <div data-testid="child-events-grid-wrapper" />)
);

describe("ChildEventsTabsSection", () => {
  const mockDispatch = jest.fn();
  const mockSelectedTab = { tabIndex: 0, id: "tab1" };

  beforeEach(() => {
    (useDispatch as jest.Mock).mockReturnValue(mockDispatch);
    (useSelectorWrap as jest.Mock).mockReturnValue({ data: mockSelectedTab });
    (getSelectedTab as jest.Mock).mockReturnValue(mockSelectedTab);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders the component with the correct title", () => {
    const { getByText } = render(<ChildEventsTabsSection />);
    expect(
      getByText("Tailored Customization for Divisions")
    ).toBeInTheDocument();
  });

  it("renders the TabBar with the correct tabs", () => {
    const { getByText } = render(<ChildEventsTabsSection />);
    expect(getByText("Tab 1")).toBeInTheDocument();
    expect(getByText("Tab 2")).toBeInTheDocument();
  });

  it("dispatches setChildEventTabConfigData on tab change", () => {
    const { getByText } = render(<ChildEventsTabsSection />);
    fireEvent.click(getByText("Tab 2"));
    expect(mockDispatch).toHaveBeenCalledWith(
      setChildEventTabConfigData(mockSelectedTab)
    );
  });

  it("does not render ChildEventsGridWrapper when selectedTab.tabIndex is not 0", () => {
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: { tabIndex: 1, id: "tab2" },
    });
    const { queryByTestId } = render(<ChildEventsTabsSection />);
    expect(queryByTestId("child-events-grid-wrapper")).not.toBeInTheDocument();
  });
});
