import { useFormContext } from "react-hook-form";
import {
  getAllowanceTypeByPerformance,
  getCreateIndByLocation,
} from "../../../../../service/allowance/allowance-service";
// eslint-disable-next-line @nrwl/nx/enforce-module-boundaries
import StepperPreviewContainer from "libs/preview-card/src/lib/preview-card-container/preview-card-container";
import {
  getConstRegKey,
  getHeaderStepperValue,
  getSearchId,
  showEditedLabel,
} from "@me/util-helpers";
// eslint-disable-next-line @nrwl/nx/enforce-module-boundaries
import PreviewCardFields from "libs/preview-card/src/lib/preview-card-fields/preview-card-fields";
import { ALLOWANCE_TO_BE_CREATED_CD_MAPPER } from "../../../../../constants/fields/allowance/allowance-steps-config";
import { checkIsValidDate } from "../../stepper/common-stepper/allowance-dates/allowance-dates-service";
import { getObjectKeys } from "../../../../../service/allowance/allowance-stepper-service";
import { arraivalDateMapper } from "../../../../../service/event-details/event-detail-service";

const AllowanceDatesPreview: React.FunctionComponent<any> = ({
  previewConfigObj,
  allowStepperType,
  stepperIndex,
  steppersData,
  offerIndex,
  allowIndex,
  childClassName,
}) => {
  const { getValues } = useFormContext();
  const { fieldsHistoryKeys = null } = previewConfigObj;
  const allowanceRegisterField = getConstRegKey(
    "allowance",
    offerIndex,
    allowIndex,
    getValues
  );
  const offerAllowance = getValues("offerAllowances");
  const isValidArrivalDate = checkIsValidDate(
    getValues(allowanceRegisterField)?.arrivalStartDate
  );
  const scanRowFieldMap = {
    "Allowance Start Date": {
      historyMapperKey: "offerallowances.allowances.allowanceStartDate",
      key: "allowanceStartDate",
    },
    "Allowance End Date": {
      historyMapperKey: "offerallowances.allowances.allowanceEndDate",
      key: "allowanceEndDate",
    },
  };
  const scanRowFieldKeys = getObjectKeys(scanRowFieldMap);

  function getDatesFieldsForEventType() {
    const performanceValue =
      offerAllowance[offerIndex]?.allowances[allowIndex]?.performance;
    const allowanceType =
      getAllowanceTypeByPerformance(performanceValue).toUpperCase();
    const allowCreateInd = getCreateIndByLocation(
      getValues(`offerAllowances[${offerIndex}]`),
      allowIndex
    );
    const key = ALLOWANCE_TO_BE_CREATED_CD_MAPPER[allowCreateInd]?.key || "";

    const basicFields = [
      "Vehicle Type/Custom Date",
      "Vehicle Start",
      "Year",
      "Vehicle End",
      "Start Week/Vehicle",
    ];

    const whseRowFields = [
      ...basicFields,
      "Order Start",
      "Order End",
      "Arrival Start",
      "Arrival End",
    ];

    const dsdRowFields = [...basicFields, "Arrival Start", "Arrival End"];
    const scanRowFields = [...basicFields, ...scanRowFieldKeys];

    const fieldsMap = {
      SCAN: {
        DSD_WHSE_RETAIL_DIVISION: scanRowFields,
        DSD_LEAD_DISTRIBUTORS: scanRowFields,
      },
      CASE: {
        DSD_LEAD_DISTRIBUTORS: dsdRowFields,
        WAREHOUSE_DIST_CENTERS: whseRowFields,
        DSD_WHSE_RETAIL_DIVISION: dsdRowFields,
      },
      "SHIP TO STORE": {
        DSD_WHSE_RETAIL_DIVISION: dsdRowFields,
        DSD_LEAD_DISTRIBUTORS: dsdRowFields,
      },
      "HEADER FLAT": {
        DSD_WHSE_RETAIL_DIVISION: basicFields,
        WAREHOUSE_DIST_CENTERS: basicFields,
      },
      "ITEM FLAT": {
        DSD_WHSE_RETAIL_DIVISION: basicFields,
        WAREHOUSE_DIST_CENTERS: basicFields,
      },
    };

    return fieldsMap?.[allowanceType]?.[key] || basicFields;
  }

  const fieldNames: string[] = getDatesFieldsForEventType();

  const updatedPreviewConfigObj = {
    ...previewConfigObj,
    fields: previewConfigObj?.fields
      ?.filter(field => fieldNames.includes(field?.label))
      ?.map(field =>
        !isValidArrivalDate && scanRowFieldKeys.includes(field?.label)
          ? {
              ...field,
              key: scanRowFieldMap[field?.label]?.key,
              historyMapperKey: scanRowFieldMap[field?.label]?.historyMapperKey,
            }
          : field
      ),
  };

  const fieldsMapper = {
    // TODO: verify these values
    vehicleTypeOrCustomDate: `${allowanceRegisterField}.vehicle.vehicleType.vehicleTypDesc`,
    vehicleStart: `${allowanceRegisterField}.allowanceStartDate`,
    year: `${allowanceRegisterField}.allowanceStartDate`, // applied getYearOnly() here
    vehicleEnd: `${allowanceRegisterField}.allowanceEndDate`,
    startWeekOrVehicle: `${allowanceRegisterField}.vehicle.vehicleNm`,
    // for dsd
    orderStart: `${allowanceRegisterField}.orderStartDate`,
    orderEnd: `${allowanceRegisterField}.orderEndDate`,
    arrivalStart: `${allowanceRegisterField}.arrivalStartDate`,
    arrivalEnd: `${allowanceRegisterField}.arrivalEndDate`,
    // for whse
    storeReceivingStart: `${allowanceRegisterField}.arrivalStartDate`,
    storeReceivingEnd: `${allowanceRegisterField}.arrivalEndDate`,
    baseKey: `${allowanceRegisterField}`,
    //for scan
    allowanceStartDate: `${allowanceRegisterField}.${
      isValidArrivalDate ? "arrivalStartDate" : "allowanceStartDate"
    }`,
    allowanceEndDate: `${allowanceRegisterField}.${
      isValidArrivalDate ? "arrivalEndDate" : "allowanceEndDate"
    }`,
  };

  const headerFieldsMapper = {
    vehicleStart: `${allowanceRegisterField}.allowanceStartDate`,
    vehicleEnd: `${allowanceRegisterField}.allowanceEndDate`,
    baseKey: `${allowanceRegisterField}`,
  };

  const getDateByKey = (formObj, searchId, dateKey: string) => {
    return formObj?.offerAllowances?.filter(
      offer => offer?.offerNumber === searchId
    )?.[0]?.allowances?.[0]?.[dateKey];
  };

  const isSameDatePlanAndPending = fieldObj => {
    const { key } = fieldObj || {};
    if (!arraivalDateMapper?.[key]) return false;
    const { planEvent, planEventPending } = getValues();
    const searchId = getSearchId(
      "allowance",
      offerAllowance,
      [],
      offerIndex,
      allowIndex
    );
    const dateKey =
      arraivalDateMapper?.[key] === "allowanceStartDate"
        ? "arrivalStartDate"
        : "arrivalEndDate";

    const arravalDateOldVal = getDateByKey(planEvent, searchId, dateKey);
    const arravalDateNewVal = getDateByKey(planEventPending, searchId, dateKey);

    if (
      !checkIsValidDate(arravalDateOldVal) ||
      !checkIsValidDate(arravalDateNewVal)
    ) {
      const prevValue = getDateByKey(
        planEvent,
        searchId,
        arraivalDateMapper?.[key]
      );
      const newValue = getDateByKey(
        planEventPending,
        searchId,
        arraivalDateMapper?.[key]
      );

      const oldValue = checkIsValidDate(arravalDateOldVal)
        ? arravalDateOldVal
        : prevValue;

      const currentValue = checkIsValidDate(arravalDateNewVal)
        ? arravalDateNewVal
        : newValue;

      return oldValue === currentValue;
    }
    return false;
  };

  const getPreviewFields = (
    <PreviewCardFields
      previewConfigObj={updatedPreviewConfigObj}
      fieldsMapper={fieldsMapper}
      module="allowance"
      getValues={getValues}
      cardIndex={offerIndex}
      cardItemIndex={allowIndex}
      getCustomPlanValue={null}
      className={childClassName}
      isHideStrikeOffCustom={isSameDatePlanAndPending}
    />
  );
  const getStepperHeaderValueFromUtils = () => {
    return null;
    // const startDateFieldObj =
    //   previewConfigObj?.stepperHeaderFields?.["vehicleStart"];
    // const endDateFieldObj =
    //   previewConfigObj?.stepperHeaderFields?.["vehicleEnd"];
    // const { vehicleStart, vehicleEnd } = getHeaderStepperValue(
    //   [startDateFieldObj, endDateFieldObj],
    //   getValues,
    //   "allowance",
    //   headerFieldsMapper,
    //   offerIndex,
    //   allowIndex,
    //   null
    // );
    // return `${vehicleStart} - ${vehicleEnd}`;
  };

  const isShowEdited = showEditedLabel(
    fieldsHistoryKeys,
    getValues,
    offerIndex,
    allowIndex,
    "allowance"
  );

  return (
    <StepperPreviewContainer
      stepperPreviewHeaderLabel={allowStepperType}
      stepperPreviewHeaderValue={getStepperHeaderValueFromUtils()}
      steppersData={steppersData}
      stepperIndex={stepperIndex}
      isShowEdited={isShowEdited}
    >
      {getPreviewFields}
    </StepperPreviewContainer>
  );
};

export default AllowanceDatesPreview;
