import { useSelectorWrap } from "@me/data-rtk";
import { cloneDeep } from "lodash";
import { useEffect, memo, useRef } from "react";
import { useDispatch } from "react-redux";
import { useLocation, useParams } from "react-router-dom";
import { useGetPlanEventDataQuery } from "../create-event/service/apis/event-api";
import {
  eventDetailsDataHandler,
  resetEventDetailsHandler,
  setPlanEventIndicators,
  setStoreGroupDivisions,
  setVendorNDDivisions,
} from "../create-event/service/slice/event-detail-slice";
import { eventProgressDataHandler } from "../create-event/service/slice/event-progress-slice";
import EventFormProvider from "./event-form-provider";
import {
  allowanceOnlyEventTypeConfiguration,
  offerResendStatus,
  setOfferDivisionsData,
} from "../create-event/service/slice/allowance-details-slice";
import { eventTypes } from "./constants/event-type-constant";
import { getEventDetails } from "./event-hook-form-wrapper-service";
import {
  checkIsEventDatesChanged,
  checkIsEventUpdatedInHistory,
  EUSER_ROLES,
  getInvalidAllowanceAndPromotions,
  GetInvalidAllowanceKeys,
  GetInvalidPromoKeys,
} from "@me/util-helpers";
import { getLoggedInUserType } from "@me-upp-js/utilities";
import { resetForecastHandler } from "../create-event/service/slice/promotion-details-slice";
import { setIsEventVehicleDatesChanged } from "./slices/event-types-slice";
import { setNdVendorDivisions } from "../create-event/components/cards/event-details/utility/utility";
import LoadingSpinner from "../create-event/constants/LoadingSpinner/LoadingSpinner";

interface IEventHookFormContainerProps {
  children?: any;
  preselectedFieldValues?: any;
}

const EventHookFormContainer: React.FunctionComponent<
  IEventHookFormContainerProps
> = ({ children, preselectedFieldValues = {} }) => {
  const location = useLocation();
  const state: any = location.state || {};
  const { id } = useParams();
  const dispatch = useDispatch();
  const events = eventTypes;
  const eventType = events[1];
  const data = useSelectorWrap("promotion_details_data");
  const allowancesData = useSelectorWrap(
    "allowance_offer_details_configuration"
  );
  const {
    data: { isResendOfferSuccess = false },
  } = useSelectorWrap("offer_resend_status_rn") || {};
  const updateAllowaceData = useSelectorWrap("allowancesResponse_rn");
  const allowancesDeletePendingCancelData = useSelectorWrap(
    "allowancesDeletePendingCancelSlice_rn"
  );
  const { data: eventData } = useSelectorWrap("event_details_data");

  const eventVechicleUpdateStatus = useRef({});

  const {
    data: { isRefetch },
  } = useSelectorWrap("refetchEventPlanx_Api");

  useEffect(() => {
    const isAllowanceOnlyEventType =
      state.type !== undefined && eventType?.headerText === state.type
        ? true
        : false;
    const userType = getLoggedInUserType();
    const vendorDivisions =
      userType?.toUpperCase() === EUSER_ROLES.VENDOR
        ? setNdVendorDivisions()
        : {
            vendorNumbers: [],
            divisions: ["98"],
          };
    dispatch(setVendorNDDivisions({ vendorDivisions }));

    dispatch(
      allowanceOnlyEventTypeConfiguration({
        isAllowanceOnlyEventType: isAllowanceOnlyEventType,
      })
    );
  }, []);

  const {
    data: eventDetailsData,
    refetch,
    isFetching: isPlanXapiDataFetched,
  } = useGetPlanEventDataQuery(id, {
    skip: !id,
  });

  useEffect(() => {
    const {
      eventDetailsEventInd,
      allowanceEventInd,
      promotionEventInd,
      otherDetailsChangedInd,
      planEvent = {},
      planEventHistory = [],
      planEventPending = {},
      planEventPendingChanges = {},
    } = eventDetailsData || {};
    eventVechicleUpdateStatus.current = {
      isEventVehicleChangedInPending: checkIsEventDatesChanged(
        eventDetailsEventInd,
        allowanceEventInd,
        promotionEventInd,
        otherDetailsChangedInd,
        planEvent,
        planEventPending,
        planEventPendingChanges
      ),
      isEventVehicleChangedInHistory:
        checkIsEventUpdatedInHistory(planEventHistory),
    };
    dispatch(
      setIsEventVehicleDatesChanged({ ...eventVechicleUpdateStatus.current })
    );
  }, [eventDetailsData]);

  const updatePlanCaedData = () => {
    const filledSections: string[] = [];
    let selectedSection = "Event Details";
    // let activeCard = 0;
    if (id && eventDetailsData?.planEvent) {
      filledSections.push("Event Details");
      selectedSection = "Allowance";
      // activeCard = 1;
      if (eventDetailsData?.planEvent?.offerAllowances?.length > 0) {
        filledSections.push("Allowance");
        if (
          ["DP", "NDP", "NCDP"].includes(eventDetailsData?.planEvent?.eventType)
        )
          selectedSection = "Promotion";
        // activeCard = 2;
      }
      if (
        eventDetailsData?.planEvent?.promotionsList?.length > 0 &&
        ["DP", "NDP", "NCDP"].includes(eventDetailsData?.planEvent?.eventType)
      ) {
        filledSections.push("Promotion");
        selectedSection = "Performance";
        // activeCard = 3;
      }
    }
    // dispatch(eventPlanCardUpdateDataSlice({ EVENT_PLAN_CARD: activeCard }));
    dispatch(
      eventProgressDataHandler({
        selected: selectedSection,
        filledSections: filledSections,
      })
    );
  };

  const setEventDetails = () => {
    const userType = getLoggedInUserType();
    if (eventDetailsData) {
      const {
        eventDetailsEventInd,
        allowanceEventInd,
        promotionEventInd,
        otherDetailsChangedInd,
        pidDetailsEventInd,
        planEvent,
        planEventPending,
      } = eventDetailsData;
      dispatch(
        setPlanEventIndicators({
          eventDetailsEventInd,
          allowanceEventInd,
          promotionEventInd,
          otherDetailsChangedInd,
          pidDetailsEventInd,
          planEvent,
          planEventPending,
        })
      );
      const { updatedEventDetails } = getEventDetails(eventDetailsData);
      const modifiedEventData = cloneDeep(updatedEventDetails);
      if (
        eventDetailsData?.planEvent?.offerAllowances &&
        eventDetailsData?.planEvent?.offerAllowances.length > 0
      ) {
        modifiedEventData?.offerAllowances?.forEach(item => {
          item["offerId"] = item?.id;
        });
      }
      if (
        eventDetailsData?.planEvent?.promotionsList &&
        eventDetailsData?.planEvent?.promotionsList?.length > 0
      ) {
        modifiedEventData["promotionsLists"] = [
          { promotionsList: modifiedEventData?.promotionsList },
        ];
      } else if (!eventDetailsData?.planEvent?.promotionsList?.length) {
        modifiedEventData["promotionsLists"] = [];
      }
      modifiedEventData["startWeekVehicle"] =
        modifiedEventData?.eventCreationVehicle?.vehicleNm;
      modifiedEventData["planStoreGroupType"] =
        modifiedEventData?.storeGroups?.[0]?.storeGroupType;
      modifiedEventData["vehicleType"] =
        modifiedEventData?.eventCreationVehicle?.vehicleType?.vehicleTypDesc;
      modifiedEventData["isChangeEventTypeVisible"] = false;
      modifiedEventData["promoProductGroup"] =
        modifiedEventData?.planProductGroups?.[0]?.name;
      modifiedEventData["storeGroupName"] =
        modifiedEventData?.storeGroups?.[0]?.storeGroupName;
      modifiedEventData["inValidAllowances"] = getInvalidAllowanceAndPromotions(
        modifiedEventData,
        modifiedEventData?.["eventCreationVehicle"],
        GetInvalidAllowanceKeys.iterateKey1,
        GetInvalidAllowanceKeys.iterateKey2,
        GetInvalidAllowanceKeys.startDateKey,
        GetInvalidAllowanceKeys.endDateKey,
        "id",
        userType,
        eventDetailsData,
        eventVechicleUpdateStatus.current?.["isEventVehicleChangedInPending"]
      );

      modifiedEventData["inValidPromotions"] = getInvalidAllowanceAndPromotions(
        modifiedEventData,
        modifiedEventData?.["eventCreationVehicle"],
        GetInvalidPromoKeys.iterateKey1,
        GetInvalidPromoKeys.iterateKey2,
        GetInvalidPromoKeys.startDateKey,
        GetInvalidPromoKeys.endDateKey,
        "id",
        userType,
        eventDetailsData,
        eventVechicleUpdateStatus.current?.["isEventVehicleChangedInPending"]
      );
      const sortedDivisions = modifiedEventData?.divisionIds?.sort() || [];
      modifiedEventData["divisionIds"] = sortedDivisions;
      dispatch(setOfferDivisionsData({ eventDivisions: sortedDivisions }));
      const { periscopeDetails } = modifiedEventData;
      if (!periscopeDetails) {
        dispatch(resetForecastHandler());
      }
      const storeGroupDivisions = [
        ...new Set(
          modifiedEventData?.storeGroups?.flatMap(
            item => item.divId || item.divisionIds
          )
        ),
      ];
      dispatch(setStoreGroupDivisions({ storeGroupDivisions }));
      dispatch(eventDetailsDataHandler(modifiedEventData));
    }
  };

  useEffect(() => {
    updatePlanCaedData();
    setEventDetails();
    return () => {
      dispatch(resetEventDetailsHandler());
    };
  }, [eventDetailsData]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const refetchEventDetails = () => {
    if (
      id &&
      (data?.data?.isPromotionAdded ||
        eventData?.updateUser?.createTs ||
        (eventDetailsData &&
          eventData.eventStatus !== "Draft" &&
          eventData.eventStatus !== eventDetailsData?.planEvent.eventStatus) ||
        allowancesData?.data?.payload?.length > 0 ||
        isResendOfferSuccess ||
        (updateAllowaceData?.data !== undefined &&
          Object.keys(updateAllowaceData?.data)?.length))
    ) {
      refetch && refetch();
      dispatch(offerResendStatus({ isResendOfferSuccess: false }));
    }
  };

  useEffect(() => {
    refetchEventDetails();
  }, [
    data?.data?.isPromotionAdded,
    allowancesData,
    eventData.eventStatus,
    eventData?.updateUser?.createTs,
    updateAllowaceData?.data,
    allowancesDeletePendingCancelData?.data?.isAllowanceCancelled,
    isResendOfferSuccess,
    isRefetch,
  ]);
  return eventData ? (
    <>
      <LoadingSpinner
        isLoading={isPlanXapiDataFetched}
        classname="!h-full !w-full rounded-md"
      />
      <EventFormProvider eventData={eventData}>{children}</EventFormProvider>
    </>
  ) : null;
};

export default memo(EventHookFormContainer);
