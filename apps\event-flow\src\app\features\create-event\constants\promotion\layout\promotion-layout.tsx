import { PROMO_CUSTOMER_FIELDS } from "../cards/promotion-customers/fields";
import { PROMO_DATE_FIELDS } from "../cards/promotion-dates/fields";
import { PROMO_DETAILS_FIELDS } from "../cards/promotion-details/fields";

const PROMOTION_CUSTOMER = {
  "Promotion Customer": {
    section: "Promotion Customers",
    multiple: false,
    title: "Customers",
    previewTitle: "Promotion",
    label: "Customers",
    ediLabel: "Event Details",
    isOpenCard: true,
    fields: PROMO_CUSTOMER_FIELDS,
    subtitle: "",
    saveLabel: "Save Event Details & Add Allowance",
    editAccordion: "Edit Event Details",
    nextLevel: false,
    remove: false,
    create: {
      isEdit: false,
      isView: false,
      label: "Save & Continue",
    },
    edit: {
      isEdit: false,
      isView: false,
      label: "Update & Continue",
    },
  },
};
const PROMOTION_DATES = {
  "Promotion Dates": {
    section: "Promotion Dates",
    title: "Promo Dates",
    label: "Promo Dates",
    offerTitle: "New Offer",
    isOpenCard: false,
    multiple: true,
    subtitle: "Not Started",
    stepper: 0,
    allowanceTye: "Case",
    allowanceToBeCreated: "Both",
    fields: PROMO_DATE_FIELDS,
    headerButton: "New",
    nextLevel: true,
    nextLevelLabel: "Skip to Promotion Details",
    allAnotherItem: "Add Another Allowance",
    addAnotherOfferAllowance: "Add Another Offer & Allowance",
    saveAndContinue: "Save & Continue",
    saveAndCreateAllowance: "Save & Create Allowance",
    create: {
      isEdit: false,
      isView: true,
      label: "Save & Continue",
    },
    edit: {
      isEdit: false,
      isView: true,
      label: "Update & Continue",
    },
  },
};
const PROMO_DETAILS = {
  "Promotion Details": {
    section: "Promotion Details",
    title: "Promo Details",
    label: "Promo Details",
    isOpenCard: false,
    multiple: true,
    headerButton: "Add Promotion",
    subtitle: "Not Started",
    fields: PROMO_DETAILS_FIELDS,
    nextLevel: true,
    nextLevelLabel: "Skip to Promotion Details",
    allAnotherItem: "Add Another Promotion",
    create: {
      isEdit: false,
      isView: false,
      label: "Save Promotion",
    },
    edit: {
      isEdit: true,
      isView: false,
      label: "Update Promotion",
    },
  },
};

const PROMO_OVERLAPS = {
  "Promotion Overlaps": {
    section: "Promotion Overlaps",
    title: "Promo Overlaps",
    label: "Promo Overlaps",
    offerTitle: "New Offer",
    isOpenCard: false,
    multiple: false,
    subtitle: "",
    stepper: 0,
    fields: "",
    headerButton: "New",
    nextLevel: true,
    nextLevelLabel: "",
    saveAndContinue: "Confirm & Continue",
    returnToPromoDates: "Return to Promo Dates",
    create: {
      isEdit: false,
      isView: true,
      label: "Save & Continue",
    },
    edit: {
      isEdit: false,
      isView: true,
      label: "Update & Continue",
    },
  },
};

export const CANCEL_PROMO_CREATION_LABEL = "Cancel Promo Creation";

export const EVENT_PROMOTION_SECTION = {
  section: "Division Promotion",
  steppers: ["Promotion Customer", "Promotion Dates", "Promotion Details"],
  ...PROMOTION_CUSTOMER,
  ...PROMOTION_DATES,
  ...PROMO_DETAILS,
};

export const EVENT_PROMOTION_WITH_OVERLAPS_SECTION = {
  section: "Division Promotion",
  steppers: [
    "Promotion Customer",
    "Promotion Dates",
    "Promotion Overlaps",
    "Promotion Details",
  ],
  ...PROMOTION_CUSTOMER,
  ...PROMOTION_DATES,
  ...PROMO_OVERLAPS,
  ...PROMO_DETAILS,
};
