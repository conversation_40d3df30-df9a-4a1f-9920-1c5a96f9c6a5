import { useEffect, useState } from "react";
import { useSelectorWrap } from "@me/data-rtk";
import { useFormContext } from "react-hook-form";
import { useParams } from "react-router-dom";
import { replaceMeuppuserToSystem, timeDifference } from "@me/util-helpers";
import { max, parseISO } from "date-fns";
import { History } from 'lucide-react';
import { EventHistory } from "@me-upp-js/features/event-history";

const EventHistorySection: React.FunctionComponent = () => {
    const [isOpen, setOpen] = useState(false);
    const { id } = useParams();
    const { getValues } = useFormContext();
    const eventNo = getValues("planEventIdNbr");
    const { data: eventDetailsData } = useSelectorWrap("event_details_data");
    const [time, setTime] = useState<any>();

    const updatedTime = (eventDetailsData: any) => {
        const eventTimeStamp: any = parseISO(
            eventDetailsData?.updateUser?.createTs
        );
        const promotionTimeStamp: any = parseISO(
            eventDetailsData?.promotionsList?.[0]?.updateUser?.createTs
        );
        let latestTimeStamp = eventTimeStamp;
        if (!isNaN(promotionTimeStamp)) {
            const timeStamps = [eventTimeStamp, promotionTimeStamp];
            latestTimeStamp = max(timeStamps);
        }
        setTime(timeDifference(latestTimeStamp));
    };

    useEffect(() => {
        updatedTime(eventDetailsData);
    }, [eventDetailsData]);

    return (
        <div
            className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative"
            id="abs-last-modification-details-text-dv"
        >
            <span className="flex items-center mr-3 font-semibold text-[#1B6EBB] cursor-pointer gap-1">
                <p
                    id="abs-last-modification-details-view-history"
                    className="flex-grow-0 flex-shrink-0 text-[16px] font-semibold text-left text-[#1b6ebb] cursor-pointer"
                    onClick={() => setOpen(true)}
                >
                   View History
                </p>
            </span>
            <EventHistory
                id={id}
                isOpen={isOpen}
                setOpen={setOpen}
                eventNo={eventNo}
            />
        </div>
    );
};

export default EventHistorySection;
