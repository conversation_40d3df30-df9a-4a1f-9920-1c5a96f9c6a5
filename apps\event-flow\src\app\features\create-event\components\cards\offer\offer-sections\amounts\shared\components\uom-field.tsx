import { InputSelect } from "../../../../../../fields/allowance-atoms";
import { getFieldWithHighletedWrapper } from "../../../../../allowance/stepper/common-stepper/allowance-amount/allowance-amounts-services";
import Disabled<PERSON><PERSON> from "./disabled-field";
import { IAmountStepFieldProps } from "../props-types";
import { memo } from "react";

const UOMField = ({
  fieldProps,
  onChange,
  options,
  disabled = false,
  value,
  allowanceAmountFields,
  getEventData,
  searchId,
  baseId = "",
  formControls
}: IAmountStepFieldProps) => {
  const { register, control } = formControls || {};
  return (
    <>
      {disabled ? (
        <DisabledField
          label={fieldProps?.label}
          required={fieldProps?.required}
          value={value}
          isSelect={true}
          baseId={baseId}
        />
      ) : (
        getFieldWithHighletedWrapper(
          <InputSelect
            fieldProps={fieldProps}
            register={register}
            control={control}
            options={options}
            displayLabel={fieldProps?.displayLabel}
            onChange={onChange}
          />,
          fieldProps,
          allowanceAmountFields,
          getEventData,
          searchId
        )
      )}
    </>
  );
};
export default memo(UOMField);
