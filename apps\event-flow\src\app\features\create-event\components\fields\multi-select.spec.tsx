import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { FormProvider, useForm } from "react-hook-form";
import InputMultiSelect from "./multi-select";
import { FormFieldError } from "@me/util-form-wrapper";
import "@testing-library/jest-dom/extend-expect";

jest.mock("@albertsons/uds/molecule/MultiSelect", () => ({
  __esModule: true,
  default: jest.fn(({ onChange, ...props }) => (
    <button {...props} onClick={() => onChange([{ label: "Option 1", value: "1" }])}>
      MultiSelect
    </button>
  )),
}));

describe("InputMultiSelect Component", () => {
  const renderComponent = ({
    storeGroups = [{ label: "Option 1", value: "1" }],
    defaultValue = [],
    onChangeHandler = jest.fn(),
    registerField = "testField",
    validationFn = jest.fn().mockReturnValue("Validation Error"),
    errorMessage = "Validation Error",
  } = {}) => {
    const Wrapper = ({ children }) => {
      const methods = useForm();
      return <FormProvider {...methods}>{children}</FormProvider>;
    };

    return render(
      <Wrapper>
        <InputMultiSelect
          storeGroups={storeGroups}
          onChangeHandler={onChangeHandler}
          defaultValue={defaultValue}
          registerField={registerField}
          validationFn={validationFn}
          errorMessage={errorMessage}
        />
      </Wrapper>
    );
  };

  it("should render MultiSelect when storeGroups is provided", () => {
    renderComponent();
    expect(screen.getByRole("button")).toBeInTheDocument();
  });

  it("should call onChangeHandler when MultiSelect value changes", () => {
  const onChangeHandler = jest.fn();
  renderComponent({ onChangeHandler });

  // Find the MultiSelect button
  const selectButton = screen.getByRole("button");
  
  // Simulate a selection event
  fireEvent.click(selectButton);

  // Ensure the handler was called
  expect(onChangeHandler).toHaveBeenCalledTimes(1);
  expect(onChangeHandler).toHaveBeenCalledWith([{ label: "Option 1", value: "1" }]);
});


  it("should not render MultiSelect when storeGroups is empty", () => {
    renderComponent({ storeGroups: [] });
    expect(screen.getByRole("button")).toBeDisabled();
  });

  it("should prevent event propagation on container click", () => {
    renderComponent();
    const container = screen.getByRole("button");

    const event = new MouseEvent("click", { bubbles: true });
    Object.assign(event, { stopPropagation: jest.fn(), preventDefault: jest.fn() });

    fireEvent(container, event);

    expect(event.stopPropagation).toHaveBeenCalled();
    expect(event.preventDefault).toHaveBeenCalled();
  });

});
