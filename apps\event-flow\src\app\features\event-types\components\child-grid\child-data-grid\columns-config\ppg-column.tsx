import { EEVENT_STATUS } from "@me/util-helpers";
import React, { useRef, useState } from "react";
import Divider from "@albertsons/uds/molecule/Divider";
import Popper from "@albertsons/uds/molecule/Popper";
import TooltipComponent from "./ppg-tooltip";
import EventDetailPromoProductHoverContent from "../../../../../create-event/components/cards/event-details/event-details-promo-product-hover-content";
import ChildGridItemsDetails from "./ppg-item-details-modal";

function PPGCell({ eventDetails, itemsData }) {
  const [ppgOpen, setPPGOpen] = useState(false);
  const ppgRef = useRef(null);
  const isNotValidStatus = [
    EEVENT_STATUS.CANCELED,
    EEVENT_STATUS.REJECTED,
  ]?.includes(eventDetails?.eventStatus);

  if (isNotValidStatus) {
    return (
      <div className="flex text-sm px-3 bg-[#f9d3d345] h-full items-center italic">
        {eventDetails?.eventStatus}
      </div>
    );
  }
  const onPopperBlur = (ev: any, ref, cb) => {
    if (ev.relatedTarget !== ref.current?.firstChild) {
      cb(false);
    }
  };

  const ppgName = eventDetails?.planProductGroups?.[0]?.planProductGroupName || '';

  return (
    <div className="text-sm px-3 red flex">
      <div className="break-words mb-1">
        {ppgName}
      </div>
      <div className="flex items-center">
        <TooltipComponent eventDetails={eventDetails} itemsData={itemsData} />
        <Divider height={15} className="inline-block mx-2" color="#C8DAEB" />
        <ChildGridItemsDetails eventDetails={eventDetails} />
        {eventDetails?.planProductGroups?.length > 1 && (
          <>
            <Divider height={15} className="inline-block mx-2" color="#C8DAEB" />
            <span
              className="self-center text-sm font-semibold text-left text-[#1b6ebb] cursor-pointer"
              ref={ppgRef}
              onClick={() => setPPGOpen(true)}
            >
              <p>More</p>
            </span>
            <Popper
              anchor={ppgRef}
              open={ppgOpen}
              onBlur={e => onPopperBlur(e, ppgRef, setPPGOpen)}
              autoFocus={true}
              zIndex={10}
            >
              <EventDetailPromoProductHoverContent
                popoverCloser={() => setPPGOpen(false)}
                event={eventDetails}
              />
            </Popper>
          </>
        )}
      </div>
    </div>
  );
}
export default PPGCell;
