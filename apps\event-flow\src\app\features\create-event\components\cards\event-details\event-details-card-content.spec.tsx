import { Mocked<PERSON>rovider } from "@apollo/client/testing";
import { app_store } from "@me/data-rtk";
import { fireEvent, render, screen } from "@testing-library/react";
import { FormProvider, useForm } from "react-hook-form";
import { Provider } from "react-redux";
import { <PERSON><PERSON><PERSON>Router } from "react-router-dom";
import EventDetailsCardContent from "./event-details-card-content";
import {
  BLANKEventDetailsSelectorMock,
  CardConfigMock,
  EventDetailsDataSelectorMock,
  EventDetailsDefaultValues,
  mockEventDetailsDefaultValues2,
  usePostPlanPPGMock,
} from "./event-details-mocks";
import * as EVENT_API_SERVICE from "../../../service/apis/event-api";
import * as graphQLSchema from "apps/event-flow/src/app/graphql/generated/schema";
import * as selectors from "@me/data-rtk";
import * as UPP_UTILS from "@me-upp-js/utilities";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom";

const Wrapper = props => {
  const formMethods = useForm<any>({
    // i think this is the same as the selector's field?...
    defaultValues: mockEventDetailsDefaultValues2,
  });
  return (
    <Provider store={app_store}>
      <FormProvider {...formMethods}>{props.children}</FormProvider>
    </Provider>
  );
};
jest.mock("react-pdf", () => ({
  Document: jest.fn(({ children }) => children),
  Page: jest.fn(() => <div data-testid="mock-page"></div>),
  pdfjs: {
    GlobalWorkerOptions: {
      workerSrc: "",
    },
    version: "2.10.377",
  },
}));

const NoWrapper = props => {
  const formMethods = useForm<any>({
    defaultValues: EventDetailsDefaultValues,
  });
  return (
    <Provider store={app_store}>
      <FormProvider {...formMethods}>{props.children}</FormProvider>
    </Provider>
  );
};

jest.mock("react-redux", () => ({
  ...jest.requireActual("react-redux"),
  useSelector: jest.fn(),
  useDispatch: jest.fn(),
}));
jest.mock("react-redux", () => ({
  ...jest.requireActual("react-redux"),
  useDispatch: jest.fn().mockImplementation(() => {
    return jest.fn(() => null);
  }),
}));

describe("Event Details Card Content Test Suite", () => {
  beforeEach(() => {
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      switch (sel_name) {
        case "event_details_data": {
          return EventDetailsDataSelectorMock;
        }
        case "allowance_only_event_type_configuration": {
          return {
            data: { isAllowanceOnlyEventType: false },
          };
        }
        case "is_event_edit_enable": {
          return {
            data: {
              isEventCardOpen: {},
            },
          };
        }
        case "cic_upc_type_data": {
          return {
            data: {
              cicsWithUpcTypeInfo: [],
            },
          };
        }
        case "check_event_vehicledates_rn": {
          return {
            data: {
              isEventVehicleChangedInPending: false,
              isEventVehicleChangedInHistory: false,
            },
          };
        }
        case "ppgs_added_through_cic_items": {
          return {
            data: {
              isPPGsAddedThroughCICItems: false,
            },
          };
        }
        case "store_group_divisions": {
          return {
            data: {
              storeGroupDivisions: [],
            },
          };
        }
        case "event_type_and_divisions": {
          return {
            data: {
              eventTypeAndDivisionsData: [],
            },
          };
        }
        default:
          break;
      }
    });
    jest
      .spyOn(EVENT_API_SERVICE, "usePostPlanPromoProductGroupMutation")
      .mockReturnValue([jest.fn, usePostPlanPPGMock]);
    jest
      .spyOn(EVENT_API_SERVICE, "usePostProductLocationValidationMutation")
      .mockReturnValue([
        jest.fn(() =>
          Promise.resolve({
            data: [
              { plannedEventItems: { invalidPlannedProductGroupItems: [] } },
            ],
          })
        ),
        {
          isLoading: true,
          data: [],
        },
      ]);
    jest
      .spyOn(EVENT_API_SERVICE, "usePostStoreGroupStoresMutation")
      .mockReturnValue([
        jest.fn,
        {
          isLoading: true,
          data: [],
        },
      ]);
    jest
      .spyOn(EVENT_API_SERVICE, "usePostProductLocationValidationMutation")
      .mockReturnValue([
        jest.fn,
        {
          isLoading: true,
          data: [],
        },
      ]);
    jest
      .spyOn(EVENT_API_SERVICE, "usePostPlanEventItemsByCICsMutation")
      .mockReturnValue([
        jest.fn,
        {
          isLoading: true,
          data: [],
        },
      ]);
    jest
      .spyOn(EVENT_API_SERVICE, "usePostPlanPPGsResolveMutation")
      .mockReturnValue([
        jest.fn,
        {
          isLoading: true,
          data: [],
        },
      ]);
    jest
      .spyOn(graphQLSchema, "useGetVehicleByTypeAndYearLazyQuery")
      .mockImplementation(() => {
        return [
          jest.fn,
          {
            data: {
              getVehicleByTypeAndYear: [
                {
                  id: "645ad70031c0c4e30d93d8e1",
                  vehicleNm: "05 Sunday Insert Wk 01 2023",
                  vehicleTypNm: "sunin",
                  sourceVehicleSk: 54271,
                  startDate: "2023-01-01",
                  endDate: "2023-01-03",
                  vehicleType: {
                    vehicleTypeId: "645acd5631c0c4e30d8c0fef",
                    sourceVehicleTypeSk: 152,
                    vehicleTypNm: "sunin",
                    vehicleTypDesc: "Sunday Insert",
                    __typename: "VehicleTypeRef",
                  },
                  __typename: "Vehicle",
                },
              ],
            },
          },
        ];
      });
    jest
      .spyOn(
        graphQLSchema,
        "useGetPromoProductGroupsByDivisionIdAndVendorNumberLazyQuery"
      )
      .mockImplementation(() => {
        return [
          jest.fn,
          {
            data: {
              getPromoProductGroupsByDivisionIdAndVendorNumber: [
                {
                  divisionId: "27",
                  id: "645e731931c0c4e30d20d6f0",
                  itemCount: 13,
                  name: "Annies Homegrown Snacks Box - 84882",
                  sourceProductGroupId: 184779,
                  simsVendors: ["006446"],
                  displayInd: false,
                  unitType: 1,
                  __typename: "PlanProductGroup",
                },
              ],
            },
          },
        ];
      });
    jest
      .spyOn(graphQLSchema, "useGetPlanProductGroupByNameLazyQuery")
      .mockImplementation(() => {
        return [
          jest.fn,
          {
            data: {
              getPlanProductGroupByName: [
                {
                  name: "Annies Dressing - 85578",
                  id: "645e734231c0c4e30d2109b1",
                  sourceProductGroupId: 186311,
                  displayInd: false,
                  unitType: 1,
                  simsVendors: ["006446"],
                  itemCount: 2,
                  __typename: "PlanProductGroup",
                },
              ],
            },
          },
        ];
      });
    jest
      .spyOn(graphQLSchema, "useGetStoreGroupsLazyQuery")
      .mockImplementation(() => {
        return [
          jest.fn,
          {
            data: {
              getStoreGroups: [
                {
                  divisionIds: ["27"],
                  id: "6453d8fa31c0c4e30d8f11d3",
                  storeGroupName: "Seattle All Stores",
                  storeIds: ["0149"],
                  sourceStoreGroupId: "EDM",
                  storeGroupType: {
                    groupInd: "D",
                    groupType: "S",
                    storeGrpTypeName: "Division",
                    __typename: "StoreGroupType",
                  },
                  __typename: "StoreGroup",
                },
              ],
            },
          },
        ];
      });
    jest
      .spyOn(EVENT_API_SERVICE, "useLazyGetEventPeriscopeDataQuery")
      .mockImplementation(() => {
        return [
          jest.fn,
          {
            data: {},
          },
        ];
      });
  });
  it("should render event details card content component", async () => {
    const { baseElement } = render(
      <BrowserRouter>
        <Wrapper>
          <MockedProvider>
            <EventDetailsCardContent
              cardConfiguration={CardConfigMock}
              updateEventHandler={jest.fn()}
              isEditEvent={true}
              wasError={false}
            />
          </MockedProvider>
        </Wrapper>
      </BrowserRouter>
    );
    expect(baseElement).toBeTruthy();
  });

  it("should contain correct texts in Event Details card content component", async () => {
    render(
      <BrowserRouter>
        <NoWrapper>
          <MockedProvider>
            <EventDetailsCardContent
              cardConfiguration={CardConfigMock}
              updateEventHandler={jest.fn()}
              isEditEvent={true}
              wasError={false}
            />
          </MockedProvider>
        </NoWrapper>
      </BrowserRouter>
    );
    expect(screen.queryByText("Event Name")).toBeTruthy();
    expect(screen.queryByText("Vehicle End")).toBeTruthy();
    expect(screen.queryByText("Vehicle Start")).toBeTruthy();
    expect(screen.queryByText("Start Week/Vehicle")).toBeTruthy();
    expect(screen.queryByText("Year")).toBeTruthy();
    expect(screen.queryByText("Vehicle Type/Custom Date")).toBeTruthy();
    expect(screen.queryByText("View Stores")).toBeNull();
    expect(screen.queryByText("Store Groups")).toBeTruthy();
    expect(screen.queryByText("Store Group Type")).toBeTruthy();
    expect(screen.queryByText("Promo Product Groups")).toBeTruthy();
    expect(screen.queryByText("Division")).toBeTruthy();
    expect(screen.queryByText("PID")).toBeTruthy();
  });

  it("should include periscope ID in Event Details card content component", async () => {
    const { container } = render(
      <BrowserRouter>
        <NoWrapper>
          <MockedProvider>
            <EventDetailsCardContent
              cardConfiguration={CardConfigMock}
              updateEventHandler={jest.fn()}
              isEditEvent={true}
              wasError={false}
            />
          </MockedProvider>
        </NoWrapper>
      </BrowserRouter>
    );

    const pID = `902333`;
    const pIdInput = container.querySelector(
      `input[name="periscopeFormField"]`
    );
    // fireEvent.change(pIdInput, {target: {value: "902333"}}

    if (pIdInput) {
      await userEvent.type(pIdInput, pID);
      // fireEvent.change(pIdInput, {target: {value: "902333"}}  )
    }
  });

  it("should select division ID Event Details card content component", async () => {
    const { container } = render(
      <BrowserRouter>
        <NoWrapper>
          <MockedProvider>
            <EventDetailsCardContent
              cardConfiguration={CardConfigMock}
              updateEventHandler={jest.fn()}
              isEditEvent={true}
              wasError={false}
            />
          </MockedProvider>
        </NoWrapper>
      </BrowserRouter>
    );

    const divisionButton = screen.findByText("Division");
    const user = userEvent.setup();
    await user.click(await divisionButton);
    screen.debug(undefined, Infinity);
  });

  it("should allow submission of form in Event Details card content component", async () => {
    jest.spyOn(UPP_UTILS, "showAddEventCTA").mockReturnValue(true);

    const { container } = render(
      <BrowserRouter>
        <NoWrapper>
          <MockedProvider>
            <EventDetailsCardContent
              cardConfiguration={CardConfigMock}
              updateEventHandler={jest.fn()}
              isEditEvent={true}
              wasError={false}
            />
          </MockedProvider>
        </NoWrapper>
      </BrowserRouter>
    );

    const divisionButton = screen.findByText("Division");
    const yearButton = screen.findByText("Year");
    const user = userEvent.setup();
    await user.click(await divisionButton);
    await user.click(await yearButton);
    const SaveButton = screen.findByText("Save Event Details & Add Allowance");
    await user.click(await SaveButton);
  }, 30000);

  xit("should allow changing vehicle types in Event Details card content component", async () => {
    jest.spyOn(UPP_UTILS, "showAddEventCTA").mockReturnValue(true);

    function container() {
      return render(
        <BrowserRouter>
          <NoWrapper>
            <MockedProvider>
              <EventDetailsCardContent
                cardConfiguration={CardConfigMock}
                updateEventHandler={jest.fn()}
                isEditEvent={true}
                wasError={false}
              />
            </MockedProvider>
          </NoWrapper>
        </BrowserRouter>
      );
    }
    expect(container()).toMatchSnapshot();
  });
});
