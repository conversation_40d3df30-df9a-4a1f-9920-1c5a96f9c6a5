// hooks, constants, interfaces, apis
import { EVENT_COMMENTS } from "./constants/event-comments-constants";
import {
  EEVENT_STATUS,
  EUSER_ROLES,
} from "./components/event-action/event-action.model";
import { useState, useEffect, useRef } from "react";
import {
  getTaskCommentMap,
  nextStatusToBe,
  statuses,
  stringToHexColorHash,
  usePostEventCommentsDataMutation,
  usePutEventCommentsDataMutation,
} from "./event-comments-section-service";
import {
  IPlanEventCommentDTO,
  IEventCommentsSectionProps,
  IPlanEventCommentProps,
  CommentCategory,
  CommentPermissions,
} from "./interfaces/plan-event-comment-interface";
import efConstants from "../../shared/ef-constants/ef-constants";

// UDS components and design imports
import { Info, Pencil, User } from "lucide-react";
import Drawer from "@albertsons/uds/molecule/Drawer";
import Timeline from "@albertsons/uds/molecule/Timeline";
import Tabs, { Tab } from "@albertsons/uds/molecule/Tabs";
import Divider from "@albertsons/uds/molecule/Divider";
import TextArea from "@albertsons/uds/molecule/TextArea";
import Button from "@albertsons/uds/molecule/Button";
import Modal from "@albertsons/uds/molecule/Modal";
import Checkbox from "@albertsons/uds/molecule/Checkbox";
import { RBAC } from "albertsons-react-rbac";

// service imports

import {
  getLoggedInUserType,
  commentsForOfferPromoFlag,
} from "@me-upp-js/utilities";
import { useSelectorWrap } from "@me/data-rtk";
import { useDispatch } from "react-redux";
import {
  doRefetchEventsPlanxAapi,
  eventDetailsDataHandler,
} from "../create-event/service/slice/event-detail-slice";
import {
  getAllowanceDraftCollections,
  getPromoDraftCollections,
} from "./event-hook-form-wrapper-service";
import { formatDateTimeWithZone, useGetQueryParams } from "@me/util-helpers";
import _ from "lodash";
import { appConstants } from "@me/utils-root-props";
import { useUpdateTaskAlertDataMutation } from "../create-event/service/apis/tasks-api";
import { getDecodedValue } from "@me-upp-js/features/event-history";
import EventCommentsListForOfferAndPromotion from "./event-comments-list-for-offer-and-promotion";
import { usePostEventComments } from "./components/event-action/useEventComments";

const EventCommentsSection: React.FunctionComponent<
  IEventCommentsSectionProps
> = ({
  eventId,
  isEventCommentsOpen,
  setIsEventCommentsOpen,
  allCommentsList,
  isEventCommentsLoading,
  parentCommentUpdater,
  setCommentOpen,
}) => {
  // start of component

  const userRole = getLoggedInUserType();
  const {
    queryParamValue: { isCommentOpen },
  } = useGetQueryParams(["isCommentOpen"]);

  // hooks and api's
  // useStates
  const [commentTextArea, setCommentTextArea] = useState<any>();
  const [editCommentTextArea, setEditCommentTextArea] = useState<string>();
  const [currentIsoTime, setCurrentIsoTime] = useState<string>();
  const [currentCommentCategory, setCurrentCommentCategory] =
    useState<string>();
  const [commentHoveredIndex, setCommentHoveredIndex] = useState<number>(-1);
  const [isCommentToBeEdited, setIsCommentToBeEdited] =
    useState<boolean>(false);
  const [commentEditedIndex, setCommentEditedIndex] = useState<number>(-1);
  const [shouldShowWarning, setShouldShowWarning] = useState<boolean>(false);
  const [disableCtsBtn, setDisableCtsBtn] = useState<boolean>(false);
  const [eventCommentsList, setEventCommentsList] = useState<any>();
  const [commentsPlaceholder, setCommentsPlaceholder] = useState(
    EVENT_COMMENTS["COMMENT_TEXT_AREA_PLACEHOLDER"]
  );
  const dispatch = useDispatch();
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const {
    data: { isFromComment, currentEventStatus },
  } = useSelectorWrap("isFromCommentSection_rn");
  const [checked, setChecked] = useState<boolean>(!isFromComment);
  const { EVENT_COMMENTS_NOTE_TEXT } = EVENT_COMMENTS;
  const {
    data: {
      allowanceEventInd,
      eventDetailsEventInd,
      promotionEventInd,
      otherDetailsChangedInd,
      pidDetailsEventInd,
    },
  } = useSelectorWrap("plan_event_indicators");
  const {
    queryParamValue: { taskType },
  } = useGetQueryParams([appConstants.TASK_TYPE_QUERY_PARAM]);
  // when the location state has the isCommentOpen element, then open the comment section clear the state
  useEffect(() => {
    if (isCommentOpen === "true") {
      setCommentOpen(true);
    }
  }, [isCommentOpen, setCommentOpen]);

  useEffect(() => {
    if (commentEditedIndex > -1) {
      setIsCommentToBeEdited(true);
    }
  }, [commentEditedIndex]);

  useEffect(() => {
    setChecked(!isFromComment);
  }, [isFromComment, isEventCommentsOpen]);

  const { divisionIds = [], negotiationSimsVendors = [] } = {
    ...eventDetailsData,
  };
  const eventStatusValue = eventDetailsData?.eventStatus;

  const [
    updateTaskAertData,
    { isSuccess: isTaskAlertUpdated, data: updatedTaskAlertData },
  ] = useUpdateTaskAlertDataMutation();

  const [
    putEventCommentsData,
    {
      isSuccess: isPutEventCommentsDataSuccess,
      isLoading: isPutEventCommentsDataLoading,
      data: toPutEventCommentData,
    },
  ] = usePutEventCommentsDataMutation();

  const isSendBackComment = () => {
    return statuses.includes(eventDetailsData?.eventStatus) &&
      userRole === EUSER_ROLES?.MERCHANT
      ? checked
      : false;
  };
  const {
    postComment,
    toPostEventCommentsData,
    isPostEventCommentsDataSuccess,
  } = usePostEventComments(
    eventId,
    userRole,
    eventDetailsData?.eventStatus,
    currentCommentCategory || CommentCategory.NOT_FOUND,
    isSendBackComment(),
    nextStatusToBe(
      isSendBackComment(),
      eventDetailsData?.eventStatus,
      currentEventStatus
    ),
    eventCommentsList,
    false
  );

  // allCommentsList returns a list of all_comments object, loading status, and event id
  const allEventComments: Array<IPlanEventCommentDTO> = allCommentsList;

  // to address the case of user always landing on External page whenever they open/close/re-open the component
  useEffect(() => {
    setCurrentCommentCategory(getDeafultCommentCatgry);
  }, [isEventCommentsOpen]);

  useEffect(() => {
    setCurrentCommentCategory(getDeafultCommentCatgry);
    setCommentEditedIndex(-1);
    setCommentHoveredIndex(-1);
  }, []);

  const commentTaskMap = taskType && getTaskCommentMap(taskType);
  const getDeafultCommentCatgry =
    commentTaskMap?.commentCategory || CommentCategory.EXTERNAL;
  // so the time stays up to date
  useEffect(() => {
    const now = new Date();
    const isoTime = now.toISOString();
    setCurrentIsoTime(isoTime);
  }, [
    isPostEventCommentsDataSuccess,
    isPutEventCommentsDataSuccess,
    editCommentTextArea,
    commentTextArea,
  ]);

  useEffect(() => {
    // to turn TextArea edit to regular text once PUT call was successful
    if (isPutEventCommentsDataSuccess) {
      resetEditedAndHoveredStateIndex();
      setIsCommentToBeEdited(false);
      parentCommentUpdater(toPutEventCommentData);
      setEditCommentTextArea(undefined);
      setShouldShowWarning(false);
    }
  }, [isPutEventCommentsDataSuccess]);

  useEffect(() => {
    if (toPostEventCommentsData) {
      parentCommentUpdater(toPostEventCommentsData);
      setCommentTextArea(undefined);
      setShouldShowWarning(false);
      setCommentTextArea("");
      refetchPlanEventData();
    }
  }, [isPostEventCommentsDataSuccess]);

  useEffect(() => {
    if (updatedTaskAlertData) {
      refetchPlanEventData();
    }
  }, [isTaskAlertUpdated]);

  const refetchPlanEventData = () =>
    dispatch(
      doRefetchEventsPlanxAapi({
        isRefetch: _.uniqueId("refetch"),
      })
    );

  // if user changes tabs / currectCommentCategory changes : reset text area
  useEffect(() => {
    setCommentTextArea("");
  }, [currentCommentCategory]);
  useEffect(() => {
    if (!commentTextArea) {
      setCommentsPlaceholder(EVENT_COMMENTS["COMMENT_TEXT_AREA_PLACEHOLDER"]);
    }
  }, [commentTextArea]);

  /**
   * Needed to reset the index for hovered and edited to prevent unwanted logic
   * running.
   */
  const resetEditedAndHoveredStateIndex = () => {
    setCommentHoveredIndex(-1);
    setCommentEditedIndex(-1);
  };

  const handleAddRemoveTask = async (taskId, taskStatus) => {
    await updateTaskAertData({
      URL_PARAMS: [taskId],
      taskId: taskId,
      taskAlertStatus: taskStatus,
    });
  };
  const handleAddNewComment = async () => {
    setDisableCtsBtn(true);
    const commentData = await postComment(commentTextArea);
    if (isSendBackComment() && commentData?.data) {
      dispatch(
        eventDetailsDataHandler({
          eventStatus:
            eventDetailsData?.eventStatus ===
            EEVENT_STATUS.PENDING_WITH_MERCHANT
              ? EEVENT_STATUS.PENDING_WITH_VENDOR
              : EEVENT_STATUS.AGREED_PENDING,
        })
      );
      setTimeout(() => {
        setDisableCtsBtn(false);
      }, 500);
      setIsEventCommentsOpen(false);
    }
    setTimeout(() => {
      setDisableCtsBtn(false);
    }, 500);
  };

  const handleSendEventBack = e => {
    const {
      target: { checked },
    } = e;
    setChecked(checked);
  };

  const handleTabChange = (tabNumber: number): void => {
    resetEditedAndHoveredStateIndex();
    // only update if the user clicks a new category
    switch (tabNumber) {
      case 0:
        if (currentCommentCategory !== CommentCategory.EXTERNAL) {
          setCurrentCommentCategory(CommentCategory.EXTERNAL);
          !isFromComment && setChecked(true);
        }
        break;
      case 1:
        if (currentCommentCategory !== CommentCategory.INTERNAL) {
          setChecked(false);
          setCurrentCommentCategory(CommentCategory.INTERNAL);
        }
        break;
      case 2:
        if (currentCommentCategory !== CommentCategory.BILLING_INQUIRY) {
          setChecked(false);
          setCurrentCommentCategory(CommentCategory.BILLING_INQUIRY);
        }
        break;
      default:
        setCurrentCommentCategory(CommentCategory.EXTERNAL);
        break;
    }
  };

  const isNewOfferOrPromoAdded = () => {
    const draftOffers = getAllowanceDraftCollections(
      eventDetailsData?.planEvent?.offerAllowances,
      "allowances",
      "allowanceStatus"
    );

    const draftPromotionsList = getPromoDraftCollections(
      eventDetailsData?.planEvent?.promotionsList,
      "promotionsList",
      "promotionWorkflowStatus",
      "MERCHANT"
    );
    return draftPromotionsList?.length || draftOffers?.length;
  };

  const showTextArea = (
    currentRole: string,
    textLabel: string,
    tooltipLabel: string,
    roleMaxCharacters: number,
    placeholder = EVENT_COMMENTS["COMMENT_TEXT_AREA_PLACEHOLDER"]
  ) => {
    return (
      <div id="abs-event-comments-section-rbac">
        <RBAC
          // show text area for user to enter comment ONLY if they have edit permission
          divisionIds={divisionIds}
          permissionsOnly={[
            CommentPermissions.EDIT_EXTERNAL,
            CommentPermissions.EDIT_INTERNAL,
            CommentPermissions.EDIT_BILLING,
          ]}
          simsVendors={negotiationSimsVendors}
        >
          <div id="abs-event-comments-section-text-area">
            <TextArea
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                setCommentTextArea(e.target.value);
              }}
              value={commentTextArea}
              className="my-3"
              placeholder={
                commentsPlaceholder ? commentsPlaceholder : placeholder
              }
              label={textLabel}
              tooltip={tooltipLabel}
              tooltipAnchor={"top"}
              tooltipVariant={"light"}
              isRequired={true}
              maxCharacters={roleMaxCharacters}
            ></TextArea>
          </div>
          {userRole === EUSER_ROLES.MERCHANT &&
          currentCommentCategory === CommentCategory?.EXTERNAL &&
          (statuses.includes(currentEventStatus) ||
            statuses.includes(eventDetailsData.eventStatus)) &&
          commentsForOfferPromoFlag() ? (
            <EventCommentsListForOfferAndPromotion
              checked={checked}
              eventDetailsData={eventDetailsData}
              setEventCommentsList={setEventCommentsList}
              setCommentsPlaceholder={setCommentsPlaceholder}
            />
          ) : null}
          {userRole === EUSER_ROLES.MERCHANT &&
          currentCommentCategory === CommentCategory?.EXTERNAL &&
          (statuses.includes(currentEventStatus) ||
            statuses.includes(eventDetailsData.eventStatus)) ? (
            <div className="mb-3" id="abs-event-comments-section-checkbox">
              <Checkbox
                label={EVENT_COMMENTS?.SEND_EVENT_BACK}
                checked={checked}
                disabled={
                  statuses.includes(currentEventStatus) ||
                  statuses.includes(eventDetailsData.eventStatus)
                    ? allowanceEventInd ||
                      (eventDetailsEventInd &&
                        (otherDetailsChangedInd || pidDetailsEventInd)) ||
                      promotionEventInd ||
                      isNewOfferOrPromoAdded()
                    : true
                }
                onChange={event => handleSendEventBack(event)}
              />
              <p className="mt-2" id="abs-event-comments-section-p-event">
                {EVENT_COMMENTS?.SEND_EVENT_BACK_HELPER_TEXT}
              </p>
            </div>
          ) : null}
          <div
            className="divide-y divide-solid"
            id="abs-event-comments-section-button"
          >
            <div
              className="mb-[15px]"
              id="abs-event-comments-section-button-sub"
            >
              <Button
                width={300}
                disabled={
                  (commentTextArea && commentTextArea?.length > 300) ||
                  commentTextArea === undefined ||
                  commentTextArea === "" ||
                  disableCtsBtn
                }
                type="button"
                onClick={handleAddNewComment}
                variant="secondary"
              >
                <div id="abs-event-comments-section-checked">
                  {checked &&
                  userRole === EUSER_ROLES.MERCHANT &&
                  statuses.includes(eventStatusValue) &&
                  currentCommentCategory === CommentCategory?.EXTERNAL
                    ? EVENT_COMMENTS?.ADD_COMMENT_LABEL_FOR_SEND_TO_VENDOR
                    : EVENT_COMMENTS["ADD_COMMENT_BUTTON"]}
                </div>
              </Button>
            </div>
            <div id="abs-event-comments-section-emty-div"></div>
          </div>
        </RBAC>
      </div>
    );
  };

  const showMerchantTabs = () => {
    const tabClassNames = "mr-4 ml-0 ";
    return (
      <Tabs
        transparent={true}
        hideDividers={false}
        hideBorder={true}
        variant={"light"}
        className="content-start mx-4"
        initialTab={commentTaskMap?.commentDefaultTab || 0}
        collapsed={false}
        onChange={handleTabChange}
      >
        <Tab>
          <Tab.Header>{EVENT_COMMENTS["EXTERNAL"]}</Tab.Header>
          <Tab.Content>
            <RBAC
              divisionIds={divisionIds}
              permissionsOnly={[CommentPermissions.VIEW_EXTERNAL]}
              simsVendors={negotiationSimsVendors}
            >
              <div
                className={tabClassNames}
                id="abs-event-comments-section-showtext-area"
              >
                {showTextArea(
                  userRole,
                  EVENT_COMMENTS["VENDOR_MERCHANT_TEXT_AREA_LABEL"],
                  EVENT_COMMENTS["MERCHANT_TOOLTIP"],
                  EVENT_COMMENTS["EXTERNAL_VENDOR_MAX_CHARACTER"]
                )}
              </div>
            </RBAC>
          </Tab.Content>
        </Tab>
        <Tab>
          <Tab.Header>{EVENT_COMMENTS["INTERNAL"]}</Tab.Header>
          <Tab.Content>
            <RBAC
              divisionIds={divisionIds}
              permissionsOnly={[CommentPermissions.VIEW_INTERNAL]}
              simsVendors={negotiationSimsVendors}
            >
              <div
                className={tabClassNames}
                id="abs-event-comments-section-show-text"
              >
                {showTextArea(
                  userRole,
                  EVENT_COMMENTS["INTERNAL_TEXT_AREA_LABEL"],
                  EVENT_COMMENTS["INTERNAL_TOOLTIP"],
                  EVENT_COMMENTS["INTERNAL_MAX_CHARACTER"]
                )}
              </div>
            </RBAC>
          </Tab.Content>
        </Tab>
        <Tab>
          <Tab.Header>{EVENT_COMMENTS["BILLING_INQUIRY"]}</Tab.Header>
          <Tab.Content>
            <RBAC
              divisionIds={divisionIds}
              permissionsOnly={[CommentPermissions.VIEW_BILLING]}
              simsVendors={negotiationSimsVendors}
            >
              <div
                className={tabClassNames}
                id="abs-event-comments-section-show-text-area-userole"
              >
                {showTextArea(
                  userRole,
                  EVENT_COMMENTS["BILLING_INQUIRY"],
                  EVENT_COMMENTS["BILLING_INQUIRY_TOOLTIP"],
                  EVENT_COMMENTS["BILLING_INQUIRY_MAX_CHARACTER"],
                  EVENT_COMMENTS["BILLING_INQUIRY_TEXT_AREA_LABEL"]
                )}
              </div>
            </RBAC>
          </Tab.Content>
        </Tab>
      </Tabs>
    );
  };

  const showVendorTab = () => {
    return showTextArea(
      userRole,
      EVENT_COMMENTS["VENDOR_MERCHANT_TEXT_AREA_LABEL"],
      EVENT_COMMENTS["VENDOR_MERCHANT_TOOLTIP"],
      EVENT_COMMENTS["EXTERNAL_VENDOR_MAX_CHARACTER"]
    );
  };

  const findCategoryIndex = () => {
    return allEventComments?.findIndex(
      findItem => findItem.commentCategory === currentCommentCategory
    );
  };

  const avatar = (
    <User color={stringToHexColorHash(window["OAM_REMOTE_USER"])} />
  );

  const shouldAllowEdit = (currentComment): boolean => {
    return currentComment?.createUserNm === window["OAM_REMOTE_USER"];
  };

  /**
   *
   * @param item_index current index that the cursor is on
   * If user clicks edit, then if the edit comment's index is the same
   * as what the index of where the pointer is at, set the Edit status to be
   * true to show the Edit TExt box later
   */
  const handleEditItemClick = (item_index: number) => {
    setCommentEditedIndex(item_index);
    // if (item_index === commentEditedIndex) {
    //   setIsCommentToBeEdited(true);
    // }
  };

  /**
   *
   * @param item_index current index that the cursor is on
   * @returns the Edit Icon only if the comment is not one to be edited
   * (a comment that's to be edited contains different text NOT and Edit icon)
   */
  const editIcon = (item_index: number, currentComment: any) => {
    return (
      // if they have edit access, they'll be shown the edit icon (But they can only see tabs which they have access to so
      // no need to check if they also have view perms here)
      <RBAC
        divisionIds={divisionIds}
        permissionsOnly={[
          CommentPermissions.EDIT_EXTERNAL,
          CommentPermissions.EDIT_INTERNAL,
          CommentPermissions.EDIT_BILLING,
        ]}
        simsVendors={negotiationSimsVendors}
      >
        {(!isCommentToBeEdited || commentEditedIndex !== item_index) && (
          <div
            id="abs-event-comments-section-shold-allow-edit"
            className={`text-right inline-block ${
              !shouldAllowEdit(currentComment)
                ? "pointer-events-none opacity-50" // this part is risky. I was able to remove pointer none in inspect element and edit someone else's comment
                : ""
            }`}
            onClick={() => {
              if (shouldAllowEdit(currentComment)) {
                // to address the pointer-events-none bypass, added a "hard stop"
                handleEditItemClick(item_index);
              }
            }}
          >
            <Pencil
              className="cursor-pointer text-right inline-block pr-[5px]"
              size={16}
              color="#1B6EBB"
            />
            <p className="text-[#1B6EBB] cursor-pointer inline-block">Edit</p>
          </div>
        )}
        {currentComment?.taskId &&
          showRemoveAddToTaskLinks(currentComment, item_index)}
      </RBAC>
    );
  };
  const showRemoveAddToTaskLinks = (currentComment, item_index) => {
    const { TASK_STATUS_COMMENT_MAPPER } = EVENT_COMMENTS,
      { taskAlertStatus = null, taskId = null } = currentComment,
      { Icon, linkText,actionButtonText, taskStatusInApi } =
        TASK_STATUS_COMMENT_MAPPER[taskAlertStatus];
    return (
      <RBAC
        divisionIds={divisionIds}
        permissionsOnly={[
          CommentPermissions.EDIT_EXTERNAL,
          CommentPermissions.EDIT_INTERNAL,
          CommentPermissions.EDIT_BILLING,
        ]}
        simsVendors={negotiationSimsVendors}
      >
        <div
          id="abs-event-comments-section-point-event"
          className={`text-right inline-block ml-2 ${
            isCommentToBeEdited && commentEditedIndex === item_index
              ? "pointer-events-none opacity-50"
              : ""
          }`}
          onClick={() => {
            if (!(isCommentToBeEdited && commentEditedIndex === item_index)) {
              // same security check if user inspect elements and remove pointer none.
              handleAddRemoveTask(taskId, taskStatusInApi);
            }
          }}
        >
          <Icon
            color="#1B6EBB"
            size={20}
            className="cursor-pointer text-right inline-block font-normal pb-1"
          />
          <p
            className="text-[#1B6EBB] font-normal text-[13px] cursor-pointer inline-block"
            id="abs-event-comments-section-link-text"
          >
            {actionButtonText}
          </p>
        </div>
      </RBAC>
    );
  };
  /**
   *
   * @param cur_comment The current comment the pointer is on
   * @param item_index the current index the pointer is on (from the list of timeline events)
   * @returns a component containing Cancel | Update button for user to interact with
   */
  const editIconOpen = (
    cur_comment: IPlanEventCommentProps,
    item_index: number
  ) => {
    return (
      <div
        className="inline-block text-right"
        id="abs-event-comments-section-cancel-div"
      >
        <div
          className="inline-block"
          id="abs-event-comments-section-cancel-sub"
        >
          <p
            id="abs-event-comments-section-cancel-p"
            className="text-[#000000] inline-block cursor-pointer"
            onClick={() => {
              resetEditedAndHoveredStateIndex();
              setIsCommentToBeEdited(false);
            }}
          >
            Cancel
          </p>
          <Divider className="inline-block mx-2" height={15} />
          <p
            id="abs-event-comments-section-update-p"
            className="text-[#1B6EBB] inline-block cursor-pointer"
            onClick={() => handleUpdateComment(cur_comment)}
          >
            Update
          </p>
        </div>
      </div>
    );
  };

  const parseUserNamesForAtSigns = (textToCheck: string) => {
    const regex = /@(\w+)/g;
    const matches = textToCheck?.match(regex);
    const usernames = matches ? matches?.map(match => match.slice(1)) : [];
    return usernames;
  };

  /**
   *
   * @param cur_comment the current comment that is to be updated
   * From the current comment, we create a new object of comment and
   * fill it appropriately and send for PUT request.
   *
   */
  const handleUpdateComment = (cur_comment: IPlanEventCommentProps): void => {
    if (
      !isPutEventCommentsDataLoading &&
      editCommentTextArea &&
      editCommentTextArea?.length <= 300
    ) {
      const eventID = eventId;
      const commentID = cur_comment.id;
      const cloneComment: IPlanEventCommentProps = { ...cur_comment };
      cloneComment.messageText =
        encodeURIComponent(editCommentTextArea) || "ERROR";
      cloneComment.lastUpdTs = currentIsoTime || "";
      cloneComment.lastUpdUserId = window["OAM_REMOTE_USER"] || "TESTUSER";
      cloneComment.notifiedUsers = parseUserNamesForAtSigns(
        editCommentTextArea || ""
      );
      const eventCommentBody: IPlanEventCommentDTO = {
        planEvent: eventId,
        commentCategory: currentCommentCategory || CommentCategory.NOT_FOUND,
        eventComments: [cloneComment],
        createUserId: window["OAM_REMOTE_USER"] || "TESTUSER",
        createTs: currentIsoTime || "",
        requestForChange: true,
      };

      putEventCommentsData({
        URL_PARAMS: [eventID, commentID],
        ...eventCommentBody,
      });
    }
  };
  /**
   *
   * @param cur_comment current comment that the cursor is on
   * @param item_indx current index that the cursor is on
   * @returns a JSX element containing a EDIT text area.
   * The else is there as to not make other comments show a text box as well
   */
  const showEditTextArea = (
    cur_comment: IPlanEventCommentProps,
    item_indx: number
  ): JSX.Element => {
    // first condition will address the user only having the selected comment showing the edit box
    // second condition will address the case of if user mouse enters a new box, the previous edit box should
    // still persist.
    const messageText = getDecodedValue(cur_comment.messageText);
    if (commentEditedIndex === item_indx) {
      return (
        <div id="abs-event-comments-section-text-area-lenght">
          <TextArea
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              e?.preventDefault();
              e?.stopPropagation();
              setEditCommentTextArea(e.target.value);
            }}
            maxCharacters={EVENT_COMMENTS["EXTERNAL_VENDOR_MAX_CHARACTER"]}
            error={editCommentTextArea && editCommentTextArea.length > 300}
            value={messageText}
          ></TextArea>
        </div>
      );
    } else {
      return (
        <div id="abs-event-comments-section-message-text">{messageText}</div>
      );
    }
  };

  /**
   *
   * @returns each event's timeline with the username, timestamps and the
   * text/EditTextArea. Depending on the current category selected from tab, we
   * map thru the appropriate category and show the edit icon on hover for each of the
   * elements
   */
  const showCommentsTimeline = (): JSX.Element => {
    return (
      <div className="break-words" id="abs-event-comments-section-edited">
        {allEventComments?.length > 0
          ? allEventComments?.[findCategoryIndex()]?.eventComments?.map(
              (currentComment, indx) => {
                const createUserNm = currentComment?.createUserNm;
                const createTs = formatDateTimeWithZone(
                  currentComment?.createTs
                );
                const editedLabel =
                  currentComment?.lastUpdTs !== currentComment?.createTs
                    ? "EDITED"
                    : "";

                const commentLabel = (
                  <>
                    <span className="block">{createUserNm}</span>
                    <span className="block">{`${createTs} ${editedLabel}`}</span>
                  </>
                );
                const messageText = getDecodedValue(
                  currentComment?.messageText
                );
                return (
                  <div
                    id="abs-event-comments-section-set-comment-hovered-index"
                    key={currentComment?.createTs}
                    onMouseEnter={() => {
                      if (!isCommentToBeEdited) {
                        setCommentHoveredIndex(indx);
                      }
                    }}
                  >
                    <Timeline.Item
                      label={commentLabel}
                      optionalStatus={editIcon(indx, currentComment)}
                      icon={avatar}
                      divider={true}
                      key={currentComment?.createTs}
                    >
                      <div
                        className="my-3 mx-4"
                        id="abs-event-comments-section-iscomment"
                      >
                        <div id="abs-event-comments-section-iscomment-index">
                          {!isCommentToBeEdited &&
                          indx !== commentEditedIndex ? (
                            <span id="abs-event-comments-section-iscomment-text">
                              <div
                                className="text-right"
                                id="abs-event-comments-section-split"
                              ></div>
                              <div id="abs-event-comments-section-message-text">
                                {messageText
                                  .split(/(?:\r\n|\r|\n)/g)
                                  .map((line, index) => {
                                    return (
                                      <p key={index} className="max-w-[550px]">
                                        {line}
                                      </p>
                                    );
                                  })}
                                {currentComment?.sendBackWithComment ? (
                                  <div
                                    className="flex items-center"
                                    id="abs-event-comments-section-info"
                                  >
                                    <Info color="#AB422D" size={15} />
                                    <span
                                      className="text-[#AB422D] text-sm ml-1"
                                      id="abs-event-comments-section-info-span"
                                    >
                                      {EVENT_COMMENTS?.EVENT_SENT_BACK_FLAG}
                                    </span>
                                  </div>
                                ) : null}
                              </div>
                            </span>
                          ) : (
                            <div id="abs-event-comments-section-comment-edited-index">
                              <div
                                className="text-right"
                                id="abs-event-comments-section-comment-edited-index"
                              >
                                {commentEditedIndex === indx &&
                                  editIconOpen(currentComment, indx)}
                              </div>
                              {showEditTextArea(currentComment, indx)}
                            </div>
                          )}
                        </div>
                      </div>
                    </Timeline.Item>
                  </div>
                );
              }
            )
          : "No Event Comments Found"}
      </div>
    );
  };

  const warningModal = () => {
    return (
      <Modal
        isOpen={shouldShowWarning}
        onClose={() => setShouldShowWarning(false)}
        height={360}
      >
        <div
          className="flex flex-col gap-[16px] mx-[56px] pt-12"
          id="abs-event-comments-section-you-have"
        >
          <div
            className="text-center select-none font-bold text-[28px] text-[#2B303C]"
            id="abs-event-comments-section-you-have-unsaved"
          >
            You have unsaved comments!
          </div>
          <div
            className="text-center select-none text-xl mt-4"
            id="abs-event-comments-section-un-saved"
          >
            <span
              className="text-[20px] text-[#bf2912]"
              id="abs-event-comments-section-wroning"
            >
              Warning:{" "}
            </span>
            Unsaved comments will be lost if you close without saving
          </div>
          <div
            className="text-center select-none text-xl mt-4"
            id="abs-event-comments-section-closing"
          >
            Do you want to save them before closing?
          </div>
          <div
            className="flex items-center justify-center w-full mt-[16px]"
            id="abs-event-comments-section-closing-btton"
          >
            <Button
              width={200}
              size="lg"
              className="mr-2 whitespace-nowrap rounded-lg"
              variant="secondary"
              onClick={() => {
                setIsEventCommentsOpen(false);
                setCommentTextArea(undefined);
                setEditCommentTextArea(undefined);
                setShouldShowWarning(false);
                setCurrentCommentCategory(CommentCategory.EXTERNAL);
                setCommentOpen(false);
              }}
            >
              Close without saving
            </Button>
            <Button
              width={216}
              size="lg"
              className="ml-2 whitespace-nowrap"
              onClick={() => {
                if (commentTextArea && commentTextArea !== "") {
                  handleAddNewComment();
                }
                setIsEventCommentsOpen(false);
                setCommentTextArea(undefined);
                setEditCommentTextArea(undefined);
                setShouldShowWarning(false);
                setCommentOpen(false);
              }}
            >
              Save and Close
            </Button>
          </div>
        </div>
      </Modal>
    );
  };

  const ref = useRef<HTMLDivElement>(null);
  useEffect(() => {
    function handleClickOutside(event) {
      // if click outside target:
      if (ref.current && !ref.current.contains(event.target as Node)) {
        if (commentTextArea) {
          setShouldShowWarning(true);
        }
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [commentTextArea, editCommentTextArea]);

  const isOpenCheck = e => {
    setCommentOpen(e);
    setIsEventCommentsOpen(shouldShowWarning);
    setCommentEditedIndex(-1);
    setCommentHoveredIndex(-1);
  };
  
  return (
    <div id="abs-event-comments-section-drawer-righ">
      <Drawer
        anchor="right"
        isOpen={isEventCommentsOpen}
        setOpen={event => {
          isOpenCheck(event);
        }}
        header={
          <p
            className="text-[#2B303C] leading-6 text-[18px] font-extrabold"
            id="abs-event-comments-section-event-p"
          >
            {EVENT_COMMENTS["COMMENTS_SECTION_HEADER"]}
          </p>
        }
        hideBackdrop={false}
        width="700px"
      >
        {userRole === EUSER_ROLES.VENDOR ? (
          <div
            className={`${
              userRole === EUSER_ROLES.MERCHANT ? "mx-4" : ""
            } text-[14px] text-[#1b6ebb]`}
          >
            <i>
              <b>Note: </b>
              {EVENT_COMMENTS_NOTE_TEXT?.text1}
              <b>{EVENT_COMMENTS_NOTE_TEXT?.text2}</b>
              {EVENT_COMMENTS_NOTE_TEXT?.text3}
            </i>
          </div>
        ) : null}

        <div id="abs-event-comments-section-user-role-grid">
          <div
            ref={ref}
            className="grid grid-cols-1 divide-y-3"
            id="abs-event-comments-section-user-role-grid-d"
          >
            <div id="abs-event-comments-section-user-role-grid-div">
              {userRole === EUSER_ROLES.MERCHANT
                ? showMerchantTabs()
                : showVendorTab()}
            </div>
            <div id="abs-event-comments-section-user-role-grid-time">
              <Timeline
                className="mx-[-24px]"
                loading={allEventComments ? false : true}
              >
                <div id="abs-event-comments-section-showcoment">
                  {showCommentsTimeline()}
                </div>
              </Timeline>
            </div>
          </div>
        </div>
      </Drawer>
      {shouldShowWarning && warningModal()}
    </div>
  );
};
export default EventCommentsSection;
