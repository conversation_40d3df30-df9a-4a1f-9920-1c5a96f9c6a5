import "moment-timezone";
import moment from "moment";
import {
  CT<PERSON>_Keys,
  CTAs_Keys_Variant,
  CTAs_label,
  EEVENT_STATUS,
  MerchantRoles,
  VendorEditRoles,
  useGetAppBasePath,
} from "@me/util-helpers";
import { appConstants } from "@me/utils-root-props";

enum EUSER_ROLES {
  MERCHANT = "MERCHANT",
  VENDOR = "VENDOR",
}
const SAFEWAY_UPPER = "SAFEWAY";
const CIC = "CIC";
const CONVERSION = "conversion";
export const PROMO_POWER_ADMIN_VENDOR_ID = "002550";

export const VendorViewerRoles = [
  "az-meupp-nonprod-promoextviewer",
  "az-meupp-prod-promoextviewer",
  "az-meupp-nonprod-promonatextviewer",
  "az-meupp-prod-promonatextviewer",
];

export const formatTimestamp = ({
  timestamp,
  pattern,
  isApi,
  isFormat,
  timestampConvert = false,
}: any) => {
  //Formats timestamp to date in the provided format in UTC

  //let utcTs = moment.utc(timestamp);
  const utcTs = timestampConvert
    ? moment(new Date(timestamp))
    : moment.utc(timestamp);
  if (isApi || isFormat) {
    pattern = "yyyy-MM-DD";
    const date = timestampConvert
      ? utcTs.format(pattern).toString()
      : utcTs.startOf("day").format(pattern);
    return `${date}`;
    // format(date, pattern);
    //For API, format is '2022-09-10T00:00:00.000Z'
  } else {
    //date = new Date(timestamp);
    return utcTs.format(pattern);
    //  return format(timestamp, pattern);
  }
};

export const getCurrentDateAndTime = (
  datePattern = "MM-DD-YYYY",
  timePattern = "HH-mm-ss"
) => {
  return `${formatTimestamp({
    timestamp: Date.now(),
    pattern: datePattern,
  })}_${moment().format(timePattern)}`;
};

export const formatDate = (date: any, pattern = "MM/DD/YY") => {
  const currentDate = new Date(moment(date).format("MM/DD/YY"));
  return currentDate.setHours(0, 0, 0, 0);
};

export const getNationalOfferStatus = allowances => {
  const isValidStatusObj: any = allowances?.find(
    (allow: any) =>
      ![EEVENT_STATUS.CANCELED, EEVENT_STATUS.REJECTED].includes(
        allow?.allowanceStatus
      )
  );
  const allowanceStatus =
    isValidStatusObj?.allowanceStatus || allowances?.[0]?.allowanceStatus;

  const statusPriorityQueue = {
    [EEVENT_STATUS.AGREED_PENDING]: 4,
    [EEVENT_STATUS.AGREED]: 3,
    [EEVENT_STATUS.EXECUTED]: 2,
    [EEVENT_STATUS.ACTIVE]: 1,
  };
  let priorityObj = { key: "", value: 0 };
  // Get offer status based on priority when allowances have different status.
  if (Object.keys(statusPriorityQueue).includes(allowanceStatus)) {
    allowances?.forEach((allow: any) => {
      if (
        statusPriorityQueue?.[allow?.allowanceStatus || ""] > priorityObj.value
      )
        priorityObj = {
          key: allow?.allowanceStatus,
          value: statusPriorityQueue?.[allow?.allowanceStatus],
        };
    });
  }

  return priorityObj?.key || allowanceStatus;
};

export const getKeyByValue = (object, value) => {
  return Object.keys(object).find(key => object[key] === value);
};

export const getAssetsPath = ({ nodePath, localPath }) => {
  return isValidUrl(nodePath) ? nodePath : localPath;
};

export const checkValidDate = date => {
  if (
    !date ||
    date?.year === undefined ||
    date?.month === undefined ||
    date?.day === undefined
  ) {
    return "";
  }
  return `${date.year}-${date.month}-${date.day}`;
};

const isValidUrl = urlString => {
  try {
    return Boolean(new URL(urlString));
  } catch (e) {
    return false;
  }
};

export const getLoggedInUserType = () => {
  const userEmail = localStorage.getItem("USER_EMAIL") || window["USER_EMAIL"];
  const userType: any = localStorage.getItem("USER_TYPE");
  const isMerchant = userEmail?.toUpperCase().includes(SAFEWAY_UPPER);
  return !userEmail
    ? userType
    : isMerchant
    ? EUSER_ROLES.MERCHANT
    : EUSER_ROLES.VENDOR;
};

const OWN_BRANDS_ID = "001112";

export const isOwnBrandsVendor = () => {
  const vendorNumberArray: string[] = JSON.parse(
    localStorage.getItem("vendorNum") || "[]" // to deal with undefined case
  );
  return vendorNumberArray.some(vendNo => vendNo === OWN_BRANDS_ID);
};

export const isFeatureFlagEnabled = (featureFlag: string) => {
  const featureFlags = JSON.parse(
    localStorage.getItem("FEATURE_FLAGS") || "[]"
  );
  return featureFlag &&
    featureFlags?.find(
      flagObj => flagObj?.featureName === featureFlag && flagObj?.enabled
    )
    ? true
    : false;
};

export const isAllowanceFeatureEnabled = isFeatureFlagEnabled(
  appConstants?.FEATURE_FLAGS?.ALLOWANCE_TYPE_CHANGE
);
export const isSortFeatureFlagEnabled = () => {
  return isFeatureFlagEnabled(
    appConstants.FEATURE_FLAGS.ALL_SCREEN_FILTER_CHANGES
  );
};

export const getDateByFormat = (date: string, format = "") => {
  return moment(date).format(format);
};

export const sortListByKey = (inputList, sortByKey) => {
  return sortByKey
    ? inputList
        ?.slice()
        ?.sort((a, b) =>
          a[sortByKey] > b[sortByKey] ? 1 : b[sortByKey] > a[sortByKey] ? -1 : 0
        )
    : inputList;
};
export const isVendorLoggedIn = () => {
  return getLoggedInUserType() === "VENDOR";
};
export const isMerchantLoggedIn = () => {
  return getLoggedInUserType() === "MERCHANT";
};

export function debounce(func, timeout = 300) {
  let timer;
  return () => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      func();
    }, timeout);
  };
}

export const numberOnlyStringCheck = (value, isSpaceAllowed) => {
  // const numbersWithSpacesRegexPattern = /^[0-9 _ ,]*$/; //Allows spaces and commas as prefix to number.
  const numbersWithSpacesRegexPattern = /^(?=.*\d)[\d ,]+$/;
  const numberOnlyRegexPattern = /^\s*\d+\s*$/;
  return isSpaceAllowed
    ? numbersWithSpacesRegexPattern.test(value)
    : numberOnlyRegexPattern.test(value);
};
export const checkEmptyValueForEvent = value => {
  if (!value.trimStart()) return false;
  return true;
};
export const splitRemoveArr = str => {
  return str.split(",").map(item => item.trim());
};
export const convertToArray = (stringVal, eventNameId) => {
  return eventNameId === "eventNames"
    ? splitRemoveArr(stringVal)
    : stringVal
        ?.trim()
        ?.split(/(?:,| )+/)
        .filter(item => item?.trim());
};

export const splitEventIdString = (stringVal, eventNameId) => {
  return convertToArray(stringVal, eventNameId);
};

export const getRoleForPowerUser = (vendorRoles, roles) => {
  return vendorRoles?.some(role => roles?.includes(role));
};
// Utility function to show add event button if at least one division id available
export const showAddEventCTA = (
  divisionIds: string[] = [],
  simsVendors: string[] = []
) => {
  const permissionsData = localStorage.getItem("USER_ROLE_PERMISSIONS");
  const userDetails = permissionsData ? JSON.parse(permissionsData) : [];
  const vendNumsList = JSON.parse(localStorage.getItem("vendorNum") || "[]");
  const simsVendorList = simsVendors?.flat(Infinity);
  const isPromoPowerAdminVendorId = vendNumsList.includes(
    PROMO_POWER_ADMIN_VENDOR_ID
  );
  if (isPromoPowerAdminVendorId) {
    const userRoles = userDetails.map(item => item.userRoles)?.flat(Infinity);
    if (getRoleForPowerUser(VendorEditRoles, userRoles)) {
      return true;
    } else if (getRoleForPowerUser(VendorViewerRoles, userRoles)) {
      return false;
    }
  }

  const roles = [...MerchantRoles, ...VendorEditRoles];
  let filteredDivisionIds: any[] = [];
  let allRoles: string[] = [];
  if (divisionIds?.length) {
    if (isVendorLoggedIn()) {
      userDetails?.forEach(item => {
        const userDivisionIds = item?.userDivisions.map(d =>
          d.divisionId?.trim()
        );
        if (
          simsVendorList?.includes(item.vendorNumber) &&
          divisionIds?.some(d => userDivisionIds?.includes(d))
        ) {
          allRoles = Array.from(
            new Set([...(allRoles || []), ...(item?.userRoles || [])])
          );
        }
      });
      return roles.some(role => allRoles.includes(role));
    } else {
      filteredDivisionIds =
        divisionIds?.filter(divisionId => {
          return roles.some(role =>
            userDetails?.some(
              item =>
                item.userRoles?.includes(role) &&
                item?.userDivisions?.find(div => div.divisionId === divisionId)
            )
          );
        }) || [];
    }
  } else {
    filteredDivisionIds =
      userDetails?.length &&
      userDetails?.filter(item => {
        return (
          roles.some(role => item.userRoles?.includes(role)) &&
          item?.userDivisions?.[0]?.divisionId
        );
      });
  }
  return !!filteredDivisionIds?.length;
};

export const getDivisionIds = (selectedEvents, planningData) => {
  return [
    ...new Set(
      selectedEvents
        ?.flatMap(id =>
          planningData?.eventTaskProductGroups?.filter(item => item?.id === id)
        )
        ?.flatMap(item => item?.divisionIds)
    ),
  ];
};
export const isMerchantLoggedin = () =>
  getLoggedInUserType() === EUSER_ROLES?.MERCHANT;

export const isUserComingFromTaskPage = taskType =>
  isMerchantLoggedIn() && taskType === appConstants.TASK_TYPE_ALLOWANCE_INQUIRY;

export const isComingFromtask = (taskType, eventDetailsData?) => {
  const {
      planEventTasks = [],
      planEvent,
      eventStatus,
    } = eventDetailsData || {},
    tasksList = planEventTasks,
    isPendingWithMerchantOrAgreedPending = [
      EEVENT_STATUS.AGREED_PENDING,
      EEVENT_STATUS.PENDING_WITH_MERCHANT,
    ].includes(eventStatus);
  return (
    isVendorLoggedIn() &&
    taskType === appConstants.TASK_TYPE_NEW_ITEM &&
    tasksList?.some(
      task =>
        task?.subType === appConstants.NEW_ITEM_TEXT &&
        task?.taskAlertStatus?.toLowerCase() === "pending"
    ) &&
    !isPendingWithMerchantOrAgreedPending
  );
};

export const removeBracketsAndKeepValue = (input: string): string => {
  return input.replace(/\[(\d+)\]/g, "$1");
};

export const getObjectValueFromString = (obj, objKey) => {
  const keys = objKey?.split(".");
  return keys?.reduce((acc, curr) => {
    if (curr?.includes("[")) curr = removeBracketsAndKeepValue(curr);
    return acc && acc?.[curr] ? acc[curr] : undefined;
  }, obj);
};

export const removeConversionAndDynamicPPGS = arr => {
  return arr?.filter(
    item =>
      !item?.name?.toLowerCase()?.includes(CONVERSION) &&
      item?.productGroupType !== CIC
  );
};
export const customSortFn = (a, b, sortByKey) => {
  if (!sortByKey) return 0;
  return a?.[sortByKey] > b?.[sortByKey]
    ? 1
    : b?.[sortByKey] > a?.[sortByKey]
    ? -1
    : 0;
};

/**
 * Function to sort descriptive size. Sorts first based on number then on units. Important for cases of having
 * mixed size units such as 10 FZ, 2 OZ, and 4 EA
 *
 * @param i1 size string of item 1
 * @param i2 size string of item 2
 *
 * @returns a number to sort items based on number then unit.
 */
export const sortSize = (i1: string, i2: string) => {
  const i1SizeSplit = i1?.split(" ");
  const i2SizeSplit = i2?.split(" ");

  if (parseInt(i1SizeSplit?.[0]) < parseInt(i2SizeSplit?.[0])) {
    return -1;
  } else if (parseInt(i1SizeSplit?.[0]) > parseInt(i2SizeSplit?.[0])) {
    return 1;
  } else {
    return i1SizeSplit?.[1].localeCompare(i2SizeSplit?.[1]);
  }
};

export const sortUpcsByLength = (a, b) => {
  const caseUpc = a?.caseUpc,
    caseUpcs = a?.itemUpcs?.filter(itemUpc => itemUpc == caseUpc),
    caseUpcsLength =
      typeof a?.caseUpc == "object" ? a?.caseUpc?.length : caseUpcs?.length;
  const caseUpcNext = b?.caseUpc,
    caseUpcsNext = b?.itemUpcs?.filter(itemUpc => itemUpc == caseUpcNext),
    caseUpcsLengthNext =
      typeof b?.caseUpc == "object" ? b?.caseUpc?.length : caseUpcsNext?.length;
  return caseUpcsLength - caseUpcsLengthNext;
};

export const INFO_GRAPHICS = {
  EVENTS_INFO:
    "Events: Events are shown based on selected filters \n that consist of Allowances and/or Promotions.",
  CALENDER_INFO: "Expand to calendar",
  MINIMIZE_CAL: "Minimize calendar",
  EVENT: "Status Workflow",
  UOM: {
    unitMeasure: "UOM: Unit Type.",
  },
  EVENT_DETAILS_INFO: {
    planProductGroups: {
      INFO_GRAPHICS_LABEL:
        "Promo Product Groups: Grouping of items managed by Merchant.",
    },

    storeGroups: {
      INFO_GRAPHICS_LABEL:
        "Store Groups: Grouping of locations managed by Albertsons.",
    },
  },
  Draft: {
    INFO_GRAPHICS_LABEL:
      "Draft: This draft event has not yet been shared and is only visible to creator.",
  },
  "Pending With Vendor": {
    INFO_GRAPHICS_LABEL:
      "Pending With Vendor - Event has been sent to vendor for review.",
  },
  "Pending With Merchant": {
    INFO_GRAPHICS_LABEL:
      "Pending With Merchant - Event has been sent to merchant for review.",
  },
  Rejected: {
    INFO_GRAPHICS_LABEL: "Rejected - Event is no longer editable.",
  },
  Agreed: {
    INFO_GRAPHICS_LABEL:
      "Agreed - Event is agreed upon by merchant and vendor.",
  },
  "Agreed-Pending": {
    INFO_GRAPHICS_LABEL:
      "Agreed-Pending - Event is pending change for other party to review.",
  },
  Canceled: {
    INFO_GRAPHICS_LABEL: "Canceled - Event is no longer editable.",
  },
  Active: {
    INFO_GRAPHICS_LABEL: "Active - Event is currently active.",
  },
  Executed: {
    INFO_GRAPHICS_LABEL: "Executed - Event has been executed.",
  },
  "Vehicle Type/Custom Date": {
    INFO_GRAPHICS_LABEL:
      "Vehicle Type/Custom Date: Albertsons vehicles used to derive total event date.",
  },
  PROMOTION_INFO: {
    itemLimit: {
      INFO_GRAPHICS_LABEL: "Item Limit: Maximum purchase per transaction.",
    },
    factor: {
      INFO_GRAPHICS_LABEL: "Factor: Number of units for a pricing multiple.",
    },
    amount: {
      INFO_GRAPHICS_LABEL: "Amount: Promotional retail for factor amount.",
    },
    minQuantity: { INFO_GRAPHICS_LABEL: "Minimum Quantity: Defaults to 1." },

    unitMeasure: { INFO_GRAPHICS_LABEL: "UOM: Unit Type.6" },
    promotionType: {
      INFO_GRAPHICS_LABEL:
        "Select 'Custom' if the desired promo type is not listed",
    },
  },
};
export const warningIcon = (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0.940166 23.998C4.62581 23.9988 8.31186 23.9984 11.9975 23.9984C14.4273 23.9984 16.857 23.9988 19.2868 23.9988C20.5567 23.9988 21.8267 24.0024 23.0967 23.9971C23.8253 23.9944 24.2204 23.267 23.8713 22.589C23.7387 22.3312 23.6107 22.0712 23.4806 21.8121C22.3615 19.5847 21.2427 17.3569 20.1235 15.13C18.8999 12.6956 17.6755 10.2616 16.4519 7.82723C15.235 5.40563 14.0193 2.9836 12.8011 0.562888C12.7493 0.46048 12.6872 0.358514 12.6109 0.276852C12.193 -0.170741 11.5183 -0.0630362 11.2283 0.509035C10.3113 2.31839 9.40248 4.13216 8.49163 5.9446C7.21006 8.49509 5.92933 11.046 4.64817 13.5969C3.13631 16.6074 1.6232 19.6169 0.113817 22.6291C0.0566555 22.7426 0.0135777 22.875 0.00363663 23.0021C-0.0423407 23.5822 0.351987 23.998 0.940166 23.998Z"
      fill="#e51010"
    ></path>
    <path d="M13 16H11V9H13V16Z" fill="#FFF"></path>
    <path
      d="M13 19.995C12.625 19.995 12.2602 19.995 11.8955 19.995C11.6406 19.995 11.3857 19.9943 11.1308 19.9953C11.0586 19.9956 10.9997 19.9933 11 19.8943C11.002 19.267 11.001 18.6393 11.001 18H12.9997V19.9946L13 19.995Z"
      fill="#FFF"
    ></path>
  </svg>
);
const enums = {
  EUSER_ROLES: EUSER_ROLES,
  EEVENT_STATUS: EEVENT_STATUS,
  CTAs_Keys: CTAs_Keys,
  CTAs_label: CTAs_label,
  CTAs_Keys_Variant: CTAs_Keys_Variant,
};

export const warningIconMaroon = (
  <svg
    data-testid="warning-icon"
    width="16"
    height="16"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0.940166 23.998C4.62581 23.9988 8.31186 23.9984 11.9975 23.9984C14.4273 23.9984 16.857 23.9988 19.2868 23.9988C20.5567 23.9988 21.8267 24.0024 23.0967 23.9971C23.8253 23.9944 24.2204 23.267 23.8713 22.589C23.7387 22.3312 23.6107 22.0712 23.4806 21.8121C22.3615 19.5847 21.2427 17.3569 20.1235 15.13C18.8999 12.6956 17.6755 10.2616 16.4519 7.82723C15.235 5.40563 14.0193 2.9836 12.8011 0.562888C12.7493 0.46048 12.6872 0.358514 12.6109 0.276852C12.193 -0.170741 11.5183 -0.0630362 11.2283 0.509035C10.3113 2.31839 9.40248 4.13216 8.49163 5.9446C7.21006 8.49509 5.92933 11.046 4.64817 13.5969C3.13631 16.6074 1.6232 19.6169 0.113817 22.6291C0.0566555 22.7426 0.0135777 22.875 0.00363663 23.0021C-0.0423407 23.5822 0.351987 23.998 0.940166 23.998Z"
      fill="#9D2210"
    ></path>
    <path d="M13 16H11V9H13V16Z" fill="#FFF"></path>
    <path
      d="M13 19.995C12.625 19.995 12.2602 19.995 11.8955 19.995C11.6406 19.995 11.3857 19.9943 11.1308 19.9953C11.0586 19.9956 10.9997 19.9933 11 19.8943C11.002 19.267 11.001 18.6393 11.001 18H12.9997V19.9946L13 19.995Z"
      fill="#FFF"
    ></path>
  </svg>
);

export const getEnumValue = enumString => {
  const [enumName, enumValue] = enumString.split(".");
  return enums[enumName] ? enums[enumName][enumValue] : "";
};

export const isOfferAttachmentFlagEnabled = () =>
  isFeatureFlagEnabled(appConstants.FEATURE_FLAGS.OFFER_ATTACHMENT_CHANGES);
export const commentsForOfferPromoFlag = () =>
  isFeatureFlagEnabled(
    appConstants.FEATURE_FLAGS.COMMENTS_FOR_OFFER_PROMO_LIST
  );
export const isRenderPromosInComments = () =>
  isFeatureFlagEnabled(appConstants.FEATURE_FLAGS.RENDER_PROMOS_IN_COMMENTS);
export const periscopeIdChangesEnabled = () =>
  isFeatureFlagEnabled(appConstants.FEATURE_FLAGS.PERISCOPE_ID_CHANGES);
export const nationalEventsFlagEnabled = () =>
  isFeatureFlagEnabled(appConstants.FEATURE_FLAGS.NATIONAL_EVENTS);
export const isPromoChanges = () =>
  nationalEventsFlagEnabled()
    ? localStorage.getItem("EVENT_TYPE")
      ? localStorage.getItem("EVENT_TYPE") === "NCDP"
      : true
    : isFeatureFlagEnabled(appConstants.FEATURE_FLAGS.PROMO_CHANGES);
export const useGetAppBaseNationalIcon = () => {
  const { baseDomain } = useGetAppBasePath();
  return `${baseDomain}assets/icons/national-icon/national-icon.svg`;
};

export const getLoggedInUserFromStorage = () => {
  const oamFromStorage = localStorage.getItem("OAM_REMOTE_USER");
  return oamFromStorage ? JSON.parse(oamFromStorage)?.toUpperCase() : "";
};
export const addYearIfNotExists = (yearOptions, startDate) => {
  const existingYear = yearOptions?.find(option => option?.name === startDate);

  if (!existingYear) {
    yearOptions?.push({ id: yearOptions?.length + 1, name: startDate });
  }

  return yearOptions;
};

export const getYearFromDate = date => {
  const convertDate = new Date(date);
  return convertDate.getFullYear();
};
export const SharedWhseIcon = ({ customClass = "" }) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 100 100"
      xmlns="http://www.w3.org/2000/svg"
      className={customClass}
    >
      <rect width="100" height="100" fill="#A0491C" rx="20" ry="20" />
      <text
        x="50%"
        y="60%"
        dominant-baseline="middle"
        text-anchor="middle"
        font-size="80"
        fill="#FFFFFF"
        font-family="Nunito, sans-serif"
      >
        S
      </text>
    </svg>
  );
};

export const hasCreateIndChange = (
  eventDetailsData,
  isAllowanceFeatureEnabled,
  offerNumber?
) => {
  const { planEventPendingChanges, allowanceEventInd, offerAllowances } =
    eventDetailsData || {};

  if (!isAllowanceFeatureEnabled || !allowanceEventInd) {
    return false;
  }

  return offerAllowances?.some(offer => {
    const offerNumberData = offerNumber ? offerNumber : offer?.offerNumber;

    const matchingOffer = planEventPendingChanges?.offerAllowanceChanges?.find(
      pendingOffer => pendingOffer?.offerNumber === offerNumberData
    );
    return matchingOffer?.changes?.some(
      change => change?.labelFieldName === "allowanceType"
    );
  });
};
