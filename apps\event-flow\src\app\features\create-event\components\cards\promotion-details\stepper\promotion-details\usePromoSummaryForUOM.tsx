import { useEffect, useRef, useState } from "react";
import {
  usePostAllowanceForUomInPromoMutation,
  usePostAllowanceStartDateDataMutation,
} from "../../../../../service/apis/allowance-api";
import { useSelectorWrap } from "@me/data-rtk";
import { useFormContext } from "react-hook-form";

type Props = {
  cardIndex: number;
  cardItemIndex: number;
};

export default function usePromoSummaryForUOM({
  cardIndex,
  cardItemIndex,
}: Props) {
  const { getValues } = useFormContext();

  const offerAllowancesGroup = "DSD_WHSE_RETAIL_DIVISION";
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");

  const endDate = getValues(
    `promotionsLists[${cardIndex}].promotionsList[${cardItemIndex}].vehicle.endDate`
  );
  const startDate = getValues(
    `promotionsLists[${cardIndex}].promotionsList[${cardItemIndex}].vehicle.startDate`
  );

  const eventId = eventDetailsData?.id;
  const division = eventDetailsData?.divisionIds;

  const [uomValue, setUomValue] = useState("");

  const [
    postStartDateData,
    { data: planEventResponse = null, isSuccess: isPlanEventSuccess },
  ] = usePostAllowanceStartDateDataMutation();

  const [postAllowanceForUom, { data, isLoading }] =
    usePostAllowanceForUomInPromoMutation();

  const hasMounted = useRef(false);

  useEffect(() => {
    if (!hasMounted.current) {
      hasMounted.current = true;

      const postStartDatePayload = {
        URL_PARAM: eventId,
        queryParams: {
          responseFilter: "itemMin",
        },
        division,
        promoStartDate: startDate,
      };

      postStartDateData(postStartDatePayload, {
        skip: !eventId,
      }).then(() => {
        postAllowanceForUom({
          URL_PARAM: eventId,

          planEventId: eventId,
          offerAllowancesGroup,
          performStartDate: startDate,
          performEndDate: endDate,
          useStubs: false,
        });
      });
    }
  }, []);

  const findSellUOMValue = () => {
    if (planEventResponse && isPlanEventSuccess) {
      const defaultSellUom: string =
        planEventResponse[cardIndex]?.plannedProductGroupItems[cardItemIndex]
          ?.sellUomTypes[0];
      setUomValue(defaultSellUom || "EA");
    }
  };

  useEffect(() => {
    findSellUOMValue();
  }, [planEventResponse, isPlanEventSuccess]);

  return {
    defaultUomValue: uomValue,
    isLoading,
    data,
    planEventResponse,
  };
}
