import { renderHook, act } from "@testing-library/react";
import useFetchCombinedData from "./useFetchCombinedData";

describe("useFetchCombinedData Hook", () => {
  const mockQuery = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should have initial state", () => {
    const { result } = renderHook(() => useFetchCombinedData());

    expect(result.current.state).toEqual({
      data: [],
      loading: false,
      error: null,
    });
  });

  it("should fetch and update state with combined sorted data", async () => {
    const mockData = [
      { divisionId: 1, name: "B" },
      { divisionId: 2, name: "A" },
    ];

    mockQuery.mockImplementation(param =>
      Promise.resolve({ data: [param] })
    );

    const { result } = renderHook(() =>
      useFetchCombinedData(mockQuery, mockData)
    );

    expect(result.current.state.loading).toBe(true);

    await act(async () => {
      await result.current.fetchCombinedData(mockQuery, mockData);
    });

    expect(result.current.state.loading).toBe(false);
    expect(result.current.state.error).toBeNull();
    expect(result.current.state.data).toEqual([
      { divisionId: 1, name: "B" },
      { divisionId: 2, name: "A" },
    ]);

    // expect(mockQuery).toHaveBeenCalledTimes(1);
  });

  it("should handle fetch errors", async () => {
    mockQuery.mockRejectedValue(new Error("Fetch failed"));

    const { result } = renderHook(() =>
      useFetchCombinedData(mockQuery, ["param1"])
    );

    await act(async () => {
      await result.current.fetchCombinedData(mockQuery, ["param1"]);
    });

    expect(result.current.state.loading).toBe(true);
    expect(result.current.state.data).toEqual([]);
    expect(result.current.state.error).toBeInstanceOf(Error);
    expect(result.current.state.error?.message).toBe("Fetch failed");
  });

  it("should not fetch data when skip is true", () => {
    const { result } = renderHook(() =>
      useFetchCombinedData(mockQuery, ["param1"], true)
    );

    expect(result.current.state).toEqual({
      data: [],
      loading: false,
      error: null,
    });

    expect(mockQuery).not.toHaveBeenCalled();
  });
});
