import { render, screen, fireEvent } from "@testing-library/react";
import SelectionListContainer from "../SelectionListContainer";
import { setSelectedStoreGroups } from "../../../create-event/service/slice/event-detail-slice";
import * as helperFunctions from "../../helper-functions-ef";
import "@testing-library/jest-dom";

// ✅ Declare the mock before using it
const mockDispatch = jest.fn();

// ✅ Mock react-redux properly
jest.mock("react-redux", () => ({
  ...jest.requireActual("react-redux"),
  useDispatch: () => mockDispatch,
}));

// ✅ Mock child components
jest.mock(
  "../SelectDeselectAllContainer",
  () =>
    ({ handleClick, isChecked }) =>
      (
        <button data-testid="select-deselect-all" onClick={handleClick}>
          {isChecked ? "Deselect All" : "Select All"}
        </button>
      )
);

jest.mock(
  "../SearchBox",
  () =>
    ({
      filterListBySearch,
      searchedText,
      setSearchedText,
      searchPlaceholder,
    }) =>
      (
        <input
          data-testid="search-box"
          placeholder={searchPlaceholder}
          value={searchedText}
          onChange={e => {
            setSearchedText(e.target.value);
            filterListBySearch(e.target.value);
          }}
        />
      )
);

jest.mock(
  "../SelectionItemsList",
  () =>
    ({ updatedData, configObj, onChangeInput }) =>
      (
        <div data-testid="selection-items-list">
          {updatedData.map((item, index) => (
            <div
              key={index}
              data-testid="item"
              onClick={() => {
                onChangeInput({
                  target: { checked: true, value: item[configObj.uniqueId] },
                });
              }}
            >
              {item[configObj.searchItemKey]}
            </div>
          ))}
        </div>
      )
);

describe("SelectionListContainer", () => {
  const mockFilterList = jest.spyOn(helperFunctions, "searchData");
  const mockSelectDeselectAll = jest.spyOn(
    helperFunctions,
    "selectDeselectAll"
  );

  const configObj = {
    uniqueId: "id",
    searchItemKey: "name",
    showSearchBox: true,
    showSelectAllFeature: true,
    id: "storeGroup",
    searchPlaceholder: "Search something",
  };

  const dropDownData = [
    { id: "1", name: "Item A" },
    { id: "2", name: "Item B" },
  ];

  const selectedData = ["1"];
  const onItemSelection = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("renders search box and items", () => {
    render(
      <SelectionListContainer
        configObj={configObj}
        dropDownData={dropDownData}
        selectedData={selectedData}
        onItemSelection={onItemSelection}
      />
    );

    expect(screen.getByTestId("search-box")).toBeInTheDocument();
    expect(screen.getByTestId("selection-items-list")).toBeInTheDocument();
  });

  test("calls filterListBySearch when text is typed", () => {
    mockFilterList.mockReturnValueOnce([dropDownData[0]]);

    render(
      <SelectionListContainer
        configObj={configObj}
        dropDownData={dropDownData}
        selectedData={[]}
        onItemSelection={onItemSelection}
      />
    );

    const input = screen.getByTestId("search-box");
    fireEvent.change(input, { target: { value: "Item A" } });

    expect(mockFilterList).toHaveBeenCalledWith(dropDownData, "name", "Item A");
  });

  test("calls handleSelectDeselectAll on button click", () => {
    mockSelectDeselectAll.mockReturnValueOnce({
      selectedItemIds: ["1", "2"],
      selectedItems: dropDownData,
    });

    render(
      <SelectionListContainer
        configObj={configObj}
        dropDownData={dropDownData}
        selectedData={[]}
        onItemSelection={onItemSelection}
      />
    );

    fireEvent.click(screen.getByTestId("select-deselect-all"));

    expect(mockSelectDeselectAll).toHaveBeenCalled();
    expect(mockDispatch).toHaveBeenCalledWith(
      setSelectedStoreGroups(["1", "2"])
    );
    expect(onItemSelection).toHaveBeenCalledWith(dropDownData);
  });

  test("calls onChangeInput when item is clicked", () => {
    render(
      <SelectionListContainer
        configObj={configObj}
        dropDownData={dropDownData}
        selectedData={[]}
        onItemSelection={onItemSelection}
      />
    );

    const item = screen.getAllByTestId("item")[0];
    fireEvent.click(item);

    expect(mockDispatch).toHaveBeenCalledWith(setSelectedStoreGroups(["1"]));
    expect(onItemSelection).toHaveBeenCalled();
  });

  test("renders fallback span when no data", () => {
    render(
      <SelectionListContainer
        configObj={configObj}
        dropDownData={[]}
        selectedData={[]}
        onItemSelection={onItemSelection}
      />
    );

    expect(
      screen.queryByTestId("selection-items-list")
    ).not.toBeInTheDocument();
  });
});
