import { screen } from "@testing-library/react";
import { FormWrapper, renderWithProviders } from "@me/util-form-wrapper";
import "@testing-library/jest-dom";
import PlanProductGroupItemModal from "./plan-product-group-item-modal";

describe("Plan Product Group Modal Test Suite", () => {
  class ResizeObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
  }
  beforeEach(() => {
    window.ResizeObserver = ResizeObserver as any;
  });
  it("Open Modal Test case", () => {
    renderWithProviders(
      <FormWrapper>
        <PlanProductGroupItemModal
          isOpen={true}
          setOpen={() => {}}
          promoProductGroups={[]}
        />
      </FormWrapper>
    );
    expect(screen.getByText("Items (0)")).toBeInTheDocument();
  });
  it("Close Modal Test case", () => {
    renderWithProviders(
      <FormWrapper>
        <PlanProductGroupItemModal
          isOpen={false}
          setOpen={() => {}}
          promoProductGroups={[]}
        />
      </FormWrapper>
    );
    const modal = screen.queryByText("modal-id");
    expect(modal).toBeNull();
  });
});
