import { useCallback, useEffect, useRef, useState } from "react";
import { useSelectorWrap } from "@me/data-rtk";
import { useLazyGetNationalAllowancesItemsQuery } from "../../../../../service/apis/allowance-api";
import {
  getBatchPayloadByDivisions,
  getDivisionName,
} from "../../offer-service";
import useFetchCombinedData from "../hooks/useFetchCombinedData";
import { DatePickerAtom } from "../../../../fields/allowance-atoms";
import {
  Accordion,
  AccordionBody,
  AccordionHeader,
} from "@material-tailwind/react";
import { useDispatch } from "react-redux";
import { OFFER_FORM_FIELDS } from "../../offer-flow-config";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";
import { getAllowanceFormRegister<PERSON>ey } from "../../../../../service/allowance/allowance-service";
import {
  addDaysHelper,
  camelCaseVendorName,
  checkInvalidDates,
  checkIsValidDate,
  getLocation,
} from "../../../allowance/stepper/common-stepper/allowance-dates/allowance-dates-service";
import {
  setAllowanceFormInfo,
  setOfferAmontsData,
  setVendorsForAllowances,
} from "../../../../../service/slice/allowance-details-slice";
import { getObjectKeys } from "../../../../../service/allowance/allowance-stepper-service";
import "./dates-accordion-wrapper.scss";
import { ChevronDown, ChevronRight, Info } from "lucide-react";
import Button from "@albertsons/uds/molecule/Button";
import LoadingSpinner from "../../../../../constants/LoadingSpinner/LoadingSpinner";
import { FormFieldError } from "@me/util-form-wrapper";
import { Tooltip } from "@albertsons/uds/molecule/Tooltip";
import useGetOfferSectionConfiguration from "../../hooks/useGetOfferSectionConfiguration";
import Divider from "@albertsons/uds/molecule/Divider";
import { useHandleAmountsResponse } from "../../hooks/amounts/useHandleAmountsResponse";
import { isFeatureFlagEnabled, isVendorLoggedIn } from "@me-upp-js/utilities";
import { appConstants } from "@me/utils-root-props";

const NationalCaseAdditionalDatesSection = ({
  cardIndex,
  cardItemIndex,
  formControls,
  sectionConfiguration,
  isEditEnable = false,
  offerMapKey = "",
  vehicleFields,
  sectionKey,
}) => {
  const {
    OFFER_ALLOWANCE_GROUP,
    ALLOWANCE_TYPES: { SHIPTOSTORE },
    NATIONAL_DIVISIONS_FOR_DIFFERENT_DATES,
  } = efConstants;
  const {
    fields: {
      vehicleStart: vehicleStartField,
      vehicleEnd: vehicleEndField,
      orderStart,
      orderEnd,
      arrivalStart,
      arrivalEnd,
      errors,
    },
  } = sectionConfiguration;
  const {
    additionalDatesChangeKey,
    orderDatesChangeKey,
    additionalDatesKey,
    datesValidateKey,
  } = OFFER_FORM_FIELDS;

  const {
    register = () => null,
    control,
    setValue = () => null,
    trigger,
  } = formControls || {};

  const isMultiVendorFeatureEnable = isFeatureFlagEnabled(
    appConstants.FEATURE_FLAGS.MULTI_VENDOR
  );

  const dispatch = useDispatch();
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { isAllowConvEnable } = useSelectorWrap("allow_conv_status_rn").data;
  const {
    data: { allowanceData: allowanceTempWorkData },
  } = useSelectorWrap("allowance_temp_work");
  const { data: allowanceSliceData } = useSelectorWrap("allowance_form_data");
  const { productSources = [] } =
    useSelectorWrap("allowance_product_sources_rn")?.data || {};
  const { offerAmounts = {}, isAdditionalDatesChanged = {} } =
    useSelectorWrap("offer_amounts_details")?.data || {};
  const {
    vendors: { myVendors = [] } = {},
    isMultiVendor = false,
    isAllowMinSfoChanged = false,
    allowancesAmontsResp = {},
    isMultiVendorEnabled,
    isAllowminSfoLoaded = false,
  } = useSelectorWrap("vendors_data_for_allowance_rn").data;
  const {
    data: { offerDivisions = [] },
  } = useSelectorWrap("national_offer_divisions") || {};

  const allowanceRegField = getAllowanceFormRegisterKey(
    cardIndex,
    cardItemIndex
  );

  const { id: eventId = "", divisionIds = [] } = eventDetailsData || {};
  const vehicleStart = vehicleFields?.startDate;
  const vehicleEnd = vehicleFields?.endDate;
  const allowanceFormData =
    allowanceSliceData?.allowanceFormData?.[allowanceRegField];
  const { allowanceType } = allowanceFormData?.allowancePrimeData || {};
  const isCancelled = allowanceFormData?.isCancelled?.[offerMapKey];
  //   const formAllowItems = allowanceFormData?.additionalDates?.[offerMapKey];
  const isDsdOffer = offerMapKey === OFFER_ALLOWANCE_GROUP.CASE.DSD;
  const isCaseWarehouse = offerMapKey === OFFER_ALLOWANCE_GROUP.CASE.WAREHOUSE;
  const isCombinedOffer = offerMapKey === OFFER_ALLOWANCE_GROUP.CASE.COMBINED;
  const isS2sOffer = allowanceType === SHIPTOSTORE.label;
  const typeKey = isDsdOffer ? "vendorNbr" : "locationName";
  const amountsSliceData = offerAmounts?.[offerMapKey] || [];
  // const offerId = offerAllowances?.[cardIndex || 0]?.id;
  const powerLineFieldsMapper = { header: "header", rowHeader: "rowHeader" };
  // const isOfferInvalid = eventDetailsData?.inValidAllowances?.includes(
  //   eventDetailsData?.offerAllowances?.[cardIndex]?.id
  // );
  const selectedFields = [
    vehicleStartField,
    vehicleEndField,
    ...(isCaseWarehouse ? [orderStart, orderEnd] : []),
    arrivalStart,
    arrivalEnd,
  ];

  const dates = useRef<any>([]);
  const additionalDatesCount = useRef(0);
  const addionalDatesUpdateCount = useRef(0);
  const [count, setCount] = useState(0);
  const [datesArray, setDatesArray] = useState<any>([]);
  const [headerFields, setHeaderFields] = useState<any>(null);
  const [invalidDatesVendors, setInvalidDatesVendors] = useState<any>([]);
  const [dateTypes, setDateTypes] = useState<any>([]);
  const [refreshDates, setRefreshDates] = useState(false);
  const [isExpandAll, setIsExpandAll] = useState(true);
  const [isDatesValid, setIsDatesValid] = useState(false);
  const [skipNationalAmountsCall, setSkipNationalAmountsCall] = useState(true);

  const [fetchNationalAmountsData] = useLazyGetNationalAllowancesItemsQuery();
  const { clearNextSectionsOnOptionChange } = useGetOfferSectionConfiguration({
    allowanceRegField,
  });
  const { handleTempDeleteForInvalidItems, isTempDelInprogress } =
    useHandleAmountsResponse();

  const [isEditDivisionalDates, setIsEditDivisionalDates] = useState(false);
  const [openAccordions, setOpenAccordions] = useState({});

  const queryParams = getBatchPayloadByDivisions(
    {
      URL_PARAMS: [eventId, offerMapKey],
    },
    offerDivisions,
    { skipOverlaps: false }
  );

  const {
    state: {
      data: allowancesResp = null,
      loading: isAmountsDataLoading = false,
      error: isAmountsFetchError = null,
    },
  } = useFetchCombinedData(
    fetchNationalAmountsData,
    queryParams,
    skipNationalAmountsCall
  );

  useEffect(() => {
    if (!amountsSliceData?.length) {
      setSkipNationalAmountsCall(() => true);
      setCount(0);
    }
  }, [amountsSliceData]);

  useEffect(() => {
    if (
      skipNationalAmountsCall &&
      eventId &&
      allowanceTempWorkData?.[0]?.tempWorkAllowanceId &&
      productSources?.length &&
      !amountsSliceData?.length &&
      !isAllowConvEnable &&
      !isAllowMinSfoChanged &&
      !count &&
      (isVendorLoggedIn() && isMultiVendorFeatureEnable
        ? isAllowminSfoLoaded
        : true)
    ) {
      setSkipNationalAmountsCall(() => false);
    }
  }, [
    eventId,
    allowanceTempWorkData?.[0]?.tempWorkAllowanceId,
    productSources?.length,
    isAllowMinSfoChanged,
    amountsSliceData?.length,
    count,
    isAllowConvEnable,
  ]);

  useEffect(() => {
    const handleAmountsData = async () => {
      if (
        (amountsSliceData?.length || allowancesResp?.length) &&
        (!count || isAllowMinSfoChanged)
      ) {
        additionalDatesCount.current = 0;
        const amountsResult =
          !isAllowMinSfoChanged && amountsSliceData?.length
            ? amountsSliceData
            : allowancesResp || allowancesAmontsResp;

        let filteredResp = amountsResult;
        if (isMultiVendorEnabled && isDsdOffer) {
          filteredResp = !isMultiVendor
            ? amountsResult?.map(item => ({
                ...item,
                allowances: item?.allowances?.filter(item =>
                  myVendors?.some(
                    vendor => vendor?.dsdVendorNbr === item?.vendorNbr
                  )
                ),
              }))
            : amountsResult;

          dispatch(
            setVendorsForAllowances({
              ...myVendors,
              isMultiVendor,
              isAllowMinSfoChanged: false,
              ...(allowancesResp && { allowancesAmontsResp: allowancesResp }),
            })
          );
        }
        const { amountsResp } = await handleTempDeleteForInvalidItems(
          filteredResp
        );
        filteredResp = amountsResp;

        (!amountsSliceData?.length || isAllowMinSfoChanged) &&
          dispatch(
            setOfferAmontsData({
              offerAmounts: { ...offerAmounts, [offerMapKey]: filteredResp },
              ...(!isCancelled && {
                isAdditionalDatesChanged: {
                  ...isAdditionalDatesChanged,
                  [offerMapKey]: 0,
                },
              }),
            })
          );

        const vendorsArray = filteredResp?.map(item => item?.allowances);
        if (vendorsArray?.length) {
          const formattedDates = vendorsArray?.map((vendor, index: number) => {
            additionalDatesCount.current =
              additionalDatesCount.current +
              (isCombinedOffer ? 1 : vendor?.length || 1) *
                (isCaseWarehouse ? 4 : 2) +
              (isCombinedOffer ? 0 : isCaseWarehouse ? 4 : 2);
            const divId = filteredResp?.[index]?.divisionId;
            return updateDatesArray(vendor, divId);
          });
          setDatesArray(() => formattedDates);
          setHeaderFields(() => formattedDates?.[0]?.[0]);
        }
        setValue(datesValidateKey, !!vendorsArray?.length);
        setValue(additionalDatesChangeKey, false);
        setValue(orderDatesChangeKey, false);
        setRefreshDates(isAllowMinSfoChanged);
      } else {
        setValue(datesValidateKey, false);
      }
    };

    handleAmountsData();
  }, [allowancesResp, isMultiVendor]);

  const updateDatesArray = (vendorsArray, divId: string) => {
    const data = vendorsArray?.map((vendor: any, index: number) => {
      return {
        ...vendor,
        defaultAllowanceDates: {
          ...vendor?.["defaultAllowanceDates"],
          overrideInd: true,
        },
        divisionId: divId,
        vehicleStartDate: checkIsValidDate(vendor?.allowanceStartDate)
          ? vendor?.allowanceStartDate
          : vehicleStart,
        vehicleEndDate: checkIsValidDate(vendor?.allowanceEndDate)
          ? vendor?.allowanceEndDate
          : vehicleEnd,
      };
    });
    return data;
  };

  // Correctly initialize openAccordions based on amountsSliceData and isExpandAll
  useEffect(() => {
    const newOpenAccordions = {};
    !isCombinedOffer &&
      amountsSliceData?.forEach((offer: any) => {
        newOpenAccordions[offer?.divisionId] = isExpandAll;
      });
    setOpenAccordions(newOpenAccordions);
  }, [isExpandAll, JSON.stringify(amountsSliceData)]); // Depend on amountsSliceData instead of dates

  const getUpdatedFieldProps = (
    fieldProps: any,
    key: string,
    index: number,
    parentIndex?: number,
    isHeader = false
  ) => {
    return {
      ...fieldProps,
      required: isHeader ? false : fieldProps?.required,
      registerField: `${fieldProps.registerField}.${key}.${
        parentIndex !== undefined ? parentIndex : ""
      }.${index}`,
    };
  };

  useEffect(() => {
    if (getObjectKeys(headerFields).length) {
      [vehicleStartField, vehicleEndField]?.map(field => {
        setValue(
          getUpdatedFieldProps(field, powerLineFieldsMapper.header, 0)
            ?.registerField,
          headerFields?.[field?.registerKey]
        );
        return field;
      });
    }
  }, [headerFields]);

  useEffect(() => {
    if (!count || refreshDates) {
      dates.current = datesArray;
      setRefreshDates(false);
    }
    setValue(additionalDatesKey, datesArray);
    if (datesArray?.length) {
      datesArray?.map((offer: any, parentIndex: number) => {
        offer?.map((allowance, index) => {
          selectedFields?.map(field => {
            setValue(
              getUpdatedFieldProps(
                field,
                allowance?.[typeKey],
                index,
                parentIndex
              )?.registerField,
              allowance?.[field?.registerKey]
            );
            (!count || refreshDates) &&
              !index &&
              !isCombinedOffer &&
              setValue(
                getUpdatedFieldProps(
                  field,
                  powerLineFieldsMapper.rowHeader,
                  0,
                  parentIndex
                )?.registerField,
                allowance?.[field?.registerKey]
              );
            return field;
          });
          return allowance;
        });
        return offer;
      });
    }

    const validations: any = {};
    datesArray?.forEach((offer: any) => {
      const { inValidVendors, type } = checkInvalidDates(
        offer,
        isCaseWarehouse,
        offer?.[0]?.vehicleStartDate,
        offer?.[0]?.vehicleEndDate,
        typeKey
      );
      validations["inValidVendors"] = [
        ...(validations?.["inValidVendors"] || []),
        inValidVendors,
      ];
      validations["types"] = [...(validations?.["types"] || []), type];
    });
    setInvalidDatesVendors(validations?.inValidVendors);
    setDateTypes(validations?.types);
    trigger?.();
  }, [JSON.stringify(datesArray)]);

  useEffect(() => {
    count && setDatesArray(() => dates.current);
  }, [count]);

  useEffect(() => {
    if (dates.current?.[0]?.length) {
      setTimeout(() => setIsDatesValid(true), 0);
    }
  }, [datesArray?.[0]?.length]);

  const getUpdatedDateByDivision = (
    isHeader = false,
    divId: string,
    date: string
  ) => {
    if (isHeader && NATIONAL_DIVISIONS_FOR_DIFFERENT_DATES.includes(divId)) {
      return addDaysHelper(date, 2) as string;
    }
    return date;
  };

  const onSectionUpdate = () => {
    if (!isEditEnable) {
      clearNextSectionsOnOptionChange(sectionKey);
    } else {
      !allowanceFormData?.isAdditionalDatesChanged &&
        dispatch(
          setAllowanceFormInfo({
            allowanceFormData: {
              [allowanceRegField]: {
                ...allowanceFormData,
                isAdditionalDatesChanged: true,
              },
            },
          })
        );
    }
  };

  const setDateValues = (
    vendorKey: string,
    dateType: string,
    date: string,
    parentIndex: number,
    index: number,
    isHeader = false,
    isRowHeader = false
  ) => {
    const isPowerLineField = isHeader || isRowHeader;
    dates.current = dates.current?.map((offer, i: number) => {
      if (isHeader || i === parentIndex) {
        return offer?.map((allowance, j: number) => {
          return isPowerLineField || j === index
            ? {
                ...allowance,
                ...(vendorKey &&
                (isPowerLineField || allowance?.[typeKey] === vendorKey)
                  ? {
                      [dateType]: getUpdatedDateByDivision(
                        isHeader,
                        allowance?.divisionId,
                        date
                      ),
                    }
                  : {}),
              }
            : allowance;
        });
      }
      return offer;
    });
    setCount(count => count + 1);
  };

  const getFeildkeyByLabel = (label: string) => {
    const selectedField = selectedFields.find(field => field?.label === label);
    return selectedField?.registerKey || "";
  };

  const onVehicleDateChange = (
    vendorKey: string,
    dateType: string,
    date: string,
    parentIndex: number,
    index: number,
    isHeader = false,
    isRowHeader = false
  ) => {
    addionalDatesUpdateCount.current = addionalDatesUpdateCount.current + 1;
    const isCountExceded =
      addionalDatesUpdateCount.current > additionalDatesCount.current;

    if (isCountExceded) {
      setValue(additionalDatesChangeKey, true);
      onSectionUpdate();
    }

    if (vendorKey) {
      if (
        isCountExceded &&
        [orderStart.label, orderEnd.label].includes(dateType)
      ) {
        setValue(orderDatesChangeKey, true);
      }
      const resgisterKey = getFeildkeyByLabel(dateType);
      setDateValues(
        vendorKey,
        resgisterKey,
        date,
        parentIndex,
        index,
        isHeader,
        isRowHeader
      );
    }
  };

  const getFieldTypeKey = (
    typeKey = "",
    isHeader = false,
    isRowHeader = false
  ) => {
    return isHeader
      ? powerLineFieldsMapper.header
      : isRowHeader
      ? powerLineFieldsMapper.rowHeader
      : typeKey;
  };

  const VehicleStartDateField = record => {
    const {
      index,
      parentIndex,
      isHeader = false,
      isRowHeader = false,
      isAccordian = false,
    } = record;
    const _key = getFieldTypeKey(record?.[typeKey], isHeader, isRowHeader);
    const fieldKey = getUpdatedFieldProps(
      vehicleStartField,
      _key,
      index,
      isHeader ? undefined : parentIndex,
      isHeader
    );
    return vehicleStartField ? (
      <div className="min-w-[120px]">
        <DatePickerAtom
          key={fieldKey?.registerField}
          fieldProps={fieldKey}
          register={register}
          control={control}
          disabled={true}
          showLabel={isHeader}
          isAccordian={isAccordian}
        />
      </div>
    ) : null;
  };

  const VehicleEndDateField = record => {
    const {
      index,
      parentIndex,
      isHeader = false,
      isRowHeader = false,
      isAccordian = false,
    } = record;
    const _key = getFieldTypeKey(record?.[typeKey], isHeader, isRowHeader);
    const fieldKey = getUpdatedFieldProps(
      vehicleEndField,
      _key,
      index,
      isHeader ? undefined : parentIndex,
      isHeader
    );
    return vehicleEndField ? (
      <div className="min-w-[120px]">
        <DatePickerAtom
          key={fieldKey?.registerField}
          fieldProps={fieldKey}
          register={register}
          control={control}
          disabled={true}
          showLabel={isHeader}
          isAccordian={isAccordian}
        />
      </div>
    ) : null;
  };

  const OrderStartDateField = record => {
    const {
      parentIndex,
      index,
      isHeader = false,
      isRowHeader = false,
      vehicleStartDate = "",
      vehicleEndDate = "",
      isAccordian = false,
    } = record;
    const _key = getFieldTypeKey(record?.[typeKey], isHeader, isRowHeader);
    const fieldKey = getUpdatedFieldProps(
      orderStart,
      _key,
      index,
      isHeader ? undefined : parentIndex,
      isHeader
    );

    return orderStart ? (
      <div className="min-w-[120px]">
        <DatePickerAtom
          key={fieldKey?.registerField}
          fieldProps={fieldKey}
          register={register}
          control={control}
          isCustomValidationEnabled={!(isHeader || isRowHeader)}
          customValidation={{
            value: !(
              invalidDatesVendors?.[parentIndex]?.includes(_key) &&
              (dateTypes?.[parentIndex]?.[_key]?.order ||
                dateTypes?.[parentIndex]?.[_key]?.orderStart)
            ),
            message: orderStart?.errors?.invalid?.text,
          }}
          onChange={e => {
            onVehicleDateChange(
              _key,
              orderStart?.label,
              e,
              parentIndex,
              index,
              isHeader,
              isRowHeader
            );
          }}
          minDate={addDaysHelper(vehicleStartDate, -200)}
          maxDate={addDaysHelper(vehicleEndDate, 30)}
          showLabel={isHeader}
          isAccordian={isAccordian}
        />
      </div>
    ) : null;
  };

  const OrderEndDateField = record => {
    const {
      parentIndex,
      index,
      isHeader = false,
      isRowHeader = false,
      vehicleStartDate = "",
      vehicleEndDate = "",
      isAccordian = false,
    } = record;
    const _key = getFieldTypeKey(record?.[typeKey], isHeader, isRowHeader);
    const fieldKey = getUpdatedFieldProps(
      orderEnd,
      _key,
      index,
      isHeader ? undefined : parentIndex,
      isHeader
    );

    return orderEnd ? (
      <div className="min-w-[120px]">
        <DatePickerAtom
          key={fieldKey?.registerField}
          fieldProps={fieldKey}
          register={register}
          control={control}
          isCustomValidationEnabled={!(isHeader || isRowHeader)}
          customValidation={{
            value: !(
              invalidDatesVendors?.[parentIndex]?.includes(_key) &&
              (dateTypes?.[parentIndex]?.[_key]?.order ||
                dateTypes?.[parentIndex]?.[_key]?.orderEnd)
            ),
            message: orderEnd?.errors?.invalid?.text,
          }}
          onChange={e => {
            onVehicleDateChange(
              _key,
              orderEnd?.label,
              e,
              parentIndex,
              index,
              isHeader,
              isRowHeader
            );
          }}
          minDate={addDaysHelper(vehicleStartDate, -30)}
          maxDate={addDaysHelper(vehicleEndDate, 30)}
          showLabel={isHeader}
          isAccordian={isAccordian}
        />
      </div>
    ) : null;
  };

  const ArrivalStartDateField = record => {
    const {
      parentIndex,
      index,
      isHeader = false,
      isRowHeader = false,
      vehicleStartDate = "",
      vehicleEndDate = "",
      isAccordian = false,
    } = record;
    const _key = getFieldTypeKey(record?.[typeKey], isHeader, isRowHeader);
    const fieldKey = getUpdatedFieldProps(
      arrivalStart,
      _key,
      index,
      isHeader ? undefined : parentIndex,
      isHeader
    );

    return arrivalStart ? (
      <div className="min-w-[120px]">
        <DatePickerAtom
          key={fieldKey?.registerField}
          fieldProps={fieldKey}
          register={register}
          control={control}
          isCustomValidationEnabled={!(isHeader || isRowHeader)}
          customValidation={{
            value: !(
              invalidDatesVendors?.[parentIndex]?.includes(_key) &&
              (dateTypes?.[parentIndex]?.[_key]?.arrival ||
                dateTypes?.[parentIndex]?.[_key]?.vehicle ||
                (isCaseWarehouse &&
                  dateTypes?.[parentIndex]?.[_key]?.orderStart) ||
                dateTypes?.[parentIndex]?.[_key]?.ship)
            ),
            message: arrivalStart?.errors?.invalid?.text,
          }}
          onChange={e => {
            onVehicleDateChange(
              _key,
              arrivalStart?.label,
              e,
              parentIndex,
              index,
              isHeader,
              isRowHeader
            );
          }}
          minDate={addDaysHelper(vehicleStartDate, -30)}
          maxDate={addDaysHelper(vehicleEndDate, 30)}
          showLabel={isHeader}
          isAccordian={isAccordian}
        />
      </div>
    ) : null;
  };

  const ArrivalEndDateField = record => {
    const {
      parentIndex,
      index,
      isHeader = false,
      isRowHeader = false,
      vehicleStartDate = "",
      vehicleEndDate = "",
      isAccordian = false,
    } = record;
    const _key = getFieldTypeKey(record?.[typeKey], isHeader, isRowHeader);
    const fieldKey = getUpdatedFieldProps(
      arrivalEnd,
      _key,
      index,
      isHeader ? undefined : parentIndex,
      isHeader
    );

    return arrivalEnd ? (
      <div className="min-w-[120px]">
        <DatePickerAtom
          key={fieldKey?.registerField}
          fieldProps={fieldKey}
          register={register}
          control={control}
          isCustomValidationEnabled={!(isHeader || isRowHeader)}
          customValidation={{
            value: !(
              invalidDatesVendors?.[parentIndex]?.includes(_key) &&
              (dateTypes?.[parentIndex]?.[_key]?.arrival ||
                dateTypes?.[parentIndex]?.[_key]?.vehicle ||
                (isCaseWarehouse &&
                  dateTypes?.[parentIndex]?.[_key]?.orderEnd) ||
                dateTypes?.[parentIndex]?.[_key]?.ship)
            ),
            message: arrivalEnd?.errors?.invalid?.text,
          }}
          onChange={e => {
            onVehicleDateChange(
              _key,
              arrivalEnd?.label,
              e,
              parentIndex,
              index,
              isHeader,
              isRowHeader
            );
          }}
          minDate={addDaysHelper(vehicleStartDate, -30)}
          maxDate={addDaysHelper(vehicleEndDate, 30)}
          showLabel={isHeader}
          isAccordian={isAccordian}
        />
      </div>
    ) : null;
  };

  const fields = [
    VehicleStartDateField,
    VehicleEndDateField,
    ...(isCaseWarehouse ? [OrderStartDateField, OrderEndDateField] : []),
    ArrivalStartDateField,
    ArrivalEndDateField,
  ];

  const renderHeaderFields = () => {
    return fields?.map(field =>
      field({ ...headerFields, index: 0, isHeader: true, isAccordian: true })
    );
  };

  const renderRowHeader = (
    record,
    parentIndex: number,
    childIndex: number,
    divisionId: string
  ) => {
    return (
      <div className="flex items-center w-full !h-auto">
        <div className="flex w-[500px]">
          <label className="ml-3">{getDivisionName(divisionId)}</label>
        </div>
        <div className="flex flex-1 space-x-4">
          {fields?.map(field =>
            field({
              ...record,
              isRowHeader: true,
              parentIndex,
              index: childIndex,
            })
          )}
        </div>
      </div>
    );
  };

  const getValidationContent = (parentIndex: number, record) => {
    const validationErrors = invalidDatesVendors?.[parentIndex]?.includes(
      record?.[typeKey]
    ) && (
      <div className="flex flex-col gap-1 my-1 !w-[500px]">
        {Object.keys(dateTypes?.[parentIndex]?.[record?.[typeKey]] || {})?.map(
          (type: string) =>
            dateTypes?.[parentIndex]?.[record?.[typeKey]]?.[type] && (
              <FormFieldError
                error={
                  errors.INVALID_DATES?.[
                    isS2sOffer
                      ? efConstants?.ALLOWANCE_TYPES.SHIPTOSTORE.key
                      : efConstants?.ALLOWANCE_TYPES.CASE.key
                  ]?.[type]
                }
              />
            )
        )}
      </div>
    );

    return validationErrors ? (
      <div className={"text-center mt-3"}>
        <Tooltip zIndex={100} anchor={"top"} showDelay={300}>
          <Tooltip.Popover>{validationErrors}</Tooltip.Popover>
          <Info size={16} color="#BF2912" />
        </Tooltip>
      </div>
    ) : null;
  };

  const getRowHeaderLabel = ({
    distCenter = "",
    vendorName = "",
    vendorNbr = "",
    costAreaDesc = "",
    locationName = "",
    allowanceDateOffsets = {},
  }) => {
    return isDsdOffer ? (
      <div className="flex w-[500px] pl-10">
        <span className="pr-1">{camelCaseVendorName(vendorName)}</span>
        <span>-</span>
        <span className="pl-1 pr-1">{vendorNbr}</span>
        <span className="pl-1 pr-1">{costAreaDesc}</span>
      </div>
    ) : isCaseWarehouse ? (
      <div className="flex w-[500px] pl-10">
        <span className="pr-1">{distCenter}</span>
        <span>-</span>
        <span className="pl-1 pr-1">{getLocation(locationName)}</span>
        <span>Warehousing</span>
        <span>
          <Divider className="mx-2" height={24} color="#97c4ed" />
        </span>
        <span className="pl-0.5">Lead Time :</span>
        <span className="pl-1 text-[#355d83]">
          {`${Math.abs(
            allowanceDateOffsets?.[
              isS2sOffer ? "startDateOffset" : "resolvedLeadTimeDays"
            ]
          )} Days`}
        </span>
      </div>
    ) : null;
  };

  const renderRowContent = (
    record,
    parentIndex: number,
    childIndex: number,
    divisionId?: string
  ) => {
    return (
      <div
        className={`flex items-center w-full p-3 border-[#C8DAEB] border-b-[1px] !h-auto ${
          !isCombinedOffer ? "!bg-[#F3F4F6]" : ""
        }`}
      >
        {!isCombinedOffer && getRowHeaderLabel(record)}
        {isCombinedOffer && (
          <div className="w-[500px] pr-30 !font-normal">
            {getDivisionName(divisionId || "")}
          </div>
        )}
        <div className="flex flex-1 space-x-4 peer pl-6">
          {fields?.map(field =>
            field({ ...record, parentIndex, index: childIndex })
          )}
          {getValidationContent(parentIndex, record)}
        </div>
      </div>
    );
  };

  const CommonHeader = (
    <div className="flex items-center w-full mt-4 p-3 !h-auto !bg-[#F3F4F6] border-b-[1px] border-[#C8DAEB]">
      <div className="w-[500px] pl-2 pb-14">Division</div>
      <div className="flex flex-1 space-x-4 peer pl-6" key={1}>
        {renderHeaderFields()}
      </div>
    </div>
  );

  const handleAccordionToggle = (divisionId: string) => {
    setOpenAccordions(prevState => ({
      ...prevState,
      [divisionId]: !prevState?.[divisionId],
    }));
  };

  const getAddionalDatesContent = useCallback(() => {
    return (
      <div>
        {amountsSliceData?.map((offer: any, index: number) => {
          const divisionId = offer?.divisionId;
          const allowances = dates.current?.[index] || [];
          return isCombinedOffer ? (
            dates.current?.[index]?.map((allowance, childIndex: number) =>
              renderRowContent(allowance, index, childIndex, divisionId)
            )
          ) : (
            <Accordion
              key={divisionId}
              open={openAccordions?.[divisionId] || false}
              id={divisionId}
              className="bg-white"
            >
              <AccordionHeader
                className="accordian-header-class py-3 text-sm font-normal cursor-pointer border-[#C8DAEB] border-b-[1px] [&>span:last-child]:!hidden"
                style={{ fontFamily: "Nunito Sans" }}
              >
                <section className="flex w-full items-center pl-3">
                  <div className="flex-shrink-0 mr-2">
                    <ChevronRight
                      className={`h-5 w-5 transition-transform duration-300 ease-in-out ${
                        openAccordions?.[divisionId] ? "rotate-90" : ""
                      }`}
                      onClick={() => handleAccordionToggle(divisionId)}
                    />
                  </div>
                  {renderRowHeader(allowances?.[0], index, 0, divisionId)}
                </section>
              </AccordionHeader>
              <div
                id={`abs-accordion-container-body-wrapper-${divisionId}`}
                className="accordion-body-wrapper [&>div:first-child]:!overflow-visible [&>div:first-child]:!h-auto"
              >
                {openAccordions?.[divisionId] && (
                  <AccordionBody className="py-0 !overflow-visible">
                    <div
                      id={`abs-accordion-container-body-${divisionId}`}
                      className="flex flex-col w-full !bg-[#F3F4F6] border-b-[1px] border-[#C8DAEB] font-normal"
                      style={{ fontFamily: "Nunito Sans", fontSize: "medium" }}
                    >
                      {allowances?.map((allowance, childIndex: number) =>
                        renderRowContent(
                          allowance,
                          index,
                          childIndex,
                          divisionId
                        )
                      )}
                    </div>
                  </AccordionBody>
                )}
              </div>
            </Accordion>
          );
        })}
      </div>
    );
  }, [
    invalidDatesVendors,
    dateTypes,
    openAccordions,
    amountsSliceData,
    datesArray,
  ]);

  return (
    <div className="mt-4">
      <LoadingSpinner
        isLoading={isAmountsDataLoading || isTempDelInprogress}
        classname="!h-full !w-full rounded-md"
      />
      <div className="flex gap-2">
        <Button
          className="w-fit p-3"
          variant="secondary"
          size="md"
          type="button"
          onClick={() => {
            setIsEditDivisionalDates(state => !state);
            addionalDatesUpdateCount.current = 0;
          }}
        >
          Edit Divison Dates
          <ChevronDown
            className={`transform transition-transform duration-300 ease-in-out ${
              isEditDivisionalDates ? "rotate-180" : "rotate-0"
            }`}
            color="#1B6EBB"
          />
        </Button>
        {!isCombinedOffer && isEditDivisionalDates && (
          <div>
            <Button
              width={120}
              variant="secondary"
              size="md"
              type="button"
              onClick={() => {
                addionalDatesUpdateCount.current = isExpandAll
                  ? additionalDatesCount.current
                  : (datesArray?.length || 1) * (isCaseWarehouse ? 4 : 2);
                setIsExpandAll(state => !state);
              }}
            >
              {isExpandAll ? "Collapse All" : "Expand All"}
            </Button>
          </div>
        )}
      </div>
      {isEditDivisionalDates && (
        <div className="bg-blue-306 flex items-center text-[#1b6ebb] w-fit mt-3">
          <span>{efConstants.POWERLINE_TEXT}</span>
        </div>
      )}
      {headerFields && isEditDivisionalDates && CommonHeader}
      {isDatesValid && control && isEditDivisionalDates && invalidDatesVendors
        ? getAddionalDatesContent()
        : null}
    </div>
  );
};

export default NationalCaseAdditionalDatesSection;
