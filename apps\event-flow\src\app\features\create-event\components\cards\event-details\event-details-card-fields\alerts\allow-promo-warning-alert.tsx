import Alert from "@albertsons/uds/molecule/Alert";
import React, { memo } from "react";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import { useSelectorWrap } from "@me/data-rtk";

function AllowPromoWarningAlert({ isAllowanceOrPromotion }) {
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const renderWarning = () => {
    const warning = {
      DP: {
        header: efConstants.EDIT_EVENT_WARNING_MESSAGE.DP.header,
        message: efConstants.EDIT_EVENT_WARNING_MESSAGE.DP.message,
      },
      AO: {
        header: efConstants.EDIT_EVENT_WARNING_MESSAGE.AO.header,
        message: efConstants.EDIT_EVENT_WARNING_MESSAGE.AO.message,
      },
      NDP: {
        header: efConstants.EDIT_EVENT_WARNING_MESSAGE.DP.header,
        message: efConstants.EDIT_EVENT_WARNING_MESSAGE.DP.message,
      },
      NCDP: {
        header: efConstants.EDIT_EVENT_WARNING_MESSAGE.DP.header,
        message: efConstants.EDIT_EVENT_WARNING_MESSAGE.DP.message,
      },
    };
    return (
      <>
        <p className="text-base font-bold ">
          {warning[eventDetailsData?.eventType]?.["header"]}
        </p>
        <span className="text-base font-normal">
          {warning[eventDetailsData?.eventType]?.["message"]}
        </span>
      </>
    );
  };
  return (
    <>
      {isAllowanceOrPromotion ? (
        <Alert
          isOpen={true}
          variant="warning"
          size="large"
          className="w-[100%] mb-4"
          dismissible={true}
        >
          {renderWarning()}
        </Alert>
      ) : null}
    </>
  );
}

export default memo(AllowPromoWarningAlert);
