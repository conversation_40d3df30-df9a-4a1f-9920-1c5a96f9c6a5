import { appConstants } from "@me/utils-root-props";
import efConstants from "../../../../../shared/ef-constants/ef-constants";
import {
  checkIsWarehouseItem,
  getAllowancePerfOption,
  getVehicleDatesMapData,
} from "../../../service/allowance/allowance-service";
import {
  allowanceFormReset,
  resetIsOfferSectionUpdated,
  resetOfferAmountsData,
  resetOfferSectionsData,
  resetOfferSectionsEnableConfig,
  setOfferDivisionsData,
} from "../../../service/slice/allowance-details-slice";
import { BASE_HEIGHT } from "./offer-flow-config";
import { isEmpty } from "lodash";
import { SHOW_ALLOWANCE_TO_BE_CREATED_OPTIONS } from "../../../constants/fields/allowance/field-allowance-to-be-created-section";
import { getDateFormat } from "../allowance/stepper/common-stepper/allowance-dates/allowance-dates-service";
import { UNITED_DIVISION_ID } from "@me/utils-root-props";
import { Tooltip } from "@albertsons/uds/molecule/Tooltip";
import { warningIconMaroon } from "@me-upp-js/utilities";

const {
    ALLOWANCE_TYPES,
    ALLOWANCE_SCREEN_TYPES,
    PRODUCT_SOURCE_INFO,
    CREATEIND_OFFER_GROUP_MAPPER,
    BOTH_KEY,
    OFFER_ITEM_ERRORS,
    OFFER_ALLOWANCE_GROUP,
  } = efConstants,
  { CASE, HEADERFLAT, SHIPTOSTORE, SCAN } = ALLOWANCE_TYPES,
  { HEADER_FLAT } = PRODUCT_SOURCE_INFO;

// Get the offer group key based on the createInd and sourceKey(DSD/ WAREHOUSE).
export const getOfferMapKey = (createInd: string, sourceKey = "DSD") => {
  const offerMapKey =
    CREATEIND_OFFER_GROUP_MAPPER[createInd?.toUpperCase()] || null;
  return typeof offerMapKey === "string"
    ? offerMapKey
    : offerMapKey?.[sourceKey];
};

export const multiplier = ({
  areInputsDisabled,
  allowancesResp,
  allowanceType,
}) =>
  areInputsDisabled
    ? allowanceType === "SCAN"
      ? allowancesResp?.summary?.packRetail
      : 1 / allowancesResp?.summary?.packRetail
    : 1;

// HF - Check if the amount refresh is required (required when the amount section is skipped)
export const checkAmountRefreshCondition = (
  createInd: string,
  allowanceType: string
) => {
  return (
    allowanceType === HEADERFLAT.key &&
    createInd === HEADER_FLAT.DSD.createIndex
  );
};

// Need this function to set storegroups in tempwork api
export const buildOverRideArray = (formValues, divIds) => {
  const { storeGroups = [], storeGroupType = {} } = formValues || {};
  return storeGroups?.map(({ id, name }) => ({
    storeGroupId: id,
    sourceStoreGroupId: storeGroupType?.id,
    storeGroupName: name,
    storeGroupType: {
      ...storeGroupType,
      storeGrpTypeName: storeGroupType?.name,
      divisionIds: divIds,
    },
    divisionIds: divIds,
    storeCount: 0,
  }));
};

export const getStoregroupFormData = (
  overrideStoreGroups,
  isEditEnable = false
) => {
  const { storeGroupType = {}, sourceStoreGroupId } =
    overrideStoreGroups?.[0] || {};

  return {
    groupInd: isEditEnable
      ? storeGroupType?.groupInd
      : sourceStoreGroupId || "",
    storeGroupType: {
      ...storeGroupType,
      id: isEditEnable ? storeGroupType?.groupInd : sourceStoreGroupId,
      name: storeGroupType?.storeGrpTypeName,
    },
    storeGroups: overrideStoreGroups?.map((value, index) => {
      return {
        id: value?.storeGroupId,
        name: isEditEnable
          ? `${value?.storeGroupName} (${overrideStoreGroups?.[index]?.storeCount} Stores)`
          : value?.storeGroupName,
      };
    }),
  };
};

export const resetOfferSectionData = dispatch => {
  dispatch(allowanceFormReset());
  dispatch(resetOfferSectionsData());
  dispatch(resetOfferSectionsEnableConfig());
  dispatch(resetOfferAmountsData());
  dispatch(resetIsOfferSectionUpdated());
};

export const setFocusedSection = (
  section,
  offerSectionsEnableConfig,
  setActiveSection
) => {
  const isSectionFocussed =
    offerSectionsEnableConfig?.[section?.key]?.isActive &&
    offerSectionsEnableConfig?.[section?.key]?.scrollTo;
  isSectionFocussed && setActiveSection?.(section?.sectionKey);
};

export const scrollToActiveSection = (sectionRefs, activeSection) => {
  if (activeSection && sectionRefs.current[activeSection]) {
    const element = sectionRefs.current[activeSection];
    return scrollToSection(element, true);
  }
  return null;
};

export function scrollToSection(elemToScroll, isTimeOutReq = false) {
  if (!elemToScroll) return;
  const scrollContainer = document.querySelector(".wrapper-container");
  const stickyHeader: HTMLElement | null = document.querySelector(
    "#abs-event-header-container"
  );
  const headerHeight = stickyHeader?.offsetHeight || 0;
  setTimeout(
    () => {
      if (scrollContainer) {
        const containerRect = scrollContainer?.getBoundingClientRect();
        const elemRect = elemToScroll?.getBoundingClientRect();
        const offset = elemRect?.top - containerRect?.top;
        const targetPosition =
          scrollContainer?.scrollTop + offset - headerHeight - BASE_HEIGHT;
        scrollContainer?.scrollTo({
          top: targetPosition,
          behavior: "smooth",
        });
      } else {
        // --- Fallback: scroll the window ---
        const elemRect = elemToScroll.getBoundingClientRect();
        const targetPosition =
          window?.scrollY + elemRect?.top - headerHeight - BASE_HEIGHT;
        window?.scrollTo({
          top: targetPosition,
          behavior: "smooth",
        });
      }
    },
    isTimeOutReq ? 800 : 0
  );
}

export const checkElementRendered = elem => {
  const scrollContainer: HTMLElement | null =
    document.querySelector(".wrapper-container");
  return elem &&
    scrollContainer &&
    (elem?.offsetHeight > 0 || elem?.offsetWidth > 0) &&
    (scrollContainer?.offsetHeight > 0 || scrollContainer?.offsetWidth > 0)
    ? true
    : false;
};

export const getConvertedCreateInd = (
  createInd: string,
  isEditEnable: boolean,
  productSources: string[],
  offerInfo: any = {}
) => {
  const isCaseBothInd = createInd === CASE.createInd[2];
  if (!isEditEnable) {
    return productSources?.length === 1 && isCaseBothInd
      ? CASE.createInd[0]
      : createInd;
  } else if (isEditEnable && isCaseBothInd) {
    return checkIsWarehouseItem(offerInfo?.allowances?.[0])
      ? CASE.createInd[0]
      : CASE.createInd[1];
  }
  return createInd;
};

export const getPayloadForAllowanceItems = (
  isSummary = false,
  tempWorkAllowanceId: string,
  allowObjFromSlice: any = null
) => {
  const queryString = window.location.search;
  const urlParams = new URLSearchParams(queryString);
  const { offerAmounts = {} } = allowObjFromSlice || {},
    allowGroup = urlParams?.get("group"),
    allowDataFromSlice =
      (allowGroup &&
        offerAmounts &&
        !isEmpty(offerAmounts) &&
        offerAmounts?.[allowGroup]) ||
      null,
    isReDesignEnable = appConstants.IS_ENABLE_REDESIGN_ALLOWANCE_CHANGES;

  return [
    {
      URL_PARAMS: [urlParams.get("eventId"), urlParams.get("group")],
    },
    {
      refetchOnMountOrArgChange: true,
      skip:
        !urlParams.get("eventId") ||
        isSummary ||
        !tempWorkAllowanceId ||
        (isReDesignEnable &&
          allowDataFromSlice &&
          !isEmpty(allowDataFromSlice)),
    },
  ];
};

export const handleZindexForCard = event => {
  event?.stopPropagation();
  (event?.target as HTMLElement)
    ?.closest(".z-index-dynamic-class")
    ?.classList?.add("z-[9]");
  window?.addEventListener("click", windowRemoveIndex);
};

const windowRemoveIndex = () => {
  document
    ?.querySelectorAll(".z-index-dynamic-class")
    ?.forEach(e => e?.classList?.remove("z-[9]"));
  window?.removeEventListener("click", windowRemoveIndex);
};

export const getDefaultPerfOption = (
  allowanceType: string,
  perfOptions,
  eventType: string,
  createInd = ""
) => {
  const defaultPerfOption = getAllowancePerfOption(
    allowanceType,
    eventType,
    createInd
  );

  return (
    perfOptions?.filter(
      ({ eventTypes, deleteInd, performanceConfig, performance }) =>
        eventTypes?.includes(eventType) &&
        deleteInd !== "Y" &&
        performanceConfig?.["defaultCreateInd"] === createInd &&
        performance === defaultPerfOption
    )?.[0] || {}
  );
};

export const getPerfOptionById = (perfOptions, id: string) => {
  return perfOptions?.find(item => item?.id === id);
};

export const getUpdatedPerfValues = (
  allowanceType: string,
  allowanceTypeKey: string,
  eventType: string,
  createIndKey: string,
  isEditEnable: boolean,
  allowancePerformances: any,
  offerAllowancesGroupInfoMap = {},
  perfOptions,
  productSources: string[]
) => {
  if (
    isEditEnable ||
    !(productSources?.length > 1 && allowanceTypeKey === CASE.key)
  )
    return { allowancePerformances, offerAllowancesGroupInfoMap };
  else {
    let perfOption = allowancePerformances;
    const perfChildId =
      allowancePerformances?.performanceConfig?.allowancePerformanceChildId;
    let isDefaultPerf = true;

    if ([CASE.createInd[2], CASE.createInd[0]].includes(createIndKey)) {
      if (!perfChildId) {
        isDefaultPerf = false;
        perfOption = getDefaultPerfOption(
          allowanceType?.toUpperCase(),
          perfOptions || [],
          eventType,
          CASE.createInd[2]
        );
      }
      offerAllowancesGroupInfoMap =
        createIndKey === CASE.createInd[2]
          ? {
              DSD_LEAD_DISTRIBUTORS: {
                allowancePerformanceId:
                  perfOption?.performanceConfig?.allowancePerformanceChildId,
              },
              WAREHOUSE_DIST_CENTERS: {
                allowancePerformanceId: perfOption?.id,
              },
            }
          : {};
    } else {
      if (perfChildId) {
        isDefaultPerf = false;
        perfOption = getPerfOptionById(perfOptions || [], perfChildId);
      }
    }

    return {
      allowancePerformances: perfOption,
      offerAllowancesGroupInfoMap,
      isDefaultPerf,
    };
  }
};

export const isAllowanceToBeCreatedEditable = (
  allowanceType: string,
  productSources: string[]
) => {
  const productKey =
    productSources?.length > 1 ? BOTH_KEY : productSources?.[0];
  return (
    SHOW_ALLOWANCE_TO_BE_CREATED_OPTIONS?.[allowanceType]?.[productKey]?.options
      ?.length > 1
  );
};

export const checkIsNationalEvent = (eventType: string) => {
  return eventType === ALLOWANCE_SCREEN_TYPES.NDP.key;
};

export const getUpdatedAllowanceTempData = (allowanceData, eventType = "") => {
  const updatedAllowanceData = checkIsNationalEvent(eventType)
    ? allowanceData?.[0] || null
    : allowanceData;
  return updatedAllowanceData;
};

export const getBatchPayloadByDivisions = (
  payload: any,
  divisionIds: string[] = [],
  additionalPrarams: any = {}
) => {
  let params: any = [];
  for (let i = 0; i < divisionIds?.length; i += 3) {
    const batch = divisionIds?.slice(i, i + 3);
    params = [
      ...params,
      {
        ...payload,
        queryParams: { divisionIds: batch?.join(","), ...additionalPrarams },
      },
    ];
  }
  return params;
};

export const postBatchPayloadByDivisions = (
  payload: any,
  divisionIds: string[] = [],
  additionalPrarams: any = {},
  isDivisonsRequired = false
) => {
  const queryPayload: any = [];
  for (let i = 0; i < divisionIds?.length; i += 3) {
    const batchPayload = payload?.slice(i, i + 3) || [];
    queryPayload.push({
      divisionBatch: batchPayload,
      ...additionalPrarams,
      ...(isDivisonsRequired && {
        divisionIds: divisionIds?.slice(i, i + 3),
      }),
    });
  }
  return queryPayload;
};

// Construct vehicle dates object and save in to temp
export function getDatesVehicleForTemp(
  vehicle,
  offerMapKey: string,
  previousValue = {}
) {
  const vehicleObject = {
    startDate: getDateFormat(vehicle?.startDate),
    endDate: getDateFormat(vehicle?.endDate),
    vehicleId: vehicle?.vehicleId,
    vehicleNm: vehicle?.vehicleNm,
    vehicleType: {
      vehicleTypeId: vehicle?.vehicleType.vehicleTypeId,
      vehicleTypDesc: vehicle?.vehicleType.vehicleTypDesc,
    },
    year: vehicle.year,
  };

  const dateRange = {
    startDate: getDateFormat(vehicle?.startDate),
    endDate: getDateFormat(vehicle?.endDate),
  };

  const apiObject = {
    dateRange,
    vehicleId: vehicle?.vehicleId,
    vehicleRef: vehicleObject,
  };

  return getVehicleDatesMapData(offerMapKey, apiObject, previousValue);
}
// Construct National vehicle dates object.
export const getNationalVehicleForTemp = (
  offerDivisions,
  nationalVehicles,
  offerMapKey: string
) => {
  const sortedVehicles = [...nationalVehicles]?.sort((a, b) => {
    const divisionA = a?.divId ? Number(a?.divId) : 0;
    const divisionB = b?.divId ? Number(b?.divId) : 0;
    return divisionA - divisionB;
  });

  const nationalVehicleMapData = sortedVehicles?.reduce(
    (nationalVehiclesInfo: any, item) => {
      nationalVehiclesInfo = offerDivisions?.includes(item?.divId)
        ? {
            ...nationalVehiclesInfo,
            [item?.divId || ""]: getDatesVehicleForTemp(
              item?.vehicle,
              offerMapKey
            ),
          }
        : nationalVehiclesInfo;
      return nationalVehiclesInfo;
    },
    {}
  );
  return nationalVehicleMapData;
};

export const compareVehicleDates = (tempVehicle, nationalVehicle) => {
  return (
    tempVehicle?.vehicleId !== nationalVehicle?.vehicleId ||
    tempVehicle?.startDate !== nationalVehicle?.startDate ||
    tempVehicle?.endDate !== nationalVehicle?.endDate
  );
};

export const compareVehicleDataOfEventAndOffer = (
  nationalVehicleMapData,
  tempworkData,
  allowName: string,
  offerMapKey: string,
  isEditEnable: boolean
) => {
  for (let idx = 0; idx <= tempworkData?.length; idx++) {
    const item = tempworkData?.[idx];
    const tempVehicle =
      item?.allowanceTypeSpecification?.[allowName]?.vehicleDatesMap?.[
        offerMapKey
      ];
    const nationalVehicle =
      nationalVehicleMapData?.[item?.divisionId]?.[offerMapKey]?.vehicleRef;
    if (
      compareVehicleDates(
        {
          ...tempVehicle?.vehicleRef,
          startDate: tempVehicle?.dateRange?.startDate,
          endDate: tempVehicle?.dateRange?.endDate,
        },
        nationalVehicle
      )
    ) {
      return false;
    }
  }
  return isEditEnable;
};

export const compareDivisionIdFn = (a, b) => {
  const divisionA = a?.divisionId ? Number(a?.divisionId) : 0;
  const divisionB = b?.divisionId ? Number(b?.divisionId) : 0;
  return divisionA - divisionB;
};

export const sortDivisionIds = (a, b) => {
  const divisionA = a ? Number(a) : 0;
  const divisionB = b ? Number(b) : 0;
  return divisionA - divisionB;
};

export const getDivisionName = (divId: string) => {
  const { divisionName = "", divisionId = "" } =
    appConstants.ALL_DIVISION_DATA?.find(item => item?.divisionId === divId) ||
    {};
  return divisionName ? `${divisionId} - ${divisionName}` : divId;
};

export const getUniqueDivisions = (
  currentDivs: string[] | number[] = [],
  oldDivs: string[] | number[] = []
) => {
  const newDivs = new Set([...currentDivs, ...oldDivs]);
  return [...newDivs];
};

export const resetOfferDivisionIds = (
  dispatch: any,
  divisionIds: string[] = []
) => {
  dispatch(
    setOfferDivisionsData({
      offerDivisions: divisionIds,
      deletedDivisions: [],
      offerDivisonErrorData: [],
    })
  );
};

export const updateOfferDivisionIds = (
  dispatch: any,
  divisionIds: string[] = [],
  isNational = false,
  tempWorks: any[],
  deletedDivIds?: any[] | null
) => {
  if (isNational && tempWorks?.length) {
    const updatedDivisions = tempWorks
      ?.map(item => item?.divisionId)
      ?.sort(sortDivisionIds);
    dispatch(
      setOfferDivisionsData({
        offerDivisions: updatedDivisions,
        deletedDivisions: deletedDivIds
          ? deletedDivIds
          : divisionIds
              ?.filter((item: string) => !updatedDivisions?.includes(item))
              ?.sort(sortDivisionIds),
      })
    );
  } else {
    dispatch(
      setOfferDivisionsData({
        offerDivisions: divisionIds,
        deletedDivisions: [],
        offerDivisonErrorData: [],
      })
    );
  }
};

const TooltipPopup = ({
  title,
  message,
  isMaxElement,
}: {
  title: string;
  message: string;
  isMaxElement: boolean;
}) => {
  return (
    <Tooltip zIndex={16} anchor="top" variant="light">
      <Tooltip.Popover>
        <span className="flex-col m-1 text-sm font-semibold text-black">
          {message}
        </span>
      </Tooltip.Popover>
      <div className="underline">{`${title}${!isMaxElement ? "," : ""}`}</div>
    </Tooltip>
  );
};

const isUnitedDivExcluded = (divisions: any[], allowanceType?: string) => {
  return (
    divisions?.includes(UNITED_DIVISION_ID) &&
    allowanceType &&
    allowanceType !== SHIPTOSTORE.label
  );
};

export const checkForDeletedDivisions = (
  offerDivisions: any[],
  deletedDivisions: any[],
  allowanceType?: string,
  errorData?: any
) => {
  if (!offerDivisions?.length)
    return OFFER_ITEM_ERRORS.NO_ITEMS_FOR_ALL_DIVISIONS;

  const isUnitedDeleted = isUnitedDivExcluded(deletedDivisions, allowanceType);
  const updateDelDivList = isUnitedDeleted
    ? deletedDivisions?.filter(
        item => item !== (isUnitedDeleted ? UNITED_DIVISION_ID : "")
      )
    : deletedDivisions;

  return updateDelDivList?.length && errorData?.length ? (
    <div className="flex flex-row gap-1">
      <span className="self-center ml-2">{warningIconMaroon}</span>
      <span className="ml-4">These division(s) were excluded:</span>
      {updateDelDivList?.map((item, index) => {
        let errorCode = errorData?.find(
          element => element?.divisionId === item
        )?.errorCode;
        errorCode = errorCode ? errorCode : "DEFAULT";
        const isMaxElement = index === (updateDelDivList?.length || 1) - 1;
        return (
          <TooltipPopup
            title={getDivisionName(item)}
            message={OFFER_ITEM_ERRORS?.[errorCode]}
            isMaxElement={isMaxElement}
          />
        );
      })}
    </div>
  ) : (
    ""
  );
};

export const resetOfferDivisions = (
  eventDivisions: string[],
  allowanceType: string,
  dispatch: any
) => {
  // If UNITED is present in selected national divisions for S2S type.
  const isUnitedDeleted = isUnitedDivExcluded(eventDivisions, allowanceType);
  const updatedDivList = isUnitedDeleted
    ? eventDivisions?.filter(
        item => item !== (isUnitedDeleted ? UNITED_DIVISION_ID : "")
      )
    : eventDivisions;
  const delDivList = isUnitedDeleted ? [UNITED_DIVISION_ID] : [];
  dispatch(
    setOfferDivisionsData({
      offerDivisions: updatedDivList,
      deletedDivisions: delDivList,
      offerDivisonErrorData: [],
    })
  );
  return;
};

export const getVendorDetailsForCombined = (
  allowanceType: string,
  allowMapKey: string,
  allowance: any
) => {
  if (
    !(
      OFFER_ALLOWANCE_GROUP.SCAN.COMBINED === allowMapKey &&
      [SCAN.label, CASE.label, SHIPTOSTORE.label].includes(allowanceType)
    )
  )
    return;

  const { vendorName = "", itemVendorNbr = "" } = allowance || {};
  return (
    <div className="text-base font-bold text-left text-[#033b69]">
      {SCAN.label === allowanceType
        ? " WAREHOUSE - Combined"
        : vendorName && itemVendorNbr
        ? `${vendorName} - ${itemVendorNbr}`
        : ""}
    </div>
  );
};
