import { <PERSON><PERSON><PERSON>ider } from "@apollo/client";
import { app_store, initiateGraphQlCall, ApiErrors } from "@me/data-rtk";
import React from "react";
import { Provider } from "react-redux";
import { RoutesContainer } from "./routes-container";
import { appConstants } from "@me/utils-root-props";

export function App() {
  //TODO: Ravi, added this as part of testing Merchant and Vendor scenarios. Will remove it once done.
  const userType = localStorage.getItem("USER_TYPE");
  sessionStorage.setItem(
    appConstants.SESSION_STORAGE_RESTRICTED_APP_KEY_NAME,
    appConstants.RESTRICTED_APP_NAME_EVENTS
  );
  // adding duplicate session storage key for current app name
  sessionStorage.setItem("currentApp", appConstants.RESTRICTED_APP_NAME_EVENTS);
  localStorage.setItem(
    `${appConstants.RESTRICT_NAV_STORAGE_NAME}${sessionStorage.getItem(
      appConstants.SESSION_STORAGE_RESTRICTED_APP_KEY_NAME
    )}`,
    "false"
  );
  localStorage.setItem("USER_TYPE", userType ? userType : "MERCHANT");

  const client = initiateGraphQlCall;

  return (
    <Provider store={app_store}>
      <ApolloProvider client={client}>
        <React.Suspense fallback={null}>
          <RoutesContainer />
        </React.Suspense>
      </ApolloProvider>
      <ApiErrors />
    </Provider>
  );
}

export default App;
