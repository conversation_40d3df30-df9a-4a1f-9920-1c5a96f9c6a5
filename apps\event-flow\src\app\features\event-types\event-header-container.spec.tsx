import { app_store } from "@me/data-rtk";
import { fireEvent, render, screen } from "@testing-library/react";
import { useForm, FormProvider } from "react-hook-form";
import { Provider } from "react-redux";
import { <PERSON>rowserRouter } from "react-router-dom";
import EventHeaderContainer from "./event-header-container";
import * as selectors from "@me/data-rtk";
import { ca } from "date-fns/locale";

const Wrapper = props => {
  const formMethods = useForm<any>();
  return <FormProvider {...formMethods}>{props.children}</FormProvider>;
};
const eventProgressData = {
  selected: "Event Details",
  sectionNames: ["Event Details", "Allowance", "Promotion"],
  filledSections: ["Event Details", "Allowance", "Promotion"],
  invalidSections: [],
  byPassedSections: [],
};

describe("event header container", () => {
  beforeEach(() => {
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      switch (sel_name) {
        case "event_progress_data":
          return {
            data: eventProgressData,
          };
        case "plan_event_indicators":
          return {
            data: {
              eventDetailsEventInd: false,
              allowanceEventInd: false,
              promotionEventInd: false,
              otherDetailsChangedInd: false,
              pidDetailsEventInd: false,
              planEvent: {},
              planEventPending: {},
            },
          };
        case "event_details_data":
          return {
            data: {
              id: 1,
              eventStatus: "ready",
              eventType: "DP",
              isChangeEventTypeVisible: false,
            },
          };
        case "allowance_temp_work":
          return {
            data: { allowanceType: "SCAN" },
          };
        default:
          break;
      }
    });
  });
  it("should render event header container", async () => {
    const { getAllByText } = render(
      <Provider store={app_store}>
        <BrowserRouter>
          <Wrapper>
            <EventHeaderContainer />
          </Wrapper>
        </BrowserRouter>
      </Provider>
    );

    expect(getAllByText("New Event").length).toBe(1);
  });

  it("should render event header container with popup", async () => {

    const { getAllByText } = render(
      <Provider store={app_store}>
        <BrowserRouter>
          <Wrapper>
            <EventHeaderContainer />
          </Wrapper>
        </BrowserRouter>
      </Provider>
    );

    expect(getAllByText("New Event").length).toBe(1);
  });
});
