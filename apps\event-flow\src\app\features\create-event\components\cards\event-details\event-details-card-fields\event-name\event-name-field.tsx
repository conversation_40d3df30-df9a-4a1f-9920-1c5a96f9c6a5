import { InputText } from "@me/input-fields";
import React, { memo } from "react";
import { useFormContext } from "react-hook-form";
import { onChangeDateAndNameHandler } from "../../utility/utility";
import { validateNCDP } from "../../event-details-card-service";
import { useSelectorWrap } from "@me/data-rtk";

function EventNameField({ eventName, isEditEvent, formFields, setFormFields }) {
  const { getValues, setValue } = useFormContext();

  const { data: eventTypeAndDivisionsData } = useSelectorWrap(
    "event_type_and_divisions"
  );
  const eventType = eventTypeAndDivisionsData?.eventType || "";

  const onChangeHandler = element => {
    onChangeDateAndNameHandler({
      element,
      setFormFields,
      getValues,
      setValue,
    });
  };
  return (
    <div className="grid grid-cols-2 w-full">
      {eventName ? (
        <InputText
          onChange={onChangeHandler}
          fieldProps={eventName}
          disabled={validateNCDP(eventType) || false}
          isRequired={isEditEvent}
          dynamicRegisterField={eventName?.registerField}
          builtInError={formFields?.eventError}
        />
      ) : null}
    </div>
  );
}

export default memo(EventNameField);
