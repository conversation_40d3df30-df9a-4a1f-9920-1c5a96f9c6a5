import * as React from "react";
import EventCreationLayout from "../create-event/components/cards/layout/event-creation-layout";
import EventHeaderContainer from "./event-header-container";
import EventHookFormWrapper from "./event-hook-form-wrapper";
import EventProgressContainer from "./event-progress-container";
import EventWrapperLayout from "./event-wrapper-layout";
import colors from "@albertsons/uds/foundation/color";
import { Loader } from "../create-event/constants/LoadingSpinner/Loader";
import { useSelectorWrap } from "@me/data-rtk";
import { useSearchParams } from "react-router-dom";
import efConstants from "../../shared/ef-constants/ef-constants";
import { useDispatch } from "react-redux";
import { eventProgressDataHandler } from "../create-event/service/slice/event-progress-slice";
import ChildGridWrapper from "./components/child-grid/child-grid-wrapper/child-grid-wrapper";
import { eventTypes } from "./constants/event-type-constant";
import { appConstants } from "@me/utils-root-props";
import { setEventTypeAndDivsionsHandler } from "../create-event/service/slice/event-detail-slice";

interface IEventCreationContainerProps {}

const EventCreationContainer: React.FunctionComponent<
  IEventCreationContainerProps
> = props => {
  const dispatch = useDispatch();
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { id: eventDataId = "", offerAllowances = [] } = eventDetailsData || {};
  const divisionIdsAvailable = eventDetailsData?.divisionIds?.length;
  const dataFetchedFromPeriscope = eventDetailsData?.dataFetchedFromPeriscope;
  const [searchParams] = useSearchParams();
  const eventTypeName = searchParams.get("eventType") || "";
  const [DP, AO, NDP, NAO] = eventTypes.map(event => event?.eventType);
  const isEventCreation = [DP, AO, NDP, NAO].includes(eventTypeName);
  const { data: eventTypeAndDivisionsData } = useSelectorWrap(
    "event_type_and_divisions"
  );
  const { NATIONAL_CHANGES } = appConstants;
  React.useEffect(() => {
    dispatch(
      eventProgressDataHandler({
        byPassedSections: [],
      })
    );
  }, []);
  React.useEffect(() => {
    dispatch(
      setEventTypeAndDivsionsHandler({
        ...eventTypeAndDivisionsData,
        eventType: eventTypeName,
      })
    );
    localStorage.setItem("EVENT_TYPE", eventTypeName);
  }, [dispatch, eventTypeName]);

  return (
    <div
      id="abs-event-creation-container"
      className={`min-h-screen bg-[#f1f4F9] px-[20px] ${efConstants.componentClassName.EVENT_CREATION_CONTAINER}`}
    >
      <EventWrapperLayout>
        <EventHookFormWrapper>
          {divisionIdsAvailable ||
          isEventCreation ||
          dataFetchedFromPeriscope ? (
            <>
              <EventHeaderContainer />
              <div
                id="abs-event-creation-container-grid"
                className="grid grid-cols-12 gap-5 mt-[100px]"
              >
                <EventCreationLayout id="abs-event-creation-container-layout"></EventCreationLayout>
                {/* commenting right section as part of UPP-16214 */}
                {/* <div
                  id="abs-event-creation-container-progress"
                  className="col-span-3"
                >
                  <EventProgressContainer />
                </div> */}
              </div>
            </>
          ) : (
            <Loader wrapperClassName="min-w-full min-h-screen flex justify-center items-center bg-blue-308" />
          )}
        </EventHookFormWrapper>
        {eventDataId &&
        offerAllowances?.length > 0 &&
        eventDetailsData?.eventType === appConstants.NDP_EVENT_TYPE ? (
          <ChildGridWrapper />
        ) : null}
      </EventWrapperLayout>
    </div>
  );
};

export default React.memo(EventCreationContainer);
