import { EEVENT_STATUS, getLoggedInUserType } from "@me/util-helpers";
import {
  isPeriscopeValidFromApi,
  setNdVendorDivisions,
} from "./utility/utility";

export const showEventDeatilsCard = eventDetailsData => {
  const userDetails = localStorage.getItem("USER_ROLE_PERMISSIONS");
  const vendNumsList = JSON.parse(localStorage.getItem("vendorNum") || "[]");
  const parsedUserDetails = JSON.parse(userDetails || "[]");
  const { divisionIds, simsVendors, manufacturerSimsVendors } =
    eventDetailsData;
  const simsCheck = simsVendors?.every(n => vendNumsList.includes(n));
  const manufacturerSimsCheck = manufacturerSimsVendors?.every(manuVendor =>
    vendNumsList.includes(manuVendor)
  );

  const filteredDivisionIds = parsedUserDetails?.filter(item => {
    const userDivisionIds = item?.userDivisions.map(d => d.divisionId?.trim());
    return divisionIds?.some(d => userDivisionIds?.includes(d));
  });
  return !!filteredDivisionIds?.length && (simsCheck || manufacturerSimsCheck);
};

const DASH_LENGTH = 5; // ppg string that includes string after name
const DASHLESS_LENGTH = 4; // ppg string with name that does not include dash  as part of the name

export const ppgFieldName = (ppgName: string): string => {
  if (ppgName) {
    const ppgParts = ppgName.split(" - ");
    let ppgNameAndId: string;
    if (ppgParts.length === DASH_LENGTH) {
      ppgNameAndId = ppgParts.slice(1, 3).join("-");
    } else if (ppgParts.length === DASHLESS_LENGTH) {
      ppgNameAndId = ppgParts.slice(1, 2).join("-");
    } else {
      ppgNameAndId = ppgName;
    }

    return ppgNameAndId.trim(); // if above operations were out of bound, or invalid split, empty string will be returned
  }
  return "";
};

export const getEventDetailsCardChangedFields = (
  eventDetailsData,
  getValues
) => {
  let pIdDetailsEventInd = false;
  let otherDetailsChangedInd = false;
  if (
    [
      EEVENT_STATUS.PENDING_WITH_MERCHANT,
      EEVENT_STATUS.PENDING_WITH_VENDOR,
      EEVENT_STATUS.AGREED,
      EEVENT_STATUS.AGREED_PENDING,
      EEVENT_STATUS.ACTIVE,
      EEVENT_STATUS.EXECUTED,
    ].includes(eventDetailsData.eventStatus)
  ) {
    const eventCreationVehicleChanged =
      eventDetailsData.eventCreationVehicle.vehicleType.vehicleTypeId !==
      getValues("eventCreationVehicle.vehicleType.vehicleTypeId");
    const startDateChanged =
      eventDetailsData.eventCreationVehicle.startDate !==
      getValues("eventCreationVehicle.startDate");
    const endDateChanged =
      eventDetailsData.eventCreationVehicle.endDate !==
      getValues("eventCreationVehicle.endDate");
    const eventNameChanged =
      eventDetailsData.name?.trim() !== getValues("name")?.trim();
    if (
      eventCreationVehicleChanged ||
      startDateChanged ||
      endDateChanged ||
      eventNameChanged
    ) {
      otherDetailsChangedInd = true;
    }
    if (
      getValues("periscopeFormField") !== undefined &&
      eventDetailsData?.periscopeDetails?.[0]?.periscopeId !==
        getValues("periscopeFormField")
    ) {
      pIdDetailsEventInd = true;
    }
  }
  return { pIdDetailsEventInd, otherDetailsChangedInd };
};
export const modifyEventDetailsData = eventDetailsData => {
  let updateEventDetailsData = eventDetailsData;
  const { vendorNumbers, divisions } = setNdVendorDivisions();
  const userRole = getLoggedInUserType();

  // check logged in user vendor for divisions
  if (
    (eventDetailsData?.eventType === "NDP" ||
      eventDetailsData?.divisionIds?.includes("98")) &&
    userRole === "VENDOR"
  ) {
    //filter storegroups based on divisions
    const filteredStoreGroups = eventDetailsData?.storeGroups?.filter(
      storeGroup => {
        const storeGroupDivisions = storeGroup?.divisionIds || [];
        return storeGroupDivisions.some(div => divisions.includes(div));
      }
    );
    updateEventDetailsData = {
      ...updateEventDetailsData,
      divisionIds: divisions,
      storeGroups: filteredStoreGroups,
    };
  }

  if (!updateEventDetailsData?.planStoreGroupType) {
    const storeGroupType = updateEventDetailsData?.storeGroups;
    updateEventDetailsData = {
      ...updateEventDetailsData,
      planStoreGroupType: storeGroupType?.[0]?.storeGroupType,
    };
  }
  updateEventDetailsData = {
    ...updateEventDetailsData,
    startDate: updateEventDetailsData?.eventCreationVehicle?.startDate,
    endDate: updateEventDetailsData?.eventCreationVehicle?.endDate,
    vehicleType:
      updateEventDetailsData?.eventCreationVehicle?.vehicleType?.vehicleTypDesc,
    promoProductGroup: updateEventDetailsData?.planProductGroups?.[0]?.name,
    startWeekVehicle: updateEventDetailsData?.eventCreationVehicle?.vehicleNm,
    periscopeValidFromApi: isPeriscopeValidFromApi(
      updateEventDetailsData?.periscopeDetails
    ),
  };
  return updateEventDetailsData;
};
