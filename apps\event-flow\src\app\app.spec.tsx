import { render } from "@testing-library/react";
import { <PERSON>rowserRouter } from "react-router-dom";
import App from "./app";

class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
}

window.ResizeObserver = ResizeObserver as any;

jest.mock("react-pdf", () => ({
  Document: jest.fn(({ children }) => children),
  Page: jest.fn(() => <div data-testid="mock-page"></div>),
  pdfjs: {
    GlobalWorkerOptions: {
      workerSrc: "",
    },
    version: "2.10.377",
  },
}));
describe("App", () => {
  it("should render successfully", () => {
    const { baseElement } = render(
      <BrowserRouter>
        <App />
      </BrowserRouter>
    );

    expect(baseElement).toBeTruthy();
  });
});
