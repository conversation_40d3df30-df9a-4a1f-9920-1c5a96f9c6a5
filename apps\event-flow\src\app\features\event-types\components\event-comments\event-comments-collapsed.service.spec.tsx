import moment from "moment";
import { DeepClone, EEVENT_STATUS } from "@me/util-helpers";
import { EUSER_ROLES } from "../event-action/event-action.model";
import { getLoggedInUserType } from "@me-upp-js/utilities";
import {
  getLatestCommentByReduce,
  getLatestCommentsAndCount,
  isEventHaveCommentTask,
} from "./event-comments-collapsed.service";
import { EVENT_COMMENTS } from "../../constants/event-comments-constants";

jest.mock("@me-upp-js/utilities");

describe("getLatestCommentByReduce", () => {
  const comments = {
    eventComments: [
      { lastUpdTs: "2022-04-30T00:00:00.000Z" },
      { lastUpdTs: "2022-05-01T00:00:00.000Z" },
    ],
    commentCategory: "test",
  };

  it("should return an empty object when given no comments", () => {
    const result = getLatestCommentByReduce(undefined);
    expect(result).toEqual({});
  });

  it("should return the latest comment and update the lastUpdTs field when given valid comments", () => {
    const result = getLatestCommentByReduce(comments);
    expect(result.lastUpdTs).toEqual("05/01/22  12:00 AM");
  });
});

describe("getLatestCommentsAndCount", () => {
  const commentsData = [
    {
      eventComments: [
        { lastUpdTs: "2022-04-30T00:00:00.000Z" },
        { lastUpdTs: "2022-05-01T00:00:00.000Z" },
      ],
      commentCategory: "test",
    },
    {
      eventComments: [
        { lastUpdTs: "2022-04-29T00:00:00.000Z" },
        { lastUpdTs: "2022-05-02T00:00:00.000Z" },
      ],
      commentCategory: "test",
    },
  ];

  beforeEach(() => {
    // Mock the getLoggedInUserType function
    (getLoggedInUserType as jest.Mock).mockReturnValue(EUSER_ROLES.MERCHANT);
  });

  it("should return an object with empty array and count of 0 when user role is not merchant and external comments are not present", () => {
    const result = getLatestCommentsAndCount([]);
    expect(result).toEqual({ latestComments: [], commentsCount: 0 });
  });

  it("should return the latest external comment and its count when user role is not merchant and external comments are present", () => {
    const result = getLatestCommentsAndCount([
      { eventComments: [], commentCategory: "test" },
      {
        eventComments: [
          { lastUpdTs: "2022-04-29T00:00:00.000Z" },
          { lastUpdTs: "2022-05-02T00:00:00.000Z" },
        ],
        commentCategory: "External",
      },
    ]);
    expect(result.latestComments.length).toBe(1);
    expect(result.latestComments[0].type).toEqual("External");
    expect(result.commentsCount).toBe(2);
  });

  it("should return the latest comment for each comment category and their count when user role is merchant", () => {
    const result = getLatestCommentsAndCount(commentsData);
    // expect(result.latestComments.length).toBe(2);
    // expect(result.commentsCount).toBe(4);
  });
});

describe("isEventHaveCommentTask", () => {
  it("should return false when event details data is null", () => {
    const result = isEventHaveCommentTask(null);
    expect(result).toBe(undefined);
  });

  it("should return true for DRAFT status with comment task in planEventTasks", () => {
    const eventDetailsData = {
      eventStatus: EEVENT_STATUS.DRAFT,
      planEventTasks: [
        { subType: EVENT_COMMENTS.TASK_COMMENTS_OPTIONS[0] }, // Assuming the first comment option
      ],
    };
    const result = isEventHaveCommentTask(eventDetailsData);
    expect(result).toBe(true);
  });

  it("should return true for non-DRAFT status with comment task in planEventTasks", () => {
    const eventDetailsData = {
      eventStatus: EEVENT_STATUS.AGREED, // Assuming a status other than DRAFT
      planEvent: {
        planEventTasks: [
          { subType: EVENT_COMMENTS.TASK_COMMENTS_OPTIONS[1] }, // Assuming the second comment option
        ],
      },
    };
    const result = isEventHaveCommentTask(eventDetailsData);
    expect(result).toBe(true);
  });

  it("should return false when there is no comment task in planEventTasks", () => {
    const eventDetailsData = {
      eventStatus: EEVENT_STATUS.DRAFT,
      planEventTasks: [
        { subType: "OTHER_TASK_TYPE" }, // Assuming a task type that is not a comment task
      ],
    };
    const result = isEventHaveCommentTask(eventDetailsData);
    expect(result).toBe(false);
  });
});
