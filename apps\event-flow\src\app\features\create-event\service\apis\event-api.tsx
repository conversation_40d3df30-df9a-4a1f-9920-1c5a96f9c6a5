import { injectEndPointsWrapper } from "@me/data-rtk";
import { API_EVENT_URL } from "../api-url";

const apiObj = {
  endPointsData: [
    //Here pass all the urls
    {
      ep_url: API_EVENT_URL["EVENT_API"],
      ep_type: "GET",
      ep_name: "getEventData",
    },
    {
      ep_url: API_EVENT_URL["EVENT_PERISCOPE"],
      ep_type: "GET",
      ep_name: "getEventPeriscopeData",
    },
    {
      ep_url: API_EVENT_URL["EVENT_API"],
      ep_type: "POST",
      ep_name: "postEventData",
    },
    {
      ep_url: API_EVENT_URL["EVENT_API"],
      ep_type: "PUT",
      ep_name: "putEventData",
    },
    {
      ep_url: API_EVENT_URL["PLAN_EVENT_API"],
      ep_type: "GET",
      ep_name: "getPlanEventData",
    },
    {
      ep_url: API_EVENT_URL["PLAN_POST_EVENT_API"],
      ep_type: "POST",
      ep_name: "postPlanEventData",
    },
    {
      ep_url: API_EVENT_URL["ALL_EVENT_COMMENTS"],
      ep_type: "GET",
      ep_name: "getCommentsData",
    },
    {
      ep_url: API_EVENT_URL["VIEW_PLAN_PROMO_PRODUCT_GROUP"],
      ep_type: "POST",
      ep_name: "postPlanPromoProductGroup",
    },
    {
      ep_url: API_EVENT_URL["VIEW_PLAN_ITEMS"],
      ep_type: "POST",
      ep_name: "postPlanItems",
    },
    {
      ep_url: API_EVENT_URL["VIEW_STORE_GROUP_STORES"],
      ep_type: "POST",
      ep_name: "postStoreGroupStores",
    },
    {
      ep_type: "POST",
      ep_name: "postProductLocationValidation",
      ep_url: API_EVENT_URL["PRODUCT_LOCATION_VALIDATION"],
    },
    {
      ep_type: "POST",
      ep_name: "postPlanEventItemsByCICs",
      ep_url: API_EVENT_URL["GET_CICS_PPG_ITEMS_API"],
    },
    {
      ep_type: "POST",
      ep_name: "postPlanPPGsResolve",
      ep_url: API_EVENT_URL["PLAN_PPG_RESOLVE_API"],
    },
    {
      ep_type: "POST",
      ep_name: "postPlanEventItemMin",
      ep_url: API_EVENT_URL["ITEM_MIN_API"],
    },
    {
      ep_type: "GET",
      ep_name: "getChildEventDetails",
      ep_url: API_EVENT_URL["CHILD_EVENT_DETAILS_API"],
    },
  ],
};

const divisionApiObj = {
  endPointsData: [
    {
      ep_type: "GET",
      ep_name: "fetchDivisionData",
      ep_url: API_EVENT_URL["GET_DIVISION_API"],
    },
  ],
};

//The naming for the  methods  decalered on the LHS would be derived from the `use${capitalizeFirstLetter(ep_name)}Mutation/Query`
export const {
  useGetEventDataQuery,
  useLazyGetEventPeriscopeDataQuery,
  usePostEventDataMutation,
  usePutEventDataMutation,
  useGetPlanEventDataQuery,
  usePostPlanEventDataMutation,
  useGetCommentsDataQuery,
  usePostPlanPromoProductGroupMutation,
  usePostPlanItemsMutation,
  usePostStoreGroupStoresMutation,
  usePostProductLocationValidationMutation,
  usePostPlanEventItemsByCICsMutation,
  usePostPlanPPGsResolveMutation,
  usePostPlanEventItemMinMutation,
  useLazyGetChildEventDetailsQuery,
} = injectEndPointsWrapper(apiObj);
export const { useFetchDivisionDataQuery } =
  injectEndPointsWrapper(divisionApiObj);
