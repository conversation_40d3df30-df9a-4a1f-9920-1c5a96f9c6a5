import { createGenericSlice } from "@me/data-rtk";


export const nationalDivisionsConfig = createGenericSlice({
  name: "national_divisions_config",
  initialState: {
    status: "loading",
    data: {
      selectedDivisionData: {},
      divisionsList: [],
      isNdpType: false
    },
  },
})({
  setNationalDivisionsConfig(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
  resetNationalDivisionsConfig(state) {
    state.data = {
      selectedDivisionData: {},
      divisionsList: [],
      isNdpType: false
    }
  },
});
export const { setNationalDivisionsConfig, resetNationalDivisionsConfig } =
  nationalDivisionsConfig.actions;


export const allDivisionWarningData = createGenericSlice({
  name: "all_divisions_warning_data",
  initialState: {
    status: "loading",
    data: {
      divisionErrObj:{},
      divisionWarningObj:{}
    },
  },
})({
  setAlDivisionWarningData(state, { payload }) {
    state.data = { ...state.data, ...payload };
  },
  resetAllDivisionsWarningData(state) {
    state.data = {
      divisionErrObj:{},
      divisionWarningObj:{}
    };
  },
});
export const { setAlDivisionWarningData, resetAllDivisionsWarningData } =
  allDivisionWarningData.actions;

export const allDivisionAllowancesRespCopySlice = createGenericSlice({
  name: "all_divisions_allowancesRespCopy_rn",
  initialState: {
    status: "loading",
    data: [],
  },
})({
  setAllDivAllowancesRespCopy(state, { payload }) {
    state.data = payload;
  },
  resetAllDivAllowancesRespCopy(state) {
    state.data = [];
  }
});

export const { setAllDivAllowancesRespCopy, resetAllDivAllowancesRespCopy } = allDivisionAllowancesRespCopySlice.actions;

export const nationalHeaderOnlyAmtSlice = createGenericSlice({
  name: "national_headerOnlyAmt_rn",
  initialState: {
    status: "loading",
    data: {},
  },
})({
  setNationalHeaderOnlyAmt(state,{ payload: { warehouseIndex, val, divisionId } }) {
    const stateCopy = JSON.parse(JSON.stringify(state.data));
    if (divisionId) {
      if (!stateCopy?.[divisionId]) {
        stateCopy[divisionId] = [];
      }
      stateCopy[divisionId][warehouseIndex] = val;
    }
    state.data = stateCopy;
  },
  resetNationalHeaderOnlyAmt(state) {
    state.data = {};
  }
});
export const { setNationalHeaderOnlyAmt, resetNationalHeaderOnlyAmt } = nationalHeaderOnlyAmtSlice.actions;

export const divisionWiseSharedWhseDataSlice = createGenericSlice({
  name: "division_wise_shared_whse_data_rn",
  initialState: {
    status: "loading",
    data: [],
  },
})({
  setDivisionWiseShrdWhseData(state, { payload }) {
    state.data = payload;
  },
  resetDivisionWiseShrdWhseData(state) {
    state.data = [];
  },
});

export const { setDivisionWiseShrdWhseData, resetDivisionWiseShrdWhseData } =
  divisionWiseSharedWhseDataSlice.actions;
