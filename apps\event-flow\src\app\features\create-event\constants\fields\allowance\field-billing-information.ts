import efConstants from "../../../../../shared/ef-constants/ef-constants";

const { ALLOWANCE_TYPES, REGEX_PATTERN } = efConstants;
const AC_AP_CHAR_LIMIT = 8;
export const VENDOR_COMMENT_CHAR_LIMIT = 1000;
const VENDOR_TRACKING_CHAR_LIMIT = 30;

export const BILLING_INFORMATION = {
  allowanceSpecificFields: {
    [ALLOWANCE_TYPES.CASE.key]: {
      DSD: {
        key: "DSD_LEAD_DISTRIBUTORS",
        routeKey: "DSD_LEAD_DISTRIBUTORS",
        billingInformationData: {
          registerKeyName: "billingInformationData.DSD_LEAD_DISTRIBUTORS",
        },
      },
      WAREHOUSE: {
        key: "WAREHOUSE_DIST_CENTERS",
        routeKey: "WAREHOUSE_DIST_CENTERS",
        billingInformationData: {
          registerKeyName: "billingInformationData.WAREHOUSE_DIST_CENTERS",
        },
      },
      COMBINED: {
        key: "DSD_WHSE_RETAIL_DIVISION",
        routeKey: "DSD_WHSE_RETAIL_DIVISION",
        billingInformationData: {
          registerKeyName: "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
        },
      },
    },
    [ALLOWANCE_TYPES.SCAN.key]: {
      DSD: {
        key: "DSD_LEAD_DISTRIBUTORS",
        routeKey: "DSD_LEAD_DISTRIBUTORS",
        billingInformationData: {
          registerKeyName: "billingInformationData.DSD_LEAD_DISTRIBUTORS",
        },
      },
      WAREHOUSE: {
        key: "DSD_WHSE_RETAIL_DIVISION",
        routeKey: "DSD_WHSE_RETAIL_DIVISION",
        billingInformationData: {
          registerKeyName: "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
        },
      },
    },
    [ALLOWANCE_TYPES.SHIPTOSTORE.key]: {
      DSD: {
        key: "DSD_LEAD_DISTRIBUTORS",
        routeKey: "DSD_LEAD_DISTRIBUTORS",
        billingInformationData: {
          registerKeyName: "billingInformationData.DSD_LEAD_DISTRIBUTORS",
        },
      },
      WAREHOUSE: {
        key: "DSD_WHSE_RETAIL_DIVISION",
        routeKey: "DSD_WHSE_RETAIL_DIVISION",
        billingInformationData: {
          registerKeyName: "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
        },
      },
    },
    [ALLOWANCE_TYPES.HEADERFLAT.key]: {
      DSD: {
        key: "DSD_WHSE_RETAIL_DIVISION",
        routeKey: "DSD_WHSE_RETAIL_DIVISION",
        billingInformationData: {
          registerKeyName: "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
        },
      },
      WAREHOUSE: {
        key: "WAREHOUSE_DIST_CENTERS",
        routeKey: "WAREHOUSE_DIST_CENTERS",
        billingInformationData: {
          registerKeyName: "billingInformationData.WAREHOUSE_DIST_CENTERS",
        },
      },
    },
    [ALLOWANCE_TYPES.ITEMFLAT.key]: {
      DSD: {
        key: "DSD_WHSE_RETAIL_DIVISION",
        routeKey: "DSD_WHSE_RETAIL_DIVISION",
        billingInformationData: {
          registerKeyName: "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
        },
      },
      WAREHOUSE: {
        key: "WAREHOUSE_DIST_CENTERS",
        routeKey: "WAREHOUSE_DIST_CENTERS",
        billingInformationData: {
          registerKeyName: "billingInformationData.WAREHOUSE_DIST_CENTERS",
        },
      },
    },
    DEFAULT: {
      key: "DSD_WHSE_RETAIL_DIVISION",
      routeKey: "DSD_WHSE_RETAIL_DIVISION",
      billingInformationData: {
        registerKeyName: "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
      },
    },
  },
  suggestedVendorPaymentType: {
    label: "",
    required: false,
    registerField: "suggestedVendorPaymentType",
    type: "select",
    default: "Deduct",
    displayLabel: "name",
    options: [
      {
        name: "Select",
        id: "",
      },
      {
        name: "Deduct",
        id: "Deduct",
      },
      {
        name: "Invoice",
        id: "Invoice",
      },
    ],
    apiUrl: "",
    slice: "",
    errors: {
      required: {
        backgroundColor: "",
        text: "ABS Vendor Payment Type is Required",
      },
    },
    tooltip:
      "This is auto generated based on your selections.\n You can edit this if you want.",
    mapperKey:
      "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
  },
  commonSuggestedVendorPaymentType: {
    label: "",
    required: false,
    registerField: "suggestedVendorPaymentTypeAll",
    type: "select",
    default: "Deduct",
    displayLabel: "name",
    options: [
      {
        name: "Select",
        id: "",
      },
      {
        name: "Deduct",
        id: "Deduct",
      },
      {
        name: "Invoice",
        id: "Invoice",
      },
    ],
    apiUrl: "",
    slice: "",
    errors: {
      required: {
        backgroundColor: "",
        text: "ABS Vendor Payment Type is Required",
      },
    },
    tooltip:
      "This is auto generated based on your selections.\n You can edit this if you want.",
  },
  acApOrArNumber: {
    label: "",
    required: false,
    registerField: "acApOrArNumber",
    type: "number",
    errors: {
      required: {
        backgroundColor: "",
        text: "A/P or A/R Vendor Number is Required",
      },
      formatError: {
        pattern: REGEX_PATTERN.NUMBERS_WITH_ASTERISK_ONLY,
        text: "Please enter valid A/P or A/R Vendor Number",
      },
      maxLength: {
        length: AC_AP_CHAR_LIMIT,
        text: `Maximum ${AC_AP_CHAR_LIMIT} characters allowed`,
      },
    },
    mapperKey: "offerallowances.allowances.allowanceBillingInfo.acApOrArNumber",
  },
  commonAcApOrArNumber: {
    label: "",
    required: false,
    registerField: "acApOrArNumberAll",
    type: "number",
    errors: {
      required: {
        backgroundColor: "",
        text: "A/P or A/R Vendor Number is Required",
      },
      formatError: {
        pattern: REGEX_PATTERN.NUMBERS_WITH_ASTERISK_ONLY,
        text: "Please enter valid A/P or A/R Vendor Number",
      },
      maxLength: {
        length: AC_AP_CHAR_LIMIT,
        text: `Maximum ${AC_AP_CHAR_LIMIT} characters allowed`,
      },
    },
  },
  vendorComment: {
    label: "",
    required: false,
    registerField: "vendorComment",
    type: "text",
    placeholder: "Enter Vendor Comments",
    errors: {
      required: {
        backgroundColor: "",
        text: "Vendor Comment is Required",
      },
      maxLength: {
        length: VENDOR_COMMENT_CHAR_LIMIT,
        text: `Maximum ${VENDOR_COMMENT_CHAR_LIMIT} characters allowed`,
      },
    },
    mapperKey: "offerallowances.allowances.allowanceBillingInfo.vendorComment",
  },
  vendorOfferTrackingNbr: {
    label: "Vendor Tracking Number",
    required: false,
    registerField: "vendorOfferTrackingNbr",
    type: "text",
    errors: {
      required: {
        backgroundColor: "",
        text: "Vendor Tracking Number is Required",
      },
      maxLength: {
        length: VENDOR_TRACKING_CHAR_LIMIT,
        text: `Maximum ${VENDOR_TRACKING_CHAR_LIMIT} characters allowed`,
      },
    },
    mapperKey:
      "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
  },
  commonComment: {
    label: "",
    required: false,
    registerField: "allComment",
    placeholder: "Enter Vendor Comments",
    type: "text",
    errors: {
      required: {
        backgroundColor: "",
        text: "Vendor Comment is Required",
      },
      maxLength: {
        length: VENDOR_COMMENT_CHAR_LIMIT,
        text: `Maximum ${VENDOR_COMMENT_CHAR_LIMIT} characters allowed`,
      },
    },
  },
  billingInformationData: {
    registerKeyName: "billingInformationData",
  },
};
