import { useFormContext } from "react-hook-form";
import { getAllowanceFormRegister<PERSON>ey } from "../../../../../service/allowance/allowance-service";
// eslint-disable-next-line @nrwl/nx/enforce-module-boundaries
import StepperPreviewContainer from "libs/preview-card/src/lib/preview-card-container/preview-card-container";
// eslint-disable-next-line @nrwl/nx/enforce-module-boundaries
import PreviewCardFields from "libs/preview-card/src/lib/preview-card-fields/preview-card-fields";
import { getConstRegKey, showEditedLabel } from "@me/util-helpers";

const AllowanceDefaultBillingInfoPreview: React.FunctionComponent<any> = ({
  previewConfigObj,
  allowStepperType,
  stepperIndex,
  steppersData,
  offerIndex,
  allowIndex,
  childClassName,
}) => {
  const { getValues } = useFormContext();
  const { fieldsHistoryKeys = null } = previewConfigObj;
  const allowanceRegisterField = getConstRegKey(
    "allowance",
    offerIndex,
    allowIndex,
    getValues
  );

  const allowanceBillingRegisterField = `${allowanceRegisterField}.allowanceBillingInfo`;

  const fieldsMapper = {
    absVendorPaymentType: `${allowanceBillingRegisterField}.absVendorPaymentType`,
    absVendorName: `${allowanceBillingRegisterField}.absVendorName`,
    billingName:
      getValues(`${allowanceBillingRegisterField}.billingName`) === undefined
        ? `${allowanceBillingRegisterField}.absVendorName`
        : `${allowanceBillingRegisterField}.billingName`,
    absMerchVendor: `${allowanceBillingRegisterField}.absMerchVendor`,
    acReceivableVendorNbr: `${allowanceBillingRegisterField}.acReceivableVendorNbr`,
    acPayableVendorNbr: `${allowanceBillingRegisterField}.acPayableVendorNbr`,
    suggestedVendorPaymentType: `${allowanceBillingRegisterField}.suggestedVendorPaymentType`,
    suggestedAcPayableVendorNbr: `${allowanceBillingRegisterField}.suggestedAcPayableVendorNbr`,
    suggestedAcReceivableVendorNbr: `${allowanceBillingRegisterField}.suggestedAcReceivableVendorNbr`,
    vendorComment: `${allowanceBillingRegisterField}.vendorComment`,
    vendorOfferTrackingNbr: `${allowanceBillingRegisterField}.vendorOfferTrackingNbr`,
    baseKey: `${allowanceRegisterField}`,
  };

  const isShowEdited = showEditedLabel(
    fieldsHistoryKeys,
    getValues,
    offerIndex,
    allowIndex,
    "allowance"
  );

  const getPreviewFields = (
    <PreviewCardFields
      previewConfigObj={previewConfigObj}
      fieldsMapper={fieldsMapper}
      module="allowance"
      getValues={getValues}
      cardIndex={offerIndex}
      cardItemIndex={allowIndex}
      getCustomPlanValue={null}
      className={childClassName}
    />
  );

  return (
    <StepperPreviewContainer
      stepperPreviewHeaderLabel={allowStepperType}
      stepperPreviewHeaderValue={checkIfBillingInfoChanged({
        suggestedVendorPaymentType: getValues(
          fieldsMapper.suggestedVendorPaymentType
        ),
        suggestedAcPayableVendorNbr: getValues(
          fieldsMapper.suggestedAcPayableVendorNbr
        ),
        suggestedAcReceivableVendorNbr: getValues(
          fieldsMapper.suggestedAcReceivableVendorNbr
        ),
      })}
      steppersData={steppersData}
      stepperIndex={stepperIndex}
      isShowEdited={isShowEdited}
    >
      {getPreviewFields}
    </StepperPreviewContainer>
  );
};

export default AllowanceDefaultBillingInfoPreview;

export function checkIfBillingInfoChanged({
  suggestedVendorPaymentType,
  suggestedAcPayableVendorNbr,
  suggestedAcReceivableVendorNbr,
}) {
  if (
    suggestedVendorPaymentType ||
    suggestedAcPayableVendorNbr ||
    suggestedAcReceivableVendorNbr
  )
    return "Changed";
  return "Unchanged";
}
