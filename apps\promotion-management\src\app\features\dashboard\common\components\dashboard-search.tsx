import Search from "@albertsons/uds/molecule/Search";
import { useSelectorWrap } from "@me/data-rtk";
import {
  DASHBOARD_VIEW_KEY_MAPPER,
  updateSearchKeyConfig,
  SEARCH_VALUE_CONFIG,
} from "../../../../config/dashboard-config";
import { SelectDropDown } from "apps/promotion-management/src/app/shared/ui/atoms";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { processSearchText } from "../utils/planning-sub-header-service";
import { isEmpty } from "lodash";
import {
  getDefaultPaginationByView,
  getSearchDopdown,
} from "@me/promotion-management/common/helpers";
import { setValidateDuplicateEventsResHandler } from "@me/promotion-management/events-container/event-task-feature-slice";
import useResetGridData from "apps/promotion-management/src/app/shared/hooks/useResetGridData";

type Props = {
  viewKey?: string;
  action: any;
  sliceKey: string;
  viewType?: string;
};

export default function DashboardSearch({
  viewKey = "",
  action,
  sliceKey,
  viewType,
}: Props) {
  const dispatch = useDispatch();
  const { data: filterData } = useSelectorWrap(sliceKey) || {};

  const { data: duplicateEventsResp } = useSelectorWrap(
    "validateDuplicateEventsRes_rn"
  );
  const {
    data: { isOpen },
  } = useSelectorWrap("duplicateModal_rn");

  const isValidEventIds = !isOpen && duplicateEventsResp?.length;
  const { resetGridData } = useResetGridData();
  const SEARCH_KEY_CONFIG = updateSearchKeyConfig(viewType);
  const defaultSearchKey = SEARCH_KEY_CONFIG?.options?.find(
    item => item?.isDefaultValue
  );
  const [value, setValue] = useState<string>("");
  const [key, setKey] = useState<any>(defaultSearchKey);
  const [searchData, setSearchData] = useState<any>({
    keyData: "Offer ID#",
    searchValue: "",
  });

  const [error, setError] = useState({
    [SEARCH_VALUE_CONFIG.key]: "",
  });

  const storedSearchValue = sessionStorage.getItem("searchValue");
  const storedSearchKey = sessionStorage.getItem("searchKey");

  const checkErrors = () => {
    const isErrorAvailable = sessionStorage.getItem("errorVal");
    const isSearchHadError = isErrorAvailable
      ? JSON.parse(isErrorAvailable || "")
      : sessionStorage.setItem("errorVal", "false");
    return isSearchHadError;
  };
  useEffect(() => {
    if (!storedSearchKey) {
      sessionStorage.setItem("searchKey", "Offer ID#");
      sessionStorage.setItem("searchValue", "");
    }
    if (checkErrors()) {
      setError({});
    }
    const searchVal = sessionStorage.getItem("searchValue");

    setValue(searchVal || "");
    setKey(
      SEARCH_KEY_CONFIG.options.find(
        item =>
          item?.eventKey ===
          getSearchDopdown(sessionStorage.getItem("searchKey"))?.eventKey
      )
    );
  }, []);
  useEffect(() => {
    if (!checkErrors()) {
      setError({});
    }
  }, [sessionStorage.getItem("errorVal")]);

  useEffect(() => {
    setSearchData({ keyData: storedSearchKey, searchValue: storedSearchValue });
  }, [storedSearchValue, storedSearchKey]);

  useEffect(() => {
    if (isValidEventIds) {
      const duplicateEvents = duplicateEventsResp
        ?.filter(
          item => item.eventIdNumber !== 0 && item.eventIdNumber !== undefined
        )
        .map(item => item.eventIdNumber)
        .sort((a, b) => a - b)
        .join(", ");

      if (duplicateEvents?.length > 0) {
        setValue(duplicateEvents);
        const defaultToEvent = SEARCH_KEY_CONFIG?.options?.find(
          item => item?.eventKey === "planEventIdNbrs"
        );

        const searchCriteria = defaultToEvent
          ? defaultToEvent?.name
          : "Event ID#";
        setSearchData({
          keyData: searchCriteria,
          searchValue: duplicateEvents,
        });
        setKey(defaultToEvent);
        dispatch(setValidateDuplicateEventsResHandler([]));
      }
    }
  }, [isOpen]);

  useEffect(() => {
    function updateSearchDataFromSlice() {
      const { searchObj } = filterData || {};
      const sessionVal = sessionStorage.getItem("searchValue");
      const sessionKey = sessionStorage.getItem("searchKey");
      const saveSearchValue = searchObj?.value ? searchObj?.value : sessionVal;
      const searchKeys = searchObj?.key
        ? searchObj?.key
        : getSearchDopdown(sessionKey)?.eventKey;
      if (isEmpty(filterData?.searchObj)) return;

      if (
        saveSearchValue === searchData?.searchValue &&
        searchKeys === getSearchDopdown(searchData?.keyData)?.eventKey
      )
        return;
      const values = searchObj?.rawString ? searchObj?.rawString : sessionVal;
      setValue(values);
      setKey(
        SEARCH_KEY_CONFIG.options.find(item => item?.eventKey === searchKeys)
      );
    }
    updateSearchDataFromSlice();
  }, [filterData]);
  useEffect(() => {
    if (
      !sessionStorage.getItem("searchValue")
      // && (toString.call(filterData?.searchObj?.value) === "[object Array]"
      //   ? filterData?.searchObj?.value?.length
      //   : filterData?.searchObj?.value)
    ) {
      validateAndApply();
    }
  }, [sessionStorage.getItem("searchValue")]);

  function validateAndApply() {
    const sessionSearchKey = sessionStorage.getItem("searchKey") || "";
    const sessionSearchValue = sessionStorage.getItem("searchValue") ?? "";

    let val = "";
    try {
      val = sessionSearchValue
        ? processSearchText({
            searchFilterOption: getSearchDopdown(sessionSearchKey),
            searchText: sessionSearchValue,
          })
        : "";
    } catch (error) {
      setError(prev => ({
        ...prev,
        [SEARCH_VALUE_CONFIG.key]: "Please check your input!",
      }));
      sessionStorage.setItem("errorVal", "true");
      return;
    }
    dispatch(
      action({
        ...filterData,
        ...getDefaultPaginationByView(
          viewKey === DASHBOARD_VIEW_KEY_MAPPER.PLANNING_VIEW_KEY,
          filterData?.pagination?.pageSize
        ),
        searchObj: {
          key: getSearchDopdown(sessionSearchKey)?.eventKey || {},
          value: val,
          rawString: sessionSearchValue,
        },
      })
    );

    sessionStorage.setItem("errorVal", "false");
  }

  const onSearchChange = searchVal => {
    if (searchVal === "") {
      sessionStorage.setItem("searchValue", "");
    }
    setError({});
    setValue(searchVal);
    setSearchData(prev => ({ ...prev, searchValue: searchVal }));
  };

  const searchClickHandler = e => {
    const trimmedSearchVal = value?.trim();
    // trimmedSearchVal && resetGridData(viewType as string);
    sessionStorage.setItem("searchValue", trimmedSearchVal);
    sessionStorage.setItem("searchKey", key?.name);
    trimmedSearchVal && validateAndApply();
  };

  return (
    <div className="flex gap-3">
      <SelectDropDown
        id={`abs-dashboard-search-select-${key?.eventKey}`}
        value={getSearchDopdown(searchData?.keyData)}
        setValue={keyVal => {
          setKey(keyVal);
          // setValue("");
          setError({});
        }}
        items={SEARCH_KEY_CONFIG.options}
        itemKey={"name"}
        width={SEARCH_KEY_CONFIG.extraConfig.width}
        variant={
          SEARCH_KEY_CONFIG.extraConfig.variant as
            | "tertiary"
            | "secondary"
            | undefined
        }
      />
      <div className={`relative`}>
        <Search
          size={"md"}
          value={searchData?.searchValue || ""}
          placeholder={SEARCH_VALUE_CONFIG.extraConfig.placeHolder}
          onChange={val => {
            onSearchChange(val);
          }}
          onClick={e => {
            searchClickHandler(e);
          }}
          onKeyDown={el => {
            if (el.key === "Enter") {
              searchClickHandler(el);
            }
          }}
          noClear={true}
          error={!!error[SEARCH_VALUE_CONFIG.key]}
        />
        {error[SEARCH_VALUE_CONFIG.key] ? (
          <span className="absolute top-350 text-sm text-error">
            {error[SEARCH_VALUE_CONFIG.key]}
          </span>
        ) : null}
      </div>
    </div>
  );
}
