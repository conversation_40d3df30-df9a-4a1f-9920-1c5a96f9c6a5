import React, { useEffect } from "react";
import {
  Accordion,
  AccordionBody,
  AccordionHeader,
} from "@material-tailwind/react";
import { ChevronRight } from "lucide-react";

function AccordionAtom({ division, children, isExpand }) {
  const [openAccordion, setOpenAccordion] = React.useState(false);

  useEffect(() => {
    setOpenAccordion(isExpand);
  }, [isExpand]);

  return (
    <Accordion
      open={openAccordion}
      id={division?.divisionId}
      className="bg-white"
    >
      <AccordionHeader
        onClick={() => setOpenAccordion(!openAccordion)}
        className={`accordian-header-class py-3 text-sm font-normal cursor-pointer`}
      >
        <section className="flex gap-5 px-2">
          <ChevronRight
            className={`h-5 w-5 transition-transform ${
              openAccordion ? "rotate-90" : ""
            }`}
          />
          <div>
            {division?.divisionId} - {division?.divisionName}
          </div>
        </section>
      </AccordionHeader>
      <div
        id={`abs-accordion-container-body-wrapper-${division?.divisionId}`}
        className={`accordion-body-wrapper`}
      >
        {openAccordion && (
          <AccordionBody className="py-0">
            <div
              id={`abs-accordion-container-body-${division?.divisionId}`}
              className="pl-4 justify-start items-center flex-grow-0 flex-shrink-0 gap-2"
            ></div>
            {children}
          </AccordionBody>
        )}
      </div>
    </Accordion>
  );
}

export default AccordionAtom;
