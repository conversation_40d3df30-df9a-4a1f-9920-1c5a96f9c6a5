import { useEffect, useRef, useState } from "react";
import AutoSelectBox from "./AutoSelectBox";
import { useGetStoreGroupsQuery } from "../../../../../../../graphql/generated/schema";

const PlanStoreGroups = ({
  register,
  control,
  storeGroupsField,
  strGrpDefaultValue,
  selectedStrGrpOptions,
  setSelStrGrpOptions,
  planProductGroups,
  divId,
  storeGroupType,
  setValue,
  isEditEnable,
  notFoundText,
  onStoreGroupChange = () => null,
  defaultStoreGroupData = { current: {} },
}) => {
  const [searchValue, setSearchValue] = useState("");
  const [noDataFoundText, setNoDataFoundText] = useState<string>("");
  const variables = {
    filter: {
      divisionIds: [divId],
      groupInd: storeGroupType?.id,
      productGroups:
        planProductGroups?.map(productGroup => {
          return productGroup?.id || productGroup?.planProductGroupId;
        }) || [],
      storeGroupName: searchValue,
    },
  };
  const { data } = useGetStoreGroupsQuery({
    variables,
    skip: !divId || !storeGroupType?.id || !planProductGroups?.length,
  });

  const strGrpFectCnt = useRef(0);

  useEffect(() => {
    if (data?.getStoreGroups && storeGroupType?.id && divId) {
      const stroGrpOptions = data?.getStoreGroups?.length
        ? data?.getStoreGroups?.map((item: any) => {
            return {
              id: item?.id,
              name: `${item?.storeGroupName} (${
                item?.storeIds?.length || 0
              } Stores)`,
              item,
            };
          })
        : [];
      setNoDataFoundText(!stroGrpOptions?.length ? notFoundText : "");
      setSelStrGrpOptions(stroGrpOptions);
      strGrpFectCnt.current = strGrpFectCnt.current + 1;
      strGrpFectCnt.current === 1 &&
        setValue(
          storeGroupsField.registerField,
          strGrpDefaultValue?.length ? strGrpDefaultValue : stroGrpOptions || []
        );
      if (
        !isEditEnable &&
        !strGrpDefaultValue?.length &&
        strGrpFectCnt.current === 1 &&
        defaultStoreGroupData
      ) {
        defaultStoreGroupData.current = {
          ...(defaultStoreGroupData?.current || {}),
          storeGroups: stroGrpOptions,
        };
      }
    }
  }, [data]);

  const onChange = option => {
    if (isEditEnable) return;
    setValue(storeGroupsField.registerField, option);
    setSearchValue("");
    onStoreGroupChange && onStoreGroupChange();
  };

  return control ? (
    <AutoSelectBox
      register={register}
      control={control}
      fieldProps={storeGroupsField}
      key={"id"}
      options={selectedStrGrpOptions}
      onChange={onChange}
      displayLabel={storeGroupsField.displayLabel}
      disabled={!selectedStrGrpOptions?.length || isEditEnable}
      setSearchValue={setSearchValue}
      searchValue={searchValue}
      noDataFoundText={noDataFoundText}
    />
  ) : null;
};

export default PlanStoreGroups;
