import { useSelectorWrap } from "@me/data-rtk";
import { useDispatch } from "react-redux";
import {
  onModalToggle,
  onModalToggleOverlapping,
} from "../../create-event/service/slice/allowance-details-slice";
import AllowancesAmounts from "../allowance-table/allowances-amounts";
import {
  checkIsItemRemoved,
  isShowExcludeAllowCheckBox,
  toFixedIfNecessary,
} from "../allowances-table-service";
import _ from "lodash";
import "./allowance-table-methods-mapper.scss";
import AmountOnlyInHeader from "../allowance-table/amount-onlyIn-header";
import ExcludeAllowanceCheckbox from "../allowance-exclude/allowance-exclude.component";
import efConstants from "../../../shared/ef-constants/ef-constants";
import Tag from "@albertsons/uds/molecule/Tag";
import Tooltip from "@albertsons/uds/molecule/Tooltip";
import {
  highlightItemAmtMainEntry,
  isValidItemEndDate,
  UOM_KEY_VALUE_MAPPER,
  useGetQueryParams,
} from "@me/util-helpers";
import {
  customSortFn,
  formatTimestamp,
  isFeatureFlagEnabled,
  isVendorLoggedIn,
  SharedWhseIcon,
  sortUpcsByLength,
  warningIcon,
} from "@me-upp-js/utilities";
import { appConstants } from "@me/utils-root-props";

/**
 *
 * @param data
 * @param switchValue
 * @param allowGroupConfigObj
 * @returns
 *
 * This file is a single place to keep all the methods related to Label or Value required by columns configuration
 * The function names we will configure in allowance-column-constants file and it returns a mapper object
 * so we can dynamically call function in columns file
 *
 */
export const AllowanceTableMethodsMapper = (
  data,
  switchValue,
  allowGroupConfigObj
) => {
  const dispatch = useDispatch();
  const { data: tabSelection } = useSelectorWrap(
    "isUpdateAllowanceDistributorSelection_rn"
  );
  const isMultiVendorFeatureEnable = isFeatureFlagEnabled(
    appConstants.FEATURE_FLAGS.MULTI_VENDOR
  );

  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const {
    queryParamValue: { isSummary, offerAllowancesId },
  } = useGetQueryParams(["isSummary", "offerAllowancesId"]);

  const renderVendorDetails = index => {
    // need to used data as rowData will be empty here
    const vendorDetails = data?.vendorDetails[index];
    const response = `${vendorDetails?.vendorName || ""} ${
        vendorDetails?.vendorNbr
      } ${vendorDetails?.costAreaDesc || ""}`,
      isShowLeadBadge =
        vendorDetails?.leadDistributorMode === efConstants.BILL_LEAD_DIST &&
        vendorDetails?.leadDistributorInd,
      shouldShowDetails =
        (!isSummary || !vendorDetails.isAllSummary) &&
        isShowExclude(index, vendorDetails);

    return (
      <div
        className="flex justify-between px-2 items-center"
        id="abs-allowance-table-methods-mapper-section-div"
      >
        <div
          className={`flex items-center text-sm text-dark-text ${
            isShowLeadBadge && shouldShowDetails ? "w-3/4" : ""
          }`}
        >
          <span>{response}</span>
          {isShowLeadBadge ? (
            <Tag
              className="capitalize ml-2"
              backgroundColor="#BCDFFD"
              borderColor="#BCDFFD"
              textColor="#000000"
              label={"Lead"}
            />
          ) : null}
        </div>
        {shouldShowDetails ? excludeCheckBox(index) : null}
      </div>
    );
  };
  const excludeCheckBox = index => {
    return (
      <ExcludeAllowanceCheckbox
        key={index}
        vendorIndex={index}
        rowData={data}
      />
    );
  };
  const sortByUpcsLength = (key, index, a, b) => sortUpcsByLength(a, b);
  const isShowExclude = (index, vendorDetails) => {
    if (isSummary || vendorDetails?.isAllSummary) return false;

    const isExcludeEnable = allowGroupConfigObj?.showExclude,
      isShowExclude = isShowExcludeAllowCheckBox(data, index);
    return isExcludeEnable && isShowExclude;
  };
  const renderCenterExcludeGroupHeader = index => {
    const vendorDetails = data?.vendorDetails[index];
    console.log("vendorDetails", vendorDetails, vendorDetails?.distCenter);
    return (
      <div
        className="flex justify-between px-2 items-center"
        id="abs-allowance-table-methods-mapper-vendordeatils"
      >
        <span
          className="text-sm leading-4 text-dark-text flex gap-1 items-center"
          id="abs-allowance-table-methods-mapper-vendor-span"
        >
          {(vendorDetails?.isSharedWhse || vendorDetails?.sharedWhse) && (
            <SharedWhseIcon />
          )}
          {vendorDetails?.distCenter}
        </span>
        {!vendorDetails?.isAllSummary &&
          isShowExclude(index, vendorDetails) &&
          excludeCheckBox(index)}
      </div>
    );
  };

  const renderCaseVendorGroupHeader = index => {
    const vendorDetails = data?.vendorDetails[index];
    return (
      <span
        className="text-sm leading-4 text-dark-text"
        id="abs-allowance-table-methods-mapper-vendordeatils-span"
      >
        {vendorDetails?.distCenter}
      </span>
    );
  };

  const getItemDesc = (index, rowData) => {
    const isRemoved = checkIsItemRemoved(rowData);
    const isValidItemEndDateVal = isValidItemEndDate(rowData?.effectiveEndDate);
    const effectiveEndDate =
      isValidItemEndDateVal &&
      formatTimestamp({
        timestamp: rowData?.effectiveEndDate,
        pattern: "MM/DD/YY",
      });

    return (
      <div
        title={rowData.itemDescription}
        className="flex gap-4"
        id="abs-allowance-table-methods-mapper-isremoved"
      >
        {isRemoved || isValidItemEndDateVal ? (
          <Tooltip
            zIndex={10}
            anchor="right"
            label={
              isValidItemEndDateVal
                ? `End Date - ${effectiveEndDate}`
                : "Removed"
            }
            className="mb-1"
          >
            {warningIcon}
          </Tooltip>
        ) : null}
        <span
          className="text-sm w-[337px] truncate pl-2"
          id="abs-allowance-table-methods-mapper-rowdata"
        >
          {rowData.itemDescription}
        </span>
      </div>
    );
  };

  const RenderUpcs = (index, rowData) => {
    const vendorObj = data?.vendorDetails?.[index];
    const caseUpc = rowData?.caseUpc,
      caseUpcs = rowData?.itemUpcs?.filter(itemUpc => itemUpc == caseUpc);
    const caseUpcsLength =
      typeof rowData?.caseUpc == "object"
        ? rowData?.caseUpc?.length
        : caseUpcs?.length;
    const displayUpcDetails = (e, data) => {
      e?.preventDefault();
      dispatch(onModalToggle({ isOpen: true, modalData: data }));
    };

    return (
      <>
        {vendorObj?.isAllSummary ? (
          <span className="text-sm leading-4 text-dark-text px-3">
            {caseUpcsLength}
          </span>
        ) : (
          <a
            href="#"
            className="text-[#1b6ebb] px-3"
            title={rowData?.itemUpcs}
            onClick={e => displayUpcDetails(e, rowData)}
          >
            {caseUpcsLength}
          </a>
        )}
      </>
    );
  };
  const getUnitCost_Allowance = (index?, rowData?) => {
    return showWkAllowanceText(index, "unitCostAllow", rowData);
  };
  const getCaseCost_Allowance = (index?, rowData?) => {
    return showWkAllowanceText(index, "caseCostAllow", rowData);
  };
  const renderEmptyGreyedField = (
    <div
      className="px-3 h-full bg-[#e7e7e7]"
      id="abs-allowance-table-methods-mapper-dispvalue-span-zero-cost"
    ></div>
  );
  const showWkAllowanceText = (index, costAllowKey, rowData) => {
    const vendrObj = rowData?.vendorDetails?.[index];
    const isZeroCost =
      vendrObj?.itemLevelModCommand === efConstants.ZERO_COST_TEXT;
    const displayUpcDetails = e => {
      e?.preventDefault();
      dispatch(onModalToggleOverlapping({ isOpen: true, modalData: rowData }));
    };
    return (
      <>
        {(isMultiVendorFeatureEnable && isVendorLoggedIn() && isZeroCost) ||
        vendrObj?.isEmptyVendor
          ? renderEmptyGreyedField
          : !vendrObj?.isEmptyVendor && (
              <a
                className="text-[#1b6ebb] px-3 cursor-pointer"
                onClick={displayUpcDetails}
              >
                {`$${toFixedIfNecessary(
                  rowData?.vendorDetails?.[index]?.[costAllowKey]
                )}`}
              </a>
            )}
      </>
    );
  };

  const noRender = () => {
    //Use this if there is nothing to render
    return;
  };

  /**
   *
   * @param index
   * @param rowData
   * @returns  if isOnly Display fields on switch, the show only display value
   * Otherwise render Amount Component
   */
  const renderAmountFieldsDSD = (index?, rowData?) => {
    const switchCaseObj = getSwitchSelectedConfigObj(),
      {
        isOnlyDisplay = false,
        displayAmtKey,
        displayUOMKey,
      } = switchCaseObj || {
        isOnlyDisplay: false,
        displayAmtKey: "",
        displayUOMKey: "",
      },
      vendrObj = rowData?.vendorDetails?.[index];
    const { isShowUOMOption, headerOnlyAmt } = allowGroupConfigObj;
    const mappedUomType = UOM_KEY_VALUE_MAPPER[vendrObj?.allowUomType] || vendrObj?.allowUomType || "";
    return !vendrObj?.isEmptyVendor ? (
      vendrObj?.isAllSummary ? (
        <div className="text-sm leading-4 text-dark-text w-14 ml-3 items-center">
          {headerOnlyAmt
            ? vendrObj?.currentHeaderAmtValue || ""
            : `$${vendrObj?.allowanceAmount || ""} ${mappedUomType || ""}` || ""}
        </div>
      ) : isSummary ? (
        getHighlightChanges(vendrObj, isShowUOMOption)
      ) : !isSummary &&
        !vendrObj?.isAllSummary &&
        !isOnlyDisplay &&
        !headerOnlyAmt ? (
        <div
          className="amountsFieldWrap pb-1"
          id="abs-allowance-table-methods-mapper-amounts-field-wrap"
        >
          <AllowancesAmounts rowData={rowData} warehouseIndex={index} type="" />
        </div>
      ) : (
        <div
          className="px-4"
          id="abs-allowance-table-methods-mapper-vendor-keys"
        >
          {vendrObj?.[displayAmtKey] && `$${vendrObj[displayAmtKey]}`}{" "}
          {vendrObj?.[displayUOMKey] && vendrObj[displayUOMKey]}
        </div>
      )
    ) : (
      renderEmptyGreyedField
    );
  };

  const getHighlightChanges = (
    vendrObj,
    isShowUOMOption,
    headerFlatAmt = false
  ) => {
    const {
      currentVal,
      prevVal,
      showStrikeOff,
      color = null,
    } = highlightItemAmtMainEntry(
      eventDetailsData,
      vendrObj,
      offerAllowancesId,
      isShowUOMOption,
      headerFlatAmt
    );
    return (
      <div
        className={`flex ${headerFlatAmt ? "font-medium" : "pl-4"}`}
        id="abs-allowance-table-methods-mapper-header-flatamt"
      >
        <div
          className={`mr-2 ${showStrikeOff ? `text-[${color}]` : ""} `}
        >{`$${currentVal}`}</div>
        {prevVal && showStrikeOff ? (
          <div
            className={`line-through`}
            id="abs-allowance-table-methods-mapper-prevval"
          >{` $${prevVal}`}</div>
        ) : null}
      </div>
    );
  };

  const getSwitchSelectedConfigObj = () => {
    const {
      isSwitchingRequires = false,
      vendorGridCols: { switchColumnsCase } = { switchColumnsCase: null },
    } = allowGroupConfigObj || {};
    return isSwitchingRequires
      ? switchColumnsCase[switchValue?.selectedSwitch]
      : null;
  };

  const renderAmountFields = (index, rowData?) => {
    return renderAmountFieldsDSD(0, rowData);
  };
  const showPckWhse = (index, rowData) => {
    const switchCaseObj = getSwitchSelectedConfigObj(),
      { isOnlyDisplay = false } = switchCaseObj || {
        isOnlyDisplay: false,
      };
    const { packWhse, displayPack } = rowData;
    const vendorData = rowData?.vendorDetails?.[index] || {};
    const isAllSummary = vendorData?.isAllSummary || false;
    return (
      <div className="text-sm ml-3">
        {isAllSummary ? packWhse : isOnlyDisplay ? displayPack : packWhse}
      </div>
    );
  };
  const renderSize = (index, rowData) => {
    const style = tabSelection.isAllowancefilterType
      ? "flex items-center justify-left px-3"
      : "gap-2";
    const copiedRowData = _.cloneDeep(rowData),
      copiedVendDetails = _.cloneDeep(copiedRowData?.vendorDetails),
      copiedItemId = _.cloneDeep(copiedRowData?.itemId);
    return (
      <div className={style}>
        <span className="text-sm leading-4 text-dark-text w-14">
          {rowData.size}
        </span>
        {tabSelection.isAllowancefilterType === 1 && !isSummary && (
          <AllowancesAmounts
            rowData={{}}
            warehouseIndex={0}
            type="byItem"
            byItemVendDetails={copiedVendDetails}
            byItemId={copiedItemId}
            isHeader={true}
          />
        )}
      </div>
    );
  };

  const renderAllowanceAmounts = ({ index, headerOnlyAmt }) => {
    const vendorDetails = data?.vendorDetails[index];
    return headerOnlyAmt ? (
      <AmountOnlyInHeader
        propsData={{ vendorDetails, warehouseIndex: index }}
      />
    ) : (
      <AllowancesAmounts
        rowData={{}}
        warehouseIndex={index}
        type="byDistributor"
        isHeader={true}
      />
    );
  };
  const renderAmountColumnHeader = (index?, rowData?) => {
    // need to used data as rowData will be empty here
    const {
        isSwitchingRequires = false,
        headerOnlyAmt = null,
        vendorGridCols: { switchColumnsCase, amtColmHeaderLabel } = {
          switchColumnsCase: null,
          amtColmHeaderLabel: "",
        },
      } = allowGroupConfigObj || {},
      amountColHeaderLbl = !isSwitchingRequires
        ? amtColmHeaderLabel
        : switchColumnsCase[switchValue?.selectedSwitch]?.label,
      displayLbl = `${amountColHeaderLbl} Amount`;
    const vendorObj = data?.vendorDetails?.[index];
    return isSummary || vendorObj?.isAllSummary
      ? showSummaryHeader(displayLbl, headerOnlyAmt, vendorObj)
      : showEditPageHeader(displayLbl, index, headerOnlyAmt);
  };

  const showEditPageHeader = (displayLbl, index, headerOnlyAmt) => {
    return (
      <>
        <div
          className="py-1 pl-3 breakLines_sy text-sm leading-4 text-dark-text pt-4"
          id="abs-allowance-table-methods-mapper-dislpayllb-div"
        >
          {displayLbl}
        </div>
        {!tabSelection.isAllowancefilterType && (
          <div
            className="pt-4 pb-1"
            id="abs-allowance-table-methods-mapper-render-allowance-amounts"
          >
            {renderAllowanceAmounts({ index, headerOnlyAmt })}
          </div>
        )}
      </>
    );
  };

  const showSummaryHeader = (displayLbl, headerOnlyAmt, vendorObj) => {
    return (
      <div
        className={`flex ${
          isSummary || vendorObj?.isAllSummary ? "items-center" : "pt-2"
        } `}
        style={{ height: "100px", width: "8rem" }}
        id="abs-allowance-table-methods-mapper-header-only-amt"
      >
        <div
          className="pl-3 breakLines_sy text-sm leading-4 text-dark-text"
          id="abs-allowance-table-methods-mapper-display-lbl-header-only-amt"
        >
          {displayLbl}
          {headerOnlyAmt ? (
            <div
              className="pt-6"
              id="abs-allowance-table-methods-mapper-get-highlight-changes"
            >
              {vendorObj?.isAllSummary
                ? vendorObj?.currentHeaderAmtValue
                : getHighlightChanges(vendorObj, false, headerOnlyAmt)}
            </div>
          ) : null}
        </div>
      </div>
    );
  };
  const renderDescColumnHeader = (index?, rowData?) => {
    // need to used data as rowData will be empty here
    const bottom_padding = tabSelection.isAllowancefilterType ? 36 : 65;
    return (
      <>
        <div
          className={`py-1 pl-3 breakLines_sy text-sm leading-4 text-dark-text pt-4`}
        >
          Item Description
        </div>
        <div
          style={{ height: bottom_padding }}
          id="abs-allowance-table-methods-mapper-description"
        ></div>
      </>
    );
  };

  const sort_RootKeys = (key, index, a, b) => {
    return a[key].replace(/[^0-9]/g, "") - b[key].replace(/[^0-9]/g, "");
  };

  const sortByAlphabet = (key, index, a, b) => {
    const val1 = a[key].toLowerCase(),
      val2 = b[key].toLowerCase();
    if (val1 < val2)
      //sort string ascending
      return -1;
    if (val1 > val2) return 1;
    return 0; //default return value (no sorting)
  };

  const sortVendorItems = (key, index, a, b) => {
    return a.vendorDetails[index][key] - b.vendorDetails[index][key];
  };
  const getCommonLabelForColumn = label => {
    return (
      <div
        className="py-1 text-sm text-dark-text pl-3 flex flex-col h-full"
        id="abs-allowance-table-methods-mapper-item-label-div"
      >
        <span className="" id="abs-allowance-table-methods-mapper-span-label">
          {label}
        </span>
      </div>
    );
  };

  const sortSize = (key, index, a, b) => {
    // for size, fields coming like 6.78 OZ in string, so to sort based on number split and compare
    const prevSize = a[key]?.split(" ")?.[0] || "";
    const nextSize = b[key]?.split(" ")?.[0] || "";
    return prevSize - nextSize;
  };

  const baseSortFunc = (key, index, a, b) => customSortFn(a, b, key);
  const getCommonValueTextByKey = (index, valueKey, rowData) => {
    const dispValue = rowData?.vendorDetails?.[index]?.[valueKey];
    const isEmpty = rowData?.vendorDetails?.[index]?.isEmptyVendor;
    const vendrObj = rowData?.vendorDetails?.[index];
    const isZeroCost =
      vendrObj?.itemLevelModCommand === efConstants.ZERO_COST_TEXT;
    return (
      <>
        {(isMultiVendorFeatureEnable && isVendorLoggedIn() && isZeroCost) ||
        isEmpty ? (
          renderEmptyGreyedField
        ) : (
          <span
            className="px-3"
            id="abs-allowance-table-methods-mapper-dispvalue-span"
          >
            {!_.isNil(dispValue) && !isEmpty
              ? `$${toFixedIfNecessary(
                  rowData?.vendorDetails?.[index]?.[valueKey]
                )}`
              : ""}
          </span>
        )}
      </>
    );
  };
  const getListCostBasedOnVendor = (index, valueKey, rowData) => {
    const dispValue = rowData?.vendorDetails?.[index]?.[valueKey];
    const isEmpty = rowData?.vendorDetails?.[index]?.isEmptyVendor;
    const isZeroCost =
      rowData?.vendorDetails?.[index]?.itemLevelModCommand ===
      efConstants.ZERO_COST_TEXT;
    return (
      <>
        {(isMultiVendorFeatureEnable && isVendorLoggedIn() && isZeroCost) ||
        isEmpty ? (
          renderEmptyGreyedField
        ) : (
          <span
            className="px-3"
            id="abs-allowance-table-methods-mapper-dispvalue-span"
          >
            {!_.isNil(dispValue) && !isEmpty
              ? `$${toFixedIfNecessary(dispValue)}`
              : ""}
          </span>
        )}
      </>
    );
  };
  const allowColsValueMapper = {
    renderVendorDetails,
    getUnitCost_Allowance,
    renderAmountFieldsDSD,
    renderAmountColumnHeader,
    renderDescColumnHeader,
    renderSize,
    RenderUpcs,
    getCommonLabelForColumn,
    renderAmountFields,
    sortVendorItems,
    sort_RootKeys,
    sortSize,
    sortByAlphabet,
    getCommonValueTextByKey,
    renderCaseVendorGroupHeader,
    getCaseCost_Allowance,
    showPckWhse,
    getItemDesc,
    noRender,
    renderCenterExcludeGroupHeader,
    getListCostBasedOnVendor,
    baseSortFunc,
    sortByUpcsLength,
  };
  return { allowColsValueMapper };
};
