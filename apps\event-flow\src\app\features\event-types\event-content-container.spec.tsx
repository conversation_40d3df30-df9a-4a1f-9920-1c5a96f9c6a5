import { render } from "@testing-library/react";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import EventContentContainer from "./event-content-container";

jest.mock("react-pdf", () => ({
  Document: jest.fn(({ children }) => children),
  Page: jest.fn(() => <div data-testid="mock-page"></div>),
  pdfjs: {
    GlobalWorkerOptions: {
      workerSrc: "",
    },
    version: "2.10.377",
  },
}));

describe("event-content-container.spec", () => {
  it("component is rendering without error", () => {
    const Component = (
      <BrowserRouter>
        <EventContentContainer />
      </BrowserRouter>
    );
    render(Component);
  });
});
