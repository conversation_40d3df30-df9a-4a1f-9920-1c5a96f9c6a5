export const national_promo_allowance = [
  {
    title: "Scan",
    info: [
      {
        isTick: true,
        title: "Price / Ad / Display (88)",
        text: "Amount per unit Scanned, Performance required.",
      },
      {
        isTick: true,
        title: "Liquor Only (38)",
        text: "Amount per unit Scanned for Liquor, Performance required and only actual redemptions Billed.",
      },
      {
        isTick: true,
        title: "4U Event (52)",
        text: "Amount per unit Scanned, Performance required. Used for 4U offers and Digital offers that must be customer selected to be activated.",
      },
      {
        isTick: true,
        title: "Coupon/OMS (75)",
        text: "Amount per unit Scanned, Performance required. Used for coupon offers where Customer must clip or click.",
      },
      {
        isTick: true,
        title: "Discontinued (66)",
        text: "Amount per unit Scanned used to support Markdowns on products being Discontinued.",
      },
      {
        isTick: true,
        title: "Fuel Rewards (77)",
        text: "Amount per unit Scanned, used to support Fuel offers.",
      },
      {
        isTick: true,
        title: "Non-Performance (01)",
        text: "Amount per unit Scanned, no Performance required.",
      },
    ],
  },
  {
    title: "Case",
    info: [
      {
        isTick: true,
        title: "Billback Liquor (85)",
        text: "Per Case Allowances for Liquor Items that cannot be Deducted.",
      },
      {
        isTick: true,
        title: "Off Invoice (01)",
        text: "Per Case Allowances for Warehouse items where Performance is optional and will be given Off Invoice.",
      },
      {
        isTick: true,
        title: "DSD Off Invoice (01)",
        text: "Per Case Allowances for DSD items that will be given Off Invoice.",
      },
      {
        isTick: true,
        title: "Auto-Deduct (01)",
        text: "Per Case Allowances for Warehouse items with no Performance required that will be Deducted.",
      },
    ],
  },
  {
    title: "Ship to Store",
    info: [
      {
        isTick: true,
        title: "Price / Ad / Display (88)",
        text: "Amount per Store receiving, Performance required.",
      },
      {
        isTick: true,
        title: "Non-Performance (01)",
        text: "Amount per Store receiving, no Performance required.",
      },
    ],
  },
  {
    title: "Header Flat",
    info: [
      {
        isTick: true,
        title: "Coupon/Complex (20)",
        text: "Rarely used Lump Sum for complex coupons and promotions that aren't item specific. To be utilized only when instructed.",
      },
      {
        isTick: true,
        title: "BBS/4U Tags (32)",
        text: "Lump Sum, Performance required. Used to support Big Book of Savings or 4U Tags.",
      },
      {
        isTick: true,
        title: "4U Event (52)",
        text: "Rarely used Lump Sum for 4U offers and Digital offers that are not item specific. To be utilized only when instructed.",
      },
      {
        isTick: true,
        title: "Fuel Rewards (77)",
        text: "Lump Sum to support Fuel Offers.",
      },
      {
        isTick: true,
        title: "Price / Ad / Display (88)",
        text: "Lump Sum, Performance required. Used only when not item specific.",
      },
      {
        isTick: true,
        title: "Contract Allow (03)",
        text: "Lump sum utilized for Rebates, Incentives, or other specific Contracts.",
      },
      {
        isTick: true,
        title: "Logistics (08)",
        text: "Lump sum for Supply Chain incentivies when not provided at an individual item level.",
      },
      {
        isTick: true,
        title: "Swell Allowance (14)",
        text: "Lump sum utilized for offset Spoilage or unsaleable products.",
      },
      {
        isTick: true,
        title: "ABS Foundation (29)",
        text: "Lump Sum for monies that will be going to Charities through the Albertsons Foundation.",
      },
      {
        isTick: true,
        title: "Boise/Corp Contract (70)",
        text: "Lump Sum for specific Corporate Agreements and Deferred Income. To be utilized only when instructed.",
      },
      {
        isTick: true,
        title: "Special Major Events (92)",
        text: "Lump Sum, used to support large Events. To be utilized only when instructed.",
      },
      {
        isTick: true,
        title: "Other (99)",
        text: "Rarely used. Lump sum to used only if no other code is more applicable.",
      },
    ],
  },
  {
    title: "Item Flat",
    info: [
      {
        isTick: true,
        title: "Price / Ad / Display (88)",
        text: "A Lump Sum per item, performance required.",
      },
      {
        isTick: true,
        title: "Placement (04)",
        text: "A Lump Sum per item.  Most commonly used for New Item / Slotting / Placement.",
      },
      {
        isTick: true,
        title: "Warehouse Logistics (08)",
        text: "A Lump Sum per item used for Supply Chain incentives by item by Warehouse.",
      },
      {
        isTick: true,
        title: "Warehouse Price Protection (96)",
        text: "A Lump Sum per item used as an offset when Costs have decreased and inventory has devalued.",
      },
      {
        isTick: true,
        title: "Other (99)",
        text: "A Lump per item to be rarely used if no other Performance applies. Usually only used as an offset to protect future Promotions when Costs have increased or for item Conversion fees for Pack, UPC, Size, or other changes.",
      },
    ],
  },
];
