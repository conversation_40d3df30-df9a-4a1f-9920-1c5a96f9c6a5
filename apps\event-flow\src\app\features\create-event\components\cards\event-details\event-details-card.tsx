/* eslint-disable @typescript-eslint/no-empty-function */
import { useState, useEffect, FunctionComponent } from "react";
import Card from "@albertsons/uds/molecule/Card";
import {
  formatTimestamp,
  getLoggedInUserType,
  hasCreateIndChange,
  isMerchantLoggedIn,
  isVendorLoggedIn,
} from "@me-upp-js/utilities";
import { useSelectorWrap } from "@me/data-rtk";
import classNames from "classnames";
import {
  EEVENT_STATUS,
  EUSER_ROLES,
  isAllowanceFeatureEnabled,
  useGetAppBasePath,
} from "@me/util-helpers";
import * as React from "react";
import { useFormContext } from "react-hook-form";
import { useDispatch } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { RenderStates } from "@me/ui-render-states";
import {
  usePostEventDataMutation,
  usePutEventDataMutation,
} from "../../../service/apis/event-api";
import {
  getSubTitle,
  getTitle,
} from "../../../service/event-details/event-detail-service";
import {
  byPassOfferAllowanceHandler,
  editEventStatusHandler,
  eventDetailsDataHandler,
  setEventStatusChangedIndicators,
} from "../../../service/slice/event-detail-slice";
import { eventProgressDataHandler } from "../../../service/slice/event-progress-slice";
import EventDetailsCardContent from "./event-details-card-content";
import EventDetailsCardHeader from "./event-details-card-header";
import "./event-details-card.scss";
import EventDetailsPreview from "./event-details-preview";
import { useDeleteAllowanceTempWorkDataMutation } from "../../../service/apis/allowance-api";
import WarningModal from "../../../../event-types/components/event-header/warning-modal";
import {
  allowanceProductSources,
  offerCardConfiguration,
  promoCardConfiguration,
  resetIsOfferSectionUpdated,
  resetOfferAmountsData,
  resetOfferSectionsData,
  resetOfferSectionsEnableConfig,
} from "../../../service/slice/allowance-details-slice";
import { allowanceTempWorkReset } from "../../../service/slice/allowance-temp-work-slice";
import { useLazyGetForecastQuery } from "../../../service/apis/promotion-api";
import { promotionForecastHandler } from "../../../service/slice/promotion-details-slice";
import {
  getEventDetailsCardChangedFields,
  showEventDeatilsCard,
} from "./event-details-helper";
import {
  EVENT_TYPE,
  PROMO_POWER_ADMIN_VENDOR_ID,
} from "../../../constants/constants";
import { usePostAgreedMutation } from "../../../../event-types/components/event-action/event-action-service";
import {
  isValidItemPresent,
  isValidItemsPresentForNationals,
} from "../../../../event-types/event-progress-container-service";
import UserNotAuthorized from "./user-not-authorized";
import { eventTypes } from "../../../../event-types/constants/event-type-constant";
import { setNdVendorDivisions } from "./utility/utility";
import DEFAULT_EVENT_DETAILS from "../../../../create-event/constants/event-details/eventDetails";
import { getIsNationalEvent } from "../../../../event-types/event-types-helper";
import { validOfferList } from "../../../service/allowance/allowance-service";

interface IEventDetailsCardProps {
  cardConfiguration?: any;
  sectionIndex?: number;
}

interface UpdateEventPayload {
  periscopeObj: any;
  filteredDivisions: string[] | null;
}

const EventDetailsCard: FunctionComponent<IEventDetailsCardProps> = ({
  cardConfiguration,
  sectionIndex,
}) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { basePath } = useGetAppBasePath();
  const dispatch = useDispatch();
  const { getValues, setValue } = useFormContext();
  // const { swyDivision } = authProvider();
  const [DP, AO, NDP, NAO] = eventTypes.map(event => event?.eventType);
  const userFilters = localStorage.getItem("USER_FILTERS");
  const parsedUserFilters = JSON.parse(userFilters || "{}");

  const [isEditEvent, setIsEditEvent] = useState<boolean>(false);
  const [isCreateEvent, setIsCreateEvent] = useState<boolean>(
    !id ? true : false
  );
  const [isEventChanged, setIsEventChanged] = useState<boolean>(false);
  const { isOpenCard: isOpen, title } = cardConfiguration;
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { data: eventStatusChanged } = useSelectorWrap(
    "event_status_change_indicators"
  );
  const { data: { isEditPromotion: { isEdit = {} } = {} } = {} } =
    useSelectorWrap("promotion_edit_enable_configutation_rn") || {};
  const { editCardConfig = {}, openCardConfig: openEventCardConfig = {} } =
      useSelectorWrap("offer_card_configutation_rn").data || {},
    { openCardConfig: promoOpenCardConfig = {}, isAddNewPromo } =
      useSelectorWrap("promo_card_configutation_rn").data || {};
  const {
    data: { allowanceData: allowanceTempWorkData },
  } = useSelectorWrap("allowance_temp_work");
  const data = useSelectorWrap("promotion_details_data");
  const {
    data: { storeGroupDivisions },
  } = useSelectorWrap("store_group_divisions");
  const { data: eventTypeAndDivisionsData } = useSelectorWrap(
    "event_type_and_divisions"
  );
  const [
    deleteAllowanceTempWorkData,
    { isLoading: isDeleteAllowanceTempDataloading, isError: isDeleteError },
  ] = useDeleteAllowanceTempWorkDataMutation();
  const isPromoOpenData = Object.values(promoOpenCardConfig).includes(true),
    isAddNewPromoData = Object.values(isAddNewPromo).includes(true);
  const [isOpenCard, setIsOpenCard] = useState<boolean>(isOpen);
  const [isCollapsible, setIsCollapsible] = useState<boolean>(true);
  const [isEventUpdated, setIsEventUpdated] = useState<boolean>(false);
  const [showEventDeatils, setShowEventDetails] = useState<boolean>(true);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  localStorage.setItem("EVENT_ID", eventDetailsData?.id);
  localStorage.setItem("DIVISION_ID", eventDetailsData?.divisionIds);
  localStorage.setItem("PLAN_EVENT_ID_NBR", eventDetailsData?.planEventIdNbr);
  const { vendorNumbers, divisions } = setNdVendorDivisions();
  const eventType =
    eventTypeAndDivisionsData?.eventType || eventDetailsData?.eventType;

  const divisionIds = [NDP, NAO].includes(eventType) ? divisions : [];

  const setDefaultOrFormDivisions = () => {
    const { divisionIds } = parsedUserFilters;
    const formDivision = getValues("divisionIds");

    if ([NDP, NAO].includes(eventTypeAndDivisionsData?.eventType)) {
      setValue("divisionIds", divisions);
    } else {
      setValue(
        "divisionIds",
        formDivision?.length
          ? formDivision
          : divisionIds && divisionIds?.length
          ? [divisionIds?.[0]]
          : ["27"]
      );
    }
  };

  setDefaultOrFormDivisions();
  const [
    postEventDetailsData,
    {
      isLoading: isPostEventDataloading,
      data: postEventData,
      error: postEventDetailsdataError,
    },
  ] = usePostEventDataMutation();

  const [
    putEventDetailsdata,
    {
      isLoading: isPutEventDataloading,
      data: putEventData,
      error: putEventDetailsdataError,
    },
  ] = usePutEventDataMutation();

  const [postAgreed, { isLoading: isPostAgreedLoading }] =
    usePostAgreedMutation();

  const [getForecastDetails, { data: forecastData }] = useLazyGetForecastQuery(
    {
      URL_PARAM: id,
    },
    {
      refetchOnMountOrArgChange: true,
    }
  );

  useEffect(() => {
    dispatch(allowanceProductSources({ productSources: [] }));
  }, []);

  useEffect(() => {
    setDefaultOrFormDivisions();
  }, [parsedUserFilters]);

  useEffect(() => {
    if (postEventData || putEventData) {
      const data = postEventData || putEventData;
      dispatch(
        eventDetailsDataHandler({
          ...data,
          isChangeEventTypeVisible: false,
          dataFetchedFromPeriscope: false,
          periscopeValidFromApi: true,
        })
      );
      // const workFlowResponse = postEventData?.planEventWorkFlowType || putEventData?.planEventWorkFlowType
      // if (workFlowResponse !== PeriscopeMessage.NOT_FOUND) {
      navigate(`${basePath}/events/edit/${data.id}`);
      // }
    }
  }, [isEventChanged, postEventData, putEventData]);

  useEffect(() => {
    const promotionsList = getValues("promotionsLists[0].promotionsList");
    const { periscopeDetails } = eventDetailsData;
    if (periscopeDetails?.length) {
      const [{ periscopeId }] = periscopeDetails;
      promotionsList?.length &&
        getForecastDetails({
          URL_PARAM: periscopeId,
        });
    }
  }, [id, data?.data?.isPromotionAdded]);

  useEffect(() => {
    forecastData &&
      Object.keys(forecastData).length &&
      dispatch(promotionForecastHandler(forecastData));
  }, [forecastData]);

  const isInvalidAllowAndPromo =
    eventDetailsData?.inValidAllowances?.length === 0 &&
    eventDetailsData?.inValidPromotions?.length === 0;
  const isPromoOpen = () => {
    return (
      isInvalidAllowAndPromo &&
      (isEdit === true ||
        (isPromoOpenData && !eventDetailsData?.promotionsList?.length) ||
        isAddNewPromoData)
    );
  };
  const isOfferCardOpen = () => {
    return (
      isInvalidAllowAndPromo &&
      ((editCardConfig &&
        Object.values(editCardConfig).includes(true) &&
        (!eventDetailsData?.inValidAllowances?.length ||
          !eventDetailsData?.inValidPromotions?.length)) ||
        (openEventCardConfig &&
          Object.values(openEventCardConfig).includes(true) &&
          !eventDetailsData?.inValidAllowances?.length &&
          !eventDetailsData?.inValidPromotions?.length &&
          allowanceTempWorkData &&
          allowanceTempWorkData?.allowanceTypeSpecification) ||
        (openEventCardConfig &&
          Object.values(openEventCardConfig).includes(false) &&
          allowanceTempWorkData &&
          Object.keys(allowanceTempWorkData)?.length &&
          allowanceTempWorkData?.allowanceTypeSpecification))
    );
  };

  const editEventHandler = e => {
    e?.stopPropagation();
    setIsEditEvent(true);
    setIsCollapsible(false);
    setIsOpenCard(false);
    dispatch(
      eventDetailsDataHandler({
        isChangeEventTypeVisible: true,
        dataFetchedFromPeriscope: false,
        periscopeValidFromApi: true,
      })
    );
    dispatch(editEventStatusHandler({ isEventCardOpen: true }));
  };

  const cancelEditEventHandler = e => {
    e?.stopPropagation();
    setValue("isEventChanged", false);
    setIsEditEvent(false);
    setIsCollapsible(true);
    setIsOpenCard(true);
    dispatch(
      eventDetailsDataHandler({
        isChangeEventTypeVisible: false,
        dataFetchedFromPeriscope: false,
        periscopeValidFromApi: true,
      })
    );
    dispatch(editEventStatusHandler({ isEventCardOpen: false }));
    dispatch(
      byPassOfferAllowanceHandler({
        isOfferBypassed: false,
      })
    );
  };

  const cardHandler = value => {
    setIsOpenCard(value);
    dispatch(
      eventProgressDataHandler({
        selected: "Event Details",
      })
    );
  };

  useEffect(() => {
    setIsEventUpdated(false);
  }, [isEventUpdated]);

  const updateEventHandler = async (
    eventTypeName,
    updatePayload: UpdateEventPayload
  ) => {
    const userRole = getLoggedInUserType();
    setIsEditEvent(false);
    setIsCreateEvent(false);
    setIsOpenCard(true);
    setIsCollapsible(true);
    const { id } = getValues();
    const { isNDPAndFlagEnabled: isNational } = getIsNationalEvent(
      eventDetailsData?.eventType
    );
    if (id) {
      const validOffers = isNational
        ? isValidItemsPresentForNationals(eventDetailsData)
        : isValidItemPresent(
            eventDetailsData?.offerAllowances,
            "allowances.0.allowanceStatus",
            "overrideStatus"
          );
      const validPromos = isValidItemPresent(
        eventDetailsData?.promotionsLists?.[0]?.promotionsList,
        "promotionWorkflowStatus",
        "overridePromotionStatus"
      );
      const payloadForCreateOrEditEventObj = createPayloadForEvents(
        eventTypeName,
        updatePayload
      );
      const { pIdDetailsEventInd, otherDetailsChangedInd } =
        payloadForCreateOrEditEventObj;
      await putEventDetailsdata(payloadForCreateOrEditEventObj);
      const { eventStatus } = eventDetailsData || {};
      if (
        (!validOffers &&
          validPromos &&
          userRole === EUSER_ROLES.MERCHANT &&
          eventStatus === EEVENT_STATUS.AGREED) ||
        (pIdDetailsEventInd &&
          !otherDetailsChangedInd &&
          userRole === EUSER_ROLES.MERCHANT &&
          [
            EEVENT_STATUS.ACTIVE,
            EEVENT_STATUS.EXECUTED,
            EEVENT_STATUS.AGREED,
          ].includes(eventStatus))
      )
        await postAgreed({
          URL_PARAM: id,
        });
      if (
        eventDetailsData?.inValidAllowances?.length &&
        allowanceTempWorkData?.tempWorkAllowanceId
      ) {
        await deleteAllowanceTempWorkData({
          URL_PARAM: allowanceTempWorkData?.tempWorkAllowanceId,
        });
      }
    } else {
      const postPayLoadBody = createPayloadForEvents(
        eventTypeName,
        updatePayload
      );
      await postEventDetailsData(postPayLoadBody);
    }

    dispatch(allowanceTempWorkReset());
    dispatch(resetOfferAmountsData());
    dispatch(resetIsOfferSectionUpdated());
    dispatch(resetOfferSectionsEnableConfig());
    dispatch(resetOfferSectionsData());
    dispatch(
      offerCardConfiguration({
        editCardConfig: {},
        openCardConfig: {},
        offerData: eventDetailsData?.invalidAllowances?.length
          ? eventDetailsData?.offerAllowances?.[0]?.id
          : "",
      })
    );
    dispatch(
      promoCardConfiguration({
        editCardConfig: {},
        openCardConfig: {},
        isAddNewPromo: { 0: false },
      })
    );
    dispatch(
      byPassOfferAllowanceHandler({
        isOfferBypassed: false,
      })
    );
    dispatch(editEventStatusHandler({ isEventCardOpen: false }));
    setIsEventChanged(true);
  };

  const navigateToTaskView = () => {
    navigate(`${basePath}/`);
  };

  const createPayloadForEvents = (
    eventTypeName,
    payloadData: UpdateEventPayload
  ) => {
    const { periscopeObj, filteredDivisions } = payloadData;
    // anyway to make below destructure implementation independent?..
    const {
      id,
      name,
      divisionIds,
      planProductGroups,
      storeGroups,
      startDate,
      endDate,
      eventCreationVehicle,
      periscopeFormField,
    } = getValues();

    const planEventType = eventTypeName;
    const startDateConverted = formatTimestamp({
      timestamp: startDate,
      isApi: true,
    });

    // const periscopeDetails:PeriscopeDetailsProps[] = [{
    //   periscopeId:periscopeFormField,
    //   periscopeMessage: PeriscopeMessage.ALL_ITEMS_MATCH,
    //   workflowState: PeriscopeFlowState.DRAFT,
    // }]
    const endDateConverted = formatTimestamp({
      timestamp: endDate,
      isApi: true,
    });

    const productGroupIds = planProductGroups.map(productGroup =>
      toString.call(productGroup) === "[object Object]"
        ? productGroup.planProductGroupId || productGroup.id
        : productGroup
    );
    eventCreationVehicle.startDate = startDateConverted;

    eventCreationVehicle.endDate = endDateConverted;
    const planEventIdNbr =
      eventDetailsData &&
      eventDetailsData["planEventIdNbr"] &&
      eventDetailsData["planEventIdNbr"];
    const planEventName = name || eventDetailsData["name"];
    const { otherDetailsChangedInd, pIdDetailsEventInd } =
      getEventDetailsCardChangedFields(eventDetailsData, getValues);
    const finalDivisionIds =
      planEventType === NDP && filteredDivisions
        ? filteredDivisions
        : planEventType === NDP
        ? storeGroupDivisions
        : divisionIds;
    const filteredStores =
      planEventType === NDP
        ? storeGroups?.filter(store =>
            store?.divisionIds?.some(divisions =>
              finalDivisionIds?.includes(divisions)
            )
          )
        : storeGroups;
    const storeGroupIds = filteredStores?.map(storeGroup =>
      toString.call(storeGroup) === "[object Object]"
        ? storeGroup.storeGroupId || storeGroup.id
        : storeGroup
    );
    return {
      id,
      name: planEventName?.trim(),
      divisionIds: finalDivisionIds,
      // periscopeDetails,
      periscopeDetails: periscopeObj,
      productGroupIds,
      storeGroupIds,
      planEventType: planEventType,
      startDate: startDateConverted,
      endDate: endDateConverted,
      eventCreationVehicle,
      ...(planEventIdNbr && { planEventIdNbr }),
      otherDetailsChangedInd:
        otherDetailsChangedInd || eventDetailsData?.otherDetailsChangedInd,
      pIdDetailsEventInd:
        pIdDetailsEventInd || eventDetailsData?.pidDetailsEventInd,
    };
  };

  useEffect(() => {
    if (eventStatusChanged.isEventStatusChanged) {
      setIsEditEvent(false);
      setIsCollapsible(false);
      setIsOpenCard(true);
      dispatch(
        setEventStatusChangedIndicators({ isEventStatusChanged: false })
      );
    }
  }, [eventStatusChanged]);
  useEffect(() => {
    if (isCreateEvent) {
      localStorage.setItem("vendorNumList", JSON.stringify(vendorNumbers));
      dispatch(
        eventDetailsDataHandler({ ...DEFAULT_EVENT_DETAILS, divisionIds })
      );
    }
  }, [dispatch, isCreateEvent]);
  useEffect(() => {
    const vendNumsList = JSON.parse(localStorage.getItem("vendorNum") || "[]");
    const eventType = eventTypeAndDivisionsData?.eventType || "";
    const { isNationalEvent, nationalRolesAvailable } =
      getIsNationalEvent(eventType);

    if (
      isVendorLoggedIn() &&
      eventDetailsData?.divisionIds?.length &&
      !vendNumsList.includes(PROMO_POWER_ADMIN_VENDOR_ID) &&
      ![NDP].includes(eventType)
    ) {
      //One Vendor should not see other Vendor event details if user doen't have access
      const isShow = showEventDeatilsCard(eventDetailsData);
      setShowEventDetails(isShow);
    } else if ([NDP].includes(eventType)) {
      setShowEventDetails(isNationalEvent && nationalRolesAvailable);
    }
  }, [eventDetailsData]);

  const checkIsDisabled = () => {
    return (
      // (eventDetailsData?.eventType === ALLOWANCE_SCREEN_TYPES?.DP?.key &&
      //   eventDetailsData?.startDate &&
      //   !isPast(new Date(eventDetailsData?.startDate)) &&
      //   isPromoOpen()) ||
      isOfferCardOpen() ||
      hasCreateIndChange(eventDetailsData, isAllowanceFeatureEnabled)
    );
  };

  const renderHtml = !showEventDeatils ? (
    <UserNotAuthorized isOpen={!showEventDeatils} />
  ) : (
    <Card
      collapsible={isEditEvent || isCollapsible}
      title={getTitle(title)}
      subtitle={
        !isEditEvent && !isCreateEvent
          ? getSubTitle(eventDetailsData?.updateUser)
          : ""
      }
      isOpen={isEditEvent || isOpenCard}
      className={classNames({
        "min-w-[600px] abs-ef-card-container z-[8]": true,
      })}
      onChange={cardHandler}
    >
      <Card.Header>
        {!isCreateEvent &&
        (eventDetailsData?.eventType !== EVENT_TYPE.NCDP ||
          (isMerchantLoggedIn() &&
            eventDetailsData?.eventType === EVENT_TYPE.NCDP)) ? (
          <EventDetailsCardHeader
            isEditEvent={isEditEvent}
            editEventOnChange={editEventHandler}
            cancelEditEventHandler={cancelEditEventHandler}
            disabled={checkIsDisabled()}
          ></EventDetailsCardHeader>
        ) : (
          ""
        )}
      </Card.Header>
      <Card.Content>
        {isEditEvent ||
        isCreateEvent ||
        putEventDetailsdataError ||
        postEventDetailsdataError ? (
          <EventDetailsCardContent
            cardConfiguration={cardConfiguration}
            updateEventHandler={updateEventHandler}
            isEditEvent={isEditEvent}
            wasError={postEventDetailsdataError?.data?.code}
            isCreateEvent={isCreateEvent}
          />
        ) : (
          <EventDetailsPreview
            isApiLoading={isPostEventDataloading || isPutEventDataloading}
            eventPreviewData={postEventData || putEventData}
          />
        )}
      </Card.Content>
    </Card>
  );

  const isApiLoading = () => {
    return (
      isPostEventDataloading ||
      isPutEventDataloading ||
      isDeleteAllowanceTempDataloading
    );
  };

  const renderDetails = {
    isApiLoading: isApiLoading(),
    isPageLevelSpinner: true,
    isRenderMainHtml: true,
    renderHtml,
  };
  return <RenderStates details={renderDetails} />;
};

export default EventDetailsCard;
