import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { useForm, FormProvider } from 'react-hook-form';
import PlanStoreGroupType from './PlanStoreGroupType';
import { useGetStoreGroupTypesByDivisionIdQuery } from '../../../../../../../graphql/generated/schema';
import { getLoggedInUserType, isFeatureFlagEnabled } from '@me-upp-js/utilities';
import { EUSER_ROLES } from '@me/util-helpers';

// Mock the dependencies
jest.mock('../../../../../../../graphql/generated/schema', () => ({
  useGetStoreGroupTypesByDivisionIdQuery: jest.fn(),
}));

jest.mock('@me-upp-js/utilities', () => ({
  getLoggedInUserType: jest.fn(),
  isFeatureFlagEnabled: jest.fn(),
}));

jest.mock('@me/utils-root-props', () => ({
  appConstants: {
    FEATURE_FLAGS: {
      ROG_HIDDEN_PRICE: 'ROG_HIDDEN_PRICE'
    }
  }
}));

// Mock the InputSelectAtom component
jest.mock('../../../../fields/index', () => ({
  InputSelectAtom: jest.fn(({ onChange, options }) => (
    <select
      data-testid="store-group-select"
      onChange={(e) =>
        onChange(options.find((opt) => opt.id === e.target.value))
      }
    >
      {options.map((option) => (
        <option key={option.id} value={option.id}>
          {option.name}
        </option>
      ))}
    </select>
  )),
}));

describe('PlanStoreGroupType', () => {
  let mockSetValue: jest.Mock;
  let mockSetStoreGroupType: jest.Mock;
  let mockSetStoreGroupTypeOptions: jest.Mock;
  let mockOnStoreGroupTypeChange: jest.Mock;

  const defaultProps = {
    planStoreGroupType: {
      registerField: 'storeGroupType',
      displayLabel: 'Store Group Type',
    },
    register: jest.fn(),
    control: {},
    stroreGrpType: null,
    setStoreGroupType: jest.fn(),
    storeGroupTypeOptions: [],
    setStoreGroupTypeOptions: jest.fn(),
    setValue: jest.fn(),
    divId: '123',
    isEditEnable: false,
    onStoreGroupTypeChange: jest.fn(),
    defaultStoreGroupData: { current: {} },
  };

  const mockStoreGroupTypes = [
    { groupInd: 'PA', storeGrpTypeName: 'Promotional' },
    { groupInd: 'DA', storeGrpTypeName: 'Display' },
    { groupInd: 'SA', storeGrpTypeName: 'Standard' },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockSetValue = jest.fn();
    mockSetStoreGroupType = jest.fn();
    mockSetStoreGroupTypeOptions = jest.fn();
    mockOnStoreGroupTypeChange = jest.fn();

    // Default feature flag value
    (isFeatureFlagEnabled as jest.Mock).mockReturnValue(false);
  });

  const RenderWithFormProvider: React.FC<{ props?: any }> = ({ props = {} }) => {
    const methods = useForm();
    return (
      <FormProvider {...methods}>
        <PlanStoreGroupType
          {...defaultProps}
          {...props}
          control={methods.control}
          setValue={mockSetValue}
          setStoreGroupType={mockSetStoreGroupType}
          setStoreGroupTypeOptions={mockSetStoreGroupTypeOptions}
          onStoreGroupTypeChange={mockOnStoreGroupTypeChange}
        />
      </FormProvider>
    );
  };

  const renderWithFormProvider = (props = {}) =>
    render(<RenderWithFormProvider props={props} />);

  it('should render the component with merchant role and include all groups', async () => {
    (getLoggedInUserType as jest.Mock).mockReturnValue(EUSER_ROLES.MERCHANT);
    (useGetStoreGroupTypesByDivisionIdQuery as jest.Mock).mockReturnValue({
      data: {
        getStoreGroupTypesByDivisionId: mockStoreGroupTypes,
      },
    });

    renderWithFormProvider();

    await waitFor(() => {
      // For merchant, all groups (PA, DA, SA) are included.
      expect(mockSetStoreGroupTypeOptions).toHaveBeenCalledWith([
        { id: 'PA', name: 'Promotional', item: mockStoreGroupTypes[0] },
        { id: 'DA', name: 'Display', item: mockStoreGroupTypes[1] },
        { id: 'SA', name: 'Standard', item: mockStoreGroupTypes[2] },
      ]);
    });
  });

  it('should render the component with vendor role and filter out PA group', async () => {
    (getLoggedInUserType as jest.Mock).mockReturnValue(EUSER_ROLES.VENDOR);
    // For vendor, even if the feature flag is enabled, PA is filtered out (see component logic).
    (isFeatureFlagEnabled as jest.Mock).mockReturnValue(true);
    (useGetStoreGroupTypesByDivisionIdQuery as jest.Mock).mockReturnValue({
      data: {
        getStoreGroupTypesByDivisionId: mockStoreGroupTypes,
      },
    });

    renderWithFormProvider();

    await waitFor(() => {
      expect(mockSetStoreGroupTypeOptions).toHaveBeenCalledWith([
        { id: 'DA', name: 'Display', item: mockStoreGroupTypes[1] },
        { id: 'SA', name: 'Standard', item: mockStoreGroupTypes[2] },
      ]);
    });
  });

  it('should handle onChange event', async () => {
    (getLoggedInUserType as jest.Mock).mockReturnValue(EUSER_ROLES.VENDOR);
    (useGetStoreGroupTypesByDivisionIdQuery as jest.Mock).mockReturnValue({
      data: {
        getStoreGroupTypesByDivisionId: mockStoreGroupTypes,
      },
    });

    renderWithFormProvider();

    await waitFor(() => {
      // Find the select element and simulate a change.
      const select = screen.getByTestId('store-group-select');
      select.dispatchEvent(new Event('change', { bubbles: true }));

      expect(mockSetStoreGroupType).toHaveBeenCalled();
      expect(mockSetValue).toHaveBeenCalled();
      expect(mockOnStoreGroupTypeChange).toHaveBeenCalled();
    });
  });
});
