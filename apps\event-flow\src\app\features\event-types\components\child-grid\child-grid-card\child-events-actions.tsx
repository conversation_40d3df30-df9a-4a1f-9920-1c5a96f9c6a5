import { Button } from "@albertsons/uds/molecule/Button";
import { useSelectorWrap } from "@me/data-rtk";
import { EEVENT_STATUS, useGetAppBasePath } from "@me/util-helpers";
import React, { useMemo } from "react";
import ChildEventActionModal from "./child-event-action-modal";
import useChildEventActionService from "../hooks/useChildEventActionService";
import { useDispatch } from "react-redux";
import {
  doRefetchEventsPlanxAapi,
  eventDetailsDataHandler,
} from "../../../../create-event/service/slice/event-detail-slice";
import _ from "lodash";
import {
  doRefetchChildEventAapi,
  resetSelectedChildEventDivIds,
} from "../slices/child-events-data-slice";
import LoadingSpinner from "../../../../create-event/constants/LoadingSpinner/LoadingSpinner";
import { useNavigate, useParams } from "react-router-dom";
import { allowanceTempWorkReset } from "../../../../create-event/service/slice/allowance-temp-work-slice";
import {
  allowanceFormReset,
  resetIsOfferSectionUpdated,
  resetOfferAmountsData,
} from "../../../../create-event/service/slice/allowance-details-slice";
import { resetNationalDivisionsConfig } from "../../../../all-allowances/nationals/slices/national-main-entry-slices";
import DEFAULT_EVENT_DETAILS from "../../../../create-event/constants/event-details/eventDetails";
import { useDeleteNationalAllowanceTempWorkDataMutation } from "../../../../create-event/service/apis/allowance-api";

function ChildEventActions({ cardIndex, selectedTabConfig }) {
  const {
    key = "",
    userActions = {},
    gridDataSliceKey = "",
  } = selectedTabConfig || {};
  const { data: gridData = [] } = useSelectorWrap(gridDataSliceKey) || {};
  const { data: selectedChildDivIds = [] } = useSelectorWrap(
    "selected_child_events_data"
  );
  const {
    data: { allowanceData: allowanceTempWorkData },
  } = useSelectorWrap("allowance_temp_work");
  const { id: eventId } = useParams();
  const navigate = useNavigate();
  const { basePath } = useGetAppBasePath();
  const [isModalPopupOpen, setModalPopupOpen] = React.useState(false);
  const [userBtnKey, setUserBtnKey] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(false);
  const dispatch = useDispatch();
  const [ deleteNationalAllowanceTempWorkData ] = useDeleteNationalAllowanceTempWorkDataMutation();
  const actionMapper = useChildEventActionService();
  const statusWiseActions = userActions?.actionConfig || {};
  const currentEventData = gridData?.[cardIndex]?.[key] || [];
  const validChildEvents =
    currentEventData?.filter(
      event =>
        ![EEVENT_STATUS.CANCELED, EEVENT_STATUS.REJECTED]?.includes(
          event.eventStatus
        )
    ) || {};
  const childEventStatus = validChildEvents?.[0]?.eventStatus || "";
  const eventActionConfig = statusWiseActions?.[childEventStatus] || {};

  const isAllValidEventSelected = useMemo(() => {
    return validChildEvents?.every(e =>
      selectedChildDivIds?.includes(e?.divisionIds?.[0])
    );
  }, [validChildEvents, selectedChildDivIds]);

  const onClickAction = key => {
    setModalPopupOpen(!isModalPopupOpen);
    setUserBtnKey(key);
  };
  const refetchPlanEventData = () => {
    dispatch(
      doRefetchEventsPlanxAapi({
        isRefetch: _.uniqueId("refetch"),
      })
    );
    dispatch(
      doRefetchChildEventAapi({
        isRefetch: _.uniqueId("refetch"),
      })
    );
  };
  const onModalActionConfirm = async () => {
    const actionHandlerConfig = actionMapper?.[userBtnKey];
    if (!eventId) return;
    setIsLoading(true);
    const response = await actionHandlerConfig?.mutationHandler({
      URL_PARAM: eventId,
      customHeaders: {
        ...(!isAllValidEventSelected && {
          divisionIds: selectedChildDivIds?.join(","),
        }),
      },
    });
    if (!response?.error) {
      setIsLoading(false);
      const isAllDelete =
        userBtnKey === "deleteChildEvent" &&
        isAllValidEventSelected;
      if (isAllDelete) {
        dispatch(eventDetailsDataHandler({ ...DEFAULT_EVENT_DETAILS }));
        deleteTempWorkData();
        navigate(`${basePath}/`);
        return;
      }
      onActionResolved();
    }
    setIsLoading(false);
  };

  const onActionResolved = () => {
    refetchPlanEventData();
    setModalPopupOpen(false);
    dispatch(resetSelectedChildEventDivIds());
  };

  const deleteTempWorkData = async () => {
    const shouldDeleteTemp =
      Array.isArray(allowanceTempWorkData) &&
      allowanceTempWorkData?.[0]?.tempWorkAllowanceId;

    if (shouldDeleteTemp) {
      const isDeleted = await deleteNationalAllowanceTempWorkData({
        URL_PARAM: eventId,
      });

      if (isDeleted) {
        dispatch(allowanceTempWorkReset());
        dispatch(allowanceFormReset());
        dispatch(resetOfferAmountsData());
        dispatch(resetIsOfferSectionUpdated());
        dispatch(resetNationalDivisionsConfig());
      }
    }
  };

  const modalProps = useMemo(() => {
    return {
      ...eventActionConfig?.modalConfig,
      isModalPopupOpen,
      setModalPopupOpen,
      cancelBtnHandler: () => setModalPopupOpen(false),
      confirmBtnHandler: onModalActionConfirm,
      ...(isAllValidEventSelected && {
        title: eventActionConfig?.allDivModalTitle,
      }),
    };
  }, [eventActionConfig?.modalConfig, isModalPopupOpen]);

  return (
    <div className="flex gap-[7px] ">
      <LoadingSpinner
        isLoading={isLoading}
        classname="!h-full !w-full rounded-md"
      />
      {eventActionConfig?.actionBtns?.map((action, index) => {
        const { key = "", label = "", variant = "primary" } = action || {};
        return (
          <Button
            variant={variant}
            key={key}
            className="flex items-center gap-1 px-2 py-1 border rounded-md"
            disabled={selectedChildDivIds?.length === 0}
            onClick={() => onClickAction(key)}
          >
            <span>{label}</span>
          </Button>
        );
      })}
      <ChildEventActionModal modalProps={modalProps} />
    </div>
  );
}

export default React.memo(ChildEventActions);
