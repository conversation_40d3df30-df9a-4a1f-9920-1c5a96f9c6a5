import React from "react";
import { render, fireEvent, screen, act } from "@testing-library/react";
import SelectionDropdown from "../SelectionDropdown";
import { useDispatch } from "react-redux";
import { setSelectedStoreGroups } from "../../../create-event/service/slice/event-detail-slice";
import * as selectors from "@me/data-rtk";
import "@testing-library/jest-dom";

// const mockUseSelectorWrap = jest.fn();

jest.mock("react-redux", () => ({
  useDispatch: jest.fn(),
}));
jest.mock("../../../create-event/service/slice/event-detail-slice", () => ({
  setSelectedStoreGroups: jest.fn(payload => ({
    type: "selected_store_groups",
    payload,
  })),
}));
jest.mock("@me/data-rtk", () => ({
  useSelectorWrap: jest.fn(),
  injectEndPointsWrapper: jest.fn(),
  createGenericSlice: jest.fn(() => () => ({
    actions: {
      setOfferAmontsData: jest.fn(),
      setIsAdditionalDatesChnaged: jest.fn(),
      resetOfferAmountsData: jest.fn(),
    },
  })),
}));

jest.mock(
  "../DropdownButton",
  () =>
    ({ handleClick, disabled, configObj, selectedItemsCount }) =>
      (
        <button
          data-testid="dropdown-button"
          onClick={handleClick}
          disabled={disabled}
        >
          Dropdown ({selectedItemsCount})
        </button>
      )
);

jest.mock("../SelectionListContainer", () => () => (
  <div data-testid="selection-list-container">
    Mock Selection List Container
  </div>
));

jest.mock(
  "@albertsons/uds/molecule/Popper",
  () =>
    ({ children, open, onBlur }) => {
      return open ? (
        <div data-testid="popper" onBlur={onBlur}>
          {children}
        </div>
      ) : null;
    }
);

const mockDispatch = jest.fn();

describe("SelectionDropdown", () => {
  const configObj = {
    id: "storeGroup",
    uniqueId: "id",
    dataSliceName: "dropdownData",
    selectedDataSlice: "selectedDropdownData",
  };

  const dropDownData = [
    { id: "1", divisionIds: [1] },
    { id: "2", divisionIds: [2] },
  ];

  const selectedData = [{ id: "1", divisionIds: [1] }];
  const storeGroupDivisions = [1];

  beforeEach(() => {
    (useDispatch as jest.Mock).mockReturnValue(mockDispatch);

    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sliceName => {
      switch (sliceName) {
        case "dropdownData":
          return { data: dropDownData };
        case "selectedDropdownData":
          return { data: selectedData };
        case "store_group_divisions":
          return { data: { storeGroupDivisions } };
        default:
          return { data: [] };
      }
    });

    jest.clearAllMocks();
  });

  it("renders DropdownButton", () => {
    render(
      <SelectionDropdown
        configObj={configObj}
        disabled={false}
        onItemSelection={jest.fn()}
      />
    );
    expect(screen.getByTestId("dropdown-button")).toBeInTheDocument();
  });

  it("calls dispatch and onItemSelection with filtered store groups", () => {
    const onItemSelection = jest.fn();
    render(
      <SelectionDropdown
        configObj={configObj}
        disabled={false}
        onItemSelection={onItemSelection}
      />
    );

    expect(mockDispatch).toHaveBeenCalledWith(setSelectedStoreGroups(["1"]));
    expect(onItemSelection).toHaveBeenCalledWith([
      { id: "1", divisionIds: [1] },
    ]);
  });

  it("opens Popper when dropdown is clicked", () => {
    render(
      <SelectionDropdown
        configObj={configObj}
        disabled={false}
        onItemSelection={jest.fn()}
      />
    );

    const button = screen.getByTestId("dropdown-button");
    act(() => {
      fireEvent.click(button);
    });

    expect(screen.getByTestId("popper")).toBeInTheDocument();
    expect(screen.getByTestId("selection-list-container")).toBeInTheDocument();
  });

  it("does not open Popper if dropDownData is empty", () => {
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sliceName => {
      if (sliceName === "store_groups_data") return { data: [] };
      if (sliceName === "selected_store_groups") return { data: [] };
      if (sliceName === "store_group_divisions")
        return { data: { storeGroupDivisions: [] } };
      return { data: [] };
    });

    render(
      <SelectionDropdown
        configObj={configObj}
        disabled={false}
        onItemSelection={jest.fn()}
      />
    );

    const button = screen.getByTestId("dropdown-button");
    fireEvent.click(button);

    expect(screen.queryByTestId("popper")).not.toBeInTheDocument();
  });
});
