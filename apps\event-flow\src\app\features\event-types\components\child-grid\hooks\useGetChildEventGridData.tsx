import { useEffect, useState, useRef } from "react";
import { useDispatch } from "react-redux";
import { useSelectorWrap } from "@me/data-rtk";
import { useParams } from "react-router-dom";
import {
  useLazyGetChildEventDetailsQuery,
  usePostPlanItemsMutation,
} from "../../../../create-event/service/apis/event-api";
import { childEventConstants } from "../child-events-constants";
import { setItemsData } from "../../../../create-event/service/slice/event-detail-slice";
import { doRefetchChildEventAapi } from "../slices/child-events-data-slice";
import { EEVENT_STATUS } from "@me/util-helpers";

function useGetChildGridData() {
  const dispatch = useDispatch();
  const { id } = useParams<{ id: string }>();
  const { data: selectedTabConfig = {} } = useSelectorWrap("child_events_tab_config_data");
  const {
    data: { isRefetch },
  } = useSelectorWrap("refetchChild_EventSlice");
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");

  const { GRID_DATA_SETTER_MAPPER } = childEventConstants;
  const [gridData, setGridData] = useState<any>([]);
  const [isInitialLoading, setIsInitialLoading] = useState<boolean>(false);

  const { key } = selectedTabConfig;
  const isEventKey = key === "events";
  const isOfferKey = key === "offers";

  const [getHandler] = useLazyGetChildEventDetailsQuery();
  const [postPlanPromoProductGroupItems] = usePostPlanItemsMutation();

  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const retryCountRef = useRef(0);
  const MAX_RETRIES = 6;

  const {
    id: eventDetailsId = "",
    divisionIds: eventDivisionIds = [],
    endDate: eventEndDate = "",
    eventStatus
  } = eventDetailsData || {};

  useEffect(() => {
    getChildEventData();
  }, [isEventKey, eventDetailsData?.id, id, eventDetailsData?.eventStatus]);

  useEffect(() => {
    if (isRefetch) {
      getChildEventData();
      dispatch(
        doRefetchChildEventAapi({
          isRefetch: false,
        })
      );
    }
  }, [isRefetch]);

  useEffect(() => {
    if (isOfferKey) {
      setFormedDataInGrid([]);
      setChildOffersGridData();
    }
  }, [
    isOfferKey,
    eventDetailsData?.eventStatus,
    eventDetailsData?.offerAllowances,
    JSON.stringify(eventDetailsData?.childEvents),
  ]);

  const getChildEventData = () => {
    if (isEventKey && id && eventDetailsData?.id) {
      setFormedDataInGrid([]);
      setTimeout(() => {
        setDataForChildEvent();
      }, 2500); // Delay can be adjusted as needed
    }
  };

  const fetchPlanPromoProductGroups = async () => {
    try {
      const payload = {
        URL_PARAM: eventDetailsId,
        division: eventDivisionIds,
        promoStartDate: eventEndDate,
      };
      const ppgData = await postPlanPromoProductGroupItems(payload);
      if (ppgData) {
        dispatch(setItemsData(ppgData?.data?.planEventItemsData || []));
      }
    } catch (error) {
      console.error("Error fetching plan promo product groups:", error);
    }
  };

  const setChildOffersGridData = () => {
    const { offerAllowances = [], childEvents = [] } = eventDetailsData || {};
    const updatedOffers = offerAllowances?.map((offer) => {
      const updatedAllowances = offer?.allowances
        ?.filter((allow) => allow?.includeInd)
        ?.map((allowance) => {
          const allowanceDivId = allowance?.divisionIds?.[0];
          const childEvent = childEvents?.find((child) => child?.divId === allowanceDivId);
          return {
            ...allowance,
            childEventId: childEvent?.eventId || "",
          };
        });
      return {
        ...offer,
        allowances: updatedAllowances,
      };
    });

    setFormedDataInGrid(updatedOffers);
  };

  const pollUntilItemsExist = async () => {
    try {
      const response = await getHandler({ URL_PARAM: id });
      const { childEvents = [], parentEventIdNbr } = response?.data || {};

    const sortedChildEventsList = childEvents
      ?.map(childEvent => ({
        ...childEvent,
        parentEventIdNbr,
      }))
      ?.sort((a, b) => a?.divisionIds?.[0] - b?.divisionIds?.[0]);
    const formedDatGrid = [{ events: sortedChildEventsList }];

      const hasAnyItems = childEvents?.some(event => event?.items && event?.items?.length > 0);
      setFormedDataInGrid(formedDatGrid)
      if (hasAnyItems || retryCountRef.current >= MAX_RETRIES) {
        clearPolling();
        if (!hasAnyItems) {
          console.warn("Max retries reached. Items never appeared.");
        }
      } else {
        retryCountRef.current += 1;
      }
    } catch (error) {
      console.error("Polling error:", error);
      clearPolling();
    }
  };

  const clearPolling = () => {
    if (pollingRef.current) {
      clearInterval(pollingRef?.current);
      pollingRef.current = null;
    }
    retryCountRef.current = 0;
  };

  const setDataForChildEvent = async () => {
    setIsInitialLoading(true);

    try {
      const response = await getHandler({ URL_PARAM: id });
      const { childEvents = [], parentEventIdNbr } = response?.data || {};

      const sortedChildEventsList = childEvents
        ?.map((childEvent) => ({
          ...childEvent,
          parentEventIdNbr,
        }))
        ?.sort((a, b) => a?.divisionIds?.[0] - b?.divisionIds?.[0]);
      const formedDatGrid = [{ events: sortedChildEventsList }];
      setFormedDataInGrid(formedDatGrid);
      setIsInitialLoading(false);
      // ✅ Immediately fetch plan promo product groups (don't wait for items)
      childEvents?.length && fetchPlanPromoProductGroups();
      const hasAnyItems = sortedChildEventsList.some(event => event.items?.length);
      const isCancldRejected = [
        EEVENT_STATUS.REJECTED,
        EEVENT_STATUS.CANCELED,
      ].includes(eventStatus);
      if (!hasAnyItems && !pollingRef.current && !isCancldRejected) {
        pollingRef.current = setInterval(pollUntilItemsExist, 3000);
      }
    } catch (error) {
      console.error("Initial load error:", error);
      setIsInitialLoading(false);
    }
  };
  const setFormedDataInGrid = (formedDatGrid) => {
    setGridData(formedDatGrid);
    dispatch(GRID_DATA_SETTER_MAPPER?.[key]?.(formedDatGrid));
  };

  useEffect(() => {
    return () => {
      clearPolling();
    };
  }, []);

  return {
    data: gridData || [],
    isFetching: isEventKey ? isInitialLoading : false,
  };
}
export default useGetChildGridData;
