export type Task = {
  id: string;
  taskId: string;
  planEventIdNbr: string;
  eventName: string;
  eventType: string;
  parentEvent: ParentEvent;
  startDate: string;
  endDate: string;
  type: string;
  taskStatus: string;
  subType: string;
  priority: string;
  eventStatus: string;
  currentEventStatus: string;
  simsTaskVendor: SimsTaskVendor[];
  divisionIds: string[];
  createdOn: string;
  completedOn: string;
  completedBy: string;
  itemDetails?: ItemDetail[];
};

export type SimsTaskVendor = {
  vendorId: string;
  vendorName: string;
};

export type ItemDetail = {
  itemId: string;
  itemDescription: string;
  primaryUpc: string;
  upcs: string[];
  unitType: string;
  pack: number;
  size: string;
};

export type RenderOfferPromoSummaryType = {
  isOffer?: boolean;
  id: string;
  values: {
    style?: "red" | "green" | "deleted";
    type: string;
    amountWithUnit: string;
  }[];
};

export type ParentEvent = {
  eventId: string;
  planEventIdNbr: number;
};

export type TaskActionType = {
  label: string;
  checkIfVisible: (eventDetails: any, taskDetails?: any) => boolean;
  isVisible?: boolean;
  onClick?: (task: Task) => null;
  disabled?: boolean;
  icon?: any;
};
