import { app_store } from "@me/data-rtk";
import { render, screen } from "@testing-library/react";
import { FormProvider, useForm } from "react-hook-form";
import { Provider } from "react-redux";
import "@testing-library/jest-dom";

import AllowanceCardHeader from "./allowance-card-header-preview";
import { BrowserRouter } from "react-router-dom";
import * as selectors from "@me/data-rtk";
import { renderTestWithProviders } from "apps/event-flow/main-test-utils";

jest.mock("../../../service/allowance/allowance-service", () => ({
  getAllowanceAmountsSubHeaderValue: jest.fn(() => "$100"),

  getAllowanceTypeByPerformance: jest.fn(() => "HeaderFlat"),

  getCreateIndByLocation: jest.fn(() => "LC"),

  getIsopenpreview: jest.fn(() => true),
}));

jest.mock("../../../service/event-details/event-detail-service", () => ({
  formateVehicleDate: jest.fn(date => date),
}));

const Wrapper = props => {
  const formMethods = useForm<any>({
    defaultValues: {
      offerAllowances: [
        {
          allowances: [
            {
              allowanceIdNbr: 700626501,
              allowanceStartDate: "2023-10-20",
              allowanceEndDate: "2023-10-20",
              allowanceItems: [
                {
                  itemId: "2011525",
                  primaryUpc: "002780006643",
                  caseUpc: "1002780006643",
                  itemUpcs: ["002780006643", "1002780006643"],
                  effectiveEndDate: "0001-01-01",
                  vendorPackConversionFactor: 0,
                  packWhse: 12,
                  ringType: 0,
                  uom: "CA",
                  allowanceItemComps: [
                    {
                      performance: {
                        allowanceCd: "C",
                        perfCode1: "48",
                        perfCode2: "85",
                        payType: "B",
                        allwPerfId: "63a3a12743a6cee87995b81c",
                      },
                      allowanceTypeStartDate: "2023-10-10",
                      allowanceTypeEndDate: "2023-10-20",
                      allowAmount: 33,
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
  });

  return (
    <Provider store={app_store}>
      <BrowserRouter>
        <FormProvider {...formMethods}>{props.children}</FormProvider>
      </BrowserRouter>
    </Provider>
  );
};

describe("AllowanceCardHeader", () => {
  beforeEach(() => {
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      switch (sel_name) {
        case "offer_card_configutation_rn":
          return {
            data: { editCardConfig: { 1: false } },
          };

        case "allowance_temp_work":
          return {
            data: {},
          };

        case "event_details_data":
          return {
            data: {
              id: 1,
            },
          };

        case "allowance_edit_tempwork_rn": {
          return {
            data: {},
          };
        }

        case "allowance_type_performance_data":
          return {
            data: {},
          };

        case "allowance_form_data":
          return {
            data: {},
          };
        case "is_event_edit_enable":
          return {
            data: {},
          };
        default:
          break;
      }
    });
  });

  xit("should render the Allowance Card Header Preview", () => {
    renderTestWithProviders(
      <Wrapper>
        <AllowanceCardHeader
          isOpenCard={true}
          openCard={""}
          cardIndex={0}
          cardItemIndex={0}
          handleCancel={() => null}
        />
      </Wrapper>
    );

    expect(screen.queryByText("Start:")).toBeNull();

    expect(screen.queryByText("- $100")).toBeTruthy();
  });

  it("should render the Allowance Card Header Preview", () => {
    const { baseElement } = renderTestWithProviders(
      <Wrapper>
        <AllowanceCardHeader
          isOpenCard={true}
          openCard={""}
          cardIndex={0}
          cardItemIndex={0}
          handleCancel={() => null}
        />
      </Wrapper>
    );
    expect(baseElement).toBeTruthy();
  });
});
