/*
 * imports
 * move interfaces to seperate file
 * store functions - (selectors)
 * API calls declaration
 * constants
 * useState variables - try to combine and reduce number of declarations
 * useEffect
 * functions
 * Return UI field elements - create multiple functions to render fields
 */

import { useSelectorWrap } from "@me/data-rtk";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";
import { useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import {
  formatTimestampToDate,
  getAllowanceKey,
  getProductSourceKey,
} from "../../../../../service/allowance/allowance-service";
import { InputRadioButtonAtom } from "../../../../fields/index";
import LoadingSpinner from "../../../../../constants/LoadingSpinner/LoadingSpinner";
import Checkbox from "@albertsons/uds/molecule/Checkbox";
import { usePutEventDataMutation } from "../../../../../service/apis/event-api";
import {
  formatTimestamp,
  isFeatureFlagEnabled,
  isVendorLoggedIn,
} from "@me-upp-js/utilities";
import { usePostAllowanceToBeCreatedMutation } from "../../../../../service/apis/allowance-api";
import { ALLOWANCE_TO_BE_CREATED_CD_MAPPER } from "../../../../../constants/fields/allowance/allowance-steps-config";
import { appConstants } from "@me/utils-root-props";
import { doRefetchEventsPlanxAapi } from "../../../../../service/slice/event-detail-slice";
import _ from "lodash";
import { IAllowanceToBeCreatedInfoProps } from "../../../allowance/stepper/allowance-to-be-created/allowance-to-be-created.model";
import { OFFER_FORM_FIELDS } from "../../offer-flow-config";
import {
  setAllowanceFormInfo,
  setVendorsForAllowances,
} from "../../../../../service/slice/allowance-details-slice";
import useGetOfferSectionConfiguration from "../../hooks/useGetOfferSectionConfiguration";
import { resetOfferDivisions } from "../../offer-service";

const AllowanceToBeCreated = ({
  formControls,
  sectionConfiguration,
  isEditEnable,
  allowanceRegField,
  values,
  setIsAllowminSfoTriggered,
}) => {
  const dispatch = useDispatch();
  const sectionKey = sectionConfiguration?.key;
  const { allowanceToBeCreated, allowanceSpecificFields } =
    sectionConfiguration?.fields?.ALLOWANCE_TO_BE_CREATED || {};
  const {
    allowanceToBeCreatedOptionKey,
    createIndKey,
    AllowanceToBeCreatedChangeKey,
  } = OFFER_FORM_FIELDS;
  const {
    ALLOWANCE_TYPES,
    ALLOWANCE_SCREEN_TYPES,
    OFFER_ALLOWANCE_GROUP,
    PRODUCT_SOURCE_DATA,
  } = efConstants;
  const { DP, AO, NDP } = ALLOWANCE_SCREEN_TYPES;
  const { CASE } = ALLOWANCE_TYPES;

  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { productSources = [] } =
    useSelectorWrap("allowance_product_sources_rn")?.data || {};
  const { allowanceFormData } =
    useSelectorWrap("allowance_form_data")?.data || {};
  const [postEventDetailsData, { isLoading: postEventsLoading }] =
    usePostAllowanceToBeCreatedMutation();
  const [putEventDetailsdata, { isLoading: isPutEventDataloading }] =
    usePutEventDataMutation();
  const { clearNextSectionsOnOptionChange } = useGetOfferSectionConfiguration({
    allowanceRegField,
  });

  const isMultiVendorFeatureEnable = isFeatureFlagEnabled(
    appConstants.FEATURE_FLAGS.MULTI_VENDOR
  );
  const allowanceFormPrimeData =
    allowanceFormData?.[allowanceRegField]?.allowancePrimeData;

  const [showMultiVendCheck, setShowMultiVendCheck] = useState(false);
  const {
    register = () => null,
    control,
    setValue = () => null,
    getValues = () => null,
  } = formControls || {};

  const formData = getValues() || {};
  const allowanceType = formData?.allowanceType;
  const allowanceTypeKey = getAllowanceKey(allowanceType?.toUpperCase());
  const allowanceToBeCreatedOption = formData?.[allowanceToBeCreatedOptionKey];
  const storeGroups = formData?.storeGroups || [];

  const [allowanceToBeCreatedConfig, setAllowanceToBeCreatedInfo] =
    useState<IAllowanceToBeCreatedInfoProps>(allowanceToBeCreated);

  const [isSelectedMultiVendor, setIsSelectedMultiVendor] = useState(
    eventDetailsData?.submitForOthersInd
  );
  const [vendorsData, setVendorsData] = useState({
    myVendors: [],
    otherVendors: [],
  });

  const hasMounted = useRef(false);

  useEffect(() => {
    async function loadAllowminSfoData() {
      if (
        !hasMounted.current &&
        (CASE.key === allowanceTypeKey ||
          storeGroups?.length ||
          [DP.key, NDP.key].includes(eventDetailsData?.eventType))
      ) {
        hasMounted.current = true;
        isMultiVendorFeatureEnable &&
          isVendorLoggedIn() &&
          (await onMountOfAllowanceToBeCreated());
      }
    }
    loadAllowminSfoData();
  }, [storeGroups?.length]);

  const onMountOfAllowanceToBeCreated = async () => {
    const allowancesRespData = await postEventDetailsData({
      URL_PARAM: eventDetailsData?.id,
      queryParams: {
        responseFilter: "allowMinSfo",
      },
      division: eventDetailsData?.divisionIds,
      promoStartDate: formatTimestampToDate(
        eventDetailsData?.eventCreationVehicle?.startDate
      ),
      ...(AO?.key === eventDetailsData?.eventType && {
        overrideStoreGroups: storeGroups?.map(e => e?.id) || [],
      }),
    });
    const vendorsData = separateVendors(
      allowancesRespData?.data?.[0],
      eventDetailsData?.simsVendors
    );
    if (vendorsData?.otherVendors?.length > 0) {
      setShowMultiVendCheck(true);
    }
    setVendorsData({
      myVendors: vendorsData?.myVendors,
      otherVendors: vendorsData?.otherVendors,
    });
    dispatch(
      setVendorsForAllowances({
        vendors: vendorsData,
        isMultiVendor: isSelectedMultiVendor,
        isAllowMinSfoChanged: false,
        isMultiVendorEnabled:
          !isEditEnable &&
          isMultiVendorFeatureEnable &&
          isVendorLoggedIn() &&
          vendorsData?.otherVendors?.length > 0,
        isAllowminSfoLoaded: !!allowancesRespData?.data,
      })
    );
    setIsAllowminSfoTriggered(() => false);
  };

  useEffect(() => {
    allowanceTypeKey && productSources?.length && setInitialValues();
  }, [
    JSON.stringify(productSources),
    allowanceType,
    JSON.stringify(allowanceFormPrimeData),
  ]);

  const createPayloadForEvents = isChecked => {
    const {
      id,
      name,
      divisionIds,
      planProductGroups,
      storeGroups,
      startDate,
      endDate,
      eventCreationVehicle,
      eventType,
    } = eventDetailsData || {};

    const startDateConverted = formatTimestamp({
      timestamp: startDate,
      isApi: true,
    });

    const endDateConverted = formatTimestamp({
      timestamp: endDate,
      isApi: true,
    });

    const productGroupIds = planProductGroups.map(productGroup =>
      toString.call(productGroup) === "[object Object]"
        ? productGroup.planProductGroupId || productGroup.id
        : productGroup
    );

    const storeGroupIds = storeGroups.map(storeGroup =>
      toString.call(storeGroup) === "[object Object]"
        ? storeGroup.storeGroupId || storeGroup.id
        : storeGroup
    );
    const obj = { ...eventCreationVehicle };
    obj.startDate = startDateConverted;

    obj.endDate = endDateConverted;
    const planEventIdNbr =
      eventDetailsData &&
      eventDetailsData["planEventIdNbr"] &&
      eventDetailsData["planEventIdNbr"];
    const planEventName = name || eventDetailsData["name"];
    return {
      id,
      name: planEventName?.trim(),
      divisionIds,
      periscopeDetails: eventDetailsData?.periscopeDetails,
      productGroupIds,
      storeGroupIds,
      planEventType: eventType,
      startDate: startDateConverted,
      endDate: endDateConverted,
      eventCreationVehicle: obj,
      submitForOthersInd: isChecked,
      ...(planEventIdNbr && { planEventIdNbr }),
    };
  };

  const isDSD =
    ALLOWANCE_TO_BE_CREATED_CD_MAPPER?.[allowanceToBeCreatedOption?.createIndex]
      ?.offerKey === PRODUCT_SOURCE_DATA.DSD.key ||
    allowanceToBeCreatedOption?.selection === OFFER_ALLOWANCE_GROUP?.CASE?.DSD;

  const callUpdateOnSelection = async isChecked => {
    const res = await putEventDetailsdata(createPayloadForEvents(isChecked));
    if (res?.data) {
      dispatch(
        doRefetchEventsPlanxAapi({
          isRefetch: _.uniqueId("refetch"),
        })
      );
      await postEventDetailsData({
        URL_PARAM: eventDetailsData?.id,
        queryParams: {
          responseFilter: "allowMin",
        },
        division: eventDetailsData?.divisionIds,
        promoStartDate: formatTimestampToDate(
          eventDetailsData?.eventCreationVehicle?.startDate
        ),
      });
    }
  };

  const separateVendors = (data, simsVendors) => {
    const { dsdVendorSummary = [] } = data;
    const { myVendors, otherVendors } = dsdVendorSummary?.reduce(
      (acc, vendor) => {
        const { vendorNumber } = vendor || {};
        const targetArray = simsVendors?.includes(vendorNumber)
          ? "myVendors"
          : "otherVendors";
        acc[targetArray]?.push(vendor);
        acc[targetArray]?.sort((a, b) => a?.vendorNumber - b?.vendorNumber);
        return acc;
      },
      { myVendors: [], otherVendors: [] }
    ) || { myVendors: [], otherVendors: [] };

    return {
      myVendors,
      otherVendors,
    };
  };

  const setInitialValues = () => {
    const defaultOption =
      allowanceSpecificFields?.[allowanceTypeKey]?.[
        getProductSourceKey(productSources)
      ] || {};
    const allowToBeCreatedOption = allowanceFormPrimeData
      ? allowanceFormPrimeData?.allowanceToBeCreatedOption
      : defaultOption?.options?.find(option => option?.default) || {};

    setAllowanceToBeCreatedInfo({
      ...allowanceToBeCreatedConfig,
      ...defaultOption,
    });
    setValue(allowanceToBeCreatedOptionKey, allowToBeCreatedOption);
    setValue(allowanceToBeCreated.registerField, allowToBeCreatedOption?.key);
    setValue(createIndKey, allowToBeCreatedOption?.createIndex);
  };

  const onSectionUpdate = () => {
    if (!isEditEnable) {
      clearNextSectionsOnOptionChange(sectionKey);
      dispatch(
        setAllowanceFormInfo({
          allowanceFormData: {
            [allowanceRegField]: {
              allowancePrimeData: allowanceFormPrimeData,
            },
          },
        })
      );
    }
  };

  const onAllowanceToBeCreatedHandler = (selectedKey: string) => {
    setValue(AllowanceToBeCreatedChangeKey, true);
    eventDetailsData?.eventType === NDP.key &&
      allowanceToBeCreatedOption?.key &&
      resetOfferDivisions(
        eventDetailsData?.divisionIds,
        allowanceType,
        dispatch
      );
    onSectionUpdate();
    allowanceToBeCreatedConfig?.options?.find(option => {
      if (selectedKey === option?.key || (!selectedKey && option?.default)) {
        setValue(allowanceToBeCreatedOptionKey, option);
        setValue(allowanceToBeCreated.registerField, option?.key);
        setValue(createIndKey, option?.createIndex);
        return true;
      }
      return false;
    });
  };

  const allowanceToBeCreatedFieldContent = (
    <div
      className="flex flex-col mb-2"
      id="abs-allowance-to-be-created-field-content-wrapper"
      data-testid="allowance-to-be-created-content-input"
    >
      {control && (
        <InputRadioButtonAtom
          register={register}
          fieldProps={allowanceToBeCreatedConfig}
          control={control}
          value={allowanceToBeCreatedOption?.key}
          onChange={onAllowanceToBeCreatedHandler}
          disabled={isEditEnable}
        />
      )}
    </div>
  );

  const handleOnMultiSelected = e => {
    setIsSelectedMultiVendor(e.target.checked);
    callUpdateOnSelection(e?.target?.checked);
    dispatch(
      setVendorsForAllowances({
        ...vendorsData,
        isMultiVendor: e.target.checked,
        isAllowMinSfoChanged: true,
      })
    );
    !isEditEnable && clearNextSectionsOnOptionChange(sectionKey);
  };

  const formVendorName = (vendor: any) => {
    return `${vendor?.vendorName} - ${vendor?.dsdVendorNbr} ${
      vendor?.costAreaDescription
    } (${vendor?.facilities?.length} ${
      vendor?.facilities?.length > 1 ? "Stores" : "Store"
    }, ${vendor?.itemIds?.length} ${
      vendor?.itemIds?.length > 1 ? "Items" : "Item"
    })`;
  };

  const renderVendorDetails = (vendors: any[], title: string) => {
    return (
      <>
        {vendors?.length > 0 ? (
          <>
            <h2 className="text-[#5a697b] font-bold mt-2">{title}</h2>
            <ul
              className="p-[15px] w-full text-sm text-left text-[#5a697b] pl-1"
              style={{ listStyle: "inside" }}
            >
              {vendors?.map((vendor: any, index: number) => (
                <li className="p-[5px]" key={index}>
                  {formVendorName(vendor)}
                </li>
              ))}
            </ul>
            <div className="w-full h-full border-gray-204 transition-border duration-200 mb-[10px] border-t"></div>
          </>
        ) : null}
      </>
    );
  };

  const multiVendorContent = () => {
    return (
      <>
        <div className="text-sm text-left font-bold py-[12px]">
          <Checkbox
            checked={isSelectedMultiVendor}
            onChange={handleOnMultiSelected}
            disabled={eventDetailsData?.offerAllowances?.length > 0}
            value={isSelectedMultiVendor}
          >
            I will submit Allowances for all Stores & Items, including those
            delivered by other Vendors and I will be funding them.
          </Checkbox>
        </div>
        {isSelectedMultiVendor ? (
          <>
            {renderVendorDetails(vendorsData?.myVendors || [], "My Vendors")}
            {renderVendorDetails(
              vendorsData?.otherVendors || [],
              "Other Vendors"
            )}

            <p className="text-sm text-left text-[#5a697b]">
              <span className="text-sm text-left text-[#5a697b]">
                If you choose to include other vendors, you will be able to
                submit allowances, but you will not be able to see their costs
                or any pre-existing allowances.
              </span>
            </p>
          </>
        ) : null}
      </>
    );
  };

  const showMultiVendorContent = () => {
    return (
      isMultiVendorFeatureEnable &&
      isVendorLoggedIn() &&
      showMultiVendCheck &&
      isDSD
    );
  };

  const bodyContent = (
    <>
      {isEditEnable && (
        <p className="text-sm text-left text-[#9D2210] italic">
          Allowance to be created cannot be changed.
        </p>
      )}
      <p className="text-sm text-left text-[#5a697b] py-4 px-3 bg-[#F3F4F6]">
        <span className="text-sm text-left text-[#5a697b]">
          {allowanceToBeCreatedConfig?.text}
        </span>
      </p>
      {showMultiVendorContent() ? multiVendorContent() : null}
    </>
  );

  return (
    <div className="my-4">
      <LoadingSpinner
        classname="!h-full !w-full rounded-md"
        isLoading={isPutEventDataloading || postEventsLoading}
      />
      {control && allowanceToBeCreatedFieldContent}
      {bodyContent}
    </div>
  );
};

export default AllowanceToBeCreated;
