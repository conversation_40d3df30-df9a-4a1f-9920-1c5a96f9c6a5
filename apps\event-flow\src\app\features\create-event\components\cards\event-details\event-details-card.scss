.card-height .transition-height {
  height: auto !important;
}

.dropdown-width ul {
  width: inherit !important;
}

span.text-dark-text.leading-6.select-none.whitespace-nowrap.truncate {
  overflow: unset !important;
}

.disable-select {
  .component-scroll {
    [aria-label="combo-btnWrapper"] {
      background: #edeeef;
      border-color: #b9c0d4;
    }
  }
  button {
    background: #edeeef;
    border-color: #b9c0d4;

    span {
      color: #2b303c !important;
    }
  }
}
.scroll-event div {
  &::-webkit-scrollbar {
    width: 10px;
    height: 8px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.2);
  }
}
.disable-summary-ppgs {
  background: #edeeef;
}

.cic-text-area {
  textarea {
    min-height: 5rem;
  }
}

.abs-ef-ppg-select-container {
  max-width: 50%;
}

.abs-ef-store-group-type-container,
.abs-ef-store-groups-container {
  max-width: 25%;
}

.abs-common-popup-hover-close {
  top: 5px;
  position: absolute;
  right: 8px;
}

.items-table div table thead tr th:last-child span {
  padding: 0 !important;
}
