import { Column } from "@albertsons/uds/molecule/Table/Table.types";
import { useMemo } from "react";
import { formatTimestamp } from "@me-upp-js/utilities";
import { appConstants } from "@me/utils-root-props";
import { useSelectorWrap } from "@me/data-rtk";
import ChildEventCheckbox from "../child-event-checkbox";
import {
  EEVENT_STATUS,
  EUSER_ROLES,
  getLoggedInUserType,
} from "@me/util-helpers";
import PPGCell from "./ppg-column";
import StoreGroupCell from "./stores-column";
import ViewEventLink from "./View-event-link";
import { Tooltip } from "@albertsons/uds/molecule/Tooltip";


function EventDetailsColumnConfig({
  gridData,
  cardIndex,
  gridConfig,
}): Column<any>[] {
  const { data: itemsData } = useSelectorWrap("items_data");
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const {
    columns: {
      id,
      divisionId,
      ppg,
      pid,
      stores,
      startDate,
      endDate,
      eventId,
      status,
    },
  } = gridConfig;

  const isHideCols = eventData =>
    [EEVENT_STATUS.CANCELED, EEVENT_STATUS.REJECTED]?.includes(
      eventData?.eventStatus
    );
  const isDisplayCheckBoxBasedOnStatus = () => {
    const statusUserMapper = {
      VENDOR: "Pending With Vendor",
      MERCHANT: "Pending With Merchant",
    };
    const AFTER_AGREED_STATUS = [
      EEVENT_STATUS.ACTIVE,
      EEVENT_STATUS.AGREED,
      EEVENT_STATUS.EXECUTED,
    ];
    const nationalEventStatus = eventDetailsData?.eventStatus;
    const userRole = getLoggedInUserType();
    return (
      nationalEventStatus === EEVENT_STATUS.DRAFT ||
      statusUserMapper?.[userRole] === nationalEventStatus ||
      (AFTER_AGREED_STATUS.includes(nationalEventStatus) &&
        userRole === EUSER_ROLES.MERCHANT)
    );
  };
  const childEventsData = gridData?.[0]?.events || [];
  let VALID_CHILD_EVENTS = {};
  childEventsData?.forEach(childData => {
    const divId = childData?.divisionIds?.[0];
    VALID_CHILD_EVENTS = {
      ...VALID_CHILD_EVENTS,
      [divId]: eventDetailsData?.offerAllowances?.length
        ? !!eventDetailsData?.offerAllowances?.map(item =>
            item?.allowances?.find(
              allowance => allowance?.divisionIds?.[0] === divId
            )
          )?.some(e => e)
        : true,
    };
  });

  const CHILD_EVENTS_COLUMNS: Column<any>[] = [
    {
      id: divisionId?.id,
      label: "",
      width: "1vw",
      value: eventDetails => {
        const divisionId = eventDetails?.divisionIds?.[0]; // extract the actual ID
        const isNotValidStatus = isHideCols(eventDetails);
        return (
          <div
            className={`${
              isNotValidStatus ? "bg-[#f9d3d345] h-full flex items-center" : ""
            }`}
          >
            {isDisplayCheckBoxBasedOnStatus() ? (
              <ChildEventCheckbox
                divisionId={divisionId}
                eventStatus={eventDetails?.eventStatus}
              />
            ) : null}
          </div>
        );
      },
      sortable: false,
    },
    {
      id: divisionId?.id,
      label: divisionId?.label,
      width: "6vw",
      sortable: divisionId?.sortable,
      value: eventDetails => {
        const { ALL_DIVISION_DATA } = appConstants;
        const divisions = ALL_DIVISION_DATA?.find(
          division =>
            division?.divisionId === eventDetails?.divisionIds?.join("")
        );

        const { divisionId = "", divisionName } = divisions || {};
        const isNotValidStatus =
          isHideCols(eventDetails);
        return (
          <div
            id="abs-allowance-view-column-config-offerId"
            className={`text-sm px-3 flex gap-1 ${
              isNotValidStatus ? "bg-[#f9d3d345] h-full items-center" : ""
            }`}
          >
            {`${divisionId} - ${divisionName}` || "-"}
            <Tooltip zIndex={16} anchor="bottom" variant="light">
              <Tooltip.Popover>
                <span className="flex-col m-1 text-sm font-bold">
                  Offer was not created for this division. You can remove / reject / cancel the event.
                </span>
              </Tooltip.Popover>
              {!VALID_CHILD_EVENTS?.[divisionId] && (
                <svg
                  data-testid="warning-icon"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                  className="mt-0.5"
                >
                  <path
                    d="M0.940166 23.998C4.62581 23.9988 8.31186 23.9984 11.9975 23.9984C14.4273 23.9984 16.857 23.9988 19.2868 23.9988C20.5567 23.9988 21.8267 24.0024 23.0967 23.9971C23.8253 23.9944 24.2204 23.267 23.8713 22.589C23.7387 22.3312 23.6107 22.0712 23.4806 21.8121C22.3615 19.5847 21.2427 17.3569 20.1235 15.13C18.8999 12.6956 17.6755 10.2616 16.4519 7.82723C15.235 5.40563 14.0193 2.9836 12.8011 0.562888C12.7493 0.46048 12.6872 0.358514 12.6109 0.276852C12.193 -0.170741 11.5183 -0.0630362 11.2283 0.509035C10.3113 2.31839 9.40248 4.13216 8.49163 5.9446C7.21006 8.49509 5.92933 11.046 4.64817 13.5969C3.13631 16.6074 1.6232 19.6169 0.113817 22.6291C0.0566555 22.7426 0.0135777 22.875 0.00363663 23.0021C-0.0423407 23.5822 0.351987 23.998 0.940166 23.998Z"
                    fill="#9D2210"
                  ></path>
                  <path d="M13 16H11V9H13V16Z" fill="#FFF"></path>
                  <path
                    d="M13 19.995C12.625 19.995 12.2602 19.995 11.8955 19.995C11.6406 19.995 11.3857 19.9943 11.1308 19.9953C11.0586 19.9956 10.9997 19.9933 11 19.8943C11.002 19.267 11.001 18.6393 11.001 18H12.9997V19.9946L13 19.995Z"
                    fill="#FFF"
                  ></path>
                </svg>
              )}
            </Tooltip>
          </div>
        );
      },
    },
    {
      id: ppg?.id,
      label: ppg?.label,
      width: "12vw",
      sortable: ppg?.sortable,
      value: eventDetails => (
        <PPGCell eventDetails={eventDetails} itemsData={itemsData} />
      ),
    },
    {
      id: stores?.id,
      label: stores?.label,
      width: "10vw",
      sortable: stores?.sortable,
      value: eventDetails => <StoreGroupCell eventDetails={eventDetails} />,
    },
    {
      id: startDate?.id,
      label: startDate?.label,
      width: "5vw",
      sortable: startDate?.sortable,
      value: eventDetails => {
        const divisionId = eventDetails?.divisionIds?.[0];
        const isNotValidStatus =
          isHideCols(eventDetails);
        return (
          <div
            className={`text-sm px-3 ${
              isNotValidStatus ? "bg-[#f9d3d345] h-full items-center" : ""
            }`}
          >
            {isNotValidStatus ? (
              <div></div>
            ) : (
              <>
                {formatTimestamp({
                  timestamp: eventDetails?.eventStartDate,
                  pattern: "MM/DD/YY",
                })}
              </>
            )}
          </div>
        );
      },
    },
    {
      id: endDate?.id,
      label: endDate?.label,
      width: "5vw",
      sortable: endDate?.sortable,
      value: eventDetails => {
        const divisionId = eventDetails?.divisionIds?.[0];
        const isNotValidStatus =
          isHideCols(eventDetails);
        return (
          <div
            className={`text-sm px-3 ${
              isNotValidStatus ? "bg-[#f9d3d345] h-full items-center" : ""
            }`}
          >
            {isNotValidStatus ? (
              <div></div>
            ) : (
              <span>
                {formatTimestamp({
                  timestamp: eventDetails?.eventEndDate,
                  pattern: "MM/DD/YY",
                })}
              </span>
            )}
          </div>
        );
      },
    },
    // {
    //   id: pid?.id,
    //   label: pid?.label,
    //   width: "4vw",
    //   value: eventDetails => {
    //     const { periscopeDetails } = eventDetails;
    //     const divisionId = eventDetails?.divisionIds?.[0];
    //     const isNotValidStatus =
    //       isHideCols(eventDetails) || !VALID_CHILD_EVENTS?.[divisionId || ""];
    //     return (
    //       <div
    //         className={`text-sm px-3 ${
    //           isNotValidStatus ? "bg-[#f9d3d345] h-full items-center" : ""
    //         }`}
    //       >
    //         {isNotValidStatus ? (
    //           <div></div>
    //         ) : (
    //           <span>
    //             {periscopeDetails?.[0]?.periscopeId
    //               ? periscopeDetails?.[0]?.periscopeId
    //               : "-"}
    //           </span>
    //         )}
    //       </div>
    //     );
    //   },
    // },
    {
      id: eventId?.id,
      label: eventId?.label,
      width: "6vw",
      value: eventDetails => {
        const divisionId = eventDetails?.divisionIds?.[0];
        const isNotValidStatus =
          isHideCols(eventDetails);
        return (
          <div
            className={`text-sm px-3 ${
              isNotValidStatus ? "bg-[#f9d3d345] h-full items-center" : ""
            }`}
          >
            {isNotValidStatus ? (
              <div></div>
            ) : (
              <span>
                {/* {eventDetails?.eventIdNbr} */}
                {eventDetails?.parentEventIdNbr
                  ? `${eventDetails?.parentEventIdNbr}-${eventDetails?.divisionIds?.[0]}`
                  : "-"}
              </span>
            )}
          </div>
        );
      },
    },
    {
      id: status?.id,
      label: status?.label,
      width: "6vw",
      value: eventDetails => {
        const divisionId = eventDetails?.divisionIds?.[0];
        const isNotValidStatus =
          isHideCols(eventDetails);
        return (
          <div
            className={`text-sm px-3 ${
              isNotValidStatus ? "bg-[#f9d3d345] h-full items-center" : ""
            }`}
          >
            {isNotValidStatus ? (
              <div></div>
            ) : (
              <ViewEventLink
                eventId={eventDetails?.eventId}
                parentEventStatus={eventDetails?.eventStatus}
              />
            )}
          </div>
        );
      },
    },
  ];
  return useMemo(() => CHILD_EVENTS_COLUMNS, [cardIndex, gridConfig.id, itemsData]);
}

export default EventDetailsColumnConfig;
