.dates-accordion-wrapper {
  /* Remove margin from the first grid to match Figma layout */
  .grid.grid-cols-5.gap-2 {
    margin-top: 0;
    align-items: center;
  }
  font-family: "Nunito Sans";
  /* Fix chevron positioning to match Figma */
  svg.lucide.lucide-chevron-down {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
  }

  /* Allow only rotation transform for open state */
  .duration-200.-rotate-180 svg.lucide.lucide-chevron-down {
    transform: translateY(-50%) rotate(-180deg) !important;
  }

  /* Adjust first column in grid to accommodate chevron */
  .grid.grid-cols-5 > div:first-child {
    padding-left: 30px;
    position: relative;
  }

  /* Fix header content alignment */
  .AccordionItem-Header-Content {
    width: 100%;
    section {
      width: 100%;
    }
  }

  /* Reset transforms that affect positioning */
  [class*="translate-x-"],
  [class*="rotate-"] {
    transform: none;
  }

  /* Fix transition animations */
  .duration-200 {
    transition: none;
  }

  /* Ensure proper spacing between rows in accordion content */
  .vendor-row {
    margin-bottom: 8px;
  }

  /* Style the accordion header */
  .AccordionItem-Header {
    padding: 12px 16px;
    border-bottom: 1px solid #e2e8f0;
  }

  /* Fix dropdown arrow positioning */
  .flex.justify-between.items-center {
    position: relative;
  }

  .accordion-header-class {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  .accordion-body-wrapper .flex {
    align-items: center;
  }

  /* Ensure the date fields have consistent spacing */
  .flex-1 {
    display: flex;
  }

  .flex-1 > div {
    min-width: 120px;
  }

  /* Fix padding in accordion content */
  .accordion-body-wrapper .flex-col > div {
    padding: 0.75rem 1rem;
  }

  .flex-row,
  .accordion-header-class,
  .accordion-body {
    display: grid;
    grid-template-columns: 15% repeat(4, 1fr);
    width: 100%;
    align-items: center;
  }

  /* Date input containers should have consistent width */
  .min-w-\[120px\] {
    width: 100%;
    padding: 0 0.5rem;
  }

  /* Remove different background colors that might create visual inconsistency */
  .accordion-body-wrapper .flex-col > div {
    background-color: transparent !important;
  }

  /* Ensure content rows have the same padding as headers */
  .accordion-body-wrapper .accordion-body {
    padding: 0.75rem 1rem;
  }

  /* Hide duplicate chevrons */
  svg.lucide.lucide-chevron-down:nth-of-type(n + 2),
  .AccordionItem-Content svg.lucide.lucide-chevron-down {
    display: none;
  }
}
