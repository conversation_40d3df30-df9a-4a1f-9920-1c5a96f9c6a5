import { getUpdatedYearBasedOnCurrentDate } from "../../../../service/event-details/event-detail-service";

export const PROMO_DATE_FIELDS = {
  vehicleTypeOrCustomDate: {
    label: "Vehicle Type/Custom Date",
    required: false,
    registerField: "vehicleType",
    gqlQueryConst: "promotions",
    default: "Weekly Insert",
    type: "select",
    apiUrl: "",
    slice: "",
    options: [
      {
        id: 1,
        name: "Weekly Insert",
      },
      {
        id: 2,
        name: "Monthly Insert",
      },
      {
        id: 3,
        name: "Yearly Insert",
      },
    ],
    errors: {
      required: {
        backgroundColor: "",
        text: "Vehicle Type/Custom Date is Required",
      },
    },
    mapperKey: "promotions.vehicle.vehicleType.vehicleTypNm",
  },
  year: {
    label: "Year",
    required: false,
    registerField: "promotionsList.year",
    registerKeyName: "year",
    default: "2023",
    type: "select",
    apiUrl: "",
    slice: "",
    options: [
      {
        name: getUpdatedYearBasedOnCurrentDate?.(Date.now())[0], //adds 2 weeks to the current date and returns year
        id: 1,
      },
      {
        name: getUpdatedYearBasedOnCurrentDate?.(Date.now())[1],
        id: 2,
      },
      {
        name: getUpdatedYearBasedOnCurrentDate?.(Date.now())[2],
        id: 3,
      },
      {
        name: getUpdatedYearBasedOnCurrentDate?.(Date.now())[3],
        id: 4,
      },
    ],
    errors: {
      required: {
        backgroundColor: "",
        text: "Year is Required",
      },
    },
    mapperKey: "promotions.vehicle.year",
  },
  startWeekOrVehicle: {
    label: "Start Week/Vehicle",
    required: false,
    registerField: "vehicle",
    optionUrl: "startWeekVehicle",
    default: "Week 02 Insert 2023",
    type: "select",
    apiUrl: "",
    slice: "",
    options: [
      {
        id: "",
        name: "",
      },
    ],
    errors: {
      required: {
        backgroundColor: "",
        text: "Start Week/Vehicle is Required",
      },
    },
    mapperKey: "promotions.vehicle.vehicleNm",
  },
  vehicleStart: {
    label: "Promotion Start",
    required: false,
    disable: true,
    registerField: "vehicle.startDate",
    type: "date",
    error: {
      required: {
        backgroundColor: "",
        text: "Promotion Start Required",
      },
    },
    mapperKey: "promotions.vehicle.startDate",
  },
  vehicleEnd: {
    label: "Promotion End",
    required: false,
    disable: true,
    registerField: "vehicle.endDate",
    type: "date",
    error: {
      required: {
        backgroundColor: "",
        text: "Promotion End Required",
      },
    },
    mapperKey: "promotions.vehicle.endDate",
  },
  promotionCreationVehicle: {
    registerKeyName: "promotionCreationVehicle",
  },
};
