import { useEffect, memo, useState, useCallback } from "react";
import Select from "@albertsons/uds/molecule/Select";
import { Controller, useFormContext } from "react-hook-form";
import { FormFieldError } from "@me/util-form-wrapper";
import "../select-dropdown.scss";
import {
  useGetAllowancePerformancesByEventTypeQuery,
  useGetDivisionsQuery,
  useGetStoreGroupTypesByDivisionIdQuery,
  useGetVehicleByStartAndEndDateQuery,
  useGetVehicleByTypeAndYearQuery,
} from "../../../../graphql/generated/schema";
import {
  formateVehicleDate,
  getYearFromFormDate,
} from "../../service/event-details/event-detail-service";
import { getAllowanceTypeByPerformance } from "../../service/allowance/allowance-service";
import { useSelectorWrap } from "@me/data-rtk";
import { useDispatch } from "react-redux";
import { allowanceTypeAndPerformanceDataHandler } from "../../service/slice/allowance-details-slice";
import InfoGraphics from "../../../event-types/components/info-graphics/info-graphics";
import { INFO_GRAPHICS } from "@me-upp-js/utilities";
import { getLoggedInUserType } from "@me-upp-js/utilities";
import {
  DateFormat,
  dateFormat,
  MerchantRoles,
  VendorEditRoles,
} from "@me/util-helpers";
import { compareAsc, isPast, lightFormat, parseISO } from "date-fns";
import { useSearchParams } from "react-router-dom";
import {
  DP_AO_EVENT_TYPES,
  NATIONAL_EVENT_DIVISION_ID,
} from "../../constants/event-types/event-type-index";
import { setNdVendorDivisions } from "../cards/event-details/utility/utility";
import { appConstants } from "@me/utils-root-props";

// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface IInputSelectProps {
  fieldProps?: any;
  onChange?: any;
  control?: any;
  allowanceIndex?: any;
  offerIndex?: any;
  dynamicRegisterField?: any;
  disabled?: boolean;
  props?: any;
  changed?: any;
  isChanged?: boolean;
  enableStartWeekVehicle?: boolean;
  enableStoreGroupType?: boolean;
  defaultValue?: any;
  onClick?: any;
  hideVehicles?: boolean;
  isNCDP?: boolean;
}

const InputSelect = ({
  onChange,
  fieldProps,
  control,
  allowanceIndex,
  offerIndex,
  dynamicRegisterField,
  disabled,
  props,
  changed,
  isChanged,
  enableStartWeekVehicle,
  enableStoreGroupType,
  defaultValue: defaultValueFromParent,
  onClick,
  hideVehicles,
  isNCDP = false,
}: IInputSelectProps) => {
  const dispatch = useDispatch();
  const {
    getValues,
    setValue,
    register,
    control: formControl,
    formState: { errors },
    setError,
    trigger,
    watch,
    getFieldState,
  } = useFormContext();

  const { data: eventDetailsData } = useSelectorWrap("event_details_data");

  const { isEventCardOpen } = useSelectorWrap("is_event_edit_enable").data;
  const [searchParams] = useSearchParams();
  const eventTypeName = searchParams.get("eventType");
  const eventType = eventTypeName || eventDetailsData?.eventType;
  const { vendorNumbers, divisions } = setNdVendorDivisions();

  const { DP, AO } = DP_AO_EVENT_TYPES;
  const { NDP_EVENT_TYPE } = appConstants;
  const setStartWeekValueForCustomDate = filteredData => {
    const startDate = getValues(`startDate`);
    const year = getYearFromFormDate(new Date(startDate || Date.now()));
    setValue("eventCreationVehicle", filteredData);
    setValue("eventCreationVehicle.year", year);
    setValue("name", getValues("name") || eventDetailsData?.name);
    setValue("startWeekVehicle", filteredData?.vehicleNm);
  };
  const userRole = getLoggedInUserType();

  const getProps = () => {
    const isCustomDate = ["Custom Date", "CustomDate"].includes(
      fieldProps?.tabType
    );
    const didEventStartInPast = isPast(new Date(getValues("startDate")));
    const currentDate = lightFormat(new Date(), "yyyy-MM-dd");
    const isPromotion = fieldProps?.registerField === "vehicle";
    return isCustomDate
      ? {
          year: null,
          vehicleTypId: props
            ? props
            : getValues(`${dynamicRegisterField}.vehicleType.vehicleTypeId`),
        }
      : {
          vehicleTypId: props
            ? props
            : getValues(`${dynamicRegisterField}.vehicleType.vehicleTypeId`),
          startDate:
            isPromotion && didEventStartInPast
              ? currentDate
              : getValues("startDate"),
          endDate: getValues("endDate"),
        };
  };

  const shouldSkipVehicleQuery = (): boolean => {
    const val = getValues("dataFetchedFromPeriscope")
      ? !(
          eventDetailsData?.["eventCreationVehicle"]?.["vehicleType"]?.[
            "vehicleTypeId"
          ] &&
          Object.keys(
            eventDetailsData?.["eventCreationVehicle"]?.["vehicleType"]
          )?.length
        )
      : !getValues("vehicleType");
    return val;
  };

  const buildQueryVariablesforAPI = () => {
    if (["NDP", "NAO"].includes(eventType)) {
      setValue(
        "divisionIds",
        divisions ? divisions : eventDetailsData?.divisionIds
      );
    }
    const vehicleTypeVal = eventDetailsData?.vehicleType || fieldProps?.tabType;
    const yearVal = getValues("dataFetchedFromPeriscope")
      ? ["Custom Date", "CustomDate"].includes(vehicleTypeVal)
        ? null
        : getYearFromFormDate(
            parseISO(eventDetailsData?.startDate || new Date().toISOString())
          )
      : ["Custom Date", "CustomDate"].includes(fieldProps?.tabType)
      ? null
      : getValues("eventCreationVehicle.year")
      ? parseInt(getValues("eventCreationVehicle.year"))
      : getYearFromFormDate(
          parseISO(
            getValues("eventCreationVehicle.startDate") ||
              new Date().toISOString()
          )
        );

    switch (fieldProps.registerField) {
      case "eventCreationVehicle":
        return {
          skip: shouldSkipVehicleQuery(),
          variables: {
            vehicleTypId: props
              ? props
              : getValues("eventCreationVehicle.vehicleType.vehicleTypeId") ||
                getValues("eventCreationVehicle.vehicleType.vehicleType.id") ||
                eventDetailsData?.["eventCreationVehicle"]?.["vehicleType"]?.[
                  "vehicleTypeId"
                ],
            year: yearVal,
          },
        };
      case "allowance.startWeekOrVehicle":
      case "vehicle":
        return {
          variables: getProps(),
        };
      case "performance.allowanceCd":
      case "performance":
        return {
          variables: {
            eventType: eventDetailsData?.eventType,
          },
          skip: !eventDetailsData?.id,
        };
      case "planStoreGroupType":
        return {
          variables: {
            divId: eventType === "NDP" ? divisions : getValues("divisionIds"),
          },
          skip: !getValues("divisionIds")?.length,
        };
      default:
        return {};
    }
  };
  const getQuery = () => {
    if (fieldProps.optionUrl === "division") {
      return useGetDivisionsQuery;
    } else if (
      fieldProps.registerField === "allowance.startWeekOrVehicle" ||
      fieldProps.registerField === "vehicle"
    ) {
      if (
        fieldProps?.tabType === "Custom Date" ||
        fieldProps?.tabType === "CustomDate"
      ) {
        return useGetVehicleByTypeAndYearQuery;
      }
      return useGetVehicleByStartAndEndDateQuery;
      // return fieldProps?.tabType === "Custom Date" ||
      //   fieldProps?.tabType === "CustomDate"
      // ? useGetVehicleByTypeAndYearQuery
      // : useGetVehicleByStartAndEndDateQuery;
    } else if (fieldProps.optionUrl === "startWeekVehicle") {
      return useGetVehicleByTypeAndYearQuery;
    } else if (
      ["allowanceType", "performance"].includes(fieldProps.optionUrl)
    ) {
      return useGetAllowancePerformancesByEventTypeQuery;
    } else if (fieldProps.optionUrl === "planStoreGroupType") {
      return useGetStoreGroupTypesByDivisionIdQuery;
    }
    return params => {
      const data =
        fieldProps?.options?.map(ele => {
          return ele;
        }) || [];
      return {
        data,
      };
    };
  };
  const { registerField } = fieldProps;
  const getQueryParams = buildQueryVariablesforAPI();
  const applyQuery = getQuery();
  const { data } = applyQuery(getQueryParams);
  const [items, setItems] = useState(data);
  const onChangeHandler = element => {
    onChange(element);
  };

  useEffect(() => {
    getQuery();
  }, [props]);

  useEffect(() => {
    if (fieldProps.optionUrl === "division") {
      //For local development comment above line and uncomment below  divisionIds assignment with all div IDs
      // divisionIds = allDivisionIds;
      let vendorDivisionIds: any = [];
      const userRoles = localStorage.getItem("USER_ROLE_PERMISSIONS") || "";

      const parsedUserRoles = JSON.parse(userRoles || "[]");
      parsedUserRoles?.forEach(item => {
        const parsedDivisionIds = item?.userDivisions?.map(
          div => div.divisionId
        );
        if (
          [...VendorEditRoles, ...MerchantRoles].some(role =>
            item?.userRoles.includes(role)
          )
        ) {
          vendorDivisionIds = [...vendorDivisionIds, ...parsedDivisionIds];
        }
      });
      let divisionData = data;
      if ([DP, AO].includes(eventType)) {
        const filteredDivisionData = data?.getDivisions?.filter(
          divApiData => divApiData?.divisionId !== NATIONAL_EVENT_DIVISION_ID
        );
        divisionData = { getDivisions: filteredDivisionData };
      }
      const filterByDivisions = (divIds): any => {
        return divisionData?.getDivisions?.filter(divApiData => {
          return divIds?.some(userDiv => {
            return userDiv === divApiData?.divisionId;
          });
        });
      };
      const filteredUserProfileDivs = filterByDivisions(vendorDivisionIds);
      const filteredData = filteredUserProfileDivs?.map(ele => {
        return {
          name: `${ele["divisionId"]}-${ele["divisionName"]}`,
          id: `${ele["divisionId"]}`,
        };
      });
      setItems(filteredData);
    } else if (
      fieldProps.optionUrl === "allowance.startWeek" ||
      fieldProps.registerField === "vehicle"
    ) {
      const apiData = data?.getVehicleByStartAndEndDate
        ? data?.getVehicleByStartAndEndDate
        : data?.getVehicleByTypeAndYear;
      const filteredData = apiData?.map(ele => {
        return {
          ...ele,
          vehicleId: ele.id,
          name: `${ele["vehicleNm"]}`,
        };
      });
      setItems(filteredData);
      const isCustom = filteredData?.find(vehicle => vehicle.name === "Other");
      if (isCustom) {
        setValue(dynamicRegisterField, filteredData?.[0]);
        setValue("startWeekVehicle", filteredData?.[0]?.vehicleNm);
      }
    } else if (fieldProps.optionUrl === "startWeekVehicle") {
      const isCustomDate = ["Custom Date", "CustomDate"].includes(
        fieldProps?.tabType
      );
      let filteredData = data?.getVehicleByTypeAndYear?.map(ele => {
        return {
          ...ele,
          vehicleId: ele.id,
          name: isCustomDate
            ? `${ele["vehicleNm"]}`
            : `${ele["vehicleNm"]} - ${formateVehicleDate(
                ele?.["startDate"]
              )} - ${formateVehicleDate(ele?.["endDate"])}`,
        };
      });
      if (hideVehicles && filteredData?.length && !isCustomDate)
        filteredData = filteredData?.filter(
          vehicle =>
            compareAsc(
              new Date(eventDetailsData?.startDate),
              new Date(vehicle.startDate)
            ) === 0
        );
      setItems(filteredData);
      const isCustom = filteredData?.find(vehicle => vehicle.name === "Other");
      if (isCustom) {
        setStartWeekValueForCustomDate(filteredData?.[0]);
      }
    } else if (fieldProps.optionUrl === "performance") {
      dispatch(allowanceTypeAndPerformanceDataHandler(data));
      setItems([]);
    } else if (fieldProps.optionUrl === "allowanceType") {
      const filteredData = data?.getAllowancePerformancesByEventType?.map(
        ele => {
          const allowanceType = ele.allowanceType;
          return {
            id: allowanceType,
            name: allowanceType,
            allowanceCd: ele.allowancePerformances[0].allowanceCd,
          };
        }
      );
      setItems(filteredData);
    } else if (fieldProps.optionUrl === "planStoreGroupType") {
      const filteredData = data?.getStoreGroupTypesByDivisionId?.map(ele => {
        return {
          name:
            eventType === NDP_EVENT_TYPE && ele?.groupInd === "D"
              ? "National"
              : ele?.storeGrpTypeName,
          groupType: ele.groupType,
          groupInd: ele.groupInd,
        };
      });
      const divId = getValues("divisionIds");
      if (divId?.length) {
        const divStoreGrp = filteredData?.find(
          storeGroupType => storeGroupType?.groupInd === "D"
        );
        setValue(fieldProps.optionUrl, defaultValueFromParent || divStoreGrp);
        setValue(
          "storeGroupType",
          defaultValueFromParent || divStoreGrp?.groupInd
        );
      }
      const updatedFilterData = filteredData?.filter(storeGroupType => {
        if (userRole === "VENDOR") {
          return storeGroupType?.groupInd !== "PA" && storeGroupType;
        } else {
          return { storeGroupType };
        }
      });
      setItems(updatedFilterData);
    }
  }, [data, changed]);

  const getDefaultValue = () => {
    if (
      ["eventCreationVehicle.year", "promotionsList.year"].includes(
        registerField
      )
    ) {
      const dynamicStartDate = dynamicRegisterField?.replace(
        ".year",
        ".promoStartDate"
      );

      const isStartDate = getValues("eventCreationVehicle.startDate");
      const fromYear = startDate => {
        return getYearFromFormDate(parseISO(startDate) || Date.now());
      };
      const dateToFormatToYear =
        registerField === "eventCreationVehicle.year"
          ? isStartDate
            ? fromYear(isStartDate)
            : defaultValueFromParent
          : fromYear(getValues(dynamicStartDate) || isStartDate);

      const selectFormYear = getValues(dynamicRegisterField || registerField);

      return fieldProps?.options.find(
        item => item?.name === parseInt(selectFormYear || dateToFormatToYear)
      );
    }
    if (registerField === "unitMeasure") {
      if (getValues(dynamicRegisterField) === "Each") {
        return fieldProps?.options[0];
      } else {
        return fieldProps?.options.find(
          option => option.id === getValues(dynamicRegisterField)
        );
      }
    }
    if (registerField === "promotionType")
      return items.find(item => item.id === getValues(dynamicRegisterField));
    if (registerField === "allowance.year") return fieldProps?.options[0];

    if (registerField === "planStoreGroupType" && defaultValueFromParent) {
      return items.find(item => item.groupInd === defaultValueFromParent);
    }
    if (registerField === "planStoreGroupType") {
      return items.find(
        item => item?.groupInd === getValues("planStoreGroupType")?.groupInd
      );
    }
    // Changes for defaulting the startweek/vehicle
    //  else if (registerField === "eventCreationVehicle") {
    //   setDefaultValueForStartweek();
    // }

    const defaultValue = items?.find(item => {
      if (registerField === "performance.allowanceCd") {
        const offerAllowance = getValues("offerAllowances");
        const allowanceTypeName = getAllowanceTypeByPerformance(
          offerAllowance[offerIndex]?.allowances[allowanceIndex]?.performance
        );
        return allowanceTypeName === item?.id;
      } else if (registerField === "performance") {
        const offerAllowance = getValues("offerAllowances");
        return (
          offerAllowance[offerIndex]?.allowances[allowanceIndex]?.performance
            ?.allwPerfId === item.id
        );
      } else if (registerField === "allowance.startWeekOrVehicle") {
        getValues(dynamicRegisterField) === "Other" && onChange(item);
        return getValues(dynamicRegisterField) === item?.name;
      } else if (registerField === "vehicle") {
        const startDate = getValues(`${dynamicRegisterField}.startDate`);
        const endDate = getValues(`${dynamicRegisterField}.endDate`);
        if (
          !getValues(`${dynamicRegisterField}.vehicleNm`) &&
          item?.name === "Other"
        ) {
          setValue(`${dynamicRegisterField}`, item);
          setValue(
            `${dynamicRegisterField}.startDate`,
            dateFormat(startDate, DateFormat["YYYY-MM-DD"])
          );
          setValue(
            `${dynamicRegisterField}.endDate`,
            dateFormat(endDate, DateFormat["YYYY-MM-DD"])
          );
          setValue("startWeekVehicle", item?.vehicleNm);
          return item?.vehicleNm === item?.name;
        }
        const vehicleNm = getValues(`${dynamicRegisterField}.vehicleNm`);
        if (!vehicleNm) {
          return true;
        }
        return getValues(`${dynamicRegisterField}.vehicleNm`) === item?.name;
      } else if (
        fieldProps.registerField === "eventCreationVehicle" &&
        (getValues(
          `eventCreationVehicle.vehicleNm` || `eventCreationVehicle.name`
        ) === "Other" ||
          item?.name === "Other")
      ) {
        if (
          !getValues(
            `eventCreationVehicle.vehicleNm` || `eventCreationVehicle.name`
          )
        ) {
          setStartWeekValueForCustomDate(item);
        }

        return (
          getValues(
            `eventCreationVehicle.vehicleNm` || `eventCreationVehicle.name`
          ) === item?.name
        );
      } else if (
        registerField === "allowance.uom" &&
        getValues(dynamicRegisterField)
      ) {
        return getValues(dynamicRegisterField) === item?.name;
      } else if (
        dynamicRegisterField &&
        dynamicRegisterField.includes("absVendorPaymentType")
      ) {
        return getValues(dynamicRegisterField) === item?.name;
      } else {
        return toString.call(getValues(registerField)) === "[object Array]"
          ? getValues(registerField).includes(item?.id) ||
              defaultValueFromParent
          : toString.call(getValues(registerField)) === "[object Object]"
          ? getValues(registerField).vehicleId === item?.id
          : getValues(registerField) === item?.id;
      }
    });

    return defaultValue;
  };

  if (
    registerField === "eventCreationVehicle.year" ||
    registerField === "promotionsList.year"
  ) {
    fieldProps.required = false;
  }
  const isRequired = isEventCardOpen && registerField === "promotionType";
  const selectName = dynamicRegisterField
    ? dynamicRegisterField
    : registerField === "eventCreationVehicle"
    ? "startWeekVehicle"
    : registerField;

  if (!enableStartWeekVehicle && selectName === "startWeekVehicle" && !isNCDP) {
    disabled =
      !enableStartWeekVehicle && getValues("startWeekVehicle")
        ? enableStartWeekVehicle
        : !enableStartWeekVehicle;
  }

  const labelsForTooltip = ["UOM", "Promotion Type"];
  const stopPropogation = (event: React.KeyboardEvent<HTMLDivElement>) => {
    event.stopPropagation();
  };

  return (
    <div className="w-full" id="abs-input-select-required-div">
      <div id="abs-input-select-required-d">
        <div className="flex font-bold gap-1" id="abs-input-select-required1">
          <label htmlFor={`${fieldProps.label}`} id="abs-input-select-label">
            {fieldProps.label}
          </label>
          {fieldProps.required ? (
            <p
              className="text-sm text-left text-[#bf2912]"
              id="abs-input-select-required"
            >
              *
            </p>
          ) : null}

          <span className="flex items-center pl-1" id="abs-input-select-span">
            <InfoGraphics
              anchor={
                fieldProps?.registerField === "promotionType" ? "right" : "top"
              }
              variant="light"
              registerField={labelsForTooltip?.includes(fieldProps?.label)}
              classNameInfo="flex-col m-3 text-xs text-[#5a697b]"
              hoverContent={
                INFO_GRAPHICS?.PROMOTION_INFO[`${fieldProps?.registerField}`]
                  ?.INFO_GRAPHICS_LABEL
              }
              size="14"
            />
          </span>
        </div>
      </div>
      <div
        className={`w-full inputSelect ml-[3px] ${
          disabled ? "disable-select" : ""
        } ${
          selectName === "startWeekVehicle" ? "inputSelect" + selectName : ""
        }`}
        id="abs-input-select-container"
      >
        {items?.length > 0 ? (
          <>
            <Controller
              name={selectName}
              control={control || formControl}
              rules={{
                required: {
                  value: isRequired ? false : fieldProps?.required,
                  message: fieldProps?.error?.required?.text,
                },
              }}
              render={({ field, fieldState: { error } }) => (
                <div
                  className={`w-full  ${
                    isChanged ? "border border-[red]" : ""
                  }`}
                  onClick={e =>
                    onClick && !disabled ? onClick(e, items) : null
                  }
                  onKeyDown={stopPropogation}
                  onKeyUp={stopPropogation}
                >
                  <Select
                    items={items}
                    disabled={disabled}
                    wrap={true}
                    search={selectName === "startWeekVehicle"}
                    size="md"
                    placeholder={fieldProps?.placeholder || "Select"}
                    value={getDefaultValue()}
                    itemText="name"
                    className={`component-scroll w-full ${
                      disabled ? " disable-select" : ""
                    } ${
                      isChanged || error?.message
                        ? "rounded border !border-[red] border-error border-2"
                        : ""
                    }`}
                    id={`${fieldProps.label}`}
                    {...(selectName === "startWeekVehicle"
                      ? { menuWidth: 500 }
                      : {})}
                    onChange={e => {
                      field.onChange(e);
                      onChangeHandler(e);
                    }}
                    multiple={false}
                  />
                  {error && <FormFieldError error={error}></FormFieldError>}
                </div>
              )}
            />
          </>
        ) : (
          <button
            className={`relative w-full px-3 min-h-[40px] ${
              disabled
                ? "bg-gray-205 rounded border border-solid border-disabled"
                : isChanged
                ? "bg-gray-205 rounded border border-error border-2"
                : "bg-white rounded border border-solid border-[#BFD4E7] pointer-events-none"
            }`}
            id={
              disabled
                ? "headlessui-listbox-button-:r7:"
                : "headlessui-listbox-button-:r1l:"
            }
            type="button"
            aria-haspopup={disabled ? "listbox" : "true"}
            aria-expanded="false"
            data-headlessui-state={disabled ? "disabled" : ""}
            disabled={disabled}
          >
            <div
              className="flex items-center justify-between w-full"
              id="abs-input-select-container-d"
            >
              <div
                className="flex flex-wrap my-[6px] truncate"
                id="abs-input-select-container-d1"
              ></div>
              <span
                className="flex items-center justify-end min-w-[24px] pointer-events-none"
                id="abs-input-select-container-span"
              >
                <svg
                  className="transition duration-200"
                  width="12"
                  height="8"
                  viewBox="0 0 12 8"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.59 0.589844L6 5.16984L1.41 0.589844L0 1.99984L6 7.99984L12 1.99984L10.59 0.589844Z"
                    fill="#2B303B"
                  ></path>
                </svg>
              </span>
            </div>
          </button>
        )}
      </div>
    </div>
  );
};

export default memo(InputSelect);
