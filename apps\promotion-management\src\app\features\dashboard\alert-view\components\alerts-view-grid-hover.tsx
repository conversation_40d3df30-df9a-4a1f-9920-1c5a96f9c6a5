import Table from "@albertsons/uds/molecule/Table";
import { formatTimestamp } from "@me-upp-js/utilities";
import { useGetItemsData } from "apps/promotion-management/src/app/shared/hooks/useGetItemsData";

const AlertsViewGridHover = ({ task }) => {
  const { itemsData, isPlanEventItemsByCICSLoading } = useGetItemsData({
    task,
  });
 
    const showCustomEndDateCol = itemsData.filter((items:any)=>items.customEndDate) 
  const columns = [
    {
      id: "cic",
      label: "CIC",
      width: "5vw",
      value: item => (
        <div className="px-3 py-[4px] leading-4 text-[14px] text-[#2B303C]">
          {item?.itemId}
        </div>
      ),
    },
    {
      id: "itemDescription",
      label: "Item Description",
      width: "10vw",
      value: item => (
        <div className="px-3 max-w-[15vw] truncate py-[4px] leading-4 text-[14px] text-[#2B303C]">
          {item?.itemDescription}
        </div>
      ),
    },
    {
      id: "pack",
      label: "Pack",
      width: "4vw",
      value: item => (
        <div className="px-3 py-[4px] leading-4 text-[14px] text-[#2B303C]">
          {item?.rogPackRetails?.primaryUpcFirstPackRetail || item?.pack}
        </div>
      ),
    },
    {
      id: "size",
      label: "Size",
      width: "5vw",
      value: item => (
        <div className="px-3 py-[4px] leading-4 text-[14px] text-[#2B303C]">
          {item?.descriptiveSize || item?.size}
        </div>
      ),
    },
    ...(showCustomEndDateCol.length ? [ {
      id: "end-date",
      label: "End Date",
      width: "5vw",
      value: item => (
        item?.customEndDate && <div className="px-3 py-[4px] leading-4 text-[14px] text-[#2B303C]">
          {formatTimestamp({
            timestamp: item?.customEndDate,
            pattern: "MM/DD/YY"
      })}
        </div>
      ),
    }] : []),
   
  ];
  return (
    <div className="abs-alerts-view-grid-hover-container">
      <Table
        id="abs-alert-hover-table"
        itemKey="alertsHoverData"
        items={itemsData}
        columns={columns}
        loading={isPlanEventItemsByCICSLoading}
        noHeader
        dividers="horizontal"
        noPagination
      />
    </div>
  );
};

export default AlertsViewGridHover;
