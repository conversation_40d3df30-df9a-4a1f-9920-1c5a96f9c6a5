import classNames from "classnames";
import { ChevronDown } from "lucide-react";
import { memo } from "react";

const DisabledField = ({
  label,
  required,
  value,
  isSelect = false,
  prefix = "",
  baseId,
}) => {
  return (
    <div id={`${baseId}-container-one`}>
      <div className="flex font-bold gap-1" id={`${baseId}-label`}>
        <p id={`${baseId}-label`}>{label}</p>
        {required && (
          <p
            id={`${baseId}-label-required`}
            className="text-sm text-left text-[#bf2912]"
          >
            *
          </p>
        )}
      </div>
      <div
        id={`${baseId}-value`}
        className="flex rounded bg-gray-205 h-[40px] text-disabled-text border border-[#a5a7ab] p-2 cursor-not-allowed items-center justify-between"
      >
        <p
          id={`${baseId}-value-text`}
          className={classNames({
            "text-base font-bold text-left mt-1": true,
            "": !isSelect,
          })}
        >
          {prefix}
          {value}
        </p>
        {isSelect && <ChevronDown />}
      </div>
    </div>
  );
};
export default memo(DisabledField);
