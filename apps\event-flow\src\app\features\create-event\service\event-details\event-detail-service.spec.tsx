import { timeDifference } from "@me/util-helpers";
import {
  formPpgName,
  formPpgNameBasedOnUnitTypeAndCount,
  formateVehicleDate,
  getDateInTimeFormat,
  getEventCreationVehicleName,
  getEventDetailsDates,
  getEventName,
  getProductGroups,
  getPromoType,
  getRegularPrice,
  getStoreGroups,
  getSubTitle,
  getTitle,
  getUpdatedYearBasedOnCurrentDate,
  formatCicPpgDisplayName,
  getCicValuesInEditMode,
  getStoreGroupsListFromOffers,
  checkForCaseAllowance,
  getUnResolvedCICItemIds,
  getUnitTypesOfAddedCICs,
  isCICTypePPGsAvailable,
  removePPGs,
  removeInvalidCICIds,
  disablePPGorCICField,
  removeObjectsById,
  checkForKey,
} from "./event-detail-service";
import * as eventDetailsServiceUtils from "./event-detail-service";

describe("eveent detail service", () => {
  it("get titles", () => {
    const val = getTitle("test1");
    expect(val).toBe("test1");
  });

  it("get event name", () => {
    const val = getEventName("test1");
    expect(val).toBe("test1");

    const val2 = getEventName();
    expect(val2).toBe("Event Name");
  });

  it("get sub title", () => {
    const updatedObj = {
      name: "test1",
      createTs: "2023-04-10T10:43:38.870Z",
    };
    const val = getSubTitle(updatedObj);
    const time = timeDifference(updatedObj.createTs);
    expect(val).toBe(`Updated by test1 ${time} ago`);
  });

  //   it("get event details default values", () => {
  //     const val = getEventDetailsDefaultValues(true, {});
  //     expect(val).toBe({});

  //     const val1 = getEventDetailsDefaultValues(false, {});
  //     expect(val1).toBe({});
  //   });

  it("get product groups", () => {
    const val = getProductGroups([
      { name: "prod;prod", id: 1, supplier: "sup1" },
    ]);
    expect(val).toBeTruthy();
  });

  it("get store groups", () => {
    const val = getStoreGroups([
      { storeGroupName: "store", storeGroupId: 1, storeCount: 1 },
    ]);
    expect(val).toBeTruthy();
  });

  //   it("get Store Group Type By Store Group", () => {
  //     const val = getStoreGroups([
  //       { storeGroupName: "store", storeGroupId: 1, storeCount: 1 },
  //     ]);
  //     expect(val).toBeTruthy();
  //   });

  it("get event Details Dates", () => {
    const val = getEventDetailsDates({});
    expect(val).toBe("");

    const val2 = getEventDetailsDates({
      eventCreationVehicle: {
        startDate: new Date(2024, 1, 1),
        endDate: new Date(2024, 2, 2),
        vehicleType: {
          vehicleTypDesc: "Weekly Insert",
        },
      },
      startDate: new Date(2000, 1, 1),
      endDate: new Date(2000, 2, 2),
    });
    expect(val2).toBe("02/01/24 - 03/02/24");
  });

  it("get event creation vehicle name ", () => {
    const val = getEventCreationVehicleName({
      vehicleNm: "test1",
    });
    expect(val).toBe("test1");
  });

  it("get updated year based on current year", () => {
    const val = getUpdatedYearBasedOnCurrentDate?.(new Date(2000, 10, 10))[2];
    expect(val).toBe(2000);
  });

  it("get time from date time object", () => {
    const val = getDateInTimeFormat("2023-04-10T10:43:38.870Z");
    expect(val).toBe(1681123418870);
  });

  it("should not add sourceProductGroupId if it is not present", () => {
    const ppgObj = {
      name: "One Tree Ciders",
      displayInd: false,
      unitType: 1,
      itemCount: 2,
    };

    const efConstants = {
      DISPLAY_TYPE_PPG_CD: "S",
      PPG_UNIT_TYPE_MAPPER: {
        1: { code: "S" },
      },
    };

    const result = formPpgName(ppgObj, efConstants);

    expect(result).toEqual("One Tree Ciders - S - 2");
  });

  it("should handle displayInd true correctly", () => {
    const ppgObj = {
      name: "One Tree Ciders",
      sourceProductGroupId: 209896,
      displayInd: true,
      unitType: 1,
      itemCount: 2,
    };

    const efConstants = {
      DISPLAY_TYPE_PPG_CD: "S",
      PPG_UNIT_TYPE_MAPPER: {
        1: { code: "MapperCode" },
      },
    };

    const result = formPpgName(ppgObj, efConstants);

    expect(result).toEqual("209896 - One Tree Ciders - D - 2");
  });
});
describe("getTitle", () => {
  it("should return the title wrapped in template literals", () => {
    const title = "test1";
    expect(getTitle(title)).toBe(`test1`);
  });
});
describe("getEventName", () => {
  it("should return the provided event name", () => {
    const eventName = "test1";
    expect(getEventName(eventName)).toBe(`test1`);
  });

  it("should return the default event name when no argument is provided", () => {
    expect(getEventName()).toBe(`Event Name`);
  });
});
describe("getStoreGroups", () => {
  it("should return an array of store groups with the correct structure", () => {
    const storeGroups = [
      {
        storeGroupName: "Store Group A",
        storeGroupId: 1,
        storeCount: 5,
      },
    ];

    const result = getStoreGroups(storeGroups);
    expect(result).toEqual([
      {
        storeGroupName: "Store Group A",
        storeGroupId: 1,
        storeCount: 5,
      },
    ]);
  });
});
describe("getStoreGroupsListFromOffers", () => {
  it("should return store groups when eventDetailsData contains offer allowances with store groups", () => {
    const eventDetailsData = {
      offerAllowances: [
        {
          allowances: [
            {
              storeGroups: [
                { name: "Store Group 1", storeGroupId: "11" },
                { name: "Store Group 2", storeGroupId: "12" },
              ],
            },
          ],
        },
        {
          allowances: [
            {
              storeGroups: [
                { name: "Store Group 3", storeGroupId: "13" },
                { name: "Store Group 1 DUPLICATE ID", storeGroupId: "11" },
                { name: "Store Group 1", storeGroupId: "11" },
              ],
            },
          ],
        },
      ],
    };
    const result = getStoreGroupsListFromOffers(eventDetailsData);
    // note that "duplicate"/same store groups in multiple offers for Allowance ONLY
    //  storeGroupId records are not included
    expect(result).toEqual([
      { name: "Store Group 1", storeGroupId: "11" },
      { name: "Store Group 2", storeGroupId: "12" },
      { name: "Store Group 3", storeGroupId: "13" },
    ]);
  });
  it("should return an empty array when eventDetailsData contains no offer allowances", () => {
    const eventDetailsData = {};
    const result = getStoreGroupsListFromOffers(eventDetailsData);
    expect(result).toEqual([]);
  });

  it("should return an empty array when offer allowances do not contain store groups", () => {
    const eventDetailsData = {
      offerAllowances: [
        {
          allowances: [{ name: "Allowance 1" }, { name: "Allowance 2" }],
        },
      ],
    };
    const result = getStoreGroupsListFromOffers(eventDetailsData);
    expect(result).toEqual([]);
  });
});

describe("formPpgNameBasedOnUnitTypeAndCount function", () => {
  it("returns an empty array for undefined input", () => {
    const result = formPpgNameBasedOnUnitTypeAndCount(undefined);
    expect(result).toEqual([]);
  });

  it("correctly updates PPG names in the list", () => {
    const ppgList = [
      {
        unitType: "UnitType1",
        displayInd: true,
        itemCount: 5,
        name: "PPG Name 1",
      },
      {
        unitType: "UnitType2",
        displayInd: false,
        itemCount: 3,
        name: "PPG Name 2",
      },
    ];

    const expectedResult = [
      {
        unitType: "UnitType1",
        displayInd: true,
        itemCount: 5,
        name: "PPG Name 1 - D - 5",
        planProductGroupId: undefined,
        ppgName: "PPG Name 1",
      },
      {
        unitType: "UnitType2",
        displayInd: false,
        itemCount: 3,
        name: "PPG Name 2 -  - 3",
        planProductGroupId: undefined,
        ppgName: "PPG Name 2",
      },
    ];

    const result = formPpgNameBasedOnUnitTypeAndCount(ppgList);
    expect(result).toEqual(expectedResult);
  });

  it("returns the same list when no changes are made", () => {
    const ppgList = [
      {
        unitType: "UnitType3",
        displayInd: false,
        itemCount: 2,
        name: "Existing Name 1 -  - 2",
        planProductGroupId: undefined,
        ppgName: "Existing Name 1 -  - 2",
      },
      {
        unitType: "UnitType4",
        displayInd: true,
        itemCount: 1,
        name: "Existing Name 2 - D - 1",
        planProductGroupId: undefined,
        ppgName: "Existing Name 2 - D - 1",
      },
    ];

    const result = formPpgNameBasedOnUnitTypeAndCount(ppgList);
    expect(result).toEqual(ppgList);
  });

  it("handles an empty list", () => {
    const result = formPpgNameBasedOnUnitTypeAndCount([]);
    expect(result).toEqual([]);
  });
  it("should return an empty array if ppgList is undefined", () => {
    expect(formPpgNameBasedOnUnitTypeAndCount(undefined)).toEqual([]);
  });

  it("should return an empty array if ppgList is null", () => {
    expect(formPpgNameBasedOnUnitTypeAndCount(null)).toEqual([]);
  });

  it("should handle an empty ppgList", () => {
    expect(formPpgNameBasedOnUnitTypeAndCount([])).toEqual([]);
  });
});

const ALLOWANCE_TYPES = {
  CASE: {
    createInd: ["Y", "N", "P"], // Sample values for the test
  },
};

describe("checkForCaseAllowance", () => {
  it("should return false if createInd is not included in ALLOWANCE_TYPES.CASE.createInd", () => {
    const offer = { createInd: "Z" };
    expect(checkForCaseAllowance(offer)).toBe(false);
  });

  it("should return false if offer is undefined", () => {
    expect(checkForCaseAllowance(undefined)).toBe(false);
  });

  it("should return false if createInd is undefined", () => {
    const offer = {};
    expect(checkForCaseAllowance(offer)).toBe(false);
  });
});
describe("formPpgName function", () => {
  it("returns an empty string for undefined input", () => {
    const result = formPpgName(undefined, {});
    expect(result).toBe("");
  });

  it("forms the PPG name correctly when displayInd is true", () => {
    const ppgObj = {
      unitType: "SomeUnitType",
      displayInd: true,
      itemCount: 5,
      name: "PPG Name",
    };
    const result = formPpgName(ppgObj, { DISPLAY_TYPE_PPG_CD: "D" });
    expect(result).toBe("PPG Name - D - 5");
  });

  it("forms the PPG name correctly when displayInd is false", () => {
    const ppgObj = {
      unitType: "",
      displayInd: false,
      itemCount: 3,
      name: "Another PPG Name",
    };
    const result = formPpgName(ppgObj, {});
    expect(result).toBe("Another PPG Name -  - 3");
  });

  it("returns the original name when it already includes PPG details", () => {
    const ppgObj = {
      unitType: "SomeUnitType",
      displayInd: false,
      itemCount: 2,
      name: "Existing Name - SomeUnitType - 2",
    };
    const result = formPpgName(ppgObj, {});
    expect(result).toBe("Existing Name - SomeUnitType - 2 -  - 2");
  });
});

describe("getPromoType", () => {
  it("returns promo type with underscores replaced by spaces for valid data", () => {
    const eventDetailsData = {
      pricing: [{ promoType: "NET_PRICE" }],
    };

    const result = getPromoType(eventDetailsData);

    expect(result).toBe("NET PRICE");
  });

  it("returns an empty string for empty pricing array", () => {
    const eventDetailsData = {
      pricing: [],
    };

    const result = getPromoType(eventDetailsData);

    expect(result).toBe(undefined);
  });

  it("returns an empty string for non-array pricing", () => {
    const eventDetailsData = {
      pricing: "NET_PRICE",
    };

    const result = getPromoType(eventDetailsData);

    expect(result).toBe("");
  });

  it("returns an empty string for missing promoType property", () => {
    const eventDetailsData = {
      pricing: [{}],
    };

    const result = getPromoType(eventDetailsData);

    expect(result).toBe(undefined);
  });

  it("returns an empty string for missing pricing property", () => {
    const eventDetailsData = {};

    const result = getPromoType(eventDetailsData);

    expect(result).toBe("");
  });

  it("returns an empty string for undefined eventDetailsData", () => {
    const result = getPromoType(undefined);

    expect(result).toBe("");
  });
});

describe("getRegularPrice", () => {
  it("should return regular price when promotionsList is an array with valid data", () => {
    const eventDetailsData = {
      promotionsList: [
        {
          promoDetails: {
            regularPrice: 50,
          },
        },
      ],
    };
    const result = getRegularPrice(eventDetailsData);
    expect(result).toBe(50);
  });

  it("should return an empty string when promotionsList is not an array", () => {
    const eventDetailsData = {
      promotionsList: null, // or undefined or anything other than an array
    };
    const result = getRegularPrice(eventDetailsData);
    expect(result).toBe("");
  });

  it("should return an empty string when promotionsList array is empty", () => {
    const eventDetailsData = {
      promotionsList: [],
    };
    const result = getRegularPrice(eventDetailsData);
    expect(result).toBe(undefined);
  });

  it("should return an empty string when promoDetails is not present in the promotionsList array", () => {
    const eventDetailsData = {
      promotionsList: [{}],
    };
    const result = getRegularPrice(eventDetailsData);
    expect(result).toBe(undefined);
  });

  it("should return an empty string when regularPrice is not present in promoDetails", () => {
    const eventDetailsData = {
      promotionsList: [
        {
          promoDetails: {},
        },
      ],
    };
    const result = getRegularPrice(eventDetailsData);
    expect(result).toBe(undefined);
  });

  it("should handle null eventDetailsData gracefully and return an empty string", () => {
    const result = getRegularPrice(null);
    expect(result).toBe("");
  });

  it("should handle undefined eventDetailsData gracefully and return an empty string", () => {
    const result = getRegularPrice(undefined);
    expect(result).toBe("");
  });

  it("should handle eventDetailsData without promotionsList array gracefully and return an empty string", () => {
    const eventDetailsData = {};
    const result = getRegularPrice(eventDetailsData);
    expect(result).toBe("");
  });
  it("should format the ppg name if added through CICs", () => {
    const data = {
      productGroupType: "CIC",
      name: "123 - promo product group",
    };
    const expectedData = "CIC #123 - promo product group";
    const result = formatCicPpgDisplayName(data);
    expect(result).toBe(expectedData);
  });
  it("should not format the ppg name if not added through CICs", () => {
    const data = {
      name: "123 - promo product group",
    };
    const result = formatCicPpgDisplayName(data);
    expect(result).toBe(data.name);
  });
  it("should return the cic ids in edit mode if added by CICs", () => {
    const data = [
      {
        productGroupType: "CIC",
        name: "123 - promo product group",
      },
      {
        productGroupType: "",
        name: "456 - promo product group111",
      },
    ];
    const result = getCicValuesInEditMode(data);
    expect(result).toBe("123");
  });
  it("should not return the cic ids in edit mode if not added by CICs", () => {
    const data = [
      {
        name: "123 - promo product group",
      },
    ];
    const result = getCicValuesInEditMode(data);
    expect(result).toBe("");
  });
});

describe("checkForCaseAllowance", () => {
  const ALLOWANCE_TYPES = {
    CASE: {
      createInd: ["CW", "CB", "CD"],
    },
  };

  test("returns true when offer includes case allowance", () => {
    const offer = { createInd: "CW" };
    const result = checkForCaseAllowance(offer);
    expect(result).toBe(true);
  });

  test("returns false when offer does not include case allowance", () => {
    const offer = { createInd: "TS" };
    const result = checkForCaseAllowance(offer);
    expect(result).toBe(false);
  });

  test("returns false when offer is undefined", () => {
    const result = checkForCaseAllowance(undefined);
    expect(result).toBe(false);
  });

  test("returns true when offer includes case allowance with true value", () => {
    const offer = { createInd: "CB" };
    const result = checkForCaseAllowance(offer);
    expect(result).toBe(true);
  });
});
describe("getStoreGroupsListFromOffers", () => {
  test("returns store groups when eventDetailsData contains offer allowances with store groups", () => {
    const eventDetailsData = {
      offerAllowances: [
        {
          allowances: [
            {
              storeGroups: [
                { name: "Store Group 1", storeGroupId: "11" },
                { name: "Store Group 2", storeGroupId: "12" },
              ],
            },
          ],
        },
        {
          allowances: [
            { storeGroups: [{ name: "Store Group 3", storeGroupId: "13" }] },
          ],
        },
      ],
    };
    const result = getStoreGroupsListFromOffers(eventDetailsData);
    expect(result).toEqual([
      { name: "Store Group 1", storeGroupId: "11" },
      { name: "Store Group 2", storeGroupId: "12" },
      { name: "Store Group 3", storeGroupId: "13" },
    ]);
  });

  test("returns empty array when eventDetailsData is undefined", () => {
    const result = getStoreGroupsListFromOffers(undefined);
    expect(result).toEqual([]);
  });

  test("returns empty array when eventDetailsData contains no offer allowances", () => {
    const eventDetailsData = {};
    const result = getStoreGroupsListFromOffers(eventDetailsData);
    expect(result).toEqual([]);
  });

  test("returns empty array when offer allowances do not contain store groups", () => {
    const eventDetailsData = {
      offerAllowances: [
        {
          allowances: [{ name: "Allowance 1" }, { name: "Allowance 2" }],
        },
      ],
    };
    const result = getStoreGroupsListFromOffers(eventDetailsData);
    expect(result).toEqual([]);
  });
});

describe("event-detail-service", () => {
  describe("getUnResolvedCICItemIds", () => {
    it("returns error for DISPLAY_ITEM_FLAG_MIXED", () => {
      jest
        .spyOn(eventDetailsServiceUtils, "getItemIds")
        .mockImplementationOnce(() => "1,2");
      const unresolvedItems = [{ resolveValidInd: "DISPLAY_ITEM_FLAG_MIXED" }];
      const result = getUnResolvedCICItemIds(unresolvedItems);
      expect(result).toEqual({ unresolvedError: "DISPLAY_ITEM_FLAG_MIXED" });
    });

    it("returns ids and error for VENDOR_NOT_LINKED", () => {
      const unresolvedItems = [
        { resolveValidInd: "VENDOR_NOT_LINKED", itemId: "1" },
        { resolveValidInd: "VENDOR_NOT_LINKED", itemId: "2" },
      ];
      const result = getUnResolvedCICItemIds(unresolvedItems);
      expect(result).toEqual({
        unresolvedError: "VENDOR_NOT_LINKED",
        unresolvedIds: "1,2",
      });
    });

    it("returns ids and error for SMIC_NOT_IN_ROLLOUT", () => {
      const unresolvedItems = [
        { resolveValidInd: "SMIC_NOT_IN_ROLLOUT", itemId: "1" },
        { resolveValidInd: "SMIC_NOT_IN_ROLLOUT", itemId: "2" },
      ];
      const result = getUnResolvedCICItemIds(unresolvedItems);
      expect(result).toEqual({
        unresolvedError: "SMIC_NOT_IN_ROLLOUT",
        unresolvedIds: "1,2",
      });
    });
  });
});

describe("getUnitTypesOfAddedCICs", () => {
  it("returns undefined when ppgs is undefined", () => {
    const result = getUnitTypesOfAddedCICs(undefined);
    expect(result).toBeUndefined();
  });

  it('returns undefined when no ppg has productGroupType "CIC"', () => {
    const ppgs = [{ productGroupType: "PPG", unitType: "1" }];
    const result = getUnitTypesOfAddedCICs(ppgs);
    expect(result).toBeUndefined();
  });

  it('returns the code of the unit type of the first ppg with productGroupType "CIC"', () => {
    const ppgs = [
      { productGroupType: "PPG", unitType: "1" },
      { productGroupType: "CIC", unitType: "2" },
    ];
    const result = getUnitTypesOfAddedCICs(ppgs);
    expect(result).toEqual("M");
  });
});

describe("isCICTypePPGsAvailable", () => {
  it("returns false when ppgs is undefined", () => {
    const result = isCICTypePPGsAvailable(undefined);
    expect(result).toEqual(false);
  });

  it("returns false when ppgs is an empty array", () => {
    const result = isCICTypePPGsAvailable([]);
    expect(result).toEqual(false);
  });

  it('returns false when no ppg has productGroupType "CIC"', () => {
    const ppgs = [{ productGroupType: "PPG" }];
    const result = isCICTypePPGsAvailable(ppgs);
    expect(result).toEqual(false);
  });

  it('returns true when at least one ppg has productGroupType "CIC"', () => {
    const ppgs = [{ productGroupType: "PPG" }, { productGroupType: "CIC" }];
    const result = isCICTypePPGsAvailable(ppgs);
    expect(result).toEqual(true);
  });
});

describe("removePPGs", () => {
  it("returns an empty array when totalPPGs is empty", () => {
    const ppg = { sourceProductGroupId: "1" };
    const result = removePPGs(ppg, []);
    expect(result).toEqual({ filteredPPGs: [] });
  });

  it("returns the same array when totalPPGs does not contain the ppg to be removed", () => {
    const ppg = { sourceProductGroupId: "1" };
    const totalPPGs = [
      { sourceProductGroupId: "2" },
      { sourceProductGroupId: "3" },
    ];
    const result = removePPGs(ppg, totalPPGs);
    expect(result).toEqual({ filteredPPGs: totalPPGs });
  });

  it("returns an array without the ppg to be removed when totalPPGs does contain the ppg", () => {
    const ppg = { sourceProductGroupId: "1" };
    const totalPPGs = [
      { sourceProductGroupId: "1" },
      { sourceProductGroupId: "2" },
    ];
    const result = removePPGs(ppg, totalPPGs);
    expect(result).toEqual({ filteredPPGs: [{ sourceProductGroupId: "2" }] });
  });
});

describe("removeInvalidCICIds", () => {
  it("returns an empty array when cicIds is empty", () => {
    const cicIds = [];
    const invalidItems = ["1", "2"];
    const result = removeInvalidCICIds(cicIds, invalidItems);
    expect(result).toEqual([]);
  });

  it("returns the same array when invalidItems is empty", () => {
    const cicIds = ["1", "2"];
    const invalidItems = [];
    const result = removeInvalidCICIds(cicIds, invalidItems);
    expect(result).toEqual(cicIds);
  });

  it("returns the same array when invalidItems does not contain any of the cicIds", () => {
    const cicIds = ["1", "2"];
    const invalidItems = ["3", "4"];
    const result = removeInvalidCICIds(cicIds, invalidItems);
    expect(result).toEqual(cicIds);
  });

  it("returns an array without the invalid cicIds when invalidItems does contain some of the cicIds", () => {
    const cicIds = ["1", "2", "3"];
    const invalidItems = ["2", "3"];
    const result = removeInvalidCICIds(cicIds, invalidItems);
    expect(result).toEqual(["1"]);
  });
});

describe("disablePPGorCICField", () => {
  it("returns false when ppgs is undefined", () => {
    const result = disablePPGorCICField(undefined);
    expect(result).toEqual(false);
  });

  it("returns false when ppgs is an empty array", () => {
    const result = disablePPGorCICField([]);
    expect(result).toEqual(false);
  });

  it('returns false when the first ppg has productGroupType "CIC" and field is "cic"', () => {
    const ppgs = [{ productGroupType: "CIC" }];
    const result = disablePPGorCICField(ppgs, "cic");
    expect(result).toEqual(false);
  });

  it('returns true when the first ppg does not have productGroupType "CIC" and field is "cic"', () => {
    const ppgs = [{ productGroupType: "PPG" }];
    const result = disablePPGorCICField(ppgs, "cic");
    expect(result).toEqual(true);
  });

  it('returns true when the first ppg has productGroupType "CIC" and field is not "cic"', () => {
    const ppgs = [{ productGroupType: "CIC" }];
    const result = disablePPGorCICField(ppgs, "PPG");
    expect(result).toEqual(true);
  });

  it('returns false when the first ppg does not have productGroupType "CIC" and field is not "cic"', () => {
    const ppgs = [{ productGroupType: "PPG" }];
    const result = disablePPGorCICField(ppgs, "PPG");
    expect(result).toEqual(false);
  });
});

describe("removeObjectsById", () => {
  it("returns an empty array when formattedPpgData is []", () => {
    const selectedPPGs = [{ sourceProductGroupId: "1" }];
    const result = removeObjectsById({
      formattedPpgData: [],
      selectedPPGs,
    });
    expect(result).toEqual([]);
  });

  it("returns the same array when selectedPPGs is empty", () => {
    const formattedPpgData = [{ sourceProductGroupId: "1" }];
    const selectedPPGs = [];
    const result = removeObjectsById({ formattedPpgData, selectedPPGs });
    expect(result).toEqual(formattedPpgData);
  });

  it("returns the same array when selectedPPGs does not contain any of the formattedPpgData items", () => {
    const formattedPpgData = [{ sourceProductGroupId: "1" }];
    const selectedPPGs = [{ sourceProductGroupId: "2" }];
    const result = removeObjectsById({ formattedPpgData, selectedPPGs });
    expect(result).toEqual(formattedPpgData);
  });

  it("returns an array without the selectedPPGs items when selectedPPGs does contain some of the formattedPpgData items", () => {
    const formattedPpgData = [
      { sourceProductGroupId: "1" },
      { sourceProductGroupId: "2" },
    ];
    const selectedPPGs = [{ sourceProductGroupId: "2" }];
    const result = removeObjectsById({ formattedPpgData, selectedPPGs });
    expect(result).toEqual([{ sourceProductGroupId: "1" }]);
  });
  it("should check for key in object", () => {
    const obj = { ppgName: "test" };
    const result = checkForKey(obj);
    expect(result).toBe("test");
  });
});
