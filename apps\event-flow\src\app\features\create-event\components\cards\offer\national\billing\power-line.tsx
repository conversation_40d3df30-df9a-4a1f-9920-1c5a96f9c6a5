import { useController } from "react-hook-form";
import { InputSelect, InputText } from "../../../../fields/allowance-atoms";
import BillingCommentAllSection from "../../offer-sections/billing/billing-comment-all-section";
import MultiSelectDropDown from "./multi-selected";
import { getObjectValueFromString } from "@me-upp-js/utilities";

export const PowerLineInfo = ({
  formControls,
  onUpdateAllCommentSubmit,
  isCommentRequired,
  commonSuggestedVendorPaymentType,
  commonAcApOrArNumber,
  commonComment,
  divisionsData,
  setDivisionsData,
  isError,
}) => {
  const {
    setValue,
    register,
    control,
    formState: { errors },
  } = formControls;
  const {
    registerField,
  } = commonComment;
  const { formState } = useController({ name: registerField, control });
  const fieldErrorData =
    getObjectValueFromString(formState.errors, registerField) || {};

  return (
    <>
      <div
        className="w-full flex flex-wrap p-2 pl-4 bg-gray-206 font-bold"
        id="abs-billing-info-allowance-one-allowance-section"
      >
        <div className="flex flex-wrap w-full gap-3 text-[15px]">
          <div
            className="select-none font-bold min-w-[150px] w-[400px]"
            data-testid="tag-id"
          >
            &nbsp;
          </div>
          <div className="min-w-[80px] md:w-[100px] break-words">
            Payment
            <br /> Type
          </div>
          <div className="min-w-[100px] md:w-[100px] break-words">
            A/P or A/R Number
          </div>
          <div className="min-w-[150px] md:w-[200px] break-words">
            Vendor <br /> Name
          </div>
          <div className="min-w-[120px] md:w-[130px] break-words">
            Suggested Payment Type
          </div>
          <div className="min-w-[140px] md:w-[150px] break-words">
            Suggested A/P or A/R Number
          </div>
        </div>
      </div>
      <div className="w-full pl-4">
        <div className="flex flex-wrap gap-3">
          <div
            className="select-none font-bold min-w-[150px] w-[500px]"
            data-testid="tag-id"
          >
            &nbsp;
          </div>
          <div className="min-w-[80px] md:w-[100px]">
            <p className="text-base font-bold text-left text-[#033b69]">_ _</p>
          </div>
          <div className="min-w-[100px] md:w-[100px]">
            <p className="text-base font-bold text-left text-[#033b69]">_ _</p>
          </div>
          <div className="min-w-[150px] md:w-[200px]">
            <p className="text-base font-bold text-left text-[#033b69] truncate">
              _ _
            </p>
          </div>
          <div className="min-w-[120px] md:w-[130px]">
            <InputSelect
              fieldProps={commonSuggestedVendorPaymentType}
              register={register}
              control={control}
              options={commonSuggestedVendorPaymentType?.options}
              displayLabel={commonSuggestedVendorPaymentType?.displayLabel}
              disabled={!commonSuggestedVendorPaymentType?.options?.length}
              onChange={e => {
                setValue(commonSuggestedVendorPaymentType.registerField, e);
              }}
            />
          </div>

          <div className="min-w-[140px] md:w-[150px] break-words">
            <InputText
              control={control}
              register={register}
              fieldProps={commonAcApOrArNumber}
              onChange={e => {
                setValue(commonAcApOrArNumber.registerField, e?.trimStart());
              }}
              tooltip={""}
              onWheel={event => event?.currentTarget?.blur()}
            />
          </div>
        </div>

        <div className="flex flex-wrap gap-3 mt-4">
          <div
            className="select-none font-bold min-w-[150px] w-[400px] relative"
            data-testid="tag-id"
          >
            <div>
              <p
                className={`text-base font-bold text-left text-[#033b69] absolute ${
                  fieldErrorData?.message || isCommentRequired || isError
                    ? "bottom-[73px]"
                    : "bottom-[45px]"
                }`}
              >
                {"Select Divisions to Update Billing"}
              </p>
            </div>
            <MultiSelectDropDown
              values={divisionsData}
              setValues={setDivisionsData}
            />
          </div>
          <div className="flex-grow">
            <BillingCommentAllSection
              formControls={formControls}
              commonComment={commonComment}
              onUpdateAllCommentSubmit={onUpdateAllCommentSubmit}
              isCommentRequired={isCommentRequired}
              isNationalEvent={true}
            />
          </div>
        </div>
      </div>
    </>
  );
};
