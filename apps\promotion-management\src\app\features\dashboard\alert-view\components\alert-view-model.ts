export type Alert = {
  id: string;
  taskId: string;
  planEventIdNbr: string;
  eventName: string;
  eventType: string;
  parentEvent: ParentEvent;
  startDate: string;
  endDate: string;
  type: string;
  taskStatus: string;
  subType: string;
  priority: string;
  eventStatus: string;
  currentEventStatus: string;
  simsTaskVendor: SimsTaskVendor[];
  divisionIds: string[];
  simsVendors?: string[];
  negotiationSimsVendors?: string[];
  customEndDate?: string;
};

export type SimsTaskVendor = {
  vendorId: string;
  vendorName: string;
};
export type ParentEvent = {
  eventId: string;
  planEventIdNbr: number;
};
