import { appConstants } from "@me/utils-root-props";

export const STATUS_CONSTANTS = {
  EVENT_MIN_WIDTH: 76,
  EVENT_MIN_HEIGHT: 72,
  WEEK_MAX_COUNT: 52,
  ICONS: {
    PLANNING: "circle_filled_8x8",
    DRAFT: "circle_transparent_8x8",
    "PENDING WITH VENDOR": "diamond_8x8",
    "PENDING WITH MERCHANT": "alert-triangle_16x16",
    AGREED: "nopa_transparent_8x8",
    "AGREED-PENDING": "nopa_transparent_8x8",
    APPROVED: "square_filled_8x8",
    EXECUTED: "square_transparent_8x8",
    READY: "nopa_transparent_8x8",
    ACTIVE: "square_filled_8x8",
    REJECTED: "circle_slashed_8x8",
    CANCELED: "cancelled_slashed_8x8",
    "AGREED-PENDING CANCEL REQUEST": "alert-triangle_16x16",
  },
  PENDING_WITH_VENDOR: "Pending With Vendor",
  PENDING_WITH_MERCHANT: "Pending With Merchant",
  DRAFT: "Draft",
  AGREED: "Agreed",
  ACTIVE: "Active",
  AGREED_PENDING: "Agreed-Pending",
  EXECUTED: "Executed",

  TIMELINES_PAYLOAD_MAP: {
    /*Map the timescale passed in the payload with the response of the timelines obj in pg API response */
    PERIOD: "Periods",
    WEEK: "Weeks",
    QUARTER: "Quarter",
    DAY: "Days",
  },
  TIMELINES_MAP_WITH_TIMESCALE: {
    DAY: "dayTimeScale",
    PERIOD: "period",
    WEEK: "weekNumber",
    QUARTER: "quarter",
  },
  SHOW_LABELS: [2, 4, 6],
  SHOW_PANEL: {
    1: ["calendar-icon", "workflowState", "promoType", "quadrant"],
    2: ["calendar-icon", "workflowState", "promoType", "quadrant"],
    3: [
      "calendar-icon",
      "workflowState",
      "promoType",
      "quadrant",
    ] /*'discountItem', 'priceAmount',*/,
  },
  STATUS: {
    ACTIVE: {
      containerBackgroundColor: "#BAE3DA",
      borderColor: "#5FBFA8",
      tileBackGroundColor: "#105F0E", //Left internal thick border 105F0E
      textColor: "#105F0E",
      priceTextColor: "#FFFFFF",
      text: "Active",
      filtertext: "Active",
      priceFactorBackgroundColor: "#124c81",
      separatorBackgroundColor: "#5FBFA8",
    },
    AGREED: {
      containerBackgroundColor: "#B8D5F0",
      borderColor: "#7296B8",
      tileBackGroundColor: "#1B6EBB",
      textColor: "#0D2D49",
      priceTextColor: "#2b303c",
      text: "Agreed",
      filtertext: "Agreed",
      priceFactorBackgroundColor: "#124c81",
      separatorBackgroundColor: "#7296B8",
      quadrantBgColor: "#124C81",
    },
    "REQUEST TO CANCEL": {
      containerBackgroundColor: "#B8D5F0",
      borderColor: "#7296B8",
      tileBackGroundColor: "#1B6EBB",
      textColor: "#0D2D49",
      priceTextColor: "#2b303c",
      text: "Agreed-Pending Cancel Request",
      filtertext: "Agreed-Pending Cancel Request",
      priceFactorBackgroundColor: "#124c81",
      separatorBackgroundColor: "#7296B8",
      quadrantBgColor: "#124C81",
    },
    "AGREED PENDING": {
      containerBackgroundColor: "#B8D5F0",
      borderColor: "#7296B8",
      tileBackGroundColor: "#1B6EBB",
      textColor: "#0D2D49",
      priceTextColor: "#2b303c",
      text: "Agreed Pending",
      filtertext: "Agreed-Pending",
      priceFactorBackgroundColor: "#124c81",
      separatorBackgroundColor: "#7296B8",
      quadrantBgColor: "#124C81",
    },
    "AGREED-PENDING": {
      containerBackgroundColor: "#B8D5F0",
      borderColor: "#7296B8",
      tileBackGroundColor: "#1B6EBB",
      textColor: "#0D2D49",
      priceTextColor: "#2b303c",
      text: "Agreed Pending",
      filtertext: "Agreed-Pending",
      priceFactorBackgroundColor: "#124c81",
      separatorBackgroundColor: "#7296B8",
      quadrantBgColor: "#124C81",
    },
    "AGREE TO CANCEL": {
      containerBackgroundColor: "#B8D5F0",
      borderColor: "#7296B8",
      tileBackGroundColor: "#1B6EBB",
      textColor: "#0D2D49",
      priceTextColor: "#2b303c",
      text: "Canceled",
      filtertext: "Canceled",
      priceFactorBackgroundColor: "#124c81",
      separatorBackgroundColor: "#7296B8",
      quadrantBgColor: "#124C81",
    },
    APPROVED: {
      containerBackgroundColor: "#BAE3DA",
      borderColor: "#96B1CA",
      tileBackGroundColor: "#775413",
      textColor: "#80662C",
      priceTextColor: "#2b303c",
      text: "Approved",
      filtertext: "Approved",
      priceFactorBackgroundColor: "#775413",
      separatorBackgroundColor: "#f7d171",
    },
    CANCELED: {
      containerBackgroundColor: "#F3F4F6",
      borderColor: "#CCE1DF",
      tileBackGroundColor: "#6C737F",
      textColor: "#384250",
      priceTextColor: "#2b303c",
      text: "Canceled",
      filtertext: "Canceled",
      priceFactorBackgroundColor: "#775413",
      separatorBackgroundColor: "#D2D6DB",
      quadrantBgColor: "#9DA4AE",
    },
    REJECTED: {
      containerBackgroundColor: "#F9FAFB",
      borderColor: "#D2D6DB",
      tileBackGroundColor: "#9DA4AE",
      textColor: "#6c737f",
      priceTextColor: "#2b303c",
      text: "Rejected",
      filtertext: "Rejected",
      priceFactorBackgroundColor: "#775413",
      separatorBackgroundColor: "#D2D6DB",
      quadrantBgColor: "#9DA4AE",
    },
    READY: {
      containerBackgroundColor: "#B8D5F0",
      borderColor: "#7296B8",
      tileBackGroundColor: "#1B6EBB",
      textColor: "#0D2D49",
      priceTextColor: "#2b303c",
      text: "Ready",
      filtertext: "Ready",
      priceFactorBackgroundColor: "#775413",
      separatorBackgroundColor: "#7296B8",
      quadrantBgColor: "#124C81",
    },
    DRAFT: {
      containerBackgroundColor: "#F5F9FD",
      borderColor: "#88B8E6",
      tileBackGroundColor: "#88B8E6",
      textColor: "#033B69",
      priceTextColor: "#2b303c",
      text: "Draft",
      filtertext: "Draft",
      priceFactorBackgroundColor: "#775413",
      separatorBackgroundColor: "#7296B8",
      quadrantBgColor: "#033B69",
    },
    EXECUTED: {
      containerBackgroundColor: "#E3F3EF",
      borderColor: "#80BCCC",
      tileBackGroundColor: "#115061",
      textColor: "#105F0E",
      priceTextColor: "#2b303c",
      text: "Executed",
      filtertext: "Executed",
      priceFactorBackgroundColor: "#775413",
      separatorBackgroundColor: "#80BCCC",
      quadrantBgColor: "#115061",
    },
    "PRE PLANNING": {
      containerBackgroundColor: "#BAE3DA",
      borderColor: "#8CD1C0",
      tileBackGroundColor: "#775413",
      textColor: "#775413",
      priceTextColor: "#2b303c",
      text: "Pre-Planning",
      filtertext: "Pre-Planning",
      priceFactorBackgroundColor: "#775413",
      separatorBackgroundColor: "#f7d171",
    },
    "PENDING WITH VENDOR": {
      containerBackgroundColor: "#FEF9EE",
      borderColor: "#F7D171",
      tileBackGroundColor: "#AB4205",
      textColor: "#AB4205",
      priceTextColor: "#2b303c",
      text: "Pending (V)",
      filtertext: "Pending With Vendor",
      priceFactorBackgroundColor: "#775413",
      separatorBackgroundColor: "#F7D171",
      quadrantBgColor: "#AB4205",
    },
    "PENDING WITH MERCHANT": {
      containerBackgroundColor: "#FFFAED",
      borderColor: "#FFD480",
      tileBackGroundColor: "#FF9901",
      textColor: "#b65611",
      priceTextColor: "#2b303c",
      text: "Pending (M)",
      filtertext: "Pending With Merchant",
      priceFactorBackgroundColor: "#775413",
      separatorBackgroundColor: "#FFD480",
      quadrantBgColor: "#b65611",
    },
    PLANNING: {
      containerBackgroundColor: "#f7d171",
      borderColor: "#589cdc",
      tileBackGroundColor: "#775413",
      textColor: "#775413",
      priceTextColor: "#2b303c",
      text: "Planning",
      filtertext: "Planning",
      priceFactorBackgroundColor: "#775413",
      separatorBackgroundColor: "#7296B8",
    },
    "WEAK OVERLAP": {
      containerBackgroundColor: "#f7d171",
      borderColor: "#f7d171",
      tileBackGroundColor: "#775413",
      textColor: "#775413",
      priceTextColor: "#2b303c",
      text: "Weak / Overlap",
      filtertext: "Weak / Overlap",
      priceFactorBackgroundColor: "#775413",
      separatorBackgroundColor: "#f7d171",
    },
    "WITH SUPPLIER": {
      containerBackgroundColor: "#f7d171",
      borderColor: "#f7d171",
      tileBackGroundColor: "#775413",
      textColor: "#775413",
      priceTextColor: "#2b303c",
      text: "With Supplier",
      filtertext: "With Supplier",
      priceFactorBackgroundColor: "#775413",
      separatorBackgroundColor: "#f7d171",
    },
  },
  MAX_WIDTH: {
    EVENT_STATUS: 96,
    PROMO_TYPE: 120,
    QUADRANT: 28,
    PROMO_TYPE_WITH_AMOUNT: 181,
  },
  STATUS_WIDTH: {
    DRAFT: 29,
    EXECUTED: 54,
    "PENDING (V)": 67,
    "PENDING (M)": 67,
    CANCELLED: 57,
    ACTIVE: 37,
    "AGREED TO CANCEL": 94,
    "AGREED-PENDING": 93,
    APPROVED: 57,
    REJECTED: 57,
    READY: 39,
    "PRE PLANNING": 75,
    PLANNING: 51,
    "WEAK / OVERLAP": 86,
    "WITH SUPPLIER": 78,
    AGREED: 42,
  },
};

export const OFFER_ITEM_ADDED_DELETED_FIELD_PATHS = [
  "offerallowances.allowances.cicItems.user",
  "offerallowances.allowances.cicItems.itemAdded",
  "offerallowances.allowances.cicItems.newItemAction",
  "offerallowances.allowances.cicItems.itemDeleted",
  "offerallowances.allowances.cicItems.removeItemAction",
  "offerallowances.allowances.cicItems.dateTs",
  "offerallowances.allowances.cicItems.endDateTs",
];
const isFeatureFlagEnabled = (featureFlag: string) => {
  const featureFlags = JSON.parse(
    localStorage.getItem("FEATURE_FLAGS") || "[]"
  );
  return featureFlag &&
    featureFlags?.find(
      flagObj => flagObj?.featureName === featureFlag && flagObj?.enabled
    )
    ? true
    : false;
};

export const PROMO_DETAILS_HIDDEN_PRICING_FEATURE = {
  hiddenPricingFeatureEnabled: isFeatureFlagEnabled(appConstants?.FEATURE_FLAGS?.ROG_HIDDEN_PRICE),
  CHECKBOXES_LIST: [
    { key: "do_not_price", label: "Do Not Price", keyMapper: "doNotPrice" },
    { key: "split_bib", label: "Split BIB", keyMapper: "splitBIB" },
    { key: "ad_details", label: "Ad Details", keyMapper: "adDetails" },
    {
      key: "do_not_show_promo_price",
      label: "Do Not Show Promo Price",
      keyMapper: "doNotShowPromoPrice",
    },
  ],
};
export const UN_SUPPORTED = "UN_SUPPORTED";
export const AD_DETAILS = "adDetails";
export const PROMO_DETAILS_STEPPER_LABEL = "Promo Details";

export const VEHICLE_TYPES_TO_SHOW_ADDETAILS = [
  "Weekly Insert",
  "Savings Guide",
  "Fri-Sun Insert",
  "Friday ROP",
  "Sunday Insert",
];

export const ENCODED_KEYS = ["planEventComment"];
