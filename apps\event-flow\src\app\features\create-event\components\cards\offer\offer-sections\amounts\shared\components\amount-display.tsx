import { formatAmount } from "apps/event-flow/src/app/features/create-event/service/allowance/allowance-service";
import { memo } from "react";
import { IAmountDisplayProps } from "../props-types";

const AmountDisplay = ({
  label,
  value,
  icon = null,
  baseId = "",
  directDisplay = false,
}: IAmountDisplayProps) => {
  return (
    <div id={`${baseId}-renderAmountVal-container`}>
      <p
        className="text-sm font-bold text-left text-[#2b303c]"
        id={`${baseId}-render-amount-val-labelOne`}
      >
        {label}
      </p>
      {directDisplay && (
        <p className="text-sm text-left mt-2 flex items-center">{value}</p>
      )}
      {!directDisplay && (
        <p
          className="text-base font-bold text-left text-[#033b69] mt-2 flex items-center"
          id={`${baseId}-render-amount-val-value`}
        >
          <span>${formatAmount(value)}</span>
          {icon}
        </p>
      )}
    </div>
  );
};
export default memo(AmountDisplay);
