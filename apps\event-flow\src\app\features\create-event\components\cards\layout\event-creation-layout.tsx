import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import EventCardConfigurationLayout from "./event-card-configuration-layout";
import { EVENT_TYPES } from "../../../constants/event-types/event-type-index";

import { useSelectorWrap } from "@me/data-rtk";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import { eventProgressDataHandler } from "../../../service/slice/event-progress-slice";
import { eventTypes } from "../../../../event-types/constants/event-type-constant";
import { useGetAppBasePath } from "@me/util-helpers";
import Alert from "@albertsons/uds/molecule/Alert";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import CICErrorModal from "../event-details/cic-error-modal";
import { setEventTypeAndDivsionsHandler } from "../../../service/slice/event-detail-slice";
// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface IEventCreationLayoutProps {
  route?: any;
  id?: string;
}
const [DP, AO, NDP, NAO] = eventTypes.map(event => event?.eventType);
const EventCreationLayout: React.FunctionComponent<
  IEventCreationLayoutProps
> = ({ route }) => {
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const eventTypeName = searchParams.get("eventType");
  const { basePath } = useGetAppBasePath();
  const navigate = useNavigate();
  const { isProductSourcesEmpty } = useSelectorWrap(
    "is_product_sources_empty"
  ).data;
  const { data: itemMinApiResp } = useSelectorWrap("item_min_res");

  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  
  const { data: eventTypeAndDivisionsData } = useSelectorWrap(
    "event_type_and_divisions"
  );

  React.useEffect(() => {
    if (location.pathname.includes("create")) {
      const isValidEventType = [DP, AO, NDP, NAO, "NCDP"].includes(eventType);
      if (!eventTypeName || !isValidEventType) {
        navigate(`${basePath}/events`);
      }
    }
  }, []);

  const { data: allowanceProgress } = useSelectorWrap(
    "event_plan_card_update_details"
  );

  const activeStepper = allowanceProgress?.EVENT_PLAN_CARD;
  const {
    ALLOWANCE_SCREEN_TYPES,
    NEW_DIRECTION_FEATURES_FLAGS: { isDirectionalChangesEnable },
  } = efConstants;
  const [step, setStep] = useState<number>(activeStepper);
  const [showPopup, setShowPopup] = useState<boolean>(false);
  const dispatch = useDispatch();
  const eventType = eventTypeName || eventDetailsData?.eventType;

  // const { ...eventConfiguration }: any = eventConfig || {};
  // const { ...eventConfiguration }: any = EVENT_TYPES[eventType];
  const eventConfig =
    EVENT_TYPES?.[EVENT_TYPES?.event_abbreviations?.[eventType]];
  const { ...eventConfiguration }: any = eventConfig || {};
  const [evntConfig, setEvntConfig] = useState(eventConfiguration);
  useEffect(() => {
    setEvntConfig(eventConfiguration);
  }, [eventDetailsData?.eventType]);

  React.useEffect(() => {
    dispatch(
      eventProgressDataHandler({
        sectionNames: eventConfiguration?.progress_types,
      })
    );
  }, []);
  useEffect(() => {
    dispatch(
      setEventTypeAndDivsionsHandler({
        ...eventTypeAndDivisionsData,
        eventType,
      })
    );
    localStorage.setItem("EVENT_TYPE", eventType);
  }, [dispatch, eventType]);

  const handleLinkClick = e => {
    e.preventDefault();
    setShowPopup(true);
  };

  const onCloseCICModalHandler = () => {
    setShowPopup(false);
    document.body.style.overflow = "visible";
  };

  function convertItemMinResp(itemMinApiResp) {
    const {
      itemDataExcludes,
      invalidItemStatuses,
      excludedPlannedProductGroupItems,
      invalidPlannedProductGroupItems,
    } = itemMinApiResp?.res?.data[0] || {};

    const result: any = [];

    const processItems = (items, matchingItems, reasonKey) => {
      items?.forEach(item => {
        const matchingItem = matchingItems?.find(
          matchingItem => matchingItem?.itemId === item?.itemId
        );
        result.push({
          itemId: item?.itemId,
          description: matchingItem?.itemDescription || "",
          itemValidInd: item[reasonKey],
        });
      });
    };

    processItems(itemDataExcludes, excludedPlannedProductGroupItems, "reason");
    processItems(
      invalidItemStatuses,
      invalidPlannedProductGroupItems,
      "itemValidInd"
    );
    return result;
  }

  return (
    <div
      className={`col-span-12 flex flex-col gap-4 mb-4 ${efConstants.componentClassName.EVENT_CREATION_LAYOUT}`}
      id="abs-event-creation-layout-cont"
    >
      <>
        {isProductSourcesEmpty ? (
          <>
            <Alert
              isOpen={true}
              sticky={false}
              autoClose={false}
              dismissible={true}
              variant="warning"
              className="w-full"
            >
              <div>
                There are errors in items added to Event for selected stores.
              </div>
              <div className="font-normal">
                Please update items or stores to proceed. View{" "}
                <span
                  className="text-amber-800 underline"
                  style={{ cursor: "pointer" }}
                  onClick={handleLinkClick}
                >
                  Details
                </span>{" "}
                for specific error information.
              </div>
            </Alert>
            <CICErrorModal
              isOpen={showPopup}
              setOpen={() => null}
              errorTitle={`Items - ${
                convertItemMinResp(itemMinApiResp)?.length
              } error(s)`}
              height={360}
              onCloseHandler={onCloseCICModalHandler}
              cicErrorList={convertItemMinResp(itemMinApiResp)}
            />
          </>
        ) : null}
      </>
      {!isDirectionalChangesEnable &&
      (eventDetailsData?.inValidPromotions?.length ||
        eventDetailsData?.inValidAllowances?.length) ? (
        <Alert
          isOpen={true}
          dismissible={true}
          variant="error"
          size="large"
          className="w-[100%]"
          zIndex={1}
        >
          <p className="text-base" id="abs-event-creation-layout-para">
            One or more <span className="font-bold">Allowance(s) </span>
            {eventType === ALLOWANCE_SCREEN_TYPES["DP"].key ? (
              <span>
                {" "}
                &nbsp;and/or <span className="font-bold">
                  Promotion(s)
                </span>{" "}
              </span>
            ) : null}
            are impacted due to updated Event Dates. Please check below sections
            to fix them.
          </p>
        </Alert>
      ) : null}

      {evntConfig?.sections?.map((section, sectionIndex) => {
        return (
          <EventCardConfigurationLayout
            key={section}
            section={section}
            eventConfiguration={evntConfig}
            step={step}
            sectionIndex={sectionIndex}
          ></EventCardConfigurationLayout>
        );
      })}
    </div>
  );
};

export default EventCreationLayout;
