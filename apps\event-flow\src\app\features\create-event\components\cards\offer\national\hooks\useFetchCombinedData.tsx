import { useState, useEffect } from "react";
import { compareDivisionIdFn, getUniqueDivisions } from "../../offer-service";
import { getObjectKeys } from "../../../../../service/allowance/allowance-stepper-service";
import { setOfferDivisionsData } from "../../../../../service/slice/allowance-details-slice";
import { useDispatch } from "react-redux";
import { useSelectorWrap } from "@me/data-rtk";

const initialState: {
  data: any[];
  loading: boolean;
  error: null | string;
} = {
  data: [],
  loading: false,
  error: null,
};

const useFetchCombinedData = (query = null, params = [], skip = false) => {
  const dispatch = useDispatch();
  const {
    data: { deletedDivisions = [], offerDivisonErrorData = [] },
  } = useSelectorWrap("national_offer_divisions") || {};

  const [state, setState] = useState(initialState);

  const handleAmountsQueryResponse = (response: any[]) => {
    const validAllowances: any[] = [];
    let inValidAllowances: any[] = [];
    response?.forEach(element => {
      if (element?.data?.length) {
        element?.data?.forEach(item => {
          if (item?.allowances?.length) validAllowances.push(item);
          else inValidAllowances.push(item?.errorContext?.amountErrors?.[0]);
        });
      } else if (element?.error?.data?.errorContext) {
        inValidAllowances = [
          ...inValidAllowances,
          ...(element?.error?.data?.errorContext?.amountErrors || []),
        ];
      }
    });

    const deletedDivIds: any[] = inValidAllowances?.map(
      item => item?.divisionId
    );
    const updatedDelDivList = getUniqueDivisions(
      deletedDivisions,
      deletedDivIds
    );
    const offerDivIds: any[] = validAllowances?.map(item => item?.divisionId);

    dispatch(
      setOfferDivisionsData({
        offerDivisions: offerDivIds,
        deletedDivisions: updatedDelDivList,
        offerDivisonErrorData: [...offerDivisonErrorData, ...inValidAllowances],
      })
    );
    return validAllowances;
  };

  const fetchCombinedData = async (fetchQuery, fetchParams) => {
    const isAmountsCall = getObjectKeys(fetchParams?.[0]?.queryParams).includes(
      "skipOverlaps"
    );
    setState({ ...state, loading: true, error: null });
    try {
      let responses = await Promise.all(
        fetchParams?.map(param => fetchQuery(param)?.then(res => res))
      );
      responses = isAmountsCall
        ? handleAmountsQueryResponse(responses)
        : responses;

      const failedResData = responses?.find(item => item?.["error"]?.data);
      const combinedData: any = failedResData
        ? []
        : isAmountsCall
        ? responses
        : responses.map(response => response?.data)?.flat();
      // Sort combinedData based on divisionId field
      combinedData.sort(compareDivisionIdFn);

      const response = {
        data: combinedData,
        loading: false,
        error: failedResData ? failedResData?.["error"]?.data?.message : null,
      };
      setState(response);
      return response;
    } catch (err: any) {
      setState({ data: [], loading: true, error: err });
      return { data: [] };
    }
  };

  useEffect(() => {
    !skip && query && fetchCombinedData(query, params);
  }, [query, skip]);

  return { state, fetchCombinedData };
};

export default useFetchCombinedData;
