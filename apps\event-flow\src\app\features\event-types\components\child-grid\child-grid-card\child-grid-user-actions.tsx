import { memo } from "react";
import ChildEventActions from "./child-events-actions";
import ChildOfferActions from "./child-offer-actions";

function ChildGridUserActions({ cardIndex, selectedTabConfig }) {
  const { key = "" } = selectedTabConfig || {};
  const userActionsComponentMapper = {
    events: ChildEventActions,
    offers: ChildOfferActions,
  };
  const UserActionsComponent = userActionsComponentMapper?.[key];

  return (
    <div className="flex gap-[7px] ">
      {UserActionsComponent ? (
        <UserActionsComponent
          cardIndex={cardIndex}
          selectedTabConfig={selectedTabConfig}
        />
      ) : (
        <div></div>
      )}
    </div>
  );
}
export default memo(ChildGridUserActions);
