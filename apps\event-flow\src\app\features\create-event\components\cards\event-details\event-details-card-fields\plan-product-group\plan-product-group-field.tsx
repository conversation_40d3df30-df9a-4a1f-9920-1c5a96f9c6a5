import { InputAutoComplete } from "@me/input-fields";
import {
  useGetPlanProductGroupByNameLazyQuery,
  useGetPromoProductGroupsByDivisionIdAndVendorNumberLazyQuery,
} from "apps/event-flow/src/app/graphql/generated/schema";
import { memo, useCallback, useEffect, useState } from "react";
import { useFormContext, useWatch } from "react-hook-form";
import {
  formatCicPpgDisplayName,
  formPpgName,
  isCICTypePPGsAvailable,
  removePPGs,
} from "../../../../../service/event-details/event-detail-service";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import { getLoggedInUserType } from "@me/util-helpers";
import _ from "lodash";
import { usePostPlanPromoProductGroupMutation } from "../../../../../service/apis/event-api";
import {
  excludeOwnBrandsFromPPGs,
  getIsMultiVendorEvent,
  validateNCDP,
} from "../../event-details-card-service";
import { useLocation } from "react-router-dom";
import { useSelectorWrap } from "@me/data-rtk";
import { setPPGsAddedThroughCICItemsHandler } from "../../../../../service/slice/event-detail-slice";
import { useDispatch } from "react-redux";
import PpgCICField from "./ppg-cic-field";
import PromoProductGroupSummary from "../../promo-product-group-summary";
import NoPPGsAdded from "../../no-ppgs-added";
import PlanProductGroupItemModal from "../../plan-product-group-item-modal";
import {
  getFieldNameValue,
  itemOrderAlphabeticalChecker,
} from "../../utility/utility";
import ErrorMessage from "../error-message";
import PeriscopePPGErrorField from "./periscope-ppg-error-field";

function EventPlanProductGroupField({
  planProductGroups,
  storeGroups,
  formFields,
  setFormFields,
  isEditEvent,
  eventName,
  setStoreGroupFieldChanged,
}) {
  const {
    EVENT_DETAILS_ERROR_LABELS: { PROMO_PRODUCT_GROUPS_ERROR },
    PERISCOPE_ERRORS: { PPG_NOT_FOUND_KEY },
  } = efConstants;
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { data: eventTypeAndDivisionsData } = useSelectorWrap(
    "event_type_and_divisions"
  );
  const eventType = eventTypeAndDivisionsData?.eventType || "";
  const isNationalEvent = ["NDP", "NAO"].includes(eventType);
  const dispatch = useDispatch();
  const isAllowanceOrPromotion =
    eventDetailsData?.offerAllowances?.length ||
    eventDetailsData?.promotionsList?.length;
  const {
    data: { isPPGsAddedThroughCICItems },
  } = useSelectorWrap("ppgs_added_through_cic_items");
  const {
    setValue,
    getValues,
    formState: { errors },
    trigger,
  } = useFormContext();
  const [promoProductGroupFieldChanged, setPromoProductGroupFieldChanged] =
    useState(false);
  const promoProductGroups = getValues("planProductGroups");
  const location = useLocation();
  const state: any = location?.state || {};
  const PPG_TEXT_CONSTANT = "promoProductGroup";
  const [ppgFieldsData, setPpgFieldsData] = useState({
    disablePromoProductGroupsField: false,
    invalidPPGSData: [],
    disableAddCICs: true,
    disableAddItems: false,
    mixedUpcTypeError: "",
    isViewItemModalOpen: false,
    cicAlphaNumericError: false,
  });
  useEffect(() => {
    const isValid =
      getValues("planProductGroups")?.length && !isAllowanceOrPromotion;
    setFormFields(prevState => {
      return {
        ...prevState,
        disableStoreGroupType: !isValid,
        disableStoreGroups: !isValid,
      };
    });
  }, [getValues("planProductGroups")]);
  useEffect(() => {
    const isCIC =
      isCICTypePPGsAvailable(getValues("planProductGroups")) ||
      isPPGsAddedThroughCICItems;
    setPpgFieldsData(prevState => {
      return {
        ...prevState,
        disablePromoProductGroupsField: isCIC,
        disableAddCICs: !isCIC,
        disableAddItems: !isCIC,
      };
    });
  }, [isPPGsAddedThroughCICItems, getValues]);
  useEffect(() => {
    const ppgsList = getValues("planProductGroups");
    const isPlanProductGroupsAvailable = ppgsList?.length;
    const isCIC =
      isCICTypePPGsAvailable(ppgsList) || isPPGsAddedThroughCICItems;
    if (!isPlanProductGroupsAvailable) {
      updatePpgFieldsData(false, false, false);
      dispatch(
        setPPGsAddedThroughCICItemsHandler({
          isPPGsAddedThroughCICItems: false,
        })
      );
    } else {
      updatePpgFieldsData(isCIC, !isCIC, !isCIC);
    }
    if (isEditEvent && isPlanProductGroupsAvailable) {
      updatePpgFieldsData(isCIC, !isCIC, !isCIC);
    }
    if (isAllowanceOrPromotion) {
      setPpgFieldsData(prevState => {
        return {
          ...prevState,
          disablePromoProductGroupsField: true,
          disableAddCICs: true,
          disableAddItems: true,
        };
      });
    }
  }, [
    getValues("planProductGroupss"),
    formFields?.promoProductGroupName,
    isEditEvent,
    useWatch({ name: "planProductGroups" }),
    isAllowanceOrPromotion,
  ]);

  useEffect(() => {
    setPromoProductGroupFieldChanged(false);
  }, [JSON.stringify(eventDetailsData?.periscopeFetchCount)]);

  const updatePpgFieldsData = (
    disablePromoProductGroupsField,
    disableAddCICs,
    disableAddItems
  ) => {
    setPpgFieldsData(prevState => ({
      ...prevState,
      disablePromoProductGroupsField,
      disableAddCICs,
      disableAddItems,
    }));
  };

  const hasPeriscopePPGErrors =
    eventDetailsData?.periscopeDetails?.[0]?.periscopeMessages?.includes(
      PPG_NOT_FOUND_KEY
    ) && !promoProductGroupFieldChanged;
  const [getPromoProductGroups] =
    useGetPromoProductGroupsByDivisionIdAndVendorNumberLazyQuery();
  const [getPlanProductGroupsByName] = useGetPlanProductGroupByNameLazyQuery();
  const [fetchInvalidItems] = usePostPlanPromoProductGroupMutation();

  const getDefaultValue = fieldProps => {
    const data = getValues(fieldProps?.registerField);
    const defaultValues = Array.isArray(data)
      ? data?.map(item => {
          return {
            ...item,
            id: item?.id || item?.[fieldProps.prop],
            name: createDefaultNameValue(item, fieldProps),
          };
        })
      : [];
    return defaultValues;
  };

  const createDefaultNameValue = (item, fieldProps) => {
    if (item?.storeGroupName && fieldProps?.registerField === "storeGroups") {
      return `${item?.storeGroupName} (${item?.storeCount})`;
    } else if (fieldProps?.registerField === "planProductGroups") {
      return formPpgName(item, efConstants);
    } else {
      return item?.name || item?.[fieldProps?.suggestionKey];
    }
  };
  const findSourceProductGroupIdsWithZeroItemCount = (
    selectedPPGS,
    responseData
  ) => {
    const resultArray: number[] = [];
    responseData?.forEach(item => {
      const matchingItem = selectedPPGS?.find(
        ppg => ppg?.id === item?.prodGrpid && item?.totalCount === 0
      );
      if (matchingItem) {
        resultArray.push(matchingItem?.sourceProductGroupId);
      }
    });
    return resultArray;
  };
  const handleInvalidPPGItems = async (ppgs, productGroups) => {
    if (!ppgs?.length || !productGroups?.length) {
      setPpgFieldsData({
        ...ppgFieldsData,
        invalidPPGSData: [],
      });
      return;
    }
    const promoIds = productGroups?.map(p => p?.id || p?.planProductGroupId);
    const payload = {
      promoProductGroups: promoIds,
    };
    const response = await fetchInvalidItems(payload);
    const ppgsData =
      findSourceProductGroupIdsWithZeroItemCount(ppgs, response?.data) || [];
    // setInvalidPPGSData(ppgsData);
    setPpgFieldsData((prevState: any) => {
      return {
        ...prevState,
        invalidPPGSData: ppgsData,
      };
    });
  };
  const getPromoMemoDefaultValue = getDefaultValue(planProductGroups);

  const onPromoProductGroupsChange = element => {
    //clearErrors("planProductGroups");
    const userType = getLoggedInUserType();
    setFormFields(prevState => {
      return {
        ...prevState,
        promoProductGroupName: _.uniqueId(element?.[0]?.name),
      };
    });
    storeGroups &&
      !isNationalEvent &&
      Object.keys(storeGroups)?.length &&
      setValue(storeGroups?.registerField, []);
    handleInvalidPPGItems(element, getValues("planProductGroups"));
    if (element?.length) {
      const checkElementLength = element?.length > 1;
      const selectedPromoProductGroup = checkElementLength
        ? element?.reduce((prev, curr) => {
            return itemOrderAlphabeticalChecker(prev, curr, "itemCount");
          })
        : element[0];
      const ppgName = formatCicPpgDisplayName(selectedPromoProductGroup);
      setValue(PPG_TEXT_CONSTANT, ppgName);
      const planProductGroupsData = excludeOwnBrandsFromPPGs(
        getValues("planProductGroups")
      );
      const { isMultiVendorEvent, canCreateEvent } = getIsMultiVendorEvent(
        planProductGroupsData,
        userType
      );
      setFormFields(prevState => {
        return {
          ...prevState,
          isMultiVendorPPG: isMultiVendorEvent,
          canCreateEvent: canCreateEvent,
        };
      });
      const formEventName = getFieldNameValue(
        state,
        eventDetailsData,
        getValues
      );
      formEventName && setValue(eventName?.registerField, formEventName);
    } else {
      setFormFields(prevState => {
        return {
          ...prevState,
          isMultiVendorPPG: false,
          canCreateEvent: false,
        };
      });
    }
    const isPlanProductGroupsAvailable = getValues("planProductGroups")?.length;
    setFormFields(prevState => {
      return {
        ...prevState,
        disableStoreGroupType: !!isPlanProductGroupsAvailable,
      };
    });
    setStoreGroupFieldChanged(true);
    setPromoProductGroupFieldChanged(true);
    // setDisableStoreGroupType(!!isPlanProductGroupsAvailable);
    Object.keys(errors).includes("planProductGroups") &&
      trigger(["planProductGroups"]);
    const isCIC =
      isCICTypePPGsAvailable(getValues("planProductGroups")) ||
      isPPGsAddedThroughCICItems;

    if (!isPlanProductGroupsAvailable) {
      setPpgFieldsData(prevState => {
        return {
          ...prevState,
          disablePromoProductGroupsField: false,
          disableAddCICs: false,
          disableAddItems: false,
        };
      });
      dispatch(
        setPPGsAddedThroughCICItemsHandler({
          isPPGsAddedThroughCICItems: false,
        })
      );
    } else {
      setPpgFieldsData(prevState => {
        return {
          ...prevState,
          disablePromoProductGroupsField: isCIC,
          disableAddCICs: !isCIC,
          disableAddItems: !isCIC,
        };
      });
    }
  };
  const getInvalidPPgsCount = () => {
    return (
      <div
        className={`${efConstants.componentClassName.GET_INVALID_PPGS_COUNT}`}
      >
        {ppgFieldsData?.invalidPPGSData?.length ? (
          <div className="text-sm leading-4 font-bold text-error mt-1 select-none">
            {ppgFieldsData?.invalidPPGSData?.map((item, index) => (
              <span key={index}>
                {`${item}${
                  index < ppgFieldsData?.invalidPPGSData?.length - 1 ? ", " : ""
                }`}
              </span>
            ))}{" "}
            {efConstants.VALIDATION_PPGS}
          </div>
        ) : null}
      </div>
    );
  };
  const deleteSelectedPPG = ppg => {
    const { filteredPPGs } = removePPGs(ppg, getValues("planProductGroups"));
    setValue("planProductGroups", filteredPPGs);
    setStoreGroupFieldChanged(true);
    if (!filteredPPGs?.length) {
      setPpgFieldsData(prevState => ({
        ...prevState,
        mixedUpcTypeError: "",
        cicAlphaNumericError: false,
      }));
      setFormFields(prevState => ({
        ...prevState,
        unresolvedItemsError: false,
      }));
    }
    onPromoProductGroupsChange(filteredPPGs);
  };
  const getPlanProductGroups = useCallback(() => {
    return planProductGroups ? (
      <InputAutoComplete
        divisionId={formFields?.divisionId}
        getAllQuery={getPromoProductGroups}
        searchQuery={getPlanProductGroupsByName}
        fieldProps={planProductGroups}
        props={{ divisionId: formFields?.divisionId }}
        onChange={onPromoProductGroupsChange}
        defaultValue={getPromoMemoDefaultValue}
        disabled={
          validateNCDP(eventType) ||
          ppgFieldsData?.disablePromoProductGroupsField
        }
        className="auto-complete-ppgs"
        noTags={true}
        promoProductName={formFields?.promoProductGroupName}
        promoProudctGroups={getValues("planProductGroups")}
        selectionPlaceholder={planProductGroups?.placeHolder}
      />
    ) : null;
  }, [
    getValues("divisionIds"),
    getValues("planProductGroups"),
    useWatch({ name: "planProductGroups" }),
    deleteSelectedPPG,
    formFields?.divisionId,
  ]);
  const planProductGroupsSummary = useCallback(() => {
    const selectedPPGs = getValues("planProductGroups");
    return selectedPPGs?.length ? (
      <PromoProductGroupSummary
        planProductGroups={selectedPPGs}
        isAllowanceOrPromotion={isAllowanceOrPromotion}
        deleteSelectedPPG={deleteSelectedPPG}
      />
    ) : (
      <NoPPGsAdded />
    );
  }, [
    getValues("planProductGroupss"),
    formFields?.promoProductGroupName,
    deleteSelectedPPG,
    useWatch({ name: "planProductGroups" }),
    isAllowanceOrPromotion,
  ]);
  const setViewItemModalOpen = isOpen => {
    setPpgFieldsData(prevState => {
      return {
        ...prevState,
        isViewItemModalOpen: isOpen,
      };
    });
  };
  const getViewItems = useCallback(() => {
    return promoProductGroups?.length ? (
      <div className="flex justify-start items-start flex-grow-0 flex-shrink-0  gap-2.5">
        <p
          className={`flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#1b6ebb] cursor-pointer`}
          onClick={() => setViewItemModalOpen(true)}
        >
          View Items
        </p>
      </div>
    ) : null;
  }, [promoProductGroups]);
  return (
    <>
      <div className="flex w-full flex-col">
        {!hasPeriscopePPGErrors ? (
          <ErrorMessage
            warnMessage={PROMO_PRODUCT_GROUPS_ERROR}
            showError={
              eventDetailsData?.dataFetchedFromPeriscope &&
              !promoProductGroupFieldChanged &&
              eventDetailsData?.periscopeValidFromApi &&
              !promoProductGroups?.length
            }
          />
        ) : (
          <PeriscopePPGErrorField
            periscopeData={eventDetailsData?.periscopeDetails}
          />
        )}
        <div className="w-full flex justify-between gap-4">
          <div className="w-1/2">
            {getPlanProductGroups()}
            {getInvalidPPgsCount()}
            {efConstants.CIC_FEATURE_FLAGS.isCICFeatureEnabled ? (
              <PpgCICField
                ppgFieldsData={ppgFieldsData}
                setPpgFieldsData={setPpgFieldsData}
                isPPGsAddedThroughCICItems={isPPGsAddedThroughCICItems}
                onPromoProductGroupsChange={onPromoProductGroupsChange}
                setFormFields={setFormFields}
                formFields={formFields}
              />
            ) : (
              getViewItems()
            )}
          </div>
          {efConstants.CIC_FEATURE_FLAGS.isCICFeatureEnabled ? (
            <div className="flex flex-col flex-grow overflow-hidden overflow-x-auto">
              <div
                className={`flex-grow border border-solid border-[#c8daeb] mt-6 rounded min-h-[3.75rem] max-h-[15rem] overflow-hidden overflow-y-auto ${
                  isAllowanceOrPromotion ? "disable-summary-ppgs" : ""
                }`}
              >
                {planProductGroupsSummary()}
              </div>
              {getViewItems()}
            </div>
          ) : null}
        </div>
      </div>
      <PlanProductGroupItemModal
        isOpen={ppgFieldsData?.isViewItemModalOpen}
        setOpen={setViewItemModalOpen}
        promoProductGroups={getValues("planProductGroups")}
      />
    </>
  );
}

export default memo(EventPlanProductGroupField);
