.pdf_view_dialog {
  overflow-y: initial !important;
}

.pdf_content {
  min-height: auto !important;
  max-height: calc(100vh - 25vh) !important;
  overflow-y: auto !important;
  min-width: 100% !important;
}

@media screen and (min-height:620px) and (max-height: 900px) {
  .pdf-viewer-modal {
    max-height: calc(100vh) !important;
  }
}

.terms_pdf {
  height: 62vh !important;
}

.terms_pdf .pdf_content {
  height: 62vh !important;
}

.pdf_non_paginated_page {
  overflow: auto !important;
  height: 50vh !important;
}

.pdf_non_paginated_container ::-webkit-scrollbar {
  width: 0.5rem;
  height: 5px;
}

/* Track */
.pdf_non_paginated_container ::-webkit-scrollbar-track {
  box-shadow: inset 0 0 1px white;
  border-radius: 10px;
}

/* Handle */
.pdf_non_paginated_container ::-webkit-scrollbar-thumb {
  background: rgb(170, 169, 169);
  border-radius: 10px;
}

.pdf__container__content__controls {
  max-width: 100%;
  display: flex;
  margin-top: 1em;
  align-self: center;
}

.pdf__container__content__controls span {
  flex-grow: 1;
  margin: 0 1em;
  text-align: center;
}

.react-pdf__Page__canvas {
  height: 82rem !important;
  width: -moz-available !important;
  width: -webkit-fill-available !important;
}

.pdf__container__content__controls button {
  width: 80px;
}

.react-pdf__Document,
.react-pdf__Page {
  --scale-factor: none !important;
  min-height: auto !important;
}

.react-pdf__Page__textContent {
  display: none !important;
}
