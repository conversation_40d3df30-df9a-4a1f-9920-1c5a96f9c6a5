import {
  InputRadioButton as InputRadio<PERSON><PERSON>on<PERSON><PERSON>,
  InputSelect as InputSelect<PERSON><PERSON>,
  InputText as Input<PERSON>ext<PERSON><PERSON>,
  InputTextArea as InputTextArea<PERSON>tom,
} from "./allowance-atoms/index";
import InputAutoComplete from "./input-auto-complete";
import InputCheckBox from "./input-checkbox";
import InputDatePickerUDS from "./input-date-picker-uds";
import Input<PERSON>abel from "./input-label";
// import InputMultiSelect from "./input-multi-select";
import InputMultiSelect from "./multi-select";
import InputRadioButton from "./input-radio-button";
import InputSelect from "./input-select";
import InputText from "./input-text";
import InputTextArea from "./input-text-area";

export {
  InputCheckBox,
  InputSelect,
  InputDatePickerUDS,
  InputLabel,
  InputMultiSelect,
  InputRadioButton,
  InputTextArea,
  InputText,
  InputAutoComplete,
  InputRadioButtonAtom,
  InputSelectAtom,
  InputTextAtom,
  InputTextAreaAtom,
};
