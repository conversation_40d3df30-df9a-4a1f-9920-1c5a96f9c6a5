import { EEVENT_STATUS } from "@me/util-helpers";
import * as EventDetailsHelper from "./event-details-helper";
describe("EventDetailsHelper test suite", () => {
  it("should return showEventDeatilsCard truthy value", () => {
    const vendorNum = ["006446"];
    const userDetails = [
      {
        userDivisions: [{ divisionId: "27", dfltDivisionInd: "Y" }],
      },
    ];
    localStorage.setItem("vendorNum", JSON.stringify(vendorNum));
    localStorage.setItem("USER_ROLE_PERMISSIONS", JSON.stringify(userDetails));
    const eventData = {
      divisionIds: ["27"],
      simsVendors: ["006446"],
      planProductGroups: [
        {
          manufacturerSimsVendors: ["006446"],
        },
      ],
    };
    const showCard = EventDetailsHelper.showEventDeatilsCard(eventData);
    expect(showCard).toBeTruthy();
  });
  it("should return showEventDeatilsCard falsy value", () => {
    localStorage.removeItem("vendorNum");
    localStorage.removeItem("USER_ROLE_PERMISSIONS");
    const eventData = {
      divisionIds: ["27"],
      simsVendors: ["006446"],
      planProductGroups: [
        {
          manufacturerSimsVendors: ["222"],
        },
      ],
    };
    const showCard = EventDetailsHelper.showEventDeatilsCard(eventData);
    expect(showCard).toBeFalsy();
  });

  it("should return dash separated ppgName with only name", () => {
    const ppgName = EventDetailsHelper.ppgFieldName(
      "194385 - Daelmans Stroopwafels - 148569 - S - 3"
    );
    expect(ppgName).toEqual("Daelmans Stroopwafels-148569");
  });

  it("should return non-dash separated ppgName with only name", () => {
    const ppgName = EventDetailsHelper.ppgFieldName(
      "194385 - Daelmans Stroopwafels NO_DASH_HERE 148569 - S - 3"
    );
    expect(ppgName).toEqual("Daelmans Stroopwafels NO_DASH_HERE 148569");
  });

  it("should return empty string on invalid ppg format", () => {
    const ppgName = EventDetailsHelper.ppgFieldName("Daelmans Stroopwafels");
    // TODO: validate the test case
    expect(ppgName).toEqual("Daelmans Stroopwafels");
  });

  it("should return empty string on empty ppg name", () => {
    const ppgName = EventDetailsHelper.ppgFieldName("");
    expect(ppgName).toEqual("");
  });

  it("should return string unchanged on correct ppg format", () => {
    const ppgName = EventDetailsHelper.ppgFieldName(
      "Simple Mills Soft Baked Cookies - 84964 - 27 Week 10 Insert 2024"
    );
    expect(ppgName).toEqual(
      "Simple Mills Soft Baked Cookies - 84964 - 27 Week 10 Insert 2024"
    );
  });
});

describe("getEventDetailsCardChangedFields", () => {
  const mockGetValues = jest.fn();

  const eventDetailsData = {
    eventStatus: EEVENT_STATUS.PENDING_WITH_MERCHANT,
    divisionIds: [1, 2, 3],
    planProductGroups: ["group1", "group2"],
    eventCreationVehicle: {
      vehicleType: "type1",
      startDate: "2023-01-01",
      endDate: "2023-01-10",
    },
    storeGroups: [{ storeGroupId: 1 }, { storeGroupId: 2 }],
    name: "Event Name",
    periscopeDetails: [{ periscopeId: 123 }],
  };

  beforeEach(() => {
    mockGetValues.mockClear();
  });

  it("should return false for both indicators if event status is not in the specified list", () => {
    const data = { ...eventDetailsData, eventStatus: "OTHER_STATUS" };
    const result = EventDetailsHelper.getEventDetailsCardChangedFields(
      data,
      mockGetValues
    );
    expect(result).toEqual({
      pIdDetailsEventInd: false,
      otherDetailsChangedInd: false,
    });
  });

  it("should return false for both indicators if there are no changes", () => {
    mockGetValues.mockImplementation(key => {
      const values = {
        divisionIds: [1, 2, 3],
        planProductGroups: ["group1", "group2"],
        "eventCreationVehicle.vehicleType": "type1",
        "eventCreationVehicle.startDate": "2023-01-01",
        "eventCreationVehicle.endDate": "2023-01-10",
        storeGroups: [{ id: 1 }, { id: 2 }],
        name: "Event Name",
        periscopeFormField: 123,
      };
      return values[key];
    });

    const result = EventDetailsHelper.getEventDetailsCardChangedFields(
      eventDetailsData,
      mockGetValues
    );
    expect(result).toEqual({
      pIdDetailsEventInd: false,
      otherDetailsChangedInd: false,
    });
  });

  it("should return true for otherDetailsChangedInd if primitive data has changed", () => {
    mockGetValues.mockImplementation(key => {
      const values = {
        divisionIds: [1, 2, 3],
        planProductGroups: ["group1", "group2"],
        "eventCreationVehicle.vehicleType": "type2", // Changed
        "eventCreationVehicle.startDate": "2023-01-01",
        "eventCreationVehicle.endDate": "2023-01-10",
        storeGroups: [{ id: 1 }, { id: 2 }],
        name: "Event Name",
        periscopeFormField: 123,
      };
      return values[key];
    });

    const result = EventDetailsHelper.getEventDetailsCardChangedFields(
      eventDetailsData,
      mockGetValues
    );
    expect(result).toEqual({
      pIdDetailsEventInd: false,
      otherDetailsChangedInd: false,
    });
  });

  it("should return true for otherDetailsChangedInd if store group IDs have changed", () => {
    mockGetValues.mockImplementation(key => {
      const values = {
        divisionIds: [1, 2, 3],
        planProductGroups: ["group1", "group2"],
        "eventCreationVehicle.vehicleType": "type1",
        "eventCreationVehicle.startDate": "2023-01-01",
        "eventCreationVehicle.endDate": "2023-01-10",
        storeGroups: [{ id: 3 }, { id: 4 }], // Changed
        name: "Event Name",
        periscopeFormField: 123,
      };
      return values[key];
    });

    const result = EventDetailsHelper.getEventDetailsCardChangedFields(
      eventDetailsData,
      mockGetValues
    );
    expect(result).toEqual({
      pIdDetailsEventInd: false,
      otherDetailsChangedInd: false,
    });
  });

  it("should return true for pIdDetailsEventInd if periscope details have changed", () => {
    mockGetValues.mockImplementation(key => {
      const values = {
        divisionIds: [1, 2, 3],
        planProductGroups: ["group1", "group2"],
        "eventCreationVehicle.vehicleType": "type1",
        "eventCreationVehicle.startDate": "2023-01-01",
        "eventCreationVehicle.endDate": "2023-01-10",
        storeGroups: [{ id: 1 }, { id: 2 }],
        name: "Event Name",
        periscopeFormField: 456, // Changed
      };
      return values[key];
    });

    const result = EventDetailsHelper.getEventDetailsCardChangedFields(
      eventDetailsData,
      mockGetValues
    );
    expect(result).toEqual({
      pIdDetailsEventInd: true,
      otherDetailsChangedInd: false,
    });
  });
});
