import { render, screen, fireEvent } from "@testing-library/react";
import VendorSelections from "./vendor-selection";
import { useSelectorWrap } from "@me/data-rtk";
import useLeadDistributors from "../../../../create-event/hooks/useLeadDistributors";
import "@testing-library/jest-dom";
import { waitFor } from "@testing-library/react";

jest.mock("@me/data-rtk", () => ({
  ...jest.requireActual("@me/data-rtk"),
  useSelectorWrap: jest.fn(),
}));

jest.mock("../../../../create-event/service/slice/allowances-dashboard-slices.tsx", () => ({
  default: jest.fn(() => ({
    actions: {},
    reducer: jest.fn(),
  })),
}));

jest.mock("../../../../create-event/hooks/useLeadDistributors", () => jest.fn());

describe("VendorSelections Component", () => {
  const mockUseSelectorWrap = useSelectorWrap as jest.Mock;
  const mockUseLeadDistributors = useLeadDistributors as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders available vendors when no vendors are selected", () => {
    mockUseSelectorWrap.mockReturnValueOnce({
      data: {
        leadOptions: [
          { vendorNbr: "123", vendorName: "Vendor A", costAreaDesc: "Area A" },
          { vendorNbr: "456", vendorName: "Vendor B", costAreaDesc: "Area B" },
        ],
        allDivisionLeadOptions: {},
      },
    });

    mockUseLeadDistributors.mockReturnValueOnce({
      selectedList: [],
      updateSelected: jest.fn(),
    });

    render(<VendorSelections currentDivision={{ divisionId: "division1" }} />);

    expect(screen.getByText(/Vendor A/i)).toBeInTheDocument();
    expect(screen.getByText(/Vendor B/i)).toBeInTheDocument();
  });

  it("renders selected vendors when vendors are selected", () => {
    mockUseSelectorWrap.mockReturnValueOnce({
      data: {
        leadOptions: [
          { vendorNbr: "123", vendorName: "Vendor A", costAreaDesc: "Area A" },
        ],
        allDivisionLeadOptions: {},
      },
    });

    mockUseLeadDistributors.mockReturnValueOnce({
      selectedList: ["123"],
      stepData: [{ id: "123", child: [] }],
      updateSelected: jest.fn(),
    });

    render(<VendorSelections currentDivision={{ divisionId: "division1" }} />);

    expect(screen.getByText(/Vendor A/i)).toBeInTheDocument();
  });

 xit("disables excluded vendors", () => {
  mockUseSelectorWrap.mockReturnValueOnce({
    data: {
      leadOptions: [{ vendorNbr: "123", vendorName: "Vendor A", costAreaDesc: "Area A" }],
      excludedVendorForAllowance_rn: {
        divisionWiseExcludedVendors: {
          division1: { "123": { isExclude: true } },
        },
      },
    },
  });

  mockUseLeadDistributors.mockReturnValueOnce({
    selectedList: [],
    updateSelected: jest.fn(),
  });

  render(<VendorSelections currentDivision={{ divisionId: "division1" }} />);
  const checkbox = screen.getByRole("checkbox", { name: /Vendor A/i });
  const disabledDiv = document.querySelector("[disabledMove='true']");
  expect(disabledDiv).not.toBeNull();
  expect(disabledDiv).toHaveAttribute("disabledMove", "true");
 });

  it("calls updateSelected when a vendor is selected", () => {
    const mockUpdateSelected = jest.fn();

    mockUseSelectorWrap.mockReturnValueOnce({
      data: {
        leadOptions: [
          { vendorNbr: "123", vendorName: "Vendor A", costAreaDesc: "Area A" },
        ],
      },
    });

    mockUseLeadDistributors.mockReturnValueOnce({
      selectedList: [],
      updateSelected: mockUpdateSelected,
    });

    render(<VendorSelections currentDivision={{ divisionId: "division1" }} />);

    const checkbox = screen.getByLabelText(/Vendor A/);
    fireEvent.click(checkbox);

    expect(mockUpdateSelected).toHaveBeenCalledWith({ id: "123" });
  });

  it("renders child checkboxes for vendors with children", () => {
  mockUseSelectorWrap.mockReturnValueOnce({
    data: {
      leadOptions: [
        { vendorNbr: "123", vendorName: "Vendor A", costAreaDesc: "Area A" },
      ],
    },
  });

  mockUseLeadDistributors.mockReturnValueOnce({
    selectedList: ["123"],
    stepData: [{ id: "123", child: ["child1", "child2"] }],
    updateSelected: jest.fn(),
  });

  render(<VendorSelections currentDivision={{ divisionId: "division1" }} />);

  // Use a regular expression to match the text
  expect(screen.getByText(/child1/i)).toBeInTheDocument();
  expect(screen.getByText(/child2/i)).toBeInTheDocument();
  });
});