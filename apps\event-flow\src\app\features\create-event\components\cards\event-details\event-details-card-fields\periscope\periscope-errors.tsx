import React from "react";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import Alert from "@albertsons/uds/molecule/Alert";

function PeriscopeErrors({ formFields, displayPeriscopeErrors, labels }) {
  const PID_Message_Fields = ["DATES_NOT_MATCH", "ITEMS_NOT_MATCH"];
  const extraField = (
    <div style={{ lineHeight: "16px", fontWeight: 400 }}>
      {efConstants?.PERISCOPE_ERRORS.EXTRA_FIELD}
    </div>
  );
  return (
    formFields?.periscopeValid &&
    displayPeriscopeErrors?.map(element => {
      if (efConstants.PERISCOPE_ERRORS["INVALID_PID"] === element) return null;
      return (
        <Alert
          isOpen={true}
          variant={"warning"}
          className={`!justify-center w-full !z-[9] my-2 ${efConstants.componentClassName.RENDER_PERISCOPE_ERRORS}`}
        >
          <div style={{ lineHeight: "16px", fontWeight: 700 }}>{element}</div>
          {PID_Message_Fields?.some((message: string) =>
            labels?.includes(message)
          ) && extraField}
        </Alert>
      );
    })
  );
}

export default PeriscopeErrors;
