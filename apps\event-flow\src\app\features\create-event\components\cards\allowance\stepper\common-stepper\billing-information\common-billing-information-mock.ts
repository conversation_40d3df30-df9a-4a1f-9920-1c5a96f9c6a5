export const mock_allowance_temp_work = {
  allowanceData: {
    tempWorkAllowanceId: "6759d2060eda705c8c88bf0f",
    planEventId: "6759d1fe6299ee135d771c00",
    createUserId: "PJAIN03",
    lastUpdUserId: "PJAIN03",
    createTs: *************,
    lastUpdTs: *************,
    allowanceType: "SCAN",
    allowanceTypeSpecification: {
      scanAllow: {
        allowanceType: "SCAN",
        createInd: "TC",
        allowancePerformanceId: "63a3a12743a6cee87995b834",
        vehicleDatesMap: {
          DSD_WHSE_RETAIL_DIVISION: {
            vehicleId: "66f1eded1d792055ff84c761",
            vehicleRef: {
              vehicleId: "66f1eded1d792055ff84c761",
              vehicleNm: "27 Week 06 Insert 2025",
              sourceVehicleSk: 0,
              startDate: "2025-02-05",
              endDate: "2025-02-11",
              vehicleType: {
                vehicleTypeId: "66a3f061900b0b47a182376f",
                sourceVehicleTypeSk: 0,
                vehicleTypDesc: "Weekly Insert",
              },
            },
            dateRange: {
              startDate: "2025-02-05",
              endDate: "2025-02-11",
            },
          },
        },
        allowancesMap: {
          DSD_WHSE_RETAIL_DIVISION: [
            {
              allowanceIdNbr: 1,
              vendorNbr: "",
              costAreaDesc: "",
              defaultAllowanceDates: {
                allowanceStartDate: "2025-02-05",
                allowanceEndDate: "2025-02-11",
                performStartDate: "2025-02-05",
                performEndDate: "2025-02-11",
                orderStartDate: "0001-01-01",
                orderEndDate: "0001-01-01",
                shipStartDate: "0001-01-01",
                shipEndDate: "0001-01-01",
                arrivalStartDate: "0001-01-01",
                arrivalEndDate: "0001-01-01",
                overrideInd: false,
                notPreSaved: false,
              },
              allowanceStartDate: "2025-02-05",
              allowanceEndDate: "2025-02-11",
              performStartDate: "2025-02-05",
              performEndDate: "2025-02-11",
              orderStartDate: "2025-02-05",
              orderEndDate: "2025-02-11",
              shipStartDate: "2025-02-05",
              shipEndDate: "2025-02-11",
              arrivalStartDate: "2025-02-05",
              arrivalEndDate: "2025-02-11",
              vehicleId: "66f1eded1d792055ff84c761",
              createInd: "TC",
              locationId: "639033d538196056762e6e28",
              locationName: "27 - Seattle",
              distCenter: "DDSE",
              locationTypeCd: "D",
              allowanceBillingInfo: {
                vendorIds: [
                  {
                    vendorNbr: "006446",
                    vendorSubAccount: "001",
                    costArea: "4",
                    fullVendorNbr: "006446-001-4",
                  },
                  {
                    vendorNbr: "006446",
                    vendorSubAccount: "001",
                    costArea: "2",
                    fullVendorNbr: "006446-001-2",
                  },
                  {
                    vendorNbr: "006446",
                    vendorSubAccount: "001",
                    costArea: "1",
                    fullVendorNbr: "006446-001-1",
                  },
                ],
                absMerchVendor: "006446-001",
                absVendorName: "KEHE DISTRIBUTORS",
                absVendorPaymentType: "D",
                acPayableVendorNbr: "110056",
                acReceivableVendorNbr: "082099",
                billingContactName: "<EMAIL>     ",
                billingContactEmail: "<EMAIL>",
                vendorComment: "",
                vendorOfferTrackingNbr: "",
                vendorBillingList: [
                  {
                    billingContactName: "<EMAIL>     ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>        ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "BRANDON SWEET            ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>        ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>     ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>        ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "BRANDON SWEET            ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>        ",
                    billingContactEmail: "<EMAIL>",
                  },
                ],
                vendorItemCount: 8,
                vendorItemCountsSet: [
                  {
                    vendorDsdWhseId: {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "4",
                      vendorRank: "B.DSD",
                      fullVendorNbr: "006446-001-4",
                      valid: true,
                    },
                    itemIdSet: [
                      "2020113",
                      "2020143",
                      "2020197",
                      "2020393",
                      "2011373",
                      "2020118",
                      "2010289",
                      "2021027",
                    ],
                    vendorDsdWhseItemCount: 8,
                  },
                  {
                    vendorDsdWhseId: {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "2",
                      vendorRank: "B.DSD",
                      fullVendorNbr: "006446-001-2",
                      valid: true,
                    },
                    itemIdSet: [
                      "2020113",
                      "2020143",
                      "2020197",
                      "2020393",
                      "2011373",
                      "2020118",
                      "2010289",
                      "2021027",
                    ],
                    vendorDsdWhseItemCount: 8,
                  },
                  {
                    vendorDsdWhseId: {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "1",
                      vendorRank: "B.DSD",
                      fullVendorNbr: "006446-001-1",
                      valid: true,
                    },
                    itemIdSet: [
                      "2020113",
                      "2020143",
                      "2020197",
                      "2020393",
                      "2011373",
                      "2020118",
                      "2010289",
                      "2021027",
                    ],
                    vendorDsdWhseItemCount: 8,
                  },
                ],
                source: "SIMS_VENDOR",
                matched: "SIMS_ITEM_VENDOR",
              },
              allowanceBillingInfos: [
                {
                  vendorIds: [
                    {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "4",
                      fullVendorNbr: "006446-001-4",
                    },
                    {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "2",
                      fullVendorNbr: "006446-001-2",
                    },
                    {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "1",
                      fullVendorNbr: "006446-001-1",
                    },
                  ],
                  absMerchVendor: "006446-001",
                  absVendorName: "KEHE DISTRIBUTORS",
                  absVendorPaymentType: "D",
                  acPayableVendorNbr: "110056",
                  acReceivableVendorNbr: "082099",
                  billingContactName: "<EMAIL>     ",
                  billingContactEmail: "<EMAIL>",
                  vendorComment: "",
                  vendorOfferTrackingNbr: "",
                  vendorBillingList: [
                    {
                      billingContactName: "<EMAIL>     ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>        ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "BRANDON SWEET            ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>        ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>     ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>        ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "BRANDON SWEET            ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>        ",
                      billingContactEmail: "<EMAIL>",
                    },
                  ],
                  vendorItemCount: 8,
                  vendorItemCountsSet: [
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "006446",
                        vendorSubAccount: "001",
                        costArea: "4",
                        vendorRank: "B.DSD",
                        fullVendorNbr: "006446-001-4",
                        valid: true,
                      },
                      itemIdSet: [
                        "2020113",
                        "2020143",
                        "2020197",
                        "2020393",
                        "2011373",
                        "2020118",
                        "2010289",
                        "2021027",
                      ],
                      vendorDsdWhseItemCount: 8,
                    },
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "006446",
                        vendorSubAccount: "001",
                        costArea: "2",
                        vendorRank: "B.DSD",
                        fullVendorNbr: "006446-001-2",
                        valid: true,
                      },
                      itemIdSet: [
                        "2020113",
                        "2020143",
                        "2020197",
                        "2020393",
                        "2011373",
                        "2020118",
                        "2010289",
                        "2021027",
                      ],
                      vendorDsdWhseItemCount: 8,
                    },
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "006446",
                        vendorSubAccount: "001",
                        costArea: "1",
                        vendorRank: "B.DSD",
                        fullVendorNbr: "006446-001-1",
                        valid: true,
                      },
                      itemIdSet: [
                        "2020113",
                        "2020143",
                        "2020197",
                        "2020393",
                        "2011373",
                        "2020118",
                        "2010289",
                        "2021027",
                      ],
                      vendorDsdWhseItemCount: 8,
                    },
                  ],
                  source: "SIMS_VENDOR",
                  matched: "SIMS_ITEM_VENDOR",
                },
                {
                  vendorIds: [
                    {
                      vendorNbr: "006446",
                      vendorSubAccount: "013",
                      costArea: "1",
                      fullVendorNbr: "006446-013-1",
                    },
                  ],
                  absMerchVendor: "006446-013",
                  absVendorName: "KEHE DISTRIBUTORS {JBG WHLSALE}",
                  absVendorPaymentType: "I",
                  acPayableVendorNbr: "110056",
                  acReceivableVendorNbr: "      ",
                  vendorBillingList: [],
                  vendorItemCount: 8,
                  vendorItemCountsSet: [
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "006446",
                        vendorSubAccount: "013",
                        costArea: "1",
                        vendorRank: "B.DSD",
                        fullVendorNbr: "006446-013-1",
                        valid: true,
                      },
                      itemIdSet: [
                        "2020113",
                        "2020201",
                        "2020143",
                        "2020197",
                        "2020393",
                        "2011373",
                        "2020118",
                        "2021027",
                      ],
                      vendorDsdWhseItemCount: 8,
                    },
                  ],
                  source: "SIMS_VENDOR",
                },
                {
                  vendorIds: [
                    {
                      vendorNbr: "000756",
                      vendorSubAccount: "013",
                      distCenter: "WAUB",
                      fullVendorNbr: "000756-013",
                    },
                  ],
                  absMerchVendor: "000756-013",
                  absVendorName: "KELLANOVA",
                  absVendorPaymentType: "D",
                  acPayableVendorNbr: "002466",
                  acReceivableVendorNbr: "054415",
                  billingContactName: "DANIEL SANDOR            ",
                  billingContactEmail: "<EMAIL>",
                  vendorBillingList: [
                    {
                      billingContactName: "DANIEL SANDOR            ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "DANIEL SANDOR            ",
                      billingContactEmail: "<EMAIL>",
                    },
                  ],
                  vendorItemCount: 2,
                  vendorItemCountsSet: [
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "000756",
                        vendorSubAccount: "013",
                        distCenter: "WAUB",
                        vendorRank: "A.WHSE",
                        wimsSubVend: "2747",
                        fullVendorNbr: "000756-013-WAUB|2747",
                        valid: true,
                      },
                      itemIdSet: ["2021223", "2021190"],
                      vendorDsdWhseItemCount: 2,
                    },
                  ],
                  source: "SIMS_VENDOR",
                },
                {
                  vendorIds: [
                    {
                      vendorNbr: "000756",
                      vendorSubAccount: "022",
                      distCenter: "WANC",
                      fullVendorNbr: "000756-022",
                    },
                  ],
                  absMerchVendor: "000756-022",
                  absVendorName: "KELLANOVA",
                  absVendorPaymentType: "D",
                  acPayableVendorNbr: "002466",
                  acReceivableVendorNbr: "054415",
                  billingContactName: "ANGIE CURRY              ",
                  billingContactEmail: "<EMAIL>",
                  vendorBillingList: [
                    {
                      billingContactName: "ANGIE CURRY              ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "ANGIE CURRY              ",
                      billingContactEmail: "<EMAIL>",
                    },
                  ],
                  vendorItemCount: 2,
                  vendorItemCountsSet: [
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "000756",
                        vendorSubAccount: "022",
                        distCenter: "WANC",
                        vendorRank: "A.WHSE",
                        wimsSubVend: "2707",
                        fullVendorNbr: "000756-022-WANC|2707",
                        valid: true,
                      },
                      itemIdSet: ["2021223", "2021190"],
                      vendorDsdWhseItemCount: 2,
                    },
                  ],
                  source: "SIMS_VENDOR",
                },
              ],
              allowanceDateOffsets: {
                allowanceTypes: ["SCAN", "HEADER_FLAT", "ITEM_FLAT"],
                startDateOffset: 0,
                endDateOffset: 0,
                defaultOrderLeadTimeDays: 0,
                defaultShipTransitDays: 0,
                resolvedLeadTimeDays: 0,
                resolvedShipTransitDays: 0,
                vendorIdWimsSubVend: {
                  vendorId: {
                    vendorNbr: "000756",
                    vendorSubAccount: "022",
                    fullVendorNbr: "000756-022",
                  },
                  wimsSubVendNumber: "2707",
                  eventNegotiationSource: "SIMS_VENDOR",
                  wimsSubVend: {
                    wimSubVendor: "2707",
                    leadTime: 1,
                    leadTimedays: 7,
                    transitTimedays: 2,
                  },
                },
              },
              leadDistributorInfos: [],
              createAllowInd: true,
              allowanceItems: [
                {
                  itemId: "2010289",
                  itemDescription: "ANNIES BUNNY GRAHAMS CHOC CHIP          ",
                  primaryUpc: "************",
                  consumerUpc: "************",
                  caseUpc: "*************",
                  itemUpcs: ["*************", "************"],
                  consumerUpcs: [
                    {
                      upc: "************",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "************",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "************",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 3.77,
                    costAllow: 3.2867,
                    initialAllowAmt: 0,
                    newCostAllow: 3.2867,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 45.24,
                    costAllow: 39.44,
                    initialAllowAmt: 0,
                    newCostAllow: 39.44,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 45.24,
                    costAllow: 39.44,
                    initialAllowAmt: 0,
                    newCostAllow: 39.44,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2011373",
                  itemDescription: "ANNIES GRAHAM CRACKER SMORES ORG        ",
                  primaryUpc: "001356212213",
                  consumerUpc: "001356212213",
                  caseUpc: "0001356212213",
                  itemUpcs: ["0001356212213", "001356212213"],
                  consumerUpcs: [
                    {
                      upc: "001356212213",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356212213",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356212213",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 4.06,
                    costAllow: 3.5767,
                    initialAllowAmt: 0,
                    newCostAllow: 3.5767,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 48.72,
                    costAllow: 42.92,
                    initialAllowAmt: 0,
                    newCostAllow: 42.92,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 48.72,
                    costAllow: 42.92,
                    initialAllowAmt: 0,
                    newCostAllow: 42.92,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2020113",
                  itemDescription: "ANNIES BUNNY GRAHAMS HONEY              ",
                  primaryUpc: "001356200015",
                  consumerUpc: "001356200015",
                  caseUpc: "0001356200015",
                  itemUpcs: ["0001356200015", "001356200015"],
                  consumerUpcs: [
                    {
                      upc: "001356200015",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356200015",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356200015",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 3.79,
                    costAllow: 3.3067,
                    initialAllowAmt: 0,
                    newCostAllow: 3.3067,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 45.48,
                    costAllow: 39.68,
                    initialAllowAmt: 0,
                    newCostAllow: 39.68,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 45.48,
                    costAllow: 39.68,
                    initialAllowAmt: 0,
                    newCostAllow: 39.68,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2020118",
                  itemDescription: "ANNIES CHEDDAR BUNNIES ORIG             ",
                  primaryUpc: "001356230215",
                  consumerUpc: "001356230215",
                  caseUpc: "0001356230215",
                  itemUpcs: ["0001356230215", "001356230215"],
                  consumerUpcs: [
                    {
                      upc: "001356230215",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356230215",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356230215",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 3.77,
                    costAllow: 3.2867,
                    initialAllowAmt: 0,
                    newCostAllow: 3.2867,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 45.24,
                    costAllow: 39.44,
                    initialAllowAmt: 0,
                    newCostAllow: 39.44,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 45.24,
                    costAllow: 39.44,
                    initialAllowAmt: 0,
                    newCostAllow: 39.44,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2020143",
                  itemDescription: "ANNIES CRACKERS WHT CHDR SQR ORG SNCK   ",
                  primaryUpc: "001356249539",
                  consumerUpc: "001356249539",
                  caseUpc: "1001356249539",
                  itemUpcs: ["001356249539", "1001356249539"],
                  consumerUpcs: [
                    {
                      upc: "001356249539",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356249539",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356249539",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 4.06,
                    costAllow: 3.5767,
                    initialAllowAmt: 0,
                    newCostAllow: 3.5767,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 48.72,
                    costAllow: 42.92,
                    initialAllowAmt: 0,
                    newCostAllow: 42.92,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 48.72,
                    costAllow: 42.92,
                    initialAllowAmt: 0,
                    newCostAllow: 42.92,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2020197",
                  itemDescription: "ANNIES HIDDEN VEGGIES REALLY RANCH      ",
                  primaryUpc: "001356212205",
                  consumerUpc: "001356212205",
                  caseUpc: "1001356212205",
                  itemUpcs: ["001356212205", "1001356212205"],
                  consumerUpcs: [
                    {
                      upc: "001356212205",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356212205",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356212205",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 4.05,
                    costAllow: 3.5667,
                    initialAllowAmt: 0,
                    newCostAllow: 3.5667,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 48.6,
                    costAllow: 42.8,
                    initialAllowAmt: 0,
                    newCostAllow: 42.8,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 48.6,
                    costAllow: 42.8,
                    initialAllowAmt: 0,
                    newCostAllow: 42.8,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2020201",
                  itemDescription: "ANNIES BIRTHDAY CAKE BUNNY GRAHAMS      ",
                  primaryUpc: "001356210923",
                  consumerUpc: "001356210923",
                  caseUpc: "0001356210923",
                  itemUpcs: ["0001356210923", "001356210923"],
                  consumerUpcs: [
                    {
                      upc: "001356210923",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356210923",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 3.33,
                    costAllow: 2.8467,
                    initialAllowAmt: 0,
                    newCostAllow: 2.8467,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 39.96,
                    costAllow: 34.16,
                    initialAllowAmt: 0,
                    newCostAllow: 34.16,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 39.96,
                    costAllow: 34.16,
                    initialAllowAmt: 0,
                    newCostAllow: 34.16,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2020393",
                  itemDescription: "ANNIES CRACKER HOMEGROWN CHEDDAR SQUARES",
                  primaryUpc: "001356200053",
                  consumerUpc: "001356200053",
                  caseUpc: "*************",
                  itemUpcs: ["*************", "001356200053"],
                  consumerUpcs: [
                    {
                      upc: "001356200053",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356200053",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356200053",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 3.77,
                    costAllow: 3.2867,
                    initialAllowAmt: 0,
                    newCostAllow: 3.2867,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 45.24,
                    costAllow: 39.44,
                    initialAllowAmt: 0,
                    newCostAllow: 39.44,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 45.24,
                    costAllow: 39.44,
                    initialAllowAmt: 0,
                    newCostAllow: 39.44,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2021027",
                  itemDescription: "ANNIES CRACKERS CHEDDAR BUNNIES WHITE   ",
                  primaryUpc: "001356230228",
                  consumerUpc: "001356230228",
                  caseUpc: "0001356230228",
                  itemUpcs: ["0001356230228", "001356230228"],
                  consumerUpcs: [
                    {
                      upc: "001356230228",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356230228",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356230228",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 3.78,
                    costAllow: 3.2967,
                    initialAllowAmt: 0,
                    newCostAllow: 3.2967,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 45.36,
                    costAllow: 39.56,
                    initialAllowAmt: 0,
                    newCostAllow: 39.56,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 45.36,
                    costAllow: 39.56,
                    initialAllowAmt: 0,
                    newCostAllow: 39.56,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2021190",
                  itemDescription: "CHEEZ-IT SNAP'D CRACKERS CHEDDAR SOUR",
                  primaryUpc: "002410011487",
                  consumerUpc: "002410011487",
                  caseUpc: "0002410011486",
                  itemUpcs: ["0002410011486", "002410011487"],
                  consumerUpcs: [
                    {
                      upc: "002410011487",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "006",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "002410011487",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "006",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "002410011487",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "006",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 6,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 4.17,
                    costAllow: 4.1367,
                    initialAllowAmt: 0,
                    newCostAllow: 4.1367,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 25.02,
                    costAllow: 24.82,
                    initialAllowAmt: 0,
                    newCostAllow: 24.82,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 25.02,
                    costAllow: 24.82,
                    initialAllowAmt: 0,
                    newCostAllow: 24.82,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0333,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0.0333,
                      allowSum: 0.0333,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0.2,
                      allowSum: 0.2,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0.2,
                      allowSum: 0.2,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2021223",
                  itemDescription: "CHEEZ-IT SNAP'D CRKRS DBL CHSE",
                  primaryUpc: "002410011441",
                  consumerUpc: "002410011441",
                  caseUpc: "0002410011440",
                  itemUpcs: ["0002410011440", "002410011441"],
                  consumerUpcs: [
                    {
                      upc: "002410011441",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "006",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "002410011441",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "006",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "002410011441",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "006",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 6,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 4.17,
                    costAllow: 4.1367,
                    initialAllowAmt: 0,
                    newCostAllow: 4.1367,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 25.02,
                    costAllow: 24.82,
                    initialAllowAmt: 0,
                    newCostAllow: 24.82,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 25.02,
                    costAllow: 24.82,
                    initialAllowAmt: 0,
                    newCostAllow: 24.82,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0333,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0.0333,
                      allowSum: 0.0333,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0.2,
                      allowSum: 0.2,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0.2,
                      allowSum: 0.2,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
              ],
              headerFlatAmt: 0,
              allowanceStatus: "Draft",
              storeGroups: [],
              leadDistributorInd: false,
              includeInd: true,
              finalizedAmountsInd: true,
              dateBindMetrics: {
                group: "DATES",
                mode: "DATES_DEFAULT_BIND",
                bindValues: {
                  srcDates: {
                    allow: {
                      startDate: "2025-02-05",
                      endDate: "2025-02-11",
                    },
                    order: {
                      startDate: "2025-02-05",
                      endDate: "2025-02-11",
                    },
                    ship: {
                      startDate: "2025-02-05",
                      endDate: "2025-02-11",
                    },
                    arrival: {
                      startDate: "2025-02-05",
                      endDate: "2025-02-11",
                    },
                  },
                  dstDates: {
                    allow: {
                      startDate: "2025-02-05",
                      endDate: "2025-02-11",
                    },
                    order: {
                      startDate: "0001-01-01",
                      endDate: "0001-01-01",
                    },
                    ship: {
                      startDate: "0001-01-01",
                      endDate: "0001-01-01",
                    },
                    arrival: {
                      startDate: "0001-01-01",
                      endDate: "0001-01-01",
                    },
                  },
                },
                bind: true,
              },
              excludedItems: [],
              allowanceProcessStatus: "",
              allowDownstreamStatus: {},
            },
          ],
        },
        productSources: ["DSD", "WAREHOUSE"],
        allowancePerformances: {
          id: "63a3a12743a6cee87995b834",
          performance: "4U Event (52)",
          allowanceType: "Scan",
          allowanceCd: "T",
          perfCode1: "20",
          perfCode2: "52",
          performanceConfig: {
            defaultCreateInd: "TC",
            allowOnlyOverrideStoreGroupsInd: false,
            changeableOnEditInd: true,
          },
        },
        offerAllowancesGroupInfoMap: {},
      },
    },
  },
};

export const mock_allowance_form_data = {
  allowanceFormData: {
    "offerAllowances[0].allowances[0]": {
      allowanceTypeAndPerformance: {
        allowanceType: "SCAN",
        performance: "4U Event (52)",
        allowancePerformances: {
          allowanceCd: "T",
          id: "63a3a12743a6cee87995b834",
          perfCode1: "20",
          perfCode2: "52",
          performanceConfig: {
            defaultCreateInd: "TC",
            allowOnlyOverrideStoreGroupsInd: false,
            changeableOnEditInd: true,
          },
        },
        overrideHeaderFlatAmt: "0",
        allowancesMap: {
          DSD_WHSE_RETAIL_DIVISION: [],
        },
        createInd: "TC",
        productSources: [],
        perfChildInfo: [],
      },
      allowanceToBeCreated: {
        option: "One Allowance: Warehouse, DSD, or Combined",
        selection: "DSD_WHSE_RETAIL_DIVISION",
        offerAllowanceGroup: "DSD_WHSE_RETAIL_DIVISION",
        allowanceMap: {
          DSD_WHSE_RETAIL_DIVISION: [],
        },
        productSources: ["DSD", "WAREHOUSE"],
        offerAllowancesGroupInfoMap: {},
      },
      allowanceCreationVehicle: {
        DSD_WHSE_RETAIL_DIVISION: {
          vehicleId: "66f1eded1d792055ff84c761",
          vehicleRef: {
            vehicleId: "66f1eded1d792055ff84c761",
            vehicleNm: "27 Week 06 Insert 2025",
            sourceVehicleSk: 0,
            startDate: "2025-02-05",
            endDate: "2025-02-11",
            vehicleType: {
              vehicleTypeId: "66a3f061900b0b47a182376f",
              sourceVehicleTypeSk: 0,
              vehicleTypDesc: "Weekly Insert",
            },
          },
          dateRange: {
            startDate: "2025-02-05",
            endDate: "2025-02-11",
          },
        },
      },
      allowanceAmountsData: {
        DSD_WHSE_RETAIL_DIVISION: {
          allowanceAmount: "0.10",
          uom: "EA",
          allowanceItems: [
            {
              itemId: "2010289",
              itemDescription: "ANNIES BUNNY GRAHAMS CHOC CHIP          ",
              primaryUpc: "************",
              consumerUpc: "************",
              caseUpc: "*************",
              itemUpcs: ["*************", "************"],
              consumerUpcs: [
                {
                  upc: "************",
                  rog: "SSEA",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "************",
                  rog: "SSPK",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "************",
                  rog: "SACG",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
              ],
              effectiveStartDate: "0001-12-24",
              effectiveEndDate: "9999-01-01",
              allowanceType: "SCAN",
              packWhse: 12,
              ringType: 0,
              size: "7.5 OZ",
              vendorPackConversionFactor: 1,
              unitNetCosts: {
                netCostType: "UNIT",
                cost: 3.77,
                costAllow: 3.2867,
                initialAllowAmt: 0,
                newCostAllow: 3.2867,
              },
              masterCaseNetCosts: {
                netCostType: "MASTER_CASE",
                cost: 45.24,
                costAllow: 39.44,
                initialAllowAmt: 0,
                newCostAllow: 39.44,
              },
              shipCaseNetCosts: {
                netCostType: "SHIP_CASE",
                cost: 45.24,
                costAllow: 39.44,
                initialAllowAmt: 0,
                newCostAllow: 39.44,
              },
              allowanceAmount: 0.1,
              allowUomType: "EA",
              allowUomTypes: ["EA"],
              overlaps: {
                offerAllowAmounts: [
                  {
                    offer: 7019321,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019321,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019321,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019373,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7019373,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7019373,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7020036,
                    rog: "SACG",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSEA",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSPK",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7018224,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7018224,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7018224,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 1,
                  },
                ],
                unitizedOverlaps: {
                  netCostType: "UNIT",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 0.3,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 0.0833,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.0167,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 0.0833,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 0.4666,
                  shipAllow: 0.0167,
                  allowSum: 0.4833,
                },
                shipCaseOverlaps: {
                  netCostType: "SHIP_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 3.6,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 5.6,
                  shipAllow: 0.2,
                  allowSum: 5.8,
                },
                masterCaseOverlaps: {
                  netCostType: "MASTER_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 3.6,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 5.6,
                  shipAllow: 0.2,
                  allowSum: 5.8,
                },
              },
              bindMetrics: {
                group: "AMOUNTS",
                mode: "AMOUNTS_DEFAULT_BIND",
                bind: true,
              },
              modCommand: "NONE",
            },
            {
              itemId: "2011373",
              itemDescription: "ANNIES GRAHAM CRACKER SMORES ORG        ",
              primaryUpc: "001356212213",
              consumerUpc: "001356212213",
              caseUpc: "0001356212213",
              itemUpcs: ["0001356212213", "001356212213"],
              consumerUpcs: [
                {
                  upc: "001356212213",
                  rog: "SSPK",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "001356212213",
                  rog: "SSEA",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "001356212213",
                  rog: "SACG",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
              ],
              effectiveStartDate: "0001-12-24",
              effectiveEndDate: "9999-01-01",
              allowanceType: "SCAN",
              packWhse: 12,
              ringType: 0,
              size: "7.5 OZ",
              vendorPackConversionFactor: 1,
              unitNetCosts: {
                netCostType: "UNIT",
                cost: 4.06,
                costAllow: 3.5767,
                initialAllowAmt: 0,
                newCostAllow: 3.5767,
              },
              masterCaseNetCosts: {
                netCostType: "MASTER_CASE",
                cost: 48.72,
                costAllow: 42.92,
                initialAllowAmt: 0,
                newCostAllow: 42.92,
              },
              shipCaseNetCosts: {
                netCostType: "SHIP_CASE",
                cost: 48.72,
                costAllow: 42.92,
                initialAllowAmt: 0,
                newCostAllow: 42.92,
              },
              allowanceAmount: 0.1,
              allowUomType: "EA",
              allowUomTypes: ["EA"],
              overlaps: {
                offerAllowAmounts: [
                  {
                    offer: 7019321,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019321,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019321,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019373,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7019373,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7019373,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7020036,
                    rog: "SACG",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSEA",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSPK",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7018224,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7018224,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7018224,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 1,
                  },
                ],
                unitizedOverlaps: {
                  netCostType: "UNIT",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 0.3,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 0.0833,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.0167,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 0.0833,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 0.4666,
                  shipAllow: 0.0167,
                  allowSum: 0.4833,
                },
                shipCaseOverlaps: {
                  netCostType: "SHIP_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 3.6,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 5.6,
                  shipAllow: 0.2,
                  allowSum: 5.8,
                },
                masterCaseOverlaps: {
                  netCostType: "MASTER_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 3.6,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 5.6,
                  shipAllow: 0.2,
                  allowSum: 5.8,
                },
              },
              bindMetrics: {
                group: "AMOUNTS",
                mode: "AMOUNTS_DEFAULT_BIND",
                bind: true,
              },
              modCommand: "NONE",
            },
            {
              itemId: "2020113",
              itemDescription: "ANNIES BUNNY GRAHAMS HONEY              ",
              primaryUpc: "001356200015",
              consumerUpc: "001356200015",
              caseUpc: "0001356200015",
              itemUpcs: ["0001356200015", "001356200015"],
              consumerUpcs: [
                {
                  upc: "001356200015",
                  rog: "SACG",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "001356200015",
                  rog: "SSEA",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "001356200015",
                  rog: "SSPK",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
              ],
              effectiveStartDate: "0001-12-24",
              effectiveEndDate: "9999-01-01",
              allowanceType: "SCAN",
              packWhse: 12,
              ringType: 0,
              size: "7.5 OZ",
              vendorPackConversionFactor: 1,
              unitNetCosts: {
                netCostType: "UNIT",
                cost: 3.79,
                costAllow: 3.3067,
                initialAllowAmt: 0,
                newCostAllow: 3.3067,
              },
              masterCaseNetCosts: {
                netCostType: "MASTER_CASE",
                cost: 45.48,
                costAllow: 39.68,
                initialAllowAmt: 0,
                newCostAllow: 39.68,
              },
              shipCaseNetCosts: {
                netCostType: "SHIP_CASE",
                cost: 45.48,
                costAllow: 39.68,
                initialAllowAmt: 0,
                newCostAllow: 39.68,
              },
              allowanceAmount: 0.1,
              allowUomType: "EA",
              allowUomTypes: ["EA"],
              overlaps: {
                offerAllowAmounts: [
                  {
                    offer: 7019321,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019321,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019321,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019373,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7019373,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7019373,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7020036,
                    rog: "SACG",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSEA",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSPK",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7018224,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7018224,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7018224,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 1,
                  },
                ],
                unitizedOverlaps: {
                  netCostType: "UNIT",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 0.3,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 0.0833,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.0167,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 0.0833,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 0.4666,
                  shipAllow: 0.0167,
                  allowSum: 0.4833,
                },
                shipCaseOverlaps: {
                  netCostType: "SHIP_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 3.6,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 5.6,
                  shipAllow: 0.2,
                  allowSum: 5.8,
                },
                masterCaseOverlaps: {
                  netCostType: "MASTER_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 3.6,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 5.6,
                  shipAllow: 0.2,
                  allowSum: 5.8,
                },
              },
              bindMetrics: {
                group: "AMOUNTS",
                mode: "AMOUNTS_DEFAULT_BIND",
                bind: true,
              },
              modCommand: "NONE",
            },
            {
              itemId: "2020118",
              itemDescription: "ANNIES CHEDDAR BUNNIES ORIG             ",
              primaryUpc: "001356230215",
              consumerUpc: "001356230215",
              caseUpc: "0001356230215",
              itemUpcs: ["0001356230215", "001356230215"],
              consumerUpcs: [
                {
                  upc: "001356230215",
                  rog: "SACG",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "001356230215",
                  rog: "SSEA",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "001356230215",
                  rog: "SSPK",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
              ],
              effectiveStartDate: "0001-12-24",
              effectiveEndDate: "9999-01-01",
              allowanceType: "SCAN",
              packWhse: 12,
              ringType: 0,
              size: "7.5 OZ",
              vendorPackConversionFactor: 1,
              unitNetCosts: {
                netCostType: "UNIT",
                cost: 3.77,
                costAllow: 3.2867,
                initialAllowAmt: 0,
                newCostAllow: 3.2867,
              },
              masterCaseNetCosts: {
                netCostType: "MASTER_CASE",
                cost: 45.24,
                costAllow: 39.44,
                initialAllowAmt: 0,
                newCostAllow: 39.44,
              },
              shipCaseNetCosts: {
                netCostType: "SHIP_CASE",
                cost: 45.24,
                costAllow: 39.44,
                initialAllowAmt: 0,
                newCostAllow: 39.44,
              },
              allowanceAmount: 0.1,
              allowUomType: "EA",
              allowUomTypes: ["EA"],
              overlaps: {
                offerAllowAmounts: [
                  {
                    offer: 7019321,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019321,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019321,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019373,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7019373,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7019373,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7020036,
                    rog: "SACG",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSEA",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSPK",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7018224,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7018224,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7018224,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 1,
                  },
                ],
                unitizedOverlaps: {
                  netCostType: "UNIT",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 0.3,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 0.0833,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.0167,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 0.0833,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 0.4666,
                  shipAllow: 0.0167,
                  allowSum: 0.4833,
                },
                shipCaseOverlaps: {
                  netCostType: "SHIP_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 3.6,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 5.6,
                  shipAllow: 0.2,
                  allowSum: 5.8,
                },
                masterCaseOverlaps: {
                  netCostType: "MASTER_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 3.6,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 5.6,
                  shipAllow: 0.2,
                  allowSum: 5.8,
                },
              },
              bindMetrics: {
                group: "AMOUNTS",
                mode: "AMOUNTS_DEFAULT_BIND",
                bind: true,
              },
              modCommand: "NONE",
            },
            {
              itemId: "2020143",
              itemDescription: "ANNIES CRACKERS WHT CHDR SQR ORG SNCK   ",
              primaryUpc: "001356249539",
              consumerUpc: "001356249539",
              caseUpc: "1001356249539",
              itemUpcs: ["001356249539", "1001356249539"],
              consumerUpcs: [
                {
                  upc: "001356249539",
                  rog: "SACG",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "001356249539",
                  rog: "SSEA",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "001356249539",
                  rog: "SSPK",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
              ],
              effectiveStartDate: "0001-12-24",
              effectiveEndDate: "9999-01-01",
              allowanceType: "SCAN",
              packWhse: 12,
              ringType: 0,
              size: "7.5 OZ",
              vendorPackConversionFactor: 1,
              unitNetCosts: {
                netCostType: "UNIT",
                cost: 4.06,
                costAllow: 3.5767,
                initialAllowAmt: 0,
                newCostAllow: 3.5767,
              },
              masterCaseNetCosts: {
                netCostType: "MASTER_CASE",
                cost: 48.72,
                costAllow: 42.92,
                initialAllowAmt: 0,
                newCostAllow: 42.92,
              },
              shipCaseNetCosts: {
                netCostType: "SHIP_CASE",
                cost: 48.72,
                costAllow: 42.92,
                initialAllowAmt: 0,
                newCostAllow: 42.92,
              },
              allowanceAmount: 0.1,
              allowUomType: "EA",
              allowUomTypes: ["EA"],
              overlaps: {
                offerAllowAmounts: [
                  {
                    offer: 7019321,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019321,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019321,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019373,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7019373,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7019373,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7020036,
                    rog: "SACG",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSEA",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSPK",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7018224,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7018224,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7018224,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 1,
                  },
                ],
                unitizedOverlaps: {
                  netCostType: "UNIT",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 0.3,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 0.0833,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.0167,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 0.0833,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 0.4666,
                  shipAllow: 0.0167,
                  allowSum: 0.4833,
                },
                shipCaseOverlaps: {
                  netCostType: "SHIP_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 3.6,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 5.6,
                  shipAllow: 0.2,
                  allowSum: 5.8,
                },
                masterCaseOverlaps: {
                  netCostType: "MASTER_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 3.6,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 5.6,
                  shipAllow: 0.2,
                  allowSum: 5.8,
                },
              },
              bindMetrics: {
                group: "AMOUNTS",
                mode: "AMOUNTS_DEFAULT_BIND",
                bind: true,
              },
              modCommand: "NONE",
            },
            {
              itemId: "2020197",
              itemDescription: "ANNIES HIDDEN VEGGIES REALLY RANCH      ",
              primaryUpc: "001356212205",
              consumerUpc: "001356212205",
              caseUpc: "1001356212205",
              itemUpcs: ["001356212205", "1001356212205"],
              consumerUpcs: [
                {
                  upc: "001356212205",
                  rog: "SSEA",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "001356212205",
                  rog: "SACG",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "001356212205",
                  rog: "SSPK",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
              ],
              effectiveStartDate: "0001-12-24",
              effectiveEndDate: "9999-01-01",
              allowanceType: "SCAN",
              packWhse: 12,
              ringType: 0,
              size: "7.5 OZ",
              vendorPackConversionFactor: 1,
              unitNetCosts: {
                netCostType: "UNIT",
                cost: 4.05,
                costAllow: 3.5667,
                initialAllowAmt: 0,
                newCostAllow: 3.5667,
              },
              masterCaseNetCosts: {
                netCostType: "MASTER_CASE",
                cost: 48.6,
                costAllow: 42.8,
                initialAllowAmt: 0,
                newCostAllow: 42.8,
              },
              shipCaseNetCosts: {
                netCostType: "SHIP_CASE",
                cost: 48.6,
                costAllow: 42.8,
                initialAllowAmt: 0,
                newCostAllow: 42.8,
              },
              allowanceAmount: 0.1,
              allowUomType: "EA",
              allowUomTypes: ["EA"],
              overlaps: {
                offerAllowAmounts: [
                  {
                    offer: 7019321,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019321,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019321,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019373,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7019373,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7019373,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7020036,
                    rog: "SACG",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSEA",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSPK",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7018224,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7018224,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7018224,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 1,
                  },
                ],
                unitizedOverlaps: {
                  netCostType: "UNIT",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 0.3,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 0.0833,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.0167,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 0.0833,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 0.4666,
                  shipAllow: 0.0167,
                  allowSum: 0.4833,
                },
                shipCaseOverlaps: {
                  netCostType: "SHIP_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 3.6,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 5.6,
                  shipAllow: 0.2,
                  allowSum: 5.8,
                },
                masterCaseOverlaps: {
                  netCostType: "MASTER_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 3.6,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 5.6,
                  shipAllow: 0.2,
                  allowSum: 5.8,
                },
              },
              bindMetrics: {
                group: "AMOUNTS",
                mode: "AMOUNTS_DEFAULT_BIND",
                bind: true,
              },
              modCommand: "NONE",
            },
            {
              itemId: "2020201",
              itemDescription: "ANNIES BIRTHDAY CAKE BUNNY GRAHAMS      ",
              primaryUpc: "001356210923",
              consumerUpc: "001356210923",
              caseUpc: "0001356210923",
              itemUpcs: ["0001356210923", "001356210923"],
              consumerUpcs: [
                {
                  upc: "001356210923",
                  rog: "SACG",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "001356210923",
                  rog: "SSEA",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
              ],
              effectiveStartDate: "0001-12-24",
              effectiveEndDate: "9999-01-01",
              allowanceType: "SCAN",
              packWhse: 12,
              ringType: 0,
              size: "7.5 OZ",
              vendorPackConversionFactor: 1,
              unitNetCosts: {
                netCostType: "UNIT",
                cost: 3.33,
                costAllow: 2.8467,
                initialAllowAmt: 0,
                newCostAllow: 2.8467,
              },
              masterCaseNetCosts: {
                netCostType: "MASTER_CASE",
                cost: 39.96,
                costAllow: 34.16,
                initialAllowAmt: 0,
                newCostAllow: 34.16,
              },
              shipCaseNetCosts: {
                netCostType: "SHIP_CASE",
                cost: 39.96,
                costAllow: 34.16,
                initialAllowAmt: 0,
                newCostAllow: 34.16,
              },
              allowanceAmount: 0.1,
              allowUomType: "EA",
              allowUomTypes: ["EA"],
              overlaps: {
                offerAllowAmounts: [
                  {
                    offer: 7019321,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019321,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019321,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019373,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7019373,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7019373,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7020036,
                    rog: "SACG",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSEA",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSPK",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7018224,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7018224,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7018224,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 1,
                  },
                ],
                unitizedOverlaps: {
                  netCostType: "UNIT",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 0.3,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 0.0833,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.0167,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 0.0833,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 0.4666,
                  shipAllow: 0.0167,
                  allowSum: 0.4833,
                },
                shipCaseOverlaps: {
                  netCostType: "SHIP_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 3.6,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 5.6,
                  shipAllow: 0.2,
                  allowSum: 5.8,
                },
                masterCaseOverlaps: {
                  netCostType: "MASTER_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 3.6,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 5.6,
                  shipAllow: 0.2,
                  allowSum: 5.8,
                },
              },
              bindMetrics: {
                group: "AMOUNTS",
                mode: "AMOUNTS_DEFAULT_BIND",
                bind: true,
              },
              modCommand: "NONE",
            },
            {
              itemId: "2020393",
              itemDescription: "ANNIES CRACKER HOMEGROWN CHEDDAR SQUARES",
              primaryUpc: "001356200053",
              consumerUpc: "001356200053",
              caseUpc: "*************",
              itemUpcs: ["*************", "001356200053"],
              consumerUpcs: [
                {
                  upc: "001356200053",
                  rog: "SACG",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "001356200053",
                  rog: "SSPK",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "001356200053",
                  rog: "SSEA",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
              ],
              effectiveStartDate: "0001-12-24",
              effectiveEndDate: "9999-01-01",
              allowanceType: "SCAN",
              packWhse: 12,
              ringType: 0,
              size: "7.5 OZ",
              vendorPackConversionFactor: 1,
              unitNetCosts: {
                netCostType: "UNIT",
                cost: 3.77,
                costAllow: 3.2867,
                initialAllowAmt: 0,
                newCostAllow: 3.2867,
              },
              masterCaseNetCosts: {
                netCostType: "MASTER_CASE",
                cost: 45.24,
                costAllow: 39.44,
                initialAllowAmt: 0,
                newCostAllow: 39.44,
              },
              shipCaseNetCosts: {
                netCostType: "SHIP_CASE",
                cost: 45.24,
                costAllow: 39.44,
                initialAllowAmt: 0,
                newCostAllow: 39.44,
              },
              allowanceAmount: 0.1,
              allowUomType: "EA",
              allowUomTypes: ["EA"],
              overlaps: {
                offerAllowAmounts: [
                  {
                    offer: 7019321,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019321,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019321,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019373,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7019373,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7019373,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7020036,
                    rog: "SACG",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSEA",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSPK",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7018224,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7018224,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7018224,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 1,
                  },
                ],
                unitizedOverlaps: {
                  netCostType: "UNIT",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 0.3,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 0.0833,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.0167,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 0.0833,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 0.4666,
                  shipAllow: 0.0167,
                  allowSum: 0.4833,
                },
                shipCaseOverlaps: {
                  netCostType: "SHIP_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 3.6,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 5.6,
                  shipAllow: 0.2,
                  allowSum: 5.8,
                },
                masterCaseOverlaps: {
                  netCostType: "MASTER_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 3.6,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 5.6,
                  shipAllow: 0.2,
                  allowSum: 5.8,
                },
              },
              bindMetrics: {
                group: "AMOUNTS",
                mode: "AMOUNTS_DEFAULT_BIND",
                bind: true,
              },
              modCommand: "NONE",
            },
            {
              itemId: "2021027",
              itemDescription: "ANNIES CRACKERS CHEDDAR BUNNIES WHITE   ",
              primaryUpc: "001356230228",
              consumerUpc: "001356230228",
              caseUpc: "0001356230228",
              itemUpcs: ["0001356230228", "001356230228"],
              consumerUpcs: [
                {
                  upc: "001356230228",
                  rog: "SSEA",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "001356230228",
                  rog: "SACG",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "001356230228",
                  rog: "SSPK",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "012",
                  sizeDesc: "7.5 OZ ",
                },
              ],
              effectiveStartDate: "0001-12-24",
              effectiveEndDate: "9999-01-01",
              allowanceType: "SCAN",
              packWhse: 12,
              ringType: 0,
              size: "7.5 OZ",
              vendorPackConversionFactor: 1,
              unitNetCosts: {
                netCostType: "UNIT",
                cost: 3.78,
                costAllow: 3.2967,
                initialAllowAmt: 0,
                newCostAllow: 3.2967,
              },
              masterCaseNetCosts: {
                netCostType: "MASTER_CASE",
                cost: 45.36,
                costAllow: 39.56,
                initialAllowAmt: 0,
                newCostAllow: 39.56,
              },
              shipCaseNetCosts: {
                netCostType: "SHIP_CASE",
                cost: 45.36,
                costAllow: 39.56,
                initialAllowAmt: 0,
                newCostAllow: 39.56,
              },
              allowanceAmount: 0.1,
              allowUomType: "EA",
              allowUomTypes: ["EA"],
              overlaps: {
                offerAllowAmounts: [
                  {
                    offer: 7019321,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019321,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019321,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 3.6,
                  },
                  {
                    offer: 7019373,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7019373,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7019373,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7020036,
                    rog: "SACG",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSEA",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSPK",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7018224,
                    rog: "SACG",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7018224,
                    rog: "SSEA",
                    basisDsc: "C",
                    amount: 1,
                  },
                  {
                    offer: 7018224,
                    rog: "SSPK",
                    basisDsc: "C",
                    amount: 1,
                  },
                ],
                unitizedOverlaps: {
                  netCostType: "UNIT",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 0.3,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 0.0833,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.0167,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 0.0833,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 0.4666,
                  shipAllow: 0.0167,
                  allowSum: 0.4833,
                },
                shipCaseOverlaps: {
                  netCostType: "SHIP_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 3.6,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 5.6,
                  shipAllow: 0.2,
                  allowSum: 5.8,
                },
                masterCaseOverlaps: {
                  netCostType: "MASTER_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7019321,
                      basisDsc: "C",
                      amount: 3.6,
                      convertedAmount: 3.6,
                    },
                    {
                      offer: 7019373,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                    {
                      offer: 7018224,
                      basisDsc: "C",
                      amount: 1,
                      convertedAmount: 1,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 5.6,
                  shipAllow: 0.2,
                  allowSum: 5.8,
                },
              },
              bindMetrics: {
                group: "AMOUNTS",
                mode: "AMOUNTS_DEFAULT_BIND",
                bind: true,
              },
              modCommand: "NONE",
            },
            {
              itemId: "2021190",
              itemDescription: "CHEEZ-IT SNAP'D CRACKERS CHEDDAR SOUR",
              primaryUpc: "002410011487",
              consumerUpc: "002410011487",
              caseUpc: "0002410011486",
              itemUpcs: ["0002410011486", "002410011487"],
              consumerUpcs: [
                {
                  upc: "002410011487",
                  rog: "SACG",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "006",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "002410011487",
                  rog: "SSPK",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "006",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "002410011487",
                  rog: "SSEA",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "006",
                  sizeDesc: "7.5 OZ ",
                },
              ],
              effectiveStartDate: "0001-12-24",
              effectiveEndDate: "9999-01-01",
              allowanceType: "SCAN",
              packWhse: 6,
              ringType: 0,
              size: "7.5 OZ",
              vendorPackConversionFactor: 1,
              unitNetCosts: {
                netCostType: "UNIT",
                cost: 4.17,
                costAllow: 4.1367,
                initialAllowAmt: 0,
                newCostAllow: 4.1367,
              },
              masterCaseNetCosts: {
                netCostType: "MASTER_CASE",
                cost: 25.02,
                costAllow: 24.82,
                initialAllowAmt: 0,
                newCostAllow: 24.82,
              },
              shipCaseNetCosts: {
                netCostType: "SHIP_CASE",
                cost: 25.02,
                costAllow: 24.82,
                initialAllowAmt: 0,
                newCostAllow: 24.82,
              },
              allowanceAmount: 0.1,
              allowUomType: "EA",
              allowUomTypes: ["EA"],
              overlaps: {
                offerAllowAmounts: [
                  {
                    offer: 7020036,
                    rog: "SACG",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSEA",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSPK",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                ],
                unitizedOverlaps: {
                  netCostType: "UNIT",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.0333,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 0,
                  shipAllow: 0.0333,
                  allowSum: 0.0333,
                },
                shipCaseOverlaps: {
                  netCostType: "SHIP_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 0,
                  shipAllow: 0.2,
                  allowSum: 0.2,
                },
                masterCaseOverlaps: {
                  netCostType: "MASTER_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 0,
                  shipAllow: 0.2,
                  allowSum: 0.2,
                },
              },
              bindMetrics: {
                group: "AMOUNTS",
                mode: "AMOUNTS_DEFAULT_BIND",
                bind: true,
              },
              modCommand: "NONE",
            },
            {
              itemId: "2021223",
              itemDescription: "CHEEZ-IT SNAP'D CRKRS DBL CHSE",
              primaryUpc: "002410011441",
              consumerUpc: "002410011441",
              caseUpc: "0002410011440",
              itemUpcs: ["0002410011440", "002410011441"],
              consumerUpcs: [
                {
                  upc: "002410011441",
                  rog: "SACG",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "006",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "002410011441",
                  rog: "SSEA",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "006",
                  sizeDesc: "7.5 OZ ",
                },
                {
                  upc: "002410011441",
                  rog: "SSPK",
                  primaryInd: true,
                  labelSize: "M",
                  packDesc: "006",
                  sizeDesc: "7.5 OZ ",
                },
              ],
              effectiveStartDate: "0001-12-24",
              effectiveEndDate: "9999-01-01",
              allowanceType: "SCAN",
              packWhse: 6,
              ringType: 0,
              size: "7.5 OZ",
              vendorPackConversionFactor: 1,
              unitNetCosts: {
                netCostType: "UNIT",
                cost: 4.17,
                costAllow: 4.1367,
                initialAllowAmt: 0,
                newCostAllow: 4.1367,
              },
              masterCaseNetCosts: {
                netCostType: "MASTER_CASE",
                cost: 25.02,
                costAllow: 24.82,
                initialAllowAmt: 0,
                newCostAllow: 24.82,
              },
              shipCaseNetCosts: {
                netCostType: "SHIP_CASE",
                cost: 25.02,
                costAllow: 24.82,
                initialAllowAmt: 0,
                newCostAllow: 24.82,
              },
              allowanceAmount: 0.1,
              allowUomType: "EA",
              allowUomTypes: ["EA"],
              overlaps: {
                offerAllowAmounts: [
                  {
                    offer: 7020036,
                    rog: "SACG",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSEA",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                  {
                    offer: 7020036,
                    rog: "SSPK",
                    basisDsc: "S",
                    amount: 0.2,
                  },
                ],
                unitizedOverlaps: {
                  netCostType: "UNIT",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.0333,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 0,
                  shipAllow: 0.0333,
                  allowSum: 0.0333,
                },
                shipCaseOverlaps: {
                  netCostType: "SHIP_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 0,
                  shipAllow: 0.2,
                  allowSum: 0.2,
                },
                masterCaseOverlaps: {
                  netCostType: "MASTER_CASE",
                  convertedAllowanceAmounts: [
                    {
                      offer: 7020036,
                      basisDsc: "S",
                      amount: 0.2,
                      convertedAmount: 0.2,
                    },
                  ],
                  scanAllow: 0,
                  caseAllow: 0,
                  shipAllow: 0.2,
                  allowSum: 0.2,
                },
              },
              bindMetrics: {
                group: "AMOUNTS",
                mode: "AMOUNTS_DEFAULT_BIND",
                bind: true,
              },
              modCommand: "NONE",
            },
          ],
          allowances: [
            {
              allowanceIdNbr: 1,
              vendorNbr: "",
              costAreaDesc: "",
              defaultAllowanceDates: {
                allowanceStartDate: "2025-02-05",
                allowanceEndDate: "2025-02-11",
                performStartDate: "2025-02-05",
                performEndDate: "2025-02-11",
                orderStartDate: "0001-01-01",
                orderEndDate: "0001-01-01",
                shipStartDate: "0001-01-01",
                shipEndDate: "0001-01-01",
                arrivalStartDate: "0001-01-01",
                arrivalEndDate: "0001-01-01",
                overrideInd: false,
                notPreSaved: false,
              },
              allowanceStartDate: "2025-02-05",
              allowanceEndDate: "2025-02-11",
              performStartDate: "2025-02-05",
              performEndDate: "2025-02-11",
              orderStartDate: "2025-02-05",
              orderEndDate: "2025-02-11",
              shipStartDate: "2025-02-05",
              shipEndDate: "2025-02-11",
              arrivalStartDate: "2025-02-05",
              arrivalEndDate: "2025-02-11",
              vehicleId: "66f1eded1d792055ff84c761",
              createInd: "TC",
              locationId: "639033d538196056762e6e28",
              locationName: "27 - Seattle",
              distCenter: "DDSE",
              locationTypeCd: "D",
              allowanceBillingInfo: {
                vendorIds: [
                  {
                    vendorNbr: "006446",
                    vendorSubAccount: "001",
                    costArea: "4",
                    fullVendorNbr: "006446-001-4",
                  },
                  {
                    vendorNbr: "006446",
                    vendorSubAccount: "001",
                    costArea: "2",
                    fullVendorNbr: "006446-001-2",
                  },
                  {
                    vendorNbr: "006446",
                    vendorSubAccount: "001",
                    costArea: "1",
                    fullVendorNbr: "006446-001-1",
                  },
                ],
                absMerchVendor: "006446-001",
                absVendorName: "KEHE DISTRIBUTORS",
                absVendorPaymentType: "D",
                acPayableVendorNbr: "110056",
                acReceivableVendorNbr: "082099",
                billingContactName: "<EMAIL>     ",
                billingContactEmail: "<EMAIL>",
                vendorComment: "",
                vendorOfferTrackingNbr: "",
                vendorBillingList: [
                  {
                    billingContactName: "<EMAIL>     ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>        ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "BRANDON SWEET            ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>        ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>     ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>        ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "BRANDON SWEET            ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>        ",
                    billingContactEmail: "<EMAIL>",
                  },
                ],
                vendorItemCount: 8,
                vendorItemCountsSet: [
                  {
                    vendorDsdWhseId: {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "4",
                      vendorRank: "B.DSD",
                      fullVendorNbr: "006446-001-4",
                      valid: true,
                    },
                    itemIdSet: [
                      "2020113",
                      "2020143",
                      "2020197",
                      "2020393",
                      "2011373",
                      "2020118",
                      "2010289",
                      "2021027",
                    ],
                    vendorDsdWhseItemCount: 8,
                  },
                  {
                    vendorDsdWhseId: {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "2",
                      vendorRank: "B.DSD",
                      fullVendorNbr: "006446-001-2",
                      valid: true,
                    },
                    itemIdSet: [
                      "2020113",
                      "2020143",
                      "2020197",
                      "2020393",
                      "2011373",
                      "2020118",
                      "2010289",
                      "2021027",
                    ],
                    vendorDsdWhseItemCount: 8,
                  },
                  {
                    vendorDsdWhseId: {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "1",
                      vendorRank: "B.DSD",
                      fullVendorNbr: "006446-001-1",
                      valid: true,
                    },
                    itemIdSet: [
                      "2020113",
                      "2020143",
                      "2020197",
                      "2020393",
                      "2011373",
                      "2020118",
                      "2010289",
                      "2021027",
                    ],
                    vendorDsdWhseItemCount: 8,
                  },
                ],
                source: "SIMS_VENDOR",
                matched: "SIMS_ITEM_VENDOR",
              },
              allowanceBillingInfos: [
                {
                  vendorIds: [
                    {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "4",
                      fullVendorNbr: "006446-001-4",
                    },
                    {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "2",
                      fullVendorNbr: "006446-001-2",
                    },
                    {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "1",
                      fullVendorNbr: "006446-001-1",
                    },
                  ],
                  absMerchVendor: "006446-001",
                  absVendorName: "KEHE DISTRIBUTORS",
                  absVendorPaymentType: "D",
                  acPayableVendorNbr: "110056",
                  acReceivableVendorNbr: "082099",
                  billingContactName: "<EMAIL>     ",
                  billingContactEmail: "<EMAIL>",
                  vendorComment: "",
                  vendorOfferTrackingNbr: "",
                  vendorBillingList: [
                    {
                      billingContactName: "<EMAIL>     ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>        ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "BRANDON SWEET            ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>        ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>     ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>        ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "BRANDON SWEET            ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>        ",
                      billingContactEmail: "<EMAIL>",
                    },
                  ],
                  vendorItemCount: 8,
                  vendorItemCountsSet: [
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "006446",
                        vendorSubAccount: "001",
                        costArea: "4",
                        vendorRank: "B.DSD",
                        fullVendorNbr: "006446-001-4",
                        valid: true,
                      },
                      itemIdSet: [
                        "2020113",
                        "2020143",
                        "2020197",
                        "2020393",
                        "2011373",
                        "2020118",
                        "2010289",
                        "2021027",
                      ],
                      vendorDsdWhseItemCount: 8,
                    },
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "006446",
                        vendorSubAccount: "001",
                        costArea: "2",
                        vendorRank: "B.DSD",
                        fullVendorNbr: "006446-001-2",
                        valid: true,
                      },
                      itemIdSet: [
                        "2020113",
                        "2020143",
                        "2020197",
                        "2020393",
                        "2011373",
                        "2020118",
                        "2010289",
                        "2021027",
                      ],
                      vendorDsdWhseItemCount: 8,
                    },
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "006446",
                        vendorSubAccount: "001",
                        costArea: "1",
                        vendorRank: "B.DSD",
                        fullVendorNbr: "006446-001-1",
                        valid: true,
                      },
                      itemIdSet: [
                        "2020113",
                        "2020143",
                        "2020197",
                        "2020393",
                        "2011373",
                        "2020118",
                        "2010289",
                        "2021027",
                      ],
                      vendorDsdWhseItemCount: 8,
                    },
                  ],
                  source: "SIMS_VENDOR",
                  matched: "SIMS_ITEM_VENDOR",
                },
                {
                  vendorIds: [
                    {
                      vendorNbr: "006446",
                      vendorSubAccount: "013",
                      costArea: "1",
                      fullVendorNbr: "006446-013-1",
                    },
                  ],
                  absMerchVendor: "006446-013",
                  absVendorName: "KEHE DISTRIBUTORS {JBG WHLSALE}",
                  absVendorPaymentType: "I",
                  acPayableVendorNbr: "110056",
                  acReceivableVendorNbr: "      ",
                  vendorBillingList: [],
                  vendorItemCount: 8,
                  vendorItemCountsSet: [
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "006446",
                        vendorSubAccount: "013",
                        costArea: "1",
                        vendorRank: "B.DSD",
                        fullVendorNbr: "006446-013-1",
                        valid: true,
                      },
                      itemIdSet: [
                        "2020113",
                        "2020201",
                        "2020143",
                        "2020197",
                        "2020393",
                        "2011373",
                        "2020118",
                        "2021027",
                      ],
                      vendorDsdWhseItemCount: 8,
                    },
                  ],
                  source: "SIMS_VENDOR",
                },
                {
                  vendorIds: [
                    {
                      vendorNbr: "000756",
                      vendorSubAccount: "013",
                      distCenter: "WAUB",
                      fullVendorNbr: "000756-013",
                    },
                  ],
                  absMerchVendor: "000756-013",
                  absVendorName: "KELLANOVA",
                  absVendorPaymentType: "D",
                  acPayableVendorNbr: "002466",
                  acReceivableVendorNbr: "054415",
                  billingContactName: "DANIEL SANDOR            ",
                  billingContactEmail: "<EMAIL>",
                  vendorBillingList: [
                    {
                      billingContactName: "DANIEL SANDOR            ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "DANIEL SANDOR            ",
                      billingContactEmail: "<EMAIL>",
                    },
                  ],
                  vendorItemCount: 2,
                  vendorItemCountsSet: [
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "000756",
                        vendorSubAccount: "013",
                        distCenter: "WAUB",
                        vendorRank: "A.WHSE",
                        wimsSubVend: "2747",
                        fullVendorNbr: "000756-013-WAUB|2747",
                        valid: true,
                      },
                      itemIdSet: ["2021223", "2021190"],
                      vendorDsdWhseItemCount: 2,
                    },
                  ],
                  source: "SIMS_VENDOR",
                },
                {
                  vendorIds: [
                    {
                      vendorNbr: "000756",
                      vendorSubAccount: "022",
                      distCenter: "WANC",
                      fullVendorNbr: "000756-022",
                    },
                  ],
                  absMerchVendor: "000756-022",
                  absVendorName: "KELLANOVA",
                  absVendorPaymentType: "D",
                  acPayableVendorNbr: "002466",
                  acReceivableVendorNbr: "054415",
                  billingContactName: "ANGIE CURRY              ",
                  billingContactEmail: "<EMAIL>",
                  vendorBillingList: [
                    {
                      billingContactName: "ANGIE CURRY              ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "ANGIE CURRY              ",
                      billingContactEmail: "<EMAIL>",
                    },
                  ],
                  vendorItemCount: 2,
                  vendorItemCountsSet: [
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "000756",
                        vendorSubAccount: "022",
                        distCenter: "WANC",
                        vendorRank: "A.WHSE",
                        wimsSubVend: "2707",
                        fullVendorNbr: "000756-022-WANC|2707",
                        valid: true,
                      },
                      itemIdSet: ["2021223", "2021190"],
                      vendorDsdWhseItemCount: 2,
                    },
                  ],
                  source: "SIMS_VENDOR",
                },
              ],
              allowanceDateOffsets: {
                allowanceTypes: ["SCAN", "HEADER_FLAT", "ITEM_FLAT"],
                startDateOffset: 0,
                endDateOffset: 0,
                defaultOrderLeadTimeDays: 0,
                defaultShipTransitDays: 0,
                resolvedLeadTimeDays: 0,
                resolvedShipTransitDays: 0,
                vendorIdWimsSubVend: {
                  vendorId: {
                    vendorNbr: "000756",
                    vendorSubAccount: "022",
                    fullVendorNbr: "000756-022",
                  },
                  wimsSubVendNumber: "2707",
                  eventNegotiationSource: "SIMS_VENDOR",
                  wimsSubVend: {
                    wimSubVendor: "2707",
                    leadTime: 1,
                    leadTimedays: 7,
                    transitTimedays: 2,
                  },
                },
              },
              leadDistributorInfos: [],
              createAllowInd: true,
              allowanceItems: [
                {
                  itemId: "2010289",
                  itemDescription: "ANNIES BUNNY GRAHAMS CHOC CHIP          ",
                  primaryUpc: "************",
                  consumerUpc: "************",
                  caseUpc: "*************",
                  itemUpcs: ["*************", "************"],
                  consumerUpcs: [
                    {
                      upc: "************",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "************",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "************",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 3.77,
                    costAllow: 3.2867,
                    initialAllowAmt: 0,
                    newCostAllow: 3.2867,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 45.24,
                    costAllow: 39.44,
                    initialAllowAmt: 0,
                    newCostAllow: 39.44,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 45.24,
                    costAllow: 39.44,
                    initialAllowAmt: 0,
                    newCostAllow: 39.44,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2011373",
                  itemDescription: "ANNIES GRAHAM CRACKER SMORES ORG        ",
                  primaryUpc: "001356212213",
                  consumerUpc: "001356212213",
                  caseUpc: "0001356212213",
                  itemUpcs: ["0001356212213", "001356212213"],
                  consumerUpcs: [
                    {
                      upc: "001356212213",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356212213",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356212213",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 4.06,
                    costAllow: 3.5767,
                    initialAllowAmt: 0,
                    newCostAllow: 3.5767,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 48.72,
                    costAllow: 42.92,
                    initialAllowAmt: 0,
                    newCostAllow: 42.92,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 48.72,
                    costAllow: 42.92,
                    initialAllowAmt: 0,
                    newCostAllow: 42.92,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2020113",
                  itemDescription: "ANNIES BUNNY GRAHAMS HONEY              ",
                  primaryUpc: "001356200015",
                  consumerUpc: "001356200015",
                  caseUpc: "0001356200015",
                  itemUpcs: ["0001356200015", "001356200015"],
                  consumerUpcs: [
                    {
                      upc: "001356200015",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356200015",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356200015",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 3.79,
                    costAllow: 3.3067,
                    initialAllowAmt: 0,
                    newCostAllow: 3.3067,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 45.48,
                    costAllow: 39.68,
                    initialAllowAmt: 0,
                    newCostAllow: 39.68,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 45.48,
                    costAllow: 39.68,
                    initialAllowAmt: 0,
                    newCostAllow: 39.68,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2020118",
                  itemDescription: "ANNIES CHEDDAR BUNNIES ORIG             ",
                  primaryUpc: "001356230215",
                  consumerUpc: "001356230215",
                  caseUpc: "0001356230215",
                  itemUpcs: ["0001356230215", "001356230215"],
                  consumerUpcs: [
                    {
                      upc: "001356230215",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356230215",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356230215",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 3.77,
                    costAllow: 3.2867,
                    initialAllowAmt: 0,
                    newCostAllow: 3.2867,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 45.24,
                    costAllow: 39.44,
                    initialAllowAmt: 0,
                    newCostAllow: 39.44,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 45.24,
                    costAllow: 39.44,
                    initialAllowAmt: 0,
                    newCostAllow: 39.44,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2020143",
                  itemDescription: "ANNIES CRACKERS WHT CHDR SQR ORG SNCK   ",
                  primaryUpc: "001356249539",
                  consumerUpc: "001356249539",
                  caseUpc: "1001356249539",
                  itemUpcs: ["001356249539", "1001356249539"],
                  consumerUpcs: [
                    {
                      upc: "001356249539",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356249539",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356249539",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 4.06,
                    costAllow: 3.5767,
                    initialAllowAmt: 0,
                    newCostAllow: 3.5767,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 48.72,
                    costAllow: 42.92,
                    initialAllowAmt: 0,
                    newCostAllow: 42.92,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 48.72,
                    costAllow: 42.92,
                    initialAllowAmt: 0,
                    newCostAllow: 42.92,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2020197",
                  itemDescription: "ANNIES HIDDEN VEGGIES REALLY RANCH      ",
                  primaryUpc: "001356212205",
                  consumerUpc: "001356212205",
                  caseUpc: "1001356212205",
                  itemUpcs: ["001356212205", "1001356212205"],
                  consumerUpcs: [
                    {
                      upc: "001356212205",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356212205",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356212205",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 4.05,
                    costAllow: 3.5667,
                    initialAllowAmt: 0,
                    newCostAllow: 3.5667,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 48.6,
                    costAllow: 42.8,
                    initialAllowAmt: 0,
                    newCostAllow: 42.8,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 48.6,
                    costAllow: 42.8,
                    initialAllowAmt: 0,
                    newCostAllow: 42.8,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2020201",
                  itemDescription: "ANNIES BIRTHDAY CAKE BUNNY GRAHAMS      ",
                  primaryUpc: "001356210923",
                  consumerUpc: "001356210923",
                  caseUpc: "0001356210923",
                  itemUpcs: ["0001356210923", "001356210923"],
                  consumerUpcs: [
                    {
                      upc: "001356210923",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356210923",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 3.33,
                    costAllow: 2.8467,
                    initialAllowAmt: 0,
                    newCostAllow: 2.8467,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 39.96,
                    costAllow: 34.16,
                    initialAllowAmt: 0,
                    newCostAllow: 34.16,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 39.96,
                    costAllow: 34.16,
                    initialAllowAmt: 0,
                    newCostAllow: 34.16,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2020393",
                  itemDescription: "ANNIES CRACKER HOMEGROWN CHEDDAR SQUARES",
                  primaryUpc: "001356200053",
                  consumerUpc: "001356200053",
                  caseUpc: "*************",
                  itemUpcs: ["*************", "001356200053"],
                  consumerUpcs: [
                    {
                      upc: "001356200053",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356200053",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356200053",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 3.77,
                    costAllow: 3.2867,
                    initialAllowAmt: 0,
                    newCostAllow: 3.2867,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 45.24,
                    costAllow: 39.44,
                    initialAllowAmt: 0,
                    newCostAllow: 39.44,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 45.24,
                    costAllow: 39.44,
                    initialAllowAmt: 0,
                    newCostAllow: 39.44,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2021027",
                  itemDescription: "ANNIES CRACKERS CHEDDAR BUNNIES WHITE   ",
                  primaryUpc: "001356230228",
                  consumerUpc: "001356230228",
                  caseUpc: "0001356230228",
                  itemUpcs: ["0001356230228", "001356230228"],
                  consumerUpcs: [
                    {
                      upc: "001356230228",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356230228",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356230228",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 3.78,
                    costAllow: 3.2967,
                    initialAllowAmt: 0,
                    newCostAllow: 3.2967,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 45.36,
                    costAllow: 39.56,
                    initialAllowAmt: 0,
                    newCostAllow: 39.56,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 45.36,
                    costAllow: 39.56,
                    initialAllowAmt: 0,
                    newCostAllow: 39.56,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2021190",
                  itemDescription: "CHEEZ-IT SNAP'D CRACKERS CHEDDAR SOUR",
                  primaryUpc: "002410011487",
                  consumerUpc: "002410011487",
                  caseUpc: "0002410011486",
                  itemUpcs: ["0002410011486", "002410011487"],
                  consumerUpcs: [
                    {
                      upc: "002410011487",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "006",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "002410011487",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "006",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "002410011487",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "006",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 6,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 4.17,
                    costAllow: 4.1367,
                    initialAllowAmt: 0,
                    newCostAllow: 4.1367,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 25.02,
                    costAllow: 24.82,
                    initialAllowAmt: 0,
                    newCostAllow: 24.82,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 25.02,
                    costAllow: 24.82,
                    initialAllowAmt: 0,
                    newCostAllow: 24.82,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0333,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0.0333,
                      allowSum: 0.0333,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0.2,
                      allowSum: 0.2,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0.2,
                      allowSum: 0.2,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2021223",
                  itemDescription: "CHEEZ-IT SNAP'D CRKRS DBL CHSE",
                  primaryUpc: "002410011441",
                  consumerUpc: "002410011441",
                  caseUpc: "0002410011440",
                  itemUpcs: ["0002410011440", "002410011441"],
                  consumerUpcs: [
                    {
                      upc: "002410011441",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "006",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "002410011441",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "006",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "002410011441",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "006",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 6,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 4.17,
                    costAllow: 4.1367,
                    initialAllowAmt: 0,
                    newCostAllow: 4.1367,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 25.02,
                    costAllow: 24.82,
                    initialAllowAmt: 0,
                    newCostAllow: 24.82,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 25.02,
                    costAllow: 24.82,
                    initialAllowAmt: 0,
                    newCostAllow: 24.82,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0333,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0.0333,
                      allowSum: 0.0333,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0.2,
                      allowSum: 0.2,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0.2,
                      allowSum: 0.2,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
              ],
              headerFlatAmt: 0,
              allowanceStatus: "Draft",
              storeGroups: [],
              leadDistributorInd: false,
              includeInd: true,
              finalizedAmountsInd: true,
              dateBindMetrics: {
                group: "DATES",
                mode: "DATES_DEFAULT_BIND",
                bindValues: {
                  srcDates: {
                    allow: {
                      startDate: "2025-02-05",
                      endDate: "2025-02-11",
                    },
                    order: {
                      startDate: "2025-02-05",
                      endDate: "2025-02-11",
                    },
                    ship: {
                      startDate: "2025-02-05",
                      endDate: "2025-02-11",
                    },
                    arrival: {
                      startDate: "2025-02-05",
                      endDate: "2025-02-11",
                    },
                  },
                  dstDates: {
                    allow: {
                      startDate: "2025-02-05",
                      endDate: "2025-02-11",
                    },
                    order: {
                      startDate: "0001-01-01",
                      endDate: "0001-01-01",
                    },
                    ship: {
                      startDate: "0001-01-01",
                      endDate: "0001-01-01",
                    },
                    arrival: {
                      startDate: "0001-01-01",
                      endDate: "0001-01-01",
                    },
                  },
                },
                bind: true,
              },
              excludedItems: [],
              allowanceProcessStatus: "",
              allowDownstreamStatus: {},
            },
          ],
        },
      },
    },
  },
};
export const mock_event_Data = {
  name: "Annies CHEEZ - 84882 and 164938 - 27 Week 06 Insert 2025",
  startDate: "2025-02-05",
  endDate: "2025-02-11",
  divisionIds: ["27"],
  divisions: [],
  planProductGroups: [
    {
      planProductGroupId: "66736bbbf0b6311e80e0b7ce",
      sourceProductGroupId: 9184779,
      productGroupType: "PPG",
      name: "Annies CHEEZ - 84882 and 164938",
      divisionId: "27",
      smicGroupCode: 2,
      smicCategoryCode: "0210",
      supplier: {
        supplierId: "515",
        supplierName: "GENERAL MILLS",
      },
      itemCount: 11,
      displayInd: false,
      simsVendors: ["000756", "006446"],
      simsSubAccntVendors: [
        "000756-013",
        "000756-022",
        "006446-001",
        "006446-013",
      ],
      negotiationSimsVendors: ["000756", "006446"],
      unitType: 1,
      promoProductGroupId: 1081817322,
      smicDescription: "CRACKERS",
      sect: "338",
      sectName: "SNACKS",
      dept: "301",
      deptName: "GROCERY",
      dsM: "JONATHAN HUNTER",
      cicCount: 389,
      cigCount: 53,
      dvp: "DENNIS SCHWARZ",
      dasm: "NICK WELLIVER",
      nvp: "",
      ncd: "",
      nacm: "",
    },
  ],
  storeGroups: [
    {
      storeGroupId: "6614614a5996dcc293a6b7bd",
      sourceStoreGroupId: "EDM",
      storeGroupName: "Seattle All Stores",
      storeGroupType: {
        groupType: "S",
        storeGrpTypeName: "Division",
        groupInd: "D",
      },
      divisionIds: ["27"],
      storeCount: 221,
      storeGrpNbr: 1713327680,
    },
  ],
  storeGroupType: "",
  allowance: [],
  isEventChanged: false,
  id: "6759d1fe6299ee135d771c00",
  forecast: {
    forecastSales: 0,
    forecastUnits: 0,
    quadrant: "",
  },
  eventCreationVehicle: {
    vehicleId: "66f1eded1d792055ff84c761",
    vehicleNm: "27 Week 06 Insert 2025",
    sourceVehicleSk: 64748,
    startDate: "2025-02-05",
    endDate: "2025-02-11",
    vehicleType: {
      vehicleTypeId: "66a3f061900b0b47a182376f",
      sourceVehicleTypeSk: 198,
      vehicleTypNm: "insrt",
      vehicleTypDesc: "Weekly Insert",
    },
  },
  planProductGroupPricing: {
    planProductGroup: {
      planProductGroupId: "",
      sourceProductGroupId: "",
      name: "",
      divisionId: "",
      smicGroupCode: "",
      smicCategoryCode: "",
      supplier: {
        id: "",
        supplierId: "",
        supplierName: "",
      },
    },
    quadrant: "",
    priceAmount: "",
    priceFactor: "",
    priceUnit: "",
    priceLimitQty: "",
    forecastAmt: "",
    userName: "",
    supplier: "",
  },
  startWeekVehicle: "27 Week 06 Insert 2025",
  planStoreGroupType: {
    groupType: "S",
    storeGrpTypeName: "Division",
    groupInd: "D",
  },
  vehicleType: "Weekly Insert",
  eventStatus: "Draft",
  eventType: "DP",
  inValidPromotions: [],
  inValidAllowances: [],
  isChangeEventTypeVisible: false,
  planEventIdNbr: 10030744,
  sourceEventType: "ECP",
  pricing: [
    {
      planProductGroup: {
        planProductGroupId: "66736bbbf0b6311e80e0b7ce",
        sourceProductGroupId: 9184779,
        productGroupType: "PPG",
        name: "Annies CHEEZ - 84882 and 164938",
        divisionId: "27",
        smicGroupCode: 2,
        smicCategoryCode: "0210",
        supplier: {
          supplierId: "515",
          supplierName: "GENERAL MILLS",
        },
        itemCount: 11,
        displayInd: false,
        simsVendors: ["000756", "006446"],
        simsSubAccntVendors: [
          "000756-013",
          "000756-022",
          "006446-001",
          "006446-013",
        ],
        negotiationSimsVendors: ["000756", "006446"],
        unitType: 1,
        promoProductGroupId: 1081817322,
        smicDescription: "CRACKERS",
        sect: "338",
        sectName: "SNACKS",
        dept: "301",
        deptName: "GROCERY",
        dsM: "JONATHAN HUNTER",
        cicCount: 389,
        cigCount: 53,
        dvp: "DENNIS SCHWARZ",
        dasm: "NICK WELLIVER",
        nvp: "",
        ncd: "",
        nacm: "",
      },
      quadrant: "",
      priceAmount: "",
      priceFactor: "",
      priceUnit: "",
      priceLimitQty: "",
      prcMtd: "",
      promoType: "",
    },
  ],
  offerAllowances: [],
  promotionsList: [],
  createUser: {
    userId: "PJAIN03",
    name: "Prayas Jain (Contractor)",
    type: "Merchant",
    userRoles: ["az-meupp-nonprod-promointeditor"],
    createTs: "2024-12-11T17:55:10.517Z",
  },
  updateUser: {
    userId: "PJAIN03",
    name: "Prayas Jain (Contractor)",
    type: "Merchant",
    userRoles: ["az-meupp-nonprod-promointeditor"],
    createTs: "2024-12-11T17:55:10.517Z",
  },
  planEventWorkFlowType: "NOT FOUND",
  simsVendors: ["000756", "006446"],
  manufacturerSimsVendors: [],
  negotiationSimsVendors: ["000756", "006446"],
  eventNegotiationVendor: {
    source: "NONE",
    vendorIds: [],
    eventNegotiationUsers: {
      merchantUser: {
        userId: "PJAIN03",
        name: "Prayas Jain (Contractor)",
        type: "Merchant",
        userRoles: ["az-meupp-nonprod-promointeditor"],
        createTs: "2024-12-11T17:55:10.523Z",
      },
      vendorUserContacts: [],
    },
    userVendorNumbers: [],
  },
  simsVendorList: [
    {
      id: "64dbf5a223a9672237a8387e",
      supplierId: "000756",
      supplierName: "KELLOGGS CO",
      vendorNumSubAccount: "000756-013",
      vendorSubAccountNumber: "013",
      vendorSubAccountName: "KELLOGGS CO                             ",
    },
    {
      id: "64dbf5a223a9672237a83892",
      supplierId: "000756",
      supplierName: "KELLOGGS CO",
      vendorNumSubAccount: "000756-022",
      vendorSubAccountNumber: "022",
      vendorSubAccountName: "KELLOGGS CO                             ",
    },
    {
      id: "64dbf5a723a9672237a86a2e",
      supplierId: "006446",
      supplierName: "KEHE DISTRIBUTORS",
      vendorNumSubAccount: "006446-001",
      vendorSubAccountNumber: "001",
      vendorSubAccountName: "KEHE DISTRIBUTORS                       ",
    },
    {
      id: "64dd0e2b2db5059ed7715d7e",
      supplierId: "006446",
      supplierName: "KEHE DISTRIBUTORS",
      vendorNumSubAccount: "006446-013",
      vendorSubAccountNumber: "013",
      vendorSubAccountName: "KEHE DISTRIBUTORS {JBG WHLSALE}         ",
    },
  ],
  planEventTasks: [],
  submitForOthersInd: false,
  otherDetailsChangedInd: false,
  pidDetailsEventInd: false,
  eventTypeEnum: "DP",
  promotionsLists: [],
  promoProductGroup: "Annies CHEEZ - 84882 and 164938",
  storeGroupName: "Seattle All Stores",
};

export const mockAllowTempWrk = {
  allowanceData: {
    tempWorkAllowanceId: "6759d2060eda705c8c88bf0f",
    planEventId: "6759d1fe6299ee135d771c00",
    createUserId: "PJAIN03",
    lastUpdUserId: "PJAIN03",
    createTs: *************,
    lastUpdTs: *************,
    allowanceType: "SCAN",
    allowanceTypeSpecification: {
      scanAllow: {
        allowanceType: "SCAN",
        createInd: "TC",
        allowancePerformanceId: "63a3a12743a6cee87995b834",
        vehicleDatesMap: {
          DSD_WHSE_RETAIL_DIVISION: {
            vehicleId: "66f1eded1d792055ff84c761",
            vehicleRef: {
              vehicleId: "66f1eded1d792055ff84c761",
              vehicleNm: "27 Week 06 Insert 2025",
              sourceVehicleSk: 0,
              startDate: "2025-02-05",
              endDate: "2025-02-11",
              vehicleType: {
                vehicleTypeId: "66a3f061900b0b47a182376f",
                sourceVehicleTypeSk: 0,
                vehicleTypDesc: "Weekly Insert",
              },
            },
            dateRange: {
              startDate: "2025-02-05",
              endDate: "2025-02-11",
            },
          },
        },
        allowancesMap: {
          DSD_WHSE_RETAIL_DIVISION: [
            {
              allowanceIdNbr: 1,
              vendorNbr: "",
              costAreaDesc: "",
              defaultAllowanceDates: {
                allowanceStartDate: "2025-02-05",
                allowanceEndDate: "2025-02-11",
                performStartDate: "2025-02-05",
                performEndDate: "2025-02-11",
                orderStartDate: "0001-01-01",
                orderEndDate: "0001-01-01",
                shipStartDate: "0001-01-01",
                shipEndDate: "0001-01-01",
                arrivalStartDate: "0001-01-01",
                arrivalEndDate: "0001-01-01",
                overrideInd: false,
                notPreSaved: false,
              },
              allowanceStartDate: "2025-02-05",
              allowanceEndDate: "2025-02-11",
              performStartDate: "2025-02-05",
              performEndDate: "2025-02-11",
              orderStartDate: "2025-02-05",
              orderEndDate: "2025-02-11",
              shipStartDate: "2025-02-05",
              shipEndDate: "2025-02-11",
              arrivalStartDate: "2025-02-05",
              arrivalEndDate: "2025-02-11",
              vehicleId: "66f1eded1d792055ff84c761",
              createInd: "TC",
              locationId: "639033d538196056762e6e28",
              locationName: "27 - Seattle",
              distCenter: "DDSE",
              locationTypeCd: "D",
              allowanceBillingInfo: {
                vendorIds: [
                  {
                    vendorNbr: "006446",
                    vendorSubAccount: "001",
                    costArea: "4",
                    fullVendorNbr: "006446-001-4",
                  },
                  {
                    vendorNbr: "006446",
                    vendorSubAccount: "001",
                    costArea: "2",
                    fullVendorNbr: "006446-001-2",
                  },
                  {
                    vendorNbr: "006446",
                    vendorSubAccount: "001",
                    costArea: "1",
                    fullVendorNbr: "006446-001-1",
                  },
                ],
                absMerchVendor: "006446-001",
                absVendorName: "KEHE DISTRIBUTORS",
                absVendorPaymentType: "D",
                acPayableVendorNbr: "110056",
                acReceivableVendorNbr: "082099",
                billingContactName: "<EMAIL>     ",
                billingContactEmail: "<EMAIL>",
                vendorComment: "",
                vendorOfferTrackingNbr: "",
                vendorBillingList: [
                  {
                    billingContactName: "<EMAIL>     ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>        ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "BRANDON SWEET            ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>        ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>     ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>        ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "BRANDON SWEET            ",
                    billingContactEmail: "<EMAIL>",
                  },
                  {
                    billingContactName: "<EMAIL>        ",
                    billingContactEmail: "<EMAIL>",
                  },
                ],
                vendorItemCount: 8,
                vendorItemCountsSet: [
                  {
                    vendorDsdWhseId: {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "4",
                      vendorRank: "B.DSD",
                      fullVendorNbr: "006446-001-4",
                      valid: true,
                    },
                    itemIdSet: [
                      "2020113",
                      "2020143",
                      "2020197",
                      "2020393",
                      "2011373",
                      "2020118",
                      "2010289",
                      "2021027",
                    ],
                    vendorDsdWhseItemCount: 8,
                  },
                  {
                    vendorDsdWhseId: {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "2",
                      vendorRank: "B.DSD",
                      fullVendorNbr: "006446-001-2",
                      valid: true,
                    },
                    itemIdSet: [
                      "2020113",
                      "2020143",
                      "2020197",
                      "2020393",
                      "2011373",
                      "2020118",
                      "2010289",
                      "2021027",
                    ],
                    vendorDsdWhseItemCount: 8,
                  },
                  {
                    vendorDsdWhseId: {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "1",
                      vendorRank: "B.DSD",
                      fullVendorNbr: "006446-001-1",
                      valid: true,
                    },
                    itemIdSet: [
                      "2020113",
                      "2020143",
                      "2020197",
                      "2020393",
                      "2011373",
                      "2020118",
                      "2010289",
                      "2021027",
                    ],
                    vendorDsdWhseItemCount: 8,
                  },
                ],
                source: "SIMS_VENDOR",
                matched: "SIMS_ITEM_VENDOR",
              },
              allowanceBillingInfos: [
                {
                  vendorIds: [
                    {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "4",
                      fullVendorNbr: "006446-001-4",
                    },
                    {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "2",
                      fullVendorNbr: "006446-001-2",
                    },
                    {
                      vendorNbr: "006446",
                      vendorSubAccount: "001",
                      costArea: "1",
                      fullVendorNbr: "006446-001-1",
                    },
                  ],
                  absMerchVendor: "006446-001",
                  absVendorName: "KEHE DISTRIBUTORS",
                  absVendorPaymentType: "D",
                  acPayableVendorNbr: "110056",
                  acReceivableVendorNbr: "082099",
                  billingContactName: "<EMAIL>     ",
                  billingContactEmail: "<EMAIL>",
                  vendorComment: "",
                  vendorOfferTrackingNbr: "",
                  vendorBillingList: [
                    {
                      billingContactName: "<EMAIL>     ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>        ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "BRANDON SWEET            ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>        ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>     ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>        ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "BRANDON SWEET            ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "<EMAIL>        ",
                      billingContactEmail: "<EMAIL>",
                    },
                  ],
                  vendorItemCount: 8,
                  vendorItemCountsSet: [
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "006446",
                        vendorSubAccount: "001",
                        costArea: "4",
                        vendorRank: "B.DSD",
                        fullVendorNbr: "006446-001-4",
                        valid: true,
                      },
                      itemIdSet: [
                        "2020113",
                        "2020143",
                        "2020197",
                        "2020393",
                        "2011373",
                        "2020118",
                        "2010289",
                        "2021027",
                      ],
                      vendorDsdWhseItemCount: 8,
                    },
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "006446",
                        vendorSubAccount: "001",
                        costArea: "2",
                        vendorRank: "B.DSD",
                        fullVendorNbr: "006446-001-2",
                        valid: true,
                      },
                      itemIdSet: [
                        "2020113",
                        "2020143",
                        "2020197",
                        "2020393",
                        "2011373",
                        "2020118",
                        "2010289",
                        "2021027",
                      ],
                      vendorDsdWhseItemCount: 8,
                    },
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "006446",
                        vendorSubAccount: "001",
                        costArea: "1",
                        vendorRank: "B.DSD",
                        fullVendorNbr: "006446-001-1",
                        valid: true,
                      },
                      itemIdSet: [
                        "2020113",
                        "2020143",
                        "2020197",
                        "2020393",
                        "2011373",
                        "2020118",
                        "2010289",
                        "2021027",
                      ],
                      vendorDsdWhseItemCount: 8,
                    },
                  ],
                  source: "SIMS_VENDOR",
                  matched: "SIMS_ITEM_VENDOR",
                },
                {
                  vendorIds: [
                    {
                      vendorNbr: "006446",
                      vendorSubAccount: "013",
                      costArea: "1",
                      fullVendorNbr: "006446-013-1",
                    },
                  ],
                  absMerchVendor: "006446-013",
                  absVendorName: "KEHE DISTRIBUTORS {JBG WHLSALE}",
                  absVendorPaymentType: "I",
                  acPayableVendorNbr: "110056",
                  acReceivableVendorNbr: "      ",
                  vendorBillingList: [],
                  vendorItemCount: 8,
                  vendorItemCountsSet: [
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "006446",
                        vendorSubAccount: "013",
                        costArea: "1",
                        vendorRank: "B.DSD",
                        fullVendorNbr: "006446-013-1",
                        valid: true,
                      },
                      itemIdSet: [
                        "2020113",
                        "2020201",
                        "2020143",
                        "2020197",
                        "2020393",
                        "2011373",
                        "2020118",
                        "2021027",
                      ],
                      vendorDsdWhseItemCount: 8,
                    },
                  ],
                  source: "SIMS_VENDOR",
                },
                {
                  vendorIds: [
                    {
                      vendorNbr: "000756",
                      vendorSubAccount: "013",
                      distCenter: "WAUB",
                      fullVendorNbr: "000756-013",
                    },
                  ],
                  absMerchVendor: "000756-013",
                  absVendorName: "KELLANOVA",
                  absVendorPaymentType: "D",
                  acPayableVendorNbr: "002466",
                  acReceivableVendorNbr: "054415",
                  billingContactName: "DANIEL SANDOR            ",
                  billingContactEmail: "<EMAIL>",
                  vendorBillingList: [
                    {
                      billingContactName: "DANIEL SANDOR            ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "DANIEL SANDOR            ",
                      billingContactEmail: "<EMAIL>",
                    },
                  ],
                  vendorItemCount: 2,
                  vendorItemCountsSet: [
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "000756",
                        vendorSubAccount: "013",
                        distCenter: "WAUB",
                        vendorRank: "A.WHSE",
                        wimsSubVend: "2747",
                        fullVendorNbr: "000756-013-WAUB|2747",
                        valid: true,
                      },
                      itemIdSet: ["2021223", "2021190"],
                      vendorDsdWhseItemCount: 2,
                    },
                  ],
                  source: "SIMS_VENDOR",
                },
                {
                  vendorIds: [
                    {
                      vendorNbr: "000756",
                      vendorSubAccount: "022",
                      distCenter: "WANC",
                      fullVendorNbr: "000756-022",
                    },
                  ],
                  absMerchVendor: "000756-022",
                  absVendorName: "KELLANOVA",
                  absVendorPaymentType: "D",
                  acPayableVendorNbr: "002466",
                  acReceivableVendorNbr: "054415",
                  billingContactName: "ANGIE CURRY              ",
                  billingContactEmail: "<EMAIL>",
                  vendorBillingList: [
                    {
                      billingContactName: "ANGIE CURRY              ",
                      billingContactEmail: "<EMAIL>",
                    },
                    {
                      billingContactName: "ANGIE CURRY              ",
                      billingContactEmail: "<EMAIL>",
                    },
                  ],
                  vendorItemCount: 2,
                  vendorItemCountsSet: [
                    {
                      vendorDsdWhseId: {
                        vendorNbr: "000756",
                        vendorSubAccount: "022",
                        distCenter: "WANC",
                        vendorRank: "A.WHSE",
                        wimsSubVend: "2707",
                        fullVendorNbr: "000756-022-WANC|2707",
                        valid: true,
                      },
                      itemIdSet: ["2021223", "2021190"],
                      vendorDsdWhseItemCount: 2,
                    },
                  ],
                  source: "SIMS_VENDOR",
                },
              ],
              allowanceDateOffsets: {
                allowanceTypes: ["SCAN", "HEADER_FLAT", "ITEM_FLAT"],
                startDateOffset: 0,
                endDateOffset: 0,
                defaultOrderLeadTimeDays: 0,
                defaultShipTransitDays: 0,
                resolvedLeadTimeDays: 0,
                resolvedShipTransitDays: 0,
                vendorIdWimsSubVend: {
                  vendorId: {
                    vendorNbr: "000756",
                    vendorSubAccount: "022",
                    fullVendorNbr: "000756-022",
                  },
                  wimsSubVendNumber: "2707",
                  eventNegotiationSource: "SIMS_VENDOR",
                  wimsSubVend: {
                    wimSubVendor: "2707",
                    leadTime: 1,
                    leadTimedays: 7,
                    transitTimedays: 2,
                  },
                },
              },
              leadDistributorInfos: [],
              createAllowInd: true,
              allowanceItems: [
                {
                  itemId: "2010289",
                  itemDescription: "ANNIES BUNNY GRAHAMS CHOC CHIP          ",
                  primaryUpc: "************",
                  consumerUpc: "************",
                  caseUpc: "*************",
                  itemUpcs: ["*************", "************"],
                  consumerUpcs: [
                    {
                      upc: "************",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "************",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "************",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 3.77,
                    costAllow: 3.2867,
                    initialAllowAmt: 0,
                    newCostAllow: 3.2867,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 45.24,
                    costAllow: 39.44,
                    initialAllowAmt: 0,
                    newCostAllow: 39.44,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 45.24,
                    costAllow: 39.44,
                    initialAllowAmt: 0,
                    newCostAllow: 39.44,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2011373",
                  itemDescription: "ANNIES GRAHAM CRACKER SMORES ORG        ",
                  primaryUpc: "001356212213",
                  consumerUpc: "001356212213",
                  caseUpc: "0001356212213",
                  itemUpcs: ["0001356212213", "001356212213"],
                  consumerUpcs: [
                    {
                      upc: "001356212213",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356212213",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356212213",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 4.06,
                    costAllow: 3.5767,
                    initialAllowAmt: 0,
                    newCostAllow: 3.5767,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 48.72,
                    costAllow: 42.92,
                    initialAllowAmt: 0,
                    newCostAllow: 42.92,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 48.72,
                    costAllow: 42.92,
                    initialAllowAmt: 0,
                    newCostAllow: 42.92,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2020113",
                  itemDescription: "ANNIES BUNNY GRAHAMS HONEY              ",
                  primaryUpc: "001356200015",
                  consumerUpc: "001356200015",
                  caseUpc: "0001356200015",
                  itemUpcs: ["0001356200015", "001356200015"],
                  consumerUpcs: [
                    {
                      upc: "001356200015",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356200015",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356200015",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 3.79,
                    costAllow: 3.3067,
                    initialAllowAmt: 0,
                    newCostAllow: 3.3067,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 45.48,
                    costAllow: 39.68,
                    initialAllowAmt: 0,
                    newCostAllow: 39.68,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 45.48,
                    costAllow: 39.68,
                    initialAllowAmt: 0,
                    newCostAllow: 39.68,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2020118",
                  itemDescription: "ANNIES CHEDDAR BUNNIES ORIG             ",
                  primaryUpc: "001356230215",
                  consumerUpc: "001356230215",
                  caseUpc: "0001356230215",
                  itemUpcs: ["0001356230215", "001356230215"],
                  consumerUpcs: [
                    {
                      upc: "001356230215",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356230215",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356230215",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 3.77,
                    costAllow: 3.2867,
                    initialAllowAmt: 0,
                    newCostAllow: 3.2867,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 45.24,
                    costAllow: 39.44,
                    initialAllowAmt: 0,
                    newCostAllow: 39.44,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 45.24,
                    costAllow: 39.44,
                    initialAllowAmt: 0,
                    newCostAllow: 39.44,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2020143",
                  itemDescription: "ANNIES CRACKERS WHT CHDR SQR ORG SNCK   ",
                  primaryUpc: "001356249539",
                  consumerUpc: "001356249539",
                  caseUpc: "1001356249539",
                  itemUpcs: ["001356249539", "1001356249539"],
                  consumerUpcs: [
                    {
                      upc: "001356249539",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356249539",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356249539",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 4.06,
                    costAllow: 3.5767,
                    initialAllowAmt: 0,
                    newCostAllow: 3.5767,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 48.72,
                    costAllow: 42.92,
                    initialAllowAmt: 0,
                    newCostAllow: 42.92,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 48.72,
                    costAllow: 42.92,
                    initialAllowAmt: 0,
                    newCostAllow: 42.92,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2020197",
                  itemDescription: "ANNIES HIDDEN VEGGIES REALLY RANCH      ",
                  primaryUpc: "001356212205",
                  consumerUpc: "001356212205",
                  caseUpc: "1001356212205",
                  itemUpcs: ["001356212205", "1001356212205"],
                  consumerUpcs: [
                    {
                      upc: "001356212205",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356212205",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356212205",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 4.05,
                    costAllow: 3.5667,
                    initialAllowAmt: 0,
                    newCostAllow: 3.5667,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 48.6,
                    costAllow: 42.8,
                    initialAllowAmt: 0,
                    newCostAllow: 42.8,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 48.6,
                    costAllow: 42.8,
                    initialAllowAmt: 0,
                    newCostAllow: 42.8,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2020201",
                  itemDescription: "ANNIES BIRTHDAY CAKE BUNNY GRAHAMS      ",
                  primaryUpc: "001356210923",
                  consumerUpc: "001356210923",
                  caseUpc: "0001356210923",
                  itemUpcs: ["0001356210923", "001356210923"],
                  consumerUpcs: [
                    {
                      upc: "001356210923",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356210923",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 3.33,
                    costAllow: 2.8467,
                    initialAllowAmt: 0,
                    newCostAllow: 2.8467,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 39.96,
                    costAllow: 34.16,
                    initialAllowAmt: 0,
                    newCostAllow: 34.16,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 39.96,
                    costAllow: 34.16,
                    initialAllowAmt: 0,
                    newCostAllow: 34.16,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2020393",
                  itemDescription: "ANNIES CRACKER HOMEGROWN CHEDDAR SQUARES",
                  primaryUpc: "001356200053",
                  consumerUpc: "001356200053",
                  caseUpc: "*************",
                  itemUpcs: ["*************", "001356200053"],
                  consumerUpcs: [
                    {
                      upc: "001356200053",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356200053",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356200053",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 3.77,
                    costAllow: 3.2867,
                    initialAllowAmt: 0,
                    newCostAllow: 3.2867,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 45.24,
                    costAllow: 39.44,
                    initialAllowAmt: 0,
                    newCostAllow: 39.44,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 45.24,
                    costAllow: 39.44,
                    initialAllowAmt: 0,
                    newCostAllow: 39.44,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2021027",
                  itemDescription: "ANNIES CRACKERS CHEDDAR BUNNIES WHITE   ",
                  primaryUpc: "001356230228",
                  consumerUpc: "001356230228",
                  caseUpc: "0001356230228",
                  itemUpcs: ["0001356230228", "001356230228"],
                  consumerUpcs: [
                    {
                      upc: "001356230228",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356230228",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "001356230228",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "012",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 12,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 3.78,
                    costAllow: 3.2967,
                    initialAllowAmt: 0,
                    newCostAllow: 3.2967,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 45.36,
                    costAllow: 39.56,
                    initialAllowAmt: 0,
                    newCostAllow: 39.56,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 45.36,
                    costAllow: 39.56,
                    initialAllowAmt: 0,
                    newCostAllow: 39.56,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7019321,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019321,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 3.6,
                      },
                      {
                        offer: 7019373,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7019373,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7018224,
                        rog: "SACG",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSEA",
                        basisDsc: "C",
                        amount: 1,
                      },
                      {
                        offer: 7018224,
                        rog: "SSPK",
                        basisDsc: "C",
                        amount: 1,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 0.3,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0167,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 0.0833,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0.4666,
                      shipAllow: 0.0167,
                      allowSum: 0.4833,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7019321,
                          basisDsc: "C",
                          amount: 3.6,
                          convertedAmount: 3.6,
                        },
                        {
                          offer: 7019373,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                        {
                          offer: 7018224,
                          basisDsc: "C",
                          amount: 1,
                          convertedAmount: 1,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 5.6,
                      shipAllow: 0.2,
                      allowSum: 5.8,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2021190",
                  itemDescription: "CHEEZ-IT SNAP'D CRACKERS CHEDDAR SOUR",
                  primaryUpc: "002410011487",
                  consumerUpc: "002410011487",
                  caseUpc: "0002410011486",
                  itemUpcs: ["0002410011486", "002410011487"],
                  consumerUpcs: [
                    {
                      upc: "002410011487",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "006",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "002410011487",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "006",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "002410011487",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "006",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 6,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 4.17,
                    costAllow: 4.1367,
                    initialAllowAmt: 0,
                    newCostAllow: 4.1367,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 25.02,
                    costAllow: 24.82,
                    initialAllowAmt: 0,
                    newCostAllow: 24.82,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 25.02,
                    costAllow: 24.82,
                    initialAllowAmt: 0,
                    newCostAllow: 24.82,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0333,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0.0333,
                      allowSum: 0.0333,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0.2,
                      allowSum: 0.2,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0.2,
                      allowSum: 0.2,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
                {
                  itemId: "2021223",
                  itemDescription: "CHEEZ-IT SNAP'D CRKRS DBL CHSE",
                  primaryUpc: "002410011441",
                  consumerUpc: "002410011441",
                  caseUpc: "0002410011440",
                  itemUpcs: ["0002410011440", "002410011441"],
                  consumerUpcs: [
                    {
                      upc: "002410011441",
                      rog: "SACG",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "006",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "002410011441",
                      rog: "SSEA",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "006",
                      sizeDesc: "7.5 OZ ",
                    },
                    {
                      upc: "002410011441",
                      rog: "SSPK",
                      primaryInd: true,
                      labelSize: "M",
                      packDesc: "006",
                      sizeDesc: "7.5 OZ ",
                    },
                  ],
                  effectiveStartDate: "0001-12-24",
                  effectiveEndDate: "9999-01-01",
                  allowanceType: "SCAN",
                  packWhse: 6,
                  ringType: 0,
                  size: "7.5 OZ",
                  vendorPackConversionFactor: 1,
                  unitNetCosts: {
                    netCostType: "UNIT",
                    cost: 4.17,
                    costAllow: 4.1367,
                    initialAllowAmt: 0,
                    newCostAllow: 4.1367,
                  },
                  masterCaseNetCosts: {
                    netCostType: "MASTER_CASE",
                    cost: 25.02,
                    costAllow: 24.82,
                    initialAllowAmt: 0,
                    newCostAllow: 24.82,
                  },
                  shipCaseNetCosts: {
                    netCostType: "SHIP_CASE",
                    cost: 25.02,
                    costAllow: 24.82,
                    initialAllowAmt: 0,
                    newCostAllow: 24.82,
                  },
                  allowanceAmount: 0.1,
                  allowUomType: "EA",
                  allowUomTypes: ["EA"],
                  overlaps: {
                    offerAllowAmounts: [
                      {
                        offer: 7020036,
                        rog: "SACG",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSEA",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                      {
                        offer: 7020036,
                        rog: "SSPK",
                        basisDsc: "S",
                        amount: 0.2,
                      },
                    ],
                    unitizedOverlaps: {
                      netCostType: "UNIT",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.0333,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0.0333,
                      allowSum: 0.0333,
                    },
                    shipCaseOverlaps: {
                      netCostType: "SHIP_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0.2,
                      allowSum: 0.2,
                    },
                    masterCaseOverlaps: {
                      netCostType: "MASTER_CASE",
                      convertedAllowanceAmounts: [
                        {
                          offer: 7020036,
                          basisDsc: "S",
                          amount: 0.2,
                          convertedAmount: 0.2,
                        },
                      ],
                      scanAllow: 0,
                      caseAllow: 0,
                      shipAllow: 0.2,
                      allowSum: 0.2,
                    },
                  },
                  bindMetrics: {
                    group: "AMOUNTS",
                    mode: "AMOUNTS_DEFAULT_BIND",
                    bind: true,
                  },
                  modCommand: "NONE",
                },
              ],
              headerFlatAmt: 0,
              allowanceStatus: "Draft",
              storeGroups: [],
              leadDistributorInd: false,
              includeInd: true,
              finalizedAmountsInd: true,
              dateBindMetrics: {
                group: "DATES",
                mode: "DATES_DEFAULT_BIND",
                bindValues: {
                  srcDates: {
                    allow: {
                      startDate: "2025-02-05",
                      endDate: "2025-02-11",
                    },
                    order: {
                      startDate: "2025-02-05",
                      endDate: "2025-02-11",
                    },
                    ship: {
                      startDate: "2025-02-05",
                      endDate: "2025-02-11",
                    },
                    arrival: {
                      startDate: "2025-02-05",
                      endDate: "2025-02-11",
                    },
                  },
                  dstDates: {
                    allow: {
                      startDate: "2025-02-05",
                      endDate: "2025-02-11",
                    },
                    order: {
                      startDate: "0001-01-01",
                      endDate: "0001-01-01",
                    },
                    ship: {
                      startDate: "0001-01-01",
                      endDate: "0001-01-01",
                    },
                    arrival: {
                      startDate: "0001-01-01",
                      endDate: "0001-01-01",
                    },
                  },
                },
                bind: true,
              },
              excludedItems: [],
              allowanceProcessStatus: "",
              allowDownstreamStatus: {},
            },
          ],
        },
        productSources: ["DSD", "WAREHOUSE"],
        allowancePerformances: {
          id: "63a3a12743a6cee87995b834",
          performance: "4U Event (52)",
          allowanceType: "Scan",
          allowanceCd: "T",
          perfCode1: "20",
          perfCode2: "52",
          performanceConfig: {
            defaultCreateInd: "TC",
            allowOnlyOverrideStoreGroupsInd: false,
            changeableOnEditInd: true,
          },
        },
        offerAllowancesGroupInfoMap: {},
      },
    },
  },
};

export const mock_cancel_status_event_data = {
  name: "Annies CHEEZ - 84882 and 164938 - 27 Week 06 Insert 2025",
  startDate: "2025-02-05",
  endDate: "2025-02-11",
  divisionIds: ["27"],
  divisions: [],
  planProductGroups: [
    {
      planProductGroupId: "66736bbbf0b6311e80e0b7ce",
      sourceProductGroupId: 9184779,
      productGroupType: "PPG",
      name: "Annies CHEEZ - 84882 and 164938",
      divisionId: "27",
      smicGroupCode: 2,
      smicCategoryCode: "0210",
      supplier: {
        supplierId: "515",
        supplierName: "GENERAL MILLS",
      },
      itemCount: 11,
      displayInd: false,
      simsVendors: ["000756", "006446"],
      simsSubAccntVendors: [
        "000756-013",
        "000756-022",
        "006446-001",
        "006446-013",
      ],
      negotiationSimsVendors: ["000756", "006446"],
      unitType: 1,
      promoProductGroupId: 1081817322,
      smicDescription: "CRACKERS",
      sect: "338",
      sectName: "SNACKS",
      dept: "301",
      deptName: "GROCERY",
      dsM: "JONATHAN HUNTER",
      cicCount: 389,
      cigCount: 53,
      dvp: "DENNIS SCHWARZ",
      dasm: "NICK WELLIVER",
      nvp: "",
      ncd: "",
      nacm: "",
    },
  ],
  storeGroups: [
    {
      storeGroupId: "6614614a5996dcc293a6b7bd",
      sourceStoreGroupId: "EDM",
      storeGroupName: "Seattle All Stores",
      storeGroupType: {
        groupType: "S",
        storeGrpTypeName: "Division",
        groupInd: "D",
      },
      divisionIds: ["27"],
      storeCount: 221,
      storeGrpNbr: 1713327680,
    },
  ],
  storeGroupType: "",
  allowance: [],
  isEventChanged: false,
  id: "6759d1fe6299ee135d771c00",
  forecast: {
    forecastSales: 0,
    forecastUnits: 0,
    quadrant: "",
  },
  eventCreationVehicle: {
    vehicleId: "66f1eded1d792055ff84c761",
    vehicleNm: "27 Week 06 Insert 2025",
    sourceVehicleSk: 64748,
    startDate: "2025-02-05",
    endDate: "2025-02-11",
    vehicleType: {
      vehicleTypeId: "66a3f061900b0b47a182376f",
      sourceVehicleTypeSk: 198,
      vehicleTypNm: "insrt",
      vehicleTypDesc: "Weekly Insert",
    },
  },
  planProductGroupPricing: {
    planProductGroup: {
      planProductGroupId: "",
      sourceProductGroupId: "",
      name: "",
      divisionId: "",
      smicGroupCode: "",
      smicCategoryCode: "",
      supplier: {
        id: "",
        supplierId: "",
        supplierName: "",
      },
    },
    quadrant: "",
    priceAmount: "",
    priceFactor: "",
    priceUnit: "",
    priceLimitQty: "",
    forecastAmt: "",
    userName: "",
    supplier: "",
  },
  startWeekVehicle: "27 Week 06 Insert 2025",
  planStoreGroupType: {
    groupType: "S",
    storeGrpTypeName: "Division",
    groupInd: "D",
  },
  vehicleType: "Weekly Insert",
  eventStatus: "Canceled",
  eventType: "DP",
  inValidPromotions: [],
  inValidAllowances: [],
  isChangeEventTypeVisible: false,
  planEventIdNbr: 10030744,
  sourceEventType: "ECP",
  pricing: [
    {
      planProductGroup: {
        planProductGroupId: "66736bbbf0b6311e80e0b7ce",
        sourceProductGroupId: 9184779,
        productGroupType: "PPG",
        name: "Annies CHEEZ - 84882 and 164938",
        divisionId: "27",
        smicGroupCode: 2,
        smicCategoryCode: "0210",
        supplier: {
          supplierId: "515",
          supplierName: "GENERAL MILLS",
        },
        itemCount: 11,
        displayInd: false,
        simsVendors: ["000756", "006446"],
        simsSubAccntVendors: [
          "000756-013",
          "000756-022",
          "006446-001",
          "006446-013",
        ],
        negotiationSimsVendors: ["000756", "006446"],
        unitType: 1,
        promoProductGroupId: 1081817322,
        smicDescription: "CRACKERS",
        sect: "338",
        sectName: "SNACKS",
        dept: "301",
        deptName: "GROCERY",
        dsM: "JONATHAN HUNTER",
        cicCount: 389,
        cigCount: 53,
        dvp: "DENNIS SCHWARZ",
        dasm: "NICK WELLIVER",
        nvp: "",
        ncd: "",
        nacm: "",
      },
      quadrant: "",
      priceAmount: "",
      priceFactor: "",
      priceUnit: "",
      priceLimitQty: "",
      prcMtd: "",
      promoType: "",
    },
  ],
  offerAllowances: [],
  promotionsList: [],
  createUser: {
    userId: "PJAIN03",
    name: "Prayas Jain (Contractor)",
    type: "Merchant",
    userRoles: ["az-meupp-nonprod-promointeditor"],
    createTs: "2024-12-11T17:55:10.517Z",
  },
  updateUser: {
    userId: "PJAIN03",
    name: "Prayas Jain (Contractor)",
    type: "Merchant",
    userRoles: ["az-meupp-nonprod-promointeditor"],
    createTs: "2024-12-11T17:55:10.517Z",
  },
  planEventWorkFlowType: "NOT FOUND",
  simsVendors: ["000756", "006446"],
  manufacturerSimsVendors: [],
  negotiationSimsVendors: ["000756", "006446"],
  eventNegotiationVendor: {
    source: "NONE",
    vendorIds: [],
    eventNegotiationUsers: {
      merchantUser: {
        userId: "PJAIN03",
        name: "Prayas Jain (Contractor)",
        type: "Merchant",
        userRoles: ["az-meupp-nonprod-promointeditor"],
        createTs: "2024-12-11T17:55:10.523Z",
      },
      vendorUserContacts: [],
    },
    userVendorNumbers: [],
  },
  simsVendorList: [
    {
      id: "64dbf5a223a9672237a8387e",
      supplierId: "000756",
      supplierName: "KELLOGGS CO",
      vendorNumSubAccount: "000756-013",
      vendorSubAccountNumber: "013",
      vendorSubAccountName: "KELLOGGS CO                             ",
    },
    {
      id: "64dbf5a223a9672237a83892",
      supplierId: "000756",
      supplierName: "KELLOGGS CO",
      vendorNumSubAccount: "000756-022",
      vendorSubAccountNumber: "022",
      vendorSubAccountName: "KELLOGGS CO                             ",
    },
    {
      id: "64dbf5a723a9672237a86a2e",
      supplierId: "006446",
      supplierName: "KEHE DISTRIBUTORS",
      vendorNumSubAccount: "006446-001",
      vendorSubAccountNumber: "001",
      vendorSubAccountName: "KEHE DISTRIBUTORS                       ",
    },
    {
      id: "64dd0e2b2db5059ed7715d7e",
      supplierId: "006446",
      supplierName: "KEHE DISTRIBUTORS",
      vendorNumSubAccount: "006446-013",
      vendorSubAccountNumber: "013",
      vendorSubAccountName: "KEHE DISTRIBUTORS {JBG WHLSALE}         ",
    },
  ],
  planEventTasks: [],
  submitForOthersInd: false,
  otherDetailsChangedInd: false,
  pidDetailsEventInd: false,
  eventTypeEnum: "DP",
  promotionsLists: [],
  promoProductGroup: "Annies CHEEZ - 84882 and 164938",
  storeGroupName: "Seattle All Stores",
};
