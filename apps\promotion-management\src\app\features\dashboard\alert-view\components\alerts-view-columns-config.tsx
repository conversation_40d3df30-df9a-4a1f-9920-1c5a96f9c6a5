import { Column } from "@albertsons/uds/molecule/Table/Table.types";
import { useDispatch } from "react-redux";
import { Tooltip } from "@albertsons/uds/molecule/Tooltip";
import { EEVENT_STATUS } from "@me/util-helpers";
import { Info, X } from "lucide-react";
import { formatDate } from "../../../../shared/utility/utility";
import {
  ALERTS_AGREED_MAPPER,
  TASK_TYPE_MAPPER,
} from "../../task-view/components/task-view-columns-config";
import { Alert } from "./alert-view-model";
import { usePostAlertsDataMutation } from "../../../../library/data-service/dashboard/dashboard-service";
import { setAlertsFilterData } from "apps/promotion-management/src/app/library/data-access/dashboard";
import { getPageNumber } from "./alert-view-service";
import { useSelectorWrap } from "@me/data-rtk";
import {
  isSortFeatureFlagEnabled,
  showAddEventCTA,
  useGetAppBaseNationalIcon,
} from "@me-upp-js/utilities";
import {
  DASHBOARD_SLICE_CONFIG,
  DEFAULT_PAGINATION_CONFIG,
} from "../../../../config/dashboard-config";
import classNames from "classnames";
import { getUniqueNumber } from "../../task-view/components/task-service";
import AlertsViewGridHover from "./alerts-view-grid-hover";
import { SortLabel } from "../../common/components/sort-label";
import { PROMOTION_MANAGEMENT_COLUMNS } from "apps/promotion-management/src/app/shared/pm-constants/pm-constants";
import { NavLink } from "react-router-dom";
import { EVENT_TYPE } from "apps/promotion-management/src/app/constants";

enum ALERT_TYPES {
  EVENT_APPROVED = "Event Approved",
}
const ALERT_TYPES_TO_SHOW_TOOLTIP = ["DROPPED_ITEM", "NEW_ITEM"];

const getDisplayAlertType = (taskSubType: string, eventStatus: string) => {
  return eventStatus === EEVENT_STATUS.AGREED
    ? ALERTS_AGREED_MAPPER[taskSubType] || taskSubType
    : TASK_TYPE_MAPPER[taskSubType] || taskSubType;
};

const alertActions = (alert: Alert) => {
  const dispatch = useDispatch();
  const [postAlerts] = usePostAlertsDataMutation();
  const { alertView } = DASHBOARD_SLICE_CONFIG;
  const {
    data: { paging },
  } = useSelectorWrap(alertView.SLICE_KEY) || {};
  const { data: alertsFilterData } =
    useSelectorWrap(alertView.FILTER_SLICE_KEY) || {};

  const clearAlert = async (item: Alert) => {
    await postAlerts({
      taskIds: [item.taskId],
    });
    const { pageNumber, pageSize, totalElements } =
      paging || DEFAULT_PAGINATION_CONFIG.pagination;
    const currentPage = getPageNumber(pageNumber, pageSize, totalElements, 1);
    dispatch(
      setAlertsFilterData({
        ...alertsFilterData,
        pagination: { pageNumber: currentPage, pageSize },
        reloadSlice: getUniqueNumber(),
      })
    );
  };

  return showAddEventCTA(alert?.divisionIds, alert?.negotiationSimsVendors) ? (
    <div
      id="abs-alerts-view-columns-config-actions"
      className="flex w-full px-6 justify-end text-muted-foreground"
    >
      <X
        width={18}
        height={18}
        color={"#747272"}
        className={"cursor-pointer"}
        onClick={() => clearAlert(alert)}
      />
    </div>
  ) : null;
};
const { Event, Date, Vendor, Event_Status, Alert_Type } =
  PROMOTION_MANAGEMENT_COLUMNS;
export const ALERT_VIEW_GRID_COLUMNS: Column<Alert>[] = [
  {
    id: "event",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel _key={"event"} label={Event} view="alertView" />
    ) : (
      Event
    ),
    width: "30vw",
    className: "!pl-1.5",
    value: (alert: Alert) => (
      <NavLink
        to={`events/edit/${alert?.id}`}
        className="flex flex-col p-3 gap-2"
        id={`abs-alerts-view-columns-config-event-container-${alert.taskId}`}
      >
        <Tooltip zIndex={10} anchor="bottom">
          <Tooltip.Popover>
            <div
              id={`abs-alerts-view-columns-config-event-name-${alert.taskId}`}
              className="m-1 w-fit overflow-auto rounded text-black text-sm font-normal leading-4"
            >
              {alert.eventName}
            </div>
          </Tooltip.Popover>
          <div
            id={`abs-alerts-view-columns-config-event-details-${alert.taskId}`}
            className={classNames({
              "text-brand text-[#1B6EBB] cursor-pointer truncate w-full font-bold text-base flex":
                true,
            })}
          >
            {alert?.eventType === EVENT_TYPE.NDP &&
              useGetAppBaseNationalIcon() && (
                <img
                  src={useGetAppBaseNationalIcon()}
                  alt="national-icon"
                  className="h-6 w-6 mr-1"
                />
              )}
            {alert?.eventType === EVENT_TYPE.NCDP
              ? `${
                  alert?.parentEvent?.planEventIdNbr
                    ? alert?.parentEvent?.planEventIdNbr
                    : alert?.planEventIdNbr
                } - ${alert?.eventName}`
              : `${alert?.planEventIdNbr} - ${alert?.eventName}`}
          </div>
        </Tooltip>
      </NavLink>
    ),
  },
  {
    id: "date",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel _key={"date"} label={Date} view="alertView" />
    ) : (
      Date
    ),
    width: "14vw",
    className: "!pl-1.5",
    value: (alert: Alert) => (
      <div
        id={`abs-alerts-view-columns-config-event-dates-${alert.taskId}`}
        className="text-[14px] leading-4 font-semibold text-[#2B303C]]"
      >
        {`${formatDate(alert?.startDate)} - ${formatDate(alert?.endDate)}`}
      </div>
    ),
  },
  {
    id: "vendor",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel _key={"vendor"} label={Vendor} view="alertView" />
    ) : (
      Vendor
    ),
    width: "20vw",
    className: "pr-[20px]",
    value: ({ simsTaskVendor, taskId }: Alert) => {
      const uniquesimsTaskList: string[] = [];
      const label = simsTaskVendor?.reduce(
        (simsTaskLabel, { vendorId, vendorName }) => {
          if (!uniquesimsTaskList.includes(vendorId)) {
            uniquesimsTaskList.push(vendorId);
            simsTaskLabel =
              simsTaskLabel +
              `${simsTaskLabel && ", "}${vendorId} ${vendorName}`;
          }
          return simsTaskLabel;
        },
        ""
      );
      return (
        <div
          id={`abs-alerts-view-columns-config-vendor-container-${taskId}`}
          className="pl-3 pr-[15px] flex gap-2 truncate whitespace-nowrap text-sm"
        >
          <Tooltip zIndex={10} anchor="bottom">
            <Tooltip.Popover>
              <span
                id={`abs-alerts-view-columns-config-vendor-label-${taskId}`}
                className="p-1"
              >
                {label}
              </span>
            </Tooltip.Popover>
            <div
              id={`abs-alerts-view-columns-config-vendor-details-${taskId}`}
              className="w-[19vw] pr-[15px] truncate font-semibold leading-6"
            >
              {label}
            </div>
          </Tooltip>
        </div>
      );
    },
  },
  {
    id: "type",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel _key={"type"} label={Event_Status} view="alertView" />
    ) : (
      Event_Status
    ),
    width: "10vw",
    value: (alert: Alert) => (
      <div
        id={`abs-alerts-view-columns-config-type-container-${alert.taskId}`}
        className="p-2 flex flex-col gap-2"
      >
        <div
          id={`abs-alerts-view-columns-config-event-status-${alert.taskId}`}
          className="text-sm"
        >
          {alert?.eventStatus ? alert?.eventStatus : alert?.currentEventStatus}
        </div>
      </div>
    ),
  },
  {
    id: "alertType",
    label: isSortFeatureFlagEnabled() ? (
      <SortLabel _key={"alertType"} label={Alert_Type} view="alertView" />
    ) : (
      Alert_Type
    ),
    width: "10vw",
    value: (alert: Alert) => (
      <div
        id={`abs-alerts-view-columns-config-alert-type-${alert.taskId}`}
        className="flex text-[13px] font-semibold"
      >
        {getDisplayAlertType(alert?.subType, alert?.eventStatus)}

        {ALERT_TYPES_TO_SHOW_TOOLTIP.includes(alert.subType) ? (
          <Tooltip zIndex={10} anchor="bottom">
            <Tooltip.Popover>
              <div
                id={`relative abs-alerts-view-columns-config-alert-type-tooltip-${alert.taskId}`}
              >
                <AlertsViewGridHover task={alert} />
              </div>
            </Tooltip.Popover>
            <Info
              width={18}
              height={18}
              color={"#1B6EBB"}
              className={"cursor-pointer ml-[5px]"}
            />
          </Tooltip>
        ) : (
          ""
        )}
      </div>
    ),
  },
  {
    id: "actions",
    label: "",
    value: alertActions,
  },
];
