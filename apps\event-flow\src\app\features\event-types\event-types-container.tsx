import React from "react";
import EventCard from "./components/event-cards/event-cards";
import EventHeader from "./components/event-header/event-header";
import { eventTypes } from "./constants/event-type-constant";
import efConstants from "../../shared/ef-constants/ef-constants";
import { getIsNationalEvent } from "./event-types-helper";

export default function EventTypeContainer() {
  const events = eventTypes;

  // const {headerText,description,allowances,promotions,Ad}= {...events}
  return (
    <div
      id="abs-event-types-container-div"
      className={`min-h-screen bg-[#f1f4F9] py-[12px] px-[20px] ${efConstants.componentClassName.EVENT_TYPES_CONTAINER}`}
    >
      <EventHeader eventId={""} eventStatus={""} />
      {events?.map((event, i) => {
        // eslint-disable-next-line react/jsx-no-useless-fragment
        const {
          isNationalEvent,
          nationalRolesAvailable,
          nationalEventsFlagEnabled,
        } = getIsNationalEvent(event?.eventType);
        if (
          isNationalEvent &&
          (!nationalRolesAvailable || !nationalEventsFlagEnabled)
        )
          return null;

        return (
          <React.Fragment key={i}>
            <EventCard event={event} />
          </React.Fragment>
        );
      })}
    </div>
  );
}
