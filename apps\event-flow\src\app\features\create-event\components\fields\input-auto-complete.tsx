import * as React from "react";
import AutoComplete from "@albertsons/uds/molecule/AutoComplete";
import { Controller, useFormContext } from "react-hook-form";
import { FormFieldError } from "@me/util-form-wrapper";
import groupInd_constants from "../../../../shared/ef-constants/ef-constants";
import "apps/event-flow/src/app/features/create-event/components/select-dropdown.scss";
import InfoGraphics from "../../../event-types/components/info-graphics/info-graphics";
import {
  INFO_GRAPHICS,
  removeConversionAndDynamicPPGS,
} from "@me-upp-js/utilities";
import {
  formPpgNameBasedOnUnitTypeAndCount,
  removeObjectsById,
} from "../../service/event-details/event-detail-service";
import { getLoggedInUserType } from "@me-upp-js/utilities";
import "./input-auto-complete.scss";
import { useMemo } from "react";
import efConstants from "../../../../shared/ef-constants/ef-constants";

// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface IInputAutoCompleteProps {
  fieldProps?: any;
  onChange?: any;
  props?: any;
  divisionId?: any;
  promoProductName?: any;
  getAllQuery?: any;
  searchQuery?: any;
  defaultValue?: any;
  groupInd?: any;
  disabled?: boolean;
  className?: string;
  selectionPlaceholder?: string;
  noTags?: boolean;
  promoProudctGroups?: any;
}

const InputAutoComplete: React.FunctionComponent<IInputAutoCompleteProps> = ({
  fieldProps,
  onChange,
  props,
  divisionId,
  promoProductName,
  groupInd,
  getAllQuery,
  searchQuery,
  defaultValue,
  disabled,
  className,
  noTags,
  promoProudctGroups,
  selectionPlaceholder,
}) => {
  const {
    registerField,
    label,
    placeHolder,
    notFoundText,
    suggestionKey,
    notSameUpcTypeErrorMsg,
    multiVendorErrorMsg,
    searchQueryName,
    getQueryName,
  } = fieldProps;
  const {
    CIC_FEATURE_FLAGS: { isCICFeatureEnabled },
  } = efConstants;
  const { getValues, setValue, register, clearErrors } = useFormContext();
  const onChangeHandler = element => {
    element.length > 0
      ? setSearchPlaceHolder("")
      : setSearchPlaceHolder(placeHolder);
    setValue(registerField, element);
    onChange(element);
  };
  const [noDataFoundText, setNoDataFoundText] =
    React.useState<string>(notFoundText);
  const [defaultGroups, setDefaultGroups] = React.useState<any>([]);
  const [searchPlaceHolder, setSearchPlaceHolder] = React.useState<string>(
    // defaultValue?.length > 0 ? "" : placeHolder
    defaultValue?.length || defaultGroups.length ? "" : placeHolder
  );
  const userType = getLoggedInUserType();
  const buildVariablesforAPI = (type, query) => {
    const vendorNumList = localStorage.getItem("vendorNumList");
    let promoProductGroupIds = [];
    if (registerField === "storeGroups") {
      const planProductGroups = getValues("planProductGroups");
      promoProductGroupIds = planProductGroups?.map(productGroup => {
        return productGroup?.id || productGroup?.planProductGroupId;
      });
    }

    const storeGroupsQueryVar = {
      filter: {
        divisionIds: getValues("divisionIds"),
        storeGroupName: query,
        productGroups: promoProductGroupIds,
        groupInd: groupInd
          ? groupInd
          : props?.groupInd || groupInd_constants?.DEFAULT_GROUP_IND,
      },
    };
    const userId = window["OAM_REMOTE_USER"]?.toUpperCase();
    const getAllVariables = {
      planProductGroups: {
        divId:
          getValues("eventType") === "NDP"
            ? "98"
            : props?.divisionId || getValues("divisionId"),
        vendorNumbers: vendorNumList ? JSON.parse(vendorNumList) : [],
        userId,
      },
      storeGroups: storeGroupsQueryVar,
    };
    const searchKeyVaiables = {
      planProductGroups: {
        divId:
          getValues("eventType") === "NDP"
            ? "98"
            : props?.divisionId || getValues("divisionId"),
        vendorNumbers: vendorNumList ? JSON.parse(vendorNumList) : [],
        // limit: 5,
        productGroupName: query,
        userId,
      },
      storeGroups: storeGroupsQueryVar,
    };
    return type === "GET"
      ? getAllVariables[registerField]
      : searchKeyVaiables[registerField];
  };
  const buildStoreGroupsObject = (data, queryName) => {
    const storeGroups = data?.data?.[queryName];
    const storeGroupArray = storeGroups?.map(ele => ({
      name: `${ele?.storeGroupName} (${ele?.storeIds?.length})`,
      id: ele?.id,
    }));
    const defaultStoreGroupType = getValues("planStoreGroupType");
    const storeGroupType =
      toString.call(defaultStoreGroupType) === "[object Object]"
        ? defaultStoreGroupType?.storeGrpTypeName || defaultStoreGroupType?.name
        : defaultStoreGroupType;
    setDefaultStoreGroups(storeGroupType, storeGroupArray);
    return { data: { storeGroupsWithCount: [...(storeGroupArray || [])] } };
  };

  const setDefaultStoreGroups = (storeGroupType, data) => {
    if (
      storeGroupType === "D" ||
      (storeGroupType === "Division" && data?.[0])
    ) {
      setValue(registerField, [data?.[0]]);
      setValue("storeGroupName", data?.[0]?.name);
      setDefaultGroups([data?.[0]]);
      setSearchPlaceHolder("");
    } else {
      const storeGroups = getValues("storeGroups");
      if (!storeGroups?.length) {
        setDefaultGroups([]);
        setSearchPlaceHolder(placeHolder);
      }
    }
  };

  const fetchCallback = React.useCallback(
    async query => {
      let data: GroupResultProps;
      if (query && query.length >= 3 && props?.divisionId) {
        data = await searchQuery({
          variables: buildVariablesforAPI("SEARCH", query),
        });
        data =
          registerField === "storeGroups"
            ? buildStoreGroupsObject(data, searchQueryName)
            : data;
      } else if (!query && props?.divisionId) {
        data = await getAllQuery({
          variables: buildVariablesforAPI("GET", ""),
        });
        data =
          registerField === "storeGroups"
            ? buildStoreGroupsObject(data, getQueryName)
            : data;
      } else {
        data = {
          data: {
            getPlanProductGroupByName: [],
          },
        };
      }

      const actualPPGData =
        data.data?.getPromoProductGroupsByDivisionIdAndVendorNumber;

      const dropdownData =
        removeConversionAndDynamicPPGS(
          data.data?.getPromoProductGroupsByDivisionIdAndVendorNumber
        ) ||
        removeConversionAndDynamicPPGS(data.data?.getPlanProductGroupByName) ||
        // data.data?.getStoreGroupsByDivisionIdsAndGroupInd ||
        data.data?.getStoreGroupByName ||
        data.data?.storeGroupsWithCount ||
        data.data?.getStoreGroups ||
        [];

      const conversionDataLength =
        actualPPGData?.length || 0 - dropdownData?.length || 0;

      //  conversionDataLength === actualPPGData?.length =>> don't show No PPG found text.;
      dropdownData &&
        !dropdownData?.length &&
        setNoDataFoundText(
          actualPPGData && conversionDataLength === actualPPGData?.length
            ? ""
            : notFoundText
        );
      if (!defaultValue && getValues("eventType") === "AO") {
        onChangeHandler([dropdownData?.[0]]);
      }
      let formattedPpgData =
        registerField === "planProductGroups"
          ? formPpgNameBasedOnUnitTypeAndCount(dropdownData)
          : dropdownData;
      const selectedPPGs = getValues("planProductGroups");
      if (registerField === "planProductGroups" && isCICFeatureEnabled) {
        if (selectedPPGs?.length && formattedPpgData?.length) {
          formattedPpgData = removeObjectsById({
            formattedPpgData,
            selectedPPGs,
          });
        }
        !selectedPPGs?.length && setSearchPlaceHolder(placeHolder);
      }

      return Promise.resolve(formattedPpgData);
    },
    [divisionId, groupInd, promoProductName, promoProudctGroups]
  );

  const customValidatorForPromo = (): any => {
    const selectedPromoGroups = getValues(registerField);
    const isDisplayCaseValid = checkIfSameDisplayType(selectedPromoGroups);
    return !isDisplayCaseValid ? notSameUpcTypeErrorMsg : true;
  };

  const checkIfSameDisplayType = selectedPromoGroups => {
    if (selectedPromoGroups?.length > 1) {
      const firstPropertyValue = selectedPromoGroups?.[0]?.displayInd;
      return selectedPromoGroups?.every(
        promoObj => promoObj?.displayInd === firstPropertyValue
      );
    }
    return true;
  };
  const defaultValueForAutoComplete = (
    registerField,
    divisionId,
    defaultValue,
    defaultGroups
  ) => {
    const value =
      registerField === "planProductGroups"
        ? useMemo(() => {
            const planProductGroupsValues = getValues("planProductGroups");
            const planProductGroupsValuesCombined =
              formPpgNameBasedOnUnitTypeAndCount(planProductGroupsValues);
            return planProductGroupsValuesCombined;
          }, [divisionId, promoProudctGroups])
        : defaultValue?.length
        ? defaultValue
        : defaultGroups;

    return value;
  };
  interface Groups {
    id: number;
    name: string;
    storeGroupName?: string;
  }

  interface GroupResultProps {
    data: {
      getPromoProductGroupsByDivisionIdAndVendorNumber?: Groups[] | [];
      getPlanProductGroupByName?: Groups[] | [];
      // getStoreGroupsByDivisionIdsAndGroupInd?: Groups[] | [];
      getStoreGroupByName?: Groups[] | [];
      storeGroupsWithCount?: Groups[] | [];
      getStoreGroups?: Groups[] | [];
    };
  }

  const stopPropogation = (
    event:
      | React.MouseEvent<HTMLDivElement>
      | React.KeyboardEvent<HTMLDivElement>
  ) => {
    event.stopPropagation();
  };

  return (
    <div className="w-full" id="abs-input-auto-complete-div">
      <div className="flex mb-1" id="abs-input-auto-complete-div1">
        <div
          className="flex font-bold gap-1"
          id="abs-input-auto-complete-field"
        >
          <p id="abs-input-auto-complete-label">{fieldProps?.label}</p>
          {fieldProps?.required ? (
            <p
              className="text-sm text-left text-[#bf2912]"
              id="abs-input-auto-complete-star"
            >
              *
            </p>
          ) : null}

          <span
            className="flex items-center pl-1 pointer-events-auto"
            id="abs-input-auto-complete-info-graphics"
          >
            <InfoGraphics
              anchor="top"
              variant="light"
              registerField={
                INFO_GRAPHICS?.EVENT_DETAILS_INFO[
                  `${fieldProps?.registerField}`
                ]
              }
              classNameInfo="flex-col m-4 text-xs text-[#5a697b]"
              hoverContent={
                INFO_GRAPHICS?.EVENT_DETAILS_INFO[
                  `${fieldProps?.registerField}`
                ]?.INFO_GRAPHICS_LABEL
              }
              size="14"
            />
          </span>
        </div>
      </div>
      <div className="" id="abs-input-auto-complete-maindiv">
        <Controller
          name={registerField}
          // control={control}
          rules={{
            required: {
              value: fieldProps?.required,
              message: fieldProps?.error?.required?.text,
            },
            validate: {
              isSameUpcTypeOrMultiVendor:
                registerField === "planProductGroups"
                  ? customValidatorForPromo
                  : () => true,
            },
          }}
          render={({ field, fieldState: { error } }) => (
            <div
              className="bg-gray-205"
              id="abs-input-auto-complete-place-holder"
              onKeyDown={stopPropogation}
              onKeyUp={stopPropogation}
            >
              <AutoComplete
                // onSearch={fetchCallback}
                placeholder={searchPlaceHolder}
                multiple
                clearInputOnOptionSelect
                onChange={e => {
                  // field.onChange(e);
                  onChangeHandler(e);
                }}
                notFound={noDataFoundText}
                itemKey={
                  registerField === "planProductGroups"
                    ? "planProductGroupId"
                    : "id"
                }
                itemText={"name"}
                items={fetchCallback}
                value={defaultValueForAutoComplete(
                  registerField,
                  divisionId,
                  defaultValue,
                  defaultGroups
                )}
                ref={null}
                footer
                wrap={true}
                disabled={disabled}
                noTags={isCICFeatureEnabled ? noTags : false}
                selectionPlaceholder={
                  isCICFeatureEnabled ? selectionPlaceholder : ""
                }
                className={`component-scroll w-full !min-h-[40px] ${
                  error?.message
                    ? "border border-error border-2 mt-[-0.25rem] rounded"
                    : "mt-[-0.25rem]"
                }${disabled ? " disable-select" : ""}`}
                id="abs-input-auto-complete"
              />
              {error && <FormFieldError error={error}></FormFieldError>}
            </div>
          )}
        />
      </div>
    </div>
  );
};

export default React.memo(InputAutoComplete);
