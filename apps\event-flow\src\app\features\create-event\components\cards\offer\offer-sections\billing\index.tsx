import Button from "@albertsons/uds/molecule/Button";
import { useSelectorWrap } from "@me/data-rtk";
import { InputText } from "../../../../fields/allowance-atoms";
import { RenderStates } from "@me/ui-render-states";
import { EEVENT_STATUS, editFieldHighlight } from "@me/util-helpers";
import { isEmpty } from "lodash";
import { useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import _ from "lodash";
import classNames from "classnames";
import { appConstants } from "@me/utils-root-props";
import { billingInformationMessages } from "../../../../../../../shared/ef-constants/ef-constants";
import {
  checkIsPaymentTypeDeduct,
  getAllowanceFormRegisterKey,
  getAllowanceKey,
  getAllowanceMap<PERSON>ey,
  removeParams,
  saveAllowanceFormData,
} from "../../../../../service/allowance/allowance-service";
import {
  useDeleteAllowanceTempWorkDataMutation,
  usePostOfferAllowanceMutation,
  usePutOfferAllowanceMutation,
} from "../../../../../service/apis/allowance-api";
import AllowanceFormWrapper, {
  IFormControls,
} from "../../../common/allowance-form-wrapper";
import {
  allowanceNewCardConfiguration,
  allowanceOfferDetailsConfiguration,
  offerCardConfiguration,
  resetAllowanceStepSkipInfo,
  resetIsOfferSectionUpdated,
  resetOfferAmountsData,
  resetOfferSectionsData,
  resetOfferSectionsEnableConfig,
  setAllowanceFormInfo,
} from "../../../../../service/slice/allowance-details-slice";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";
import { allowanceTempWorkReset } from "../../../../../service/slice/allowance-temp-work-slice";
import { setAllowancesResponse } from "../../../../../service/slice/allowances-response-slice";
import { VENDOR_COMMENT_CHAR_LIMIT } from "../../../../../constants/fields/allowance/field-billing-information";
import LoadingSpinner from "../../../../../constants/LoadingSpinner/LoadingSpinner";
import BillingCommentAllSection from "./billing-comment-all-section";
import BillingInfoForAllowance, {
  displayData,
  getRegKeyForBilling,
} from "./billing-info-allowance";
import { OFFER_ALLOWANCE } from "../../offer-flow-config";
import { getOfferMapKey } from "../../offer-service";
import useGetOfferSectionConfiguration from "../../hooks/useGetOfferSectionConfiguration";
import useAllowTempworkUpdate from "../../../../../hooks/useAllowTempworkUpdate";
import { useFormContext } from "react-hook-form";
type IBillingInformationProps = {
  sectionKey;
  cardIndex;
  cardItemIndex;
  isEditEnable;
  isLastStep;
};
export default function CommonBillingInformation({
  sectionKey,
  cardIndex = 0,
  cardItemIndex = 0,
  isLastStep,
  isEditEnable,
}: IBillingInformationProps) {
  const sectionConfiguration = OFFER_ALLOWANCE?.[sectionKey] || {};
  const {
    fields: {
      billingInformationData,
      suggestedVendorPaymentType,
      acApOrArNumber,
      vendorComment,
      vendorOfferTrackingNbr,
      commonComment,
    },
  } = sectionConfiguration;
  const formContext = useFormContext();
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const dispatch = useDispatch();
  const { allowanceData } = useSelectorWrap("allowance_temp_work")?.data || {};
  const { editCardConfig = {}, openCardConfig = {} } =
    useSelectorWrap("offer_card_configutation_rn")?.data || {};
  const offerSectionsEnableConfig =
    useSelectorWrap("offer_sections_enable_config")?.data || {};
  const {
    isOfferSectionUpdated = false,
    isPrimeSectionSectionUpdated = false,
  } = useSelectorWrap("offer_section_update")?.data || {};
  const allowanceRegField = getAllowanceFormRegisterKey(
    cardIndex,
    cardItemIndex
  );
  const { addAllowancesBillingInformation, isLoading: isSaveBillingLoading } =
    useAllowTempworkUpdate();
  const allowFormData = useSelectorWrap("allowance_form_data")?.data || {};
  const allowanceFormData =
      allowFormData?.allowanceFormData?.[allowanceRegField] || {},
    { allowanceType, createInd, performance } =
      allowanceFormData?.allowancePrimeData || {},
    allowanceTypeKey = getAllowanceKey(allowanceType?.toUpperCase()),
    mapperKey = getOfferMapKey(createInd, sectionConfiguration?.offerTypeKey),
    billingRegFieldKey = billingInformationData?.registerKeyName,
    billingInitialValueOnFormLoad =
      allowanceFormData?.[billingRegFieldKey]?.[mapperKey],
    billingInitialValueOnLoad =
      allowanceData?.allowanceTypeSpecification?.[
        getAllowanceMapKey(allowanceTypeKey) || ""
      ]?.allowancesMap?.[mapperKey],
    searchId = eventDetailsData?.offerAllowances?.[cardIndex]?.offerNumber,
    eventStatus = eventDetailsData?.eventStatus,
    allowanceStatus =
      eventDetailsData?.offerAllowances?.[cardIndex]?.allowances[cardItemIndex]
        ?.allowanceStatus;

  const [putOfferAllowance, { isLoading: isAllowancePutLoading }] =
    usePutOfferAllowanceMutation();
  const [postOfferAllowance, { isLoading: isAllowancePostLoading }] =
    usePostOfferAllowanceMutation();
  const isCancelled = useRef(false);
  const [
    deleteAllowanceTempWorkData,
    { isLoading: isDeleteAllowanceTempDataloading, isError: isDeleteError },
  ] = useDeleteAllowanceTempWorkDataMutation();

  const { moveToNextSectionOnCreate, moveToNextSectionOnUpdate } =
    useGetOfferSectionConfiguration({
      allowanceRegField,
    });

  const isCommentRequired = performance === "Other (99)";

  const [formControls, setFormControls] = useState<IFormControls>();
  const [errors, setErrors] = useState<any>([]);
  const [vendorTrackingNumberChanged, setVendorTrackingNumberChanged] =
    useState(false);
  const defaultTrackNumber = useMemo(() => {
    let trackingNumber = "";

    billingInitialValueOnFormLoad?.allowanceBillingInfo?.forEach(item => {
      if (item?.vendorOfferTrackingNbr) {
        trackingNumber = item?.vendorOfferTrackingNbr;
      }
    });

    return trackingNumber;
  }, [billingInitialValueOnFormLoad]);

  const isShowCommentField =
    (allowanceType?.toUpperCase() === "CASE" &&
      mapperKey === "WAREHOUSE_DIST_CENTERS") ||
    mapperKey === "DSD_LEAD_DISTRIBUTORS";

  const {
    register = () => null,
    control,
    setValue = () => null,
    getValues = (key: string) => null,
    setError = () => null,
    clearErrors = () => null,
    formState,
    reset = () => null,
  } = formControls || {};

  const getEventData = () => {
    return eventDetailsData;
  };

  const getHighletedClassName = (fieldProps, fieldChanged: boolean) => {
    return isEditEnable && fieldChanged
      ? "border-2 border-blue-500 "
      : editFieldHighlight(
          fieldProps.mapperKey,
          getEventData,
          searchId,
          "allowance"
        );
  };

  const handleCancel = () => {
    isCancelled.current = true;
    allowanceData?.tempWorkAllowanceId &&
      deleteAllowanceTempWorkData({
        URL_PARAM: allowanceData?.tempWorkAllowanceId,
      });
    if (eventDetailsData?.inValidAllowances) {
      dispatch(
        offerCardConfiguration({
          ...editCardConfig,
          ...openCardConfig,
          offerData: `${
            eventDetailsData?.offerAllowances?.[cardIndex]?.id
          }_${_.uniqueId()}`,
        })
      );
    }
    changeStoreDataAfterUpdate(null);
    dispatch(resetOfferAmountsData());
    dispatch(resetIsOfferSectionUpdated());
  };

  const cancelButton = isEditEnable && (
    <Button onClick={handleCancel} width={140} variant="secondary">
      Cancel
    </Button>
  );

  const getSubmitBtnLabel = () => {
    if (isEditEnable) return efConstants.UPDATE_ALLOWANCE;
    else if (!isEditEnable && isLastStep) return efConstants.CREATE_ALLOWANCE;
    return sectionConfiguration.create.label;
  };

  const getOfferStatus = () => {
    if (eventStatus === EEVENT_STATUS.CANCELED) return true;
    else if (isEditEnable) {
      const isInValidOffer = eventDetailsData?.inValidAllowances?.includes(
        eventDetailsData?.offerAllowances?.[cardIndex]?.id
      );
      if (isInValidOffer) {
        return !isPrimeSectionSectionUpdated;
      }
      return allowanceStatus === EEVENT_STATUS.CANCELED || isOfferSectionUpdated
        ? false
        : !formState?.isDirty;
    }
    return false;
  };

  const getFormControls = (controls: IFormControls) => {
    setFormControls(controls);
  };

  const changeStoreDataAfterUpdate = data => {
    dispatch(allowanceTempWorkReset());
    dispatch(
      offerCardConfiguration({
        editCardConfig: {},
        openCardConfig: {},
      })
    );
    dispatch(resetAllowanceStepSkipInfo());
    data && dispatch(setAllowancesResponse(data));
    dispatch(
      setAllowanceFormInfo({
        allowanceFormData: {
          [cardIndex]: { cancel: true },
        },
      })
    );
    dispatch(resetOfferSectionsData());
    dispatch(resetOfferAmountsData());
    dispatch(resetIsOfferSectionUpdated());
    dispatch(resetOfferSectionsEnableConfig());
  };

  const handleUpdateOrCreateOffer = async (data: object = {}) => {
    data = !isEditEnable
      ? data
      : {
          ...data,
          offerNumber: searchId,
          allowanceChangeStatus: allowanceStatus,
        };
    if (isEditEnable) {
      const putOfferData = await putOfferAllowance({
        URL_PARAMS: [searchId],
        ...data,
      });
      putOfferData?.data && changeStoreDataAfterUpdate(putOfferData?.data);
    } else {
      const postOfferData = await postOfferAllowance(data);
      postOfferData?.data && handleDataAfterCreate(postOfferData?.data);
    }
    //remove temp work id from session storage after update or save
    sessionStorage.removeItem("TEMPWORK_ID");
    sessionStorage.removeItem("OFFER_ID");
  };

  const handleDataAfterCreate = postOfferData => {
    dispatch(
      allowanceNewCardConfiguration({
        isNewAllowance: true,
        stepperType: "offerAllowances",
        isAddAnotherOffer: false,
      })
    );
    changeStoreDataAfterUpdate(postOfferData);
    dispatch(allowanceOfferDetailsConfiguration(postOfferData));
  };

  const handleSave = async formValues => {
    if (!isCancelled.current) {
      const { ...restFormValues } = formValues;
      // TODO: earlier it was using the allowanceFormData but object is empty there
      let allowances = billingInitialValueOnLoad;

      let invalidFormData = false;
      const newErrors: any[] = [];

      allowances = allowances?.map((allowance, index) => {
        const formAllow = restFormValues?.allowanceBillingInfo?.[index] || {};
        const isDeduction = formAllow?.suggestedVendorPaymentType === "Deduct";

        formAllow.suggestedVendorPaymentType =
          formAllow?.suggestedVendorPaymentType === "Select"
            ? ""
            : formAllow?.suggestedVendorPaymentType;

        const error: any = {};

        if (isCommentRequired && !formAllow?.vendorComment) {
          error.vendorComment = "Vendor Comment is required";
        }

        if (
          Boolean(formAllow?.suggestedVendorPaymentType) !==
          Boolean(formAllow?.acApOrArNumber)
        ) {
          if (formAllow?.suggestedVendorPaymentType) {
            error.acApOrArNumber = "Suggested A/P or A/R Number is required";
          } else {
            error.suggestedVendorPaymentType =
              "Suggested Payment Type is required";
          }
        }

        invalidFormData = invalidFormData || !isEmpty(error);
        newErrors.push(error);

        return {
          ...allowance,
          allowanceBillingInfo: {
            ...allowance?.allowanceBillingInfo,
            absVendorPaymentType: checkIsPaymentTypeDeduct(
              performance,
              allowance
            )
              ? "Deduct"
              : "Invoice",
            suggestedVendorPaymentType:
              formAllow?.suggestedVendorPaymentType || "",
            vendorComment: formAllow?.vendorComment || "",
            vendorOfferTrackingNbr: formAllow?.vendorOfferTrackingNbr || "",
            suggestedAcPayableVendorNbr:
              (isDeduction ? formAllow?.acApOrArNumber : "") || "",
            suggestedAcReceivableVendorNbr:
              (!isDeduction ? formAllow?.acApOrArNumber : "") || "",
          },
        };
      });

      setErrors(newErrors);
      if (invalidFormData) return;
      if (isLastStep) {
        const result = await addAllowancesBillingInformation({
          allowances,
          createAllowInd: false,
          offerAllowanceGroup: mapperKey,
          isLastStep,
        });
        formContext?.setValue("isAllowanceChanged", false);
        await handleUpdateOrCreateOffer(result);
        removeParams();
        localStorage.removeItem(
          `${appConstants.RESTRICT_NAV_STORAGE_NAME}${sessionStorage.getItem(
            appConstants.SESSION_STORAGE_RESTRICTED_APP_KEY_NAME
          )}`
        );
      } else {
        const result = await addAllowancesBillingInformation({
          allowances,
          createAllowInd: false,
          offerAllowanceGroup: mapperKey,
          isLastStep,
        });
        if (result?.data) {
          const updatedFormValues = {
            ...allowanceFormData,
            [billingRegFieldKey]: {
              ...allowanceFormData?.[billingRegFieldKey],
              [mapperKey]: {
                ...restFormValues,
              },
            },
          };
          isEditEnable &&
            saveAllowanceFormData({
              dispatch,
              key: billingRegFieldKey,
              allowanceRegField,
              value: {
                ...allowanceFormData?.[billingRegFieldKey],
                [mapperKey]: {
                  ...restFormValues,
                  //  changedFields.length !== 0,
                },
              },
              isPreviousDataRequired: true,
              previousData: allowanceFormData,
            });
          !isEditEnable
            ? moveToNextSectionOnCreate(
                sectionKey,
                mapperKey,
                updatedFormValues
              )
            : moveToNextSectionOnUpdate(sectionKey);
          reset?.(undefined, { keepValues: true });
        }
      }
      analyticsLinkClickEvent();
    }
  };

  const analyticsLinkClickEvent = () => {
    if (
      window["AB"] &&
      window["AB"]["DATALAYER"] &&
      typeof window["AB"]["DATALAYER"].setLinkClickEvents === "function" &&
      getSubmitBtnLabel() === efConstants.CREATE_ALLOWANCE
    ) {
      window["AB"]["DATALAYER"].setLinkClickEvents("promotions:create-offer");
    }
  };

  function onUpdateAllCommentSubmit(e) {
    if (
      getValues(commonComment.registerField)?.trimStart().length >
      VENDOR_COMMENT_CHAR_LIMIT
    ) {
      setError(commonComment.registerField, {
        message: `Maximum ${VENDOR_COMMENT_CHAR_LIMIT} characters allowed`,
      });
      return;
    } else {
      clearErrors(commonComment.registerField);
    }

    billingInitialValueOnLoad?.forEach((element, index) => {
      if (billingInitialValueOnLoad?.[index]?.includeInd) {
        setValue(
          getRegKeyForBilling(vendorComment, index),
          getValues(commonComment.registerField)?.trimStart()
        );
      }
    });
  }

  function onPaymentTypeChange(e, cardItemIndex: number) {
    updateAllRelatedAllowances(cardItemIndex, index => {
      setValue(getRegKeyForBilling(suggestedVendorPaymentType, index), e.id);
    });
  }

  function onApArNumberChange(e, cardItemIndex: number) {
    updateAllRelatedAllowances(cardItemIndex, index => {
      setValue(getRegKeyForBilling(acApOrArNumber, index), e);
    });
  }

  function onVendorCommentChange(e, cardItemIndex: number) {
    updateAllRelatedAllowances(cardItemIndex, index => {
      setValue(getRegKeyForBilling(vendorComment, index), e?.trimStart());
    });
  }

  function onVendorTrackingNumberChange(e) {
    const value = e?.trim();
    billingInitialValueOnLoad?.forEach((element, index) => {
      if (billingInitialValueOnLoad?.[index]?.includeInd) {
        setValue(getRegKeyForBilling(vendorOfferTrackingNbr, index), value);
      }
    });
    setVendorTrackingNumberChanged(true);
    setValue(vendorOfferTrackingNbr.registerField, value);
  }

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  function updateAllRelatedAllowances(index, callBackFunction = i => {}) {
    callBackFunction(index);

    const allowance = billingInitialValueOnLoad?.[index];

    if (efConstants.LEAD_DIST_ONLY !== allowance?.leadDistributorMode) return;

    allowance?.leadDistributorInfos?.forEach((element, index) => {
      // TODO check if vendorNbr is only option
      const subAllowIndex = billingInitialValueOnLoad?.findIndex(
        allow => element?.vendorNbr === allow?.vendorNbr
      );
      if (billingInitialValueOnLoad?.[subAllowIndex]?.includeInd) {
        callBackFunction(subAllowIndex);
      }
    });
  }

  function getErrorForVendorTrackingNumber() {
    let errorMsg = "";

    for (let i = 0; i < errors.length; ++i) {
      const item = errors[i];
      if (item?.vendorOfferTrackingNbr) {
        errorMsg = item?.vendorOfferTrackingNbr;
        break;
      }
    }
    return errorMsg;
  }

  const uniqueVendorNames = billingInitialValueOnLoad?.filter((obj, index) => {
    return (
      index ===
      billingInitialValueOnLoad?.findIndex(
        o =>
          obj?.allowanceBillingInfo?.absMerchVendor ===
          o?.allowanceBillingInfo?.absMerchVendor
      )
    );
  });

  const billingContent = (
    <div
      className="flex flex-col gap-4 mb-4"
      id="abs-common-billing-information-text-container"
    >
      <div className="py-4 px-3 bg-[#F3F4F6] text-sm text-[#5a697b]">
        <p id="abs-common-billing-information-text1">
          For reference, the items in this Offer are setup under the following
          Merchandising System Vendors:
        </p>
      </div>
      {uniqueVendorNames?.map(vendor => {
        return (
          <div
            className="flex gap-4"
            id="abs-common-billing-information-merch-vendor"
          >
            {displayData(
              "ABS Merch Vendor",
              vendor?.allowanceBillingInfo?.absMerchVendor,
              true
            )}
            {displayData(
              "ABS Vendor Name",
              vendor?.allowanceBillingInfo?.absVendorName
            )}
          </div>
        );
      })}
      {billingInformationMessages.map(
        ({
          preContent,
          postContent,
          urlLink,
          css,
          id,
          urlText,
          isShowCommentField,
        }) => {
          const formUrl = (urlLink, css, urlText) => {
            return (
              <a
                href={urlLink}
                target="_blank"
                rel="noreferrer"
                className={css}
              >
                {urlText}
              </a>
            );
          };
          return (
            <>
              <p
                id={id}
                className="py-4 px-3 bg-[#F3F4F6] text-sm text-[#5a697b]"
              >
                {preContent} {formUrl(urlLink, css, urlText)} {postContent}
              </p>
              {isShowCommentField && (
                <BillingCommentAllSection
                  formControls={formControls}
                  commonComment={commonComment}
                  onUpdateAllCommentSubmit={onUpdateAllCommentSubmit}
                  isCommentRequired={isCommentRequired}
                  isNationalEvent={false}
                />
              )}
            </>
          );
        }
      )}

      {billingInitialValueOnLoad?.map((allowance, index) => {
        if (!allowance?.includeInd) return null;

        if (
          efConstants.LEAD_DIST_ONLY === allowance?.leadDistributorMode &&
          !allowance?.leadDistributorInd
        )
          return null;

        return (
          <BillingInfoForAllowance
            control={control}
            register={register}
            sectionKey={sectionConfiguration}
            allowance={allowance}
            allowancesResp={billingInitialValueOnLoad}
            index={index}
            onPaymentTypeChange={onPaymentTypeChange}
            onApArNumberChange={onApArNumberChange}
            onVendorCommentChange={onVendorCommentChange}
            getHighletedClassName={getHighletedClassName}
            error={errors[index]}
            getValues={getValues}
            keyValue={mapperKey}
            allowanceType={allowanceType}
            performance={performance}
            key={`${allowanceType} - ${index}`}
          />
        );
      })}
      <div className="py-4 px-3 bg-[#F3F4F6] text-sm text-[#5a697b]">
        <p id="abs-common-billing-optional-text">
          Optional - if Vendor wishes to enter a Tracking Number to be displayed
          on all Allowance Agreements for the Offer enter it here:
        </p>
      </div>
      <div className="w-1/5">
        <InputText
          control={control}
          register={register}
          fieldProps={vendorOfferTrackingNbr}
          onChange={onVendorTrackingNumberChange}
          className={classNames(
            getHighletedClassName(
              vendorOfferTrackingNbr,
              vendorTrackingNumberChanged
            )
          )}
          error={{
            message: getErrorForVendorTrackingNumber(),
          }}
        />
      </div>
    </div>
  );
  const checkIfVisible = () => {
    if (formControls) {
      const isSectionFocus = offerSectionsEnableConfig?.[sectionKey]?.scrollTo;
      if (!isEditEnable && !isLastStep) {
        return isSectionFocus ? true : formState?.isDirty;
      }
    }
    return true;
  };
  const renderHtml = (
    <>
      <div className="text-xl text-[#3997EF] font-bold mb-3">
        {sectionConfiguration?.label}
      </div>
      <AllowanceFormWrapper
        defaultValues={{
          ...billingInitialValueOnFormLoad,
          vendorOfferTrackingNbr: defaultTrackNumber,
        }}
        handleSave={handleSave}
        getFormControls={getFormControls}
        variant={isLastStep ? "primary" : "secondary"}
        footerProps={{
          label: getSubmitBtnLabel(),
          className: "flex gap-2 p-3 pl-[0px]",
          FooterChildren: cancelButton,
          visable: checkIfVisible(),
          style: {
            width:
              getSubmitBtnLabel() === efConstants.UPDATE_ALLOWANCE ? 180 : 260,
          },
          disabled: getOfferStatus(),
        }}
      >
        <>
          <LoadingSpinner
            isLoading={
              isDeleteAllowanceTempDataloading ||
              isAllowancePostLoading ||
              isAllowancePutLoading ||
              isSaveBillingLoading
            }
            classname="!h-full !w-full rounded-md"
          />
          {control && billingContent}
        </>
      </AllowanceFormWrapper>
    </>
  );

  const renderDetails = {
    isApiLoading: false,
    isPageLevelSpinner: true,
    isRenderMainHtml: true,
    renderHtml,
  };

  return <RenderStates details={renderDetails} />;
}
