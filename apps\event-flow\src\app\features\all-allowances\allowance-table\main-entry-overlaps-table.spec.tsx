import { render } from "@testing-library/react";
import MainEntryOverlapsTable from "./main-entry-overlaps-table";
import { Provider } from "react-redux";
import { configureStore } from '@reduxjs/toolkit';

describe('AllowancesUnitCostDetails', () => {
    let store;

    beforeEach(() => {
        store = configureStore({
            reducer: {
                overlapping_allowance_amount_modal: () => ({
                    data: {
                        modalData: { itemId: '1' },
                        isOpen: true,
                    },
                })
            },
        });
    });

    it('should render modal with no records available', () => {
        const { getByText } = render(
            <Provider store={store}>
                <MainEntryOverlapsTable isSummary={false} offerAllowancesId="1" allowRespData={{}} />
            </Provider>
        );

        expect(getByText(/Allowances in Item Unit Cost/i)).toBeTruthy()
    });
    it('should render modal with Allowances in Item Unit Cost', () => {
        const { getByText } = render(
            <Provider store={store}>
                <MainEntryOverlapsTable isSummary={false} offerAllowancesId="1" allowRespData={{}} />
            </Provider>
        );
 
        expect(getByText(/Allowances in Item Unit Cost/i)).toBeTruthy()
    });

});