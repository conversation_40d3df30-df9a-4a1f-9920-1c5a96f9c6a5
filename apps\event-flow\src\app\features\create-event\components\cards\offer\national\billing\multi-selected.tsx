import Checkbox from "@albertsons/uds/molecule/Checkbox";
import Popper from "@albertsons/uds/molecule/Popper";
import { ChevronDown } from "lucide-react";
import React, { useState } from "react";

type Props = {
  values: any[];
  setValues: any;
  renderOptionText?: any;
};

export default function MultiSelectDropDown({
  values,
  setValues,
  renderOptionText = null,
}: Props) {
  const ref = React.useRef<HTMLDivElement>(null);
  const [open, setOpen] = useState(false);

  const onBlur = (ev: React.FocusEvent<HTMLElement>): void => {
    if (
      ev?.relatedTarget !== ref?.current &&
      ev?.relatedTarget !== ref?.current?.firstChild
    ) {
      setOpen(false);
    }
  };

  const isAllSelected = values.every(i => i.checked);
  const isAllUnselected = values.every(i => !i.checked);

  function handleAllSelect() {
    setValues(values.map(val => ({ ...val, checked: !isAllSelected })));
  }
  
  return (
    <>
      <div id="abs-multi-select-dropdown0" className="h-5 flex">
        <div
          className="flex justify-between w-64 px-3 h-[40px] items-center border border-solid focus-visible:outline-[#BFD4E7] border-[#BFD4E7] rounded-sm"
          ref={ref}
          onClick={() => setOpen(!open)}
        >
          {!values.filter(i => i.checked).length
            ? "Select"
            : `Selected (${values.filter(i => i.checked).length})`}
          <ChevronDown color="#2B303C" size={24} />
        </div>
      </div>
      <Popper open={open} anchor={ref} onBlur={onBlur} zIndex={10} autoFocus>
        <div
          id="abs-multi-select-dropdown1"
          className="w-64 overflow-y-scroll h-[300px] component-scroll rounded-lg border border-gray-204 inline-block relative bg-white z-10 pt-2 pb-4 px-4"
        >
          <div
            id="abs-multi-select-dropdown2"
            className="flex h-10 w-full items-center border-solid border-[#C8DAEB] border-b"
          >
            <Checkbox
              id="abs-multi-select-dropdown-select-all"
              checked={isAllSelected}
              indeterminate={!isAllSelected && !isAllUnselected}
              onClick={handleAllSelect}
            />
            <span className="ml-4 font-bold "> Select / Unselect All</span>
          </div>

          <div
            id="abs-multi-select-dropdown3"
            className="flex flex-col gap-4 pt-4"
          >
            {values?.map((value, index) => {
              return (
                <div className="flex gap-3 items-center" key={value.name}>
                  <Checkbox
                    id={"abs-multi-select-dropdown-select-" + index}
                    checked={value.checked}
                    onClick={() => {
                      const vals = values.map(val => ({
                        ...val,
                        checked:
                          val.name === value.name ? !val.checked : val.checked,
                      }));

                      setValues(vals);
                    }}
                  />
                  <>
                    {value?.divisionId} - {value?.divisionName}
                  </>
                </div>
              );
            })}
          </div>
        </div>
      </Popper>
    </>
  );
}
