import { render, screen } from "@testing-library/react";
import EventDetailPromoProductHoverContent from "./event-details-promo-product-hover-content";

const mockEvent1 = {
  id: "63a3f10dce2d351bc2a74df2",
  name: "elisa test UC7 - Store level Pricing",
  divisionIds: ["27"],
  startDate: 1670976000000,
  endDate: 1671494400000,
  eventType: "DP",
  sourceEventType: "ECP",
  eventStatus: "Draft",
  planProductGroups: [
    {
      planProductGroupId: "636bdeac9665d0440e009d7b",
      sourceProductGroupId: 204432,
      name: "Signature SELECT� Mayonnaise (22-30) OUNCE - 85549",
      divisionId: "27",
      smicGroupCode: 6,
      smicCategoryCode: "0601",
      supplier: {
        supplierId: "7877",
        supplierName: "ABS-ASMs",
      },
    },
  ],
  pricing: [
    {
      planProductGroup: {
        planProductGroupId: "636bdeac9665d0440e009d7b",
        sourceProductGroupId: 204432,
        name: "Signature SELECT� Mayonnaise (22-30) OUNCE - 85549",
        divisionId: "27",
        smicGroupCode: 6,
        smicCategoryCode: "0601",
        supplier: {
          supplierId: "7877",
          supplierName: "ABS-ASMs",
        },
      },
      quadrant: "",
      priceAmount: "",
      priceFactor: "",
      priceUnit: "",
      priceLimitQty: "",
      prcMtd: "",
    },
  ],
  storeGroups: [
    {
      storeGroupId: "634fa3cc6ed2c97a3b10eae4",
      sourceStoreGroupId: "92",
      storeGroupName: "SSEA",
      storeGroupType: {
        groupType: "S",
        storeGrpTypeName: "Geographic",
        groupInd: "G",
      },
      divisionIds: ["27"],
      storeCount: 145,
    },
    {
      storeGroupId: "634fa3cc6ed2c97a3b10eae5",
      sourceStoreGroupId: "93",
      storeGroupName: "SSPK",
      storeGroupType: {
        groupType: "S",
        storeGrpTypeName: "Geographic",
        groupInd: "G",
      },
      divisionIds: ["27"],
      storeCount: 53,
    },
    {
      storeGroupId: "634fa3cc6ed2c97a3b10eae6",
      sourceStoreGroupId: "94",
      storeGroupName: "SACG",
      storeGroupType: {
        groupType: "S",
        storeGrpTypeName: "Geographic",
        groupInd: "G",
      },
      divisionIds: ["27"],
      storeCount: 26,
    },
  ],
  createUserId: "SRAVI10",
  lastUpdUserId: "SRAVI10",
  createTs: 1673541259032,
  lastUpdTs: 1673541259032,
  eventCreationVehicle: {
    vehicleId: "636b043250f8fb41c21b9442",
    vehicleNm: "27 Week 50 GO Insert 2022",
    sourceVehicleSk: 209505,
    startDate: 1670976000000,
    endDate: 1671494400000,
    vehicleType: {
      vehicleTypeId: "636abba1b426ee543a94d2c4",
      sourceVehicleTypeSk: 196,
      vehicleTypNm: "gropn",
      vehicleTypDesc: "Grand Opening Insert",
    },
  },
  eventTypeEnum: "DP",
};

const mockEvent2 = {
  id: "63aaeb64f39eee20b6d68475",
  planEventIdNbr: 0,
  name: "Reward Bonus Event12",
  divisionIds: ["36", "56"],
  startDate: 1671494400000,
  endDate: 1671926400000,
  eventType: "DP",
  sourceEventType: "ECP",
  eventStatus: "Draft",
  planProductGroups: [
    {
      planProductGroupId: "636bddcd9665d0440eff5260",
      sourceProductGroupId: 1960,
      name: "KLLGG SPCL K MEAL BAR PROTEIN ; 6-1.59 OZ; CIG: 052239",
      divisionId: "05",
      smicGroupCode: 11,
      smicCategoryCode: "1110",
      supplier: {
        supplierId: "673",
        supplierName: "KELLOGGS CO",
      },
    },
    {
      planProductGroupId: "636bddcd9665d0440eff5261",
      sourceProductGroupId: 2374,
      name: "ELITE CHOCOLATE BAR BITTERSWEET; 3 OZ; CIG: 054063",
      divisionId: "05",
      smicGroupCode: 27,
      smicCategoryCode: "2720",
      supplier: {
        supplierId: "944",
        supplierName: "NESTLE",
      },
    },
  ],
  pricing: [
    {
      planProductGroup: {
        planProductGroupId: "636bddcd9665d0440eff5260",
        sourceProductGroupId: 1960,
        name: "KLLGG SPCL K MEAL BAR PROTEIN ; 6-1.59 OZ; CIG: 052239",
        divisionId: "05",
        smicGroupCode: 11,
        smicCategoryCode: "1110",
        supplier: {
          supplierId: "673",
          supplierName: "KELLOGGS CO",
        },
      },
      quadrant: "",
      priceAmount: "",
      priceFactor: "",
      priceUnit: "",
      priceLimitQty: "",
      prcMtd: "",
    },
    {
      planProductGroup: {
        planProductGroupId: "636bddcd9665d0440eff5261",
        sourceProductGroupId: 2374,
        name: "ELITE CHOCOLATE BAR BITTERSWEET; 3 OZ; CIG: 054063",
        divisionId: "05",
        smicGroupCode: 27,
        smicCategoryCode: "2720",
        supplier: {
          supplierId: "944",
          supplierName: "NESTLE",
        },
      },
      quadrant: "",
      priceAmount: "",
      priceFactor: "",
      priceUnit: "",
      priceLimitQty: "",
      prcMtd: "",
    },
  ],
  storeGroups: [
    {
      storeGroupId: "634fa3cc6ed2c97a3b10ead7",
      sourceStoreGroupId: "43",
      storeGroupName: "SHAW",
      storeGroupType: {
        groupType: "S",
        storeGrpTypeName: "Geographic",
        groupInd: "G",
      },
      divisionIds: ["25"],
      storeCount: 24,
    },
    {
      storeGroupId: "634fa3cc6ed2c97a3b10ead8",
      sourceStoreGroupId: "44",
      storeGroupName: "SNCA",
      storeGroupType: {
        groupType: "S",
        storeGrpTypeName: "Geographic",
        groupInd: "G",
      },
      divisionIds: ["25"],
      storeCount: 273,
    },
  ],
  createUserId: "dmish12",
  lastUpdUserId: "dmish12",
  createTs: 1672145764607,
  lastUpdTs: 1672145764607,
  eventCreationVehicle: {
    vehicleId: "636b037250f8fb41c219e6e8",
    vehicleNm: "32 Week 17 Insert 2018",
    sourceVehicleSk: 3,
    startDate: 1524614400000,
    endDate: 1525132800000,
    vehicleType: {
      vehicleTypeId: "636abba1b426ee543a94d3b8",
      sourceVehicleTypeSk: 2,
      vehicleTypNm: "insrt",
      vehicleTypDesc: "Weekly Insert",
    },
  },
  eventTypeEnum: "DP",
};

describe("Event Detail Promo Product Hover Test Suites", () => {
  it("should render the component", () => {
    const { baseElement } = render(
      <EventDetailPromoProductHoverContent
        event={mockEvent1}
        popoverCloser={jest.fn()}
      />
    );
    expect(baseElement).toBeTruthy();
  });

  it("should show the promo product", () => {
    const { container } = render(
      <EventDetailPromoProductHoverContent
        event={mockEvent1}
        popoverCloser={jest.fn()}
      />
    );
    const classNames = container.getElementsByClassName(
      "float-right self-center"
    );

    expect(classNames.length).toBe(1);
  });

  it("should trigger the sort with more than one element", () => {
    const { getByText } = render(
      <EventDetailPromoProductHoverContent
        event={mockEvent2}
        popoverCloser={jest.fn()}
      />
    );
    expect(
      getByText(/ELITE CHOCOLATE BAR BITTERSWEET; 3 OZ; CIG: 054063/i)
    ).toBeTruthy();
    expect(
      getByText(/KLLGG SPCL K MEAL BAR PROTEIN ; 6-1.59 OZ; CIG: 052239/i)
    ).toBeTruthy();
  });

  it("should show the items actually sorted A-Z", () => {
    // NOTE: to check order of appearance in the doc compareDocumentPosition is used
    // docs: https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition

    const { getByText } = render(
      <EventDetailPromoProductHoverContent
        event={mockEvent2}
        popoverCloser={jest.fn()}
      />
    );
    const p1 = getByText(/ELITE CHOCOLATE BAR BITTERSWEET; 3 OZ; CIG: 054063/i);

    console.debug(p1, "p1");
    const p2 = getByText(
      /KLLGG SPCL K MEAL BAR PROTEIN ; 6-1.59 OZ; CIG: 052239/i
    );
    expect(p1?.compareDocumentPosition(p2)).toBe(2);
  });
});
