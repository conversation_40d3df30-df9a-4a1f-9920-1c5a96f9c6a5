// EventNameField.test.tsx
import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { useFormContext } from "react-hook-form";
import EventNameField from "./event-name-field";
import { useSelectorWrap } from "@me/data-rtk";
import { validateNCDP } from "../../event-details-card-service";
import "@testing-library/jest-dom";

// Mock necessary imports
jest.mock("react-hook-form", () => ({
  useFormContext: jest.fn(),
}));

jest.mock("../../utility/utility", () => ({
  onChangeDateAndNameHandler: jest.fn(({ setFormFields }) => {
    setFormFields(); // manually call it for test
  }),
}));

jest.mock("@me/input-fields", () => ({
  InputText: jest.fn(props => <input type="text" {...props} />),
}));
// Mock validateNCDP as a jest function
jest.mock("../../event-details-card-service", () => ({
  validateNCDP: jest.fn(),
}));
jest.mock("@me/data-rtk", () => ({
  useSelectorWrap: jest.fn(),
  injectEndPointsWrapper: jest.fn(() => ({
    useFetchDivisionDataQuery: jest.fn(() => Promise.resolve({ data: [] })),
  })),
  createGenericSlice: jest.fn(() => () => ({
    actions: {
      setOfferAmontsData: jest.fn(),
      setIsAdditionalDatesChnaged: jest.fn(),
      resetOfferAmountsData: jest.fn(),
    },
  })),
}));

describe("EventNameField", () => {
  const mockSetFormFields = jest.fn();
  const mockSetValue = jest.fn();
  const mockGetValues = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();

    // Cast useFormContext as jest.Mock to allow mockReturnValue usage
    (useFormContext as jest.Mock).mockReturnValue({
      getValues: mockGetValues,
      setValue: mockSetValue,
    });
  });

  test("renders InputText when eventName is provided", () => {
    const mockEventName = { registerField: "testField", someProp: "value" };

    // Mocking useSelectorWrap return value
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: { eventType: "testType" },
    });

    render(
      <EventNameField
        eventName={mockEventName}
        isEditEvent={true}
        formFields={{}}
        setFormFields={mockSetFormFields}
      />
    );

    expect(screen.getByRole("textbox")).toBeInTheDocument();
  });

  test("does not render InputText when eventName is not provided", () => {
    render(
      <EventNameField
        eventName={null}
        isEditEvent={true}
        formFields={{}}
        setFormFields={mockSetFormFields}
      />
    );

    expect(screen.queryByRole("textbox")).toBeNull();
  });

  test("calls onChangeHandler when InputText value changes", () => {
    const mockEventName = { registerField: "testField", someProp: "value" };

    // Mocking useSelectorWrap return value
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: { eventType: "testType" },
    });

    render(
      <EventNameField
        eventName={mockEventName}
        isEditEvent={true}
        formFields={{}}
        setFormFields={mockSetFormFields}
      />
    );

    const input = screen.getByRole("textbox");
    fireEvent.change(input, { target: { value: "new value" } });

    expect(mockSetFormFields).toHaveBeenCalled();
  });

  test("disables InputText when validateNCDP returns true", () => {
    const mockEventName = { registerField: "testField", someProp: "value" };

    // Mock validateNCDP return value
    (validateNCDP as jest.Mock).mockReturnValue(true);

    render(
      <EventNameField
        eventName={mockEventName}
        isEditEvent={true}
        formFields={{}}
        setFormFields={mockSetFormFields}
      />
    );

    expect(screen.getByRole("textbox")).toBeDisabled();
  });

  test("does not disable InputText when validateNCDP returns false", () => {
    const mockEventName = { registerField: "testField", someProp: "value" };

    // Mock validateNCDP return value
    (validateNCDP as jest.Mock).mockReturnValue(false);

    render(
      <EventNameField
        eventName={mockEventName}
        isEditEvent={true}
        formFields={{}}
        setFormFields={mockSetFormFields}
      />
    );

    expect(screen.getByRole("textbox")).not.toBeDisabled();
  });

  test("calls onChangeHandler when value changes", async () => {
    const mockEventName = { registerField: "testField", someProp: "value" };

    // Mocking useSelectorWrap return value
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: { eventType: "testType" },
    });

    render(
      <EventNameField
        eventName={mockEventName}
        isEditEvent={true}
        formFields={{}}
        setFormFields={mockSetFormFields}
      />
    );

    const input = screen.getByRole("textbox");
    fireEvent.change(input, { target: { value: "new event name" } });

    await waitFor(() => {
      expect(mockSetFormFields).toHaveBeenCalledTimes(1);
    });
  });
});
