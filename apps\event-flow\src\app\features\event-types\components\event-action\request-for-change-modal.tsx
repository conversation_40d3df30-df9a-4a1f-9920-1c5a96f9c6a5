import { CommonModal } from "../../../create-event/components/cards/common/common-modal";
import TextArea from "@albertsons/uds/molecule/TextArea";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import { memo, useState } from "react";
import { CTAs_label } from "../../../create-event/constants/event-status/contsants";

interface IRequestForChangeProps {
  isRequestForChangePopupOpen: any;
  setIsRequestForChangePopupOpen: any;
  confrimBtnHandler: any;
}
const RequestForChangeModal = ({
  isRequestForChangePopupOpen,
  setIsRequestForChangePopupOpen,
  confrimBtnHandler,
}: IRequestForChangeProps) => {
  const [comment, setComment] = useState("");
  const handleComment = e => {
    setComment(e?.target?.value);
  };
  const textAreaComp = () => (
    <div className="flex justify-center" id="request-for-change-text-div">
      <TextArea
        id="request-for-change-text"
        className="w-[320px]"
        width="md"
        maxCharacters={300}
        onChange={e => handleComment(e)}
        value={comment}
        name="textarea content"
        isRequired
      />
    </div>
  );
  const onCloseHandler = () => {
    setIsRequestForChangePopupOpen(false);
  };
  const handleClick = event => {
    confrimBtnHandler(event, comment);
  };
  const checkIsDisable = () => {
    return (
      (comment && comment?.length > 300) ||
      comment === undefined ||
      comment === ""
    );
  };
  return (
    <div>
      <CommonModal
        minHeight={380}
        isModalPopupOpen={isRequestForChangePopupOpen}
        setModalPopupOpen={setIsRequestForChangePopupOpen}
        confrimBtnHandler={handleClick}
        title={efConstants?.REQUEST_FOR_CHANGE}
        confirmBtnTitle={CTAs_label?.SEND_TO_MERCHANT}
        cancelBtnTitle={efConstants?.CONTINUE_EDITING}
        onClose={onCloseHandler}
        showHideBtns={true}
        infoMessage={efConstants?.POPUP_TEXT_FOR_REQUEST_CHANGE}
        childComp={textAreaComp}
        isConfirmDisabled={checkIsDisable()}
      />
    </div>
  );
};

export default memo(RequestForChangeModal);
