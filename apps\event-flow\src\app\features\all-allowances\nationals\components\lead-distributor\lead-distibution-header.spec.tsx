import React from "react";
import { render, screen } from "@testing-library/react";
import '@testing-library/jest-dom';
import LeadDistributorHeader from "./lead-distributor-header";

describe("LeadDistributorHeader Component", () => {
  it("renders the correct text", () => {
    render(<LeadDistributorHeader />);
    const headerElement = screen.getByText(/Select a Lead Distributor\(s\)/i);
    expect(headerElement).toBeInTheDocument();
  });

  it("has the correct class names for styling", () => {
    render(<LeadDistributorHeader />);
    const headerElement = screen.getByText(/Select a Lead Distributor\(s\)/i);
    expect(headerElement).toHaveClass(
      "text-center border-b pb-4 select-none font-bold text-[28px] mt-8"
    );
  });
});