import Alert from "@albertsons/uds/molecule/Alert";
import Button from "@albertsons/uds/molecule/Button";
import { useSelectorWrap } from "@me/data-rtk";
import { RenderStates } from "@me/ui-render-states";
import { useGetAppBasePath } from "@me/util-helpers";
//TODO: change path to absolute import
import { setSelectedFilters } from "@me/feature-primary-filters";
import _ from "lodash";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  getAllowAmtLabel,
  getAllowanceMapKey,
  getQueryParams,
  saveAllowancesItemReqBody,
} from "../create-event/service/allowance/allowance-service";
import {
  useDeleteAllowanceTempWorkDataMutation,
  usePostAllowanceTempWorkDataMutation,
  usePutOfferAllowanceMutation,
} from "../create-event/service/apis/allowance-api";
import { setEmptyFieldsList } from "../create-event/service/slice/allowances-dashboard-slices";
import {
  setFilteredAndSortedIdsAction,
  setTableDataAction,
} from "../create-event/service/slice/table-data-slice";
import {
  check_isAnyColumnAllZero,
  generateAllowanceTempObject,
  getEmptyFields,
} from "./allowances-table-service";
import {
  allowanceTempWorkHandler,
  allowanceTempWorkReset,
} from "../create-event/service/slice/allowance-temp-work-slice";
import { textMapping } from "./all-allowances-container/text-mapping";
import {
  leadDistributorsHandler,
  setIsLeadDistributorError,
} from "../create-event/service/slice/lead-distributors-slice";
import {
  allowanceProductSources,
  offerCardConfiguration,
  resetIsOfferSectionUpdated,
  resetOfferAmountsData,
  resetOfferSectionsData,
  resetOfferSectionsEnableConfig,
  setIsOfferSectionUpdated,
  setOfferAmontsData,
} from "../create-event/service/slice/allowance-details-slice";
import { isComingFromtask } from "@me-upp-js/utilities";
import { CommonModal } from "../create-event/components/cards";
import efConstants from "../../shared/ef-constants/ef-constants";
import { appConstants } from "@me/utils-root-props";
import { useGetPlanEventDataQuery } from "../create-event/service/apis/event-api";
import {
  isInValidLeadDists,
  validateIfAllOtherVendorsAssignedLeads,
} from "./allowance-lead-distributors/billing-selection-utils";
export default function AllAllowancesTopRight({ offerId, isDsd }) {
  const { OFFER_POPUP_MESSAGES } = efConstants;
  const [isDisplayFieldAlert, setIsDisplayFieldAlert] = useState(false);
  const [isAmountsError, setIsAmountsError] = useState(false);
  const queryString = window.location.search;
  const urlParams = new URLSearchParams(queryString);
  const isEditEnable: boolean = urlParams.get("isEdit") === "true";
  const isAllowConvEnable: boolean =
    urlParams.get("isAllowConvEnable") === "true";
  const isAllowanceChanged: boolean =
    urlParams.get("isAllowTypeChange") === "true";
  const eventId = urlParams.get("eventId");
  const offerGroup = urlParams.get("group");
  const { data: switchValue } = useSelectorWrap("selectedSwitchValue_rn");
  const { data: allowancesRespCopy } = useSelectorWrap("allowancesRespCopy_rn");
  const { isAdditionalDatesChanged = {}, offerAmounts = {} } =
    useSelectorWrap("offer_amounts_details")?.data || {};
  const { taskType } = getQueryParams();
  const [inValidOtherVendorError, setInValidOtherVendorError] = useState(false);
  const {
    data: eventDataResp,
    isFetching: isEventDataFetched,
    refetch,
  } = useGetPlanEventDataQuery(eventId, {
    skip: !eventId,
  });
  const {
      data: { tableData },
    } = useSelectorWrap("allowanceTableData_rn"),
    { data: headerOnlyAmtsArr } = useSelectorWrap("headerOnlyAmt_rn");
  const {
    data: { leadDistributorsMode },
  } = useSelectorWrap("leadDistributorsMode_rn");
  const [
      postAllowanceTempWorkData,
      {
        isSuccess: isSaveSuccess,
        isError: isSaveApiError,
        error: saveApiError,
        isLoading: isSaveLoading,
        data: postTempWorkResp,
      },
    ] = usePostAllowanceTempWorkDataMutation(),
    allowanceTempWorkData = useSelectorWrap("allowance_temp_work")?.data
      ?.allowanceData;
  const [putOfferAllowance] = usePutOfferAllowanceMutation();
  const [deleteAllowanceTempWorkData] =
    useDeleteAllowanceTempWorkDataMutation();
  const { isAllowanceTypeChanged } = useSelectorWrap(
    "allow_type_change_rn"
  ).data;
  const { data: allowGroupConfigObj } = useSelectorWrap(
      "allowanceTableColsGroupConfig_rn"
    ),
    allowName = getAllowanceMapKey(allowanceTempWorkData?.allowanceType) || "",
    offerNumber =
      allowanceTempWorkData?.allowanceTypeSpecification?.[allowName]
        ?.offerNumber ||
      allowanceTempWorkData?.offerNumber ||
      "",
    tempAllowItems =
      Object.values(
        allowanceTempWorkData?.allowanceTypeSpecification?.[allowName]
          ?.allowancesMap || {}
      )?.[0] || [];
  const { basePath } = useGetAppBasePath();
  const dispatch = useDispatch(),
    navigate = useNavigate();

  useEffect(() => {
    dispatch(allowanceProductSources({ productSources: [] }));
  }, []);

  useEffect(() => {
    if (isSaveSuccess) {
      dispatch(
        allowanceTempWorkHandler({
          allowanceData: getAllowanceTempFromTable(),
          isTempLoaded: true,
        })
      );
      navigateOnsaveChanges(postTempWorkResp);
    }
  }, [isSaveSuccess]);

  const clearTableSlices = () => {
    dispatch(setTableDataAction([]));
    dispatch(setFilteredAndSortedIdsAction([]));
    dispatch(
      leadDistributorsHandler({
        stepData: [],
        leadOptions: [],
        isLeadChange: false,
        leadDistMode: null,
        leadSelectionType: null,
      })
    );
  };

  const updateOfferDateSlice = postTempWorkResp => {
    if (!appConstants.IS_ENABLE_REDESIGN_ALLOWANCE_CHANGES) return;
    const allowMap =
      postTempWorkResp?.allowanceTypeSpecification?.[allowName]
        ?.allowancesMap;
    const updatedAllowances =
      offerGroup && allowMap ? allowMap?.[offerGroup] : null;
    const getUpdatedAmtLabel = getAllowAmtLabel(
      postTempWorkResp?.allowanceType,
      updatedAllowances || [],
      efConstants.ALLOWANCE_TYPES.HEADERFLAT.key ===
        postTempWorkResp?.allowanceType,
      true
    );
    const isSameAmtAndUOM = getUpdatedAmtLabel
      ? !/-|and/.test(getUpdatedAmtLabel)
      : null;
    const hasRequiredKeys = (obj, keys) =>
      keys?.some(key => obj?.hasOwnProperty(key));
    const setItemSummarized = offerGroup => {
      const amountSummary = offerAmounts?.[offerGroup]?.summary;
      const isCostsAvailable = amountSummary
        ? hasRequiredKeys(amountSummary, [
            "masterCaseNetCosts",
            "unitNetCosts",
            "allowUomType",
          ])
        : false;
      return isSameAmtAndUOM ? (isCostsAvailable || amountSummary?.itemAmountsCouldBeSummarized) : false;
    };
    offerGroup &&
      dispatch(
        setOfferAmontsData({
          isAdditionalDatesChanged: {
            ...isAdditionalDatesChanged,
            [offerGroup]: 0,
          },
          offerAmounts: {
            ...offerAmounts,
            [offerGroup]: {
              ...(offerAmounts?.[offerGroup] || {}),
              ...(updatedAllowances && { allowances: updatedAllowances }),
              summary : {
                ...offerAmounts?.[offerGroup]?.summary,
                ...(isSameAmtAndUOM !== null && {itemAmountsCouldBeSummarized: setItemSummarized(offerGroup)}),
              }
            },
          },
        })
      );

    isEditEnable &&
      dispatch(
        setIsOfferSectionUpdated({
          isOfferSectionUpdated: true,
          isAmountsSectionUpdated: true
        })
      );
  };

  const navigateOnsaveChanges = async postTempWorkResp => {
    updateOfferDateSlice(postTempWorkResp);
    if (isComingFromtask(taskType, eventDataResp?.planEvent) && isEditEnable) {
      const putOfferData = await putOfferAllowance({
        URL_PARAMS: [offerNumber],
        ...postTempWorkResp,
      });
      putOfferData && (await refetch());
      putOfferData &&
        dispatch(
          offerCardConfiguration({
            offerData: `${offerId}_${_.uniqueId()}`,
            editCardConfig: {},
          })
        );
      if (allowanceTempWorkData?.tempWorkAllowanceId) {
        const isDeletedTemp = await deleteAllowanceTempWorkData({
          URL_PARAM: allowanceTempWorkData?.tempWorkAllowanceId,
        });
        isDeletedTemp && dispatch(allowanceTempWorkReset());
        if (appConstants.IS_ENABLE_REDESIGN_ALLOWANCE_CHANGES) {
          // On main entry if coming from task, on save amount and after Update Offer reset slices
          dispatch(resetOfferAmountsData());
          dispatch(resetIsOfferSectionUpdated());
          dispatch(resetOfferSectionsEnableConfig());
          dispatch(resetOfferSectionsData());
        }
      }

      navigate(
        `${basePath}/events/edit/${eventId}?${
          taskType === appConstants.TASK_TYPE_NEW_ITEM
            ? `taskType=${taskType}`
            : ""
        }`
      );
    } else {
      navigateToStepper();
    }
  };

  const navigateToStepper = () => {
    clearTableSlices();
    const allowanceTypeChangedParam = isAllowanceChanged
      ? `&isAllowTypeChange=${isAllowanceChanged}`
      : "";
    navigate(
      `${basePath}/events/edit/${eventId}?isSaved=true&offerKey=${offerGroup}&offerId=${offerNumber}${allowanceTypeChangedParam}${
        taskType === appConstants.TASK_TYPE_NEW_ITEM
          ? `&taskType=${taskType}`
          : ""
      }`
    );
  };

  const checkIfFieldIsEmptyAtHeaderLevel = () => {
    //Checks if the headerOnlyAmtsArr contains empry/ null values
    const hasNullOrEmptyElement = _.some(headerOnlyAmtsArr, function (element) {
      return (
        _.isNull(element) ||
        (_.isString(element) && element.trim().length === 0)
      );
    });
    return hasNullOrEmptyElement;
  };

  const checkIsEmptyField = ({ emptyFieldsObj }) => {
    //In case of HF, we need to check field at header level
    const { headerOnlyAmt } = textMapping({ allowanceTempWorkData });

    if (headerOnlyAmt && headerOnlyAmt.headerOnlyAmtKey) {
      return checkIfFieldIsEmptyAtHeaderLevel();
    }

    return _.isEmpty(emptyFieldsObj.emptyFieldsDetails) ? false : true;
  };

  const check_isAnyAmountFieldEmtpy = () => {
    const emptyFieldsObj = getEmptyFields(
      {
        tableData,
      },
      allowGroupConfigObj?.isDistCenter
    );
    dispatch(
      setEmptyFieldsList({
        ...emptyFieldsObj,
      })
    );

    return checkIsEmptyField({ emptyFieldsObj });
  };

  const getAllowanceTempFromTable = () => {
    return saveAllowancesItemReqBody({
      allowancesRespCopy: generateAllowanceTempObject({
        tableData,
        dataFromAmountsAPI: allowancesRespCopy,
        switchValue,
        allowGroupConfigObj,
        headerOnlyAmtsArr,
        tempAllowItems,
        isAllowConvEnable,
      }),
      allowanceTempWorkData: _.cloneDeep(
        allowanceTempWorkData?.allowanceData
          ? allowanceTempWorkData?.allowanceData
          : allowanceTempWorkData
      ),
      isEditEnable,
    });
  };

  const handle_saveAllowances = async () => {
    if (leadDistributorsMode === efConstants.LEAD_DIST_LABEL) {
      await dispatch(
        setIsLeadDistributorError({ isLeadDistributorError: false })
      );
      const isInvalidLeadDists = isInValidLeadDists(tableData);
      if (isInvalidLeadDists) {
        dispatch(setIsLeadDistributorError({ isLeadDistributorError: true }));
        return;
      }
    }
    const isAnyAmountFieldEmtpy = check_isAnyAmountFieldEmtpy();
    const isAnyColumnAllZero = check_isAnyColumnAllZero(
      tableData,
      allowGroupConfigObj?.showLeadDistributorSection
    );
    if (
      isDsd &&
      !validateIfAllOtherVendorsAssignedLeads({ tableData: tableData })
    ) {
      setInValidOtherVendorError(true);
      return;
    }

    if (isAnyAmountFieldEmtpy) {
      //Would be true, if any field is cleared or empty
      setIsDisplayFieldAlert(true);
      dispatch(setSelectedFilters({ isFilterReset: true }));
    } else if (!isAnyAmountFieldEmtpy) {
      //If user clicks on update All button / Data has all non empty values
      setIsDisplayFieldAlert(false);
      //TODO:Allowances
      if (isAnyColumnAllZero?.length) {
        setIsAmountsError(true);
        return;
      }
      const data = getAllowanceTempFromTable();
      await postAllowanceTempWorkData(data);
    }
  };
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const onClosePopup = () => {
    setIsDisplayFieldAlert(false);
    setInValidOtherVendorError(false);
  };
  const navigateOnCancel = () => {
    if (isComingFromtask(taskType, eventDataResp?.planEvent) && isEditEnable) {
      setIsPopupOpen(true);
    } else {
      onChangeHandler();
    }
  };

  function getAlertDetails(): any {
    if (isDisplayFieldAlert) {
      return {
        variant: "error",
        msg: ` One or more required fields are missing! Please update those fields
          below before you submit again.`,
      };
    } else if (isSaveSuccess) {
      return {
        variant: "success",
        msg: `Successfully submitted`,
      };
    } else if (isSaveApiError) {
      return {
        variant: "error",
        msg: saveApiError?.data?.message || saveApiError?.message,
      };
    } else if (isAmountsError) {
      return {
        variant: "error",
        msg: "Overall allowance amount cannot be zero",
      };
    } else if (inValidOtherVendorError) {
      return {
        variant: "error",
        msg: "You have chosen to add additional Vendor Cost Areas and need to specify where they are to be Billed to.  Please utilize the Lead Distributor(s) Only or Billing Selections function to provide where to Bill them to",
      };
    } else {
      return { variant: null, msg: null };
    }
  }

  const SaveStatus = () => {
    const isOpen =
      isDisplayFieldAlert ||
      isSaveSuccess ||
      isSaveApiError ||
      isAmountsError ||
      inValidOtherVendorError;
    if (!isOpen) {
      return false;
    }
    const { variant, msg } = getAlertDetails();
    return (
      <Alert
        isOpen={isOpen}
        dismissible={true}
        variant={variant}
        onClose={onClosePopup}
      >
        {msg}
      </Alert>
    );
  };

  const cancelBtn = (
    <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 abs-ef-all-allowances-top-right-cancel-btn">
      <div className="flex flex-col justify-center items-center flex-grow-0 flex-shrink-0 h-10 overflow-hidden gap-2.5 px-3 py-2.5 rounded-lg">
        <button
          onClick={() => navigateOnCancel()}
          className="flex-grow-0 flex-shrink-0 text-base font-semibold text-center text-[#1b6ebb]"
          data-testid="cta-navigate-to-cancel"
        >
          Cancel
        </button>
      </div>
    </div>
  );
  const saveBtn = (
    <div
      className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative abs-ef-all-allowances-top-right-save-btn"
      data-testid="cta-atr-save-allowance"
    >
      <div className="absolute z-50 right-[244px]">{SaveStatus()}</div>
      <Button
        width={190}
        disabled={!tableData?.length}
        onClick={handle_saveAllowances}
      >
        Save All Changes
      </Button>
    </div>
  );

  const onChangeHandler = async () => {
    clearTableSlices();
    if (isComingFromtask(taskType, eventDataResp?.planEvent) && isEditEnable) {
      const onDeleteTemp =
        (await allowanceTempWorkData?.tempWorkAllowanceId) &&
        deleteAllowanceTempWorkData({
          URL_PARAM: allowanceTempWorkData?.tempWorkAllowanceId,
        });
      onDeleteTemp &&
        dispatch(
          offerCardConfiguration({
            offerData: `${offerId}_${_.uniqueId()}`,
          })
        );
      navigate(
        `${basePath}/events/edit/${eventId}?${
          taskType === appConstants.TASK_TYPE_NEW_ITEM
            ? `taskType=${taskType}`
            : ""
        }`
      );
    } else {
      const allowanceTypeChangedParam = isAllowanceChanged
        ? `&isAllowTypeChange=${isAllowanceChanged}`
        : "";
      const isOverlapsAvailable =
          allowancesRespCopy?.offerAllowanceOverlapResults
            ?.offerAllowanceOverlaps?.length > 0,
        isItemSummarized =
          allowancesRespCopy?.summary?.itemAmountsCouldBeSummarized,
        redirectToMainEntry =
          !isOverlapsAvailable && isItemSummarized === false;
      const isRedirectToMainEntryParam =
        appConstants.IS_ENABLE_REDESIGN_ALLOWANCE_CHANGES
          ? `&isRedirectToMainEntry=${redirectToMainEntry}`
          : "";
      navigate(
        `${basePath}/events/edit/${eventId}?isCancelled=true&offerKey=${offerGroup}&offerId=${offerNumber}${allowanceTypeChangedParam}${isRedirectToMainEntryParam}`
      );
    }
  };

  const onCloseHandler = () => {
    setIsPopupOpen(false);
  };

  const showPopup = (
    <div className="abs-ef-all-allowances-top-right-show-popup">
      {isPopupOpen ? (
        <CommonModal
          isModalPopupOpen={isPopupOpen}
          setModalPopupOpen={setIsPopupOpen}
          title={OFFER_POPUP_MESSAGES?.TITLE}
          confirmBtnTitle={OFFER_POPUP_MESSAGES?.CONFIRM_BTN_TITLE}
          cancelBtnTitle={OFFER_POPUP_MESSAGES?.CANCEL_BTN_TITLE}
          showHideBtns={true}
          height={316}
          onClose={onCloseHandler}
          cancelBtnHandler={onCloseHandler}
          modalNameHandler={onChangeHandler}
          isCancelAmountStep={true}
          minBtnWidth={240}
        />
      ) : null}
    </div>
  );

  const renderDetails = {
    isApiLoading: isSaveLoading || isEventDataFetched,
    isPageLevelSpinner: true,
    isRenderMainHtml: true,
    renderHtml: (
      <div className="flex justify-end items-end gap-4 abs-ef-all-allowances-top-right-render-details-container">
        {showPopup}
        {cancelBtn}
        {saveBtn}
      </div>
    ),
  };
  return <RenderStates details={renderDetails} />;
}
