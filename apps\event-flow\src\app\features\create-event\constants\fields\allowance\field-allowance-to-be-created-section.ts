import efConstants from "../../../../../shared/ef-constants/ef-constants";

const { ALLOWANCE_TYPES } = efConstants;
const verbiage = {
  SCAN: "Select DSD Separate to create separate allowances that can be Billed for each Distributor or choose Combined to create only one Allowance.",
  CASE: {
    DSD: "Select DSD Separate to create separate allowances that can be Billed for each Distributor or choose Combined to create only one Allowance.",
    BOTH: "Your selected Products and Store Group(s) are serviced by both DSD and Warehouse Items, if you wish to not create both Allowances change the default from Both. Be advised that this will result in some Store/Product promotions not being funded.",
  },
  SHIPTOSTORE:
    "Select Separate Allowances By DSD Distributor to create separate Allowances that can be billed by Distributor, or choose Combined to create only one Allowance.",
};

export const ALLOWANCE_TO_BE_CREATED_OPTIONS = {
  CASE: {
    warehouseOnly: "Warehouse Only",
    dsdOnly: "DSD Only",
    oneAllowance: "One Allowance: DSD Combined",
    separateAllowances: "Separate Allowances By DSD Distributor",
    both: "Both",
  },
  SCAN: {
    oneAllowance: "One Allowance: Warehouse, DSD, or Combined",
    separateAllowances: "Separate Allowances By DSD Distributor",
  },
  SHIPTOSTORE: {
    oneAllowance: "Combined DSD",
    separateAllowances: "Separate Allowances By DSD Distributor",
  },
};

export const SHOW_ALLOWANCE_TO_BE_CREATED_OPTIONS = {
  [ALLOWANCE_TYPES.CASE.key]: {
    WAREHOUSE: {
      name: ALLOWANCE_TO_BE_CREATED_OPTIONS.CASE.warehouseOnly,
      key: "WAREHOUSE_DIST_CENTERS",
      registerField: "allowanceToBeCreated",
      createIndex: "CW",
      disable: true,
      text: verbiage.CASE.BOTH,
      options: [
        {
          name: ALLOWANCE_TO_BE_CREATED_OPTIONS.CASE.warehouseOnly,
          key: "WAREHOUSE_DIST_CENTERS",
          routingKey: "WAREHOUSE_DIST_CENTERS",
          default: true,
          allowanceMap: {
            WAREHOUSE_DIST_CENTERS: [],
          },
          createIndex: "CW",
        },
      ],
    },
    DSD: {
      name: ALLOWANCE_TO_BE_CREATED_OPTIONS.CASE.oneAllowance,
      key: "DSD_WHSE_RETAIL_DIVISION",
      registerField: "allowanceToBeCreated",
      disable: false,
      createIndex: "CC",
      text: verbiage.CASE.DSD,
      options: [
        {
          name: ALLOWANCE_TO_BE_CREATED_OPTIONS.CASE.oneAllowance,
          key: "DSD_WHSE_RETAIL_DIVISION",
          routingKey: "DSD_WHSE_RETAIL_DIVISION",
          allowanceMap: {
            DSD_WHSE_RETAIL_DIVISION: [],
          },
          default: false,
          createIndex: "CC",
        },
        {
          name: ALLOWANCE_TO_BE_CREATED_OPTIONS.CASE.separateAllowances,
          key: "DSD_LEAD_DISTRIBUTORS",
          routingKey: "DSD_LEAD_DISTRIBUTORS",
          allowanceMap: {
            DSD_LEAD_DISTRIBUTORS: [],
          },
          default: false,
          createIndex: "CD",
        },
      ],
    },
    BOTH: {
      name: ALLOWANCE_TO_BE_CREATED_OPTIONS.CASE.both,
      key: "BOTH",
      registerField: "allowanceToBeCreated",
      createIndex: "CB",
      disable: false,
      text: verbiage.CASE.BOTH,
      options: [
        {
          name: ALLOWANCE_TO_BE_CREATED_OPTIONS.CASE.both,
          key: "BOTH",
          routingKey: "DSD_LEAD_DISTRIBUTORS",
          default: false,
          allowanceMap: {
            DSD_LEAD_DISTRIBUTORS: [],
            WAREHOUSE_DIST_CENTERS: [],
          },
          createIndex: "CB",
        },
        {
          name: ALLOWANCE_TO_BE_CREATED_OPTIONS.CASE.warehouseOnly,
          key: "WAREHOUSE_DIST_CENTERS",
          routingKey: "WAREHOUSE_DIST_CENTERS",
          default: false,
          allowanceMap: {
            WAREHOUSE_DIST_CENTERS: [],
          },
          createIndex: "CW",
        },
        {
          name: ALLOWANCE_TO_BE_CREATED_OPTIONS.CASE.dsdOnly,
          key: "DSD_LEAD_DISTRIBUTORS",
          routingKey: "DSD_LEAD_DISTRIBUTORS",
          allowanceMap: {
            DSD_LEAD_DISTRIBUTORS: [],
          },
          default: false,
          createIndex: "CD",
        },
      ],
    },
    DSD_ONLY: {
      name: "Separate Allowances By DSD Distributor",
      key: "DSD_LEAD_DISTRIBUTORS",
      registerField: "allowanceToBeCreated",
      disable: true,
      createIndex: "CD",
      text: verbiage.CASE.DSD,
      options: [
        {
          name: "Separate Allowances By DSD Distributor",
          key: "DSD_LEAD_DISTRIBUTORS",
          routingKey: "DSD_LEAD_DISTRIBUTORS",
          allowanceMap: {
            DSD_LEAD_DISTRIBUTORS: [],
          },
          default: true,
          createIndex: "CD",
        },
      ],
    },
  },
  [ALLOWANCE_TYPES.SCAN.key]: {
    WAREHOUSE: {
      name: ALLOWANCE_TO_BE_CREATED_OPTIONS.SCAN.oneAllowance,
      key: "DSD_WHSE_RETAIL_DIVISION",
      registerField: "allowanceToBeCreated",
      createIndex: "TC",
      disable: true,
      text: verbiage.SCAN,
      options: [
        {
          name: ALLOWANCE_TO_BE_CREATED_OPTIONS.SCAN.oneAllowance,
          key: "DSD_WHSE_RETAIL_DIVISION",
          routingKey: "DSD_WHSE_RETAIL_DIVISION",
          allowanceMap: {
            DSD_WHSE_RETAIL_DIVISION: [],
          },
          default: true,
          createIndex: "TC",
        },
      ],
    },
    DSD: {
      name: ALLOWANCE_TO_BE_CREATED_OPTIONS.SCAN.separateAllowances,
      key: "DSD_LEAD_DISTRIBUTORS",
      registerField: "allowanceToBeCreated",
      disable: false,
      createIndex: "TS",
      text: verbiage.SCAN,
      options: [
        {
          name: ALLOWANCE_TO_BE_CREATED_OPTIONS.SCAN.oneAllowance,
          key: "DSD_WHSE_RETAIL_DIVISION",
          routingKey: "DSD_WHSE_RETAIL_DIVISION",
          allowanceMap: {
            DSD_WHSE_RETAIL_DIVISION: [],
          },
          default: false,
          createIndex: "TC",
        },
        {
          name: ALLOWANCE_TO_BE_CREATED_OPTIONS.SCAN.separateAllowances,
          key: "DSD_LEAD_DISTRIBUTORS",
          routingKey: "DSD_LEAD_DISTRIBUTORS",
          allowanceMap: {
            DSD_LEAD_DISTRIBUTORS: [],
          },
          default: false,
          createIndex: "TS",
        },
      ],
    },
    BOTH: {
      name: ALLOWANCE_TO_BE_CREATED_OPTIONS.SCAN.oneAllowance,
      key: "DSD_WHSE_RETAIL_DIVISION",
      registerField: "allowanceToBeCreated",
      createIndex: "TC",
      disable: true,
      text: verbiage.SCAN,
      options: [
        {
          name: ALLOWANCE_TO_BE_CREATED_OPTIONS.SCAN.oneAllowance,
          key: "DSD_WHSE_RETAIL_DIVISION",
          routingKey: "DSD_WHSE_RETAIL_DIVISION",
          allowanceMap: {
            DSD_WHSE_RETAIL_DIVISION: [],
          },
          default: true,
          createIndex: "TC",
        },
      ],
    },
  },
  [ALLOWANCE_TYPES.SHIPTOSTORE.key]: {
    WAREHOUSE: {
      name: ALLOWANCE_TO_BE_CREATED_OPTIONS.SHIPTOSTORE.oneAllowance,
      key: "DSD_WHSE_RETAIL_DIVISION",
      registerField: "allowanceToBeCreated",
      createIndex: "SB",
      disable: true,
      text: verbiage.SHIPTOSTORE,
      options: [
        {
          name: ALLOWANCE_TO_BE_CREATED_OPTIONS.SHIPTOSTORE.oneAllowance,
          key: "DSD_WHSE_RETAIL_DIVISION",
          routingKey: "DSD_WHSE_RETAIL_DIVISION",
          allowanceMap: {
            DSD_WHSE_RETAIL_DIVISION: [],
          },
          default: true,
          createIndex: "SB",
        },
      ],
    },
    DSD: {
      name: ALLOWANCE_TO_BE_CREATED_OPTIONS.SHIPTOSTORE.separateAllowances,
      key: "DSD_LEAD_DISTRIBUTORS",
      registerField: "allowanceToBeCreated",
      disable: false,
      createIndex: "SD",
      text: verbiage.SHIPTOSTORE,
      options: [
        {
          name: ALLOWANCE_TO_BE_CREATED_OPTIONS.SHIPTOSTORE.oneAllowance,
          key: "DSD_WHSE_RETAIL_DIVISION",
          routingKey: "DSD_WHSE_RETAIL_DIVISION",
          allowanceMap: {
            DSD_WHSE_RETAIL_DIVISION: [],
          },
          default: false,
          createIndex: "SB",
        },
        {
          name: ALLOWANCE_TO_BE_CREATED_OPTIONS.SHIPTOSTORE.separateAllowances,
          key: "DSD_LEAD_DISTRIBUTORS",
          routingKey: "DSD_LEAD_DISTRIBUTORS",
          allowanceMap: {
            DSD_LEAD_DISTRIBUTORS: [],
          },
          default: false,
          createIndex: "SD",
        },
      ],
    },
    BOTH: {
      name: ALLOWANCE_TO_BE_CREATED_OPTIONS.SHIPTOSTORE.oneAllowance,
      key: "DSD_WHSE_RETAIL_DIVISION",
      registerField: "allowanceToBeCreated",
      createIndex: "SB",
      disable: true,
      text: verbiage.SHIPTOSTORE,
      options: [
        {
          name: ALLOWANCE_TO_BE_CREATED_OPTIONS.SHIPTOSTORE.oneAllowance,
          key: "DSD_WHSE_RETAIL_DIVISION",
          routingKey: "DSD_WHSE_RETAIL_DIVISION",
          allowanceMap: {
            DSD_WHSE_RETAIL_DIVISION: [],
          },
          default: true,
          createIndex: "SB",
        },
      ],
    },
  },
  [ALLOWANCE_TYPES.HEADERFLAT.value]: [],
  [ALLOWANCE_TYPES.ITEMFLAT.value]: [],
};

export const ALLOWANCE_TO_BE_CREATED = {
  allowanceToBeCreated: {
    name: "",
    key: "",
    required: true,
    registerField: "allowanceToBeCreated",
    registerKeyName: "allowanceToBeCreated",
    createIndex: "",
    disable: true,
    text: verbiage.CASE.BOTH,
    options: [],
    errors: {
      required: {
        backgroundColor: "",
        text: "Please select Allowance To Be Created option",
      },
    },
  },
  allowanceSpecificFields: {
    ...SHOW_ALLOWANCE_TO_BE_CREATED_OPTIONS,
  },
};
