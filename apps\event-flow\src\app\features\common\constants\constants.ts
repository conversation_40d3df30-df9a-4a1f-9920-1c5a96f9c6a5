export interface MultiSelectConfig {
  title: string;
  id: string;
  uniqueId: string;
  type: "checkBox" | "radio" | string; // extend as needed
  showSearchBox: boolean;
  searchPlaceholder: string;
  showSelectAllFeature: boolean;
  dataSliceName: string;
  selectedDataSlice: string;
  displayKey: string;
  searchItemKey: string;
  selectDeselectLabel: string;
  isRequired: boolean;
  requiredErrorMsg: string;
}


export const multiSelectConfigObj: MultiSelectConfig[] = [
  {
    title: "Division",
    id: "storeGroup",
    uniqueId: "id",
    type: "checkBox",
    showSearchBox: true,
    searchPlaceholder: "Search Store Groups",
    showSelectAllFeature: true,
    dataSliceName: "store_groups_data",
    selectedDataSlice: "selected_store_groups",
    displayKey:"label",
    searchItemKey:"label",
    selectDeselectLabel:"Select/Deselect All",
    isRequired: true,
    requiredErrorMsg: "Store groups required",
  },
]