import { useEffect, useRef, useState, memo, useMemo } from "react";
import LoadingSpinner from "../../../../../constants/LoadingSpinner/LoadingSpinner";
import {
  getAllowanceKey,
  getAllowanceMapKey,
  isHfOrIfType,
} from "../../../../../service/allowance/allowance-service";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";
import { useSelectorWrap } from "@me/data-rtk";
import { cloneDeep } from "lodash";
import useAllowTempworkUpdate from "../../../../../hooks/useAllowTempworkUpdate";
import AllowanceDatesInitialText from "../../../allowance/stepper/common-stepper/allowance-dates/allowance-dates-initial-text";
import BaseAllowanceDates from "./base-allowance-dates";
import { OFFER_FORM_FIELDS } from "../../offer-flow-config";
import {
  checkAmountRefreshCondition,
  compareVehicleDataOfEventAndOffer,
  compareVehicleDates,
  getDatesVehicleForTemp,
  getNationalVehicleForTemp,
  getOfferMapKey,
  getUpdatedPerfValues,
} from "../../offer-service";
import AdditionalDatesSection from "./additional-dates-section";
import {
  allowancePerfConfigHandler,
  resetOfferAmountsData,
} from "../../../../../service/slice/allowance-details-slice";
import { useDispatch } from "react-redux";
import { checkIsNationalEvent } from "../../offer-service";
import NationalAdditionalDatesSection from "../../national/dates/national-additional-dates-section";
import useNationalAllowTempUpdate from "../../../../../hooks/useNationalAllowTempUpdate";
import { getObjectKeys } from "../../../../../service/allowance/allowance-stepper-service";

const AllowanceDatesSection = ({
  cardIndex,
  cardItemIndex,
  formControls,
  sectionConfiguration,
  sectionKey,
  isEditEnable = false,
  allowanceRegField,
  values,
}) => {
  const dispatch = useDispatch();

  // read data from redux store
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { data: allowancePerfData } = useSelectorWrap(
    "allowance_type_performance_data"
  );
  const { productSources = [] } =
    useSelectorWrap("allowance_product_sources_rn")?.data || {};
  const { isAllowConvEnable } = useSelectorWrap("allow_conv_status_rn").data;
  const { data: allowanceForm } = useSelectorWrap("allowance_form_data");
  const { allowanceData: tempworkData } =
    useSelectorWrap("allowance_temp_work")?.data || {};
  const { offerDivisions = [] } =
    useSelectorWrap("national_offer_divisions")?.data || {};

  // API call to save data in temp
  const { getTempworkObject, saveAllowancePrimeSectionData, isLoading } =
    useAllowTempworkUpdate();
  const {
    getNationalTempworkObject,
    saveNationalAllowancePrimeSectionData,
    isLoading: isNationalTempLoading,
  } = useNationalAllowTempUpdate();

  // read data from configuration
  const { initialText } = sectionConfiguration?.fields || {};
  const { perfConfigKey, createIndKey, allowanceToBeCreatedOptionKey } =
    OFFER_FORM_FIELDS;
  const {
      ALLOWANCE_TYPES,
      NEW_DIRECTION_FEATURES_FLAGS,
      ALLOWANCE_SCREEN_TYPES,
    } = efConstants,
    { isDirectionalChangesEnable } = NEW_DIRECTION_FEATURES_FLAGS,
    { CASE } = ALLOWANCE_TYPES,
    { DP, NDP } = ALLOWANCE_SCREEN_TYPES;

  // local state
  const [vehicleFields, setVehicleFields] = useState({
    vehicleTypeId: "",
    startDate: "",
    endDate: "",
    vehicleType: {},
    vehicleTabType: "",
    vehicleId: "",
    vehicleNm: "",
    vehicleTypeDesc: "",
    year: "",
  });
  const isFirstLoad = useRef(true);
  const [enableAddintionalDates, setEnableAdditionalDates] = useState(false);

  // get data from formControls
  const { getValues = () => null, setValue = () => null } = formControls || {};
  const formData = getValues() || {};
  const {
    allowanceType = "",
    performance = "",
    overrideHeaderFlatAmt = "",
  } = formData || {};
  const allowanceTypeKey = getAllowanceKey(allowanceType?.toUpperCase());
  const allowName = getAllowanceMapKey(allowanceTypeKey) || "";
  const perfOption = formData?.[perfConfigKey];
  const allowanceToBeCreatedOption = formData?.[allowanceToBeCreatedOptionKey];
  const defaultPerfConfig =
    allowancePerfData?.performanceConfig?.[allowanceRegField];
  const defaultCreateInd = formData?.[createIndKey];
  const offerMapKey = getOfferMapKey(
    allowanceToBeCreatedOption?.createIndex || defaultCreateInd
  );
  const isHfIfType = isHfOrIfType(allowanceTypeKey);
  const allowanceFormData =
    allowanceForm?.allowanceFormData?.[allowanceRegField];
  const isNationalEvent = checkIsNationalEvent(eventDetailsData?.eventType);
  const nationalVehicles = eventDetailsData?.nationalInfos?.nationalDivs || [];

  // HF - Check if the amount refresh is required
  const amountRefreshCheck = checkAmountRefreshCondition(
    defaultCreateInd,
    allowanceTypeKey
  );

  const getUpdatedPerfDetails = () => {
    const allowancePerformance = {
      allowanceType: allowanceType,
      ...perfOption,
      performance,
      performanceConfig: defaultPerfConfig,
    };

    // Get updated performance values based on the allowance type(CASE - BOTH).
    const {
      allowancePerformances,
      offerAllowancesGroupInfoMap = {},
      isDefaultPerf,
    } = getUpdatedPerfValues(
      allowanceType?.toUpperCase(),
      allowanceTypeKey,
      eventDetailsData?.eventType,
      allowanceToBeCreatedOption?.createIndex,
      isEditEnable,
      allowancePerformance,
      {},
      allowancePerfData?.getAllowancePerformanceTypes || [],
      productSources
    );

    if (!isDefaultPerf) {
      setValue("performance", allowancePerformances?.performance);
      setValue(perfConfigKey, allowancePerformances);
      dispatch(
        allowancePerfConfigHandler({
          performanceConfig: {
            [allowanceRegField]: allowancePerformances?.performanceConfig,
          },
        })
      );
    }
    return {
      allowancePerformances,
      offerAllowancesGroupInfoMap,
    };
  };

  const saveNationalVehicleDates = () => {
    const nationalVehicleMapData = getNationalVehicleForTemp(
      offerDivisions,
      nationalVehicles,
      offerMapKey
    );

    if (
      !isEditEnable
        ? tempworkData?.[0]?.allowanceTypeSpecification?.[allowName]
            ?.allowancesMap?.[offerMapKey]?.length
        : compareVehicleDataOfEventAndOffer(
            nationalVehicleMapData,
            tempworkData,
            allowName,
            offerMapKey,
            isEditEnable
          )
    ) {
      setEnableAdditionalDates(true);
      isFirstLoad.current = true;
      return;
    }

    saveDatesInToNationalTemp(nationalVehicleMapData);
    return nationalVehicleMapData;
  };

  async function saveDatesInToNationalTemp(nationalVehicleMaps) {
    const vehicleKeys = getObjectKeys(nationalVehicleMaps);
    try {
      const { allowancePerformances, offerAllowancesGroupInfoMap } =
        getUpdatedPerfDetails();

      // Initial TEMPWORK construction when there is no temp data or dependent fields are changed related to date.
      const updatedTempwork = vehicleKeys?.map(key => {
        return getNationalTempworkObject({
          allowanceTypeKey,
          allowancePerformanceId: allowancePerformances?.id,
          allowancePerformances,
          offerAllowancesGroupInfoMap,
          allowanceToBeCreatedOption,
          amountRefreshCheck,
          overrideHeaderFlatAmt,
          productSources,
          vehicleDatesMap: nationalVehicleMaps?.[key],
          additionalDates: [],
          allowKey: allowName,
          offerMapKey,
          isEditEnable,
          divisionId: key,
          planEventId: eventDetailsData?.id,
          planEventType: "NDP",
        });
      });

      const result = await saveNationalAllowancePrimeSectionData(
        updatedTempwork
      );
      if (result?.data?.[0]?.allowanceType) {
        dispatch(resetOfferAmountsData());
        isFirstLoad.current = true;
        setEnableAdditionalDates(true);
      }
    } catch (e) {
      console.log(e);
    }
  }

  // Save allowance, performance, createInd & dates in to temp
  async function saveDatesInToTemp(vehicleDatesMap) {
    const formVehicle =
      allowanceFormData?.allowanceCreationVehicle?.[offerMapKey];
    if (
      formVehicle?.vehicleId &&
      !compareVehicleDates(formVehicle, vehicleDatesMap?.[offerMapKey])
    ) {
      setEnableAdditionalDates(true);
      isFirstLoad.current = true;
      return;
    }

    try {
      const { allowancePerformances, offerAllowancesGroupInfoMap } =
        getUpdatedPerfDetails();

      // Initial TEMPWORK construction when there is no temp data or dependent fields are changed related to date.
      const updatedTempwork = getTempworkObject({
        allowanceTypeKey,
        allowancePerformanceId: allowancePerformances?.id,
        allowancePerformances,
        offerAllowancesGroupInfoMap,
        allowanceToBeCreatedOption,
        allowKey: allowName,
        amountRefreshCheck,
        overrideHeaderFlatAmt,
        productSources,
        vehicleDatesMap,
        isEditEnable,
      });
      const result = await saveAllowancePrimeSectionData(updatedTempwork);
      if (result?.data?.allowanceType) {
        isFirstLoad.current = true;
        setEnableAdditionalDates(true);
      }
    } catch (e) {
      console.log(e);
    }
  }

  // Initial load of the component
  const handleInitialStepLoad = async () => {
    setEnableAdditionalDates(false);

    if (isHfIfType) return;

    if (
      // (!isEmpty(datesInitialValueOnLoad) &&
      !isDirectionalChangesEnable &&
      !isAllowConvEnable
    ) {
      setEnableAdditionalDates(true);
      return;
    }

    if (!isAllowConvEnable) {
      isFirstLoad.current = false;
      return isNationalEvent
        ? await saveNationalVehicleDates()
        : await saveDatesInToTemp(
            getDatesVehicleForTemp(
              cloneDeep(eventDetailsData?.eventCreationVehicle),
              offerMapKey
            )
          );
    }
  };

  // Initial load of the component
  useEffect(() => {
    allowanceTypeKey &&
      allowanceToBeCreatedOption?.key &&
      perfOption?.id &&
      isFirstLoad.current &&
      (CASE.key === allowanceTypeKey ||
        values?.[2]?.length ||
        [DP.key, NDP.key].includes(eventDetailsData?.eventType)) &&
      handleInitialStepLoad();
  }, [JSON.stringify(values)]);

  const AdditionalDatesComponent = useMemo(
    () => (
      <AdditionalDatesSection
        cardIndex={cardIndex}
        cardItemIndex={cardItemIndex}
        formControls={formControls}
        sectionConfiguration={sectionConfiguration}
        isEditEnable={isEditEnable}
        offerMapKey={offerMapKey}
        vehicleFields={vehicleFields}
        sectionKey={sectionKey}
      />
    ),
    [enableAddintionalDates]
  );

  const NationalAdditionalDatesComponent = useMemo(
    () => (
      <NationalAdditionalDatesSection
        cardIndex={cardIndex}
        cardItemIndex={cardItemIndex}
        formControls={formControls}
        sectionConfiguration={sectionConfiguration}
        isEditEnable={isEditEnable}
        offerMapKey={offerMapKey}
        vehicleFields={vehicleFields}
        sectionKey={sectionKey}
      />
    ),
    [enableAddintionalDates]
  );

  return (
    <div>
      <div className="text-xl text-[#3997EF] font-bold my-3">
        {sectionConfiguration?.label || ""}
      </div>

      <LoadingSpinner
        isLoading={isLoading || isNationalTempLoading}
        classname="!h-full !w-full rounded-md"
      />
      <AllowanceDatesInitialText
        allowanceType={allowanceTypeKey}
        option={offerMapKey}
        stepperElement={{ fields: { initialText } }}
        className="py-4 px-3 bg-[#F3F4F6]"
      />
      <BaseAllowanceDates
        formControls={formControls}
        allowanceRegField={allowanceRegField}
        sectionConfiguration={sectionConfiguration}
        isEditEnable={isEditEnable}
        offerMapKey={offerMapKey}
        vehicleFields={vehicleFields}
        setVehicleFields={setVehicleFields}
      />
      {!isHfIfType &&
      vehicleFields?.vehicleId &&
      (enableAddintionalDates || isAllowConvEnable)
        ? !isNationalEvent
          ? AdditionalDatesComponent
          : NationalAdditionalDatesComponent
        : null}
    </div>
  );
};

export default memo(AllowanceDatesSection);
