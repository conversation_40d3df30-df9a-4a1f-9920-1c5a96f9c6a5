import { useSelectorWrap } from "@me/data-rtk";
import UOMField from "./shared/components/uom-field";
import InValidAmountTooltip from "../../../../../../../features/all-allowances/allowance-table/invalid-amount-tooltip";
import { dashedVerticalDivider } from "../../../allowance/stepper/common-stepper/allowance-amount/allowance-amounts-services";
import AllowanceAmountField from "./shared/components/allowance-amount-field";
import AmountsToggleSwitch from "../../../allowance/stepper/common-stepper/allowance-amount/allowance-amounts-options/amounts-toggle-switch";
import { Timer } from "lucide-react";
import AmountDisplay from "./shared/components/amount-display";
import TopAmountSection from "./shared/components/top-amount-section";
import RenderOverlapsTable from "./shared/components/render-overlaps-table";
import { multiplier } from "../../offer-service";
import { memo, useEffect, useMemo } from "react";
import UserActions from "./shared/components/user-actions";
import { IBaseAmtProp } from "./shared/props-types";
import { useHandleAmountFieldsChange } from "../../hooks/amounts/useHandleAmountFieldsChange";
import { isEmpty } from "lodash";
import MultiCostMessage from "./shared/components/multi-cost-message";
import { getIsNationalEvent } from "../../../../../../event-types/event-types-helper";

const BaseAllowanceAmount = (props: IBaseAmtProp) => {

  let {
    cardIndex = 0,
    cardItemIndex = 0,
    isEditEnable = false,
    sectionConfiguration,
    formControls,
    isZeroCost,
    allowanceType,
    allowancesResp,
    editViewAllItems,
    allowanceAmountData,
    setAllowanceAmountData,
    allowanceAmountFields,
    setAllowanceAmountFields,
    amountsInitialValueOnLoad,
    isSectionCompleted = false,
    amtSubLabelDisplayVal,
    isAmtSavedInTemp
  } = props;
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { isNDPAndFlagEnabled: isNational } = getIsNationalEvent(eventDetailsData?.eventType);
  const getValidAllowResp = allowancesResp => {
    return allowancesResp?.find(
      allowanceObj =>
        allowanceObj?.offerAllowanceOverlapResults?.offerAllowanceOverlaps
          ?.length > 0
    ) || allowancesResp?.[0];
  };
  allowancesResp =
    isNational && Array.isArray(allowancesResp)
      ? getValidAllowResp(allowancesResp)
      : allowancesResp;
  const searchId = eventDetailsData?.offerAllowances?.[cardIndex]?.offerNumber;
  const isInValidOffer = eventDetailsData?.inValidAllowances?.includes(
    eventDetailsData?.offerAllowances?.[cardIndex]?.id
  );
  const isHideFieldsForMainEntry = isNational ? true :
    allowancesResp?.summary?.itemAmountsCouldBeSummarized === false;
  const isOverlaps = allowancesResp?.offerAllowanceOverlapResults?.offerAllowanceOverlaps
        ?.length > 0;
  const {
    fields: { allowanceAmount, uom },
    description = {},
  } = sectionConfiguration;
  const getEventData = () => {
    return eventDetailsData;
  };
  const {
    // State
    digitError,
    calcError,
    switchEnabled,
    setSwitchEnabled,
    switchValue,
    areInputsDisabled,
    switchObj,
    cost,
    costAllow,
    defaultToggleValue,

    // Handlers
    handleAmountChange,
    handleUOMChange,
    calculateFinalCost,

    // UOM options
    getUOMOptions,
  } = useHandleAmountFieldsChange({
    allowancesResp,
    allowanceType,
    formControls,
    initialValues: amountsInitialValueOnLoad,
    setAllowanceAmountData,
    allowanceAmountFields,
    setAllowanceAmountFields,
    cardIndex,
    cardItemIndex,
    searchId,
    eventDetailsData,
    fieldProp: allowanceAmount,
    isAmtSavedInTemp,
    isEditEnable
  });

  const memoedFunc = useMemo(()=>{
    return editViewAllItems
  },[])

  useEffect(() => {
    formControls?.setValue(uom?.registerField, allowanceAmountData?.uom);
  }, [allowanceAmountData?.uom]);

  const { costLabel, costAllowLabel, newCostAllowLabel } =
    switchObj?.labels || {};
  const multiplierVal = multiplier({
    areInputsDisabled,
    allowancesResp,
    allowanceType,
  });
  const { BASE_DESC = "" } = description;

  const renderTopSection = (
    <>
      <TopAmountSection
        description={BASE_DESC}
        baseId="abs-base-allowance-amount"
      >
        <div className="mr-0 mb-5 mt-5 flex justify-between items-center">
          {!isZeroCost && !isHideFieldsForMainEntry && (
            <AmountDisplay
              label={costLabel}
              value={cost}
              icon={<Timer size={15} className="ml-1 text-brand-light" />}
              baseId="abs-basic-allowance-amount"
            />
          )}

          {isOverlaps && (
            <div className="ml-auto">
              <AmountsToggleSwitch
                {...{
                  switchEnabled,
                  setSwitchEnabled,
                  switchValue,
                  defaultToggleValue,
                }}
              />
            </div>
          )}
        </div>
      </TopAmountSection>
    </>
  );
  const renderMiddleSection = (
    <RenderOverlapsTable
      allowancesResp={allowancesResp}
      switchEnabled={switchEnabled}
    />
  );
  const renderBottomSection = (
    <>
      {isHideFieldsForMainEntry && <MultiCostMessage />}
      <div
        className="flex mt-4 w-full h-full"
        id="abs-basic-allowance-amount-render-bottom-section-container"
      >
        <div
          className="flex gap-3 h-auto"
          id="abs-basic-allowance-amount-cost-allow-label-cost-allow"
        >
          {!isZeroCost && !isHideFieldsForMainEntry && (
            <>
              <AmountDisplay
                label={costAllowLabel}
                value={costAllow}
                baseId="abs-basic-allowance-amount"
              />
              {dashedVerticalDivider}
            </>
          )}
          {formControls?.control && !isHideFieldsForMainEntry && (
            <span
              className="relative max-w-[180px]"
              id="abs-basic-allowance-amount-max-w-container"
            >
              <AllowanceAmountField
                fieldProps={allowanceAmount}
                formControls={formControls}
                onChange={handleAmountChange}
                error={{ message: digitError }}
                className={
                  calcError && !isZeroCost
                    ? "rounded border-error border-2 inputSelect"
                    : ""
                }
                disabled={areInputsDisabled}
                value={allowanceAmountData?.allowanceAmount}
                multiplier={multiplierVal}
                allowanceAmountFields={allowanceAmountFields}
                getEventData={getEventData}
                searchId={searchId}
                baseId="abs-basic-allowance-amount"
              />
            </span>
          )}
          {formControls?.control && !isHideFieldsForMainEntry && (
            <div
              className="min-w-[100px]"
              id="abs-basic-allowance-amount-are-inputs-disabled-container"
            >
              <UOMField
                fieldProps={uom}
                formControls={formControls}
                onChange={handleUOMChange}
                options={getUOMOptions()}
                disabled={areInputsDisabled}
                value={allowanceAmountData.uom}
                allowanceAmountFields={allowanceAmountFields}
                getEventData={getEventData}
                searchId={searchId}
                baseId="abs-basic-allowance-amount"
              />
            </div>
          )}

          {!isZeroCost && !isHideFieldsForMainEntry && (
            <>
              <div
                className="pt-5"
                id="abs-basic-allowance-amount-invalid-amount-tooltip-one"
              >
                <InValidAmountTooltip calcError={calcError} />
              </div>
              {dashedVerticalDivider}
              <AmountDisplay
                label={newCostAllowLabel}
                value={calculateFinalCost(allowanceAmountData?.allowanceAmount)}
                baseId="abs-basic-allowance-amount"
              />
              {dashedVerticalDivider}
            </>
          )}
          <div className="mr-2"></div>
          {amtSubLabelDisplayVal && (
            <>
              <AmountDisplay
                label={"Allowance Amount"}
                value={amtSubLabelDisplayVal}
                baseId="abs-basic-allowance-amount"
                directDisplay={true}
              />
              {dashedVerticalDivider}
              <div className="mr-2"></div>
            </>
          )}
        </div>
        <UserActions
          actionsConfig={useMemo(() => {
            return {
              isEditEnable,
              isSectionCompleted,
              isFormDirty: formControls?.formState?.isDirty,
              isHideFieldsForMainEntry,
              baseId: "abs-basic-allowance-amount",
              onClick: memoedFunc,
              sectionConfiguration,
              isInValidOffer
            };
          }, [
            isEditEnable,
            isSectionCompleted,
            formControls?.formState?.isDirty,
            isHideFieldsForMainEntry,
            isInValidOffer
          ])}
        />
      </div>
    </>
  );
  const renderHtml = (
    <>
      <div
        className="w-full"
        id="abs-basic-allowance-amount-render-top-section-one-con"
      >
        {renderTopSection}
      </div>
      {!isEmpty(allowancesResp) && (
        <div id="abs-basic-allowance-amount-render-middle-section-two">
          {renderMiddleSection}
          {formControls?.control && renderBottomSection}
        </div>
      )}
    </>
  );

  return renderHtml;
};
export default memo(BaseAllowanceAmount);
