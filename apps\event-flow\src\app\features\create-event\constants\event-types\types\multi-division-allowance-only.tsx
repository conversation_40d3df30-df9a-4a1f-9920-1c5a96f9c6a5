import { EVENT_ALLOWANCE } from "../../fields/allowance/allowance-steps-config";
import { EVENT_DETAILS_ALLOWANCE_ONLY_FIELDS } from "../../fields/event-details/event-details-allowance-only";

export const NATIONAL_DIVISION_ALLOWANCE_ONLY = {
  section: "Allowance Only",
  sections: ["Event Details", "Allowance"],
  generate_event_name: ["promoProductGroup", "startWeekVehicle"],
  showField: false,
  progress_types: ["Event Details", "Allowance"],
  "Event Details": {
    section: "Event Details",
    key: "Event Details",
    multiple: false,
    title: "Event Details",
    ediLabel: "Event Details",
    isOpenCard: true,
    fields: EVENT_DETAILS_ALLOWANCE_ONLY_FIELDS,
    subtitle: "",
    saveLabel: "Save Event Details & Add Allowance",
    editAccordion: "Edit Event Details",
    nextLevel: false,
    cardLayout: false,
    remove: false,
    eventTypeName: "AO",
    showField: false,
  },
  Allowance: {
    section: "Allowance",
    key: "Allowance",
    title: "Allowance",
    offerTitle: "New Offer",
    isOpenCard: false,
    multiple: true,
    subtitle: "Not Started",
    stepper: 0,
    allowanceTye: "Case",
    allowanceToBeCreated: "Both",
    fields: EVENT_ALLOWANCE,
    headerButton: "New",
    nextLevel: true,
    cardLayout: true,
    cardFieldProp: "offerAllowances",
    cardFieldSubProp: "allowances",
    nextLevelLabel: "Skip to Promotion Details",
    allAnotherItem: "Add Another Allowance",
    addAnotherOfferAllowance: "Add Another Offer & Allowance",
    saveAndContinue: "Save & Continue",
    saveAndCreateAllowance: "Save & Create Allowance",
    permission: "PROMOTION_ALLOWANCE_MGMT_EDIT",
    module: "ALLOWANCE",
    previewModuleKey: "allowance",
  },
};
