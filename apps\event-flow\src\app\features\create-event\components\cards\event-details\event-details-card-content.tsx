/* eslint-disable @nrwl/nx/enforce-module-boundaries */
import {
  FunctionComponent,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { isEqual } from "lodash";
import { useFormContext, useWatch } from "react-hook-form";
import "./event-details-card.scss";
import { useDispatch } from "react-redux";
import "apps/event-flow/src/app/features/create-event/components/select-dropdown.scss";
import {
  eventDetailsAllowance,
  eventDetailsDataHandler,
  setSelectedStoreGroups,
  setStoreGroupDivisions,
} from "../../../service/slice/event-detail-slice";
import { useSelectorWrap } from "@me/data-rtk";
import { compareAsc } from "date-fns";
import {
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from "react-router-dom";
import _ from "lodash";
import Alert from "@albertsons/uds/molecule/Alert";
import { EUSER_ROLES, useGetAppBasePath } from "@me/util-helpers";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import {
  getLoggedInUserType,
  periscopeIdChangesEnabled,
} from "@me-upp-js/utilities";
import {
  buildDivErrStr,
  excludeOwnBrandsFromPPGs,
  getIsMultiVendorEvent,
  getUniqueInvalidDivisionIds,
} from "./event-details-card-service";
import { usePostProductLocationValidationMutation } from "../../../service/apis/event-api";
import { eventTypes } from "../../../../event-types/constants/event-type-constant";
import LoadingSpinner from "../../../constants/LoadingSpinner/LoadingSpinner";
import EventDivisionFields from "./event-details-card-fields/division/event-division-field";
import EventPlanProductGroupField from "./event-details-card-fields/plan-product-group/plan-product-group-field";
import StoreGroupTypeField from "./event-details-card-fields/store-group-type/store-group-type-field";
import StoreGroupsField from "./event-details-card-fields/store-groups/store-groups-field";
import YearField from "./event-details-card-fields/year/year-field";
import VehicleTypeField from "./event-details-card-fields/vehicle-type/vehicle-type-field";
import StartWeekVehicle from "./event-details-card-fields/start-week/start-week-field";
import EventDatesField from "./event-details-card-fields/event-dates/event-dates-field";
import InvalidAllowPromoFieldMessage from "./event-details-card-fields/invalid-allow-promo-fields";
import EventNameField from "./event-details-card-fields/event-name/event-name-field";
import PeriscopeField from "./event-details-card-fields/periscope/periscope-field";
import PeriscopeErrors from "./event-details-card-fields/periscope/periscope-errors";
import {
  analyticsLinkClickEvent,
  checkIfAnyNotValidFields,
  compareFieldValuesAndSetValue,
  EVENT_DATES_MEMO_DEPENDENCIES,
  EventGroupFilter,
  fieldValuesToWatch,
  getEventDefaultState,
  getFieldNameValue,
  IEventDetailsCardContentProps,
  invalidPeriscopeChecker,
  isPeriscopeInputValid,
  isPeriscopeValidFromApi,
  mismatchPPGsError,
  nameChangedHandler,
  nameChangeMemoDependencies,
  PPG_MEMO_DEPENDENCIES,
  preparePlanEventPaylod,
  promoStatusList,
  renderPastEventDateWarning,
  resetValuesMemoDependency,
  setNdVendorDivisions,
  START_WEEK_MEMO_DEPENDENCIES,
  STORE_GROUP_TYPE_MEMO_DEPENDENCIES,
  STORE_GROUPS_MEMO_DEPENDENCIES,
  updateEventDetaultStateFromPeriscope,
  VEHICLE_TYPE_MEMO_DEPENDENCIES,
} from "./utility/utility";
import MultivendorPpgInfoAlert from "./event-details-card-fields/alerts/multivendor-ppg-info-alert";
import CicErrorAlert from "./event-details-card-fields/alerts/cic-error-alert";
import EventRequiredFieldHeader from "./event-details-card-fields/event-content-required-field-header";
import MultivendorsWarningModal from "./event-details-card-fields/multivendors-warning-modal";
import EventCreateUpdateButton from "./event-details-card-fields/event-create-update-button";
import ErrorMessage from "./event-details-card-fields/error-message";
import StoreGroupsFieldMD from "./event-details-card-fields/store-groups/store-groups-field-md";
import { getIsNationalEvent } from "../../../../event-types/event-types-helper";
import WarningModal from "../../../../event-types/components/event-header/warning-modal";
import { AlertCircle } from "lucide-react";

const EventDetailsCardContent: FunctionComponent<
  IEventDetailsCardContentProps
> = ({ cardConfiguration, updateEventHandler, isEditEvent, isCreateEvent }) => {
  const { getValues, setValue, handleSubmit } = useFormContext();

  const watchAll = useWatch({
    name: fieldValuesToWatch,
  });
  const dispatch = useDispatch();
  const location = useLocation();
  const state: any = location.state || {};
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { data: storeGroupsData } = useSelectorWrap("store_groups_data") ?? [];
  const selectedStoreGroups =
    useSelectorWrap("selected_store_groups")?.data ?? [];
  const {
    data: { storeGroupDivisions },
  } = useSelectorWrap("store_group_divisions");
  const { data: allowanceOnlyEvent } = useSelectorWrap(
    "allowance_only_event_type_configuration"
  );
  const { data: eventTypeAndDivisionsData } = useSelectorWrap(
    "event_type_and_divisions"
  );
  const [searchParams] = useSearchParams();
  const eventTypeName = searchParams.get("eventType");
  const eventType = eventTypeName || eventDetailsData?.eventType;
  const navigate = useNavigate();
  const { basePath } = useGetAppBasePath();

  const { isNationalEvent, nationalRolesAvailable, nationalEventsFlagEnabled } =
    getIsNationalEvent(eventTypeName || "");
  const navigateToTaskView = () => {
    navigate(`${basePath}/`);
  };

  const [DP, AO, NDP, NAO] = eventTypes.map(event => event?.eventType);
  const { id: eventId } = useParams();
  const {
    DIVISION_PROMO,
    AO_ONLY,
    CIC_FEATURE_FLAGS: { isCICFeatureEnabled },

    EVENT_DETAILS_ERROR_LABELS: { VEHICLE_TYPE_GROUPS_ERROR },
  } = efConstants;

  /********************** HOOKS **********************/

  const eventFormData = useRef({});
  const formEvent = useRef({});
  const [formFields, setFormFields] = useState(() =>
    getEventDefaultState(getValues, eventDetailsData, eventId)
  );
  const [show, setShow] = useState(false);
  const [isValidSelection, setIsValidSelection] = useState<any>({
    isShow: false,
    label: "",
    variant: "",
    errorDetails: "",
  });
  const [invalidDivisionIds, setInvalidDivisionIds] = useState<string[]>([]);
  const [displayPeriscopeErrors, setDisplayPeriscopeErrors] = useState<[]>([]);
  const [labels, setLabels] = useState([] as any);
  const [itemDataResponse, setItemDataResponse] = useState(); // to store response from pepg call for promise resolve
  const [eventGroupPayload, setEventGroupPayload] = useState<EventGroupFilter>({
    promoProductGroups: [],
    divisionIds: [],
    storedIds: [],
    periscopeIds: [],
    startDate: "",
    endDate: "",
  }); // to check whether payload's are different to resolve promise immediately
  const [multiVendorModalData, setMultiVendorModalData] = useState({
    isMultiVendorModalOpen: false,
    isMultiVendorErrModalOpen: false,
  });
  const [storeGroupFieldChanged, setStoreGroupFieldChanged] = useState(false);
  const [vehicleTypeFieldChanged, setVehicleTypeFieldChanged] = useState(false);
  const filteredDivisionsRef = useRef<string[] | null>(null);

  /************************ RTK MUTATIONS/QUERY****************/

  const [
    postProductLocationValidationItems,
    { isLoading: isValidationLoading, data: productLocationValidationItem },
  ] = usePostProductLocationValidationMutation();

  /*************************EFFECTS ****************************/

  useEffect(() => {
    fieldsWatcher();
  }, [watchAll]);

  useEffect(() => {
    compareFieldValuesAndSetValue(eventDetailsData, getValues, setFormFields);
  }, [...resetValuesMemoDependency(getValues)]);

  useEffect(() => {
    if (eventDetailsData?.dataFetchedFromPeriscope) {
      const data = updateEventDetaultStateFromPeriscope(eventDetailsData);
      setFormFields(prevState => ({
        ...prevState,
        ...data,
        dataFetchedFromPeriscope: true,
      }));
      const formEventName = getFieldNameValue(state, eventDetailsData, "");
      setValue("name", formEventName || eventDetailsData?.name);
    }
  }, [eventDetailsData, eventDetailsData?.dataFetchedFromPeriscope, getValues]);

  useEffect(() => {
    if (isCreateEvent) {
      const userFilters = localStorage.getItem("USER_FILTERS");
      const parsedUserFilters = JSON.parse(userFilters || "{}");
      const eventType = eventTypeAndDivisionsData?.eventType || "";
      const { parsedDivisionIds } = parsedUserFilters;
      const formDivision = getValues("divisionIds");
      const nonNationalDivisionIds = formDivision?.length
        ? formDivision
        : parsedDivisionIds && parsedDivisionIds?.length
        ? [parsedDivisionIds?.[0]]
        : ["27"];
      const { vendorNumbers, divisions } = setNdVendorDivisions();
      const isNationalEvent = ["NDP", "NAO"].includes(eventType);
      const divisionIds = isNationalEvent ? divisions : nonNationalDivisionIds;
      localStorage.setItem("vendorNumList", JSON.stringify(vendorNumbers));
      const divisionId = isNationalEvent ? "98" : divisionIds?.[0];
      dispatch(
        eventDetailsDataHandler({
          ...formFields,
          divisionIds,
          divisionId,
        })
      );
      setFormFields(prevState => ({
        ...prevState,
        divisionIds,
        divisionId,
      }));
    }
  }, [dispatch, isCreateEvent]);

  useEffect(() => {
    nameChangedHandler({
      allowanceOnlyEvent,
      formFields,
      getValues,
      setValue,
      eventDetailsData,
      state,
      eventName,
    });
  }, [
    ...nameChangeMemoDependencies(getValues),
    formFields?.count === 0,
    allowanceOnlyEvent?.isAllowanceOnlyEventType,
    eventDetailsData?.dataFetchedFromPeriscope,
  ]);

  useEffect(() => {
    checkAndSetPastDate();
  }, [
    isEditEvent,
    getValues("startDate"),
    getValues("endDate"),
    eventDetailsData?.promotionsList,
  ]);

  const isAllowanceOrPromotion =
    eventDetailsData?.offerAllowances?.length ||
    eventDetailsData?.promotionsList?.length;

  const {
    fields: {
      endDate,
      eventName,
      planProductGroups,
      planStoreGroupType,
      startDate,
      startWeek,
      storeGroups,
      vehicleType,
      year,
      periscopeField,
    },
  } = cardConfiguration;

  const hasUnitedSelected = getValues("storeGroups")?.some(item =>
    item?.label?.includes("United")
  );

  const fieldsWatcher = () => {
    if (checkIfAnyNotValidFields(watchAll)) {
      setValue("isEventChanged", _.uniqueId("eventChanged"));
    }
    setFormFields(prevState => ({ ...prevState, warningCount: 0 }));
  };
  const checkAndSetPastDate = () => {
    let pastDate = false;
    if (
      isEditEvent &&
      eventDetailsData?.promotionsList?.length &&
      !promoStatusList({ eventDetailsData }) &&
      getValues("startDate")
    ) {
      const result = compareAsc(
        new Date(getValues("startDate")),
        new Date(Date.now())
      );
      pastDate = result < 1;
    }
    setFormFields(prevState => ({ ...prevState, isPastDate: pastDate }));
  };

  const saveEventHandler = (event, periscopeObj) => {
    Object.keys(event ?? {})?.length && event?.stopPropagation();
    Object.keys(event ?? {})?.length && event?.preventDefault();
    updateEventHandler(cardConfiguration?.eventTypeName, {
      periscopeObj,
      filteredDivisions: filteredDivisionsRef.current,
    });
    filteredDivisionsRef.current = null;
  };

  const onSubmit = (data, event, isMultiVendor) => {
    if (data || eventFormData?.current) {
      dispatch(eventDetailsAllowance({ enableAllowance: !isMultiVendor }));
      saveEventHandler(
        event || formEvent?.current,
        data
          ? data?.periscopeDetails
          : productLocationValidationItem?.periscopeDetails
      );
    }
    if (eventFormData) eventFormData.current = {};
    if (formEvent) formEvent.current = {};
  };
  const handleRemoveInvalidDivisions = () => {
    if (!Array.isArray(storeGroupsData) || !Array.isArray(selectedStoreGroups))
      return;

    const selectedStoreGroupsData = storeGroupsData?.filter(x =>
      selectedStoreGroups?.includes(x?.id)
    );
    const filteredGroupIds = selectedStoreGroupsData
      ?.filter(item => !invalidDivisionIds?.includes(item?.divisionIds?.[0]))
      .map(item => item?.id);
    const filteredStoreGroupDivisions = selectedStoreGroupsData
      ?.filter(item => !invalidDivisionIds?.includes(item?.divisionIds?.[0]))
      .map(item => item?.divisionIds?.[0]);
    if (filteredGroupIds?.length > 0) {
      dispatch(setSelectedStoreGroups(filteredGroupIds));
      dispatch(
        setStoreGroupDivisions({
          storeGroupDivisions: filteredStoreGroupDivisions,
        })
      );
      setIsValidSelection(false);
    }
  };
  const submitBasedOnUserEvent = (userType, data, isMultiVendor, event) => {
    if (userType === EUSER_ROLES?.MERCHANT) {
      eventFormData.current = data;
      formEvent.current = event;
      if ([DP, NDP, "NCDP"].includes(eventType)) {
        isMultiVendor
          ? setMultiVendorModalData(prevState => ({
              ...prevState,
              isMultiVendorModalOpen: true,
            }))
          : onSubmit(data, event, isMultiVendor);
      } else if ([AO, NAO].includes(eventType)) {
        isMultiVendor
          ? setMultiVendorModalData(prevState => ({
              ...prevState,
              isMultiVendorErrModalOpen: true,
            })) // NOTE: Names are similar but this is the ERROR MODAL
          : onSubmit(data, event, isMultiVendor);
      }
    } else {
      onSubmit(data, event, isMultiVendor);
    }
  };

  const createPeriscopePayloadObjects = itemData =>
    itemData?.data?.periscopeDetails;

  const processProductValidationResponse = async (
    itemData,
    payload,
    // validPeriscope,
    data,
    userType,
    isMultiVendorEvent,
    event
  ) => {
    if (!isEqual(itemData, itemDataResponse)) {
      setItemDataResponse(itemData);
    }
    if (!isEqual(payload, eventGroupPayload)) {
      setEventGroupPayload(payload);
    }
    let validPeriscope = true;
    if (formFields?.periscopeIdState) {
      if (
        invalidPeriscopeChecker(
          itemData,
          setDisplayPeriscopeErrors,
          setLabels
        ) &&
        formFields?.warningCount === 0
      ) {
        validPeriscope = false;
        setFormFields(prevState => ({ ...prevState, warningCount: 1 }));
      }
    }
    const hasValidItems = currentValue => {
      return currentValue?.plannedEventItems?.plannedProductGroupItems?.length;
    };

    if (eventType === NDP) {
      const uniqueInvalidDivisionIds = getUniqueInvalidDivisionIds(
        itemData?.data?.plannedProductGroups
        //[0].plannedEventItems?.invalidDivisionIds
      );
      setInvalidDivisionIds(uniqueInvalidDivisionIds as string[]);

      if (uniqueInvalidDivisionIds?.length) {
        const buildDivErrorStr = buildDivErrStr(
          getValues("storeGroups"),
          uniqueInvalidDivisionIds
        );
        setIsValidSelection({
          isShow: true,
          label: efConstants.INVALID_DIVISIONS_WITHOUT_ITEMS,
          variant: "warning",
          errorDetails: buildDivErrorStr,
        });
        return;
      }
    }
    // if that response was valid, then we proceed with only doing hasValid items check
    if (itemData?.data?.plannedProductGroups?.some(hasValidItems)) {
      if (validPeriscope) {
        const periscopeDetailsLoad = createPeriscopePayloadObjects(itemData);
        const dataWithPeriscope = {
          ...data,
          periscopeDetails: periscopeDetailsLoad,
        };
        submitBasedOnUserEvent(
          userType,
          dataWithPeriscope,
          isMultiVendorEvent,
          event
        );
        analyticsLinkClickEvent(eventType, getValues);
      }
      setIsValidSelection({
        isShow: false,
        label: "",
        variant: "informational",
        errorDetails: "",
      });
    } else {
      setIsValidSelection({
        isShow: !itemData?.error,
        label: !itemData?.error
          ? [NDP, "NCDP", DP].includes(eventType)
            ? DIVISION_PROMO
            : AO_ONLY
          : "",
        variant: "warning",
        errorDetails: "",
      });
    }
  };

  const processValidationResponse = (
    itemData: any,
    payload: any,
    data: any,
    userType: string,
    isMultiVendorEvent: boolean,
    event: any
  ) => {
    processProductValidationResponse(
      itemData,
      payload,
      data,
      userType,
      isMultiVendorEvent,
      event
    );
  };
  const getResponseForValidation = (
    payload: any,
    eventGroupPayload: any,
    productLocationValidationItem: any
  ) => {
    if (productLocationValidationItem && isEqual(payload, eventGroupPayload)) {
      return Promise.resolve(itemDataResponse); // resolved immediately
    } else {
      return postProductLocationValidationItems(payload); // API call
    }
  };
  const handleSubmitMultiAndNonMultiVendor = async (
    data,
    event,
    isFromModal = false,
    invalidDivisionIds: string[] = []
  ) => {
    const userType = getLoggedInUserType();
    const planProductGroupsData = excludeOwnBrandsFromPPGs(
      data?.planProductGroups
    );
    const { isMultiVendorEvent } = getIsMultiVendorEvent(
      planProductGroupsData,
      userType
    );

    let finalStoreGroupDivisions = storeGroupDivisions;

    if (isFromModal && invalidDivisionIds?.length > 0) {
      finalStoreGroupDivisions = storeGroupDivisions?.filter(
        (item: any) => !invalidDivisionIds?.includes(item)
      );
      filteredDivisionsRef.current = Array.from(
        new Set(finalStoreGroupDivisions)
      );
    } else {
      filteredDivisionsRef.current = null;
    }

    const payload: EventGroupFilter = preparePlanEventPaylod(
      data,
      formFields,
      getValues,
      eventType,
      finalStoreGroupDivisions
    );

    const isValidID = isPeriscopeInputValid({ formFields, setFormFields });
    if (isValidID) {
      setFormFields(prevState => ({
        ...prevState,
        periscopeValid: true,
      }));
      const response = getResponseForValidation(
        payload,
        eventGroupPayload,
        productLocationValidationItem
      );
      response?.then(async itemData => {
        setValue("isEventChanged", false);
        const periscopeValidFromApi = isPeriscopeValidFromApi(
          itemData?.data?.periscopeDetails
        );
        setFormFields(prevState => ({
          ...prevState,
          periscopeValidFromApi,
        }));
        processValidationResponse(
          itemData,
          payload,
          data,
          userType,
          isMultiVendorEvent,
          event
        );
      });
    } else {
      setFormFields(prevState => ({
        ...prevState,
        periscopeValid: false,
      }));
    }
  };

  const onErrorHandler = err => console.log("err:::", err);

  const memoizedSetFormFields = useCallback(updatedFields => {
    setFormFields(updatedFields);
  }, []);

  const alertCompWithContent = (warningLabel, errorDetails = "") => (
    <div>
      {warningLabel && <p>{warningLabel}</p>}
      {errorDetails && <p>{errorDetails}</p>}
    </div>
  );

  return (
    <div
      className={`${efConstants.componentClassName.EVENT_DETAILS_CARD_CONTENT}`}
    >
      <LoadingSpinner
        isLoading={isValidationLoading}
        classname="!h-full !w-full rounded-md"
      />
      <MultivendorPpgInfoAlert formFields={formFields} eventType={eventType} />
      <CicErrorAlert formFields={formFields} setFormFields={setFormFields} />
      <div className="flex flex-col flex-grow-0 flex-shrink-0  ">
        <EventRequiredFieldHeader
          isAllowanceOrPromotion={isAllowanceOrPromotion}
        />
      </div>
      {periscopeIdChangesEnabled() ? (
        <>
          <PeriscopeErrors
            formFields={formFields}
            displayPeriscopeErrors={displayPeriscopeErrors}
            labels={labels}
          />
          <PeriscopeField
            periscopeField={useMemo(() => periscopeField, [periscopeField])}
            formFields={useMemo(() => formFields, [formFields])}
            setFormFields={memoizedSetFormFields}
            setStoreGroupFieldChanged={setStoreGroupFieldChanged}
            eventType={eventType}
          />
        </>
      ) : null}
      <form
        key={1}
        onSubmit={handleSubmit(
          handleSubmitMultiAndNonMultiVendor,
          onErrorHandler
        )}
      >
        {!periscopeIdChangesEnabled() ? (
          <PeriscopeErrors
            formFields={formFields}
            displayPeriscopeErrors={displayPeriscopeErrors}
            labels={labels}
          />
        ) : null}
        {formFields?.isPastDate ? renderPastEventDateWarning() : null}
        <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0  gap-4">
          {![NDP, NAO].includes(eventType) && (
            <EventDivisionFields
              setFormFields={memoizedSetFormFields}
              cardFields={useMemo(() => cardConfiguration?.fields, [])}
              isAllowanceOrPromotion={isAllowanceOrPromotion}
            />
          )}
          {isCICFeatureEnabled ? (
            <>
              <div className="flex component-scroll w-full gap-4">
                <EventPlanProductGroupField
                  planProductGroups={useMemo(() => planProductGroups, [])}
                  storeGroups={useMemo(() => storeGroups, [])}
                  formFields={useMemo(
                    () => formFields,
                    [...PPG_MEMO_DEPENDENCIES(formFields)]
                  )}
                  setFormFields={memoizedSetFormFields}
                  isEditEvent={useMemo(() => isEditEvent, [])}
                  eventName={useMemo(() => eventName, [])}
                  setStoreGroupFieldChanged={setStoreGroupFieldChanged}
                />
              </div>
              {eventType === DP && (
                <div className="flex-grow-0 flex-shrink-0 w-full h-px  bg-[#fdfdff] border-t border-r border-b-0 border-l border-[#c8daeb]"></div>
              )}

              <div className="flex flex-col w-full">
                {/* <ErrorMessage
                  warnMessage={STORE_GROUPS_ERROR}
                  showError={
                    eventDetailsData?.dataFetchedFromPeriscope &&
                    !storeGroupFieldChanged &&
                    eventDetailsData?.periscopeValidFromApi &&
                    !eventDetailsData?.storeGroups?.length
                  }
                /> */}
                <div className="flex component-scroll w-full gap-4">
                  <StoreGroupTypeField
                    formFields={useMemo(
                      () => formFields,
                      [...STORE_GROUP_TYPE_MEMO_DEPENDENCIES(formFields)]
                    )}
                    setFormFields={memoizedSetFormFields}
                    planStoreGroupType={useMemo(() => planStoreGroupType, [])}
                    storeGroups={useMemo(() => storeGroups, [])}
                    setStoreGroupFieldChanged={setStoreGroupFieldChanged}
                  />
                  {eventType !== NDP ? (
                    <StoreGroupsField
                      formFields={useMemo(
                        () => formFields,
                        [...STORE_GROUPS_MEMO_DEPENDENCIES(formFields)]
                      )}
                      storeGroups={useMemo(() => storeGroups, [])}
                      showField={useMemo(
                        () => cardConfiguration?.showField,
                        []
                      )}
                      setStoreGroupFieldChanged={setStoreGroupFieldChanged}
                    />
                  ) : (
                    <StoreGroupsFieldMD
                      formFields={useMemo(
                        () => formFields,
                        [...STORE_GROUPS_MEMO_DEPENDENCIES(formFields)]
                      )}
                      storeGroups={useMemo(() => storeGroups, [])}
                      showField={useMemo(
                        () => cardConfiguration?.showField,
                        []
                      )}
                      setStoreGroupFieldChanged={setStoreGroupFieldChanged}
                      storeGroupFieldChanged={storeGroupFieldChanged}
                    />
                  )}
                </div>
              </div>
            </>
          ) : (
            <div className="flex component-scroll w-full gap-4">
              <EventPlanProductGroupField
                planProductGroups={planProductGroups}
                storeGroups={storeGroups}
                formFields={formFields}
                setFormFields={memoizedSetFormFields}
                isEditEvent={isEditEvent}
                eventName={useMemo(() => eventName, [])}
                setStoreGroupFieldChanged={setStoreGroupFieldChanged}
              />

              <StoreGroupTypeField
                formFields={useMemo(
                  () => formFields,
                  [...STORE_GROUP_TYPE_MEMO_DEPENDENCIES(formFields)]
                )}
                setFormFields={memoizedSetFormFields}
                planStoreGroupType={useMemo(() => planStoreGroupType, [])}
                storeGroups={useMemo(() => storeGroups, [])}
                setStoreGroupFieldChanged={setStoreGroupFieldChanged}
              />

              {eventType !== NDP ? (
                <StoreGroupsField
                  formFields={useMemo(
                    () => formFields,
                    [...STORE_GROUPS_MEMO_DEPENDENCIES(formFields)]
                  )}
                  storeGroups={useMemo(() => storeGroups, [])}
                  showField={cardConfiguration?.showField}
                  setStoreGroupFieldChanged={setStoreGroupFieldChanged}
                />
              ) : (
                <StoreGroupsFieldMD
                  formFields={useMemo(
                    () => formFields,
                    [...STORE_GROUPS_MEMO_DEPENDENCIES(formFields)]
                  )}
                  storeGroups={useMemo(() => storeGroups, [])}
                  showField={useMemo(() => cardConfiguration?.showField, [])}
                  setStoreGroupFieldChanged={setStoreGroupFieldChanged}
                  storeGroupFieldChanged={storeGroupFieldChanged}
                />
              )}
            </div>
          )}

          {isNationalEvent && hasUnitedSelected && (
            <div className="flex justify-start items-center gap-2 flex-grow-0 flex-shrink-0 text-m text-[16px] font-bold text-left text-[#033B69]">
              <AlertCircle fill="#1B6EBB" color="#FFFFFF" size={20} />
              United division only supports Ship-to-Store allowance type.
            </div>
          )}
          <div className="flex-grow-0 flex-shrink-0 w-full h-px  bg-[#fdfdff] border-t border-r border-b-0 border-l border-[#c8daeb]"></div>

          <div className="flex flex-wrap w-full gap-4">
            <div className="flex flex-col w-full">
              <ErrorMessage
                warnMessage={VEHICLE_TYPE_GROUPS_ERROR}
                showError={
                  eventDetailsData?.dataFetchedFromPeriscope &&
                  !vehicleTypeFieldChanged &&
                  eventDetailsData?.periscopeValidFromApi &&
                  !eventDetailsData?.startWeekVehicle
                }
              />
              <div className="w-full col-span-8">
                <div className="w-full gap-4 flex inline-vehicle-info">
                  <VehicleTypeField
                    vehicleType={useMemo(() => vehicleType, [])}
                    formFields={useMemo(
                      () => formFields,
                      [...VEHICLE_TYPE_MEMO_DEPENDENCIES(formFields)]
                    )}
                    setFormFields={memoizedSetFormFields}
                    isEditEvent={useMemo(() => isEditEvent, [])}
                    startWeek={useMemo(() => startWeek, [])}
                    eventName={useMemo(() => eventName, [])}
                    show={show}
                    setShow={setShow}
                    setVehicleTypeFieldChanged={setVehicleTypeFieldChanged}
                    year={useMemo(() => year, [])}
                  />
                  <YearField
                    formFields={useMemo(
                      () => formFields,
                      [formFields?.yearVal, formFields?.vehicleTypeProps]
                    )}
                    setFormFields={memoizedSetFormFields}
                    year={useMemo(() => year, [])}
                    setVehicleTypeFieldChanged={setVehicleTypeFieldChanged}
                  />
                  <StartWeekVehicle
                    formFields={useMemo(
                      () => formFields,
                      [...START_WEEK_MEMO_DEPENDENCIES(formFields)]
                    )}
                    setFormFields={memoizedSetFormFields}
                    startWeek={useMemo(() => startWeek, [])}
                    show={show}
                    setVehicleTypeFieldChanged={setVehicleTypeFieldChanged}
                  />
                  <EventDatesField
                    formFields={useMemo(
                      () => formFields,
                      [...EVENT_DATES_MEMO_DEPENDENCIES(formFields)]
                    )}
                    startDate={useMemo(() => startDate, [])}
                    endDate={useMemo(() => endDate, [])}
                    setFormFields={memoizedSetFormFields}
                    setVehicleTypeFieldChanged={setVehicleTypeFieldChanged}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="flex-grow-0 flex-shrink-0 w-full  bg-[#fdfdff] border-t border-r border-b-0 border-l border-[#c8daeb]"></div>
          <InvalidAllowPromoFieldMessage />
          <EventNameField
            eventName={useMemo(() => eventName, [])}
            isEditEvent={isEditEvent}
            formFields={useMemo(() => formFields, [formFields?.eventError])}
            setFormFields={memoizedSetFormFields}
          />
          {!periscopeIdChangesEnabled() ? (
            <PeriscopeField
              periscopeField={useMemo(() => periscopeField, [])}
              formFields={useMemo(
                () => formFields,
                [formFields?.periscopeIdState, formFields?.periscopeValid]
              )}
              setFormFields={memoizedSetFormFields}
              setStoreGroupFieldChanged={setStoreGroupFieldChanged}
              eventType={eventType}
            />
          ) : null}

          <div className="flex-grow-0 flex-shrink-0 w-full h-px  bg-[#fdfdff] border-t border-r border-b-0 border-l border-[#c8daeb]"></div>
          {isValidSelection?.isShow && (
            <Alert
              isOpen={true}
              variant={isValidSelection?.variant}
              className="!justify-center w-full !z-[9]"
            >
              {invalidDivisionIds && (
                <button
                  className="absolute right-[2%] text-blue-600 font-medium whitespace-nowrap hover:underline ml-4"
                  onClick={handleRemoveInvalidDivisions}
                >
                  Remove Invalid Divisions
                </button>
              )}
              {alertCompWithContent(
                isValidSelection?.label,
                isValidSelection.errorDetails
              )}
            </Alert>
          )}
          <Alert
            isOpen={!formFields?.canCreateEvent}
            variant="warning"
            className="!justify-center w-full !z-[9]"
          >
            {mismatchPPGsError}
          </Alert>

          <div className="flex">
            <EventCreateUpdateButton formFields={formFields} />
          </div>
        </div>
      </form>
      <MultivendorsWarningModal
        multiVendorModalData={multiVendorModalData}
        setMultiVendorModalData={setMultiVendorModalData}
        onSubmit={onSubmit}
      />
      {isNationalEvent &&
      (!nationalRolesAvailable || !nationalEventsFlagEnabled) ? (
        <WarningModal
          onChange={navigateToTaskView}
          isOpen={
            isNationalEvent &&
            (!nationalRolesAvailable || !nationalEventsFlagEnabled)
          }
          setOpen={() => {}}
          warningTitle="The user isn't Authorized!"
          warningBodyText="The user does not have permissions to proceed."
          warningLabel="Return to Task View?"
          showCancelBtn={false}
          cancelButtonLabel="No, Continue Editing"
          confirmButtonLabel="Yes, Return to Task View"
          onCloseHandler={navigateToTaskView}
        />
      ) : null}
    </div>
  );
};

export default EventDetailsCardContent;
