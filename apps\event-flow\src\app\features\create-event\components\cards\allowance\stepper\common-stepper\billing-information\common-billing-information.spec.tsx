import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import CommonBillingInformation from "./common-billing-information";
import { Provider } from "react-redux"; // Adjust the import path
import { FormProvider, useForm } from "react-hook-form";
import { app_store } from "@me/data-rtk";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom";
import * as selectors from "@me/data-rtk";
import { BrowserRouter } from "react-router-dom";
import {
  mock_allowance_form_data,
  mock_allowance_temp_work,
  mock_cancel_status_event_data,
  mock_event_Data,
  mockAllowTempWrk,
} from "./common-billing-information-mock";

jest.mock("react-redux", () => ({
  ...jest.requireActual("react-redux"),
  useDispatch: jest.fn().mockImplementation(() => {
    return jest.fn(() => null);
  }),
}));

jest.mock("../../../../../hooks/useAllowanceTempUpdate", () =>
  jest.fn().mockReturnValue({
    addAllowancesBillingInformation: jest.fn().mockReturnValue({ data: {} }),
    isLoading: false,
  })
);
jest.mock("../../../../../../service/apis/allowance-api", () => ({
  usePostAllowanceTempWorkDataMutation: jest.fn().mockReturnValue([
    jest.fn().mockReturnValue({
      allowanceData: {
        allowanceType: "SCAN",
        allowanceTypeSpecification: {
          scanAllow: {
            allowancesMap: { key1: "value1", key2: "value2" },
            createInd: true,
          },
        },
      },
    }),
    {
      isSuccess: true,
      isLoading: false,
      isSaveApiError: false,
      error: {},
    },
  ]),
  usePostOfferCreateUpdateMutation: jest.fn().mockReturnValue([
    jest.fn().mockReturnValue({ data: {} }),
    {
      isSuccess: true,
      isLoading: false,
      isSaveApiError: false,
      error: {},
    },
  ]),
  usePostOfferAllowanceMutation: jest
    .fn()
    .mockReturnValue([
      jest.fn().mockReturnValue({ data: {} }),
      { isSuccess: true, isLoading: false, isSaveApiError: false, error: {} },
    ]),
  usePutOfferAllowanceMutation: jest
    .fn()
    .mockReturnValue([
      jest.fn().mockReturnValue({ data: {} }),
      { isSuccess: true, isLoading: false, isSaveApiError: false, error: {} },
    ]),
  useDeleteAllowanceTempWorkDataMutation: jest
    .fn()
    .mockReturnValue([
      jest.fn().mockReturnValue({}),
      { isSuccess: true, isLoading: false, isSaveApiError: false, error: {} },
    ]),
}));

function Wrapper({ children }) {
  const formMethods = useForm<any>();
  return (
    <BrowserRouter>
      <Provider store={app_store}>
        <FormProvider {...formMethods}>{children}</FormProvider>
      </Provider>
    </BrowserRouter>
  );
}

describe("CommonBillingInformation Component", () => {

  beforeEach(() => {
    // Mocking selectors and hooks
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      return (
        {
          event_details_data: {
            data: mock_event_Data,
          },
          allowance_form_data: {
            data: mock_allowance_form_data,
          },
          allowance_temp_work: {
            data: mock_allowance_temp_work,
            allowance_details_data: {
              data: {},
            },
            offer_card_configutation_rn: {
              data: {
                editCardConfig: {},
                openCardConfig: { 0: true },
              },
            },
          },
        }[sel_name] || {}
      );
    });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders the component with initial data", () => {
    window["AB"] = {
      DATALAYER: {
        setLinkClickEvents: function () {
          return true;
        },
      },
    };
    render(
      <Wrapper>
        <CommonBillingInformation
          {...{
            saveAndContinueHanlder: jest.fn(),
            stepperElement: {
              fields: {
                allowanceSpecificFields: {
                  CASE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  SCAN: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  SHIP_TO_STORE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  HEADER_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  ITEM_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  DEFAULT: {
                    key: "DSD_WHSE_RETAIL_DIVISION",
                    routeKey: "DSD_WHSE_RETAIL_DIVISION",
                    billingInformationData: {
                      registerKeyName:
                        "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                    },
                  },
                },
                suggestedVendorPaymentType: {
                  label: "Suggested Payment Type",
                  required: false,
                  registerField: "suggestedVendorPaymentType",
                  type: "select",
                  default: "Deduct",
                  displayLabel: "name",
                  options: [
                    {
                      name: "Select",
                      id: "",
                    },
                    {
                      name: "Deduct",
                      id: "Deduct",
                    },
                    {
                      name: "Invoice",
                      id: "Invoice",
                    },
                  ],
                  apiUrl: "",
                  slice: "",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "ABS Vendor Payment Type is Required",
                    },
                  },
                  tooltip:
                    "This is auto generated based on your selections.\n You can edit this if you want.",
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
                },
                acApOrArNumber: {
                  label: "Suggested A/P or A/R Number",
                  required: false,
                  registerField: "acApOrArNumber",
                  type: "number",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "A/P or A/R Vendor Number is Required",
                    },
                    formatError: {
                      pattern: {},
                      text: "Please enter valid A/P or A/R Vendor Number",
                    },
                    maxLength: {
                      length: 8,
                      text: "Maximum 8 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.acApOrArNumber",
                },
                vendorComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "vendorComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorComment",
                },
                vendorOfferTrackingNbr: {
                  label: "",
                  required: false,
                  registerField: "vendorOfferTrackingNbr",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Tracking Number is Required",
                    },
                    maxLength: {
                      length: 30,
                      text: "Maximum 30 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
                },
                commonComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "allComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                },
                billingInformationData: {
                  registerKeyName: "billingInformationData",
                },
              },
              isOpen: false,
              allowanceTypeAndPerformance: [
                "SCAN",
                "SHIP_TO_STORE",
                "HEADER_FLAT",
                "ITEM_FLAT",
              ],
              allowanceToBeCreated: [
                "One Allowance: Warehouse, DSD, or Combined",
                "Combined DSD",
              ],
              label: "Default Billing Information",
              offerTypeKey: "WAREHOUSE",
              allowanceScreenTypes: ["DP", "AO"],
              create: {
                isEdit: false,
                isView: false,
                label: "Save & Continue",
              },
              edit: {
                isEdit: true,
                isView: false,
                label: "Update & Continue",
              },
            }, // Provide required props
            offerIndex: 0,
            allowanceIndex: 0,
            step: 4,
            isLastStep: true,
            isEditEnable: false,
          }}
        ></CommonBillingInformation>
      </Wrapper>
    );
    expect(screen.getByText("ABS Merch Vendor")).toBeInTheDocument();
    expect(screen.getByText("006446-001")).toBeInTheDocument();
    expect(screen.getAllByText("KEHE DISTRIBUTORS")?.length).toEqual(2);
    expect(screen.getByText("Suggested Payment Type")).toBeInTheDocument();
    expect(screen.getByText("Save Comment & Apply to All")).toBeInTheDocument();
  });

  it("renders the component with initial data with cancel event status", () => {
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      return (
        {
          event_details_data: {
            data: mock_cancel_status_event_data,
          },
          allowance_form_data: {
            data: mock_allowance_form_data,
          },
          allowance_temp_work: {
            data: mockAllowTempWrk,
            allowance_details_data: {
              data: {},
            },
            offer_card_configutation_rn: {
              data: {
                editCardConfig: {},
                openCardConfig: { 0: true },
              },
            },
          },
        }[sel_name] || {}
      );
    });
    render(
      <Wrapper>
        <CommonBillingInformation
          {...{
            saveAndContinueHanlder: jest.fn(),
            stepperElement: {
              fields: {
                allowanceSpecificFields: {
                  CASE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  SCAN: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  SHIP_TO_STORE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  HEADER_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  ITEM_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  DEFAULT: {
                    key: "DSD_WHSE_RETAIL_DIVISION",
                    routeKey: "DSD_WHSE_RETAIL_DIVISION",
                    billingInformationData: {
                      registerKeyName:
                        "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                    },
                  },
                },
                suggestedVendorPaymentType: {
                  label: "Suggested Payment Type",
                  required: false,
                  registerField: "suggestedVendorPaymentType",
                  type: "select",
                  default: "Deduct",
                  displayLabel: "name",
                  options: [
                    {
                      name: "Select",
                      id: "",
                    },
                    {
                      name: "Deduct",
                      id: "Deduct",
                    },
                    {
                      name: "Invoice",
                      id: "Invoice",
                    },
                  ],
                  apiUrl: "",
                  slice: "",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "ABS Vendor Payment Type is Required",
                    },
                  },
                  tooltip:
                    "This is auto generated based on your selections.\n You can edit this if you want.",
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
                },
                acApOrArNumber: {
                  label: "Suggested A/P or A/R Number",
                  required: false,
                  registerField: "acApOrArNumber",
                  type: "number",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "A/P or A/R Vendor Number is Required",
                    },
                    formatError: {
                      pattern: {},
                      text: "Please enter valid A/P or A/R Vendor Number",
                    },
                    maxLength: {
                      length: 8,
                      text: "Maximum 8 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.acApOrArNumber",
                },
                vendorComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "vendorComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorComment",
                },
                vendorOfferTrackingNbr: {
                  label: "",
                  required: false,
                  registerField: "vendorOfferTrackingNbr",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Tracking Number is Required",
                    },
                    maxLength: {
                      length: 30,
                      text: "Maximum 30 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
                },
                commonComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "allComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                },
                billingInformationData: {
                  registerKeyName: "billingInformationData",
                },
              },
              isOpen: false,
              allowanceTypeAndPerformance: [
                "SCAN",
                "SHIP_TO_STORE",
                "HEADER_FLAT",
                "ITEM_FLAT",
              ],
              allowanceToBeCreated: [
                "One Allowance: Warehouse, DSD, or Combined",
                "Combined DSD",
              ],
              label: "Default Billing Information",
              offerTypeKey: "WAREHOUSE",
              allowanceScreenTypes: ["DP", "AO"],
              create: {
                isEdit: false,
                isView: false,
                label: "Save & Continue",
              },
              edit: {
                isEdit: true,
                isView: false,
                label: "Update & Continue",
              },
            }, // Provide required props
            offerIndex: 0,
            allowanceIndex: 0,
            step: 4,
            isLastStep: true,
            isEditEnable: false,
          }}
        ></CommonBillingInformation>
      </Wrapper>
    );
    expect(screen.getByText("ABS Merch Vendor")).toBeInTheDocument();
    expect(screen.getByText("006446-001")).toBeInTheDocument();
    expect(screen.getAllByText("KEHE DISTRIBUTORS")?.length).toEqual(2);
    expect(screen.getByText("Suggested Payment Type")).toBeInTheDocument();
    expect(screen.getByText("Save Comment & Apply to All")).toBeInTheDocument();
  });

  it("calls handleCancel function when cancel button is clicked", async () => {
    render(
      <Wrapper>
        <CommonBillingInformation
          {...{
            saveAndContinueHanlder: jest.fn(),
            stepperElement: {
              fields: {
                allowanceSpecificFields: {
                  CASE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  SCAN: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  SHIP_TO_STORE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  HEADER_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  ITEM_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  DEFAULT: {
                    key: "DSD_WHSE_RETAIL_DIVISION",
                    routeKey: "DSD_WHSE_RETAIL_DIVISION",
                    billingInformationData: {
                      registerKeyName:
                        "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                    },
                  },
                },
                suggestedVendorPaymentType: {
                  label: "Suggested Payment Type",
                  required: false,
                  registerField: "suggestedVendorPaymentType",
                  type: "select",
                  default: "Deduct",
                  displayLabel: "name",
                  options: [
                    {
                      name: "Select",
                      id: "",
                    },
                    {
                      name: "Deduct",
                      id: "Deduct",
                    },
                    {
                      name: "Invoice",
                      id: "Invoice",
                    },
                  ],
                  apiUrl: "",
                  slice: "",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "ABS Vendor Payment Type is Required",
                    },
                  },
                  tooltip:
                    "This is auto generated based on your selections.\n You can edit this if you want.",
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
                },
                acApOrArNumber: {
                  label: "Suggested A/P or A/R Number",
                  required: false,
                  registerField: "acApOrArNumber",
                  type: "number",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "A/P or A/R Vendor Number is Required",
                    },
                    formatError: {
                      pattern: {},
                      text: "Please enter valid A/P or A/R Vendor Number",
                    },
                    maxLength: {
                      length: 8,
                      text: "Maximum 8 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.acApOrArNumber",
                },
                vendorComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "vendorComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorComment",
                },
                vendorOfferTrackingNbr: {
                  label: "",
                  required: false,
                  registerField: "vendorOfferTrackingNbr",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Tracking Number is Required",
                    },
                    maxLength: {
                      length: 30,
                      text: "Maximum 30 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
                },
                commonComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "allComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                },
                billingInformationData: {
                  registerKeyName: "billingInformationData",
                },
              },
              isOpen: false,
              allowanceTypeAndPerformance: [
                "SCAN",
                "SHIP_TO_STORE",
                "HEADER_FLAT",
                "ITEM_FLAT",
              ],
              allowanceToBeCreated: [
                "One Allowance: Warehouse, DSD, or Combined",
                "Combined DSD",
              ],
              label: "Default Billing Information",
              offerTypeKey: "WAREHOUSE",
              allowanceScreenTypes: ["DP", "AO"],
              create: {
                isEdit: false,
                isView: false,
                label: "Save & Continue",
              },
              edit: {
                isEdit: true,
                isView: false,
                label: "Update & Continue",
              },
            }, // Provide required props
            offerIndex: 0,
            allowanceIndex: 0,
            step: 4,
            isLastStep: true,
            isEditEnable: true,
          }}
        ></CommonBillingInformation>
      </Wrapper>
    );

    const cancelButton = screen.getByRole("button", { name: "Cancel" });
    fireEvent.click(cancelButton);
    await waitFor(() => {
      fireEvent.click(cancelButton);

    });
    expect(screen.queryByText("Save Comment & Apply to All")).toBeInTheDocument();
  });

  it("handles save button click in 5th step", async () => {
    render(
      <Wrapper>
        <CommonBillingInformation
          {...{
            saveAndContinueHanlder: jest.fn(),
            stepperElement: {
              fields: {
                allowanceSpecificFields: {
                  CASE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  SCAN: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  SHIP_TO_STORE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  HEADER_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  ITEM_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  DEFAULT: {
                    key: "DSD_WHSE_RETAIL_DIVISION",
                    routeKey: "DSD_WHSE_RETAIL_DIVISION",
                    billingInformationData: {
                      registerKeyName:
                        "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                    },
                  },
                },
                suggestedVendorPaymentType: {
                  label: "Suggested Payment Type",
                  required: false,
                  registerField: "suggestedVendorPaymentType",
                  type: "select",
                  default: "Deduct",
                  displayLabel: "name",
                  options: [
                    {
                      name: "Select",
                      id: "",
                    },
                    {
                      name: "Deduct",
                      id: "Deduct",
                    },
                    {
                      name: "Invoice",
                      id: "Invoice",
                    },
                  ],
                  apiUrl: "",
                  slice: "",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "ABS Vendor Payment Type is Required",
                    },
                  },
                  tooltip:
                    "This is auto generated based on your selections.\n You can edit this if you want.",
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
                },
                acApOrArNumber: {
                  label: "Suggested A/P or A/R Number",
                  required: false,
                  registerField: "acApOrArNumber",
                  type: "number",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "A/P or A/R Vendor Number is Required",
                    },
                    formatError: {
                      pattern: {},
                      text: "Please enter valid A/P or A/R Vendor Number",
                    },
                    maxLength: {
                      length: 8,
                      text: "Maximum 8 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.acApOrArNumber",
                },
                vendorComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "vendorComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorComment",
                },
                vendorOfferTrackingNbr: {
                  label: "",
                  required: false,
                  registerField: "vendorOfferTrackingNbr",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Tracking Number is Required",
                    },
                    maxLength: {
                      length: 30,
                      text: "Maximum 30 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
                },
                commonComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "allComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                },
                billingInformationData: {
                  registerKeyName: "billingInformationData",
                },
              },
              isOpen: false,
              allowanceTypeAndPerformance: [
                "SCAN",
                "SHIP_TO_STORE",
                "HEADER_FLAT",
                "ITEM_FLAT",
              ],
              allowanceToBeCreated: [
                "One Allowance: Warehouse, DSD, or Combined",
                "Combined DSD",
              ],
              label: "Default Billing Information",
              offerTypeKey: "WAREHOUSE",
              allowanceScreenTypes: ["DP", "AO"],
              create: {
                isEdit: false,
                isView: false,
                label: "Save & Continue",
              },
              edit: {
                isEdit: true,
                isView: false,
                label: "Update & Continue",
              },
            }, // Provide required props
            offerIndex: 0,
            allowanceIndex: 0,
            step: 4,
            isLastStep: true,
            isEditEnable: false,
          }}
        ></CommonBillingInformation>
      </Wrapper>
    );

    const saveButton = screen.getByRole("button", {
      name: /save & create allowance/i,
    }); // Assuming this is the save button.
    await waitFor(() => {
        fireEvent.click(saveButton);
    });
  });
  it("handles save button click with different status", async () => {
    render(
      <Wrapper>
        <CommonBillingInformation
          {...{
            saveAndContinueHanlder: jest.fn(),
            stepperElement: {
              fields: {
                allowanceSpecificFields: {
                  CASE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  SCAN: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  SHIP_TO_STORE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  HEADER_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  ITEM_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  DEFAULT: {
                    key: "DSD_WHSE_RETAIL_DIVISION",
                    routeKey: "DSD_WHSE_RETAIL_DIVISION",
                    billingInformationData: {
                      registerKeyName:
                        "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                    },
                  },
                },
                suggestedVendorPaymentType: {
                  label: "Suggested Payment Type",
                  required: false,
                  registerField: "suggestedVendorPaymentType",
                  type: "select",
                  default: "Deduct",
                  displayLabel: "name",
                  options: [
                    {
                      name: "Select",
                      id: "",
                    },
                    {
                      name: "Deduct",
                      id: "Deduct",
                    },
                    {
                      name: "Invoice",
                      id: "Invoice",
                    },
                  ],
                  apiUrl: "",
                  slice: "",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "ABS Vendor Payment Type is Required",
                    },
                  },
                  tooltip:
                    "This is auto generated based on your selections.\n You can edit this if you want.",
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
                },
                acApOrArNumber: {
                  label: "Suggested A/P or A/R Number",
                  required: false,
                  registerField: "acApOrArNumber",
                  type: "number",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "A/P or A/R Vendor Number is Required",
                    },
                    formatError: {
                      pattern: {},
                      text: "Please enter valid A/P or A/R Vendor Number",
                    },
                    maxLength: {
                      length: 8,
                      text: "Maximum 8 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.acApOrArNumber",
                },
                vendorComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "vendorComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorComment",
                },
                vendorOfferTrackingNbr: {
                  label: "",
                  required: false,
                  registerField: "vendorOfferTrackingNbr",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Tracking Number is Required",
                    },
                    maxLength: {
                      length: 30,
                      text: "Maximum 30 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
                },
                commonComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "allComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                },
                billingInformationData: {
                  registerKeyName: "billingInformationData",
                },
              },
              isOpen: false,
              allowanceTypeAndPerformance: [
                "SCAN",
                "SHIP_TO_STORE",
                "HEADER_FLAT",
                "ITEM_FLAT",
              ],
              allowanceToBeCreated: [
                "One Allowance: Warehouse, DSD, or Combined",
                "Combined DSD",
              ],
              label: "Default Billing Information",
              offerTypeKey: "WAREHOUSE",
              allowanceScreenTypes: ["DP", "AO"],
              create: {
                isEdit: false,
                isView: false,
                label: "Save & Continue",
              },
              edit: {
                isEdit: true,
                isView: false,
                label: "Update & Continue",
              },
            }, // Provide required props
            offerIndex: 0,
            allowanceIndex: 0,
            step: 4,
            isLastStep: false,
            isEditEnable: false,
          }}
        ></CommonBillingInformation>
      </Wrapper>
    );

    const saveButton = screen.getByRole("button", {
      name: /Save & Continue/i,
    }); // Assuming this is the save button.
    fireEvent.click(saveButton);

    // Simulate saving form data and wait for async operations
    await waitFor(() => {
      expect(saveButton).toBeInTheDocument();
    });
  });

  it("handles update button click in edit", async () => {
    render(
      <Wrapper>
        <CommonBillingInformation
          {...{
            saveAndContinueHanlder: jest.fn(),
            stepperElement: {
              fields: {
                allowanceSpecificFields: {
                  CASE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  SCAN: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  SHIP_TO_STORE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  HEADER_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  ITEM_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  DEFAULT: {
                    key: "DSD_WHSE_RETAIL_DIVISION",
                    routeKey: "DSD_WHSE_RETAIL_DIVISION",
                    billingInformationData: {
                      registerKeyName:
                        "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                    },
                  },
                },
                suggestedVendorPaymentType: {
                  label: "Suggested Payment Type",
                  required: false,
                  registerField: "suggestedVendorPaymentType",
                  type: "select",
                  default: "Deduct",
                  displayLabel: "name",
                  options: [
                    {
                      name: "Select",
                      id: "",
                    },
                    {
                      name: "Deduct",
                      id: "Deduct",
                    },
                    {
                      name: "Invoice",
                      id: "Invoice",
                    },
                  ],
                  apiUrl: "",
                  slice: "",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "ABS Vendor Payment Type is Required",
                    },
                  },
                  tooltip:
                    "This is auto generated based on your selections.\n You can edit this if you want.",
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
                },
                acApOrArNumber: {
                  label: "Suggested A/P or A/R Number",
                  required: false,
                  registerField: "acApOrArNumber",
                  type: "number",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "A/P or A/R Vendor Number is Required",
                    },
                    formatError: {
                      pattern: {},
                      text: "Please enter valid A/P or A/R Vendor Number",
                    },
                    maxLength: {
                      length: 8,
                      text: "Maximum 8 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.acApOrArNumber",
                },
                vendorComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "vendorComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorComment",
                },
                vendorOfferTrackingNbr: {
                  label: "",
                  required: false,
                  registerField: "vendorOfferTrackingNbr",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Tracking Number is Required",
                    },
                    maxLength: {
                      length: 30,
                      text: "Maximum 30 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
                },
                commonComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "allComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                },
                billingInformationData: {
                  registerKeyName: "billingInformationData",
                },
              },
              isOpen: false,
              allowanceTypeAndPerformance: [
                "SCAN",
                "SHIP_TO_STORE",
                "HEADER_FLAT",
                "ITEM_FLAT",
              ],
              allowanceToBeCreated: [
                "One Allowance: Warehouse, DSD, or Combined",
                "Combined DSD",
              ],
              label: "Default Billing Information",
              offerTypeKey: "WAREHOUSE",
              allowanceScreenTypes: ["DP", "AO"],
              create: {
                isEdit: false,
                isView: false,
                label: "Save & Continue",
              },
              edit: {
                isEdit: true,
                isView: false,
                label: "Update & Continue",
              },
            }, // Provide required props
            offerIndex: 0,
            allowanceIndex: 0,
            step: 4,
            isLastStep: true,
            isEditEnable: true,
          }}
        ></CommonBillingInformation>
      </Wrapper>
    );
    const updateBtn = screen.getByRole("button", {
      name: /Update Offer/i,
    });
    await waitFor(() => {
      expect(updateBtn).toBeInTheDocument();
      fireEvent.click(updateBtn);
    });
  });
  it("displays error message when vendor tracking number exceeds char limit", async () => {
    render(
      <Wrapper>
        <CommonBillingInformation
          {...{
            saveAndContinueHanlder: jest.fn(),
            stepperElement: {
              fields: {
                allowanceSpecificFields: {
                  CASE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  SCAN: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  SHIP_TO_STORE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  HEADER_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  ITEM_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  DEFAULT: {
                    key: "DSD_WHSE_RETAIL_DIVISION",
                    routeKey: "DSD_WHSE_RETAIL_DIVISION",
                    billingInformationData: {
                      registerKeyName:
                        "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                    },
                  },
                },
                suggestedVendorPaymentType: {
                  label: "Suggested Payment Type",
                  required: false,
                  registerField: "suggestedVendorPaymentType",
                  type: "select",
                  default: "Deduct",
                  displayLabel: "name",
                  options: [
                    {
                      name: "Select",
                      id: "",
                    },
                    {
                      name: "Deduct",
                      id: "Deduct",
                    },
                    {
                      name: "Invoice",
                      id: "Invoice",
                    },
                  ],
                  apiUrl: "",
                  slice: "",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "ABS Vendor Payment Type is Required",
                    },
                  },
                  tooltip:
                    "This is auto generated based on your selections.\n You can edit this if you want.",
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
                },
                acApOrArNumber: {
                  label: "Suggested A/P or A/R Number",
                  required: false,
                  registerField: "acApOrArNumber",
                  type: "number",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "A/P or A/R Vendor Number is Required",
                    },
                    formatError: {
                      pattern: {},
                      text: "Please enter valid A/P or A/R Vendor Number",
                    },
                    maxLength: {
                      length: 8,
                      text: "Maximum 8 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.acApOrArNumber",
                },
                vendorComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "vendorComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorComment",
                },
                vendorOfferTrackingNbr: {
                  label: "",
                  required: false,
                  registerField: "vendorOfferTrackingNbr",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Tracking Number is Required",
                    },
                    maxLength: {
                      length: 30,
                      text: "Maximum 30 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
                },
                commonComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "allComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                },
                billingInformationData: {
                  registerKeyName: "billingInformationData",
                },
              },
              isOpen: false,
              allowanceTypeAndPerformance: [
                "SCAN",
                "SHIP_TO_STORE",
                "HEADER_FLAT",
                "ITEM_FLAT",
              ],
              allowanceToBeCreated: [
                "One Allowance: Warehouse, DSD, or Combined",
                "Combined DSD",
              ],
              label: "Default Billing Information",
              offerTypeKey: "WAREHOUSE",
              allowanceScreenTypes: ["DP", "AO"],
              create: {
                isEdit: false,
                isView: false,
                label: "Save & Continue",
              },
              edit: {
                isEdit: true,
                isView: false,
                label: "Update & Continue",
              },
            }, // Provide required props
            offerIndex: 0,
            allowanceIndex: 0,
            step: 4,
            isLastStep: true,
            isEditEnable: false,
          }}
        ></CommonBillingInformation>
      </Wrapper>
    );
    const inputField = screen.getAllByRole("textbox");
    fireEvent.change(inputField[3], { target: { value: "A".repeat(300) } });
    userEvent.type(inputField[3], "A".repeat(300));

    // Simulate form submit
    const submitButton = screen.getByRole("button", {
      name: /save & create allowance/i,
    });
    fireEvent.click(submitButton);

    await waitFor(() => {
      // Check for the error message for exceeding char limit
      expect(
        screen.getByText("Maximum 30 characters allowed")
      ).toBeInTheDocument();
    });
  });

  it("handles change in vendor comment", async () => {
    render(
      <Wrapper>
        <CommonBillingInformation
          {...{
            saveAndContinueHanlder: jest.fn(),
            stepperElement: {
              fields: {
                allowanceSpecificFields: {
                  CASE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  SCAN: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  SHIP_TO_STORE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  HEADER_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  ITEM_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  DEFAULT: {
                    key: "DSD_WHSE_RETAIL_DIVISION",
                    routeKey: "DSD_WHSE_RETAIL_DIVISION",
                    billingInformationData: {
                      registerKeyName:
                        "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                    },
                  },
                },
                suggestedVendorPaymentType: {
                  label: "Suggested Payment Type",
                  required: false,
                  registerField: "suggestedVendorPaymentType",
                  type: "select",
                  default: "Deduct",
                  displayLabel: "name",
                  options: [
                    {
                      name: "Select",
                      id: "",
                    },
                    {
                      name: "Deduct",
                      id: "Deduct",
                    },
                    {
                      name: "Invoice",
                      id: "Invoice",
                    },
                  ],
                  apiUrl: "",
                  slice: "",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "ABS Vendor Payment Type is Required",
                    },
                  },
                  tooltip:
                    "This is auto generated based on your selections.\n You can edit this if you want.",
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
                },
                acApOrArNumber: {
                  label: "Suggested A/P or A/R Number",
                  required: false,
                  registerField: "acApOrArNumber",
                  type: "number",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "A/P or A/R Vendor Number is Required",
                    },
                    formatError: {
                      pattern: {},
                      text: "Please enter valid A/P or A/R Vendor Number",
                    },
                    maxLength: {
                      length: 8,
                      text: "Maximum 8 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.acApOrArNumber",
                },
                vendorComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "vendorComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorComment",
                },
                vendorOfferTrackingNbr: {
                  label: "",
                  required: false,
                  registerField: "vendorOfferTrackingNbr",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Tracking Number is Required",
                    },
                    maxLength: {
                      length: 30,
                      text: "Maximum 30 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
                },
                commonComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "allComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                },
                billingInformationData: {
                  registerKeyName: "billingInformationData",
                },
              },
              isOpen: false,
              allowanceTypeAndPerformance: [
                "SCAN",
                "SHIP_TO_STORE",
                "HEADER_FLAT",
                "ITEM_FLAT",
              ],
              allowanceToBeCreated: [
                "One Allowance: Warehouse, DSD, or Combined",
                "Combined DSD",
              ],
              label: "Default Billing Information",
              offerTypeKey: "WAREHOUSE",
              allowanceScreenTypes: ["DP", "AO"],
              create: {
                isEdit: false,
                isView: false,
                label: "Save & Continue",
              },
              edit: {
                isEdit: true,
                isView: false,
                label: "Update & Continue",
              },
            }, // Provide required props
            offerIndex: 0,
            allowanceIndex: 0,
            step: 4,
            isLastStep: true,
            isEditEnable: false,
          }}
        ></CommonBillingInformation>
      </Wrapper>
    );

    const commentInput: any = screen.getAllByRole("textbox");
    fireEvent.change(commentInput?.[2], { target: { value: "Test Comment" } });
    expect(commentInput?.[2]?.value).toBe("Test Comment");
  });

  it("handles change in global vendor comment", async () => {
    render(
      <Wrapper>
        <CommonBillingInformation
          {...{
            saveAndContinueHanlder: jest.fn(),
            stepperElement: {
              fields: {
                allowanceSpecificFields: {
                  CASE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  SCAN: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  SHIP_TO_STORE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  HEADER_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  ITEM_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  DEFAULT: {
                    key: "DSD_WHSE_RETAIL_DIVISION",
                    routeKey: "DSD_WHSE_RETAIL_DIVISION",
                    billingInformationData: {
                      registerKeyName:
                        "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                    },
                  },
                },
                suggestedVendorPaymentType: {
                  label: "Suggested Payment Type",
                  required: false,
                  registerField: "suggestedVendorPaymentType",
                  type: "select",
                  default: "Deduct",
                  displayLabel: "name",
                  options: [
                    {
                      name: "Select",
                      id: "",
                    },
                    {
                      name: "Deduct",
                      id: "Deduct",
                    },
                    {
                      name: "Invoice",
                      id: "Invoice",
                    },
                  ],
                  apiUrl: "",
                  slice: "",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "ABS Vendor Payment Type is Required",
                    },
                  },
                  tooltip:
                    "This is auto generated based on your selections.\n You can edit this if you want.",
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
                },
                acApOrArNumber: {
                  label: "Suggested A/P or A/R Number",
                  required: false,
                  registerField: "acApOrArNumber",
                  type: "number",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "A/P or A/R Vendor Number is Required",
                    },
                    formatError: {
                      pattern: {},
                      text: "Please enter valid A/P or A/R Vendor Number",
                    },
                    maxLength: {
                      length: 8,
                      text: "Maximum 8 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.acApOrArNumber",
                },
                vendorComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "vendorComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorComment",
                },
                vendorOfferTrackingNbr: {
                  label: "",
                  required: false,
                  registerField: "vendorOfferTrackingNbr",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Tracking Number is Required",
                    },
                    maxLength: {
                      length: 30,
                      text: "Maximum 30 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
                },
                commonComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "allComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                },
                billingInformationData: {
                  registerKeyName: "billingInformationData",
                },
              },
              isOpen: false,
              allowanceTypeAndPerformance: [
                "SCAN",
                "SHIP_TO_STORE",
                "HEADER_FLAT",
                "ITEM_FLAT",
              ],
              allowanceToBeCreated: [
                "One Allowance: Warehouse, DSD, or Combined",
                "Combined DSD",
              ],
              label: "Default Billing Information",
              offerTypeKey: "WAREHOUSE",
              allowanceScreenTypes: ["DP", "AO"],
              create: {
                isEdit: false,
                isView: false,
                label: "Save & Continue",
              },
              edit: {
                isEdit: true,
                isView: false,
                label: "Update & Continue",
              },
            }, // Provide required props
            offerIndex: 0,
            allowanceIndex: 0,
            step: 4,
            isLastStep: true,
            isEditEnable: false,
          }}
        ></CommonBillingInformation>
      </Wrapper>
    );

    const commentInput: any = screen.getAllByRole("textbox");
    fireEvent.change(commentInput?.[0], { target: { value: "Test Comment" } });
    const saveCommentAndApplyBtn = screen.getByRole("button", {
      name: /Save Comment & Apply to All/i,
    });
    fireEvent.click(saveCommentAndApplyBtn);
    expect(commentInput?.[0]?.value).toBe("Test Comment");
  });

  it("handles change in payment type", async () => {
    render(
      <Wrapper>
        <CommonBillingInformation
          {...{
            saveAndContinueHanlder: jest.fn(),
            stepperElement: {
              fields: {
                allowanceSpecificFields: {
                  CASE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  SCAN: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  SHIP_TO_STORE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  HEADER_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  ITEM_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  DEFAULT: {
                    key: "DSD_WHSE_RETAIL_DIVISION",
                    routeKey: "DSD_WHSE_RETAIL_DIVISION",
                    billingInformationData: {
                      registerKeyName:
                        "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                    },
                  },
                },
                suggestedVendorPaymentType: {
                  label: "Suggested Payment Type",
                  required: false,
                  registerField: "suggestedVendorPaymentType",
                  type: "select",
                  default: "Deduct",
                  displayLabel: "name",
                  options: [
                    {
                      name: "Select",
                      id: "",
                    },
                    {
                      name: "Deduct",
                      id: "Deduct",
                    },
                    {
                      name: "Invoice",
                      id: "Invoice",
                    },
                  ],
                  apiUrl: "",
                  slice: "",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "ABS Vendor Payment Type is Required",
                    },
                  },
                  tooltip:
                    "This is auto generated based on your selections.\n You can edit this if you want.",
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
                },
                acApOrArNumber: {
                  label: "Suggested A/P or A/R Number",
                  required: false,
                  registerField: "acApOrArNumber",
                  type: "number",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "A/P or A/R Vendor Number is Required",
                    },
                    formatError: {
                      pattern: {},
                      text: "Please enter valid A/P or A/R Vendor Number",
                    },
                    maxLength: {
                      length: 8,
                      text: "Maximum 8 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.acApOrArNumber",
                },
                vendorComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "vendorComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorComment",
                },
                vendorOfferTrackingNbr: {
                  label: "",
                  required: false,
                  registerField: "vendorOfferTrackingNbr",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Tracking Number is Required",
                    },
                    maxLength: {
                      length: 30,
                      text: "Maximum 30 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
                },
                commonComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "allComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                },
                billingInformationData: {
                  registerKeyName: "billingInformationData",
                },
              },
              isOpen: false,
              allowanceTypeAndPerformance: [
                "SCAN",
                "SHIP_TO_STORE",
                "HEADER_FLAT",
                "ITEM_FLAT",
              ],
              allowanceToBeCreated: [
                "One Allowance: Warehouse, DSD, or Combined",
                "Combined DSD",
              ],
              label: "Default Billing Information",
              offerTypeKey: "WAREHOUSE",
              allowanceScreenTypes: ["DP", "AO"],
              create: {
                isEdit: false,
                isView: false,
                label: "Save & Continue",
              },
              edit: {
                isEdit: true,
                isView: false,
                label: "Update & Continue",
              },
            }, // Provide required props
            offerIndex: 0,
            allowanceIndex: 0,
            step: 4,
            isLastStep: true,
            isEditEnable: false,
          }}
        ></CommonBillingInformation>
      </Wrapper>
    );

    const paymentTypeSlect = screen.getByRole("button", { name: "Select" });
    fireEvent.click(paymentTypeSlect);
    const invoivceSel = screen.getByText("Invoice");
    fireEvent.click(invoivceSel);
    expect(screen.getByText("Invoice")).toBeInTheDocument();
  });

  it("handles change in ap/ar number", async () => {
    render(
      <Wrapper>
        <CommonBillingInformation
          {...{
            saveAndContinueHanlder: jest.fn(),
            stepperElement: {
              fields: {
                allowanceSpecificFields: {
                  CASE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  SCAN: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  SHIP_TO_STORE: {
                    DSD: {
                      key: "DSD_LEAD_DISTRIBUTORS",
                      routeKey: "DSD_LEAD_DISTRIBUTORS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_LEAD_DISTRIBUTORS",
                      },
                    },
                    WAREHOUSE: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                  },
                  HEADER_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  ITEM_FLAT: {
                    DSD: {
                      key: "DSD_WHSE_RETAIL_DIVISION",
                      routeKey: "DSD_WHSE_RETAIL_DIVISION",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                      },
                    },
                    WAREHOUSE: {
                      key: "WAREHOUSE_DIST_CENTERS",
                      routeKey: "WAREHOUSE_DIST_CENTERS",
                      billingInformationData: {
                        registerKeyName:
                          "billingInformationData.WAREHOUSE_DIST_CENTERS",
                      },
                    },
                  },
                  DEFAULT: {
                    key: "DSD_WHSE_RETAIL_DIVISION",
                    routeKey: "DSD_WHSE_RETAIL_DIVISION",
                    billingInformationData: {
                      registerKeyName:
                        "billingInformationData.DSD_WHSE_RETAIL_DIVISION",
                    },
                  },
                },
                suggestedVendorPaymentType: {
                  label: "Suggested Payment Type",
                  required: false,
                  registerField: "suggestedVendorPaymentType",
                  type: "select",
                  default: "Deduct",
                  displayLabel: "name",
                  options: [
                    {
                      name: "Select",
                      id: "",
                    },
                    {
                      name: "Deduct",
                      id: "Deduct",
                    },
                    {
                      name: "Invoice",
                      id: "Invoice",
                    },
                  ],
                  apiUrl: "",
                  slice: "",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "ABS Vendor Payment Type is Required",
                    },
                  },
                  tooltip:
                    "This is auto generated based on your selections.\n You can edit this if you want.",
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
                },
                acApOrArNumber: {
                  label: "Suggested A/P or A/R Number",
                  required: false,
                  registerField: "acApOrArNumber",
                  type: "number",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "A/P or A/R Vendor Number is Required",
                    },
                    formatError: {
                      pattern: {},
                      text: "Please enter valid A/P or A/R Vendor Number",
                    },
                    maxLength: {
                      length: 8,
                      text: "Maximum 8 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.acApOrArNumber",
                },
                vendorComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "vendorComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorComment",
                },
                vendorOfferTrackingNbr: {
                  label: "",
                  required: false,
                  registerField: "vendorOfferTrackingNbr",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Tracking Number is Required",
                    },
                    maxLength: {
                      length: 30,
                      text: "Maximum 30 characters allowed",
                    },
                  },
                  mapperKey:
                    "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
                },
                commonComment: {
                  label: "Vendor Comment",
                  required: false,
                  registerField: "allComment",
                  type: "text",
                  errors: {
                    required: {
                      backgroundColor: "",
                      text: "Vendor Comment is Required",
                    },
                    maxLength: {
                      length: 1000,
                      text: "Maximum 1000 characters allowed",
                    },
                  },
                },
                billingInformationData: {
                  registerKeyName: "billingInformationData",
                },
              },
              isOpen: false,
              allowanceTypeAndPerformance: [
                "SCAN",
                "SHIP_TO_STORE",
                "HEADER_FLAT",
                "ITEM_FLAT",
              ],
              allowanceToBeCreated: [
                "One Allowance: Warehouse, DSD, or Combined",
                "Combined DSD",
              ],
              label: "Default Billing Information",
              offerTypeKey: "WAREHOUSE",
              allowanceScreenTypes: ["DP", "AO"],
              create: {
                isEdit: false,
                isView: false,
                label: "Save & Continue",
              },
              edit: {
                isEdit: true,
                isView: false,
                label: "Update & Continue",
              },
            }, // Provide required props
            offerIndex: 0,
            allowanceIndex: 0,
            step: 4,
            isLastStep: true,
            isEditEnable: false,
          }}
        ></CommonBillingInformation>
      </Wrapper>
    );
    const apArBox: any = screen.getAllByRole("textbox");
    fireEvent.change(apArBox?.[1], { target: { value: "12345678" } });
    expect(apArBox?.[1]?.value).toBe("12345678");
  });

});
