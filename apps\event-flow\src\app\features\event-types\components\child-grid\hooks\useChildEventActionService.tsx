import React from "react";
import {
  useDeleteEventMutation,
  usePostCancelMutation,
  usePostRejectMutation,
} from "../../event-action/event-action-service";

function useChildEventActionService() {
  const [
    deleteChildEvent,
    {
      isLoading: isDeleteLoading,
      isSuccess: isDeleteSuccess,
    },
  ] = useDeleteEventMutation();
  const [postChilEventReject, { isLoading: isPostRejectionLoading }] =
    usePostRejectMutation();
  const [postChildEventCancel, { isLoading: isPostCancelPending }] =
    usePostCancelMutation();

  const actionBtnMutationMapper = {
    deleteChildEvent: {
      mutationHandler: deleteChildEvent,
      isLoading: isDeleteLoading,
      isSuccess: isDeleteSuccess,
    },
    rejectChildEvent: {
      mutationHandler: postChilEventReject,
      isLoading: isPostRejectionLoading,
    },
    cancelChildEvent: {
      mutationHandler: postChildEventCancel,
      isLoading: isPostCancelPending,
    },
  };
  return actionBtnMutationMapper;
}

export default useChildEventActionService;
