//import { formateVehicleDate } from "../../../../../apps/event-flow/src/app/features/create-event/service/event-details/event-detail-service";

import { appConstants } from "@me/utils-root-props";
import {
  isEventReturn,
  HIDDEN_PRICE_MAPPER_KEYS,
  isAllowanceFeatureEnabled,
} from "./common-helpers";
import {
  EEVENT_STATUS,
  EVENT_ACTION_LABELS,
  UOM_KEY_VALUE_MAPPER,
} from "./common-helpers.model";
import {
  PROMO_DETAILS_HIDDEN_PRICING_FEATURE,
  STATUS_CONSTANTS,
} from "./constants/status-constants";

const statusUserMapper = {
  VENDOR: "Pending With Vendor",
  MERCHANT: "Pending With Merchant",
};
const moduleHistoryMapper = {
  allowance: "offerAllowanceChanges",
  promotion: "promotionsChanges",
};
const searchIdMap = {
  allowance: "offerNumber",
  promotion: "promotionId",
};
const pendingObjMapper = {
  allowance: "offerAllowances",
  promotion: "promotionsList",
};
const userTypeMapping = {
  allowance: "offerallowances.updateUser.type",
  promotion: "promotions.updateUser.type",
};
enum EUSER_ROLES {
  MERCHANT = "MERCHANT",
  VENDOR = "VENDOR",
}
enum STATUS_KEY_MAPPER {
  allowance = "allowanceStatus",
  promotion = "promotionWorkflowStatus",
}
const CHECK_HISTORY_ID_KEY_LIST = [
  "offerallowances.allowances.allowanceDateOffsets.allowanceTypes",
  "offerallowances.allowances.performance.performance",
  "offerallowances.allowances.allowanceItems.allowanceItemComps.allowAmount",
  "offerallowances.allowances.allowanceItems.uom",
  "offerallowances.allowances.headerFlatAmt",
  "offerallowances.allowances.allowanceBillingInfo.suggestedVendorPaymentType",
  "offerallowances.allowances.allowanceBillingInfo.suggestedAcPayableVendorNbr",
  "offerallowances.allowances.allowanceBillingInfo.suggestedAcReceivableVendorNbr",
  "offerallowances.allowances.allowanceBillingInfo.vendorComment",
  "offerallowances.allowances.allowanceBillingInfo.vendorOfferTrackingNbr",
  "offerallowances.allowances.orderEndDate",
  "offerallowances.allowances.orderStartDate",
  "offerallowances.allowances.arrivalStartDate",
  "offerallowances.allowances.arrivalEndDate",
  "offerallowances.allowances.shipStartDate",
  "offerallowances.allowances.shipEndDate",
  "offerallowances.allowances.allowanceBillingInfo.absMerchVendor",
  "offerallowances.allowances.allowanceBillingInfo.absVendorName",
  "offerallowances.allowances.allowanceBillingInfo.absVendorPaymentType",
  "offerallowances.allowances.allowanceBillingInfo.acPayableVendorNbr",
  "offerallowances.allowances.allowanceBillingInfo.acReceivableVendorNbr",
];

export const getLoggedInUserType = () => {
  const userType = localStorage.getItem("USER_TYPE");
  return !userType
    ? window["OAM_USER_EMAIL"]?.includes("safeway")
      ? EUSER_ROLES.MERCHANT
      : EUSER_ROLES.VENDOR
    : userType;
};
const loggedInUserType = getLoggedInUserType(); // either vendor or merchant

const getUpdatedFieldValue = (
  fieldLevelChange,
  planPendingObj,
  module,
  searchId,
  latestLastUpdateUserType
) => {
  const moduleCardList = planPendingObj?.[`${pendingObjMapper[module]}`], // will get either offerAllowances or promotionList
    updatedPendgAllowPromObj = moduleCardList?.find(
      cardObj => cardObj[`${searchIdMap[module]}`] === searchId
    ),
    type = updatedPendgAllowPromObj?.updateUser?.type;
  const { beforeVal, afterVal } = fieldLevelChange;
  return {
    colorName:
      (latestLastUpdateUserType || type)?.toLowerCase() === loggedInUserType?.toLowerCase()
        ? "#1B6EBB"
        : "#AB4205",
    prevValue: beforeVal,
    currentValue: afterVal,
  };
};
const checkKeyFromHistOrPendingChange = (
  changeObj,
  historyMapperKey,
  allowId
) => {
  return CHECK_HISTORY_ID_KEY_LIST.includes(historyMapperKey)
    ? changeObj?.historyGroupId == allowId &&
    changeObj?.labelFieldName === historyMapperKey
    : checkForHiddenPriceInHistory(changeObj, historyMapperKey);
};

const checkForHiddenPriceInHistory = (changeObj, historyMapperKey) => {
  const { hiddenPricingFeatureEnabled } = PROMO_DETAILS_HIDDEN_PRICING_FEATURE;
  if (
    hiddenPricingFeatureEnabled &&
    loggedInUserType === EUSER_ROLES.MERCHANT &&
    HIDDEN_PRICE_MAPPER_KEYS?.[historyMapperKey]
  ) {
    return HIDDEN_PRICE_MAPPER_KEYS?.[historyMapperKey]?.includes(
      changeObj?.labelFieldName
    );
  } else {
    return changeObj?.labelFieldName === historyMapperKey;
  }
};
const getPayloadForMainEntryHighlight = (
  eventData,
  vendorObj,
  offerAllowId,
  isShowUOMOption,
  headerFlatAmt
) => {
  const { planEvent, planEventPending } = eventData;
  const loggedInUserType = getLoggedInUserType(),
    offerInPending = planEventPending?.offerAllowances?.find(
      oj => oj?.id === offerAllowId
    ),
    offerInPlan = planEvent?.offerAllowances?.find(
      oj => oj?.id === offerAllowId
    ),
    currentOffer = offerInPending || offerInPlan,
    allowStatus = currentOffer?.allowances?.[0]?.allowanceStatus,
    offerNumber = currentOffer?.offerNumber;
    const isReturnForDraft = isEventReturnForDraft(eventData);
  return {
    loggedInUserType,
    allowStatus,
    vendorObj,
    planEventPending,
    offerNumber,
    headerFlatAmt,
    isShowUOMOption,
    planEvent,
    isReturnForDraft
  };
};
export const highlightItemAmtMainEntry = (
  eventData,
  vendorObj,
  offerAllowId,
  isShowUOMOption,
  headerFlatAmt = false
) => {
  const payload = getPayloadForMainEntryHighlight(
    eventData,
    vendorObj,
    offerAllowId,
    isShowUOMOption,
    headerFlatAmt
  ),
    statusMapper = getStatusMapperUtility(payload, vendorObj),
    utilityFunc = statusMapper?.[eventData?.planEvent?.eventStatus];
  return utilityFunc?.();
};

const getStatusMapperUtility = (payload, vendorObj) => {
  return {
    [STATUS_CONSTANTS.DRAFT]: () =>
      takeCurrentFromAllowance(vendorObj, payload, true),
    [EEVENT_STATUS.REJECTED]: () =>
      takeCurrentFromAllowance(vendorObj, payload, true),
    [EEVENT_STATUS.CANCELED]: () =>
      takeCurrentFromAllowance(vendorObj, payload, true),
    [STATUS_CONSTANTS.PENDING_WITH_VENDOR]: () =>
      mainEntryUtilityForPWMorPWV(payload),
    [STATUS_CONSTANTS.PENDING_WITH_MERCHANT]: () =>
      mainEntryUtilityForPWMorPWV(payload),
    [STATUS_CONSTANTS.AGREED]: () => mainEntryUtilityForAgreed(payload),
    [STATUS_CONSTANTS.AGREED_PENDING]: () => mainEntryUtilityForAgreed(payload),
    [STATUS_CONSTANTS.EXECUTED]: () => mainEntryUtilityForAgreed(payload),
    [STATUS_CONSTANTS.ACTIVE]: () => mainEntryUtilityForAgreed(payload),
  };
};
const takeCurrentFromAllowance = (
  vendorObj,
  payload,
  hideStrikeOff = false
) => {
  const { currentVal, prevVal, color } = getAmtKeyForMainEntry(
    vendorObj,
    payload
  );
  return {
    currentVal,
    prevVal,
    showStrikeOff: hideStrikeOff ? false : prevVal && prevVal != currentVal,
    color,
  };
};
const getAmtKeyForMainEntry = (vendorObj, payload) => {
  const {
    currentHeaderAmtValue,
    prevHeaderAmtValue,
    prevAllowAmtValue,
    allowanceAmount,
    updateUser,
    allowUomType,
    prevUomValue,
  } = vendorObj || {},
    {
      headerFlatAmt: isHeaderFlatAmt,
      loggedInUserType,
      isShowUOMOption,
    } = payload,
    latestUpdateUserType = updateUser?.type,
    currentAmt = isHeaderFlatAmt ? currentHeaderAmtValue : allowanceAmount,
    prevAmt = isHeaderFlatAmt ? prevHeaderAmtValue : prevAllowAmtValue,
    currentUom = allowUomType ? UOM_KEY_VALUE_MAPPER[allowUomType] : "",
    prevUom = prevUomValue ? UOM_KEY_VALUE_MAPPER[prevUomValue] : "";
  return {
    currentVal: `${parseFloat(currentAmt)?.toFixed(2)} ${isShowUOMOption ? currentUom : ""
      }`,
    prevVal: prevAmt
      ? `${parseFloat(prevAmt)?.toFixed(2)} ${isShowUOMOption ? prevUom : ""}`
      : "",
    color:
      loggedInUserType?.toLowerCase() === latestUpdateUserType?.toLowerCase()
        ? "#1B6EBB"
        : "#AB4205",
  };
};
const mainEntryUtilityForAgreed = configObj => {
  const {
    vendorObj,
    allowStatus,
    loggedInUserType,
    offerNumber,
    planEventPending,
    planEvent,
  } = configObj,
    { pendingChangeFlag } = vendorObj;
  const offerFromPending = planEventPending?.offerAllowances?.find(
    cardObj => cardObj.offerNumber === offerNumber
  ),
    offerFromPlan = planEvent?.offerAllowances?.find(
      cardObj => cardObj.offerNumber === offerNumber
    ),
    moduleCardList = pendingChangeFlag ? offerFromPending : offerFromPlan,
    type = moduleCardList?.updateUser?.type;
  const { currentVal, prevVal } = getAmtKeyForMainEntry(vendorObj, configObj);
  if (pendingChangeFlag) {
    if (loggedInUserType?.toLowerCase() === type?.toLowerCase()) {
      return takeCurrentFromAllowance(vendorObj, configObj);
    } else {
      if (
        [
          STATUS_CONSTANTS.AGREED,
          STATUS_CONSTANTS.ACTIVE,
          EEVENT_STATUS.CANCELED,
          EEVENT_STATUS.EXECUTED,
        ].includes(allowStatus)
      ) {
        return getPrevValueAndNoStrikeOff(prevVal, currentVal);
      }
      return takeCurrentFromAllowance(vendorObj, configObj);
    }
  } else {
    return takeCurrentFromAllowance(vendorObj, configObj);
  }
};

const getPrevValueAndNoStrikeOff = (prevVal, currentVal) => {
  return {
    currentVal: prevVal != currentVal ? prevVal : currentVal,
    prevVal: prevVal,
    showStrikeOff: false,
    color: "",
  };
};

const mainEntryUtilityForPWMorPWV = configObj => {
  const { vendorObj, allowStatus, loggedInUserType, isReturnForDraft } = configObj,
    { pendingChangeFlag } = vendorObj;
  const { currentVal, prevVal } = getAmtKeyForMainEntry(vendorObj, configObj);
  if (pendingChangeFlag) {
    if (isReturnForDraft) {
      return takeCurrentFromAllowance(vendorObj, configObj, true);
    }
    if (
      statusUserMapper?.[loggedInUserType] === allowStatus ||
      allowStatus === EEVENT_STATUS.DRAFT
    ) {
      return takeCurrentFromAllowance(vendorObj, configObj);
    } else {
      return getPrevValueAndNoStrikeOff(prevVal, currentVal);
    }
  } else {
    return takeCurrentFromAllowance(vendorObj, configObj);
  }
};
export const highlightFieldForPreview = ({
  planValue,
  pendingValue,
  planEventPendingChanges,
  planEventHistory,
  indicatorValue,
  planPendingObj,
  module,
  historyMapperKey,
  searchId,
  allowId,
  allowPromoStatus,
  isNational = false
}) => {
  if ([EEVENT_STATUS.REJECTED].includes(allowPromoStatus))
    return getFromPlan(planValue);
  const allowTypeChngVal = returnFieldValForAllowType({
    indicatorValue,
    planEventPendingChanges,
    pendingValue,
    planValue,
    planEventHistory,
    searchId,
    module,
  });
  if (allowTypeChngVal) return allowTypeChngVal;
  if (indicatorValue) {
    if (
      statusUserMapper[loggedInUserType] === allowPromoStatus ||
      allowPromoStatus == "Draft"
    ) {
      const currentChangeObj = planEventPendingChanges?.[
        moduleHistoryMapper[module]
      ]?.find(histObj => histObj?.[searchIdMap[module]] === searchId),
        fieldLevelChange = currentChangeObj?.changes?.find(changeObj =>
          checkKeyFromHistOrPendingChange(changeObj, historyMapperKey, allowId)
        );
      const lastUpdateUserType = currentChangeObj?.changes?.find(
        changeObj => changeObj?.labelFieldName === userTypeMapping?.[module]
      );
      const { afterVal: latestLastUpdateUserType } = lastUpdateUserType || {};
      if (fieldLevelChange) {
        return getUpdatedFieldValue(
          fieldLevelChange,
          planPendingObj,
          module,
          searchId,
          isNational ? latestLastUpdateUserType : null
        );
      }
    } else {
      return getCommonFieldUpdate(
        planEventHistory,
        historyMapperKey,
        module,
        searchId,
        loggedInUserType,
        planValue,
        allowId,
        allowPromoStatus
      );
    }
  }
  return getCommonFieldUpdate(
    planEventHistory,
    historyMapperKey,
    module,
    searchId,
    loggedInUserType,
    planValue,
    allowId,
    allowPromoStatus
  );
};

export const hightlightUtiltyForAgreed = ({
  planValue,
  pendingValue,
  planEventPendingChanges,
  planEventHistory,
  indicatorValue,
  planPendingObj,
  module,
  historyMapperKey,
  searchId,
  allowId,
  allowPromoStatus,
}) => {
  const allowTypeChngVal = returnFieldValForAllowType({
    indicatorValue,
    planEventPendingChanges,
    pendingValue,
    planValue,
    planEventHistory,
    searchId,
    module,
  });
  if (allowTypeChngVal) return allowTypeChngVal;
  if (indicatorValue) {
    const currentChangeObj = planEventPendingChanges?.[
      moduleHistoryMapper[module]
    ]?.find(histObj => histObj?.[searchIdMap[module]] === searchId),
      fieldLevelChange = currentChangeObj?.changes?.find(changeObj =>
        checkKeyFromHistOrPendingChange(changeObj, historyMapperKey, allowId)
      ),
      lastUpdateUserType = currentChangeObj?.changes?.find(
        changeObj => changeObj?.labelFieldName === userTypeMapping[module]
      );
    const moduleCardList = planPendingObj?.[`${pendingObjMapper[module]}`], // will get either offerAllowances or promotionList
      updatedPendgAllowPromObj = moduleCardList?.find(
        cardObj => cardObj[`${searchIdMap[module]}`] === searchId
      ),
      type = updatedPendgAllowPromObj?.updateUser?.type;
    const { afterVal: latestLastUpdateUserType } = lastUpdateUserType || {};
    if (
      fieldLevelChange &&
      (latestLastUpdateUserType || type)?.toLowerCase() ===
      loggedInUserType?.toLowerCase()
    ) {
      const { beforeVal, afterVal } = fieldLevelChange;
      return {
        colorName:
          (latestLastUpdateUserType || type)?.toLowerCase() ===
            loggedInUserType?.toLowerCase()
            ? "#1B6EBB"
            : "#AB4205",
        prevValue: beforeVal,
        currentValue: afterVal,
      };
    } else {
      if (
        [
          STATUS_CONSTANTS.AGREED,
          STATUS_CONSTANTS.ACTIVE,
          EEVENT_STATUS.CANCELED,
          EEVENT_STATUS.EXECUTED,
        ].includes(allowPromoStatus)
      )
        return getFromPlan(planValue);
      return getCommonFieldUpdate(
        planEventHistory,
        historyMapperKey,
        module,
        searchId,
        loggedInUserType,
        planValue,
        allowId,
        allowPromoStatus
      );
    }
  }
  if (
    [
      STATUS_CONSTANTS.AGREED,
      STATUS_CONSTANTS.ACTIVE,
      EEVENT_STATUS.CANCELED,
      EEVENT_STATUS.EXECUTED,
    ].includes(allowPromoStatus)
  )
    return getFromPlan(planValue);
  return getCommonFieldUpdate(
    planEventHistory,
    historyMapperKey,
    module,
    searchId,
    loggedInUserType,
    planValue,
    allowId,
    allowPromoStatus
  );
};
const getCurrentHistoryObj = (planEventHistory, module, searchId) => {
  const historyDataLength = planEventHistory?.length;
  if (historyDataLength) {
    const lastHistoryObj = planEventHistory?.[historyDataLength - 1],
      currentHistoryObj = lastHistoryObj?.[moduleHistoryMapper[module]]?.find(
        histObj => histObj?.[searchIdMap?.[module]] === searchId
      );
    return currentHistoryObj;
  }
};
const getCurrentPendingChangeObj = (
  planEventPendingChanges,
  module,
  searchId
) => {
  const currentChangeObj = planEventPendingChanges?.[
    moduleHistoryMapper?.[module]
  ]?.find(histObj => histObj?.[searchIdMap?.[module]] === searchId);
  return currentChangeObj;
};
const isAllowTypeChange = ({
  planEventHistory,
  module,
  searchId,
  planEventPendingChanges,
}) => {
  if (!isAllowanceFeatureEnabled || module !== "allowance")
    return { isAllowChange: false };
  const currentHistObj = getCurrentHistoryObj(
    planEventHistory,
    module,
    searchId
  );
  const currentChangeObj = getCurrentPendingChangeObj(
    planEventPendingChanges,
    module,
    searchId
  );
  const isAllowanceChangedInPending = currentChangeObj?.changes?.find(
    item => item?.labelFieldName === "allowanceType"
  );
  const isAllowanceChangedInHistory = currentHistObj?.changes?.find(
    item => item?.labelFieldNam === "allowanceType"
  );
  return {
    isAllowChange: isAllowanceChangedInPending || isAllowanceChangedInHistory,
    isAllowanceChangedInPending,
    isAllowanceChangedInHistory,
  };
};
const returnFieldValForAllowType = ({
  indicatorValue,
  planEventPendingChanges,
  pendingValue,
  planValue,
  planEventHistory,
  searchId,
  module,
}) => {
  if (!isAllowanceFeatureEnabled || module !== "allowance") return null;
  const {
    isAllowChange,
    isAllowanceChangedInPending,
    isAllowanceChangedInHistory,
  } = isAllowTypeChange({
    planEventHistory,
    module,
    searchId,
    planEventPendingChanges,
  });
  if (!isAllowChange) return null;
  if (indicatorValue) {
    if (isAllowanceChangedInPending) {
      return {
        colorName: "",
        prevValue: "",
        currentValue: pendingValue,
      };
    }
  }
  if (isAllowanceChangedInHistory) {
    return {
      colorName: "",
      prevValue: "",
      currentValue: planValue,
    };
  }
  return null;
};

export const getSearchId = (
  module,
  offerAllow,
  promoList,
  cardIndex,
  cardItemIndex
) => {
  const searchIdMapper = {
    allowance: offerAllow?.[cardIndex]?.offerNumber,
    promotion: promoList?.[0]?.promotionsList[cardItemIndex]?.promotionId,
  };
  return searchIdMapper[module];
};

export const checkIsPrevStatusDraft = (prevHistoryObj, key) => {
  const { eventChanges } = prevHistoryObj || {
    eventChanges: [],
  };
  const isPrevStatusDraft = eventChanges?.find(changeObj => {
    return (
      changeObj?.labelFieldName === key &&
      changeObj?.beforeVal === EEVENT_STATUS.DRAFT
    );
  });
  return toString.call(isPrevStatusDraft) === "[object Object]";
};

const getCommonFieldUpdate = (
  planEventHistory,
  historyMapperkey,
  module,
  searchId,
  loggedInUserType,
  planValue,
  allowId,
  allowPromoStatus
) => {
  const historyDataLength = planEventHistory?.length;

  if (historyDataLength) {
    const lastHistoryObj = planEventHistory?.[historyDataLength - 1],
      { createUser } = lastHistoryObj,
      currentHistoryObj = lastHistoryObj?.[moduleHistoryMapper[module]]?.find(
        histObj => histObj[searchIdMap[module]] === searchId
      );

    const { isReturnEvent, isReturnEventByCurrUser } = isEventReturn(
      lastHistoryObj,
      EVENT_ACTION_LABELS.RETURN,
      loggedInUserType
    );

    const { changes = [] } = currentHistoryObj || {};
    const fieldLevelHistory = changes?.find(changeObj =>
      checkKeyFromHistOrPendingChange(changeObj, historyMapperkey, allowId)
    );
    const lastUpdateuserType = changes?.find(
      changeObj => changeObj?.labelFieldName === userTypeMapping[module]
    );
    if (isReturnEvent) {
      const lastButOneHistoryObj = planEventHistory?.[historyDataLength - 2],
        prevHistoryObj = lastButOneHistoryObj?.[
          moduleHistoryMapper[module]
        ]?.find(histObj => histObj[searchIdMap[module]] === searchId);

      const isPrevStatusDraft = checkIsPrevStatusDraft(
        lastButOneHistoryObj,
        "planevents.eventStatus"
      );
      if (isReturnEventByCurrUser) {
        if (!isPrevStatusDraft) {
          const { changes = [], nonDisplayChanges = [] } = prevHistoryObj || {};
          const combinedChanges =
            loggedInUserType === EUSER_ROLES.MERCHANT
              ? [...changes, ...nonDisplayChanges]
              : changes;

          const prevFieldLevelHistory = combinedChanges?.find(changeObj =>
            checkKeyFromHistOrPendingChange(
              changeObj,
              historyMapperkey,
              allowId
            )
          );
          if (prevFieldLevelHistory) {
            const { beforeVal: beforeValPrev, afterVal: afterValPrev } =
              prevFieldLevelHistory;
            return {
              colorName: "#1B6EBB",
              prevValue: beforeValPrev,
              currentValue: afterValPrev,
            };
          } else {
            return getFromPlan(planValue);
          }
        } else {
          // const { beforeVal } = fieldLevelHistory;
          return fieldLevelHistory
            ? {
              colorName: "",
              prevValue: "",
              currentValue: fieldLevelHistory?.beforeVal,
            }
            : getFromPlan(planValue);
        }
      } else {
        if (!isPrevStatusDraft) {
          const { changes = [] } = prevHistoryObj || {};
          const prevFieldLevelHistory = changes?.find(changeObj =>
            checkKeyFromHistOrPendingChange(
              changeObj,
              historyMapperkey,
              allowId
            )
          );
          if (
            prevFieldLevelHistory &&
            statusUserMapper[loggedInUserType] === allowPromoStatus
          ) {
            const { beforeVal: beforeValPrev, afterVal: afterValPrev } =
              prevFieldLevelHistory;
            return {
              colorName: "#AB4205",
              prevValue: beforeValPrev,
              currentValue: afterValPrev,
            };
          } else {
            return getFromPlan(planValue);
          }
        } else {
          return fieldLevelHistory
            ? {
              colorName: "",
              prevValue: "",
              currentValue: fieldLevelHistory?.beforeVal,
            }
            : getFromPlan(planValue);
        }
      }
    }
    if (fieldLevelHistory && (lastUpdateuserType || createUser?.type)) {
      const { beforeVal, afterVal } = fieldLevelHistory;
      const { afterVal: latestLastUpdateUser } = lastUpdateuserType || {};

      return {
        colorName:
          (latestLastUpdateUser || createUser?.type)?.toUpperCase() ===
            loggedInUserType?.toUpperCase()
            ? "#1B6EBB"
            : "#AB4205",
        prevValue: beforeVal,
        currentValue: afterVal,
      };
    } else {
      return getFromPlan(planValue);
    }
  } else {
    return getFromPlan(planValue);
  }
};

const getFromPlan = planValue => {
  return {
    colorName: "",
    prevValue: "",
    currentValue: planValue,
  };
};
const getAllowPromoStatus = (
  module,
  offerAllowances,
  promotionsLists,
  cardIndex,
  cardItemIndex
) => {
  const mapper = {
    allowance: offerAllowances?.[cardIndex]?.allowances?.[cardItemIndex],
    promotion: promotionsLists?.[0]?.promotionsList?.[cardItemIndex],
  };
  return mapper?.[module]?.[STATUS_KEY_MAPPER?.[module]];
};

export const getFieldLevelConfig = (
  configObj,
  getValues,
  module,
  fieldsMapper,
  customPlanValue,
  cardIndex,
  cardItemIndex,
  allowanceType = ""
) => {
  const {
    planEventHistory,
    planEventPending,
    planEventPendingChanges,
    planEvent,
    promotionsLists,
    offerAllowances,
    eventStatus,
  } = getValues(),
    { historyMapperKey, allowTypeHistoryKeyMapper = null, key } = configObj,
    planValue = getPlanValue({
      configObj,
      getValues,
      fieldsMapper,
      customPlanValue,
    }),
    indicatorValue = getIndicatorValue(getValues, module);
  const searchId = getSearchId(
    module,
    offerAllowances,
    promotionsLists,
    cardIndex,
    cardItemIndex
  );
  const allowId = getAllowIdFromOffer(
    offerAllowances,
    cardIndex,
    cardItemIndex
  );
  const pendingValue = getPendingValue({
    configObj,
    getValues,
    fieldsMapper,
    allowId,
    searchId,
    module,
  });
  const allowPromoStatus = getAllowPromoStatus(
    module,
    offerAllowances,
    promotionsLists,
    cardIndex,
    cardItemIndex
  );
  const histryKey =
    allowTypeHistoryKeyMapper && allowanceType
      ? allowTypeHistoryKeyMapper?.[allowanceType] || historyMapperKey
      : historyMapperKey;
  const isNational = planEvent?.eventType === appConstants?.NDP_EVENT_TYPE
  return {
    key,
    planValue,
    pendingValue,
    planEventPendingChanges,
    planEventHistory,
    indicatorValue,
    planPendingObj: planEventPending,
    module,
    historyMapperKey: histryKey,
    searchId,
    planEvent,
    allowPromoStatus,
    eventStatus,
    getValues,
    allowId,
    isNational
  };
};

export const getAllowIdFromOffer = (offerAllow, cardIndex, cardItemIndex) => {
  return offerAllow?.[cardIndex]?.allowances?.[cardItemIndex]?.allowanceIdNbr;
};

export const showEditedLabel = (
  fieldKeysList,
  getValues,
  cardIndex,
  cardItemIndex,
  module,
  eventDetailsData = null
) => {
  if (!fieldKeysList) return false;
  const {
    promotionsLists,
    offerAllowances,
    planEventPendingChanges,
    planEventHistory,
    planEventPending,
  } = eventDetailsData || getValues();
  const loggedInUserType = getLoggedInUserType();
  const historyDataLength = planEventHistory?.length;
  const latestHistory = planEventHistory?.[historyDataLength - 1];
  const { isReturnEvent, isReturnEventByCurrUser } = isEventReturn(
    latestHistory,
    EVENT_ACTION_LABELS.RETURN,
    loggedInUserType
  );

  if (historyDataLength && isReturnEvent && isReturnEventByCurrUser) {
    const lastButOneHistoryObj = planEventHistory?.[historyDataLength - 2];

    const isPrevStatusDraft = checkIsPrevStatusDraft(
      lastButOneHistoryObj,
      "planevents.eventStatus"
    );
    if (isPrevStatusDraft) return false;
  }
  const searchId = getSearchId(
    module,
    offerAllowances,
    promotionsLists,
    cardIndex,
    cardItemIndex
  );
  const allowId = getAllowIdFromOffer(
    offerAllowances,
    cardIndex,
    cardItemIndex
  );
  const indicatorVal = getIndicatorValue(getValues, module, eventDetailsData);
  const currentChangeObj = planEventPendingChanges?.[
    moduleHistoryMapper[module]
  ]?.find(histObj => histObj?.[searchIdMap[module]] === searchId),
    currentHistoryObj = planEventHistory?.[planEventHistory?.length - 1]?.[
      moduleHistoryMapper[module]
    ]?.find(histObj => histObj?.[searchIdMap[module]] === searchId),
    allowPromoStatus = getAllowPromoStatus(
      module,
      offerAllowances,
      promotionsLists,
      cardIndex,
      cardItemIndex
    ),
    isSameUser = checkIfSameUserUpdate(
      currentChangeObj,
      module,
      planEventPending,
      searchId,
      loggedInUserType
    );
  const { isAllowChange = false } =
    isAllowTypeChange({
      planEventHistory,
      module,
      searchId,
      planEventPendingChanges,
    }) || {};
  if (isAllowChange) return false;
  const checkForAgrredActive =
    [
      EEVENT_STATUS.AGREED,
      EEVENT_STATUS.ACTIVE,
      EEVENT_STATUS.EXECUTED,
      EEVENT_STATUS.AGREED_PENDING
    ].includes(allowPromoStatus) && isSameUser;
  if (
    [EEVENT_STATUS.REJECTED, EEVENT_STATUS.CANCELED].includes(allowPromoStatus)
  )
    return false;
  if (indicatorVal) {
    if (
      statusUserMapper[loggedInUserType] === allowPromoStatus ||
      [EEVENT_STATUS.DRAFT].includes(allowPromoStatus) ||
      checkForAgrredActive
    ) {
      return checkKeyFromPendingOrHistory(
        true,
        fieldKeysList,
        currentChangeObj?.changes,
        currentHistoryObj?.changes,
        allowId,
        [EEVENT_STATUS.AGREED, EEVENT_STATUS.ACTIVE].includes(allowPromoStatus)
      );
    }
  }
  if (
    [
      EEVENT_STATUS.AGREED,
      EEVENT_STATUS.ACTIVE,
      EEVENT_STATUS.EXECUTED,
    ].includes(allowPromoStatus)
  )
    return false;
  return checkKeyFromPendingOrHistory(
    false,
    fieldKeysList,
    currentChangeObj?.changes,
    currentHistoryObj?.changes,
    allowId
  );
};
const checkIfSameUserUpdate = (
  currentChangeObj,
  module,
  planeEventPending,
  searchId,
  loggedInUserType
) => {
  const lastUpdateUserType = currentChangeObj?.changes?.find(
    changeObj => changeObj?.labelFieldName === userTypeMapping[module]
  );
  const moduleCardList = planeEventPending?.[`${pendingObjMapper[module]}`], // will get either offerAllowances or promotionList
    updatedPendgAllowPromObj = moduleCardList?.find(
      cardObj => cardObj[`${searchIdMap[module]}`] === searchId
    ),
    type = updatedPendgAllowPromObj?.updateUser?.type;
  const { afterVal: latestLastUpdateUserType } = lastUpdateUserType || {};
  return (
    (latestLastUpdateUserType || type)?.toLowerCase() ===
    loggedInUserType?.toLowerCase()
  );
};
const checkIfKeyExist = (changesList, key, allowId) => {
  return changesList?.some(changeObj =>
    CHECK_HISTORY_ID_KEY_LIST.includes(key)
      ? changeObj?.historyGroupId == allowId &&
      changeObj?.labelFieldName === key
      : changeObj?.labelFieldName === key
  );
};
export const checkKeyFromPendingOrHistory = (
  checkPending,
  fieldKeysList,
  pendingChanges,
  histryChanges,
  allowId,
  isCheckOnlyPending = false
) => {
  return fieldKeysList.some(historyKey => {
    return checkPending
      ? isCheckOnlyPending
        ? checkIfKeyExist(pendingChanges, historyKey, allowId)
        : checkIfKeyExist(pendingChanges, historyKey, allowId) ||
        checkIfKeyExist(histryChanges, historyKey, allowId)
      : checkIfKeyExist(histryChanges, historyKey, allowId);
  });
};
export const getIndicatorValue = (
  getValues,
  module,
  eventDetailsData = null
) => {
  const { allowanceEventInd, promotionEventInd } =
    eventDetailsData || getValues?.();
  switch (module) {
    case "allowance":
      return allowanceEventInd;
    case "promotion":
      return promotionEventInd;
    default:
      return false;
  }
};
const getPlanValue = ({
  configObj,
  getValues,
  fieldsMapper,
  customPlanValue,
}) => {
  const {
    isCustomPlanValue = false,
    key,
    formatAsVehicleDate = false,
  } = configObj || {};
  const planValue = getValues(`planEvent.${fieldsMapper["baseKey"]}`)
    ? getValues(`planEvent.${fieldsMapper[key]}`)
    : getValues(fieldsMapper[key]),
    formattedPlanValue = formatAsVehicleDate ? planValue : planValue;
  return isCustomPlanValue ? customPlanValue : formattedPlanValue;
};

const getPendingValue = ({
  configObj,
  getValues,
  fieldsMapper,
  allowId,
  searchId,
  module,
}) => {
  if (!isAllowanceFeatureEnabled || module != "allowance") return "";
  const { planEventPending = {} } = getValues() || {};
  const cardIndex = planEventPending?.offerAllowances?.findIndex(
    e => e?.offerNumber === searchId
  );
  const cardItemIndex = planEventPending?.offerAllowances?.[
    cardIndex
  ]?.allowances?.findIndex(e => e?.allowanceIdNbr === allowId);
  if (
    cardIndex == undefined ||
    cardItemIndex == undefined ||
    cardIndex < 0 ||
    cardItemIndex < 0
  )
    return "";
  const { key } = configObj || {};
  const allowFormKey = `offerAllowances[${cardIndex}].allowances[${cardItemIndex}]`;
  const planPendingBaseKey = `planEventPending.${allowFormKey}`;
  if (fieldsMapper?.["baseKey"] !== allowFormKey) {
    const splitKeys =
      key && fieldsMapper?.[key] && fieldsMapper?.["baseKey"]
        ? fieldsMapper?.[key]?.split(fieldsMapper?.["baseKey"])
        : null;
    if (splitKeys?.length) {
      const baseKey = splitKeys?.[1];
      if (baseKey) {
        return planPendingBaseKey
          ? getValues(`${planPendingBaseKey}${baseKey}`)
          : getValues(`${allowFormKey}${baseKey}`);
      }
    }
    return getValues(fieldsMapper[key]);
  } else {
    if (getValues(`planEventPending.${fieldsMapper["baseKey"]}`)) {
      if (key === "allowanceAmount") {
        const splittedAmtKeys =
          key &&
          fieldsMapper?.[key] &&
          fieldsMapper["baseKey"] &&
          fieldsMapper?.[key]?.split(fieldsMapper["baseKey"]);
        if (splittedAmtKeys?.length) {
          const baseKey = splittedAmtKeys?.[1] || "";
          return baseKey
            ? getValues(`planEventPending.${fieldsMapper["baseKey"]}${baseKey}`)
            : getValues(`planEventPending.${fieldsMapper[key]}`);
        }
      } else {
        return getValues(`planEventPending.${fieldsMapper[key]}`);
      }
    }
    return getValues(fieldsMapper[key]);
  }
};
// const getHiglighedValue = () => {

// }

const eventStatusList = [
  EEVENT_STATUS.PENDING_WITH_MERCHANT,
  EEVENT_STATUS.PENDING_WITH_VENDOR,
  EEVENT_STATUS.AGREED,
  EEVENT_STATUS.AGREED_PENDING,
  EEVENT_STATUS.READY,
  EEVENT_STATUS.ACTIVE,
];
/**
 *
 * @param param0 - @object
 * @param cardConfiguration
 * @param getValues - form values
 * @param isParent - parent card
 * @param isChild - individual card
 * @param sectionIndex - individual card section id
 * @returns
 */
export const getCardOpenStatus = ({
  cardConfiguration,
  getValues,
  isParent = false,
  isChild = false,
  sectionIndex,
}) => {
  let isCardOpen = false;
  const { planEventPendingChanges, planEventHistory, eventStatus } =
    getValues();
  if (eventStatusList.includes(eventStatus)) {
    const historyLength = planEventHistory?.length;
    const module = cardConfiguration?.key?.toLowerCase();
    const searchIdMapper = {
      allowance: `planEvent.offerAllowances[${sectionIndex}].offerNumber`,
      promotion: `planEvent.promotionsList[${sectionIndex}].promotionId`,
    };
    const searchId = getValues(searchIdMapper[module]);

    const currentChangeObj =
      planEventPendingChanges?.[`${moduleHistoryMapper[module]}`];

    const currentHistoryChanges = planEventHistory?.[historyLength - 1]?.[
      `${moduleHistoryMapper[module]}`
    ]?.find(histObj => histObj[`${searchIdMap[module]}`] === searchId);

    if (module === "allowance") {
      // if allownace offer number mateches, will open the offer and its allowance cards by default as we don't have allowance number
      const filteredVal =
        currentChangeObj?.filter(
          allowance => allowance.offerNumber === searchId
        )?.[0]?.changes?.length ||
        (currentHistoryChanges?.offerNumber === searchId &&
          currentHistoryChanges?.changes?.length);
      isCardOpen = !!filteredVal;
    }

    if (module === "promotion") {
      if (
        isParent &&
        (currentChangeObj?.length || currentHistoryChanges?.changes?.length)
      ) {
        isCardOpen = true;
      } else if (isChild) {
        isCardOpen =
          currentChangeObj?.filter(promo => promo.promotionId === searchId)?.[0]
            ?.changes?.length ||
          (currentHistoryChanges?.promotionId === searchId &&
            currentHistoryChanges?.changes?.length);
      }
    }
  }
  return isCardOpen;
};

/**
 *
 * @param mapperKey @string - feild mapper key to match with label field name in changes response api
 * @param getValues @object - form values
 * @param searchId @number - promo id or allowance offer number
 * @param module @string - promotion/allowance
 * @returns @string - color name for the field
 */
export const editFieldHighlight = (mapperKey, getValues, searchId, module) => {
  const { planEventPendingChanges, planEventHistory, eventStatus } =
    getValues();
  let fieldColor = "";
  if ([EEVENT_STATUS.AGREED].includes(eventStatus)) {
    return fieldColor;
  }
  const historyDataLength = planEventHistory?.length;

  const loggedInUserType = getLoggedInUserType();
  const latestHistory = planEventHistory?.[planEventHistory?.length - 1],
    currentChanges = planEventPendingChanges?.[
      moduleHistoryMapper?.[module]
    ]?.find(item => item?.[searchIdMap[module]] === searchId),
    currentHistory = latestHistory?.[moduleHistoryMapper?.[module]]?.find(
      item => item?.[searchIdMap[module]] === searchId
    ),
    fieldAvailableInCurrentChanges = currentChanges?.changes?.find(
      field => field.labelFieldName === mapperKey
    )?.labelFieldName,
    fieldAvailableInHistoryChanges = currentHistory?.changes?.find(
      field => field.labelFieldName === mapperKey
    )?.labelFieldName;

  const previousHistory = planEventHistory?.[planEventHistory?.length - 2];
  const { isReturnEvent, isReturnEventByCurrUser } = isEventReturn(
    latestHistory,
    EVENT_ACTION_LABELS.RETURN,
    loggedInUserType
  );

  if (historyDataLength && isReturnEvent && isReturnEventByCurrUser) {
    const lastButOneHistoryObj = planEventHistory?.[historyDataLength - 2];
    const isPrevStatusDraft = checkIsPrevStatusDraft(
      lastButOneHistoryObj,
      "planevents.eventStatus"
    );
    if (isPrevStatusDraft) return "";
  }
  if (isReturnEvent) {
    const prevChanges = previousHistory?.[moduleHistoryMapper?.[module]]?.find(
      item => item?.[searchIdMap[module]] === searchId
    ),
      fieldAvailableInPrevHistoryChanges = prevChanges?.changes?.find(
        field => field.labelFieldName === mapperKey
      )?.labelFieldName;
    const historyUserType = prevChanges?.changes?.find(
      item => item.labelFieldName === userTypeMapping[module]
    )?.afterVal;
    if (historyUserType && fieldAvailableInPrevHistoryChanges) {
      historyUserType?.toLowerCase() === loggedInUserType?.toLowerCase()
        ? (fieldColor = "outline-blue")
        : (fieldColor = "outline-red");
    }
    return fieldColor;
  }

  const currentUserType = currentChanges?.changes?.find(
    item => item.labelFieldName === userTypeMapping[module]
  )?.afterVal;
  const historyUserType = currentHistory?.changes?.find(
    item => item.labelFieldName === userTypeMapping[module]
  )?.afterVal;
  const type = currentUserType || historyUserType;
  if (
    type &&
    (fieldAvailableInCurrentChanges || fieldAvailableInHistoryChanges)
  ) {
    type?.toLowerCase() === loggedInUserType?.toLowerCase()
      ? (fieldColor = "outline-blue")
      : (fieldColor = "outline-red");
  }
  return fieldColor;
};
export const checkIfRunHighlightUtility = (
  fieldObj,
  getValues,
  fieldsMapper,
  customPlanValue
) => {
  const { eventStatus } = getValues();

  const statusMapping = {
    [STATUS_CONSTANTS.DRAFT]: () =>
      getPlanValueForPreview(
        fieldObj,
        getValues,
        fieldsMapper,
        customPlanValue
      ),
    [STATUS_CONSTANTS.PENDING_WITH_VENDOR]: () => ({
      runUtility: true,
      planValue: "",
      utilityFunc: highlightFieldForPreview,
    }),
    [STATUS_CONSTANTS.PENDING_WITH_MERCHANT]: () => ({
      runUtility: true,
      planValue: "",
      utilityFunc: highlightFieldForPreview,
    }),
    [STATUS_CONSTANTS.AGREED]: () => ({
      runUtility: true,
      planValue: "",
      utilityFunc: hightlightUtiltyForAgreed,
    }),
    [STATUS_CONSTANTS.EXECUTED]: () => ({
      runUtility: true,
      planValue: "",
      utilityFunc: hightlightUtiltyForAgreed,
    }),
    [STATUS_CONSTANTS.ACTIVE]: () => ({
      runUtility: true,
      planValue: "",
      utilityFunc: hightlightUtiltyForAgreed,
    }),
    [STATUS_CONSTANTS.AGREED_PENDING]: () => ({
      runUtility: true,
      planValue: "",
      utilityFunc: hightlightUtiltyForAgreed,
    }),
  };

  return (
    statusMapping[eventStatus]?.() ||
    getPlanValueForPreview(fieldObj, getValues, fieldsMapper, customPlanValue)
  );
};

const getPlanValueForPreview = (
  fieldObj,
  getValues,
  fieldsMapper,
  customPlanValue
) => {
  return {
    runUtility: false,
    planValue: getPlanValue({
      configObj: fieldObj,
      getValues,
      fieldsMapper,
      customPlanValue,
    }),
    utilityFunc: null,
  };
};
const sortListByKey = (inputList, sortKey) => {
  return sortKey
    ? inputList
      ?.slice()
      ?.sort((a, b) =>
        a[sortKey] > b[sortKey] ? 1 : b[sortKey] > a[sortKey] ? -1 : 0
      )
    : inputList;
};
export const getConstRegKey = (module, cardIndex, cardItemIndex, getValues) => {
  const validIndex = getValidCardIndex(
    module,
    cardIndex,
    cardItemIndex,
    getValues
  );
  return module === "promotion"
    ? `${pendingObjMapper[module]}[${validIndex}]`
    : `${pendingObjMapper[module]}[${validIndex}].allowances[${cardItemIndex}]`;
};

export const getValidCardIndex = (
  module,
  cardIndex,
  cardItemIndex,
  getValues
) => {
  const { promotionsLists, offerAllowances } = getValues();
  const searchId = getSearchId(
    module,
    offerAllowances,
    promotionsLists,
    cardIndex,
    cardItemIndex
  );
  const planEventList =
    getValues(`planEvent.${pendingObjMapper[module]}`) ||
    getValues(`${pendingObjMapper[module]}`),
    sortPlanList = sortListByKey(planEventList, searchIdMap[module]),
    validIndex = sortPlanList?.findIndex(
      planObj => planObj?.[searchIdMap[module]] == searchId
    );
  return validIndex;
};

export const getHeaderStepperValue = (
  fieldsObjList,
  getValues,
  module,
  headerFieldsMapper,
  cardIndex,
  cardItemIndex,
  customvValue,
  allowanceType = ""
) => {
  const dataObj: any = {};
  fieldsObjList?.forEach(fieldObj => {
    if (fieldObj) {
      const { callback } = fieldObj;
      const {
        runUtility,
        planValue = "",
        utilityFunc,
      } = checkIfRunHighlightUtility(
        fieldObj,
        getValues,
        headerFieldsMapper,
        customvValue
      );
      const payload = getFieldLevelConfig(
        fieldObj,
        getValues,
        module,
        headerFieldsMapper,
        customvValue,
        cardIndex,
        cardItemIndex,
        allowanceType
      );
      if (runUtility && utilityFunc) {
        const { currentValue } = utilityFunc?.(payload) || {};
        dataObj[fieldObj?.key] = callback
          ? callback?.(currentValue, true, payload, true)
          : currentValue;
      } else {
        dataObj[fieldObj?.key] = callback
          ? callback?.(planValue, true, payload, true)
          : planValue;
      }
    }
  });
  return dataObj;
};

export const isEventReturnForDraft = (
  eventDetailsData,
  getValues = () => {}
) => {
  const { planEventHistory } = eventDetailsData || getValues?.();
  const historyDataLength = planEventHistory?.length;
  const loggedInUserType = getLoggedInUserType();
  const latestHistory = planEventHistory?.[historyDataLength - 1];
  const { isReturnEvent, isReturnEventByCurrUser } = isEventReturn(
    latestHistory,
    EVENT_ACTION_LABELS.RETURN,
    loggedInUserType
  );
  if (historyDataLength && isReturnEvent && isReturnEventByCurrUser) {
    const lastButOneHistoryObj = planEventHistory?.[historyDataLength - 2];
    const isPrevStatusDraft = checkIsPrevStatusDraft(
      lastButOneHistoryObj,
      "planevents.eventStatus"
    );
    if (isPrevStatusDraft) return true;
  }
  return false;
};
