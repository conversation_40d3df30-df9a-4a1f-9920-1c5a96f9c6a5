import { configureStore } from "@reduxjs/toolkit";
import {
  allowanceTempWork<PERSON><PERSON><PERSON>,
  allowanceTempWorkReset,
  allowanceTempWorkSlice,
} from "./allowance-temp-work-slice";

describe("allowance_temp_work slice", () => {
  const initialState = {
    status: "loading",
    data: {},
  };
  it("should set allowancestempResponse", () => {
    const initialState = allowanceTempWorkSlice.reducer(undefined, {
      type: undefined,
    });

    const data = { some: "data", "allowanceData": {},
    "isTempLoaded": false };
    const newState = allowanceTempWorkSlice.reducer(
      initialState,
      allowanceTempWorkHandler(data)
    );
    expect(newState.data).toEqual(data);
  });
  it("should handle allowanceTempWorkHandler correctly", () => {
    const payload = {};

    // Dispatch the action
    allowanceTempWorkHandler({ payload });

    // Assert the state has been updated correctly
    expect(initialState.data).toEqual(payload);
    expect(initialState.status).toEqual("loading");
  });

  it("should handle allowanceTempWorkReset correctly", () => {
    // Dispatch the action
    allowanceTempWorkReset();

    // Assert the state has been reset
    expect(initialState.data).toEqual({});
  });
});

describe('allowanceTempWorkSlice', () => {
  let store;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        allowanceTempWork: allowanceTempWorkSlice.reducer,
      },
    });
  });

  it('should handle initial state correctly', () => {
    const initialState = store.getState().allowanceTempWork;
    expect(initialState.status).toEqual('loading');
    expect(initialState.data).toEqual( {"allowanceData": {},
    "isTempLoaded": false });
  });

  it('should handle allowanceTempWorkHandler correctly', () => {
    const testData = { key: 'value', allowanceData: {}, isTempLoaded: false };

    store.dispatch(allowanceTempWorkHandler(testData));

    const updatedState = store.getState().allowanceTempWork;
    expect(updatedState.data).toEqual(testData);
    expect(updatedState.status).toEqual('finished');
  });

  it('should handle allowanceTempWorkReset correctly', () => {
    store.dispatch(allowanceTempWorkReset());

    const updatedState = store.getState().allowanceTempWork;
    expect(updatedState.data).toEqual({allowanceData: {}, isTempLoaded: true});
    expect(updatedState.status).toEqual('loading');
  });
});
