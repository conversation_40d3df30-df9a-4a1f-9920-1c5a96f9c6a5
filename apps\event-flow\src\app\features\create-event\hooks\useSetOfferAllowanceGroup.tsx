import { useEffect, useRef } from "react";
import {
  FieldValues,
  UseFormGetValues,
  UseFormSetValue,
} from "react-hook-form";
import { useDispatch } from "react-redux";
import {
  formatTimestampToDate,
  getAllowanceMapKey,
  getProductSourceKey,
  getUniqueItems,
  handleSkipStep,
  isHfOrIfType,
  setProductSourceError,
} from "../service/allowance/allowance-service";
import {
  allowanceProductSources,
  allowanceStepSkipInfo,
} from "../service/slice/allowance-details-slice";
import { usePostAllowanceToBeCreatedMutation } from "../service/apis/allowance-api";
import { SHOW_ALLOWANCE_TO_BE_CREATED_OPTIONS } from "../constants/fields/allowance/field-allowance-to-be-created";
import { useSelectorWrap } from "@me/data-rtk";
import efConstants from "../../../shared/ef-constants/ef-constants";

interface IOfferAllowanceGroupProps {
  allowanceRegisterField: string;
  getValues: UseFormGetValues<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  allowanceTypeHandler: (type: string, toBeCreated: string) => void;
  saveAndContinueHanlder: (step: number) => void;
  isEdit: boolean;
}

export default function useSetOfferAllowanceGroup({
  allowanceRegisterField,
  setValue,
  getValues,
  allowanceTypeHandler,
  saveAndContinueHanlder,
  isEdit,
}: IOfferAllowanceGroupProps) {
  const { ALLOWANCE_TYPES } = efConstants;
  const dispatch = useDispatch();
  const {
      data: { apiErrorsMsgs: rtkErrorsArr, showTokenError = false },
    } = useSelectorWrap("apiError_rn"),
    allowanceTempWorkData = useSelectorWrap("allowance_temp_work")?.data
      ?.allowanceData,
    { data: productSourceData } = useSelectorWrap(
      "allowance_product_sources_rn"
    ),
    allowName = getAllowanceMapKey(allowanceTempWorkData?.allowanceType) || "",
    storeGroups =
      allowanceTempWorkData?.allowanceTypeSpecification?.[allowName];

  const [postEventDetailsData, { isLoading, data: postEventData }] =
    usePostAllowanceToBeCreatedMutation();

  const step = useRef(0);
  const productSourceAndKeysData = useRef({});
  const overrideStoreGroups = storeGroups?.overrideStoreGroups?.map(
    ({ storeGroupId }) => storeGroupId
  );
  const saveAllowanceStepSkipInfo = (skipStep: number) => {
    const allowRegField = isEdit
      ? allowanceRegisterField?.split(".edit")?.[0]
      : allowanceRegisterField;
    dispatch(
      allowanceStepSkipInfo({
        skipStep: {
          [allowRegField]: skipStep,
        },
      })
    );
  };

  useEffect(() => {
    if (postEventData && postEventData?.[0]?.productSources?.length === 0) {
      setProductSourceError(dispatch, rtkErrorsArr, showTokenError);
    } else if (postEventData && postEventData?.length) {
      const productSources = getUniqueItems(postEventData, "productSources");
      productSourceAndKeysData.current = {
        ...productSourceAndKeysData.current,
        productSources,
      };

      !isHfOrIfType(productSourceAndKeysData.current?.["allowanceName"]) &&
        saveOfferAllowanceOption();
      dispatch(
        allowanceProductSources({
          productSources,
        })
      );
    }
  }, [postEventData]);

  const saveOfferAllowanceOption = () => {
    const productSources = productSourceAndKeysData.current?.["productSources"],
      offerGroupKeys = productSourceAndKeysData.current?.["offerGroupKeys"],
      allowanceName = productSourceAndKeysData.current?.["allowanceName"],
      productSource = getProductSourceKey(productSources),
      createInd = productSourceAndKeysData.current?.["createInd"],
      offerAllowancesGroupInfoMap =
        productSourceAndKeysData.current?.["offerAllowancesGroupInfoMap"],
      allowanceToBeCreated = {
        option: "",
        selection: "",
        offerAllowanceGroup: "",
        allowanceMap: {},
        productSources: [],
        offerAllowancesGroupInfoMap: {},
      };

    let offerGroupKey = "";
    if (offerGroupKeys.length) {
      offerGroupKey =
        createInd === ALLOWANCE_TYPES.CASE.createInd[2]
          ? productSource
          : offerGroupKeys.length > 1
          ? offerGroupKeys[1]
          : offerGroupKeys[0];
      const optionsInfo =
        SHOW_ALLOWANCE_TO_BE_CREATED_OPTIONS?.[allowanceName] || {};

      Object.keys(optionsInfo).forEach(offerType => {
        if (optionsInfo[offerType]?.key === offerGroupKey) {
          allowanceToBeCreated.option = optionsInfo[offerType].name;
          allowanceToBeCreated.allowanceMap =
            optionsInfo[offerType].allowanceMap ||
            optionsInfo?.[offerType]?.options?.find(
              option => option?.key === offerGroupKey
            )?.allowanceMap;
        }
      });
      allowanceTypeHandler(allowanceName, allowanceToBeCreated.option);
    }

    allowanceToBeCreated.selection = offerGroupKey;
    allowanceToBeCreated.offerAllowanceGroup = offerGroupKey;
    allowanceToBeCreated.productSources = productSources;
    allowanceToBeCreated.offerAllowancesGroupInfoMap =
      offerAllowancesGroupInfoMap;
    setValue(
      `${allowanceRegisterField}.allowanceToBeCreated`,
      allowanceToBeCreated
    );

    const skipCurrentStep = handleSkipStep(
      allowanceName,
      productSources,
      productSource
    );

    skipCurrentStep && saveAllowanceStepSkipInfo(step.current);
    saveAndContinueHanlder(step.current);
  };

  async function fetchAllowanceToBeCreated(
    skipStep: number,
    allowanceName: string,
    productSources: string[] | [] = productSourceData?.productSources,
    offerGroupKeys: string[] | [],
    createInd: string,
    offerAllowancesGroupInfoMap = {}
  ) {
    const eventData = getValues("eventCreationVehicle");
    step.current = skipStep;
    productSourceAndKeysData.current = {
      allowanceName,
      productSources: productSourceData?.productSources,
      offerGroupKeys,
      createInd,
      offerAllowancesGroupInfoMap,
    };

    //Condition: on page refresh, where we get product scources from Temp API.
    if (
      productSourceData?.productSources?.length &&
      !isHfOrIfType(allowanceName)
    ) {
      saveOfferAllowanceOption();
    }
    //Condition: on edit allowance, where initial data doesn't have any product scources from Temp API.
    else if (!productSourceData?.productSources?.length) {
      const id = getValues("id");
      if (id) {
        await postEventDetailsData({
          URL_PARAM: id,
          queryParams: {
            responseFilter: "allowMin",
          },
          division: getValues("divisionIds"),
          promoStartDate: formatTimestampToDate(eventData.startDate),
          ...(overrideStoreGroups && { overrideStoreGroups }),
        });
      }
    }
  }

  return { fetchAllowanceToBeCreated, isLoading };
}
