import React, { memo, useState } from "react";
import MultiSelect, {
  MultiSelectProps,
} from "@albertsons/uds/molecule/MultiSelect";
import { Controller, useFormContext } from "react-hook-form";
import { FormFieldError } from "@me/util-form-wrapper";

function InputMultiSelect({
  storeGroups,
  onChangeHandler,
  defaultValue,
  validationFn,
  errorMessage,
  registerField,
}) {
  const { control } = useFormContext();
  const disabled = true;
  const isChanged = true;
  const onChangeHandlerMS = items => {
    onChangeHandler(items);
  };
  const stopPropogation = (
    event:
      | React.MouseEvent<HTMLDivElement>
      | React.KeyboardEvent<HTMLDivElement>
  ) => {
    event.preventDefault();
    event.stopPropagation();
  };
  return storeGroups?.length ? (
    <Controller
      control={control}
      name={registerField}
      rules={{
        required: {
          value: false,
          message: "",
        },
        validate: { validationFn },
      }}
      render={({ field, fieldState: { error } }) => (
        <div onClick={stopPropogation}>
          <MultiSelect
            {...field}
            options={storeGroups}
            required
            search
            value={defaultValue}
            onChange={onChangeHandlerMS}
          />
          {error && <FormFieldError error={errorMessage} />}
        </div>
      )}
    />
  ) : (
    <button
      className={`relative w-full px-3 min-h-[40px] ${
        disabled
          ? "bg-gray-205 rounded border border-solid border-disabled"
          : isChanged
          ? "bg-gray-205 rounded border border-error border-2"
          : "bg-white rounded border border-solid border-[#BFD4E7] pointer-events-none"
      }`}
      id={
        disabled
          ? "headlessui-listbox-button-:r7:"
          : "headlessui-listbox-button-:r1l:"
      }
      type="button"
      aria-haspopup={disabled ? "listbox" : "true"}
      aria-expanded="false"
      data-headlessui-state={disabled ? "disabled" : ""}
      disabled={disabled}
    >
      <div
        className="flex items-center justify-between w-full"
        id="abs-input-select-container-d"
      >
        <div
          className="flex flex-wrap my-[6px] truncate"
          id="abs-input-select-container-d1"
        ></div>
        <span
          className="flex items-center justify-end min-w-[24px] pointer-events-none"
          id="abs-input-select-container-span"
        >
          <svg
            className="transition duration-200"
            width="12"
            height="8"
            viewBox="0 0 12 8"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.59 0.589844L6 5.16984L1.41 0.589844L0 1.99984L6 7.99984L12 1.99984L10.59 0.589844Z"
              fill="#2B303B"
            ></path>
          </svg>
        </span>
      </div>
    </button>
  );
}

export default memo(InputMultiSelect);
