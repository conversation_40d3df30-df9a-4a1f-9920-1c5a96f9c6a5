import { DIVISION_PROMOTION as division_promotion } from "./types/division-promotion";
import { NATIONAL_DIVISION_PROMOTION as national_division_promotion } from "./types/multi-division";
import { NON_PROMOTION as non_promotion } from "./types/non-promotion";
import { ALLOWANCE_ONLY as allowance_only } from "./types/allowance-only";
import { NATIONAL_DIVISION_ALLOWANCE_ONLY as national_division_allowance_only } from "./types/multi-division-allowance-only";
import { NATIONAL_CHILD_DIVISION_PROMOTION as national_child_division_promotion } from "./types/national-child-division-promotion";

export const EVENT_TYPES = {
  division_promotion,
  allowance_only,
  national_division_promotion,
  national_division_allowance_only,
  non_promotion,
  national_child_division_promotion,
  event_abbreviations: {
    DP: "division_promotion",
    AO: "allowance_only",
    NDP: "national_division_promotion",
    NAO: "national_division_allowance_only",
    NCDP: "national_child_division_promotion",
  },
};
export const DP_AO_EVENT_TYPES = {
  DP: "DP",
  AO: "AO",
};
export const NATIONAL_EVENT_TYPES = {
  ...DP_AO_EVENT_TYPES,
  NDP: "NDP",
  NAO: "NAO",
  NCDP: "NCDP",
};
export const NATIONAL_EVENT_DIVISION_ID = "98";
