import { renderHook, act } from "@testing-library/react";
import { useDispatch } from "react-redux";
import useGetOfferSectionConfiguration from "./useGetOfferSectionConfiguration";
import { setAllowanceFormInfo, setOfferSectionsEnableConfig } from "../../../../service/slice/allowance-details-slice";
import { useSelectorWrap } from "@me/data-rtk";

jest.mock("react-redux", () => ({
  useDispatch: jest.fn(),
}));

jest.mock("../../../../service/slice/allowance-details-slice", () => ({
  setAllowanceFormInfo: jest.fn(),
  setOfferSectionsEnableConfig: jest.fn(),
}));

jest.mock("@me/data-rtk", () => ({
  useSelectorWrap: jest.fn(),
}));

describe("useGetOfferSectionConfiguration Hook", () => {
  let dispatchMock;
  beforeEach(() => {
    dispatchMock = jest.fn();
    (useDispatch as jest.Mock).mockReturnValue(dispatchMock);
    (useSelectorWrap as jest.Mock).mockImplementation((key) => {
      const mockData = {
        offer_sections_enable_config: { data: {} },
        offer_sections_data: { data: [
          { key: "section1", formKey: "form1" },
          { key: "section2", formKey: "form2" },
        ] },
        allowance_form_data: { data: { testField: {} } },
      };
      return mockData[key] || {};
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should update form data for first section on moveToNextSectionOnCreate", () => {
    const { result } = renderHook(() => useGetOfferSectionConfiguration({ allowanceRegField: "testField" }));
    act(() => {
      result.current.moveToNextSectionOnCreate("section1", "offerKey", { testField: {} });
    });

    expect(dispatchMock).toHaveBeenCalledWith(setAllowanceFormInfo(expect.objectContaining({
      allowanceFormData: expect.objectContaining({ testField: expect.objectContaining({ form1: expect.any(Object) }) })
    })));
  });

  it("should update form data with offerKey when index is 0", () => {
    const { result } = renderHook(() => useGetOfferSectionConfiguration({ allowanceRegField: "testField" }));
    act(() => {
      result.current.moveToNextSectionOnCreate("section1", "offerKey", { testField: { form1: {} } });
    });

    expect(dispatchMock).toHaveBeenCalledWith(setAllowanceFormInfo(expect.objectContaining({
      allowanceFormData: expect.objectContaining({
        testField: expect.objectContaining({
          form1: expect.objectContaining({
            offerKey: expect.any(Object)
          })
        })
      })
    })));
  });

  it("should activate correct section on moveToNextSectionOnCreate", () => {
    const { result } = renderHook(() => useGetOfferSectionConfiguration({ allowanceRegField: "testField" }));
    act(() => {
      result.current.moveToNextSectionOnCreate("section1", "offerKey", { testField: {} });
    });
    expect(dispatchMock).toHaveBeenCalledWith(setOfferSectionsEnableConfig(expect.objectContaining({
      section1: { isActive: true, scrollTo: false },
      section2: { isActive: true, scrollTo: true },
    })));
  });

  it("should update subsequent section only if previous section is updated", () => {
    const { result } = renderHook(() => useGetOfferSectionConfiguration({ allowanceRegField: "testField" }));
    act(() => {
      result.current.moveToNextSectionOnCreate("section1", "offerKey", { testField: {} });
    });
    expect(dispatchMock).toHaveBeenCalledWith(setAllowanceFormInfo(expect.objectContaining({
      allowanceFormData: expect.objectContaining({ testField: expect.objectContaining({ form1: expect.any(Object) }) })
    })));
  });

  it("should update section configuration on moveToNextSectionOnUpdate", () => {
    const { result } = renderHook(() => useGetOfferSectionConfiguration({}));
    act(() => {
      result.current.moveToNextSectionOnUpdate("section1");
    });
    expect(dispatchMock).toHaveBeenCalledWith(setOfferSectionsEnableConfig(expect.any(Object)));
  });

  it("should clear next sections on option change", () => {
    const { result } = renderHook(() => useGetOfferSectionConfiguration({}));
    act(() => {
      result.current.clearNextSectionsOnOptionChange("section1");
    });
    expect(dispatchMock).toHaveBeenCalledWith(setOfferSectionsEnableConfig(expect.any(Object)));
  });
});