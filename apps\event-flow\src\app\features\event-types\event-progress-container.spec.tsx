import { app_store } from "@me/data-rtk";
import { fireEvent, render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import { FormProvider, useForm } from "react-hook-form";
import { Provider } from "react-redux";
import EventProgressContainer from "./event-progress-container";
import { <PERSON>rowserRouter } from "react-router-dom";
import * as selectors from "@me/data-rtk";
import * as event_api_service from "../create-event/service/apis/event-api";
import { promo10003136 } from "../../shared/event-flow-mocks/promotions-mock";
import * as services from "./components/event-progress/event-toggle-service";
import { isValidItemPresent } from "./event-progress-container-service";
import efConstants from "../../shared/ef-constants/ef-constants";
import { EEVENT_STATUS } from "@me/util-helpers";

const mockPlanXAPIDraftData = {
  eventDetailsEventInd: false,
  allowanceEventInd: false,
  promotionEventInd: false,
  planEvent: {
    id: "6448ee5453eea24e88fba49b",
    planEventIdNbr: 10006146,
    name: "Knudsen Cottage Cheese 16 OZ - Norcal California All Stores -Teste5",
    divisionIds: ["05"],
    startDate: 1690351200000,
    endDate: 1690869600000,
    eventType: "DP",
    sourceEventType: "ECP",
    eventStatus: "Draft",
    promotionsList: [promo10003136],
    promotions: ["6448ee8d4440742b1218498f"],
    createUser: {
      userId: "SPOTH03",
      name: "Sridhar pothanaveni",
      type: "Vendor",
      userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
      createTs: "2023-05-10T06:55:55.355Z",
    },
    updateUser: {
      userId: "SPOTH03",
      name: "Sridhar pothanaveni",
      type: "Vendor",
      userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
      createTs: "2023-05-10T06:55:55.355Z",
    },
    planEventWorkFlowType: "NOT FOUND",
    eventTypeEnum: "DP",
  },
  planEventPending: null,
  planEventHistory: null,
  planEventPendingChanges: null,
};

const mockEventDetailsData = {
  id: "6448ee5453eea24e88fba49b",
  planEventIdNbr: 10006146,
  name: "Knudsen Cottage Cheese 16 OZ - Norcal California All Stores -Teste5",
  divisionIds: ["25"],
  startDate: 1690351200000,
  endDate: 1690869600000,
  eventType: "DP",
  sourceEventType: "ECP",
  eventStatus: "Draft",
  eventDetailsEventInd: false,
  allowanceEventInd: false,
  promotionEventInd: false,
  promotionsList: [promo10003136],
  promotions: ["6448ee8d4440742b1218498f"],
  createUser: {
    userId: "SPOTH03",
    name: "Sridhar pothanaveni",
    type: "Vendor",
    userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
    createTs: "2023-05-10T06:55:55.355Z",
  },
  updateUser: {
    userId: "SPOTH03",
    name: "Sridhar pothanaveni",
    type: "Vendor",
    userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
    createTs: "2023-05-10T06:55:55.355Z",
  },
  planEventWorkFlowType: "NOT FOUND",
  eventTypeEnum: "DP",
};

const mockEventDetailsDataEventStatusPendingWithMerchant = {
  id: "6448ee5453eea24e88fba49b",
  planEventIdNbr: 10006146,
  name: "Knudsen Cottage Cheese 16 OZ - Norcal California All Stores -Teste5",
  divisionIds: ["25"],
  startDate: 1690351200000,
  endDate: 1690869600000,
  eventType: "DP",
  sourceEventType: "ECP",
  eventStatus: "Pending With Merchant",
  eventDetailsEventInd: false,
  allowanceEventInd: false,
  promotionEventInd: false,
  promotionsList: [promo10003136],
  promotions: ["6448ee8d4440742b1218498f"],
  createUser: {
    userId: "SPOTH03",
    name: "Sridhar pothanaveni",
    type: "Vendor",
    userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
    createTs: "2023-05-10T06:55:55.355Z",
  },
  updateUser: {
    userId: "SPOTH03",
    name: "Sridhar pothanaveni",
    type: "Vendor",
    userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
    createTs: "2023-05-10T06:55:55.355Z",
  },
  planEventWorkFlowType: "NOT FOUND",
  eventTypeEnum: "DP",
};

const mockEventDetailsDataEventStatusPendingWithVendor = {
  id: "6448ee5453eea24e88fba49b",
  planEventIdNbr: 10006146,
  name: "Knudsen Cottage Cheese 16 OZ - Norcal California All Stores -Teste5",
  divisionIds: ["25"],
  startDate: 1690351200000,
  endDate: 1690869600000,
  eventType: "DP",
  sourceEventType: "ECP",
  eventStatus: "Pending With Vendor",
  eventDetailsEventInd: false,
  allowanceEventInd: false,
  promotionEventInd: false,
  promotionsList: [promo10003136],
  promotions: ["6448ee8d4440742b1218498f"],
  createUser: {
    userId: "SPOTH03",
    name: "Sridhar pothanaveni",
    type: "Vendor",
    userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
    createTs: "2023-05-10T06:55:55.355Z",
  },
  updateUser: {
    userId: "SPOTH03",
    name: "Sridhar pothanaveni",
    type: "Vendor",
    userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
    createTs: "2023-05-10T06:55:55.355Z",
  },
  planEventWorkFlowType: "NOT FOUND",
  eventTypeEnum: "DP",
};

const mockEventDetailsDataEventStatusRejected = {
  id: "6448ee5453eea24e88fba49b",
  planEventIdNbr: 10006146,
  name: "Knudsen Cottage Cheese 16 OZ - Norcal California All Stores -Teste5",
  divisionIds: ["25"],
  startDate: 1690351200000,
  endDate: 1690869600000,
  eventType: "DP",
  sourceEventType: "ECP",
  eventStatus: "Rejected",
  eventDetailsEventInd: false,
  allowanceEventInd: false,
  promotionEventInd: false,
  promotionsList: [promo10003136],
  promotions: ["6448ee8d4440742b1218498f"],
  createUser: {
    userId: "SPOTH03",
    name: "Sridhar pothanaveni",
    type: "Vendor",
    userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
    createTs: "2023-05-10T06:55:55.355Z",
  },
  updateUser: {
    userId: "SPOTH03",
    name: "Sridhar pothanaveni",
    type: "Vendor",
    userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
    createTs: "2023-05-10T06:55:55.355Z",
  },
  planEventWorkFlowType: "NOT FOUND",
  eventTypeEnum: "DP",
};

const mockEventDetailsdataEventStatusAgreed = {
  id: "6448ee5453eea24e88fba49b",
  planEventIdNbr: 10006146,
  name: "Knudsen Cottage Cheese 16 OZ - Norcal California All Stores -Teste5",
  divisionIds: ["25"],
  startDate: 1690351200000,
  endDate: 1690869600000,
  eventType: "DP",
  sourceEventType: "ECP",
  eventStatus: "Agreed",
  eventDetailsEventInd: false,
  allowanceEventInd: false,
  promotionEventInd: false,
  promotionsList: [promo10003136],
  promotions: ["6448ee8d4440742b1218498f"],
  createUser: {
    userId: "SPOTH03",
    name: "Sridhar pothanaveni",
    type: "Vendor",
    userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
    createTs: "2023-05-10T06:55:55.355Z",
  },
  updateUser: {
    userId: "SPOTH03",
    name: "Sridhar pothanaveni",
    type: "Vendor",
    userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
    createTs: "2023-05-10T06:55:55.355Z",
  },
  planEventWorkFlowType: "NOT FOUND",
  eventTypeEnum: "DP",
};

const mockEventDetailsDataEventStatusAgreedPending = {
  id: "6448ee5453eea24e88fba49b",
  planEventIdNbr: 10006146,
  name: "Knudsen Cottage Cheese 16 OZ - Norcal California All Stores -Teste5",
  divisionIds: ["25"],
  startDate: 1690351200000,
  endDate: 1690869600000,
  eventType: "DP",
  sourceEventType: "ECP",
  eventStatus: "Agreed-Pending",
  eventDetailsEventInd: false,
  allowanceEventInd: false,
  promotionEventInd: false,
  promotionsList: [promo10003136],
  promotions: ["6448ee8d4440742b1218498f"],
  createUser: {
    userId: "SPOTH03",
    name: "Sridhar pothanaveni",
    type: "Vendor",
    userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
    createTs: "2023-05-10T06:55:55.355Z",
  },
  updateUser: {
    userId: "SPOTH03",
    name: "Sridhar pothanaveni",
    type: "Vendor",
    userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
    createTs: "2023-05-10T06:55:55.355Z",
  },
  planEventWorkFlowType: "NOT FOUND",
  eventTypeEnum: "DP",
  planEventHistory: []
};

const mockEventDetailsDataEventStatusReady = {
  id: "6448ee5453eea24e88fba49b",
  planEventIdNbr: 10006146,
  name: "Knudsen Cottage Cheese 16 OZ - Norcal California All Stores -Teste5",
  divisionIds: ["25"],
  startDate: 1690351200000,
  endDate: 1690869600000,
  eventType: "DP",
  sourceEventType: "ECP",
  eventStatus: "Ready",
  eventDetailsEventInd: false,
  allowanceEventInd: false,
  promotionEventInd: false,
  promotionsList: [promo10003136],
  promotions: ["6448ee8d4440742b1218498f"],
  createUser: {
    userId: "SPOTH03",
    name: "Sridhar pothanaveni",
    type: "Vendor",
    userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
    createTs: "2023-05-10T06:55:55.355Z",
  },
  updateUser: {
    userId: "SPOTH03",
    name: "Sridhar pothanaveni",
    type: "Vendor",
    userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
    createTs: "2023-05-10T06:55:55.355Z",
  },
  planEventWorkFlowType: "NOT FOUND",
  eventTypeEnum: "DP",
};

const mockEventDetilsDataEventStatusActive = {
  id: "6448ee5453eea24e88fba49b",
  planEventIdNbr: 10006146,
  name: "Knudsen Cottage Cheese 16 OZ - Norcal California All Stores -Teste5",
  divisionIds: ["25"],
  startDate: 1690351200000,
  endDate: 1690869600000,
  eventType: "DP",
  sourceEventType: "ECP",
  eventStatus: "Active",
  eventDetailsEventInd: false,
  allowanceEventInd: false,
  promotionEventInd: false,
  promotionsList: [promo10003136],
  promotions: ["6448ee8d4440742b1218498f"],
  createUser: {
    userId: "SPOTH03",
    name: "Sridhar pothanaveni",
    type: "Vendor",
    userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
    createTs: "2023-05-10T06:55:55.355Z",
  },
  updateUser: {
    userId: "SPOTH03",
    name: "Sridhar pothanaveni",
    type: "Vendor",
    userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
    createTs: "2023-05-10T06:55:55.355Z",
  },
  planEventWorkFlowType: "NOT FOUND",
  eventTypeEnum: "DP",
};

const mockEventDetailsDataStatusExecuted = {
  id: "6448ee5453eea24e88fba49b",
  planEventIdNbr: 10006146,
  name: "Knudsen Cottage Cheese 16 OZ - Norcal California All Stores -Teste5",
  divisionIds: ["25"],
  startDate: 1690351200000,
  endDate: 1690869600000,
  eventType: "DP",
  sourceEventType: "ECP",
  eventStatus: "Executed",
  eventDetailsEventInd: false,
  allowanceEventInd: false,
  promotionEventInd: false,
  promotionsList: [promo10003136],
  promotions: ["6448ee8d4440742b1218498f"],
  createUser: {
    userId: "SPOTH03",
    name: "Sridhar pothanaveni",
    type: "Vendor",
    userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
    createTs: "2023-05-10T06:55:55.355Z",
  },
  updateUser: {
    userId: "SPOTH03",
    name: "Sridhar pothanaveni",
    type: "Vendor",
    userRoles: ["PROMO EXTERNAL EDITOR", "PROMO EXTERNAL VIEWER"],
    createTs: "2023-05-10T06:55:55.355Z",
  },
  planEventWorkFlowType: "NOT FOUND",
  eventTypeEnum: "DP",
};

const eventProgressData = {
  selected: "Event Details",
  sectionNames: ["Event Details", "Allowance", "Promotion"],
  filledSections: ["Event Details", "Allowance", "Promotion"],
  invalidSections: [],
  byPassedSections: [],
};
const mockAllCommentsList = [
  {
    id: "643f7943a0e35e2f1f6e4005",
    planEvent: "643f7753a0e35e2f1f6e4003",
    commentCategory: "External",
    eventComments: [
      {
        id: "6442af492324ba31a6f76b6a",
        messageText: "Test External",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        notifiedUsers: [],
        createTs: 1682091847822,
        lastUpdTs: 1683022421037,
      },
    ],
  },
  {
    id: "6442ad1a2324ba31a6f76b5f",
    planEvent: "643f7753a0e35e2f1f6e4003",
    commentCategory: "Internal",
    eventComments: [
      {
        id: "6442ad1a2324ba31a6f76b60",
        messageText: "Test Internal",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        notifiedUsers: [],
        createTs: 1682091289915,
        lastUpdTs: 1683022440998,
      },
    ],
  },
  {
    id: "6442ae002324ba31a6f76b63",
    planEvent: "643f7753a0e35e2f1f6e4003",
    commentCategory: "Billing Inquiry",
    eventComments: [
      {
        id: "6442ae002324ba31a6f76b64",
        messageText: "Test Billing Enquiry",
        createUserNm: "TESTUSER",
        createUserId: "TESTUSER",
        lastUpdUserId: "TESTUSER",
        notifiedUsers: [],
        createTs: 1682091519771,
        lastUpdTs: 1683022454702,
      },
    ],
  },
];

const mockEventCTAs = {
  Draft: {
    USERS: {
      VENDOR: {
        edit: {
          event: true,
          promotion: true,
          allowance: true,
          relatedInfo: true,
        },
        add: {
          event: true,
          promotion: true,
          allowance: true,
          relatedInfo: true,
        },
        label: "Draft",
        buttons: [
          {
            label: "Send to Merchant",
            variant: "primary",
            disabled: true,
            key: "SEND_TO_MERCHANT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Delete Draft",
            variant: "tertiary",
            disabled: false,
            key: "DELETE_EVENT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
      },
      MERCHANT: {
        edit: {
          event: true,
          promotion: true,
          allowance: true,
          relatedInfo: true,
        },
        add: {
          event: true,
          promotion: true,
          allowance: true,
          relatedInfo: true,
        },
        label: "Draft",
        buttons: [
          {
            label: "Send to Vendor",
            variant: "primary",
            disabled: true,
            key: "SEND_TO_VENDOR",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Delete Draft",
            variant: "tertiary",
            disabled: false,
            key: "DELETE_EVENT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
        modifyEventDraftButtons: [
          {
            label: "Send to Vendor",
            variant: "primary",
            disabled: true,
            key: "SEND_TO_VENDOR",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Auto Approve",
            variant: "secondary",
            disabled: false,
            key: "AUTO_APPROVE",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Delete Draft",
            variant: "tertiary",
            disabled: false,
            key: "DELETE_EVENT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
      },
    },
  },
  "Pending With Vendor": {
    USERS: {
      VENDOR: {
        edit: {
          event: true,
          promotion: true,
          allowance: true,
          relatedInfo: true,
        },
        add: {
          promotion: true,
        },
        label: "Pending with Vendor",
        buttons: [
          {
            label: "Agree to Event",
            variant: "primary",
            disabled: false,
            key: "AGREE",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Send to Merchant",
            variant: "primary",
            disabled: true,
            key: "SEND_TO_MERCHANT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Reject Event",
            variant: "secondary",
            disabled: false,
            key: "REJECT_EVENT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
      },
      MERCHANT: {
        edit: {
          event: false,
          promotion: false,
          allowance: false,
          relatedInfo: false,
        },
        add: {
          promotion: false,
        },
        label: "Pending with Vendor",
        buttons: [
          {
            label: "Send to Vendor",
            variant: "primary",
            disabled: true,
            key: "SEND_TO_VENDOR",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Return",
            variant: "secondary",
            disabled: false,
            key: "RETURN",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
      },
    },
  },
  "Pending With Merchant": {
    USERS: {
      VENDOR: {
        edit: {
          event: false,
          promotion: false,
          allowance: false,
          relatedInfo: false,
        },
        add: {
          promotion: false,
        },
        label: "Draft",
        buttons: [
          {
            label: "Send to Merchant",
            variant: "primary",
            disabled: true,
            key: "SEND_TO_MERCHANT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Return",
            variant: "secondary",
            disabled: false,
            key: "RETURN",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
      },
      MERCHANT: {
        edit: {
          event: true,
          promotion: true,
          allowance: true,
          relatedInfo: true,
        },
        add: {
          promotion: true,
        },
        label: "Draft",
        buttons: [
          {
            label: "Agree to Event",
            variant: "primary",
            disabled: false,
            key: "AGREE",
            conditions: ["allCheck", ["isEventUpdated"]],
            validate: false,
          },
          {
            label: "Send to Vendor",
            variant: "primary",
            disabled: true,
            key: "SEND_TO_VENDOR",
            conditions: ["allCheck", ["isEventUpdated"]],
            validate: true,
          },
          {
            label: "Send Back With Comment",
            variant: "secondary",
            disabled: false,
            key: "SEND_BACK_WITH_COMMENT",
            conditions: ["allCheck", []],
            validate: true,
          },
          {
            label: "Reject Event",
            variant: "secondary",
            disabled: false,
            key: "REJECT_EVENT",
            conditions: ["allCheck", []],
            validate: false,
          },
        ],
      },
    },
  },
  Rejected: {
    USERS: {
      VENDOR: {
        add: {
          event: false,
          promotion: false,
          allowance: false,
          relatedInfo: false,
        },
        edit: {
          event: false,
          promotion: false,
          allowance: false,
          relatedInfo: false,
        },
        label: "Rejected",
        buttons: [],
      },
      MERCHANT: {
        edit: {
          event: false,
          promotion: false,
          allowance: false,
          relatedInfo: false,
        },
        label: "Rejected",
        buttons: [],
      },
    },
  },
  Agreed: {
    USERS: {
      VENDOR: {
        edit: {
          event: true,
          promotion: true,
          allowance: true,
          relatedInfo: true,
        },
        add: {
          promotion: true,
        },
        label: "Draft",
        buttons: [
          {
            label: "Send to Merchant",
            variant: "primary",
            disabled: false,
            key: "SEND_TO_MERCHANT",
            conditions: ["allCheck", ["isEventUpdated"]],
            validate: false,
          },
          {
            label: "Cancel Event",
            variant: "secondary",
            disabled: false,
            key: "CANCEL_EVENT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
        edit_buttons: [
          {
            label: "Send to Merchant",
            variant: "primary",
            disabled: false,
            key: "SEND_TO_MERCHANT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Cancel Event",
            variant: "secondary",
            disabled: false,
            key: "CANCEL_EVENT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
      },
      MERCHANT: {
        edit: {
          event: true,
          promotion: true,
          allowance: true,
          relatedInfo: true,
        },
        add: {
          promotion: true,
        },
        label: "Agree",
        buttons: [
          {
            label: "Send to Vendor",
            variant: "primary",
            disabled: false,
            key: "SEND_TO_VENDOR",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Cancel Event",
            variant: "secondary",
            disabled: false,
            key: "CANCEL_EVENT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
      },
    },
  },
  Ready: {
    USERS: {
      VENDOR: {
        edit: {
          event: true,
          promotion: true,
          allowance: true,
          relatedInfo: true,
        },
        label: "Ready",
        buttons: [
          {
            label: "Send to Merchant",
            variant: "primary",
            disabled: false,
            key: "SEND_TO_MERCHANT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Cancel Event",
            variant: "secondary",
            disabled: false,
            key: "CANCEL_EVENT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
        edit_buttons: [
          {
            label: "Send to Merchant",
            variant: "primary",
            disabled: false,
            key: "SEND_TO_MERCHANT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Cancel Event",
            variant: "secondary",
            disabled: false,
            key: "CANCEL_EVENT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
      },
      MERCHANT: {
        edit: {
          event: true,
          promotion: true,
          allowance: true,
          relatedInfo: true,
        },
        label: "Ready",
        buttons: [
          {
            label: "Send to Vendor",
            variant: "primary",
            disabled: false,
            key: "SEND_TO_VENDOR",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Cancel Event",
            variant: "secondary",
            disabled: false,
            key: "CANCEL_EVENT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
      },
    },
  },
  "Agreed-Pending": {
    USERS: {
      VENDOR: {
        edit: {
          event: false,
          promotion: false,
          allowance: false,
          relatedInfo: false,
        },
        label: "Agreed-Pending",
        buttons: [
          {
            label: "Agree to Pending Request",
            variant: "primary",
            disabled: false,
            key: "AGREE_TO_PENDING_REQUEST",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Reject Pending Request",
            variant: "secondary",
            disabled: false,
            key: "REJECT_PENDING_REQUEST",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
        modifyEventAgreedPendingButtons: [
          {
            label: "Agree to Pending Request",
            variant: "primary",
            disabled: false,
            key: "AGREE_TO_PENDING_REQUEST",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Reject Pending Request",
            variant: "secondary",
            disabled: false,
            key: "REJECT_PENDING_REQUEST",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
        returnEventAgreedPendingButtons: [
          {
            label: "Return",
            variant: "secondary",
            disabled: false,
            key: "RETURN",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
      },
      MERCHANT: {
        edit: {
          event: false,
          promotion: false,
          allowance: false,
          relatedInfo: false,
        },
        label: "Agreed-Pending",
        buttons: [
          {
            label: "Agree to Cancel Event",
            variant: "primary",
            disabled: false,
            key: "AGREE_TO_CANCEL_EVENT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Reject Pending Request",
            variant: "secondary",
            disabled: false,
            key: "REJECT_PENDING_REQUEST",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
        modifyEventAgreedPendingButtons: [
          {
            label: "Agree to Pending Request",
            variant: "primary",
            disabled: false,
            key: "AGREE_TO_PENDING_REQUEST",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Reject Pending Request",
            variant: "secondary",
            disabled: false,
            key: "REJECT_PENDING_REQUEST",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
        returnEventAgreedPendingButtons: [
          {
            label: "Return",
            variant: "secondary",
            disabled: false,
            key: "RETURN",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
      },
    },
  },
  Canceled: {
    USERS: {
      VENDOR: {
        edit: {
          event: false,
          promotion: false,
          allowance: false,
          relatedInfo: false,
        },
        label: "Canceled",
        buttons: [],
      },
      MERCHANT: {
        edit: {
          event: false,
          promotion: false,
          allowance: false,
          relatedInfo: false,
        },
        label: "Canceled",
        buttons: [],
      },
    },
  },
  Active: {
    USERS: {
      VENDOR: {
        edit: {
          event: true,
          promotion: true,
          allowance: true,
          relatedInfo: true,
        },
        add: {
          promotion: true,
        },
        label: "Active",
        buttons: [
          {
            label: "Send to Merchant",
            variant: "primary",
            disabled: false,
            key: "SEND_TO_MERCHANT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Cancel Event",
            variant: "secondary",
            disabled: false,
            key: "CANCEL_EVENT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
      },
      MERCHANT: {
        edit: {
          event: true,
          promotion: true,
          allowance: true,
          relatedInfo: true,
        },
        add: {
          promotion: true,
        },
        label: "Active",
        buttons: [
          {
            label: "Send to Vendor",
            variant: "primary",
            disabled: false,
            key: "SEND_TO_VENDOR",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Cancel Event",
            variant: "secondary",
            disabled: false,
            key: "CANCEL_EVENT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
      },
    },
  },
  Executed: {
    USERS: {
      VENDOR: {
        edit: {
          event: false,
          promotion: false,
          allowance: true,
          relatedInfo: false,
        },
        add: {
          promotion: false,
        },
        label: "Executed",
        buttons: [
          {
            label: "Send to Merchant",
            variant: "primary",
            disabled: false,
            key: "SEND_TO_MERCHANT",
            conditions: ["allCheck", ["isEventUpdated"]],
            validate: false,
          },
          {
            label: "Cancel Event",
            variant: "secondary",
            disabled: false,
            key: "CANCEL_EVENT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
        edit_buttons: [
          {
            label: "Send to Merchant",
            variant: "primary",
            disabled: false,
            key: "SEND_TO_MERCHANT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Cancel Event",
            variant: "secondary",
            disabled: false,
            key: "CANCEL_EVENT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
      },
      MERCHANT: {
        edit: {
          event: false,
          promotion: false,
          allowance: true,
          relatedInfo: false,
        },
        add: {
          promotion: false,
        },
        label: "Executed",
        buttons: [
          {
            label: "Send to Vendor",
            variant: "primary",
            disabled: false,
            key: "SEND_TO_VENDOR",
            conditions: ["allCheck", [""]],
            validate: false,
          },
          {
            label: "Cancel Event",
            variant: "secondary",
            disabled: false,
            key: "CANCEL_EVENT",
            conditions: ["allCheck", [""]],
            validate: false,
          },
        ],
      },
    },
  },
};

const Wrapper = props => {
  const formMethods = useForm<any>({
    defaultValues: {
      eventStatus: "Draft",
    },
  });
  return (
    <Provider store={app_store}>
      <BrowserRouter>
        <FormProvider {...formMethods}>{props.children}</FormProvider>
      </BrowserRouter>
    </Provider>
  );
};

// jest.mock("react-redux", () => ({
// ...jest.requireActual("react-redux"),

// useSelector: jest.fn().mockReturnValue({
// status: "finished",
// data: {
// selected: "Event Details",
// sectionNames: ["Event Details", "Allowance", "Promotion"],
// filledSections: ["Event Details", "Allowance"],
// invalidSections: [],
// },
// }),
// }));
const alowanceTempData = {
  allowanceData: { tempWorkAllowanceId: "1bc1231bc" },
};

jest.mock("./event-progress-container-service", () => ({
  ...jest.requireActual("./event-progress-container-service"),
  isEventReturn: () => true,
}));

describe("event progress container", () => {
  beforeEach(() => {
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      switch (sel_name) {
        case "plan_event_indicators":
          return {
            data: mockPlanXAPIDraftData,
          };
        case "event_details_data":
          return {
            data: mockEventDetailsData,
          };
        case "event_progress_data":
          return {
            data: eventProgressData,
          };
        case "event_comments_data":
          return {
            data: eventProgressData,
          };
        case "allowance_temp_work":
          return {
            data: alowanceTempData,
          };
        case "isFromCommentSection_rn":
          return {
            data: {},
          };
        default:
          break;
      }
    });

    jest
      .spyOn(event_api_service, "useGetCommentsDataQuery")
      .mockReturnValue({ data: mockAllCommentsList });
  });

  it("should render event progress container", async () => {
    const { getAllByText } = render(
      <Wrapper>
        <EventProgressContainer />
      </Wrapper>
    );
    expect(getAllByText("Event").length).toBe(1);
    expect(getAllByText("Event Details").length).toBe(1);
    // expect(getAllByText("Duplicate Event").length).toBe(1);
  });

  it("should show correct section names", () => {
    const { getByText } = render(
      <Wrapper>
        <EventProgressContainer />
      </Wrapper>
    );
    expect(getByText("Event Details")).toBeInTheDocument();
    expect(getByText("Allowance")).toBeInTheDocument();
    expect(getByText("Promotion")).toBeInTheDocument();
  });

  it("should show checkmark on Event Details and Allowance sections", () => {
    const allowanceOnly = JSON.parse(JSON.stringify(eventProgressData));
    delete allowanceOnly.sectionNames[2];
    delete allowanceOnly.filledSections[2];
    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      switch (sel_name) {
        case "plan_event_indicators":
          return {
            data: mockPlanXAPIDraftData,
          };
        case "event_details_data":
          return {
            data: mockEventDetailsData,
          };
        case "event_progress_data":
          return {
            data: allowanceOnly,
          };
        case "event_comments_data":
          return {
            data: eventProgressData,
          };
        case "allowance_temp_work":
          return {
            data: alowanceTempData,
          };
        case "isFromCommentSection_rn":
          return {
            data: {},
          };
        default:
          break;
      }
    });
    const { getByText, container } = render(
      <Wrapper>
        <EventProgressContainer />
      </Wrapper>
    );
    expect(getByText("Event Details").nextSibling).toBeTruthy();
    expect(getByText("Allowance").nextSibling).toBeTruthy();
    expect(container.querySelectorAll("svg")).toHaveLength(5);
  });

  it("should not show checkmark on Promotion section", () => {
    const allowanceOnlyProgressData = JSON.parse(
      JSON.stringify(eventProgressData)
    );
    // delete allowanceOnlyProgressData.sectionNames[2];
    delete allowanceOnlyProgressData.filledSections[2];
    const allowanceOnlyEventData = JSON.parse(
      JSON.stringify(eventProgressData)
    );
    delete allowanceOnlyEventData.promotionsList;
    delete allowanceOnlyEventData.promotions;

    jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
      switch (sel_name) {
        case "plan_event_indicators":
          return {
            data: mockPlanXAPIDraftData,
          };
        case "event_details_data":
          return {
            data: allowanceOnlyEventData,
          };
        case "promo_card_configutation_rn":
          return {
            data: {},
          };
        case "event_progress_data":
          return {
            data: allowanceOnlyProgressData,
          };
        case "event_comments_data":
          return {
            data: eventProgressData,
          };
        case "allowance_temp_work":
          return {
            data: alowanceTempData,
          };
        case "isFromCommentSection_rn":
          return {
            data: {},
          };
        default:
          break;
      }
    });
    const { getByText } = render(
      <Wrapper>
        <EventProgressContainer />
      </Wrapper>
    );
    expect(getByText("Promotion").nextSibling).toBe(null);
  });

  it("should show border line in Event Details section", () => {
    const { getByText } = render(
      <Wrapper>
        <EventProgressContainer />
      </Wrapper>
    );
    expect(getByText("Event Details").closest("div")?.className).toContain(
      "border-[#2997ef]"
    );
  });

  it("should not show border line in Allowance and Promotion section", () => {
    const { getByText } = render(
      <Wrapper>
        <EventProgressContainer />
      </Wrapper>
    );
    expect(getByText("Allowance").closest("div")?.className).toContain(
      "border-[#fdfdff]"
    );
    expect(getByText("Promotion").closest("div")?.className).toContain(
      "border-[#fdfdff]"
    );
  });

  xit("check for View History Text", () => {
    const { getByText } = render(
      <Wrapper>
        <EventProgressContainer />
      </Wrapper>
    );
    expect(getByText("View History")).toBeInTheDocument();
  });

  test("eventHandler called on click of View History link", () => {
    const handleClick = jest.fn();
    render(
      <p
        className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#1b6ebb] cursor-pointer"
        onClick={handleClick}
      >
        View History
      </p>
    );
    fireEvent.click(screen.getByText(/View History/i));

    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});

it("call handleToggledDisable function with case pending with merchant", () => {
  const allowanceOnly = JSON.parse(JSON.stringify(eventProgressData));
  delete allowanceOnly.sectionNames[2];
  delete allowanceOnly.filledSections[2];
  jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
    switch (sel_name) {
      case "plan_event_indicators":
        return {
          data: mockPlanXAPIDraftData,
        };
      case "event_details_data":
        return {
          data: mockEventDetailsDataEventStatusPendingWithMerchant,
        };
      case "event_progress_data":
        return {
          data: allowanceOnly,
        };
      case "event_comments_data":
        return {
          data: eventProgressData,
        };
      case "allowance_temp_work":
        return {
          data: alowanceTempData,
        };
      case "isFromCommentSection_rn":
        return {
          data: {},
        };
      default:
        break;
    }
  });
  const { getByText } = render(
    <Wrapper>
      <EventProgressContainer />
    </Wrapper>
  );
  expect(getByText("Event Details").nextSibling).toBeTruthy();
  expect(getByText("Allowance").nextSibling).toBeTruthy();
});

it("call handleToggledDisable function with case pending with vendor", () => {
  const allowanceOnly = JSON.parse(JSON.stringify(eventProgressData));
  delete allowanceOnly.sectionNames[2];
  delete allowanceOnly.filledSections[2];
  jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
    switch (sel_name) {
      case "plan_event_indicators":
        return {
          data: mockPlanXAPIDraftData,
        };
      case "event_details_data":
        return {
          data: mockEventDetailsDataEventStatusPendingWithVendor,
        };
      case "event_progress_data":
        return {
          data: allowanceOnly,
        };
      case "event_comments_data":
        return {
          data: eventProgressData,
        };
      case "allowance_temp_work":
        return {
          data: alowanceTempData,
        };
      case "isFromCommentSection_rn":
        return {
          data: {},
        };
      default:
        break;
    }
  });
  const { getByText } = render(
    <Wrapper>
      <EventProgressContainer />
    </Wrapper>
  );
  expect(getByText("Event Details").nextSibling).toBeTruthy();
  expect(getByText("Allowance").nextSibling).toBeTruthy();
});

it("call handleToggledDisable function with case event status is rejected", () => {
  const allowanceOnly = JSON.parse(JSON.stringify(eventProgressData));
  delete allowanceOnly.sectionNames[2];
  delete allowanceOnly.filledSections[2];
  jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
    switch (sel_name) {
      case "plan_event_indicators":
        return {
          data: mockPlanXAPIDraftData,
        };
      case "event_details_data":
        return {
          data: mockEventDetailsDataEventStatusRejected,
        };
      case "event_progress_data":
        return {
          data: allowanceOnly,
        };
      case "event_comments_data":
        return {
          data: eventProgressData,
        };
      case "allowance_temp_work":
        return {
          data: alowanceTempData,
        };
      case "isFromCommentSection_rn":
        return {
          data: {},
        };
      default:
        break;
    }
  });
  const { getByText } = render(
    <Wrapper>
      <EventProgressContainer />
    </Wrapper>
  );
  expect(getByText("Event Details").nextSibling).toBeTruthy();
  expect(getByText("Allowance").nextSibling).toBeTruthy();
});

it("call handleToggledDisable function with case event status is agreed", () => {
  const allowanceOnly = JSON.parse(JSON.stringify(eventProgressData));
  delete allowanceOnly.sectionNames[2];
  delete allowanceOnly.filledSections[2];
  jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
    switch (sel_name) {
      case "plan_event_indicators":
        return {
          data: mockPlanXAPIDraftData,
        };
      case "event_details_data":
        return {
          data: mockEventDetailsdataEventStatusAgreed,
        };
      case "event_progress_data":
        return {
          data: allowanceOnly,
        };
      case "event_comments_data":
        return {
          data: eventProgressData,
        };
      case "allowance_temp_work":
        return {
          data: alowanceTempData,
        };
      case "isFromCommentSection_rn":
        return {
          data: {},
        };
      default:
        break;
    }
  });
  const { getByText } = render(
    <Wrapper>
      <EventProgressContainer />
    </Wrapper>
  );
  expect(getByText("Event Details").nextSibling).toBeTruthy();
  expect(getByText("Allowance").nextSibling).toBeTruthy();
});

it("call handleToggledDisable function with case event status is agreed pending", () => {
  const allowanceOnly = JSON.parse(JSON.stringify(eventProgressData));
  delete allowanceOnly.sectionNames[2];
  delete allowanceOnly.filledSections[2];
  jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
    switch (sel_name) {
      case "plan_event_indicators":
        return {
          data: mockPlanXAPIDraftData,
        };
      case "event_details_data":
        return {
          data: mockEventDetailsDataEventStatusAgreedPending,
        };
      case "event_progress_data":
        return {
          data: allowanceOnly,
        };
      case "event_comments_data":
        return {
          data: eventProgressData,
        };
      case "allowance_temp_work":
        return {
          data: alowanceTempData,
        };
      case "isFromCommentSection_rn":
        return {
          data: {},
        };
      default:
        break;
    }
  });
  const { getByText } = render(
    <Wrapper>
      <EventProgressContainer />
    </Wrapper>
  );
  expect(getByText("Event Details").nextSibling).toBeTruthy();
  expect(getByText("Allowance").nextSibling).toBeTruthy();
});

it("call handleToggledDisable function with case event status is ready", () => {
  const allowanceOnly = JSON.parse(JSON.stringify(eventProgressData));
  delete allowanceOnly.sectionNames[2];
  delete allowanceOnly.filledSections[2];
  jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
    switch (sel_name) {
      case "plan_event_indicators":
        return {
          data: mockPlanXAPIDraftData,
        };
      case "event_details_data":
        return {
          data: mockEventDetailsDataEventStatusReady,
        };
      case "event_progress_data":
        return {
          data: allowanceOnly,
        };
      case "event_comments_data":
        return {
          data: eventProgressData,
        };
      case "allowance_temp_work":
        return {
          data: alowanceTempData,
        };
      case "isFromCommentSection_rn":
        return {
          data: {},
        };
      default:
        break;
    }
  });
  const { getByText } = render(
    <Wrapper>
      <EventProgressContainer />
    </Wrapper>
  );
  expect(getByText("Event Details").nextSibling).toBeTruthy();
  expect(getByText("Allowance").nextSibling).toBeTruthy();
});

it("call handleToggledDisable function with case event status is active", () => {
  const allowanceOnly = JSON.parse(JSON.stringify(eventProgressData));
  delete allowanceOnly.sectionNames[2];
  delete allowanceOnly.filledSections[2];
  jest.spyOn(services, "default").mockReturnValue({
    handleToggleDisable: jest.fn().mockReturnValue(mockEventCTAs),
  });
  jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
    switch (sel_name) {
      case "plan_event_indicators":
        return {
          data: mockPlanXAPIDraftData,
        };
      case "event_details_data":
        return {
          data: mockEventDetilsDataEventStatusActive,
        };
      case "event_progress_data":
        return {
          data: allowanceOnly,
        };
      case "event_comments_data":
        return {
          data: eventProgressData,
        };
      case "allowance_temp_work":
        return {
          data: alowanceTempData,
        };
      case "isFromCommentSection_rn":
        return {
          data: {},
        };
      default:
        break;
    }
  });
  const { getByText } = render(
    <Wrapper>
      <EventProgressContainer />
    </Wrapper>
  );
  expect(getByText("Event Details").nextSibling).toBeTruthy();
  expect(getByText("Allowance").nextSibling).toBeTruthy();
});

it("call handleToggledDisable function with case event status is executed", () => {
  const allowanceOnly = JSON.parse(JSON.stringify(eventProgressData));
  delete allowanceOnly.sectionNames[2];
  delete allowanceOnly.filledSections[2];
  jest.spyOn(selectors, "useSelectorWrap").mockImplementation(sel_name => {
    switch (sel_name) {
      case "plan_event_indicators":
        return {
          data: mockPlanXAPIDraftData,
        };
      case "event_details_data":
        return {
          data: mockEventDetailsDataStatusExecuted,
        };
      case "event_progress_data":
        return {
          data: allowanceOnly,
        };
      case "event_comments_data":
        return {
          data: eventProgressData,
        };
      case "allowance_temp_work":
        return {
          data: alowanceTempData,
        };
      case "isFromCommentSection_rn":
        return {
          data: {},
        };
      default:
        break;
    }
  });
  const { getByText } = render(
    <Wrapper>
      <EventProgressContainer />
    </Wrapper>
  );
  expect(getByText("Event Details").nextSibling).toBeTruthy();
  expect(getByText("Allowance").nextSibling).toBeTruthy();
});

describe("isValidItemPresent", () => {
  it("returns false when items is undefined", () => {
    const result = isValidItemPresent(
      undefined,
      "statusKey",
      "overrideStatusKey"
    );
    expect(result).toEqual(false);
  });

  it("returns false when items is an empty array", () => {
    const result = isValidItemPresent([], "statusKey", "overrideStatusKey");
    expect(result).toEqual(false);
  });

  it("returns false when items contains an item that is excluded or cancelled", () => {
    const items = [
      {
        status: efConstants.EXCLUDED_OFFER_PROMO_STATUS[0],
        overrideStatus: EEVENT_STATUS.REJECTED,
      },
    ];
    const result = isValidItemPresent(items, "status", "overrideStatus");
    expect(result).toEqual(false);
  });

  it("returns true when items contains an item that is not excluded and is not cancelled", () => {
    const items = [{ status: "ACTIVE", overrideStatus: "ACTIVE" }];
    const result = isValidItemPresent(items, "status", "overrideStatus");
    expect(result).toEqual(true);
  });
});
