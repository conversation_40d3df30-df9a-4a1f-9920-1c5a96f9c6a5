import React, { useRef } from "react";

interface SearchBoxProps {
  filterListBySearch: (searchText: string) => void;
  searchPlaceholder?: string;
  searchedText: string;
  setSearchedText: (text: string) => void;
}

const SearchBox: React.FC<SearchBoxProps> = ({
  filterListBySearch,
  searchPlaceholder = "Search...",
  searchedText,
  setSearchedText,
}) => {
  const firstRender = useRef(true);

  React.useEffect(() => {
    if (firstRender.current) {
      firstRender.current = false;
      return;
    }
    filterListBySearch(searchedText);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchedText]);

  return (
    <div
      id={`abs-selection-list-container-search-box`}
      className="flex justify-between flex-grow-0 border border-#C8DAEB h-11 text-[16px] w-full rounded-[4px] px-4 outline-0 my-2"
    >
      <input
        className="outline-0 placeholder-[#2B303C] w-full"
        placeholder={searchPlaceholder}
        type="text"
        value={searchedText}
        onChange={e => setSearchedText(e.target.value)}
        aria-label={searchPlaceholder}
      ></input>
      <span className="inset-y-0 left-0 flex items-center pl-2">
        <SearchIcon />
      </span>
    </div>
  );
};

export const SearchIcon = () => {
  return (
    <button
      type="button"
      className="p-1 focus:outline-none focus:shadow-outline"
    >
      <svg
        fill="none"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        viewBox="0 0 24 24"
        className="w-6 h-6"
      >
        <path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
      </svg>
    </button>
  );
};

export default React.memo(SearchBox);
