import { EVENT_ALLOWANCE } from "../../fields/allowance/allowance-steps-config";
import { EVENT_DETAILS_FIELDS } from "../../fields/event-details/event-details";
import { EVENT_PROMOTION_SECTION } from "../../promotion/layout/promotion-layout";

export const NATIONAL_CHILD_DIVISION_PROMOTION = {
  section: "National Child Division Promotion",
  showField: true,
  sections: [
    "Event Details",
    "Allowance",
    "Promotion Details",
    // "Performance", //TODO
    // "Related Info",
  ],
  generate_event_name: ["promoProductGroup", "startWeekVehicle"],
  progress_types: ["Event Details", "Allowance", "Promotion"],
  "Event Details": {
    section: "Event Details",
    key: "Event Details",
    multiple: false,
    title: "Event Details",
    ediLabel: "Event Details",
    isOpenCard: true,
    fields: EVENT_DETAILS_FIELDS,
    subtitle: "",
    saveLabel: "Save Event Details & Add Allowance",
    editAccordion: "Edit Event Details",
    nextLevel: false,
    cardLayout: false,
    remove: false,
    eventTypeName: "DP",
    showField: true,
  },
  Allowance: {
    section: "Allowance",
    key: "Allowance",
    title: "Allowance",
    offerTitle: "New Offer",
    isOpenCard: false,
    multiple: true,
    subtitle: "Not Started",
    stepper: 0,
    allowanceTye: "Case",
    allowanceToBeCreated: "Both",
    fields: EVENT_ALLOWANCE,
    headerButton: "New",
    nextLevel: true,
    cardLayout: true,
    cardFieldProp: "offerAllowances",
    cardFieldSubProp: "allowances",
    nextLevelLabel: "Skip to Promotion Details",
    allAnotherItem: "Add Another Allowance",
    addAnotherOfferAllowance: "Add Another Offer & Allowance",
    saveAndContinue: "Save & Continue",
    saveAndCreateAllowance: "Save & Create Allowance",
    permission: "PROMOTION_ALLOWANCE_MGMT_EDIT",
    module: "ALLOWANCE",
    previewModuleKey: "allowance",
  },
  "Promotion Details": {
    section: "Promotion Details",
    title: "Promotion",
    key: "Promotion",
    offerTitle: "Promotion",
    itemTitle: "Promo",
    isOpenCard: false,
    multiple: true,
    cardLayout: true,
    cardFieldProp: "promotionsLists",
    cardFieldSubProp: "promotionsList",
    headerButton: "Add Promotion",
    subtitle: "Unavailable",
    fields: EVENT_PROMOTION_SECTION,
    nextLevel: true,
    nextLevelLabel: "Skip to Promotion Details",
    allAnotherItem: "Add Another Promotion",
    permission: "PROMOTION_PROMO_MGMT_EDIT_ADD_PROMOTION",
    module: "PROMO_MGMT",
    previewModuleKey: "promotion",
  },
  Performance: {
    section: "Performance",
    key: "Performance",
    title: "Performance",
    offerTitle: "Performance",
    isOpenCard: false,
    multiple: true,
    subtitle: "Not Started",
    cardLayout: true,
    fields: EVENT_ALLOWANCE,
    cardFieldProp: "performance",
    cardFieldSubProp: "performance",
    nextLevel: true,
    nextLevelLabel: "Skip to Promotion Details",
    allAnotherItem: "Add Another Allowance",
  },
  "Related Info": {
    section: "Related Info",
    title: "Related Info",
    key: "Related Infos",
    offerTitle: "Related Info",
    isOpenCard: false,
    cardLayout: true,
    multiple: true,
    subtitle: "Not Started",
    fields: EVENT_ALLOWANCE,
    cardFieldProp: "relatedInfo",
    cardFieldSubProp: "relatedInfo",
    nextLevel: true,
    nextLevelLabel: "Skip to Promotion Details",
    allAnotherItem: "Add Another Allowance",
  },
};

export const WARNING_TEXT = {
  event: {
    warningBodyText: "Your edit event details will be lost if you change to",
    warningTitle: "Your edit event details will be lost!",
    warningLabel: "Change to Event Edit?",
    cancelButtonLabel: "No, Continue Editing event",
    confirmButtonLabel: "Yes, Change to Event edit",
  },
  offer: {
    warningBodyText: "Your edit offer details will be lost if you change to",
    warningTitle: "Your edit offer details will be lost!",
    warningLabel: "Change to offer allowance edit?",
    cancelButtonLabel: "No, Continue Editing offer",
    confirmButtonLabel: "Yes, Change to Offer edit",
  },
  promo: {
    warningBodyText: "Your edit promo details will be lost if you change to",
    warningTitle: "Your edit promo details will be lost!",
    warningLabel: "Change to promo Edit?",
    cancelButtonLabel: "No, Continue Editing promo",
    confirmButtonLabel: "Yes, Change to Promo edit",
  },
};
