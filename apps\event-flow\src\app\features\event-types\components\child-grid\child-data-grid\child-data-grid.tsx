import { memo } from "react";
import useGetGridConfigData from "../hooks/useGetGridConfigData";
import Datagrid from "./datagrid";

function ChildDataGrid({ cardIndex, isFetching }) {
  const { gridData, gridConfig, columns, key } = useGetGridConfigData({ cardIndex });
  return (
    <Datagrid
      cardIndex={cardIndex}
      gridData={gridData}
      gridConfig={gridConfig}
      columns={columns}
      isFetching={isFetching}
      key={key}
    />
  );
}

export default memo(ChildDataGrid);
