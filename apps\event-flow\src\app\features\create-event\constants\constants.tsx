export const EVENT_PLAN_CARD = {
  0: "Event Detail",
  1: "Allowance",
  2: "Promotion Details",
  3: "Performance",
  4: "Related Info",
};

export const STORE_DATA_LOAD_FINISHED_STATUS = "finished";
export const PROMO_POWER_ADMIN_VENDOR_ID = "002550";

export enum EVENT_TYPE {
  NCDP = "NCDP",
  NDP = "NDP",
}

export const OFFER_ATTACHMENT_TYPES = ["pdf", "xls", "xlsx", "csv"];
export const OFFER_ATTACHMENT_SIZE_LIMIT_IN_BYTES = 10 * 1024 * 1024; // in MB
export const OFFER_ATTACHMENT_SIZE_ERROR_LABEL =
  "File size exceeds amount permitted";
export const CONFIRM_BUTTON_LABEL = "Confirm";
export const CANCEL_BUTTON_LABEL = "Cancel";
export const OFFER_UPLOAD_API_LABEL_ONE = "/meupp/allowance/offer/";
export const OFFER_UPLOAD_API_LABEL_TWO = "/blob/download/";
export const OFFER_UPLOAD_API_BLOB_LABEL = "/blob/";
