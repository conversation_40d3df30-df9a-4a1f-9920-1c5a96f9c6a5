import { getLoggedInUserType } from "@me/util-helpers";
import { setNdVendorDivisions } from "../../components/cards/event-details/utility/utility";

export default {
  name: "",
  startDate: "",
  endDate: "",
  divisionIds: [],
  divisions: [],
  planProductGroups: [],
  storeGroups: [],
  storeGroupType: "",
  allowance: [],
  isEventChanged: false,
  id: "",
  forecast: { forecastSales: 0, forecastUnits: 0, quadrant: "" },
  eventCreationVehicle: {
    vehicleId: "",
    vehicleNm: "",
    sourceVehicleSk: "",
    startDate: "",
    year: "",
    endDate: "",
    vehicleType: {
      vehicleTypeId: "",
      sourceVehicleTypeSk: "",
      vehicleTypDesc: "",
    },
  },
  planProductGroupPricing: {
    planProductGroup: {
      planProductGroupId: "",
      sourceProductGroupId: "",
      name: "",
      divisionId: "",
      smicGroupCode: "",
      smicCategoryCode: "",
      supplier: {
        id: "",
        supplierId: "",
        supplierName: "",
      },
    },
    quadrant: "",
    priceAmount: "",
    priceFactor: "",
    priceUnit: "",
    priceLimitQty: "",
    forecastAmt: "",
    userName: "",
    supplier: "",
  },
  startWeekVehicle: "",
  planStoreGroupType: "Division",
  vehicleType: "",
  eventStatus: "Draft",
  eventType: "DP",
  inValidPromotions: [],
  inValidAllowances: [],
  isChangeEventTypeVisible: true,
  dataFetchedFromPeriscope: false,
};
