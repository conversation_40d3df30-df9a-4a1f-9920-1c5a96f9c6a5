import { CommonModal } from "../../../../shared/components/common-modal";

export default function TaskGridModal({
  modalProps
}) {
  const {isModalPopupOpen, setModalPopupOpen, title, warningMessage, infoMessage, confirmBtnTitle, cancelBtnTitle, modalNameHandler, cancelBtnHandler, onClose, minCancelBtnWidth, minBtnWidth, divIds, divisionName} = modalProps;
  return (
    <CommonModal
      isModalPopupOpen={isModalPopupOpen}
      setModalPopupOpen={setModalPopupOpen}
      title={title}
      warningMessage={warningMessage}
      infoMessage={infoMessage}
      confirmBtnTitle={confirmBtnTitle}
      cancelBtnTitle={cancelBtnTitle}
      showHideBtns
      height={"auto"}
      maxHeight={"null"}
      modalNameHandler={modalNameHandler}
      cancelBtnHandler={cancelBtnHandler}
      onClose={onClose}
      minCancelBtnWidth={minCancelBtnWidth}
      minBtnWidth={minBtnWidth}
      divisionIds={divIds}
      divisionName={divisionName}
    />
  );
}
