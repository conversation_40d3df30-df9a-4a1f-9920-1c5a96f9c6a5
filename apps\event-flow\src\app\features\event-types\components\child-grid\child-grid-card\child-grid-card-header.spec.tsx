import React from 'react';
import '@testing-library/jest-dom'
import { render, screen } from '@testing-library/react';
import { useSelectorWrap } from '@me/data-rtk';
import ChildGridCardHeader from './child-grid-card-header';
import ChildOfferCardHeader from './child-offers-card-header';
import ChildEventsHeader from './child-events-header';
import ChildGridUserActions from './child-grid-user-actions';

jest.mock('@me/data-rtk', () => ({
  useSelectorWrap: jest.fn(),
}));

jest.mock('./child-offers-card-header', () => jest.fn(() => <div data-testid="child-offer-card-header">Child Offer Card Header</div>));
jest.mock('./child-events-header', () => jest.fn(() => <div data-testid="child-events-header">Child Events Header</div>));
jest.mock('./child-grid-user-actions', () => jest.fn(() => <div data-testid="child-grid-user-actions">Child Grid User Actions</div>));

describe('ChildGridCardHeader', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render the ChildEventsHeader and ChildGridUserActions by default', () => {
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: { key: '' }, // Default case
    });

    render(<ChildGridCardHeader cardIndex={1} />);

    expect(screen.getByTestId('child-events-header')).toBeInTheDocument();
    expect(screen.getByTestId('child-grid-user-actions')).toBeInTheDocument();
  });

  it('should render the ChildOfferCardHeader when the key is "offers"', () => {
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: { key: 'offers' },
    });

    render(<ChildGridCardHeader cardIndex={2} />);

    expect(screen.getByTestId('child-offer-card-header')).toBeInTheDocument();
    expect(screen.getByTestId('child-grid-user-actions')).toBeInTheDocument();
  });

  it('should render the ChildEventsHeader when the key is "events"', () => {
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: { key: 'events' },
    });

    render(<ChildGridCardHeader cardIndex={3} />);

    expect(screen.getByTestId('child-events-header')).toBeInTheDocument();
    expect(screen.getByTestId('child-grid-user-actions')).toBeInTheDocument();
  });

  it('should pass the correct props to the HeaderComponent and ChildGridUserActions', () => {
    const mockSelectedTabConfig = { key: 'offers', someOtherProp: 'value' };
    (useSelectorWrap as jest.Mock).mockReturnValue({
      data: mockSelectedTabConfig,
    });

    render(<ChildGridCardHeader cardIndex={4} />);

    expect(ChildOfferCardHeader).toHaveBeenCalledWith(
      { cardIndex: 4, selectedTabConfig: mockSelectedTabConfig },
      {}
    );
    expect(ChildGridUserActions).toHaveBeenCalledWith(
      { cardIndex: 4, selectedTabConfig: mockSelectedTabConfig },
      {}
    );
  });
});