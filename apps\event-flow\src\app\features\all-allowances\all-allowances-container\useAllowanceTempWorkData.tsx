import { useSelectorWrap } from "@me/data-rtk";
import { useEffect } from "react";
import { useGetAllowanceTempWorkDataQuery } from "../../create-event/service/apis/allowance-api";
import { allowanceTempWorkHandler } from "../../create-event/service/slice/allowance-temp-work-slice";
import { useDispatch } from "react-redux";

export const useAllowanceTempWorkData = (eventId, isSummary) => {
  const dispatch = useDispatch();
  const { data: allowanceTempWorkData } = useSelectorWrap(
    "allowance_temp_work"
  );
  const {
    data: allowanceTempDataFromGet,
    isFetching: isTempworkGetCreateLoaded,
  } = useGetAllowanceTempWorkDataQuery(
    {
      URL_PARAM: eventId,
      queryParams: {},
    },
    {
      skip: !eventId || allowanceTempWorkData || isSummary,
    }
  );

  useEffect(() => {
    if (allowanceTempWorkData || !allowanceTempDataFromGet || isSummary) return;
    dispatch(
      allowanceTempWorkHandler({
        allowanceData: allowanceTempDataFromGet,
        isTempLoaded: true,
      })
    );
  }, [allowanceTempDataFromGet, allowanceTempWorkData, dispatch, isSummary]);

  return { allowanceTempWorkData, isTempworkGetCreateLoaded };
};
