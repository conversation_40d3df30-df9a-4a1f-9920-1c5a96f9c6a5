import { renderHook, act } from "@testing-library/react";
import { useDispatch } from "react-redux";
import useHandleOfferSections from "./useHandleOfferSections";
import { setOfferSectionsData } from "../../../../service/slice/allowance-details-slice";
import { DEFAULT_OFFER_SECTIONS, OFFER_ALLOWANCE } from "../offer-flow-config";

jest.mock("react-redux", () => ({
  useDispatch: jest.fn(),
}));

jest.mock("../../../../service/slice/allowance-details-slice", () => ({
  setOfferSectionsData: jest.fn(),
}));

describe("useHandleOfferSections Hook", () => {
  let dispatchMock;

  beforeEach(() => {
    dispatchMock = jest.fn();
    useDispatch.mockReturnValue(dispatchMock);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should dispatch default sections if createInd is not provided", () => {
    const { result } = renderHook(() => useHandleOfferSections());
    act(() => {
      result.current.setOfferSections({ createInd: "" });
    });

    expect(dispatchMock).toHaveBeenCalledWith(setOfferSectionsData(DEFAULT_OFFER_SECTIONS));
  });

  it("should filter and dispatch correct sections when createInd is provided", () => {
    const mockSections = [
      { name: "Section1", includeAll: true },
      { name: "Section2", createInd: ["test"] },
    ];

    // Mocking the OFFER_ALLOWANCE object directly
    OFFER_ALLOWANCE.sections = ["Section1", "Section2"];
    OFFER_ALLOWANCE.Section1 = { name: "Section1", includeAll: true };
    OFFER_ALLOWANCE.Section2 = { name: "Section2", createInd: ["test"] };

    const { result } = renderHook(() => useHandleOfferSections());
    act(() => {
      result.current.setOfferSections({ createInd: "test" });
    });

    expect(dispatchMock).toHaveBeenCalledWith(setOfferSectionsData(mockSections));
  });
});
