import React, { useMemo, useState } from "react";
import Modal from "@albertsons/uds/molecule/Modal";
import PlanProductGroupItemCard from "../../../../../create-event/components/cards/event-details/plan-product-group-item-card"; 
import { FileText } from 'lucide-react';

const ModalHeader = ({ itemCount }: { itemCount: number }) => (
  <div className="h-[72px] w-[100%] pt-[24px] pl-[24px] pb-[18px] border-b-[1px] border-[#c8daeb]">
    <p className="text-2xl font-bold text-[#2b303c]">Items ({itemCount})</p>
  </div>
);

const ModalContent = ({ card }: { card: JSX.Element }) => (
  <div
    className={`text-medium text-center select-none font-bold text-[28px] px-[18px] py-[23.5px]`}
  >
    {card}
  </div>
);

function ChildGridItemsDetails({ eventDetails }) {
  const [itemDetailsPoper, setItemDetailsPoper] = useState(false);

  const itemCount = eventDetails?.items?.length || 0;
  const itemsDetails = useMemo(
    () => ({
      productGroupName:
        eventDetails?.planProductGroups?.[0]?.planProductGroupName,
      productGroupItems:
        eventDetails?.items.map(item => ({
          ...item,
          cic: item?.itemId || "",
          size: item?.descriptiveSize || "",
        })) || [],
    }),
    [eventDetails]
  );

  const card = (
    <PlanProductGroupItemCard promoProductGroup={itemsDetails} index={0} />
  );

  const handleIconClick = () => {
    setItemDetailsPoper(true);
  };

  return (
    <>
      <span onClick={handleIconClick} className="cursor-pointer">
        <FileText
          height={16}
          strokeWidth={1}
          color={"#1b6ebb"}
        />
      </span>
      <Modal
        isOpen={itemDetailsPoper}
        onClose={() => setItemDetailsPoper(false)}
        height={itemCount ? 820 : 316}
        width={827}
        className="overflow-auto rounded-none"
      >
        <ModalHeader itemCount={itemCount} />
        <ModalContent card={card} />
      </Modal>
    </>
  );
}
export default ChildGridItemsDetails;
