import { isEqual, pick } from "lodash";
import efConstants from "../../../shared/ef-constants/ef-constants";
import { leadDistributors<PERSON><PERSON><PERSON>, leadDistributorsModeHandler, setInitialLeadDistData } from "../../create-event/service/slice/lead-distributors-slice";
import { setIsUpdateAllowanceDistributorSelection } from "../../create-event/service/slice/allowances-dashboard-slices";
import _ from "lodash";
import { appConstants } from '../../../../../../../libs/utils/root-props/src/lib/app-constants/app-constants';
import { isFeatureFlagEnabled } from "@me-upp-js/utilities";

export const getMainAndOtherVendors = (tableData, leadOptions) => {
  const vendors: any[] = getVendorWithDetails(tableData);
  return filterVendors(leadOptions, vendors);
};

const isCsdFeatureEnabled = isFeatureFlagEnabled(appConstants.FEATURE_FLAGS.CSD_VENDOR_ACCESS);

const filterVendors = (leadOptions: string[], vendors) => {
  const isAllExternal = isCsdFeatureEnabled ? vendors?.[0]?.isAllExternal : false;
  return leadOptions?.reduce(
    (acc: any, item) => {
      const vendor = vendors?.find(v => v?.vendorNbr === item),
        isExternalVend = checkIfExternal(vendor);
      isExternalVend
        ? acc?.otherVendors?.push(item)
        : acc?.mainVendors?.push(item);
      return acc;
    },
    { mainVendors: [], otherVendors: [], isAllExternal }
  );
};


export const getVendorWithDetails = (tableData) =>  tableDataToVendorsArray(tableData);
export function validateIfAllOtherVendorsAssignedLeads({
  tableData,
  addIsBillingCheck = false,
  allDivisionsTableData = []
}) {
  const vendors: any[] = tableDataToVendorsArray(tableData);
  const defaultMode = vendors?.[0]?.leadDistributorMode;
  const isAllExternal = isCsdFeatureEnabled ? vendors?.[0]?.isAllExternal : false;
  const isAllOtherVendorExcluded = vendors
    ?.filter(vendor => vendor?.modCommand === efConstants.ZERO_COST_TEXT)
    ?.every(vendor => !vendor?.includeInd);
  const isBillingInfoShown = validateToShowExternalVendorOption(vendors, allDivisionsTableData);
  if (!isBillingInfoShown && !defaultMode && !isAllExternal) return true;
  if (isAllExternal && !defaultMode && !addIsBillingCheck) return true;
  if (!isBillingInfoShown && defaultMode && !isAllExternal && addIsBillingCheck) return false;
  if (isAllOtherVendorExcluded && !addIsBillingCheck) return true;

  let isValid = true;
  vendors.forEach(vendor => {
    const isExternal = checkIfExternal(vendor);
    const isUnderOtherLead = !!vendors.find(v => {
      return v?.leadDistributorInfos
        ?.map(item => item.vendorNbr)
        .includes(vendor.vendorNbr);
    });
    if (isExternal && vendor?.includeInd) {
      if(isAllExternal && !addIsBillingCheck) {
        isValid = isUnderOtherLead || vendor?.leadDistributorInd ? true : false;
      } else if (!isUnderOtherLead) {
        isValid = false;
      }
    } else if (addIsBillingCheck) {
      if (isUnderOtherLead && !isAllExternal) {
        isValid = false;
      }
    }
  });

  return isValid;
}

export function getDefaultBillingModalState({
  tableData,
  mainVendors,
  otherVendors,
  allDivisionsTableData = []
}) {
  const isProperData = validateIfAllOtherVendorsAssignedLeads({
    tableData,
    addIsBillingCheck: true,
    allDivisionsTableData,
  });

  const obj = {};

  if (isProperData) {
    const vendors = tableDataToVendorsArray(tableData);

    mainVendors?.forEach(mVendor => {
      const vendor = vendors.find(v => v.vendorNbr === mVendor);
      vendor?.leadDistributorInfos?.forEach(v => {
        obj[v.vendorNbr] = mVendor;
      });
    });
  } else {
    otherVendors?.forEach(vNbr => {
      obj[vNbr] = mainVendors[0];
    });
  }

  return obj;
}

export function checkIfExternal(vendor) {
  return (
    vendor?.allowanceItems?.[0]?.modCommand === efConstants.ZERO_COST_TEXT ||
    vendor?.modCommand === efConstants.ZERO_COST_TEXT
  );
}

export function checkIfAnyExternalVendorPresent(vendors) {
  let externalPreset = false;
  vendors.forEach(element => {
    if (checkIfExternal(element)) {
      externalPreset = true;
    }
  });
  return externalPreset;
}

export function tableDataToVendorsArray(tableData) {
  const vendors: any[] = [];

  tableData?.forEach(element => {
    const details = element.vendorDetails;

    details?.forEach(vendor => {
      const item = vendors.find(item => item.vendorNbr === vendor.vendorNbr);
      const index = vendors.findIndex(
        item => item.vendorNbr === vendor.vendorNbr
      );

      if (item) {
        vendors[index] = { ...item, ...vendor };
      } else {
        vendors.push(vendor);
      }
    });
  });

  return vendors;
}

export function validateToShowExternalVendorOption(
  vendors,
  allDivisionsTableData = []
) {
  if (allDivisionsTableData?.length) {
    return allDivisionsTableData?.some((divTableData: any) => {
      const vendors = tableDataToVendorsArray(divTableData?.tableData || []);
      return isShowBillingOption(vendors);
    });
  } else {
    return isShowBillingOption(vendors);
  }
}

const isShowBillingOption = (vendors) => {
 const externalVendorPreset = checkIfAnyExternalVendorPresent(vendors);
  if (externalVendorPreset === false) return false;

  const getApArNumber = billing =>
    pick(billing, ["acPayableVendorNbr", "acReceivableVendorNbr"]);

  const internalVendors = vendors.filter(vendor => !checkIfExternal(vendor));
  const commonVendor = getApArNumber(
    internalVendors?.[0]?.allowanceBillingInfo
  );

  let isBillingDetailsCommon = true;

  internalVendors.forEach(vendor => {
    const vendorBilling = getApArNumber(vendor.allowanceBillingInfo);
    if (!isEqual(commonVendor, vendorBilling)) {
      isBillingDetailsCommon = false;
    }
  });
  return !isBillingDetailsCommon;
}

export function getLeadDistributorInitialState(tableData, allDivisionsTableData = []) {
  const vendors = tableDataToVendorsArray(tableData);
  const defaultMode = vendors?.[0]?.leadDistributorMode;
  const externalVendorPreset = checkIfAnyExternalVendorPresent(vendors);

  if (!defaultMode) return "ALL";
  if (
    defaultMode === "BILL_LEAD_DIST" &&
    validateIfAllOtherVendorsAssignedLeads({
      tableData,
      addIsBillingCheck: true,
      allDivisionsTableData
    }) &&
    externalVendorPreset
  ) {
    return "BILLING";
  }
  return "LEAD";
}

export function getValidBillingInfo(
  allowances,
  index,
  vendorFromTable,
  tableData
) {
  const vendors = tableDataToVendorsArray(tableData);
  const isAllExternal = isCsdFeatureEnabled ? vendors?.[0]?.isAllExternal : false;
  if (vendorFromTable?.leadDistributorMode === efConstants.BILL_LEAD_DIST) {
    if (vendorFromTable?.leadDistributorInd) {
      return allowances[index]?.allowanceBillingInfo;
    } else {
      const parentIndex = vendors.findIndex(v =>
        v?.leadDistributorInfos?.find(
          ld => ld.vendorNbr === vendorFromTable.vendorNbr
        )
      );
      return parentIndex < 0 ? allowances?.[index]?.allowanceBillingInfo : allowances?.[parentIndex]?.allowanceBillingInfo;
    }
  }

  const externalVendorPreset = checkIfAnyExternalVendorPresent(vendors);
  const showBillingOp = validateToShowExternalVendorOption(vendors);

  if (
    !vendorFromTable?.leadDistributorMode &&
    externalVendorPreset &&
    !showBillingOp
  ) {
    const mainVendorIndex = vendors.findIndex(
      vendor => !checkIfExternal(vendor)
    );

    return isAllExternal ? allowances?.[index]?.allowanceBillingInfo :
      allowances?.[mainVendorIndex]?.allowanceBillingInfo
  }
  return allowances[index].allowanceBillingInfo;
}
export const onCloseDistPopup = ({
  selectedSteps,
  defaultModeSelection,
  dispatch,
  setIsModelOpen,
  leadSelectionType,
}) => {
  const isAlreadyLeadSelected =
    selectedSteps?.length > 0 || defaultModeSelection || leadSelectionType;
  dispatch(
    leadDistributorsModeHandler({
      leadDistributorsMode: isAlreadyLeadSelected ? leadSelectionType : "ALL",
    })
  );
  dispatch(
    setIsUpdateAllowanceDistributorSelection({
      isAllowancesDistributorType: isAlreadyLeadSelected
        ? leadSelectionType
        : "ALL",
    })
  );
  setIsModelOpen(false);
};

 export const getExternalDistDetails = (
   tableData,
   leadOptions,
   setExternalVendData
 ) => {
   const { mainVendors, otherVendors } = getMainAndOtherVendors(
     tableData,
     leadOptions?.map(item => item?.vendorNbr) || []
   );
   setExternalVendData({
     mainVendors,
     otherVendors,
     isExternal: otherVendors?.length > 0,
   });
 };
export const leadDistError = (isLeadDistributorError) =>  (
    <>
      {isLeadDistributorError && (
        <div className="text-center text-red-900 font-semibold text-lg">
          {efConstants.LEAD_DIST_ERR_MSG}
        </div>
      )}
    </>
  );
export const isInValidLeadDists = (tableData) => {
    return tableData?.some(
      item =>
        item?.vendorDetails?.filter(vendor => vendor?.leadDistributorInd)
          ?.length > 2
    );
};
export const updateLeadData = (
  vendorNbr,
  leadDistData,
  isAdding,
  isExternalVendor
) => {
  return !isAdding
    ? isExternalVendor
      ? handleReIncludeExternalVend(leadDistData, vendorNbr)
      : _.uniqBy([...leadDistData, { id: vendorNbr, child: [] }], "id")
    : leadDistData?.map(item => {
        return item?.child?.includes(vendorNbr)
          ? {
              ...item,
              child: item?.child?.filter(
                childVndNo => childVndNo !== vendorNbr
              ),
            }
          : item;
      });
};
export const handleReIncludeExternalVend = (leadDistData, vendorNbr) => {
  const isAlreadyChild = leadDistData?.some(item =>
    item?.child?.includes(vendorNbr)
  );
  if (!isAlreadyChild && leadDistData?.length) {
    const updatedLead = [...leadDistData];
    updatedLead[0] = {
      id: leadDistData?.[0]?.id,
      child: [...leadDistData?.[0]?.child, vendorNbr],
    };
    return updatedLead;
  }
  return leadDistData;
};

export const setLeadsForOwnDistOnReInclude = ({
  dispatch,
  checked,
  leadDistData,
  vendorNbr,
  leadDistSliceData,
  isExternalVendor,
  divisionId = "",
  isNational = false,
}) => {
  const updatedLeadData = updateLeadData(
    vendorNbr,
    isNational && divisionId ? leadDistData?.[divisionId] ?? {} : leadDistData,
    checked,
    isExternalVendor
  );
  dispatch(
    setInitialLeadDistData({
      leadDistData:
        isNational && divisionId
          ? { ...leadDistData, [divisionId]: updatedLeadData }
          : updatedLeadData,
    })
  );
  dispatch(
    leadDistributorsHandler(
      isNational && divisionId
        ? {
            ...leadDistSliceData,
            allDivisionStepsData: {
              ...leadDistSliceData?.allDivisionStepsData,
              [divisionId]: {
                ...leadDistSliceData?.allDivisionStepsData?.[divisionId],
                stepData: updatedLeadData,
              },
            },
          }
        : { ...leadDistSliceData, stepData: updatedLeadData }
    )
  );
};
export const disableIntrnlVendor = (externalvendData, excludedVendors) => {
  const  { mainVendors } = externalvendData;
      return (
        mainVendors?.length -
          excludedVendors?.filter((vendor: any) =>
            mainVendors?.includes(vendor?.vendorNbr)
          )?.length <=
        1
      );
}
export function moveItemBetweenParents(fromVal, toVal, id, leadDistSets) {
    return leadDistSets?.map(distSetData => {
      return {
        ...distSetData,
        child:
          distSetData?.id === fromVal?.id
            ? distSetData?.child?.filter(item => item !== id)
            : (distSetData?.id === toVal?.id
            ? [...distSetData?.child, id]
            : distSetData?.child),
      };
    });
}

export const updatedLeadDistSet = (
    leadDistSets,
    id,
    direction
  ) => {
    const childIndex = leadDistSets?.findIndex(distSet =>
      distSet?.child?.includes(id)
    );
    if (childIndex === -1) return null;

    const targetIndex =
      direction === efConstants.MOVE_BTNS_LABELS.UP
        ? childIndex - 1
        : childIndex + 1;
    if (targetIndex < 0 || targetIndex >= leadDistSets.length) return null;
    return moveItemBetweenParents(
      leadDistSets?.[childIndex],
      leadDistSets?.[targetIndex],
      id,
      leadDistSets
    );
};
    /**
 * Filters lead options based on exclusion and selection criteria.
 * @param {Array} leadOptions - The list of all lead options.
 * @param {Array} selectedList - The current selected list of items.
 * @returns {Array} Filtered lead options.
 */
export const getFilteredLeads = (leadOptions, selectedList, excludedVendorData) =>
  leadOptions?.filter(lead =>
    !excludedVendorData?.excludedVendors?.includes(lead) && !selectedList?.includes(lead)
  ) || [];

  /**
 * Creates step data from the selected list and filtered leads.
 * @param {Array} selectedList - The current selected list of items.
 * @param {Array} filteredLeads - The filtered list of leads.
 * @returns {Array} The step data to be set.
 */
export const createStepData = (selectedList, filteredLeads) =>
  selectedList?.map((item, index) => ({
    id: item,
    child: index === 0 ? filteredLeads : []
  }));
export const isNationalType = isNdpType =>
  (isFeatureFlagEnabled(appConstants?.FEATURE_FLAGS?.NATIONAL_EVENTS)) &&
  isNdpType;
