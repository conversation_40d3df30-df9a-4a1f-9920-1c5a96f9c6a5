import { ChevronRightIcon } from "lucide-react";
import React, { memo, useCallback, useEffect } from "react";
import InvalidCICItems from "../../invalid-cic-items";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import {
  formatCicPpgDisplayName,
  getUnitTypesOfAddedCICs,
  isCICTypePPGsAvailable,
  removeInvalidCICIds,
} from "../../../../../service/event-details/event-detail-service";
import TextArea from "@albertsons/uds/molecule/TextArea";
import { useFormContext } from "react-hook-form";
import UnresolvedCICItems from "../../unresolved-cic-items";
import Button from "@albertsons/uds/molecule/Button";
import {
  findMaxValue,
  getTodaysDate,
  validateNCDP,
} from "../../event-details-card-service";
import {
  usePostPlanEventItemsByCICsMutation,
  usePostPlanPPGsResolveMutation,
} from "../../../../../service/apis/event-api";
import {
  setCICsWithUPCTypeInfoHandler,
  setPPGsAddedThroughCICItemsHandler,
} from "../../../../../service/slice/event-detail-slice";
import { useDispatch } from "react-redux";
import CICErrorModal from "../../cic-error-modal";
import { CommonModal } from "../../../common";
import { SELECT_UPC_TYPE_labels } from "../../../../../constants/event-status/contsants";
import Select from "@albertsons/uds/molecule/Select";
import { useSelectorWrap } from "@me/data-rtk";
import { itemOrderAlphabeticalChecker } from "../../utility/utility";
import LoadingSpinner from "../../../../../constants/LoadingSpinner/LoadingSpinner";

function PpgCICField({
  isPPGsAddedThroughCICItems,
  ppgFieldsData,
  setPpgFieldsData,
  onPromoProductGroupsChange,
  setFormFields,
  formFields,
}) {
  const { setValue, getValues } = useFormContext();
  const {
    data: { cicsWithUpcTypeInfo },
  } = useSelectorWrap("cic_upc_type_data");
  const [
    postPlanEventItemsByCICs,
    { isLoading: isPlanEventItemsByCICSLoading },
  ] = usePostPlanEventItemsByCICsMutation();
  const [
    postPlanPPGsResolve,
    {
      isLoading: isPlanPPGResolveLoading,
      data: planPPGsResolveData,
      isError: planPPGsResolveError,
    },
  ] = usePostPlanPPGsResolveMutation();
  const dispatch = useDispatch();
  const promoProductGroups = getValues("planProductGroups");
  const [cicFieldsdata, setCicFieldsData] = React.useState({
    cicInput: "",
    cicIDs: [],
    isCICTextInputArea: false,
    unresolvedItemStatuses: [],
    invalidCICIdItems: [],
    upcTypeData: [],
    isUPCTypeModalOpen: false,
    selectedUPCTypeCode: "",
    mixedUpcTypeErrorForModal: "",
  });

  const { data: eventTypeAndDivisionsData } = useSelectorWrap(
    "event_type_and_divisions"
  );
  const eventType = eventTypeAndDivisionsData?.eventType || "";

  useEffect(() => {
    planPPGsResolveData?.length && addPPGsFromResolve(planPPGsResolveData);
  }, [planPPGsResolveData]);

  const checkCicLength = splitStrCICs => {
    const isError = splitStrCICs?.some(
      cic => cic?.length > 8 || /[a-zA-Z]+|[^\w\s]+/.test(cic)
    );
    return isError;
  };
  const addPPGsFromResolve = element => {
    if (element?.length > 1) {
      const selectedPromoProductGroup = element?.reduce((prev, curr) => {
        return itemOrderAlphabeticalChecker(prev, curr, "itemCount");
      });

      // Only setValue for promoProductGroup if there was an actual change between the current and element.
      if (selectedPromoProductGroup?.name !== getValues("promoProductGroup")) {
        const ppgName = formatCicPpgDisplayName(selectedPromoProductGroup);
        setValue("promoProductGroup", ppgName);
      }
    } else if (element?.length === 1) {
      const ppgName = formatCicPpgDisplayName(element?.[0]);
      setValue("promoProductGroup", ppgName);
    }
    setFormFields(prevState => ({
      ...prevState,
      disableStoreGroupType: getValues?.("planProductGroups")?.length
        ? false
        : true,
    }));
  };
  const resolveCICPPGsResponse = data => {
    addPPGsFromResolve(data?.resolvedPlanProductGroups);
    if (data?.resolvedPlanProductGroups?.length) {
      setValue("planProductGroups", data?.resolvedPlanProductGroups);
      onPromoProductGroupsChange(data?.resolvedPlanProductGroups);
      dispatch(
        setPPGsAddedThroughCICItemsHandler({ isPPGsAddedThroughCICItems: true })
      );
      setPpgFieldsData(prevState => ({
        ...prevState,
        disablePromoProductGroupsField: true,
        disableAddItems: true,
      }));
      setFormFields(prevState => ({
        ...prevState,
        unresolvedItemsError: data?.unresolvedItemStatuses?.length,
      }));
    }
    // setUnresolvedItemsError(data?.unresolvedItemStatuses?.length);
    // assign resolveValidInd from unresolvedItemStatuses to itemValidInd and map it with plannedProductGroupItems itemId to get itemDescription
    const invalidItemStatusesWithDescription =
      data?.unresolvedItemStatuses?.map(item => {
        const matchingItem =
          data?.plannedEventItems?.[0]?.plannedProductGroupItems?.find(
            el => el?.itemId === item?.itemId
          );
        return {
          itemId: item?.itemId,
          itemValidInd: item?.resolveValidInd,
          description: matchingItem?.itemDescription,
        };
      });
    setPpgFieldsData(prevState => ({
      ...prevState,
      disablePromoProductGroupsField: true,
      disableAddItems: true,
      mixedUpcTypeError: "",
    }));
    setFormFields(prevState => ({
      ...prevState,
      unresolvedItemsError: data?.unresolvedItemStatuses?.length,
    }));
    setCicFieldsData(prevState => ({
      ...prevState,
      invalidCICIdItems: invalidItemStatusesWithDescription,
      unresolvedItemStatuses: data?.unresolvedItemStatuses,
    }));
  };
  const filterUPCs = async plannedProductGroupItems => {
    let cicsWithUpcTypeInfo: any = [];
    const { PPG_UNIT_TYPE_MAPPER } = efConstants;
    plannedProductGroupItems.forEach(ele => {
      const { plannedCicUpcs = [] } = ele || {};

      type MyObject = Record<any, any>;
      const rogUnitTypes: MyObject = {};

      plannedCicUpcs?.forEach(upcDetail => {
        rogUnitTypes[upcDetail?.rogs?.[0]?.unitType] =
          upcDetail?.rogs?.length || 0;
      });
      const max = findMaxValue(rogUnitTypes, PPG_UNIT_TYPE_MAPPER);
      cicsWithUpcTypeInfo = [
        ...cicsWithUpcTypeInfo,
        { ...max, upcID: ele?.itemId },
      ];
    });
    dispatch(setCICsWithUPCTypeInfoHandler({ cicsWithUpcTypeInfo }));
    const arrayUniqueByKey: any = [
        ...new Map(
          cicsWithUpcTypeInfo?.map(item => [item?.["code"], item])
        )?.values(),
      ],
      isValidLength = arrayUniqueByKey?.length > 1;
    setCicFieldsData(prevState => ({
      ...prevState,
      upcTypeData: arrayUniqueByKey,
      isUPCTypeModalOpen: isValidLength,
    }));

    if (!isValidLength) {
      const cicsToCreatePPGs = cicsWithUpcTypeInfo?.map(cic => cic?.upcID);
      const unitTypeCode = cicsWithUpcTypeInfo?.[0]?.code;
      const existingPPGUnitTypeCode =
        getValues("planProductGroups")?.[0]?.unitType;
      const unitTypeMapperCode =
        PPG_UNIT_TYPE_MAPPER?.[existingPPGUnitTypeCode]?.code;
      if (unitTypeMapperCode && unitTypeMapperCode !== unitTypeCode) {
        setPpgFieldsData(prevState => ({
          ...prevState,
          mixedUpcTypeError: efConstants.MIXED_UPC_TYPE_ERROR,
        }));
        setCicFieldsData(prevState => ({
          ...prevState,
          unresolvedItemStatuses: [],
        }));
        return;
      } else {
        // Resolve payload
        setPpgFieldsData(prevState => ({
          ...prevState,
          mixedUpcTypeError: "",
        }));
        const payload = {
          division:
            getValues("eventType") === "NDP"
              ? ["98"]
              : [formFields?.divisionId],
          promoStartDate: getTodaysDate(),
          unitType: unitTypeCode,
          itemIds: cicsToCreatePPGs,
        };
        const { data } = await postPlanPPGsResolve(payload);
        resolveCICPPGsResponse(data);
      }
    }
  };
  const removeInvalidItems = () => {
    const cicInputValues = removeInvalidCICIds(
      cicFieldsdata?.cicIDs,
      cicFieldsdata?.invalidCICIdItems?.map((el: any) => el?.itemId)
    );
    setFormFields(prevState => ({ ...prevState, cicsErrorMessage: "" }));
    setCicFieldsData(prevState => ({
      ...prevState,
      cicInput: cicInputValues?.length ? cicInputValues.join(",") : "",
      cicIDs: cicInputValues,
    }));
    setPpgFieldsData(prevState => ({
      ...prevState,
      cicAlphaNumericError: false,
    }));
  };
  const handleCICsAddItems = async () => {
    if (cicFieldsdata?.cicIDs?.length) {
      const payload = {
        division:
          getValues("eventType") === "NDP" ? ["98"] : [formFields?.divisionId],
        promoStartDate: getTodaysDate(),
        overrideStoreGroups: [],
        itemIds: cicFieldsdata?.cicIDs,
        savePlannedEventItems: false,
      };
      setFormFields(prevState => ({ ...prevState, cicsErrorMessage: "" }));
      const { data = [] } = await postPlanEventItemsByCICs(payload);
      if (data?.length) {
        const {
          invalidItemStatuses = [],
          plannedProductGroupItems = [],
          invalidPlannedProductGroupItems = [],
        } = data?.[0] || {};
        if (invalidItemStatuses?.length) {
          setFormFields(prevState => ({
            ...prevState,
            cicsErrorMessage: efConstants.INVALID_CICS_ERROR,
          }));
          // join invalidItemStatuses with invalidPlannedProductGroupItems with itemid to return item id, description from invalidPlannedProductGroupItems and itemValidInd from invalidItemStatuses
          const invalidItemStatusesWithDescription = invalidItemStatuses?.map(
            item => {
              const matchingItem = invalidPlannedProductGroupItems?.find(
                el => el.itemId === item.itemId
              );
              return {
                ...item,
                description: matchingItem?.itemDescription,
              };
            }
          );
          setCicFieldsData(prevState => ({
            ...prevState,
            invalidCICIdItems: invalidItemStatusesWithDescription || [],
            unresolvedItemStatuses: [],
          }));
          setFormFields(prevState => ({
            ...prevState,
            unresolvedItemsError: false,
          }));
          return;
        } else {
          setPpgFieldsData(prevState => ({
            ...prevState,
            cicAlphaNumericError: false,
          }));
          setFormFields(prevState => ({ ...prevState, cicsErrorMessage: "" }));
          removeInvalidItems();
          const filteredUPCs = filterUPCs(plannedProductGroupItems);
          return filteredUPCs;
        }
      }
    }
    return null;
  };
  const stopPropogation = (
    event:
      | React.MouseEvent<HTMLDivElement>
      | React.KeyboardEvent<HTMLDivElement>
      | React.ChangeEvent<HTMLElement>
  ) => {
    event.stopPropagation();
  };

  const oneEnterCICIdHandler = e => {
    stopPropogation(e);
    const cicValues = e.target?.value;
    if (cicValues) {
      const splitStrCICs = cicValues
        ?.trim()
        ?.split(/(?:,| |\n)+/)
        .filter(item => item?.trim());
      setCicFieldsData(prevState => ({
        ...prevState,
        cicInput: cicValues,
        cicIDs: splitStrCICs,
      }));
      setPpgFieldsData(prevState => ({
        ...prevState,
        disableAddCICs: !cicValues,
        disableAddItems: checkCicLength(splitStrCICs),
        cicAlphaNumericError: checkCicLength(splitStrCICs),
      }));
    } else {
      setCicFieldsData(prevState => ({
        ...prevState,
        cicInput: "",
        cicIDs: [],
      }));
      setFormFields(prevState => ({ ...prevState, cicsErrorMessage: "" }));
      setPpgFieldsData(prevState => ({
        ...prevState,
        disableAddCICs: false,
        disableAddItems: false,
        mixedUpcTypeError: "",
        cicAlphaNumericError: false,
      }));
    }
  };

  const onEnterCICOptionHandler = () => {
    setCicFieldsData(prevState => ({
      ...prevState,
      isCICTextInputArea: !prevState?.isCICTextInputArea,
      cicInput: !prevState?.isCICTextInputArea ? "" : prevState?.cicInput,
    }));
  };
  const enterCICIdsField = useCallback(() => {
    const isCIC = isCICTypePPGsAvailable(promoProductGroups);
    const errorMessage = ppgFieldsData?.cicAlphaNumericError
      ? efConstants.ALPHA_NUMERIC_ERROR
      : !(isPPGsAddedThroughCICItems || isCIC) && promoProductGroups?.length
      ? efConstants.REMOVE_PPG_ERROR
      : ppgFieldsData?.mixedUpcTypeError
      ? ppgFieldsData?.mixedUpcTypeError
      : "";

    return (
      <div onKeyDown={stopPropogation} onKeyUp={stopPropogation}>
        <TextArea
          onChange={e => {
            stopPropogation(e);
            oneEnterCICIdHandler(e);
          }}
          onPaste={e =>
            setTimeout(() => {
              oneEnterCICIdHandler(e);
            }, 10)
          }
          value={cicFieldsdata?.cicInput}
          className="cic-text-area"
          placeholder={efConstants.ENTER_CICS_PLACEHOLDER}
          label={""}
          isRequired={true}
          error={errorMessage}
          disabled={
            validateNCDP(eventType) || ppgFieldsData?.disableAddCICs
          }
        />
      </div>
    );
  }, [
    getValues,
    isPPGsAddedThroughCICItems,
    cicFieldsdata?.cicInput,
    efConstants.ENTER_CICS_PLACEHOLDER,
    ppgFieldsData?.cicAlphaNumericError,
    efConstants.ALPHA_NUMERIC_ERROR,
    efConstants.REMOVE_PPG_ERROR,
    ppgFieldsData?.disableAddCICs,
    ppgFieldsData?.mixedUpcTypeError,
  ]);
  const onCloseCICModalHandler = () => {
    setFormFields(prevState => ({ ...prevState, isCICModalOpen: false }));
    document.body.style.overflow = "visible";
  };
  const onUPCTypeChangeHandler = e => {
    setCicFieldsData(prevState => ({
      ...prevState,
      selectedUPCTypeCode: e.code,
    }));
    const addedUpcType = getUnitTypesOfAddedCICs(
      getValues("planProductGroups")
    );
    if (addedUpcType && addedUpcType !== e.code) {
      setCicFieldsData(prevState => ({
        ...prevState,
        mixedUpcTypeErrorForModal: efConstants.MIXED_UPC_TYPE_ERROR_FOR_MODAL,
      }));
      return;
    } else {
      setCicFieldsData(prevState => ({
        ...prevState,
        mixedUpcTypeErrorForModal: "",
      }));
    }
  };
  const selectUPCTypeComp = () =>
    cicFieldsdata?.upcTypeData?.length ? (
      <Select
        items={cicFieldsdata?.upcTypeData}
        itemKey={"code"}
        itemText={"name"}
        className={`min-w-[310px] ${efConstants.componentClassName.SELECT_UPC_TYPE_COMP}`}
        placeholder="Select UPC types"
        onChange={e => onUPCTypeChangeHandler(e)}
      />
    ) : null;
  const onUPCTypeCloseHandler = () => {
    setCicFieldsData(prevState => ({
      ...prevState,
      isUPCTypeModalOpen: false,
      selectedUPCTypeCode: "",
      mixedUpcTypeErrorForModal: "",
    }));
    document.body.style.overflow = "visible";
  };
  const onConfirmHandler = async () => {
    const selectedCICs = cicsWithUpcTypeInfo
      ?.map(cic => {
        return cic.code === cicFieldsdata?.selectedUPCTypeCode
          ? cic?.upcID
          : "";
      })
      ?.filter(ele => ele);
    const payload = {
      division:
        getValues("eventType") === "NDP" ? ["98"] : [formFields?.divisionId],
      promoStartDate: getTodaysDate(),
      unitType: cicFieldsdata?.selectedUPCTypeCode,
      itemIds: selectedCICs,
    };
    const { data } = await postPlanPPGsResolve(payload);
    resolveCICPPGsResponse(data);
  };
  const onModalPopUpReturnorClose = val => {
    setCicFieldsData(prevState => ({
      ...prevState,
      isUPCTypeModalOpen: val,
      selectedUPCTypeCode: "",
    }));
  };
  return (
    <>
      <LoadingSpinner
        isLoading={isPlanEventItemsByCICSLoading || isPlanPPGResolveLoading}
        classname="!h-full !w-full rounded-md"
      />
      <div
        className="flex cursor-pointer"
        onClick={() => onEnterCICOptionHandler()}
      >
        <ChevronRightIcon
          height={24}
          strokeWidth={1}
          color="#1B6EBB"
          className={`min-w-[24px] min-h-[24px] transition duration-200	 ${
            cicFieldsdata?.isCICTextInputArea ? "rotate-90" : ""
          }`}
        />
        <span className="text-[#1B6EBB] text-[14px] leading-6 font-semibold">
          {efConstants.ENTER_CIC_IDS_LABEL}
        </span>
      </div>
      {cicFieldsdata?.isCICTextInputArea ? (
        <div>
          {enterCICIdsField()}
          {formFields?.cicsErrorMessage ? (
            <InvalidCICItems
              cicsErrorMessage={formFields?.cicsErrorMessage}
              invalidCICIdItems={cicFieldsdata?.invalidCICIdItems}
              removeInvalidItems={removeInvalidItems}
            />
          ) : null}
          {formFields?.unresolvedItemsError ? (
            <UnresolvedCICItems
              unresolvedItemStatuses={cicFieldsdata?.unresolvedItemStatuses}
            />
          ) : null}
          <Button
            variant="secondary"
            type="button"
            className="mt-2.5"
            onClick={handleCICsAddItems}
            width={100}
            disabled={
              validateNCDP(eventType)
                ? true
                : !cicFieldsdata?.cicInput || ppgFieldsData?.disableAddItems
            }
          >
            {efConstants.ADD_ITEMS_LABEL}
          </Button>
        </div>
      ) : null}
      <CommonModal
        isModalPopupOpen={cicFieldsdata?.isUPCTypeModalOpen}
        showHideBtns={true}
        setModalPopupOpen={onModalPopUpReturnorClose}
        title={SELECT_UPC_TYPE_labels.MODAL_TITLE}
        infoMessage={SELECT_UPC_TYPE_labels.INFO_MESSAGE}
        confirmBtnTitle={SELECT_UPC_TYPE_labels.CONFIRM_BUTTON_TITLE}
        cancelBtnTitle={SELECT_UPC_TYPE_labels.CANCEL_BUTTON_TITLE}
        childComp={selectUPCTypeComp}
        isChildRender={true}
        height={360}
        onClose={onUPCTypeCloseHandler}
        extClassName={"flex items-center"}
        modalNameHandler={onConfirmHandler}
        isConfirmDisabled={!cicFieldsdata?.selectedUPCTypeCode}
        mixedTypeError={cicFieldsdata?.mixedUpcTypeErrorForModal}
        cancelBtnHandler={onUPCTypeCloseHandler}
      />
      <CICErrorModal
        isOpen={formFields?.isCICModalOpen}
        setOpen={() => null}
        errorTitle={`Items - ${cicFieldsdata?.invalidCICIdItems.length} error(s)`}
        height={360}
        onCloseHandler={onCloseCICModalHandler}
        cicErrorList={cicFieldsdata?.invalidCICIdItems}
      />
    </>
  );
}

export default memo(PpgCICField);
