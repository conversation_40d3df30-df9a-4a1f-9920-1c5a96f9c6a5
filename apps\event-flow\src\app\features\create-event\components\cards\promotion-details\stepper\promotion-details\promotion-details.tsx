/* eslint-disable @typescript-eslint/no-empty-function */
import Button from "@albertsons/uds/molecule/Button";
import Divider from "@albertsons/uds/molecule/Divider";
import { useState, useEffect, useCallback } from "react";
import {
  PROMOTION_DETAILS_FIELDS,
  PROMOTION_TYPES,
  SHOW_FIELDS,
  UOM_TYPES,
} from "../../../../../constants/promotion/types";
import { useFormContext, useWatch } from "react-hook-form";
import { useSelectorWrap } from "@me/data-rtk";
import { InputText, InputSelect } from "@me/input-fields";
import {
  RegularPriceCostDataProps,
  isAddPromoFn,
  promotionCancelHandler,
  promotionDatesHandler,
  promotionTypeHandler,
  regularPriceDetailDataHandler,
  setHiddenPriceValue,
  setPromoDetailsOldData,
  promotionHiddenPricingChecboxesStatusHandler,
} from "../../../../../service/slice/promotion-details-slice";
import {
  getListAgpValue,
  getPriceValue,
  getListValue,
} from "../../../../../service/promotion-details/promotion-details-service";
import { STORE_DATA_LOAD_FINISHED_STATUS } from "../../../../../constants/constants";
import LoadingSpinner from "../../../../../constants/LoadingSpinner/LoadingSpinner";
import PromotionDetailsForecast from "../../promotion-details-forecast";
import { RBAC } from "albertsons-react-rbac";
import {
  createSuggestedPriceText,
  editFieldHighlight,
  EEVENT_STATUS,
  EUSER_ROLES,
  roundToDecimalPlaces,
  SUGGESTED,
} from "@me/util-helpers";
import "../../../../edit-field-highlight.scss";
import { useDispatch } from "react-redux";
import { isAddingPromoConfig } from "../../../common/card-container-service";
import { isLastPromoStep } from "./promotion-details-service";
import usePromoSummaryForUOM from "./usePromoSummaryForUOM";
import { OverlappingAllowanceGrid } from "libs/features/expandable-table/src/lib/overlapping-allowance-grid";
import ViewAllPromoModal from "./view-all-promo-modal";
import TextArea from "@albertsons/uds/molecule/TextArea";
import { Alert } from "@albertsons/uds/molecule/Alert";
import { promoCardConfiguration } from "../../../../../service/slice/allowance-details-slice";
import { formatTimestampToDate } from "../../../../../service/allowance/allowance-service";
import { usePostRegularPriceCostDataMutation } from "../../../../../service/apis/promotion-api";
import {
  formatTimestamp,
  getLoggedInUserType,
  isVendorLoggedIn,
  warningIcon,
} from "@me-upp-js/utilities";
import { PROMO_DATE_FIELDS } from "../../../../../constants/promotion/cards/promotion-dates/fields";
import efConstants from "apps/event-flow/src/app/shared/ef-constants/ef-constants";
import WarningModal from "apps/event-flow/src/app/features/event-types/components/event-header/warning-modal";
import {
  checkForHiddenPrice,
  eventStatusForHiddenPricing,
  getIsChecked,
} from "../../promotion-details-helper";
import {
  PROMO_DETAILS_HIDDEN_PRICING_FEATURE,
  VEHICLE_TYPES_TO_SHOW_ADDETAILS,
} from "libs/utils/helpers/src/lib/constants/status-constants";
import HiddenPricingCheckbox from "./promo-details-hidden-price-checkbox";
import { useGetVendorAuthorizationsByVendorNumbersQuery } from "apps/event-flow/src/app/graphql/generated/schema";
import { appConstants } from "@me/utils-root-props";
import { handleKeyDown } from "apps/event-flow/src/app/shared/helpers/event-flow-helpers";

interface IPromotionDetailsProps {
  stepperElement?: any;
  saveAndContinueHandler?: any;
  savePromotionHandler?: any;
  // TODO removed after completion of saving promotion data
  setIsEdit?: any;
  cardIndex?: any;
  cardItemIndex?: any;
  isGetForecastLoading?: boolean;
  isForecastFetching?: boolean;
  saveAndGetForecastHandler: () => void;
  postPromoData?: Record<string, unknown>;
  putPromoData?: Record<string, unknown>;
  getForecastData?: any;
  setStep?: any;
  getForecastCallLimit?: any;
  isApiLoading?: any;
}

const PromotionDetails: React.FunctionComponent<IPromotionDetailsProps> = ({
  saveAndContinueHandler,
  cardItemIndex,
  cardIndex,
  stepperElement,
  savePromotionHandler,
  // TODO removed after completion of saving promotion data
  setIsEdit,
  isGetForecastLoading,
  isForecastFetching,
  getForecastCallLimit,
  postPromoData,
  putPromoData,
  getForecastData,
  saveAndGetForecastHandler,
  setStep,
  isApiLoading,
}) => {
  const { getValues, setValue, control, formState } = useFormContext();
  const priceData = useSelectorWrap("promotion_regular_price_data");
  const { data: isPromoCardOnLastStep } = useSelectorWrap("lastPromoStep_rn");
  const { data: isToggleOn } = useSelectorWrap("hidden_price_rn");
  const { data: promoHiddenPricingChecboxesStatus } = useSelectorWrap(
    "promotion_hidden_pricing_checkboxes_status_rn"
  );
  const { isEventCardOpen = false } = useSelectorWrap(
    "is_event_edit_enable"
  ).data;
  const {
    data: { isCancelled },
  } = useSelectorWrap("promotion_cancel_rn");
  const { data: { isEditPromotion: { isEdit = {} } = {} } = {} } =
    useSelectorWrap("promotion_edit_enable_configutation_rn") || {};
  const priceCostData: RegularPriceCostDataProps = priceData?.data;

  const { data: promoOverlapsSliceData } = useSelectorWrap(
    "promotion_overlaps_rn"
  );
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");

  const isPriceDataAvailable =
    priceData?.status === STORE_DATA_LOAD_FINISHED_STATUS;

  const {
    fields: {
      promotionType,
      factor,
      amount,
      uom,
      itemLimit,
      minQuantity,
      comments,
    },
  } = stepperElement;
  const {
    NEW_DIRECTION_FEATURES_FLAGS: {
      isDirectionalChangesEnable,
      ONE_PROMOTION: { disableVehicleStepper },
    },
  } = efConstants;
  const { hiddenPricingFeatureEnabled, CHECKBOXES_LIST } =
    PROMO_DETAILS_HIDDEN_PRICING_FEATURE;

  const { startWeekOrVehicle, vehicleTypeOrCustomDate } = PROMO_DATE_FIELDS;

  promotionType.options = Object.keys(PROMOTION_TYPES).map(key => {
    return { id: key, name: PROMOTION_TYPES[key] };
  });

  uom.options = Object.keys(UOM_TYPES).map(key => {
    return { id: UOM_TYPES[key], name: UOM_TYPES[key] };
  });

  const vendorsInfo = localStorage.getItem("vendorNumList");
  const parsedvendorsInfo = JSON.parse(vendorsInfo || "[]");
  const { data: vendorsData } = useGetVendorAuthorizationsByVendorNumbersQuery({
    variables: { vendorNumbers: parsedvendorsInfo },
    skip: !eventDetailsData?.id || !isVendorLoggedIn(),
  });
  function checkZeroCostInd(vendorsData, smicIdsData) {
    if (!vendorsData) return false;
    for (const item of vendorsData) {
      if (!item?.zeroCostInd) {
        const matchingAuth = item?.smicAuths?.find(auth =>
          smicIdsData?.some(smicId => auth?.smics?.includes(smicId))
        );
        if (matchingAuth) {
          return matchingAuth?.zeroCostInd;
        }
      }
    }
    return false;
  }

  const smicIds = [
    ...new Set(
      eventDetailsData?.planProductGroups?.map(item => item?.smicCategoryCode)
    ),
  ];
  const vendorAuthorizations =
    vendorsData?.getVendorAuthorizationsByVendorNumbers;

  const isZeroCostCheck =
    vendorAuthorizations?.reduce((acc, item) => {
      return acc || item?.zeroCostInd === true;
    }, false) || false;
  const isZeroCost = !isZeroCostCheck
    ? checkZeroCostInd(vendorAuthorizations, smicIds)
    : isZeroCostCheck;
  const promotionRegField = `promotionsLists[${cardIndex}].promotionsList[${cardItemIndex}]`;
  const startWeekRegisterField = `${promotionRegField}.${startWeekOrVehicle.registerField}`;
  const vehicleTypeDynRegisterField = `${promotionRegField}.${startWeekOrVehicle.registerField}.${vehicleTypeOrCustomDate.registerField}`;
  const eventVehicle = getValues("eventCreationVehicle");
  if (isDirectionalChangesEnable) {
    setValue(`${promotionRegField}.vehicle`, eventVehicle);
    setValue(`${promotionRegField}.promoStartDate`, eventVehicle?.startDate);
    setValue(`${promotionRegField}.promoEndDate`, eventVehicle?.endDate);
  }

  const [vehicleTypeProps, setCustomVehicleType] = useState(
    getValues(`${vehicleTypeDynRegisterField}`)
  );
  const isCustomDate = ["Custom Date", "CustomDate"].includes(
    vehicleTypeProps?.vehicleTypDesc || vehicleTypeProps?.name
  );

  const { id, divisionIds, promotionsList, negotiationSimsVendors } =
    getValues();

  useEffect(() => {
    dispatch(
      setPromoDetailsOldData(
        eventDetailsData?.promotionsList?.[`${cardItemIndex}`]?.promoDetails
      )
    );
  }, []);

  const [customStartDate, setCustomStartDate] = useState<string | number>(
    isCustomDate
      ? formatTimestamp({
          timestamp: getValues(`${promotionRegField}.promoStartDate`),
          isFormat: true,
        })
      : formatTimestamp({
          timestamp: getValues(`${startWeekRegisterField}.startDate`) || "",
          isFormat: true,
        })
  );

  useEffect(() => {
    if (hiddenPricingFeatureEnabled) {
      const promoValues =
        eventDetailsData?.promotionsList?.[`${cardItemIndex}`];
      dispatch(
        promotionHiddenPricingChecboxesStatusHandler({
          ...promoHiddenPricingChecboxesStatus,
          doNotShowPromoPrice: checkForHiddenPrice(promoValues),
          adDetails: promoValues?.adDetails,
          doNotPrice: promoValues?.doNotPrice,
          splitBIB: promoValues?.splitBIB,
        })
      );

      if (
        loggedInUser === EUSER_ROLES?.MERCHANT &&
        checkForHiddenPrice(promoValues)
      ) {
        const { promoDetails, ...newPromoValues } = promoValues;
        newPromoValues["promoDetails"] = { ...newPromoValues.hiddenPrice };
        setValue(
          `${promotionRegField}.promoDetails`,
          newPromoValues?.promoDetails
        );
      }
    }
  }, [eventDetailsData?.promotionsList]);

  const promoOverlapsWarningMessage =
    'There are Promotions planned or active for "Promo Product Group" in "Store Group" for selected period.';

  const dynamicRegisterFieldPrefix = `promotionsLists[${cardIndex}].promotionsList[${cardItemIndex}].promoDetails`;
  const promoDynRefFields = {
    promoTypeDynRegField: `${dynamicRegisterFieldPrefix}.${promotionType.registerField}`,
    amountDynRegField: `${dynamicRegisterFieldPrefix}.${amount.registerField}`,
    uomDynRegField: `${dynamicRegisterFieldPrefix}.${uom.registerField}`,
    factorDynRegField: `${dynamicRegisterFieldPrefix}.${factor.registerField}`,
    itemLimitDynRegField: `${dynamicRegisterFieldPrefix}.${itemLimit.registerField}`,
    minQuantityDynRegField: `${dynamicRegisterFieldPrefix}.${minQuantity.registerField}`,
    promoCommentDynRegField: `${dynamicRegisterFieldPrefix}.${comments?.registerField}`,
  };
  const promoIdField = `promotionsLists[${cardIndex}].promotionsList[${cardItemIndex}].promotionId`;
  const promoTypeDynRegField = `${dynamicRegisterFieldPrefix}.${promotionType.registerField}`;
  const amountDynRegField = `${dynamicRegisterFieldPrefix}.${amount.registerField}`;
  const uomDynRegField = `${dynamicRegisterFieldPrefix}.${uom.registerField}`;
  const promoCommentDynRegField = `${dynamicRegisterFieldPrefix}.${comments?.registerField}`;
  const factorDynRegField = `${dynamicRegisterFieldPrefix}.${factor.registerField}`;
  const itemLimitDynRegField = `${dynamicRegisterFieldPrefix}.${itemLimit.registerField}`;
  const minQuantityDynRegField = `${dynamicRegisterFieldPrefix}.${minQuantity.registerField}`;
  const regularPriceReqField = `${dynamicRegisterFieldPrefix}.regularPrice`;
  const listCostReqField = `${dynamicRegisterFieldPrefix}.listCost`;
  const listAgpReqField = `${dynamicRegisterFieldPrefix}.listAgp`;
  const promoForecast = `promotionsLists[${cardIndex}].promotionsList[${cardItemIndex}].forecast`;
  const promoStatus = getValues(
    `promotionsLists[${cardIndex}].promotionsList[${cardItemIndex}].promotionWorkflowStatus`
  );
  const eventStatus = getValues("eventStatus");

  const watchAll = useWatch({
    name: [
      factorDynRegField,
      amountDynRegField,
      uomDynRegField,
      promoTypeDynRegField,
      minQuantityDynRegField,
      // promoCommentDynRegField,
    ],
  });

  const watchItemLimit = useWatch({ name: [itemLimitDynRegField] });

  const [isDisabled, setIsDisabled] = useState(true);
  const [isMinQuantityErr, setIsMinQuantityErr] = useState(false);
  const [isFactorErr, setIsFactorErr] = useState(false);
  const [isItemLimitErr, setIsItemLimitErr] = useState(false);
  const [isAmountErr, setIsAmountErr] = useState(false);
  const [isAmountFieldDirty, setIsAmountFieldDirty] = useState(false);
  const [currentField, setCurrentField] = useState("");
  const loggedInUser = getLoggedInUserType();
  const [isWarning, setIsWarning] = useState(false);
  const [isItemWarning, setIsItemWarning] = useState(false);

  const [errorFieldMessagesObj, setErrorFieldMessagesObj] = useState({
    factorErrMessage: "",
    itemLimitErrMessage: "",
    amountErrMessage: "",
    minQuantityErrMessage: "",
    itemWarning:
      "If either limit or quanity is > 9, the Limit must be equal to 1",
  });
  const [isPromoTypeSelected, setIsPromoTypeSelected] = useState(
    getValues(promoTypeDynRegField) || false
  );

  const [isNetPriceSelected, setIsNetPriceSelected] = useState(false);
  const [isBOGOSelected, setIsBOGOSelected] = useState(false);
  const [factorDefValue, setFactorDefValue] = useState(
    getValues(factorDynRegField) || ""
  );
  const [amountDefValue, setAmountDefValue] = useState(
    getValues(amountDynRegField) || ""
  );

  const [itemLimitDefValue, setItemLimitDefValue] = useState(
    getValues(itemLimitDynRegField) || 0
  );
  const [minQDefValue, setMinQDefValue] = useState(
    getValues(minQuantityDynRegField) || ""
  );
  const [promotionIdField, setPromotionIdFieldValue] = useState(
    getValues(promoIdField) || ""
  );
  const [commentsDefValue, setCommentsDefValue] = useState(
    getValues(promoCommentDynRegField) || ""
  );
  const [promotTypeFields, setPromotTypeFields] = useState<{
    [key: string]: any;
  }>({});

  const [periscopeId, setPeriscopeId] = useState(
    getValues("periscopeDetails[0].periscopeId")
  );
  const [promoOverlapsModalOpen, setPromoOverlapsModalOpen] = useState(false);
  const { defaultUomValue, isLoading, data, planEventResponse } =
    usePromoSummaryForUOM({
      cardIndex,
      cardItemIndex,
    });

  const [promoDetailsFields, setPromoDetailsFeilds] = useState({
    hideInternalPricingChange: false,
    promoTypeChange: false,
    factorChange: false,
    amountChange: false,
    itemLimitChange: false,
    minQuantityChange: false,
    uomChange: false,
    promoCommentChange: false,
  });
  const [getRegularPriceCostData] = usePostRegularPriceCostDataMutation();
  const [formTouched, setFormTouched] = useState(false);
  useEffect(() => {
    setFormTouched(Object.keys(formState?.touchedFields).length > 0);
  }, [formState, control, getValues, promotionType]);

  const handleGetRegularPrice = () => {
    getRegularPriceCostData({
      URL_PARAM: id,
      queryParams: {},
      division: divisionIds,
      promoStartDate: formatTimestampToDate(customStartDate),
    }).then(
      (priceData: {
        data: RegularPriceCostDataProps;
        error?: { status: string; error: string };
      }) => {
        if (!priceData?.error) {
          dispatch(regularPriceDetailDataHandler(priceData?.data));
          !isDirectionalChangesEnable &&
            !disableVehicleStepper &&
            dispatch(promotionDatesHandler({ isDatesUpdated: true }));
        } else {
          dispatch(
            regularPriceDetailDataHandler({
              minRegularPrice: 0,
              maxRegularPrice: 0,
              maxListCost: 0,
              minListCost: 0,
              maxListAGP: 0,
              minListAGP: 0,
            })
          );
          dispatch(promotionDatesHandler({ isDatesUpdated: false }));
        }
      }
    );
    // to signal that we are about to be on the last step of promo flow, we update store
    if (getValues("promotionsList")?.length > 0) {
      // only allow cancel when atleast one promo exists
      dispatch(isLastPromoStep(true));
    }
  };

  const numberRegexPattern = /^[0-9]+$/;
  const numberOnlyRegexPattern = /^\d+$/;
  const decimalRegexPattern = /^[0-9]*(\.[0-9]{0,2})?$/;

  const validateFieldValueWithPattern = (
    pattern: any,
    fieldName: string,
    inputValue: string
  ) => {
    if (!inputValue) {
      setValue(fieldName, "");
    } else if (pattern.test(inputValue)) {
      setValue(fieldName, inputValue);
      onChangeFieldHighlight(fieldName);
    }
  };

  // setValue(minQuantityDynRegField, minQuantityDefaultValue);
  // setValue(factorDynRegField, factorDefaultValue);
  const onPromoTypeChange = ele => {
    setIsMinQuantityErr(false);
    setIsAmountFieldDirty(false);
    setIsAmountErr(false);
    setIsWarning(false);
    setIsItemWarning(false);
    setIsItemLimitErr(false);
    setIsFactorErr(false);
    setCurrentField("");

    setErrorMessages(ele?.id);
    setFormTouched(true);

    if (
      getValues(promoIdField) &&
      eventStatus !== EEVENT_STATUS.DRAFT &&
      promoStatus !== EEVENT_STATUS.DRAFT
    ) {
      setPromoDetailsFeilds({
        ...promoDetailsFields,
        factorChange: false,
        amountChange: false,
        itemLimitChange: false,
        minQuantityChange: false,
        uomChange: false,
        promoCommentChange: false,
        promoTypeChange: true,
      });
    }
    setPromotTypeFields(SHOW_FIELDS[ele.id].fields);
    modifyPromoDetailsObj(SHOW_FIELDS[ele.id].fields);
    setIsPromoTypeSelected(true);
    setValue(promoTypeDynRegField, ele?.id);
    dispatch(
      promotionTypeHandler({
        promoType: ele?.id,
      })
    );
    if (priceData) {
      setValue(regularPriceReqField, priceCostData?.maxRegularPrice);
      setValue(listCostReqField, priceCostData?.maxListCost);
      setValue(listAgpReqField, priceCostData?.maxListAGP);
    }

    const uomValue = getValues(uomDynRegField);
    defaultUomValue && !uomValue && setValue(uomDynRegField, defaultUomValue);
  };

  const setErrorMessages = promoType => {
    const fieldsErrorMessages = {
      factorErrMessage: SHOW_FIELDS?.[promoType]?.fields?.factor?.errorMessage,
      itemLimitErrMessage:
        SHOW_FIELDS?.[promoType]?.fields?.itemLimit?.errorMessage,
      amountErrMessage: SHOW_FIELDS?.[promoType]?.fields?.amount?.errorMessage,
      minQuantityErrMessage:
        SHOW_FIELDS?.[promoType]?.fields?.minQuantity?.errorMessage,
    };
    setErrorFieldMessagesObj({
      ...errorFieldMessagesObj,
      ...fieldsErrorMessages,
    });
  };

  const modifyPromoDetailsObj = field => {
    Object.keys(field).forEach(ele => {
      setValue(
        promoDynRefFields[field[ele]?.dynamicRegisterKeyName],
        field[ele]?.defaultValue
      );
    });
  };

  useEffect(() => {
    const promotionType = getValues(promoTypeDynRegField);
    dispatch(
      promotionTypeHandler({
        promoType: promotionType,
      })
    );
    promotionType && setPromotTypeFields(SHOW_FIELDS[promotionType]?.fields);
  }, []);

  useEffect(() => {
    // invoke regular price api call only if planEventResponse is available
    isDirectionalChangesEnable &&
      planEventResponse?.length &&
      handleGetRegularPrice();
  }, [JSON.stringify(planEventResponse)]);

  useEffect(() => {
    validatePromoDetailsFields();
  }, [watchAll]);

  useEffect(() => {
    if (getValues(itemLimitDynRegField) !== 0) validatePromoDetailsFields();
  }, [watchItemLimit]);

  useEffect(() => {
    if (isCancelled) {
      handleCancelClick();
      dispatch(promotionCancelHandler({ isCancelled: false }));
    }
  }, [isCancelled]);

  useEffect(() => {
    setValue(regularPriceReqField, priceCostData?.maxRegularPrice);
    setValue(listCostReqField, priceCostData?.maxListCost);
    setValue(listAgpReqField, priceCostData?.maxListAGP);
  }, [priceData]);

  useEffect(() => {
    const uomValue = getValues(uomDynRegField);

    defaultUomValue && !uomValue && setValue(uomDynRegField, defaultUomValue);
  }, [defaultUomValue]);

  const validatePromoDetailsFields = () => {
    //Validating Promo Details fields to Enable/Disable Create Promotion button.
    const promotionType = getValues(promoTypeDynRegField);
    if (!promotionType) return;
    const promotionTypeFields = SHOW_FIELDS[promotionType].fields;
    const keys = Object.keys(promotionTypeFields).filter(prop => {
      return promotionTypeFields[prop].show;
    });
    if (PROMOTION_TYPES[promotionType] !== PROMOTION_TYPES.UN_SUPPORTED)
      fieldsValidation(promotionType, keys, promotionTypeFields);
    else if (PROMOTION_TYPES[promotionType] === PROMOTION_TYPES.UN_SUPPORTED) {
      const ele = [getValues(promoCommentDynRegField)].filter(ele => !ele);
      ele && setIsDisabled(ele?.length > 0);
    } else {
      const ele = watchAll?.filter(
        ele => ele === undefined || ele === "" || ele === "."
      );
      const fieldValidationCheck =
        (ele && ele.length > 0) || watchAll.includes("UN_SUPPORTED");
      setIsDisabled(fieldValidationCheck);
    }
  };

  const checkPromoType = (types, promotionType) => {
    return types.includes(promotionType);
  };

  const fieldsValidation = (promotionType, fieldKeys, promotionTypeFields) => {
    setIsItemWarning(false);

    if (
      promotionTypeFields?.factor?.show &&
      currentField === PROMOTION_DETAILS_FIELDS.FACTOR
    ) {
      const factor = getValues(factorDynRegField);
      const isFactorValid =
        factor >= promotionTypeFields?.factor.minValue &&
        factor < promotionTypeFields?.factor.maxValue;
      setIsFactorErr(!isFactorValid);
    }
    const itemLimit = getValues(itemLimitDynRegField);
    const minQuantity = getValues(minQuantityDynRegField);
    // If ( Limit per txn > 9 AND min purchase > 1 ) OR    ( min purchase > 9 AND Limit per txn > 1 ) OR
    // ( min purchase > 9 AND Limit per txn = 0 or SPACES ) And this is for all methods, net, percentage off, cents off and BXG1.
    // then display the below warning message above the Update Promotion or Create promotion button
    if (
      checkPromoType(["NET_PRICE", "BUY_X_GET_ONE"], promotionType) &&
      ((itemLimit > 9 && minQuantity > 1) ||
        (minQuantity > 9 &&
          (itemLimit > 1 || itemLimit === 0 || itemLimit === "")))
    ) {
      setIsItemWarning(true);
    }
    if (
      promotionTypeFields?.itemLimit?.show &&
      currentField === PROMOTION_DETAILS_FIELDS.ITEM_LIMIT
    ) {
      const itemLimit = getValues(itemLimitDynRegField);
      const isItemLimitValid =
        itemLimit === undefined ||
        itemLimit === "" ||
        (itemLimit >= promotionTypeFields?.itemLimit?.minValue &&
          itemLimit <= promotionTypeFields?.itemLimit?.maxValue);
      setIsItemLimitErr(!isItemLimitValid);
    }
    if (
      promotionTypeFields?.amount?.show &&
      currentField === PROMOTION_DETAILS_FIELDS.AMOUNT
    ) {
      const amount = getValues(amountDynRegField)
        ? Number(getValues(amountDynRegField))
        : parseInt(getValues(amountDynRegField));
      if (amount === 0)
        setErrorFieldMessagesObj({
          ...errorFieldMessagesObj,
          amountErrMessage: promotionTypeFields?.amount?.emptyOrZeroErrMessage,
        });
      else if (isNaN(amount)) {
        setErrorFieldMessagesObj({
          ...errorFieldMessagesObj,
          amountErrMessage: promotionTypeFields?.amount?.emptyErrMessage,
        });
      } else
        setErrorFieldMessagesObj({
          ...errorFieldMessagesObj,
          amountErrMessage: promotionTypeFields?.amount?.errorMessage,
        });
      const { showWarning, isAmountFieldDirty, isAmountErr } =
        validatePromotionAmount({
          amount,
          promotionType,
          promotionTypeFields,
          priceCostData,
          factorDynRegField,
          currentField,
          getValues,
        });

      setIsWarning(showWarning);
      setIsAmountFieldDirty(isAmountFieldDirty);
      setIsAmountErr(isAmountErr);
    }

    if (
      promotionTypeFields?.minQuantity?.show &&
      currentField === PROMOTION_DETAILS_FIELDS.MIN_QUANTITY
    ) {
      const minQuantity = getValues(minQuantityDynRegField);
      if (
        !minQuantity ||
        +minQuantity < promotionTypeFields?.minQuantity?.minValue
      )
        setErrorFieldMessagesObj({
          ...errorFieldMessagesObj,
          minQuantityErrMessage: promotionTypeFields?.minQuantity?.errorMessage,
        });
      const isMinQuantityValid =
        minQuantity >= promotionTypeFields?.minQuantity?.minValue &&
        minQuantity < promotionTypeFields?.minQuantity?.maxValue;
      setIsMinQuantityErr(!isMinQuantityValid);
    }
    return;
  };

  const validatePromotionAmount = ({
    amount,
    promotionType,
    promotionTypeFields,
    priceCostData,
    factorDynRegField,
    currentField,
    getValues,
  }) => {
    const amountMax =
      promotionTypeFields?.amount?.maxValue || priceCostData?.maxRegularPrice;
    const amountWithFactor = amount / getValues(factorDynRegField);
    const isValidAmount = amountWithFactor <= amountMax;

    const isAmountGreaterThanMinValue =
      amount > promotionTypeFields?.amount?.minValue;

    const isValidNetPricePromotion =
      isAmountGreaterThanMinValue &&
      (checkPromoType(["NET_PRICE"], promotionType) ? isValidAmount : true);

    const isAmountValid =
      isAmountGreaterThanMinValue &&
      (checkPromoType(["PERCENT_OFF", "CENT_OFF"], promotionType)
        ? amount <= amountMax
        : true);

    const showWarning =
      (checkPromoType(["NET_PRICE"], promotionType) &&
        !isValidNetPricePromotion) ||
      (checkPromoType(["PERCENT_OFF", "CENT_OFF"], promotionType) &&
        !isAmountValid);

    const isAmountInvalid =
      currentField === PROMOTION_DETAILS_FIELDS.AMOUNT &&
      (isNaN(amount) || amount === 0);

    return {
      showWarning,
      isAmountInvalid,
      isAmountFieldDirty: isAmountInvalid,
      isAmountErr: isAmountInvalid,
    };
  };

  useEffect(() => {
    const isFormValid = [
      isAmountErr,
      isItemLimitErr,
      isFactorErr,
      isMinQuantityErr,
    ].every(item => !item);
    let ele;
    if (
      checkPromoType(
        ["BUY_X_GET_ONE", "BUY_ONE_GET_ONE"],
        getValues(promoTypeDynRegField)
      )
    ) {
      ele = watchItemLimit?.filter(ele => (ele === undefined || ele) === ".");
    } else {
      ele = watchAll?.filter(
        ele => ele === undefined || ele === "" || ele === "."
      );
    }
    setIsDisabled(!isFormValid || (ele && ele.length > 0));
  }, [
    isAmountErr,
    isItemLimitErr,
    isFactorErr,
    isMinQuantityErr,
    currentField,
    getValues(promoTypeDynRegField),
    JSON.stringify(errorFieldMessagesObj),
  ]);

  const onUOMChange = ele => {
    setCurrentField(PROMOTION_DETAILS_FIELDS.UOM);
    setValue(uomDynRegField, ele?.id);
    if (
      getValues(promoIdField) &&
      eventStatus !== EEVENT_STATUS.DRAFT &&
      promoStatus !== EEVENT_STATUS.DRAFT
    ) {
      setPromoDetailsFeilds({
        ...promoDetailsFields,
        uomChange: true,
      });
    }
  };

  const promoDetailsCommentChange = ele => {
    !ele?.target?.value?.trim().length
      ? setIsDisabled(true)
      : setIsDisabled(false);
    setValue(promoCommentDynRegField, ele?.target?.value);
    setPromoDetailsFeilds({
      ...promoDetailsFields,
      promoCommentChange: true,
    });
  };

  const onMinQuantityChange = ele => {
    setCurrentField(PROMOTION_DETAILS_FIELDS.MIN_QUANTITY);
    validateFieldValueWithPattern(
      numberOnlyRegexPattern,
      minQuantityDynRegField,
      ele?.target?.value
    );
  };

  const onAmountChangeHandler = ele => {
    setCurrentField(PROMOTION_DETAILS_FIELDS.AMOUNT);
    validateFieldValueWithPattern(
      decimalRegexPattern,
      amountDynRegField,
      ele?.target?.value
    );
  };

  const onFactorChangeHandler = ele => {
    setCurrentField(PROMOTION_DETAILS_FIELDS.FACTOR);
    validateFieldValueWithPattern(
      numberOnlyRegexPattern,
      factorDynRegField,
      ele?.target?.value
    );
  };

  const itemOnChangeHandler = ele => {
    setCurrentField(PROMOTION_DETAILS_FIELDS.ITEM_LIMIT);
    validateFieldValueWithPattern(
      numberOnlyRegexPattern,
      itemLimitDynRegField,
      ele?.target?.value
    );
  };

  const handleClick = () => {
    if (promoOverlapsSliceData?.isPromotionOverlapsDataAvailable) {
      setPromoOverlapsModalOpen(true);
    } else {
      setValue("isPromoChanged", false);
      handleCreatePromotion();
    }
  };
  const handleCreatePromotion = () => {
    setValue("isPromoChanged", false);
    savePromotionHandler();
    setPromoOverlapsModalOpen(false);
    // setIsEdit(false);
  };
  const handleModalClose = () => {
    setPromoOverlapsModalOpen(false);
    saveAndContinueHandler(3);
  };

  const dispatch = useDispatch();
  const handleCancelClick = () => {
    setIsEdit(false);
    dispatch(
      promoCardConfiguration({
        isAddNewPromo: { 0: false },
      })
    );
    dispatch(
      isAddPromoFn({
        isAddPromo: false,
      })
    );
    dispatch(promotionDatesHandler({ isDatesUpdated: false }));
    setStep();
    // on clicking cancel on this last step, reset the redux states to allow
    // correct rendering on card-container component view
    dispatch(isAddingPromoConfig(false));
    dispatch(isLastPromoStep(false));
  };

  useEffect(() => {
    setValue(
      factorDynRegField,
      getValues(factorDynRegField) || factor.defaultValue
    );
    setValue(
      minQuantityDynRegField,
      getValues(minQuantityDynRegField) || minQuantity.defaultValue
    );
  }, []);

  useEffect(() => {
    !getValues(promoTypeDynRegField) && setIsPromoTypeSelected(false);
  }, [getValues(promoTypeDynRegField)]);

  const regularPrice = getPriceValue(
    priceCostData?.minRegularPrice,
    priceCostData?.maxRegularPrice
  );

  const listPrice = getListValue(
    data?.maxUnitCostAllowItem?.unitNetCosts?.cost
  );

  const listAGP = getListAgpValue(
    priceCostData?.minListAGP,
    priceCostData?.maxListAGP
  );

  const netCost = getListValue(
    data?.maxUnitCostAllowItem?.unitNetCosts?.costAllow
  );
  const getUOMField = useCallback(() => {
    return promotTypeFields?.["uom"]?.show ? (
      <div
        id="promotion-details-container1"
        className={`basis-[10.7%] ${
          promoDetailsFields?.uomChange
            ? borderBlueLight
            : promoDetailsFields?.promoTypeChange
            ? "promo-change"
            : editFieldHighlight(
                uom.mapperKey,
                getValues,
                searchId,
                "promotion"
              )
        }`}
      >
        {uom ? (
          <InputSelect
            onChange={onUOMChange}
            disabled={true}
            fieldProps={uom}
            dynamicRegisterField={uomDynRegField}
          />
        ) : null}
      </div>
    ) : null;
  }, [promotTypeFields, promoDetailsFields]);

  const promotionId = postPromoData?.["id"] || putPromoData?.["id"];
  const searchId = getValues(promoIdField);

  const borderBlueLight = `borderBlueLight`;
  const onChangeFieldHighlight = (field: string) => {
    // if promotion id is available, that means promotion is in edit mode, highlight the field on change
    if (
      getValues(promoIdField) &&
      eventStatus !== EEVENT_STATUS.DRAFT &&
      promoStatus !== EEVENT_STATUS.DRAFT
    ) {
      const fieldPath = field?.split(".");
      const fieldLen = fieldPath?.length;
      const fieldName = fieldPath[fieldLen - 1];
      setPromoDetailsFeilds({
        ...promoDetailsFields,
        [`${fieldName}Change`]: true,
      });
    }
  };
  const handlePromoCheckboxesChange = (e, ele) => {
    dispatch(
      promotionHiddenPricingChecboxesStatusHandler({
        ...promoHiddenPricingChecboxesStatus,
        [ele.keyMapper]: e.target.checked,
      })
    );
  };

  const renderOverlapsData = () => {
    return (
      <>
        {data?.offerAllowanceOverlapResults?.offerAllowanceOverlaps && (
          <OverlappingAllowanceGrid
            quickEntryData={
              data?.offerAllowanceOverlapResults?.offerAllowanceOverlaps
            }
            isCase={false}
          />
        )}
        <div
          id="promotion-details-container2"
          className="flex-grow-0 flex-shrink-0 w-full mt-4 h-px  bg-[#fdfdff] border-t border-r border-b-0 border-l border-[#c8daeb]"
        ></div>
      </>
    );
  };
  const hidePricingHandler = element => {
    dispatch(setHiddenPriceValue({ hiddenToggleStatus: element }));
    setPromoDetailsFeilds({
      ...promoDetailsFields,
      hideInternalPricingChange: true,
    });
  };

  const noForecastData = () => {
    const noForecastDataTitle = "No Forecast information.";
    const noForecastDataMsg =
      "Forecast information is not available, please try after sometime.";
    return (
      <>
        <div
          id="promotion-details-container3"
          className="flex-grow-0 flex-shrink-0 w-full mb-4 h-px  bg-[#fdfdff] border-t border-r border-b-0 border-l border-[#c8daeb]"
        ></div>
        <Alert
          isOpen={true}
          variant="informational"
          className="!justify-center w-full"
        >
          <div
            className="font-bold text-base text-[#1B6EBB]"
            id="promotion-details-container4"
          >
            {noForecastDataTitle}
          </div>
          <div
            id="promotion-details-container5"
            className="text-[#1B6EBB] font-normal"
          >
            {noForecastDataMsg}
          </div>
        </Alert>
      </>
    );
  };
  const value_fields = {
    NET_PRICE: amountDynRegField,
    PERCENT_OFF: amountDynRegField,
    CENT_OFF: amountDynRegField,
    BUY_X_GET_ONE: itemLimitDynRegField,
    BUY_ONE_GET_ONE: itemLimitDynRegField,
    UN_SUPPORTED: promoCommentDynRegField,
  };

  const getSuggestedPrice = () => {
    const suggestedText = createSuggestedPriceText(
      getValues(value_fields[getValues(promoTypeDynRegField)]),
      getValues(promoTypeDynRegField)
    );

    return (
      <div className="px-3 p-1.5 text-sm text-light pl-5 w-full my-4 bg-[#F7F8FA] rounded">
        <p>
          {SUGGESTED} {suggestedText}
        </p>
      </div>
    );
  };

  const handleKeyDownForAmount = e => {
    return ["e", "E", "+", "-"].includes(e?.key) && e?.preventDefault();
  };
  const errorMessages = ["Promo amount", "Cents off", "Percentage should"];
  const hasError = errorMessages?.some(msg =>
    errorFieldMessagesObj?.amountErrMessage?.includes(msg)
  );
  const currentPromotion =
    eventDetailsData?.promotionsList?.[`${cardItemIndex}`];

  const [isViewAllPromoModalOpen, setIsViewAllPromoModalOpen] =
    useState<boolean>(false);
  return (
    <>
      <LoadingSpinner
        isLoading={!isPriceDataAvailable || isLoading || isApiLoading}
        classname="!w-full !h-full"
      />
      <div id="promotion-details-container6">
        {hiddenPricingFeatureEnabled &&
        loggedInUser === EUSER_ROLES.MERCHANT &&
        eventStatusForHiddenPricing?.includes(eventStatus) ? (
          <>
            <div className="flex justify-start align-center mb-5">
              {CHECKBOXES_LIST.map(ele => {
                return (
                  <HiddenPricingCheckbox
                    label={ele.label}
                    key={ele.keyMapper}
                    keyMapper={ele.keyMapper}
                    initialChecked={getIsChecked(
                      ele.keyMapper,
                      currentPromotion
                    )}
                    onChange={e => handlePromoCheckboxesChange(e, ele)}
                    className="mr-5"
                    currentPromotion={currentPromotion}
                  />
                );
              })}
            </div>
            {promoHiddenPricingChecboxesStatus?.doNotShowPromoPrice &&
            getValues(promoTypeDynRegField)
              ? getSuggestedPrice()
              : null}
          </>
        ) : null}

        <div className="flex items-center flex-wrap flex-grow-0 flex-shrink-0 gap-4 mb-5">
          <div
            className="basis-[16%] flex flex-row"
            id="promotion-details-container7"
          >
            {promotionType ? (
              <>
                <div
                  id="promotion-details-container8"
                  className={`${
                    promoDetailsFields?.promoTypeChange
                      ? borderBlueLight
                      : editFieldHighlight(
                          promotionType.mapperKey,
                          getValues,
                          searchId,
                          "promotion"
                        )
                  } w-[216px]`}
                >
                  <InputSelect
                    onChange={onPromoTypeChange}
                    control={control}
                    fieldProps={promotionType}
                    dynamicRegisterField={promoTypeDynRegField}
                  />
                </div>
                <Divider
                  height={60}
                  className="inline-block ml-[16px]"
                  color="#C8DAEB"
                />
              </>
            ) : null}
          </div>
          <div className="basis-[13.5%]" id="promotion-details-container9">
            <p
              className="text-base font-bold text-left text-[#2b303c]"
              id="promotion-details-parag1"
            >
              Regular Price
            </p>
            <p
              id="promotion-details-parag2"
              className={`text-base font-bold ${
                regularPrice !== "-" ? "underline" : ""
              } text-left text-[#1b6ebb] mt-3`}
            >
              {isPriceDataAvailable && regularPrice}
            </p>
          </div>
          {!isZeroCost ? (
            <>
              <div className="basis-[13.5%]" id="promotion-details-container10">
                <p
                  id="promotion-details-parag3"
                  className="text-base font-bold text-left text-[#2b303c]"
                >
                  List Cost
                </p>
                <p
                  id="promotion-details-parag4"
                  className={`text-base font-bold ${
                    listPrice !== "-" ? "underline" : ""
                  } text-left text-[#1b6ebb] mt-3`}
                >
                  {listPrice}
                </p>
              </div>
              <div className="basis-[14%]" id="promotion-details-container11">
                <p
                  id="promotion-details-parag5"
                  className="text-base font-bold text-left text-[#2b303c]"
                >
                  List AGP
                </p>
                <p
                  id="promotion-details-parag6"
                  className={`text-base font-bold  ${
                    listAGP !== "-" ? "underline" : ""
                  }
            text-left text-[#033b69] mt-3`}
                >
                  {isPriceDataAvailable && listAGP}
                </p>
              </div>
            </>
          ) : null}

          <Divider height={60} className="inline-block" color="#C8DAEB" />

          <div
            className="flex items-center flex-grow-0 flex-shrink-0"
            id="promotion-details-container12"
          >
            <p
              id="promotion-details-parag6"
              className="flex-grow-0 flex-shrink-0 text-base font-semibold text-[#1b6ebb] cursor-pointer"
              onClick={() => {
                setIsViewAllPromoModalOpen(true);
              }}
            >
              View All Items
            </p>
          </div>
        </div>
      </div>
      {/* {isPromoTypeSelected ? renderOverlapsData() : null} */}
      <div
        id="promotion-details-container13"
        className="flex flex-grow-0 flex-shrink-0 justify-center w-full h-px  bg-[#fdfdff] border-t border-r border-b-0 border-l border-[#c8daeb] mb-5"
      ></div>
      {isPromoTypeSelected ? (
        <>
          {!promotTypeFields["comments"]?.show && (
            <div
              className="flex flex-wrap flex-grow-0 flex-shrink-0 gap-4 mb-6"
              id="promotion-details-container14"
            >
              {!isZeroCost ? (
                <div className="basis-[10%]" id="promotion-details-container15">
                  <p
                    className="text-base font-bold text-left text-[#2b303c]"
                    id="promotion-details-parag7"
                  >
                    Net Cost
                  </p>
                  <p
                    className="text-base font-bold text-left text-[#033b69] mt-3"
                    id="promotion-details-parag8"
                  >
                    {netCost}
                  </p>
                </div>
              ) : null}
              {promotTypeFields["factor"]?.show ? (
                <div
                  id="promotion-details-container16"
                  className={`basis-[16%] ${
                    !isFactorErr &&
                    (promoDetailsFields?.factorChange
                      ? borderBlueLight
                      : promoDetailsFields?.promoTypeChange
                      ? "promo-change"
                      : editFieldHighlight(
                          promotTypeFields["factor"].mapperKey,
                          getValues,
                          searchId,
                          "promotion"
                        ))
                  }`}
                >
                  {factor ? (
                    <InputText
                      onChange={onFactorChangeHandler}
                      type="number"
                      onKeyDown={handleKeyDown}
                      dynamicRegisterField={factorDynRegField}
                      fieldProps={factor}
                      builtInError={isFactorErr}
                      defaultValue={
                        factorDefValue ||
                        promotTypeFields?.["factor"].defaultValue
                      }
                      fieldErrorMessage={errorFieldMessagesObj.factorErrMessage}
                    />
                  ) : null}
                </div>
              ) : null}
              {promotTypeFields?.["amount"]?.show ? (
                <div
                  id="promotion-details-container17"
                  className={`basis-[13.5%] ${
                    !isAmountErr &&
                    (promoDetailsFields?.amountChange
                      ? borderBlueLight
                      : promoDetailsFields?.promoTypeChange
                      ? "promo-change"
                      : editFieldHighlight(
                          promotTypeFields["amount"].mapperKey,
                          getValues,
                          searchId,
                          "promotion"
                        ))
                  }`}
                >
                  {amount ? (
                    <InputText
                      onChange={onAmountChangeHandler}
                      type="number"
                      onKeyDown={handleKeyDownForAmount}
                      dynamicRegisterField={amountDynRegField}
                      fieldProps={amount}
                      prefixElement={promotTypeFields?.["amount"]?.prefix}
                      suffixElement={promotTypeFields?.["amount"]?.suffix}
                      defaultValue={roundToDecimalPlaces(amountDefValue, 2)}
                      isPromotionAmountField={true}
                      isRequired={isEventCardOpen}
                      builtInError={isAmountErr}
                      fieldErrorMessage={errorFieldMessagesObj.amountErrMessage}
                    />
                  ) : null}
                </div>
              ) : null}
              {getUOMField()}
              {promotTypeFields?.["itemLimit"]?.show ? (
                <div
                  id="promotion-details-container18"
                  className={`basis-[14%] ${
                    !isItemLimitErr &&
                    (promoDetailsFields?.itemLimitChange
                      ? borderBlueLight
                      : promoDetailsFields?.promoTypeChange
                      ? "promo-change"
                      : editFieldHighlight(
                          promotTypeFields["itemLimit"].mapperKey,
                          getValues,
                          searchId,
                          "promotion"
                        ))
                  }`}
                >
                  {itemLimit ? (
                    <InputText
                      type="number"
                      onChange={itemOnChangeHandler}
                      fieldProps={itemLimit}
                      onKeyDown={handleKeyDown}
                      dynamicRegisterField={itemLimitDynRegField}
                      defaultValue={itemLimitDefValue}
                      builtInError={isItemLimitErr}
                      fieldErrorMessage={
                        errorFieldMessagesObj.itemLimitErrMessage
                      }
                    />
                  ) : null}
                </div>
              ) : null}
              {promotTypeFields?.["minQuantity"]?.show ? (
                <div
                  id="promotion-details-container19"
                  className={`basis-[16.77%] ${
                    !isMinQuantityErr &&
                    (promoDetailsFields?.minQuantityChange
                      ? borderBlueLight
                      : promoDetailsFields?.promoTypeChange
                      ? "promo-change"
                      : editFieldHighlight(
                          promotTypeFields["minQuantity"].mapperKey,
                          getValues,
                          searchId,
                          "promotion"
                        ))
                  }`}
                >
                  {minQuantity ? (
                    <InputText
                      onChange={onMinQuantityChange}
                      type="number"
                      onKeyDown={handleKeyDown}
                      dynamicRegisterField={minQuantityDynRegField}
                      fieldProps={minQuantity}
                      defaultValue={
                        minQDefValue ||
                        promotTypeFields?.["minQuantity"].defaultValue
                      }
                      builtInError={isMinQuantityErr}
                      fieldErrorMessage={
                        errorFieldMessagesObj.minQuantityErrMessage
                      }
                    />
                  ) : null}
                </div>
              ) : null}
              {/* {promotTypeFields?.["viewALLItems"]?.show ? ( */}
              {/* <> */}

              {/* </> */}
              {/* ) : null} */}
            </div>
          )}
          {promotTypeFields["comments"]?.show ? (
            <div
              id="promotion-details-container20"
              className={`basis-[16%] ${
                promoDetailsFields?.promoCommentChange
                  ? borderBlueLight
                  : promoDetailsFields?.promoCommentChange
                  ? "promo-change"
                  : editFieldHighlight(
                      promotTypeFields["comments"].mapperKey,
                      getValues,
                      searchId,
                      "promotion"
                    )
              }`}
            >
              {comments ? (
                <TextArea
                  className="mt-10"
                  maxCharacters={300}
                  onChange={promoDetailsCommentChange}
                  value={getValues(promoCommentDynRegField)}
                  name="comments"
                  label="Add Promo Details"
                  maxLength={300}
                  isRequired
                />
              ) : null}
            </div>
          ) : null}
          {periscopeId ? (
            <div
              id="promotion-details-container21"
              className="flex items-center flex-grow-0 flex-shrink-0 gap-4 mb-6"
            >
              {/* <span>Calculate</span>
            <p className="flex-grow-0 flex-shrink-0 text-base font-semibold text-center text-[#1b6ebb] cursor-pointer">
              AGP
            </p>
            <Divider height={24} className="inline-block" color="#C8DAEB" /> */}
              <RBAC
                divisionIds={divisionIds}
                permissionsOnly={["PROMOTION_PROMO_MGMT_EDIT_GET_FORECAST"]}
                simsVendors={negotiationSimsVendors}
              >
                <Button
                  variant="tertiary"
                  size="md"
                  width={180}
                  onClick={() => {
                    saveAndGetForecastHandler();
                  }}
                  disabled={isDisabled || isGetForecastLoading}
                >
                  {efConstants.FORECAST_DISPLAY[loggedInUser]
                    ? isDirectionalChangesEnable
                      ? "Get Forecast"
                      : getValues(promoIdField) || promotionId
                      ? "Update & Get Forecast"
                      : "Save & Get Forecast"
                    : null}
                </Button>
              </RBAC>
            </div>
          ) : (
            <></>
          )}
        </>
      ) : null}
      <RBAC
        divisionIds={divisionIds}
        permissionsOnly={["PROMOTION_PROMO_MGMT_EDIT_GET_FORECAST"]}
        simsVendors={negotiationSimsVendors}
      >
        {(isPromoTypeSelected && Object.keys(getForecastData)?.length) ||
        isGetForecastLoading ? (
          <PromotionDetailsForecast
            isLoading={isGetForecastLoading}
            cardIndex={cardIndex}
            cardItemIndex={cardItemIndex}
            getForecastData={getForecastData}
          />
        ) : null}
      </RBAC>
      {/* <div className="flex-grow-0 flex-shrink-0 w-full mt-4 h-px  bg-[#fdfdff] border-t border-r border-b-0 border-l border-[#c8daeb]"></div> */}
      {isWarning && hasError ? (
        <div className="flex gap-3 text-amber-700">
          <div>{warningIcon}</div>
          <span>{errorFieldMessagesObj.amountErrMessage}</span>
        </div>
      ) : null}
      {isItemWarning && errorFieldMessagesObj.itemWarning ? (
        <div className="flex gap-3 text-amber-700">
          <div>{warningIcon}</div>
          <span>{errorFieldMessagesObj.itemWarning}</span>
        </div>
      ) : null}
      {!getForecastData && isForecastFetching && getForecastCallLimit === 5
        ? noForecastData()
        : null}
      <div
        id="promotion-details-container22"
        className="flex items-center flex-wrap flex-grow-0 flex-shrink-0 gap-4 mb-5 mt-4"
      >
        <Button
          width={getValues(promoIdField) ? 270 : 180}
          onClick={(event: any) => {
            event?.preventDefault();
            event?.stopPropagation();
            handleClick();
          }}
          disabled={!!(isDisabled || isItemWarning || !formTouched)}
        >
          {`${
            getValues(promoIdField) ? "Update Promotion" : "Create Promotion"
          }`}
        </Button>
        {/* {getValues("promotions")?.length &&
        isDirectionalChangesEnable &&
        !isEdit ? (
          <Button
            width={229}
            variant="secondary"
            onClick={(event: any) => {
              event?.preventDefault();
              event?.stopPropagation();
              handleCancelClick();
            }}
          >
            Cancel New Promo Creation
          </Button>
        ) : null} */}
      </div>
      <ViewAllPromoModal
        isViewAllPromoModalOpen={isViewAllPromoModalOpen}
        setIsViewAllPromoModalOpen={setIsViewAllPromoModalOpen}
        promotionData={priceCostData}
        data={data}
        isZeroCost={isZeroCost}
      />
      <WarningModal
        onChange={handleCreatePromotion}
        isOpen={promoOverlapsModalOpen}
        setOpen={() => {}}
        warningTitle=""
        warningBodyText={promoOverlapsWarningMessage}
        warningLabel=""
        showCancelBtn={false}
        cancelButtonLabel=""
        confirmButtonLabel="Yes, Proceed"
        onCloseHandler={handleModalClose}
        width={600}
        height={200}
      />
    </>
  );
};

export default PromotionDetails;
