import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import { useForm, useController } from "react-hook-form";
import { PowerLineInfo } from "./power-line";

// Mock dependencies
jest.mock("../../../../fields/allowance-atoms", () => ({
  InputSelect: ({ fieldProps, onChange, options, disabled }) => (
    <select
      data-testid="input-select"
      disabled={disabled}
      onChange={(e) => onChange(e.target.value)}
    >
      {options &&
        options.map((opt) => (
          <option key={opt.value} value={opt.value}>
            {opt.label}
          </option>
        ))}
    </select>
  ),
  InputText: ({ onChange, fieldProps }) => (
    <input
      data-testid="input-text"
      placeholder={fieldProps?.placeholder || ""}
      onChange={(e) => onChange(e.target.value)}
    />
  ),
}));

jest.mock("../../offer-sections/billing/billing-comment-all-section", () => (props) => {
  const { onSubmit } = props;
  return (
    <form
      data-testid="billing-comment-all-section"
      onSubmit={(e) => {
        e.preventDefault();
        if (onSubmit) {
          onSubmit(); // Ensure the onSubmit function is called
        }
      }}
    >
      BillingCommentAllSection
    </form>
  );
});

jest.mock("./multi-selected", () => (props) => {
  const { setValues } = props;
  return (
    <div
      data-testid="multi-select-dropdown"
      onClick={() => {
        if (setValues) {
          setValues([{ name: "div1", divisionId: "div1", checked: true }]);
        }
      }}
    >
      MultiSelectDropDown
    </div>
  );
});

// Mock react-hook-form's useController
jest.mock("react-hook-form", () => ({
  ...jest.requireActual("react-hook-form"),
  useController: jest.fn(() => ({
    field: { value: "", onChange: jest.fn() },
    formState: { errors: {} },
  })),
}));

// Dummy Props & Callbacks
const formControls = {
  setValue: jest.fn(),
  register: jest.fn(),
  errors: {},
  formState: { errors: {} },
  control: {}, // Mock control object
  getValues: jest.fn((field) => {
    if (field === "vendorPaymentType") return "defaultPaymentType";
    return "";
  }),
};

const commonSuggestedVendorPaymentType = {
  registerField: "vendorPaymentType",
  options: [
    { value: "type1", label: "Payment Type 1" },
    { value: "type2", label: "Payment Type 2" },
  ],
  displayLabel: "Suggested Payment Type",
};

const commonAcApOrArNumber = {
  registerField: "apArNumber",
  placeholder: "Enter A/P or A/R Number",
};

const commonComment = {
  registerField: "comment",
  placeholder: "Enter Comment",
};

const divisionsData = [
  { name: "div1", divisionId: "div1", divisionName: "Division 1", checked: false },
  { name: "div2", divisionId: "div2", divisionName: "Division 2", checked: false },
];

const setDivisionsData = jest.fn();
const onUpdateAllCommentSubmit = jest.fn();
const isCommentRequired = false;

describe("PowerLineInfo", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

it("renders header and input fields", () => {
  render(
    <PowerLineInfo
      formControls={formControls}
      onUpdateAllCommentSubmit={onUpdateAllCommentSubmit}
      isCommentRequired={isCommentRequired}
      commonSuggestedVendorPaymentType={commonSuggestedVendorPaymentType}
      commonAcApOrArNumber={commonAcApOrArNumber}
      commonComment={commonComment}
      divisionsData={divisionsData}
      setDivisionsData={setDivisionsData}
      isError={false}
    />
  );

  expect(screen.getByText("BillingCommentAllSection")).toBeInTheDocument();
  expect(screen.getByTestId("input-select")).toBeInTheDocument();
  expect(screen.getByTestId("input-text")).toHaveAttribute("placeholder", "Enter A/P or A/R Number");
  expect(screen.getByTestId("multi-select-dropdown")).toBeInTheDocument(); // Updated to match the mock
});

  it("calls setValue when input text is changed", () => {
    render(
      <PowerLineInfo
        formControls={formControls}
        onUpdateAllCommentSubmit={onUpdateAllCommentSubmit}
        isCommentRequired={isCommentRequired}
        commonSuggestedVendorPaymentType={commonSuggestedVendorPaymentType}
        commonAcApOrArNumber={commonAcApOrArNumber}
        commonComment={commonComment}
        divisionsData={divisionsData}
        setDivisionsData={setDivisionsData}
        isError={false}
      />
    );

    const inputText = screen.getByTestId("input-text");
    fireEvent.change(inputText, { target: { value: "12345" } });
    expect(formControls.setValue).toHaveBeenCalledWith("apArNumber", "12345");
  });

  it("renders MultiSelectDropDown and calls setDivisionsData when divisions change", () => {
    render(
      <PowerLineInfo
        formControls={formControls}
        onUpdateAllCommentSubmit={onUpdateAllCommentSubmit}
        isCommentRequired={isCommentRequired}
        commonSuggestedVendorPaymentType={commonSuggestedVendorPaymentType}
        commonAcApOrArNumber={commonAcApOrArNumber}
        commonComment={commonComment}
        divisionsData={divisionsData}
        setDivisionsData={setDivisionsData}
        isError={false}
      />
    );

    expect(screen.getByTestId("multi-select-dropdown")).toBeInTheDocument();
    fireEvent.click(screen.getByTestId("multi-select-dropdown"));
    expect(setDivisionsData).toHaveBeenCalledWith([{ name: "div1", divisionId: "div1", checked: true }]);
  });
});
