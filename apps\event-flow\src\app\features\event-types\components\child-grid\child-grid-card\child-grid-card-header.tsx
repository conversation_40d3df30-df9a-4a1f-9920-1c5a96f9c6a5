import React, { memo } from "react";
import <PERSON><PERSON><PERSON>C<PERSON>Header from "./child-offers-card-header";
import Child<PERSON><PERSON><PERSON>Header from "./child-events-header";
import { useSelectorWrap } from "@me/data-rtk";
import ChildGridUserActions from "./child-grid-user-actions";

function ChildGridCardHeader({ cardIndex }) {
    const { data: selectedTabConfig = {} } = useSelectorWrap(
    "child_events_tab_config_data"
  );
  const { key = "" } = selectedTabConfig|| {};
  const headerComponentMapper = {
    offers: ChildOfferCardHeader,
    events: ChildEventsHeader
  }
  const HeaderComponent = headerComponentMapper?.[key] || ChildEventsHeader;

  return (
    <section className="flex justify-between w-full">
      <HeaderComponent cardIndex={cardIndex} selectedTabConfig={selectedTabConfig}/>
      <ChildGridUserActions cardIndex={cardIndex} selectedTabConfig={selectedTabConfig} />
    </section>
  );
}

export default memo(ChildGridCardHeader);
