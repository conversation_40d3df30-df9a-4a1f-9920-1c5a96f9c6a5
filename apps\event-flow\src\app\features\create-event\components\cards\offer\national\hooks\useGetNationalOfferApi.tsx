import { useSelectorWrap } from "@me/data-rtk";
import { useGetAppBasePath } from "@me/util-helpers";
import { useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  getQueryParams,
  isHfOrIfType,
  sortAllowanceById,
} from "../../../../../service/allowance/allowance-service";
import { appConstants } from "@me/utils-root-props";
import { buildURLforAllowanceDashboard } from "../../../allowance/stepper/allowance-amounts/allowance-amounts-helper";
import { useLazyGetNationalAllowancesItemsQuery } from "../../../../../service/apis/allowance-api";
import { saveToSessionStorage } from "../../../../../../../shared/helpers/event-flow-helpers";
import efConstants from "../../../../../../../shared/ef-constants/ef-constants";
import { isEmpty, cloneDeep } from "lodash";
import {
  isComingFromtask,
  isFeatureFlagEnabled,
  isVendorLoggedIn,
} from "@me-upp-js/utilities";
import { setOfferAmontsData } from "../../../../../service/slice/allowance-details-slice";
import { useDispatch } from "react-redux";
import { setNationalDivisionsConfig } from "../../../../../../all-allowances/nationals/slices/national-main-entry-slices";
import useFetchCombinedData from "./useFetchCombinedData";
import { getBatchPayloadByDivisions } from "../../offer-service";
import { useHandleAmountsResponse } from "../../hooks/amounts/useHandleAmountsResponse";

/**
 * this hook is used to make the amounts API call,
 * navigate to main entry screen
 * as well as return path for main entry screen
 * @param isEditEnable
 * @param key
 * @param productSources
 * @returns
 */
export default function useGetNationalOfferAmountApi(
  cardIndex: number,
  isEditEnable: boolean,
  key: string,
  productSources: string[],
  isSkipRedirection = false,
  isNational = false
) {
  const { basePath } = useGetAppBasePath();
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { data: offerAmountsDataObj } =
    useSelectorWrap("offer_amounts_details") || {};
  const [fetchNationalAmountsData] = useLazyGetNationalAllowancesItemsQuery();
  const { handleTempDeleteForInvalidItems, isTempDelInprogress } =
    useHandleAmountsResponse();
  const {
      data: { allowanceData: allowanceTempWorkData },
    } = useSelectorWrap("allowance_temp_work"),
    { isAllowConvEnable } = useSelectorWrap("allow_conv_status_rn").data;
  const { isPrimeSectionSectionUpdated = false } =
    useSelectorWrap("offer_section_update")?.data || {};
  const { offerDivisions = [] } =
    useSelectorWrap("national_offer_divisions")?.data || {};
  const { isMultiVendorEnabled = false } = useSelectorWrap(
    "vendors_data_for_allowance_rn"
  ).data;

  const isOfferInvalid = eventDetailsData?.inValidAllowances?.includes(
    eventDetailsData?.offerAllowances?.[cardIndex]?.id
  );
  const isMultiVendorFeatureEnable = isFeatureFlagEnabled(
    appConstants.FEATURE_FLAGS.MULTI_VENDOR
  );

  const { id: eventId = "" } = eventDetailsData || {};

  const [allowData, setAllowData] = useState<any>(null);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { taskType } = getQueryParams();
  const ROUTE_PARAM = {
    eventId: eventDetailsData?.id,
    offerAllowanceGroup: key,
    isEdit: isEditEnable,
    ...(taskType === appConstants.TASK_TYPE_NEW_ITEM && { taskType }),
    isNdpType: isNational,
  };
  const isCommingFromTaskView = useMemo(
    () => isComingFromtask(taskType, eventDetailsData),
    [taskType, eventDetailsData]
  );
  const { offerAmounts = {}, isAdditionalDatesChanged = {} } =
    offerAmountsDataObj || {};

  const amountsApiDataFromSlice =
    offerAmounts && !isEmpty(offerAmounts) && key ? offerAmounts?.[key] : {};
  const isDateChanged =
    !isEmpty(isAdditionalDatesChanged) && key
      ? isAdditionalDatesChanged?.[key]
      : 0;

  const allAllowancesPath = `${basePath}/${buildURLforAllowanceDashboard(
    ROUTE_PARAM,
    isAllowConvEnable
  )}`;
  const isIfHf = isHfOrIfType(allowanceTempWorkData?.[0]?.allowanceType);

  const queryParams = getBatchPayloadByDivisions(
    {
      URL_PARAMS: [eventId, key],
    },
    offerDivisions,
    { skipOverlaps: false }
  );

  const isAnyTempIdNotAvail = allowanceTempWorkData?.find(
    tempObj => !tempObj?.tempWorkAllowanceId
  );

  const isMultiVendorEabled =
    !isIfHf && isVendorLoggedIn() && isMultiVendorFeatureEnable
      ? isMultiVendorEnabled
      : false;

  const isSkipAmtCal =
    isMultiVendorEabled ||
    !key ||
    isAnyTempIdNotAvail ||
    !productSources?.length ||
    (isOfferInvalid && !isPrimeSectionSectionUpdated && !isIfHf) ||
    (amountsApiDataFromSlice &&
      amountsApiDataFromSlice?.length &&
      !isDateChanged);

  const {
    state: { data: allowancesResp = null, loading: isFetching = false },
  } = useFetchCombinedData(fetchNationalAmountsData, queryParams, isSkipAmtCal);

  useEffect(() => {
    const handleAmountsData = async () => {
      if (
        allowancesResp?.length &&
        allowancesResp?.every(allowance => allowance) &&
        (!amountsApiDataFromSlice ||
          !amountsApiDataFromSlice?.length ||
          isDateChanged)
      ) {
        const { amountsResp: filteredAmountsResp } =
          await handleTempDeleteForInvalidItems(allowancesResp);

        const updatedAllowRespData = filteredAmountsResp?.map(
          (itemObj: any) => {
            const sortedAllowances = sortAllowanceById([
              ...(itemObj?.allowances || []),
            ]);
            return {
              ...itemObj,
              allowances: sortedAllowances?.length
                ? sortedAllowances
                : itemObj?.allowances,
            };
          }
        );
        setAllowData(updatedAllowRespData);
        dispatch(
          setOfferAmontsData({
            offerAmounts: { ...offerAmounts, [key]: updatedAllowRespData },
          })
        );
        isNavigatedToMainEntry(updatedAllowRespData);
      }
    };

    handleAmountsData();
  }, [JSON.stringify(allowancesResp)]);

  useEffect(() => {
    if (amountsApiDataFromSlice?.length && !isDateChanged) {
      const updatedAllowRespData = amountsApiDataFromSlice?.map(
        (itemObj: any) => {
          const sortedAllowances = sortAllowanceById([
            ...(itemObj?.allowances || []),
          ]);
          return {
            ...itemObj,
            allowances: sortedAllowances?.length
              ? sortedAllowances
              : itemObj?.allowances,
          };
        }
      );
      setAllowData(updatedAllowRespData);
      isNavigatedToMainEntry(updatedAllowRespData);
    }
  }, [offerAmountsDataObj]);

  const setNationalDivisionConfig = () => {
    const divsionIdsCopy = cloneDeep(offerDivisions);
    const mappedDivisionsData = divsionIdsCopy?.map(divId => ({
      divisionId: divId,
      divisionName: appConstants.DIVISIONS_ID_NAME_MAP?.[divId],
    }));
    mappedDivisionsData?.length &&
      dispatch(
        setNationalDivisionsConfig({
          selectedDivisionData: mappedDivisionsData?.[0],
          divisionsList: mappedDivisionsData,
          isNdpType: true,
        })
      );
  };

  const isNavigatedToMainEntry = allowRespData => {
    const isAnySummarizedFalse = allowRespData?.find(
      allowObj => allowObj?.summary?.itemAmountsCouldBeSummarized === false
    );
    const isItemAmountsSummarized: boolean = isAnySummarizedFalse
      ? false
      : true;
    const isOverlapsAvailable = allowRespData?.some(
      respObj =>
        respObj?.offerAllowanceOverlapResults?.offerAllowanceOverlaps?.length >
        0
    );
    const isSummary = allowRespData?.some(allowObj => allowObj?.summary);
    if (isSummary) {
      saveToSessionStorage(
        efConstants.ITEM_AMOUNTS_SUMMARIZED_KEY,
        isItemAmountsSummarized
      );
    }
    const isRedirectToMainEntry = !isEditEnable
      ? !isOverlapsAvailable &&
        isItemAmountsSummarized === false &&
        !isSkipRedirection
      : false;
    const isNDP = () => {
      return !isEditEnable ? isNational && !isSkipRedirection : false;
    };
    isNational && setNationalDivisionConfig();
    if (isRedirectToMainEntry || isCommingFromTaskView || isNDP()) {
      navigate(allAllowancesPath);
    }
  };
  return {
    allAllowancesPath,
    allowancesResp: allowData,
    isFetching,
    basePath,
  };
}
