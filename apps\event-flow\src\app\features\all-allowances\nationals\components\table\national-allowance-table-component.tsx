import { useSelectorWrap } from "@me/data-rtk";
import React, { useEffect } from "react";
import { GetAllowanceGridCols } from "../../../columns/allowance-table-columns";
import { Column } from "@albertsons/uds/molecule/Table/Table.types";
import { useUpcFormatter } from "@me/util-helpers";
import _ from "lodash";
import Table from "@albertsons/uds/molecule/Table";
import { sortViewItems } from "../../../../../shared/helpers/event-flow-helpers";
import "../../../allowance-table/allowances-table-styles.scss";

type Allowance = {
  index: number;
  itemDescription: string;
  itemId: string;
  primaryUpc: string;
  itemUpcs: object;
  packWhse: number;
  size: string;
  vendorDetails: object;
};

function NationalAllowanceTableComponent({ items }) {
  const { data: switchValue } = useSelectorWrap("selectedSwitchValue_rn");
  const { data: tabSelection } = useSelectorWrap(
    "isUpdateAllowanceDistributorSelection_rn"
  );
  const [itemObj, setItemObj] = React.useState({});
  const [formattedItems, setFormattedItems] = React.useState([]);
  const { selectedDivisionData = {} } =
    useSelectorWrap("national_divisions_config")?.data || {};
  const {
    data: { isUpdateAll },
  } = useSelectorWrap("isUpdateAllClicked_rn");

  const {
    data: { isLeadChange },
  } = useSelectorWrap("leadDistributorsChanged_rn");
  const [formattedUPC, formatUPC] = useUpcFormatter();

  useEffect(() => {
    const validItemObj = items?.find(itemObj =>
      itemObj?.vendorDetails?.map(vendorObj => !vendorObj?.isEmptyVendor)
    );
    setItemObj(validItemObj);
    const formattedItemsData = items?.map(item => ({
      ...item,
      primaryUpc: formatUPC(item?.primaryUpc),
    }));
    setFormattedItems(formattedItemsData);
  }, [JSON.stringify(items)]);

  const AllowanceTableColumns: Column<Allowance>[] = GetAllowanceGridCols(
    _.cloneDeep(itemObj)
  );

  const memoedCol = React.useMemo(
    () => AllowanceTableColumns,
    [
      AllowanceTableColumns?.length,
      switchValue?.selectedSwitch,
      tabSelection.isAllowancefilterType,
      isUpdateAll,
      isLeadChange,
      selectedDivisionData?.divisionId,
      JSON.stringify(itemObj),
    ]
  );

  return (
    <Table
      itemKey="itemId"
      id="allowance-table"
      items={sortViewItems(formattedItems, "itemId")}
      columns={memoedCol}
      dividers={true}
      noPagination={true}
      noHeader={true}
      singleSort={true}
      className="allowance-table-wrapper overflow-x-auto"
    />
  );
}

export default NationalAllowanceTableComponent;
