import efConstants from "../../../../../shared/ef-constants/ef-constants";
import { getUpdatedYearBasedOnCurrentDate } from "../../../service/event-details/event-detail-service";
const { ALLOWANCE_TYPES } = efConstants;

export const ALLOWANCE_DATES = {
  allowanceSpecificFields: {
    [ALLOWANCE_TYPES.CASE.key]: {
      DSD: {
        key: "DSD_LEAD_DISTRIBUTORS",
        routeKey: "DSD_LEAD_DISTRIBUTORS",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
        },
      },
      WAREHOUSE: {
        key: "WAREHOUSE_DIST_CENTERS",
        routeKey: "WAREHOUSE_DIST_CENTERS",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
        },
      },
      COMBINED: {
        key: "DSD_WHSE_RETAIL_DIVISION",
        routeKey: "DSD_WHSE_RETAIL_DIVISION",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
        },
      },
    },
    [ALLOWANCE_TYPES.SCAN.key]: {
      DSD: {
        key: "DSD_LEAD_DISTRIBUTORS",
        routeKey: "DSD_LEAD_DISTRIBUTORS",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
        },
      },
      WAREHOUSE: {
        key: "DSD_WHSE_RETAIL_DIVISION",
        routeKey: "DSD_WHSE_RETAIL_DIVISION",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
        },
      },
    },
    [ALLOWANCE_TYPES.SHIPTOSTORE.key]: {
      DSD: {
        key: "DSD_LEAD_DISTRIBUTORS",
        routeKey: "DSD_LEAD_DISTRIBUTORS",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.DSD_LEAD_DISTRIBUTORS",
        },
      },
      WAREHOUSE: {
        key: "DSD_WHSE_RETAIL_DIVISION",
        routeKey: "DSD_WHSE_RETAIL_DIVISION",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
        },
      },
    },
    [ALLOWANCE_TYPES.HEADERFLAT.key]: {
      DSD: {
        key: "DSD_WHSE_RETAIL_DIVISION",
        routeKey: "DSD_WHSE_RETAIL_DIVISION",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
        },
      },
      WAREHOUSE: {
        key: "WAREHOUSE_DIST_CENTERS",
        routeKey: "WAREHOUSE_DIST_CENTERS",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
        },
      },
    },
    [ALLOWANCE_TYPES.ITEMFLAT.key]: {
      DSD: {
        key: "DSD_WHSE_RETAIL_DIVISION",
        routeKey: "DSD_WHSE_RETAIL_DIVISION",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
        },
      },
      WAREHOUSE: {
        key: "WAREHOUSE_DIST_CENTERS",
        routeKey: "WAREHOUSE_DIST_CENTERS",
        allowanceDatesData: {
          registerKeyName: "allowanceCreationVehicle.WAREHOUSE_DIST_CENTERS",
        },
      },
    },
    DEFAULT: {
      key: "DSD_WHSE_RETAIL_DIVISION",
      routeKey: "DSD_WHSE_RETAIL_DIVISION",
      allowanceDatesData: {
        registerKeyName: "allowanceCreationVehicle.DSD_WHSE_RETAIL_DIVISION",
      },
    },
  },
  vehicleTypeOrCustomDate: {
    label: "Vehicle Type/Custom Date",
    required: false,
    registerField: "allowance.vehicleTypeOrCustomDate",
    gqlQueryConst: "allowance",
    default: "Weekly Insert",
    type: "select",
    apiUrl: "",
    slice: "",
    displayLabel: "name",

    options: [
      {
        id: 1,
        name: "Weekly Insert",
      },
      {
        id: 2,
        name: "Monthly Insert",
      },
      {
        id: 3,
        name: "Yearly Insert",
      },
    ],
    errors: {
      required: {
        backgroundColor: "",
        text: "Vehicle Type/Custom Date is Required",
      },
    },
    mapperKey: "offerallowances.allowances.vehicle.vehicleType.vehicleTypDesc",
  },
  year: {
    label: "Year",
    required: false,
    registerField: "year",
    default: "2023",
    type: "select",
    apiUrl: "",
    slice: "",
    displayLabel: "name",

    options: [
      {
        name: getUpdatedYearBasedOnCurrentDate?.(Date.now())[0], //adds 2 weeks to the current date and returns year
        id: 1,
      },
      {
        name: getUpdatedYearBasedOnCurrentDate?.(Date.now())[1],
        id: 2,
      },
      {
        name: getUpdatedYearBasedOnCurrentDate?.(Date.now())[2],
        id: 3,
      },
      {
        name: getUpdatedYearBasedOnCurrentDate?.(Date.now())[3],
        id: 4,
      },
    ],
    errors: {
      required: {
        backgroundColor: "",
        text: "Year is Required",
      },
    },
  },
  startWeekOrVehicle: {
    label: "Start Week/Vehicle",
    required: false,
    registerField: "startWeekOrVehicle",
    optionUrl: "allowance.startWeek",
    default: "Week 02 Insert 2023",
    type: "select",
    apiUrl: "",
    slice: "",
    displayLabel: "vehicleNm",

    options: [
      {
        id: "",

        vehicleNm: "",
      },
    ],
    errors: {
      required: {
        backgroundColor: "",
        text: "Start Week/Vehicle is Required",
      },
    },
    mapperKey: "offerallowances.allowances.vehicle.vehicleNm",
  },
  vehicleStart: {
    label: "Vehicle Start",
    required: false,
    disable: true,
    registerField: "allowance.vehicleStart",
    type: "date",
    displayLabel: "name",

    error: {
      required: {
        backgroundColor: "",
        text: "Allowance Start Required",
      },
    },
    mapperKey: "offerallowances.allowances.vehicle.startDate",
  },
  vehicleEnd: {
    label: "Vehicle End",
    required: false,
    disable: true,
    registerField: "allowance.vehicleEnd",
    type: "date",
    displayLabel: "name",

    error: {
      required: {
        backgroundColor: "",
        text: "Allowance End Required",
      },
    },
    mapperKey: "offerallowances.allowances.vehicle.endDate",
  },
  orderStart: {
    label: "Order Start",
    required: true,
    disable: true,
    registerField: "allowance.orderStart",
    type: "date",
    error: {
      required: {
        backgroundColor: "",
        text: "Order start Required",
      },
    },
    mapperKey: "offerallowances.allowances.orderStartDate",
  },
  orderEnd: {
    label: "Order End",
    required: true,
    disable: true,
    registerField: "allowance.orderEnd",
    type: "date",
    error: {
      required: {
        backgroundColor: "",
        text: "Order End Required",
      },
    },
    mapperKey: "offerallowances.allowances.orderEndDate",
  },
  arrivalStart: {
    label: "Arrival Start",
    required: true,
    disable: true,
    registerField: "allowance.arrivalStart",
    type: "date",
    error: {
      required: {
        backgroundColor: "",
        text: "Arrival Start Required",
      },
    },
    mapperKey: "offerallowances.allowances.arrivalStartDate",
  },
  arrivalEnd: {
    label: "Arrival End",
    required: true,
    disable: true,
    registerField: "allowance.arrivalEnd",
    type: "date",
    error: {
      required: {
        backgroundColor: "",
        text: "Arrival End Required",
      },
    },
    mapperKey: "offerallowances.allowances.arrivalEndDate",
  },
  storeReceivingStart: {
    label: "Shipment Start",
    required: true,
    disable: true,
    registerField: "allowance.storeReceivingStart",
    type: "date",
    error: {
      required: {
        backgroundColor: "",
        text: "Shipment Start Required",
      },
    },
    mapperKey: "offerallowances.allowances.storeReceivingStartDate",
  },
  storeReceivingEnd: {
    label: "Shipment End",
    required: true,
    disable: true,
    registerField: "allowance.storeReceivingEnd",
    type: "date",
    error: {
      required: {
        backgroundColor: "",
        text: "Shipment End Required",
      },
    },
    mapperKey: "offerallowances.allowances.storeReceivingEndDate",
  },
  startDate: {
    label: "Start Date",
    required: true,
    disable: true,
    registerField: "allowance.startDate",
    type: "date",
    error: {
      required: {
        backgroundColor: "",
        text: "Start Date Required",
      },
    },
    mapperKey: "offerallowances.allowances.orderStartDate",
  },
  endDate: {
    label: "End Date",
    required: true,
    disable: true,
    registerField: "allowance.endDate",
    type: "date",
    error: {
      required: {
        backgroundColor: "",
        text: "End Date Required",
      },
    },
    mapperKey: "offerallowances.allowances.orderEndDate",
  },
  errors: {
    EMPTY_ALLOWANCES:
      "Invalid combination of Promo Product Groups and Store Groups. Please update Promo Product Groups or Store Groups to continue.",
    INVALID_DATES: {
      SCAN: {
        all: "Invalid Date combination, End date must be >= Start date",
        vehicle: "Allowance Dates must overlap Vehicle Dates by at least 1 day",
      },
      CASE: {
        orderStart:
          "Invalid Date combination, Arrival Start Date must be >= Order Start date",
        orderEnd:
          "Invalid Date combination, Arrival End Date must be >= Order End date",
        order:
          "Invalid Date combination, Order End date must be >= Order Start date",
        arrival:
          "Invalid Date combination, Arrival End date must be >= Arrival Start date",
        vehicle: "Allowance Dates must overlap Vehicle Dates by at least 1 day",
      },
      SHIP_TO_STORE: {
        ship: "Invalid Date combination, Arrival End date must be >= Arrival Start date",
        arrival:
          "Invalid Date combination, Arrival End date must be >= Arrival Start date",
        vehicle: "Allowance Dates must overlap Vehicle Dates by at least 1 day",
      },
      OTHER: "Invalid Date combination, End date must be >= Start date",
    },
  },
  allowanceCreationVehicle: {
    registerKeyName: "allowanceCreationVehicle",
  },
  initialText: {
    SCAN: {
      DSD_WHSE_RETAIL_DIVISION: {
        header:
          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
      },
      DSD_LEAD_DISTRIBUTORS: {
        header:
          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
      },
    },
    CASE: {
      DSD_LEAD_DISTRIBUTORS: {
        header:
          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than that of the full Event. These dates will be used for O/I and/or Billing.",
        bulletPointsText:
          "Based on ((Albertsons Accounting Policies,)) the dates for DSD Case Allowances will be pre-calculated as:",
        bulletPoints: [
          "DSD Arrival Start: set to be 2 days prior to Vehicle Start",
          "DSD Arrival End: set to match the Vehicle End",
          "If an exception to these default dates is necessary due to either other Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
        ],
      },
      WAREHOUSE_DIST_CENTERS: {
        header:
          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for O/I and/or Billing.",
        bulletPointsText:
          "Based on ((Albertsons Accounting Policies,)) the dates for Warehouse Case Allowances will be pre-calculated as:",
        bulletPoints: [
          "Order Start: Arrival Start Date - Lead Time* ",
          "Order End: Arrival End Date - Lead Time*",
          "Arrival Start: 10 days prior to Vehicle Start. 16 days in Northern California due to Hawaii, 21 days in Alaska.",
          "Arrival End: Vehicle End date",
          "For Displayer items only, an additional 14 days will be incorporated into the Order Start and Arrival Start date calculations",
        ],
        footer:
          "* As setup in the Albertsons buying system to represent total time in days from P.O. generation through warehouse receiving. If an exception to these default dates is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
      },
      DSD_WHSE_RETAIL_DIVISION: {
        header:
          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period less than that of the full Event. These dates will be used for O/I and/or Billing.",
        bulletPointsText:
          "Based on ((Albertsons Accounting Policies,)) the dates for DSD Case Allowances will be pre-calculated as:",
        bulletPoints: [
          "DSD Arrival Start: set to be 2 days prior to Vehicle Start",
          "DSD Arrival End: set to match the Vehicle End",
          "If an exception to these default dates is necessary due to either other Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
        ],
      },
    },
    SHIP_TO_STORE: {
      DSD_WHSE_RETAIL_DIVISION: {
        header:
          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period shorter than the full Event.",
        subHeader:
          "Based on ((Albertsons Accounting Policies,)) the dates for Ship To Store Allowances are not tied only to the Vehicle / Performance.  These allowances are earned upon Arrival and have no less than 7 days prior to the Performance start and 3 days after the end date.  14 Days prior for Displays.",
        footer:
          "If an exception to these defaults is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
      },
      DSD_LEAD_DISTRIBUTORS: {
        header:
          "Vehicle and Dates defaulted to match Event, override only if adding an Allowance that is for a time period shorter than the full Event.",
        subHeader:
          "Based on ((Albertsons Accounting Policies,)) the dates for Ship To Store Allowances are not tied only to the Vehicle / Performance.  These allowances are earned upon Arrival and have no less than 7 days prior to the Performance start and 3 days after the end date.  14 Days prior for Displays.",
        footer:
          "If an exception to these defaults is necessary due to either Overlapping / Back-To-Back Allowance(s) or other unique circumstances, both the Vendor and Albertsons Merchandising must explicitly agree to it.",
      },
    },
    HEADER_FLAT: {
      DSD_WHSE_RETAIL_DIVISION: {
        header:
          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
      },
      WAREHOUSE_DIST_CENTERS: {
        header:
          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
      },
    },
    ITEM_FLAT: {
      DSD_WHSE_RETAIL_DIVISION: {
        header:
          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
      },
      WAREHOUSE_DIST_CENTERS: {
        header:
          "Vehicle and Dates Defaulted to match Event, override only if adding an Allowance that is for a time period less than the full Event. These dates will be used for Billing.",
      },
    },
  },
};
