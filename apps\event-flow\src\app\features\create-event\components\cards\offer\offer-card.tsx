import { useSelectorWrap } from "@me/data-rtk";
import {
  getAllowanceFormReg<PERSON><PERSON><PERSON>,
  getAllowanceMap<PERSON>ey,
  getOfferFormRegister<PERSON><PERSON>,
  getQueryParams,
} from "../../../service/allowance/allowance-service";
import { useEffect, useState } from "react";
import { checkIsAllowConversionEnable } from "@me/util-helpers";
import {
  allowancePerfConfigHandler,
  setAllowanceFormInfo,
  setAllowConvEnable,
  setOfferSectionsEnableConfig,
} from "../../../service/slice/allowance-details-slice";
import { useDispatch } from "react-redux";
import { useFormContext } from "react-hook-form";
import OfferCardContent from "./offer-card-content";
import { setOfferConfigAndFormData } from "./offer-section-service";
import useHandleOfferSections from "./hooks/useHandleOfferSections";
import { SkeletonLoader } from "../../fields/allowance-atoms";
import {
  checkIsNationalEvent,
  // checkIsNationalEvent,
  getConvertedCreateInd,
  getUpdatedAllowanceTempData,
  resetOfferSectionData,
  updateOfferDivisionIds,
} from "./offer-service";
import { getObjectKeys } from "../../../service/allowance/allowance-stepper-service";
import Button from "@albertsons/uds/molecule/Button";
import Tooltip from "@albertsons/uds/molecule/Tooltip";
import { byPassOfferAllowanceHandler } from "../../../service/slice/event-detail-slice";
import { eventProgressDataHandler } from "../../../service/slice/event-progress-slice";
import { Info } from "lucide-react";
import efConstants from "../../../../../shared/ef-constants/ef-constants";

interface IOfferCardProps {
  cardIndex: number;
  cardItemIndex: number;
}

const OfferCard = ({ cardIndex = 0, cardItemIndex = 0 }: IOfferCardProps) => {
  const dispatch = useDispatch(),
    queryParams = getQueryParams();

  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { allowanceData: tempworkData, isTempLoaded = false } = useSelectorWrap(
    "allowance_temp_work"
  ).data;
  const { editCardConfig: isEditEnableValue } = useSelectorWrap(
    "offer_card_configutation_rn"
  ).data;
  const { data: allowancePerfData } = useSelectorWrap(
    "allowance_type_performance_data"
  );
  const { isEventVehicleChangedInPending, isEventVehicleChangedInHistory } =
    useSelectorWrap("check_event_vehicledates_rn").data;
  const { data: isNewAllowanceDetails } = useSelectorWrap(
    "allowance_new_card_configuration_rn"
  );
  const { productSources = [] } =
    useSelectorWrap("allowance_product_sources_rn")?.data || {};

  const { getValues } = useFormContext();

  const isEditEnable = isEditEnableValue?.[cardIndex] || false;
  const allowanceRegisterField = getAllowanceFormRegisterKey(
    cardIndex,
    cardItemIndex
  );

  const {
    eventType = "",
    offerAllowances = [],
    divisionIds = [],
  } = eventDetailsData || {};
  const isNationalEvent = checkIsNationalEvent(eventType);
  const updatedTempworkData = getUpdatedAllowanceTempData(
    tempworkData,
    eventType
  );
  const { isNewAllowance = false, isAddAnotherOffer = false } =
    isNewAllowanceDetails || {};
  const offerRegisterField = getOfferFormRegisterKey(cardIndex);

  const [isTempworkFormLoaded, setIsTempworkFormLoaded] = useState(false);

  const { setOfferSections } = useHandleOfferSections();

  // Set isAllowConvEnable value.
  useEffect(() => {
    const isAllowConvEnable =
      checkIsAllowConversionEnable(
        getValues(offerRegisterField)?.origOfferNumber
      ) && !(isEventVehicleChangedInPending || isEventVehicleChangedInHistory);

    dispatch(
      setAllowConvEnable({
        isAllowConvEnable,
      })
    );
  }, [isEventVehicleChangedInPending, isEventVehicleChangedInHistory]);

  // handle tempwork data and it's dependencies.
  useEffect(() => {
    if (isTempworkFormLoaded) return;
    updateOfferDivisionIds(
      dispatch,
      divisionIds,
      isNationalEvent,
      tempworkData
    );
    if (
      updatedTempworkData?.allowanceType &&
      getObjectKeys(updatedTempworkData?.allowanceTypeSpecification).length &&
      allowancePerfData?.getAllowancePerformanceTypes?.length &&
      productSources?.length &&
      isTempLoaded
    ) {
      //OFFER - WITH TEMPDATA
      const allowName =
        getAllowanceMapKey(updatedTempworkData?.allowanceType) || "";
      const tempFormData =
        updatedTempworkData?.allowanceTypeSpecification?.[allowName];
      const createInd = getConvertedCreateInd(
        tempFormData?.createInd,
        isEditEnable,
        productSources,
        offerAllowances?.[cardIndex] || {}
      );
      const offerSections = setOfferSections({
        createInd,
      });
      const { offerFormData, offerSectionConfig } = setOfferConfigAndFormData({
        tempworkData: tempFormData,
        isEditEnable,
        offerSections,
        additionalDetails: {
          eventType,
          createInd,
          allowancePerfData:
            allowancePerfData?.getAllowancePerformanceTypes || [],
          queryParams,
        },
      });
      dispatch(
        setAllowanceFormInfo({
          allowanceFormData: {
            [allowanceRegisterField]: {
              ...offerFormData,
            },
          },
        })
      );
      getObjectKeys(offerSectionConfig).length &&
        dispatch(setOfferSectionsEnableConfig(offerSectionConfig));
      dispatch(
        allowancePerfConfigHandler({
          performanceConfig: {
            [allowanceRegisterField]: isEditEnable
              ? offerFormData?.["allowancePrimeData"]?.perfConfig
                  ?.performanceConfig
              : tempFormData?.allowancePerformances?.performanceConfig,
          },
        })
      );
      setIsTempworkFormLoaded(true);
    } else if (
      !isEditEnable &&
      !updatedTempworkData?.allowanceType &&
      (isNewAllowance || isAddAnotherOffer) &&
      isTempLoaded
    ) {
      //NEW OFFER - CREATE MODE -  WITHOUT TEMPDATA
      resetOfferSectionData(dispatch);
      setIsTempworkFormLoaded(true);
    }
  }, [
    updatedTempworkData,
    JSON.stringify(productSources),
    allowancePerfData?.getAllowancePerformanceTypes?.length,
  ]);

  const showByPassAllowanceButton = () => {
    return (
      !getObjectKeys(updatedTempworkData?.allowanceTypeSpecification).length &&
      !isEditEnable &&
      eventDetailsData?.eventType ===
        efConstants.ALLOWANCE_SCREEN_TYPES.DP.key &&
      !eventDetailsData?.offerAllowances?.length
    );
  };

  const handleByPassAllowanceClick = () => {
    dispatch(
      byPassOfferAllowanceHandler({
        isOfferBypassed: true,
      })
    );
    dispatch(
      eventProgressDataHandler({
        selected: "Promotion",
        byPassedSections: ["Allowance"],
      })
    );
    resetOfferSectionData(dispatch);
  };

  const renderByPassAllowanceButton = () => {
    return showByPassAllowanceButton() ? (
      <div
        className="flex justify-start items-center abs-allowance-card-content-bypass-allowance"
        id="abs-allowance-card-content-bypass-allowance"
      >
        <Button
          onClick={handleByPassAllowanceClick}
          variant="tertiary"
          width={150}
        >
          <span className="font-medium">Bypass Allowance</span>
        </Button>
        <span>
          <Tooltip zIndex={10} anchor={"top"} variant="light">
            <Tooltip.Popover>
              <span className="flex flex-col m-4 text-xs text-[#5a697b] whitespace-nowrap">
                {efConstants.BYPASS_ALLOWANCE_LABEL}
              </span>
            </Tooltip.Popover>
            <Info color="#1B6EBB" size={15} />
          </Tooltip>
        </span>
      </div>
    ) : null;
  };

  return (
    <div className="!min-h-[210px]">
      {isTempworkFormLoaded ? (
        <>
          <OfferCardContent
            cardIndex={cardIndex}
            cardItemIndex={cardItemIndex}
            isEditEnable={isEditEnable}
          />
          {renderByPassAllowanceButton()}
        </>
      ) : (
        <div>
          <SkeletonLoader height={50} />
          <SkeletonLoader height={50} />
          <SkeletonLoader height={50} />
          <SkeletonLoader height={50} />
        </div>
      )}
    </div>
  );
};

export default OfferCard;
