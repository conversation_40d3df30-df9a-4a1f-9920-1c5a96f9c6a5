import { Button } from "@albertsons/uds/molecule/Button";
import React, { memo } from "react";
import { IUserActionsProps } from "../props-types";
import { useSelectorWrap } from "@me/data-rtk";

function UserActions({ actionsConfig }: IUserActionsProps) {
  const { isPrimeSectionSectionUpdated = false, isAmountsSectionUpdated = false } = useSelectorWrap("offer_section_update")?.data || {};
  const {
    onClick,
    sectionConfiguration: { create, edit } = { create: {}, edit: {} },
    isEditEnable,
    isFormDirty,
    baseId,
    isHideFieldsForMainEntry,
    isInValidOffer = false
  } = actionsConfig || {};
  const hideSaveBtn = isHideFieldsForMainEntry
    ? true
    : (isEditEnable)
    ? !isFormDirty
    : false;
  const hideForInValidOffer = isInValidOffer && !isAmountsSectionUpdated && !isHideFieldsForMainEntry  ? false : hideSaveBtn;
  const disableForInvalidOffer = isInValidOffer && !isPrimeSectionSectionUpdated;
  const isDisable = !isEditEnable ? isFormDirty : !disableForInvalidOffer;
  return (
    <div
      id={`${baseId}-user-action`}
      className={`mr-4 flex space-x-4 h-full relative ${
        isHideFieldsForMainEntry ? "" : "top-5"
      }`}
    >
      {!hideForInValidOffer && (
        <Button
          id={`${baseId}-save-btn`}
          className="cursor-pointer p-3"
          variant="primary"
          width={65}
          type="submit"
          disabled={!isDisable}
        >
          {isEditEnable ? edit?.label : create?.label}
        </Button>
      )}
      <Button
        type="button"
        id={`${baseId}-view-all-link`}
        className="cursor-pointer"
        variant="secondary"
        onClick={onClick}
        width={170}
        disabled={disableForInvalidOffer}
      >
        Edit / View All Items
      </Button>
    </div>
  );
}

export default memo(UserActions);
