import { roundOfNumber } from "apps/event-flow/src/app/features/create-event/service/allowance/allowance-service";
import Disabled<PERSON>ield from "./disabled-field";
import { InputText } from "../../../../../../fields/allowance-atoms";
import { getFieldWithHighletedWrapper } from "../../../../../allowance/stepper/common-stepper/allowance-amount/allowance-amounts-services";
import { IAmountStepFieldProps } from "../props-types";

const AllowanceAmountField = ({
  fieldProps,
  formControls,
  onChange,
  error,
  prefixValue = "$",
  className = "",
  disabled = false,
  value,
  multiplier = 1,
  allowanceAmountFields,
  getEventData,
  searchId,
  baseId = "",
} : IAmountStepFieldProps) => {
  const { control, register, setValue } = formControls || {};

  return (
    <>
      {disabled ? (
        <DisabledField
          label={fieldProps.label}
          required={fieldProps.required}
          value={roundOfNumber(parseFloat(value) * multiplier) || ""}
          prefix={prefixValue}
          baseId={baseId}
        />
      ) : (
        control && getFieldWithHighletedWrapper(
          <InputText
            fieldProps={fieldProps}
            type={fieldProps?.type}
            register={register}
            control={control}
            onChange={onChange}
            className={`allowance-amounts-padding amounts-input-field  ${className}`}
            onFocus={value => {
              setValue(fieldProps?.registerField, value);
            }}
            onWheel={event => event?.currentTarget?.blur()}
            prefixValue={prefixValue}
            error={error}
          />,
          fieldProps,
          allowanceAmountFields,
          getEventData,
          searchId
        )
      )}
    </>
  );
};

export default AllowanceAmountField;
