import React from "react";
import { useEffect, useRef, useState } from "react";
import { useFieldArray, useFormContext } from "react-hook-form";
import { useDispatch } from "react-redux";
import Button from "@albertsons/uds/molecule/Button";
import { useSelectorWrap } from "@me/data-rtk";
import { EVENT_STATUS as eventStatusData } from "../../../constants/event-status/event-status";
import {
  offerCardConfiguration,
  promoCardConfiguration,
  allowanceNewCardConfiguration,
  setOfferSubCardConfiguration,
  setPromoSubCardConfiguration,
  saveAllowanceTypeDataHandler,
  setAllowTypeChange,
} from "../../../service/slice/allowance-details-slice";
import MultiCardContainer from "./multi-card-container";
import SingleCardContainer from "./single-card-container";
import { RBAC } from "albertsons-react-rbac";
import { isPast } from "date-fns";
import {
  getLoggedInUserType,
  isVendorLoggedIn,
  isMerchantLoggedIn,
  isComingFromtask,
  isAllowanceFeatureEnabled,
  isPromoChanges,
  getNationalOfferStatus,
} from "@me-upp-js/utilities";
import { allowanceTempWorkHandler } from "../../../service/slice/allowance-temp-work-slice";
import {
  checkIsAllowanceTypeChanged,
  getAllowanceTypeByPerformance,
  getInvalidDataObject,
  getQueryParams,
  isCrossUserEditingCard,
  removeParams,
} from "../../../service/allowance/allowance-service";
import {
  useGetAllowanceTempWorkDataQuery,
  useLazyGetNationalAllowanceTempWorkDataQuery,
  usePostAllowanceEditTempWorkDataMutation,
  usePostNationalOfferEditMutation,
} from "../../../service/apis/allowance-api";
import {
  useGetAllowancePerformancesByEventTypeLazyQuery,
  useGetAllowancePerformanceTypeOptionsLazyQuery,
} from "../../../../../graphql/generated/schema";
import { allowanceTypeAndPerformanceDataHandler } from "../../../service/slice/allowance-details-slice";
import efConstants from "../../../../../shared/ef-constants/ef-constants";
import {
  getObjectKeys,
  getObjectValues,
} from "../../../service/allowance/allowance-stepper-service";
import {
  EEVENT_STATUS,
  EVENT_ACTION_LABELS,
  replaceMeuppuserToSystem,
  showEditedLabel,
} from "@me/util-helpers";
import LoadingSpinner from "../../../constants/LoadingSpinner/LoadingSpinner";
import Alert from "@albertsons/uds/molecule/Alert";
import {
  excludeOwnBrandsFromPPGs,
  getIsMultiVendorEvent,
} from "../event-details/event-details-card-service";
import _ from "lodash";
import {
  filterHistory,
  isEventReturnOnAgreedPending,
  isEventSendBackWtComment,
} from "../../../../event-types/event-progress-container-service";
import {
  checkIsNationalEvent,
  getBatchPayloadByDivisions,
  getUpdatedAllowanceTempData,
  resetOfferSectionData,
} from "../offer/offer-service";
import {
  ALLOWANCE_TOTAL_KEYS,
  PROMO_TOTAL_KEYS,
} from "../../../constants/fields/allowance/allowance-preview-fields-config";
import useFetchCombinedData from "../offer/national/hooks/useFetchCombinedData";
import { EVENT_TYPE } from "../../../../create-event/constants/constants";

interface ICardContainerLayout {
  cardConfiguration?: any;
  route?: any;
  cardFieldProp: any;
  step: any;
  sectionIndex: any;
}

const CardContainerLayout = ({
  route,
  cardConfiguration,
  cardFieldProp,
  step,
  sectionIndex,
}: ICardContainerLayout) => {
  const userRole = getLoggedInUserType();
  const dispatch = useDispatch();
  const { control, getValues } = useFormContext();
  const { taskType, offerId, isOfferViewMode } = getQueryParams();
  const { fields } = useFieldArray({
    name: cardConfiguration.cardFieldProp,
    control,
  });
  const TIMEOUT_INTERVAL = 3000;
  const { data: eventDetailsData } = useSelectorWrap("event_details_data");
  const { allowanceData } = useSelectorWrap("allowance_temp_work")?.data || {};
  const { data: isNewAllowanceDetails } = useSelectorWrap(
      "allowance_new_card_configuration_rn"
    ),
    {
      editCardConfig = {},
      openCardConfig = {},
      offerData,
    } = useSelectorWrap("offer_card_configutation_rn").data || {},
    {
      openCardConfig: promoOpenCardConfig = {},
      editCardConfig: promoEditCardConfig = {},
      isAddNewPromo: newPromoAdd = {},
    } = useSelectorWrap("promo_card_configutation_rn").data || {};
  const { offerSubCardConfig = {} } =
    useSelectorWrap("offer_sub_card_configutation_rn").data || {};
  const { promoSubCardConfig = {} } =
    useSelectorWrap("promo_sub_card_configutation_rn").data || {};
  const { isEventCardOpen = false } = useSelectorWrap(
    "is_event_edit_enable"
  ).data;
  const {
    data: { isEditPromotion: { isEdit = {} } = {} },
  } = useSelectorWrap("promotion_edit_enable_configutation_rn") || {};
  const { isDatesUpdated = false } =
    useSelectorWrap("promotion_dates_update_rn")?.data || {};
  const { isOfferBypassed = false } =
    useSelectorWrap("by_pass_offer_allowance")?.data || {};
  const { data: eventProgressConfigData } = useSelectorWrap(
    "eventProgressConfigData_rn"
  );
  const {
    data: { offerDivisions = [] },
  } = useSelectorWrap("national_offer_divisions") || {};
  const { productSources = [] } =
    useSelectorWrap("allowance_product_sources_rn")?.data || {};

  const [eventStatusFormData] = useState<unknown>({
    ...eventStatusData,
  });
  const [skipNationalTempCall, setSkipNationalTempCall] = useState(true);
  const isNewCardDisable = useRef(false);
  const isTempGetTriggered = useRef(false);
  const isTempDataLoaded = useRef(false);

  const { ALLOWANCE_TYPES, OFFER_EDITING_MODE_ERROR_MSG } = efConstants;
  const { HEADERFLAT } = ALLOWANCE_TYPES;
  const {
    DRAFT,
    PENDING_WITH_MERCHANT,
    PENDING_WITH_VENDOR,
    AGREED_PENDING,
    EXECUTED,
    REJECTED,
    CANCELED,
  } = EEVENT_STATUS;
  const {
    id: eventId = "",
    eventStatus = "",
    divisionIds = [],
    negotiationSimsVendors = [],
    eventType = "",
  } = eventDetailsData || {};
  const {
      isNewAllowance = false,
      stepperType = "",
      isAddAnotherOffer = false,
    } = isNewAllowanceDetails || {},
    isOfferDisabled = getObjectValues(editCardConfig).includes(true),
    isAllowanceCard = cardConfiguration.key === "Allowance",
    isPromoCard = cardConfiguration.key === "Promotion";
  const isNationalEvent = checkIsNationalEvent(eventType);
  const updatedTempworkData = getUpdatedAllowanceTempData(
    allowanceData,
    eventType
  );
  const isAddButtonRender =
    eventProgressConfigData?.[eventStatus]?.USERS?.[userRole]?.edit?.[
      cardConfiguration.key.toLowerCase()
    ];
  const cardRecords = eventDetailsData[cardConfiguration.cardFieldProp];
  const ppgsWithoutOwnBrands = excludeOwnBrandsFromPPGs(
    eventDetailsData?.planProductGroups
  );
  const { isMultiVendorEvent = false } = getIsMultiVendorEvent(
    ppgsWithoutOwnBrands,
    userRole
  );
  // const { isAllowanceTypeChanged } = useSelectorWrap(
  //   "allow_type_change_rn"
  // ).data;
  const offerAllowances = isAllowanceCard ? cardRecords : [];
  const viewOfferIdIndex = offerAllowances?.findIndex(
    item => item?.id === isOfferViewMode
  );
  const allowancesIds = offerAllowances?.[viewOfferIdIndex]?.allowances;
  const keyValue = {};
  for (let i = 0; i < allowancesIds?.length; i++) {
    keyValue[i] = true;
  }
  let isNew = true;
  let cardContainerData;

  const [
    postAllowanceEditTempWorkData,
    { isLoading: isAlowanceEditTempLoading },
  ] = usePostAllowanceEditTempWorkDataMutation();

  const [
    postNationalOfferEdit,
    { isLoading: isNationalAlowanceEditTempLoading },
  ] = usePostNationalOfferEditMutation();

  const [getAllowancePerformancesByEventType, { data: allowanceTypeInfo }] =
    useGetAllowancePerformancesByEventTypeLazyQuery({
      variables: {
        eventType: eventType,
      },
    });

  const [getAllowancePerformanceTypeOptions, { data: allowanceAndPerfInfo }] =
    useGetAllowancePerformanceTypeOptionsLazyQuery();

  const isEditRemoveButtonsRender =
    eventStatusFormData?.[eventDetailsData.eventStatus]?.USERS?.[userRole]
      ?.edit?.[cardConfiguration.key.toLowerCase()];

  const isEditingModeByCrossUser = isCrossUserEditingCard(
    eventStatus,
    allowanceData
  );

  const {
    data: allowanceTempDataFromGet = null,
    isFetching: isTempworkGetCreateLoaded,
  } = useGetAllowanceTempWorkDataQuery(
    {
      URL_PARAM: eventId,
      queryParams: {},
    },
    {
      skip: isNationalEvent || !eventId || !isEditRemoveButtonsRender,
    }
  );

  const [fetchNationalTempData] =
    useLazyGetNationalAllowanceTempWorkDataQuery();

  const queryParams = getBatchPayloadByDivisions(
    {
      URL_PARAM: eventId,
    },
    offerDivisions
  );

  const {
    state: {
      data: nationalTempData = null,
      loading: isNationalTempIsLoaded = false,
      error: isNationalTempFetchError = null,
    },
  } = useFetchCombinedData(
    fetchNationalTempData,
    queryParams,
    skipNationalTempCall
  );

  useEffect(() => {
    if (eventId && isNationalEvent) {
      setSkipNationalTempCall(() => false);
    }
  }, [eventId]);

  useEffect(() => {
    if (eventId) {
      getAllowancePerformancesByEventType();
      getAllowancePerformanceTypeOptions();
    }
  }, [
    eventId,
    getAllowancePerformanceTypeOptions,
    getAllowancePerformancesByEventType,
  ]);

  useEffect(() => {
    if (allowanceTypeInfo) {
      dispatch(saveAllowanceTypeDataHandler(allowanceTypeInfo));
    }
  }, [allowanceTypeInfo]);

  useEffect(() => {
    if (allowanceAndPerfInfo) {
      dispatch(allowanceTypeAndPerformanceDataHandler(allowanceAndPerfInfo));
    }
  }, [allowanceAndPerfInfo]);

  useEffect(() => {
    isTempDataLoaded.current = isNationalEvent
      ? isNationalTempIsLoaded
      : isTempworkGetCreateLoaded;
    const tempObjectData = isNationalEvent
      ? nationalTempData?.[0] || null
      : allowanceTempDataFromGet;

    if (isTempDataLoaded.current) {
      // Only make it true once api starts fetching
      isTempGetTriggered.current = isTempDataLoaded.current;
    }
    if (!isTempDataLoaded.current && isTempGetTriggered.current) {
      // our code should run only when api is finished fetching
      if (
        eventId &&
        tempObjectData &&
        !getObjectKeys(updatedTempworkData).length
      ) {
        // TEMP WORK GET API CALL WITH VALID DATA
        isTempGetTriggered.current = false;
        dispatch(
          allowanceTempWorkHandler({
            allowanceData: isNationalEvent
              ? nationalTempData
              : allowanceTempDataFromGet,
            isTempLoaded: true,
          })
        );
        if (isAllowanceFeatureEnabled) {
          const { offerIndex, isAllowanceUpdated } =
            checkIsAllowanceTypeChanged(tempObjectData, eventDetailsData);
          isAllowanceUpdated &&
            dispatch(
              setAllowTypeChange({
                isAllowanceTypeChanged: { [offerIndex]: !!isAllowanceUpdated },
              })
            );
        }
      } else if (tempObjectData === null) {
        isTempGetTriggered.current = false; // Reset trigger when data is null
        dispatch(
          allowanceTempWorkHandler({
            allowanceData: {},
            isTempLoaded: true,
          })
        );
      }
    }
  }, [
    allowanceTempDataFromGet,
    nationalTempData,
    isTempworkGetCreateLoaded,
    isNationalTempIsLoaded,
  ]);

  const checkEventStatus = () => {
    if ([CANCELED].includes(eventStatus)) return false;
    return true;
  };

  const handleOfferSubCardConfig = subCardConfig => {
    dispatch(
      setOfferSubCardConfiguration({
        offerSubCardConfig: {
          ...offerSubCardConfig,
          ...subCardConfig,
        },
      })
    );
  };
  const handleSetOfferCardConfig = openCardConfigValue => {
    dispatch(
      offerCardConfiguration({
        editCardConfig,
        openCardConfig: { ...openCardConfig, ...openCardConfigValue },
      })
    );
  };

  const handleOfferCardConfigStatus = (
    records = {},
    cardIndex = 0,
    value = false,
    isEditRequired = false
  ) => {
    const values = getObjectKeys(records).length
      ? records
      : { [cardIndex]: value };
    dispatch(
      offerCardConfiguration({
        editCardConfig: isEditRequired
          ? { [cardIndex]: value }
          : editCardConfig,
        openCardConfig: { ...openCardConfig, ...values },
      })
    );
    handleOfferSubCardConfig({
      [cardIndex]: { [isAddAnotherOffer ? cardIndex : 0]: value },
    });
    isOfferBypassed &&
      handlePromoSubCardConfig({
        [cardIndex]: { 0: true },
      });
  };

  const handleSubCardConfigStatus = () => {
    dispatch(
      offerCardConfiguration({
        editCardConfig: { ...editCardConfig },
        openCardConfig: { [viewOfferIdIndex]: true },
      })
    );
    handleOfferSubCardConfig({ [viewOfferIdIndex]: keyValue });
  };

  const handlePromoSubCardConfig = subCardConfig => {
    dispatch(
      setPromoSubCardConfiguration({
        promoSubCardConfig: {
          ...promoSubCardConfig,
          ...subCardConfig,
        },
      })
    );
  };
  const handleSetPromoCardConfig = openCardConfigValue => {
    dispatch(
      promoCardConfiguration({
        editCardConfig: promoEditCardConfig,
        openCardConfig: { ...promoOpenCardConfig, ...openCardConfigValue },
        isAddNewPromo: newPromoAdd,
      })
    );
  };

  const handlePromoCardConfigStatus = (
    records = {},
    cardIndex = 0,
    value = false
  ) => {
    const values = getObjectKeys(records).length
      ? records
      : { [cardIndex]: value };
    dispatch(
      promoCardConfiguration({
        editCardConfig: promoEditCardConfig,
        openCardConfig: { ...promoOpenCardConfig, ...values },
        isAddNewPromo: newPromoAdd,
      })
    );
    handlePromoSubCardConfig({
      [cardIndex]: { 0: value },
    });
  };

  const checkOffercardIsValid = () => {
    return checkEventStatus() && eventId && isAllowanceCard;
  };

  useEffect(() => {
    if (!isAllowanceCard) return;
    if (eventDetailsData?.eventType !== EVENT_TYPE.NCDP) {
      if (isComingFromtask(taskType, eventDetailsData)) {
        let nullAmountOffers = findNullAllowAmountOffers(
          eventDetailsData?.offerAllowances
        );
        nullAmountOffers = _.uniqBy(nullAmountOffers, "offerId");
        if (!nullAmountOffers?.length) return;
        handleInvalidOfferOpenCard(nullAmountOffers);
      }
      if (eventDetailsData?.inValidAllowances?.length) {
        handleInvalidOfferOpenCard();
      }
    }
  }, [eventDetailsData, offerData]);

  const disablePromoForPastEvent =
    cardConfiguration.key === "Promotion" &&
    isPast(new Date(eventDetailsData?.startDate));

  const isPromoCardOnAgreedPending = () => {
    const isSendBackEvent = isEventSendBackWtComment(
      eventDetailsData?.planEventHistory,
      EVENT_ACTION_LABELS.STATUS_UPDATE_WITH_COMMENT
    );

    const isVendorAfterSendBackWComment = filterHistory(
      eventDetailsData?.planEventHistory,
      EVENT_ACTION_LABELS.STATUS_UPDATE_WITH_COMMENT
    );
    const { isReturnEvent, isReturnByCurrUser } = isEventReturnOnAgreedPending(
      [
        eventDetailsData?.eventDetailsEventInd &&
          eventDetailsData?.otherDetailsChangedInd,
        eventDetailsData?.allowanceEventInd,
        eventDetailsData?.promotionEventInd,
      ],
      eventDetailsData?.planEventHistory,
      userRole,
      EVENT_ACTION_LABELS.RETURN,
      eventStatus
    );
    if (
      (isSendBackEvent ||
        (isVendorAfterSendBackWComment &&
          isReturnEvent &&
          isReturnByCurrUser)) &&
      isVendorLoggedIn()
    )
      return false;
    else return true;
  };

  const disableCardContainerFn = () => {
    return (
      (((isPromoChanges() &&
        ![DRAFT, PENDING_WITH_VENDOR].includes(eventStatus)) ||
        PENDING_WITH_MERCHANT === eventStatus) &&
        isVendorLoggedIn()) ||
      (PENDING_WITH_VENDOR === eventStatus && isMerchantLoggedIn()) ||
      (eventStatus === AGREED_PENDING && isPromoCardOnAgreedPending()) ||
      [EXECUTED, REJECTED, CANCELED].includes(eventStatus)
    );
  };
  useEffect(() => {
    /** Disbale offer/promo card when user adds only promo/offer and sends it for review and vice versa on below conditions*/
    isNewCardDisable.current =
      disablePromoForPastEvent || disableCardContainerFn();
  }, [cardConfiguration.key, eventDetailsData]);

  const handleOfferCardConfig = () => {
    const allowanceObjectKeys = getObjectKeys(allowanceData);
    const cardIndex = offerAllowances?.length || 0;
    if (allowanceObjectKeys.length) {
      handleAllowanceCardChanges();
    } else if (
      offerAllowances?.length &&
      isAddAnotherOffer &&
      isEventCardOpen
    ) {
      handleOfferCardConfigStatus({}, cardIndex, false);
    } else if (
      offerAllowances?.length &&
      isAddAnotherOffer &&
      !isEventCardOpen
    ) {
      handleOfferCardConfigStatus({}, cardIndex, true);
    } else if (
      !offerAllowances?.length &&
      !allowanceObjectKeys.length &&
      eventType !== EVENT_TYPE.NCDP
    ) {
      handleOfferCardConfigStatus({}, 0, !isEventCardOpen && !isOfferBypassed);
      dispatch(
        allowanceNewCardConfiguration({
          isNewAllowance: true,
          stepperType: "offerAllowances",
          isAddAnotherOffer: false,
        })
      );
    } else if (isOfferViewMode !== null && isAllowanceCard) {
      handleSubCardConfigStatus();
    } else if (
      offerAllowances?.length &&
      !isAddAnotherOffer &&
      !isNew &&
      !allowanceObjectKeys.length
    ) {
      const isAnyFieldEdited = handleOpenCardIfAnyEdited();
      if (!isAnyFieldEdited) {
        handleOfferCardConfigStatus({}, offerAllowances?.length, false);
      }
    }
  };

  const handleOpenCardIfAnyEdited = () => {
    if (!eventDetailsData?.id || eventStatus === EEVENT_STATUS.CANCELED) {
      return false;
    }
    let openCardConfigValue = {};
    let subCardConfig = {};
    const totalKeys = isAllowanceCard ? ALLOWANCE_TOTAL_KEYS : PROMO_TOTAL_KEYS;
    const _key = isAllowanceCard ? "allowances" : "promotionsList";
    cardRecords?.forEach((record, cardIndex) => {
      let subOpenCardConfigValue = {};
      record?.[_key]?.forEach((cardRecord, cardItemIndex: number) => {
        const subCardOpenStatus = showEditedLabel(
          totalKeys,
          getValues,
          cardIndex,
          cardItemIndex,
          cardConfiguration.previewModuleKey,
          eventDetailsData
        );
        subOpenCardConfigValue = {
          ...subOpenCardConfigValue,
          [cardItemIndex]: subCardOpenStatus,
        };
      });
      const subCardConfigValues = getObjectValues(subOpenCardConfigValue);
      const cardOpenStatus = subCardConfigValues.some(value => value);

      subCardConfig = {
        ...subCardConfig,
        [cardIndex]: subOpenCardConfigValue,
      };

      openCardConfigValue = {
        ...openCardConfigValue,
        [cardIndex]: editCardConfig?.[cardIndex] || cardOpenStatus,
      };
    });
    const isAnyFieldEdited = getObjectValues(subCardConfig).some(val => val);
    if (isAnyFieldEdited) {
      handleCardConfig(subCardConfig, openCardConfigValue);
      return true;
    }
    return false;
  };

  const handleCardConfig = (subCardConfig, openCardConfigValue) => {
    if (isAllowanceCard && isOfferViewMode == null) {
      handleOfferSubCardConfig(subCardConfig);
      handleSetOfferCardConfig(openCardConfigValue);
    }
    if (isPromoCard) {
      handlePromoSubCardConfig(subCardConfig);
      handleSetPromoCardConfig(openCardConfigValue);
    }
  };

  useEffect(() => {
    checkOffercardIsValid() && !isMultiVendorEvent && handleOfferCardConfig();
    return () => {
      if (isOfferViewMode) {
        setTimeout(() => removeParams(), TIMEOUT_INTERVAL);
      }
    };
  }, [
    allowanceData,
    eventDetailsData,
    offerAllowances,
    isAddAnotherOffer,
    isEventCardOpen,
    isOfferViewMode,
    isOfferBypassed,
  ]);

  const findNullAllowAmountOffers = offers =>
    offers.flatMap(offer => {
      const allowanceType = getAllowanceTypeByPerformance(
        offer?.allowances?.[0].performance
      );
      const isHeaderFlatAmount = allowanceType === HEADERFLAT.label;
      if (!isHeaderFlatAmount) {
        return (
          offer?.allowances?.flatMap(allowance => {
            const isCancelledOrRejected = [
              EEVENT_STATUS.CANCELED,
              EEVENT_STATUS.REJECTED,
            ].includes(allowance.allowanceStatus);
            if (!isCancelledOrRejected) {
              return (
                allowance?.allowanceItems?.flatMap(item =>
                  item?.allowanceItemComps
                    ?.filter(comp => {
                      return !Object.prototype.hasOwnProperty.call(
                        comp || {},
                        "allowAmount"
                      );
                    })
                    ?.map(() => {
                      return {
                        offerId: offer?.id,
                        offerNumber: offer?.offerNumber,
                      };
                    })
                ) || []
              );
            }
            return [];
          }) || []
        );
      }
      return [];
    });

  const handlePromoCardConfig = () => {
    const { inValidAllowances = [] } = getValues() || {};
    if (isMultiVendorEvent || isEventCardOpen) {
      handlePromoCardConfigStatus({}, 0, !isEventCardOpen);
    } else if (
      eventDetailsData?.offerAllowances?.length > 0 &&
      !isOfferDisabled &&
      !getObjectKeys(allowanceData).length &&
      eventDetailsData?.promotionsLists?.length === 0 &&
      !getObjectValues(promoOpenCardConfig).includes(true) &&
      !isAddAnotherOffer &&
      !inValidAllowances?.length
    ) {
      handlePromoCardConfigStatus({}, 0, !isEventCardOpen);
    } else if (
      eventDetailsData?.promotionsLists?.length > 0 &&
      !isEventCardOpen &&
      !isAddAnotherOffer &&
      !getObjectValues(newPromoAdd)?.includes(true)
    ) {
      const isAnyFieldEdited = handleOpenCardIfAnyEdited();
      if (!isAnyFieldEdited) {
        handlePromoCardConfigStatus({}, 0, false);
      }
    } else if (
      (eventDetailsData && !getObjectKeys(promoOpenCardConfig).length) ||
      isAddAnotherOffer ||
      isOfferDisabled
    ) {
      handlePromoCardConfigStatus({}, 0, false);
    } else if (
      eventDetailsData &&
      !getObjectKeys(newPromoAdd)?.filter(index => newPromoAdd[index]?.length)
    ) {
      eventDetailsData?.promotionsLists?.length > 0 &&
        dispatch(
          promoCardConfiguration({
            ...promoOpenCardConfig,
            ...promoEditCardConfig,
            isAddNewPromo: { 0: false },
          })
        );
    }
  };

  useEffect(() => {
    // Need this timer to wait for event response to resolved before opening promo card
    const timer = setTimeout(() => {
      !isAllowanceCard && checkEventStatus() && handlePromoCardConfig();
    }, 1000);
    return () => clearTimeout(timer);
  }, [
    allowanceData,
    eventDetailsData,
    isMultiVendorEvent,
    isEventCardOpen,
    isOfferDisabled,
  ]);

  const handleAllowanceCardChanges = () => {
    const offerNumber = isNationalEvent
      ? allowanceData?.[0]?.offerNumber
      : allowanceData?.offerNumber;
    const offerNum = offerNumber
      ? offerNumber?.toString()
      : offerId === "undefined"
      ? null
      : offerId;
    if (isEditingModeByCrossUser) return;
    if (offerNum) {
      handleOfferCardOnEdit(offerNum);
    } else {
      handleOfferCardOnCreate();
    }
  };

  const handleInvalidOfferOpenCard = async (nullAmountOffers?) => {
    const inValidOfferAndPromos = getInvalidDataObject(
      eventDetailsData,
      eventDetailsData?.["inValidAllowances"],
      eventDetailsData?.["inValidPromotions"]
    );
    if (
      (cardConfiguration.key === "Allowance" &&
        inValidOfferAndPromos?.offerAllowances?.length > 0 &&
        !getObjectKeys(allowanceData)?.length) ||
      nullAmountOffers?.length
    ) {
      let index;
      const offersData = !nullAmountOffers?.length
        ? inValidOfferAndPromos?.offerAllowances
        : nullAmountOffers;
      const offerNumber = offerData && offerData?.split("_")[0];

      if (offerData) {
        index = offersData?.findIndex(item => item?.offerId === offerNumber);
        index = index + 1;
        if (index === offersData?.length) {
          index = 0;
        }
      }

      const isNational = isNationalEvent;
      const offerKey = isNational ? "offerNumber" : "offerId";

      const offerId = !offerNumber
        ? offersData?.[0]?.[offerKey]
        : offersData?.[index]?.[offerKey];

      const tempRes = await (isNational
        ? postNationalOfferEdit({ URL_PARAM: offerId })
        : postAllowanceEditTempWorkData({ URL_PARAM: offerId }));

      dispatch(
        allowanceTempWorkHandler({
          allowanceData: tempRes?.data,
          isTempLoaded: true,
        })
      );
      sessionStorage.setItem("OFFER_ID", tempRes?.data?.offerAllowancesId);
    }
  };

  const handleOfferCardOnEdit = (offerNum: string) => {
    const currentOfferIndex = offerAllowances?.findIndex(
      item => item?.offerNumber?.toString() === offerNum
    );
    const allowanceStatus =
      offerAllowances?.[currentOfferIndex]?.allowances?.[0]?.allowanceStatus;
    const validStatusCheck = isNationalEvent
      ? getNationalOfferStatus(offerAllowances?.[currentOfferIndex]?.allowances)
      : allowanceStatus;
    const currentIndex =
      currentOfferIndex >= 0 ? currentOfferIndex : offerAllowances?.length;
    if (validStatusCheck !== CANCELED) {
      dispatch(
        allowanceTempWorkHandler({
          allowanceData,
          isTempLoaded: true,
        })
      );
      dispatch(allowanceNewCardConfiguration({}));
      handleOfferCardConfigStatus({}, currentIndex, true, !isEventCardOpen);
    }
  };

  const handleOfferCardOnCreate = () => {
    const cardIndex = offerAllowances?.length || 0;

    if (isEditingModeByCrossUser) return;
    handleOfferCardConfigStatus(
      {},
      cardIndex,
      !isEventCardOpen && !isOfferBypassed
    );
    dispatch(
      allowanceTempWorkHandler({
        isNewAllowance: cardIndex === 0,
        allowanceData: allowanceData,
        isTempLoaded: true,
      })
    );
    dispatch(
      allowanceNewCardConfiguration({
        isNewAllowance: cardIndex === 0,
        stepperType: "offerAllowances",
        isAddAnotherOffer: !!cardIndex,
      })
    );
    return;
  };

  const handleAddAnotherOfferClick = (event, stepType) => {
    resetOfferSectionData(dispatch);
    event?.stopPropagation();
    event?.preventDefault();
    dispatch(
      allowanceTempWorkHandler({
        isNewAllowance: true,
        allowanceData: {},
        isTempLoaded: true,
      })
    );
    handleOfferCardConfigStatus({}, offerAllowances?.length, true);
    dispatch(
      allowanceNewCardConfiguration({
        isNewAllowance: false,
        stepperType: stepType,
        isAddAnotherOffer: true,
      })
    );
  };

  // check if the offer/promo is new or existing
  if (eventId && cardRecords && cardRecords?.length > 0) {
    isNew = false;
  }

  // change the parent config for new card
  if (eventId && cardRecords && cardRecords?.length === 0) {
    cardConfiguration.title = cardConfiguration["offerTitle"];
    cardConfiguration.isOpenCard = true;
  }

  const singleCard = () =>
    checkEventStatus() &&
    !(isAllowanceCard && isMultiVendorEvent) &&
    !isNewCardDisable.current ? (
      <>
        {[fields[0]].map((ele, index) => (
          <SingleCardContainer
            key={cardConfiguration?.key}
            cardContainerData={cardContainerData}
            cardConfiguration={cardConfiguration}
            cardIndex={index}
            cardItemIndex={index}
            isNew={true}
            step={step}
            sectionIndex={sectionIndex}
            isMultiple={false}
            isAddAnotherOffer={isAddAnotherOffer}
            isMultiVendorEvent={isMultiVendorEvent}
            isEventCardOpened={isEventCardOpen}
          />
        ))}
      </>
    ) : null;

  const newCardComponent = () => (
    <SingleCardContainer
      cardContainerData={cardContainerData}
      cardConfiguration={cardConfiguration}
      cardIndex={cardRecords?.length}
      cardItemIndex={cardRecords?.length}
      isNew={true}
      step={step}
      sectionIndex={sectionIndex}
      isMultiple={false}
      isAddAnotherOffer={isAddAnotherOffer}
      isMultiVendorEvent={isMultiVendorEvent}
      isEventCardOpened={isEventCardOpen}
    />
  );

  const multipleCard = () => (
    <MultiCardContainer
      cardContainerData={cardContainerData}
      cardConfiguration={cardConfiguration}
      isNewCard={false}
      fields={fields}
      step={step}
      sectionIndex={sectionIndex}
      isAddAnotherOffer={isAddAnotherOffer}
    />
  );

  const cardComponent = isNew ? singleCard() : multipleCard();

  // get error message for editing mode by cross user
  const getEditingModeAlertMsg = () => {
    if (allowanceData) {
      const { lastUpdUserId, createUserId } = allowanceData || {},
        displayUserId = lastUpdUserId || createUserId;
      return OFFER_EDITING_MODE_ERROR_MSG.replace(
        "USER_ID",
        replaceMeuppuserToSystem(displayUserId)
      );
    }
    return "";
  };

  // show error message for editing mode by cross user
  const showAlertBar = () => {
    const editingModeAlerMsg = getEditingModeAlertMsg();
    return (
      editingModeAlerMsg && (
        <div
          className="absolute z-50 top-0 left-[20%]"
          id="abs-common-card-editing-mode-aler-msg-cont"
        >
          <Alert isOpen={true} dismissible={false} variant={"error"}>
            {editingModeAlerMsg}
          </Alert>
        </div>
      )
    );
  };

  const isPromoOpen = () => {
    return isEdit === true || isDatesUpdated;
  };
  const isTempLoaded = isNationalEvent
    ? isNationalTempIsLoaded
    : isTempworkGetCreateLoaded;
  return (
    <>
      {isEditingModeByCrossUser ? showAlertBar() : null}
      <LoadingSpinner
        classname="!h-full !w-full rounded-md"
        isLoading={
          isTempLoaded ||
          isAlowanceEditTempLoading ||
          isNationalAlowanceEditTempLoading
        }
      />
      {cardComponent}
      {!isNewAllowance &&
      isAddAnotherOffer &&
      stepperType === cardConfiguration.cardFieldProp
        ? newCardComponent()
        : null}
      {!isNew &&
      cardConfiguration?.section !== "Promotion Details" &&
      isAddButtonRender &&
      !isMultiVendorEvent &&
      eventDetailsData?.eventType !== EVENT_TYPE.NCDP ? (
        <RBAC
          divisionIds={divisionIds}
          permissionsOnly={["PROMOTION_ALLOWANCE_MGMT_EDIT"]}
          simsVendors={negotiationSimsVendors}
        >
          <Button
            width={320}
            onClick={event =>
              handleAddAnotherOfferClick(event, "offerAllowances")
            }
            disabled={
              isOfferDisabled ||
              isAddAnotherOffer ||
              isEventCardOpen ||
              isEditingModeByCrossUser ||
              !productSources?.length ||
              isPromoOpen()
            }
            variant="secondary"
          >
            + Add Another Offer & Allowance
          </Button>
        </RBAC>
      ) : null}
    </>
  );
};

export default React.memo(CardContainerLayout);
